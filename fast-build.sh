#!/bin/bash

# Fast Docker Build Script with BuildKit optimization
# Optimized for faster builds using Docker BuildKit and parallel processing

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Enable BuildKit for faster builds
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

log "Docker BuildKit enabled for faster builds"

# Check Docker
if ! command -v docker &> /dev/null; then
    error "Docker is not installed"
fi

if ! docker info &> /dev/null; then
    error "Docker daemon is not running"
fi

# Detect docker compose
if command -v docker-compose &> /dev/null; then
    COMPOSE="docker-compose"
elif docker compose version &> /dev/null; then
    COMPOSE="docker compose"
else
    error "Docker Compose not available"
fi

info "Using: $COMPOSE with BuildKit"

# Show build progress
show_progress() {
    local pid=$1
    local delay=0.5
    local spinstr='|/-\'
    while [ "$(ps a | awk '{print $1}' | grep $pid)" ]; do
        local temp=${spinstr#?}
        printf " [%c] Building..." "$spinstr"
        local spinstr=$temp${spinstr%"$temp"}
        sleep $delay
        printf "\b\b\b\b\b\b\b\b\b\b\b\b\b"
    done
    printf "    \b\b\b\b"
}

# Fast build function
fast_build() {
    local service=${1:-""}
    local target=${2:-""}
    
    log "Starting fast build..."
    
    # Build arguments for optimization
    local build_args=""
    if [ -n "$target" ]; then
        build_args="--target $target"
    fi
    
    # Use parallel builds and BuildKit features
    if [ -n "$service" ]; then
        log "Building service: $service"
        DOCKER_BUILDKIT=1 $COMPOSE build $build_args --parallel "$service"
    else
        log "Building all services with parallel processing"
        DOCKER_BUILDKIT=1 $COMPOSE build $build_args --parallel
    fi
}

# Build with cache optimization
build_with_cache() {
    log "Building with cache optimization..."
    
    # Try to pull existing images for cache
    info "Pulling existing images for cache..."
    $COMPOSE pull --ignore-pull-failures || warn "Could not pull some images for cache"
    
    # Build with cache
    DOCKER_BUILDKIT=1 $COMPOSE build --parallel
}

# Clean and fast build
clean_fast_build() {
    log "Clean fast build (removing old images)..."
    
    # Remove old images
    docker image prune -f || warn "Could not prune images"
    
    # Build fresh
    fast_build
}

# Show build stats
show_build_stats() {
    log "Build statistics:"
    echo "=================="
    
    # Show image sizes
    echo "Image sizes:"
    docker images | grep -E "(appradio|radio-mentions)" | head -10
    
    echo ""
    echo "Build cache usage:"
    docker system df
    
    echo ""
    echo "Recent build layers:"
    docker history $(docker images -q | head -1) | head -10 2>/dev/null || echo "No recent images found"
}

# Optimize build environment
optimize_build_env() {
    log "Optimizing build environment..."
    
    # Set optimal BuildKit settings
    export BUILDKIT_PROGRESS=plain
    export DOCKER_BUILDKIT=1
    
    # Clean up build cache if it's too large
    local cache_size=$(docker system df --format "table {{.Type}}\t{{.Size}}" | grep "Build Cache" | awk '{print $3}' | sed 's/[^0-9.]//g')
    if [ -n "$cache_size" ] && (( $(echo "$cache_size > 1" | bc -l) )); then
        warn "Build cache is large (${cache_size}GB), cleaning..."
        docker builder prune -f
    fi
    
    info "Build environment optimized"
}

# Monitor build progress
monitor_build() {
    local service=${1:-""}
    
    log "Starting monitored build..."
    
    # Start build in background
    if [ -n "$service" ]; then
        fast_build "$service" &
    else
        fast_build &
    fi
    
    local build_pid=$!
    
    # Monitor progress
    show_progress $build_pid
    
    # Wait for completion
    wait $build_pid
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log "Build completed successfully!"
        show_build_stats
    else
        error "Build failed with exit code $exit_code"
    fi
}

# Show usage
show_usage() {
    echo "Fast Docker Build Script with BuildKit"
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build [SERVICE]    Fast build (default)"
    echo "  cache              Build with cache optimization"
    echo "  clean              Clean fast build"
    echo "  monitor [SERVICE]  Build with progress monitoring"
    echo "  optimize           Optimize build environment"
    echo "  stats              Show build statistics"
    echo "  web                Build web service only"
    echo "  prod               Build production target"
    echo "  dev                Build development target"
    echo ""
    echo "Examples:"
    echo "  $0 build           # Fast build all services"
    echo "  $0 web             # Build web service only"
    echo "  $0 cache           # Build with cache optimization"
    echo "  $0 clean           # Clean build"
    echo "  $0 monitor         # Build with progress monitoring"
}

# Main execution
case "${1:-build}" in
    "build")
        optimize_build_env
        fast_build "$2"
        ;;
    "cache")
        optimize_build_env
        build_with_cache
        ;;
    "clean")
        optimize_build_env
        clean_fast_build
        ;;
    "monitor")
        optimize_build_env
        monitor_build "$2"
        ;;
    "optimize")
        optimize_build_env
        log "Build environment optimization completed"
        ;;
    "stats")
        show_build_stats
        ;;
    "web")
        optimize_build_env
        fast_build "web"
        ;;
    "prod")
        optimize_build_env
        fast_build "" "production"
        ;;
    "dev")
        optimize_build_env
        fast_build "" "development"
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        warn "Unknown command: $1"
        show_usage
        ;;
esac
