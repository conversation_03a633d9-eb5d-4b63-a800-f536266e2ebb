# Git
.git
.gitignore
README.md

# Docker (keep only essential files)
Dockerfile.dev
docker-compose.dev.yml
docker-compose.override.yml
.dockerignore

# Python cache and build artifacts
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
nosetests.xml
coverage.xml
*.cover
*.log
.mypy_cache
.hypothesis

# Virtual environments
appenv/
venv/
env/
ENV/
env.bak/
venv.bak/

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Environment files
.env
.env.*
!.env.example

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
tmp/
temp/
*.tmp
*.bak
*.backup

# Documentation
docs/
*.md
!README.md

# Test files
test_*.py
*_test.py
tests/

# Template structure (development only)
template_structure/
structure/

# Logs
logs/
*.log

# Development files (exclude from build context)
current_packages.txt
debug_*.py
test_*.pdf
*.bak
*.backup
*.tmp

# Large directories that slow down builds
backups/
*.sql
*.dump
logs/
postgres_data/
redis_data/
node_modules/.cache/
.npm/
.cache/

# Certificates (should be mounted as volumes)
certs/

# Build optimization - exclude heavy files
*.mp4
*.avi
*.mov
*.mkv
*.pdf
*.zip
*.tar.gz
*.tar.bz2

# Build scripts (not needed in container)
build*.sh
fast-build.sh
deploy*.sh

# SSL certificates (mounted as volumes)
docker/ssl/
docker/certbot/

# Additional build optimizations
.pytest_cache/
.mypy_cache/
.coverage
htmlcov/
