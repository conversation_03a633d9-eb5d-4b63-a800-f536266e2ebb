#!/bin/bash

# Simple deployment script for RadioMention
set -e

# Detect Docker Compose command
if command -v docker-compose >/dev/null 2>&1; then
    COMPOSE_CMD="docker-compose"
elif docker compose version >/dev/null 2>&1; then
    COMPOSE_CMD="docker compose"
else
    echo "❌ Docker Compose not found. Please install Docker Compose."
    exit 1
fi

echo "🚀 Starting RadioMention deployment..."
echo "📋 Using: $COMPOSE_CMD"

# Stop existing containers
echo "📦 Stopping existing containers..."
$COMPOSE_CMD down

# Build and start services
echo "🔨 Building and starting services..."
$COMPOSE_CMD up -d --build

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 30

# Check if web service is responding
echo "🔍 Checking web service..."
for i in {1..10}; do
    if curl -f http://localhost:8000/ >/dev/null 2>&1; then
        echo "✅ Web service is responding!"
        break
    elif curl -f http://localhost/ >/dev/null 2>&1; then
        echo "✅ Nginx is responding!"
        break
    else
        echo "⏳ Waiting for web service... ($i/10)"
        sleep 10
    fi
done

# Show container status
echo "📊 Container status:"
$COMPOSE_CMD ps

echo ""
echo "🎉 Deployment completed!"
echo "📱 Application: http://localhost"
echo "🔧 Admin: http://localhost/admin/"
echo "📊 Health: http://localhost:8000/health/"
echo ""
echo "📋 To view logs: $COMPOSE_CMD logs -f"
echo "🛑 To stop: $COMPOSE_CMD down"
