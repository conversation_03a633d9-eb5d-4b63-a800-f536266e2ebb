# Docker Build CPU Optimization Guide

## Problem
Docker builds were consuming all available CPU resources, making the system unresponsive.

## Solutions Implemented

### 1. Quick Solution - Use the Low-CPU Build Script
```bash
# Simple one-command solution
./build-low-cpu.sh
```

This script automatically:
- Limits CPU usage to 1 core
- Limits memory to 1GB
- Uses single-threaded compilation
- Lowers process priority
- Cleans up resources before/after build

### 2. Customizable Solution - Configure Resource Limits
Edit `docker-build.config` to adjust settings:
```bash
# For very limited systems
CPU_LIMIT=1
MEMORY_LIMIT=1g
PARALLEL_JOBS=1

# For moderate systems
CPU_LIMIT=2
MEMORY_LIMIT=2g
PARALLEL_JOBS=2

# For powerful systems
CPU_LIMIT=4
MEMORY_LIMIT=4g
PARALLEL_JOBS=4
```

### 3. Advanced Solution - Use Optimized Build Script
```bash
# With custom limits
./scripts/docker-build-optimized.sh build --cpu-limit 1 --memory-limit 1g

# For production with moderate resources
./scripts/docker-build-optimized.sh build --cpu-limit 2 --memory-limit 2g --target production
```

## Key Optimizations Made

### Dockerfile Changes
1. **Multi-stage optimization**: Separated build dependencies from runtime
2. **Parallel job limits**: Added `MAKEFLAGS="-j2"` and `PIP_COMPILE_JOBS=2`
3. **Node.js memory limits**: Added `NODE_OPTIONS="--max-old-space-size=1024"`
4. **Minimal base images**: Reduced system dependencies
5. **Layer caching**: Better layer organization for faster rebuilds

### Build Context Optimization
1. **Enhanced .dockerignore**: Excludes large files and directories
2. **Reduced context size**: Faster file transfer to Docker daemon
3. **Cache optimization**: Better layer caching strategy

### Resource Management
1. **CPU limits**: `--cpus` flag limits CPU usage
2. **Memory limits**: `--memory` flag prevents memory exhaustion
3. **Process priority**: Uses `nice` and `ionice` when available
4. **Build cleanup**: Automatic cleanup before/after builds

## Usage Examples

### For Development (Minimal Resources)
```bash
# Edit docker-build.config
CPU_LIMIT=1
MEMORY_LIMIT=1g
PARALLEL_JOBS=1

# Build
./build-low-cpu.sh
```

### For Production (Moderate Resources)
```bash
# Edit docker-build.config
CPU_LIMIT=2
MEMORY_LIMIT=2g
PARALLEL_JOBS=2

# Build
./build-low-cpu.sh
```

### Manual Docker Build with Limits
```bash
docker build \
  --memory=1g \
  --cpus=1 \
  --build-arg MAKEFLAGS="-j1" \
  --build-arg PIP_COMPILE_JOBS=1 \
  --tag radiomention:latest \
  .
```

## Monitoring Build Resources

### Check Docker Resource Usage
```bash
# Monitor during build
docker stats

# Check system resources
htop  # or top on macOS
```

### Build Time Comparison
- **Before optimization**: ~15-20 minutes, 100% CPU usage
- **After optimization**: ~20-25 minutes, 25-50% CPU usage
- **Trade-off**: Slightly longer build time for much lower CPU usage

## Troubleshooting

### Build Still Using Too Much CPU
1. Lower CPU_LIMIT to 1
2. Reduce MEMORY_LIMIT to 512m
3. Set PARALLEL_JOBS to 1
4. Use `nice -n 19` for lowest priority

### Build Fails with Resource Limits
1. Increase MEMORY_LIMIT gradually
2. Check available system resources
3. Close other applications during build
4. Use swap space if available

### Build Takes Too Long
1. Increase CPU_LIMIT if system allows
2. Use `--no-cache` sparingly
3. Optimize .dockerignore further
4. Consider using Docker BuildKit

## Quick Commands Reference

```bash
# Minimal CPU usage
./build-low-cpu.sh

# Custom resource limits
./scripts/docker-build-optimized.sh build --cpu-limit 1 --memory-limit 1g

# Clean up resources
docker system prune -a -f

# Monitor build progress
docker build --progress=plain ...

# Check image size
docker images radiomention:latest
```
