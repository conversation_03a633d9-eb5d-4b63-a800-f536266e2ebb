# Docker Compose override for SSL configuration
# Use with: docker-compose -f docker-compose.yml -f docker-compose.ssl.yml up

services:
  web:
    environment:
      # Enable SSL in Django
      - USE_TLS=true
      - DISABLE_SSL_REDIRECT=false
      - SECURE_SSL_REDIRECT=true

  nginx:
    environment:
      - NGINX_SERVER_NAME=${DOMAIN_NAME}
    volumes:
      # Additional SSL certificate volumes
      - ./docker/certbot/conf:/etc/letsencrypt:ro
      - ./docker/certbot/www:/var/www/certbot:ro
      - ./docker/ssl:/etc/ssl:ro
    depends_on:
      - web

  # Override the certbot service from main compose file to remove profile restriction
  certbot:
    image: certbot/certbot:latest
    volumes:
      - ./docker/certbot/conf:/etc/letsencrypt
      - ./docker/certbot/www:/var/www/certbot
      - ./docker/certbot/logs:/var/log/letsencrypt
    networks:
      - app-network
    command: echo "Certbot service ready for SSL certificate management"
    restart: "no"
    profiles: []  # Remove profile restriction for SSL setup
