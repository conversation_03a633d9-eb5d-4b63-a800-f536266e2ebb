# AppRadio Environment Configuration Template
# Copy this file to .env and update the values for your deployment

# =============================================================================
# Django Configuration
# =============================================================================
DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production
DEBUG=false
SECRET_KEY=your-secret-key-here-change-this-in-production

# =============================================================================
# Database Configuration
# =============================================================================
POSTGRES_DB=radio_mentions
POSTGRES_USER=postgres
POSTGRES_PASSWORD=SecureP@ssw0rd2025!
POSTGRES_HOST=db
POSTGRES_PORT=5432

# Alternative: Database URL format
# DATABASE_URL=*************************************************/radio_mentions

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Alternative: Redis URL format
# REDIS_URL=redis://redis:6379/0

# =============================================================================
# Email Configuration
# =============================================================================
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=true
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=AppRadio <<EMAIL>>

# =============================================================================
# Gunicorn Configuration
# =============================================================================
GUNICORN_WORKERS=4
GUNICORN_WORKER_CLASS=sync
GUNICORN_TIMEOUT=120
GUNICORN_MAX_REQUESTS=1000
GUNICORN_MAX_REQUESTS_JITTER=100
GUNICORN_LOG_LEVEL=info

# =============================================================================
# Security Configuration
# =============================================================================
NGINX_SERVER_NAME=your-domain.com
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
DISABLE_SSL_REDIRECT=false
SECURE_SSL_REDIRECT=true
SECURE_HSTS_SECONDS=31536000

# =============================================================================
# Superuser Configuration (Optional)
# =============================================================================
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=your-admin-password

# =============================================================================
# File Storage Configuration
# =============================================================================
MEDIA_ROOT=/app/media
STATIC_ROOT=/app/staticfiles

# =============================================================================
# Logging Configuration
# =============================================================================
LOG_LEVEL=INFO
DJANGO_LOG_FILE=/app/logs/django.log
GUNICORN_ACCESS_LOG=/app/logs/gunicorn_access.log
GUNICORN_ERROR_LOG=/app/logs/gunicorn_error.log

# =============================================================================
# Celery Configuration
# =============================================================================
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
CELERY_WORKER_CONCURRENCY=2

# =============================================================================
# Monitoring and Health Checks
# =============================================================================
# SENTRY_DSN=your-sentry-dsn-here  # DISABLED
HEALTH_CHECK_ENABLED=true

# =============================================================================
# PDF Generation Configuration
# =============================================================================
PDF_FONT_PATH=/app/static/fonts
PDF_TEMP_DIR=/app/media/temp_uploads

# =============================================================================
# Time Zone Configuration
# =============================================================================
TIME_ZONE=Africa/Nairobi
USE_TZ=true

# =============================================================================
# Development Settings (set to false in production)
# =============================================================================
ENABLE_DEBUG_TOOLBAR=false
ENABLE_DJANGO_EXTENSIONS=false
