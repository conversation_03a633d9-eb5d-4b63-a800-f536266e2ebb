import os
from celery import Celery
from celery.schedules import crontab
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'radio_mentions_project.settings')

app = Celery('radio_mentions_project')

# Load config from Django settings and use 'CELERY_' prefix.
app.config_from_object('django.conf:settings', namespace='CELERY')

# ✅ Ensure connection retry on startup for Celery 6+
app.conf.broker_connection_retry_on_startup = True

# Autodiscover tasks in all installed apps.
app.autodiscover_tasks()

# Beat schedule for periodic tasks
app.conf.beat_schedule = {
    # Show monitoring tasks
    'monitor-show-status': {
        'task': 'apps.core.tasks.monitor_show_status',
        'schedule': 60.0,
    },
    'check-show-sessions': {
        'task': 'apps.core.tasks.check_show_sessions',
        'schedule': 300.0,
    },
    'track-presenter-activity': {
        'task': 'apps.core.tasks.track_presenter_activity',
        'schedule': 600.0,
    },
    'detect-show-conflicts': {
        'task': 'apps.core.tasks.detect_show_conflicts',
        'schedule': 1800.0,
    },
    'send-show-reminders': {
        'task': 'apps.core.tasks.send_show_reminders',
        'schedule': 300.0,
    },

    # Mention workflow tasks
    'check-pending-approvals': {
        'task': 'apps.mentions.tasks.check_pending_approvals',
        'schedule': 900.0,
    },
    'send-deadline-reminders': {
        'task': 'apps.mentions.tasks.send_deadline_reminders',
        'schedule': 3600.0,
    },
    'check-overdue-mentions': {
        'task': 'apps.mentions.tasks.check_overdue_mentions',
        'schedule': 300.0,
    },
    'detect-scheduling-conflicts': {
        'task': 'apps.mentions.tasks.detect_scheduling_conflicts',
        'schedule': 600.0,
    },
    'auto-resolve-conflicts': {
        'task': 'apps.mentions.tasks.auto_resolve_conflicts',
        'schedule': 1800.0,
    },

    # Reporting and analytics
    'generate-daily-reports': {
        'task': 'apps.reports.tasks.generate_daily_reports',
        'schedule': 86400.0,
        'options': {'expires': 3600}
    },
    'generate-weekly-analytics': {
        'task': 'apps.reports.tasks.generate_weekly_analytics',
        'schedule': 604800.0,
    },
    'generate-presenter-performance-reports': {
        'task': 'apps.reports.tasks.generate_presenter_performance_reports',
        'schedule': 2592000.0,
    },
    'analyze-system-performance': {
        'task': 'apps.reports.tasks.analyze_system_performance',
        'schedule': 86400.0,
    },
    'generate-client-reports': {
        'task': 'apps.reports.tasks.generate_client_reports',
        'schedule': 2592000.0,
    },

    # System maintenance
    'cleanup-old-logs': {
        'task': 'apps.activity_logs.tasks.cleanup_old_logs',
        'schedule': 86400.0,
    },
    'monitor-database-health': {
        'task': 'apps.activity_logs.tasks.monitor_database_health',
        'schedule': 3600.0,
    },
    'cleanup-orphaned-data': {
        'task': 'apps.activity_logs.tasks.cleanup_orphaned_data',
        'schedule': 86400.0,
    },
    'generate-system-health-report': {
        'task': 'apps.activity_logs.tasks.generate_system_health_report',
        'schedule': 604800.0,
    },
    'optimize-database-performance': {
        'task': 'apps.activity_logs.tasks.optimize_database_performance',
        'schedule': 604800.0,
    },
    'monthly-maintenance-reminder': {
        'task': 'apps.activity_logs.tasks.monthly_database_reindex',
        'schedule': 2592000.0,  # 30 days - reminder only
        'options': {'expires': 300}  # 5 minutes to complete
    },
    'daily-database-analyze': {
        'task': 'apps.activity_logs.tasks.safe_database_analyze',
        'schedule': 86400.0,  # Daily - safe statistics update
        'options': {'expires': 1800}  # 30 minutes to complete
    },
    'weekly-automated-reindex': {
        'task': 'apps.activity_logs.tasks.automated_maintenance_reindex',
        'schedule': crontab(hour=3, minute=0, day_of_week=1),  # Monday 3:00 AM
        'options': {'expires': 7200}  # 2 hours to complete
    },
    'monthly-deep-reindex': {
        'task': 'apps.activity_logs.tasks.automated_maintenance_reindex',
        'schedule': crontab(hour=2, minute=30, day_of_month=1),  # 1st of month 2:30 AM
        'options': {'expires': 10800}  # 3 hours to complete
    },

    # System health monitoring
    'system-health-check': {
        'task': 'apps.core.tasks.system_health_check',
        'schedule': 1800.0,
    },

    # User notifications
    'send-daily-summaries': {
        'task': 'apps.core.tasks.send_daily_summaries',
        'schedule': 86400.0,
    },
    'process-notification-queue': {
        'task': 'apps.core.tasks.process_notification_queue',
        'schedule': 30.0,
    },

    # Conflict resolution and monitoring
    'generate-conflict-reports': {
        'task': 'apps.mentions.tasks.generate_conflict_reports',
        'schedule': 604800.0,
    },
    'monitor-resolution-effectiveness': {
        'task': 'apps.mentions.tasks.monitor_resolution_effectiveness',
        'schedule': 2592000.0,
    },
}

# Time zone configuration
app.conf.timezone = 'Africa/Nairobi'

# Debugging helper
@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
