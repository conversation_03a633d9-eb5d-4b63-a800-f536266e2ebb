# =============================================================================
# DJANGO PRODUCTION SETTINGS FOR RADIO MENTION - SECTION 1
# =============================================================================
import os
from pathlib import Path
from .settings import *

BASE_DIR = Path(__file__).resolve().parent.parent

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

ALLOWED_HOSTS = [
  'radiocity.ugapp.net', 'www.radiocity.ugapp.net','*************', 'localhost', '127.0.0.1', '0.0.0.0',
]

# Django Security Settings - Updated Configuration


# Django Security Settings - Updated Configuration

# Core security headers
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin'

# SSL/HTTPS Configuration
# USE_TLS = os.environ.get('USE_TLS', 'True').lower() == 'true'
# if USE_TLS and not DEBUG:
#     SECURE_SSL_REDIRECT = True
#     SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
#     SESSION_COOKIE_SECURE = True
#     CSRF_COOKIE_SECURE = True
#     CSRF_COOKIE_HTTPONLY = True
#     SESSION_COOKIE_HTTPONLY = True
# else:
#     SECURE_SSL_REDIRECT = False
#     SESSION_COOKIE_SECURE = False
#     CSRF_COOKIE_SECURE = False

SECURE_SSL_REDIRECT = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False
SECURE_PROXY_SSL_HEADER = None

# Session Configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cached_db'
SESSION_COOKIE_AGE = 60 * 60 * 4  # 4 hours
SESSION_SAVE_EVERY_REQUEST = True  # Refresh session on activity
SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # Allow persistent sessions
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_IP_CHECK = False  # Disabled to prevent mobile/VPN issues
SESSION_MAX_AGE = 24 * 60 * 60  # Keep at 24 hours
SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access
SESSION_COOKIE_NAME = 'sessionid'  # Default, but explicit

# CSRF Configuration
CSRF_FAILURE_VIEW = 'apps.core.error_handlers.custom_csrf_failure_view'
CSRF_COOKIE_AGE = 60 * 60 * 4  # 4 hours to match session
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_USE_SESSIONS = True  # Use session-based CSRF tokens
CSRF_TOKEN_TIMEOUT = 60 * 60 * 4  # 4 hours token validity

# Trusted Origins
CSRF_TRUSTED_ORIGINS = [
    'https://*************',
    'https://mention.ugapp.net',
    'https://www.mention.ugapp.net',
    'http://localhost:8000',
    'http://127.0.0.1:8000',
]

# Optional: Add session cleanup middleware to settings
# Add 'django.contrib.sessions.middleware.SessionMiddleware' to MIDDLEWARE if not present

# Optional: For production, consider adding:
# SESSION_CACHE_ALIAS = 'default'  # If using cache-based sessions
# # Core security headers
# SECURE_BROWSER_XSS_FILTER = True
# SECURE_CONTENT_TYPE_NOSNIFF = True
# SECURE_HSTS_SECONDS = 31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True
# SECURE_HSTS_PRELOAD = True
# SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
# SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin'

# # SSL/HTTPS Configuration
# # USE_TLS = os.environ.get('USE_TLS', 'True').lower() == 'true'
# # if USE_TLS and not DEBUG:
# #     SECURE_SSL_REDIRECT = True
# #     SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
# #     SESSION_COOKIE_SECURE = True
# #     CSRF_COOKIE_SECURE = True
# #     CSRF_COOKIE_HTTPONLY = True
# #     SESSION_COOKIE_HTTPONLY = True
# # else:
# #     SECURE_SSL_REDIRECT = False
# #     SESSION_COOKIE_SECURE = False
# #     CSRF_COOKIE_SECURE = False

# SECURE_SSL_REDIRECT = False
# SESSION_COOKIE_SECURE = False
# CSRF_COOKIE_SECURE = False
# SECURE_PROXY_SSL_HEADER = None

# # Session Configuration - Updated for better UX
# SESSION_COOKIE_AGE = 60 * 60 * 24  # 24 hours (was 4 hours)
# SESSION_EXPIRE_AT_BROWSER_CLOSE = False  # Allow persistent sessions
# SESSION_COOKIE_SAMESITE = 'Lax'
# SESSION_IP_CHECK = False  # Disabled to prevent mobile/VPN issues
# SESSION_MAX_AGE = 24 * 60 * 60  # Keep at 24 hours
# SESSION_SAVE_EVERY_REQUEST = True  # Refresh session on activity

# # CSRF Configuration - Updated for better UX
# CSRF_COOKIE_AGE = 60 * 60 * 24  # Match session age (24 hours)
# CSRF_COOKIE_SAMESITE = 'Lax'
# CSRF_USE_SESSIONS = True  # Use session-based CSRF tokens
# CSRF_FAILURE_VIEW = 'apps.core.error_handlers.custom_csrf_failure_view'
# CSRF_TOKEN_TIMEOUT = 60 * 60 * 24  # 24 hours token validity

# # Trusted Origins
# CSRF_TRUSTED_ORIGINS = [
#     'https://radiocity.ugapp.net', 
#     'http://www.radiocity.ugapp.net',
#     'http://localhost:8000', 
#     'http://127.0.0.1:8000'
# ]

# # Additional session security (optional)
# SESSION_COOKIE_HTTPONLY = True  # Prevent JavaScript access
# SESSION_COOKIE_NAME = 'sessionid'  # Default, but explicit

# Optional: Add session cleanup middleware to settings
# Add 'django.contrib.sessions.middleware.SessionMiddleware' to MIDDLEWARE if not present

# Optional: For production, consider adding:
# SESSION_ENGINE = 'django.contrib.sessions.backends.db'  # Database sessions
# SESSION_CACHE_ALIAS = 'default'  # If using cache-based sessions

# SECURE_BROWSER_XSS_FILTER = True
# SECURE_CONTENT_TYPE_NOSNIFF = True
# SECURE_HSTS_SECONDS = 31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS = True
# SECURE_HSTS_PRELOAD = True
# SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
# SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin'

# # USE_TLS = os.environ.get('USE_TLS', 'True').lower() == 'true'
# # if USE_TLS and not DEBUG:
# #     SECURE_SSL_REDIRECT = True
# #     SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
# #     SESSION_COOKIE_SECURE = True
# #     CSRF_COOKIE_SECURE = True
# #     CSRF_COOKIE_HTTPONLY = True
# #     SESSION_COOKIE_HTTPONLY = True
# # else:
# #     SECURE_SSL_REDIRECT = False
# #     SESSION_COOKIE_SECURE = False
# #     CSRF_COOKIE_SECURE = False

# SECURE_SSL_REDIRECT = False
# SESSION_COOKIE_SECURE = False
# CSRF_COOKIE_SECURE = False
# SECURE_PROXY_SSL_HEADER = None

# SESSION_COOKIE_AGE = 60 * 60 * 4
# SESSION_EXPIRE_AT_BROWSER_CLOSE = True
# SESSION_COOKIE_SAMESITE = 'Lax'
# SESSION_IP_CHECK = True
# SESSION_MAX_AGE = 24 * 60 * 60

# CSRF_COOKIE_AGE = 60 * 60 * 4
# CSRF_COOKIE_SAMESITE = 'Lax'
# CSRF_USE_SESSIONS = False
# CSRF_FAILURE_VIEW = 'apps.core.error_handlers.custom_csrf_failure_view'
# CSRF_TRUSTED_ORIGINS = [
#     'https://radiocity.ugapp.net', 'http://www.radiocity.ugapp.net',
#     'http://localhost:8000', 'http://127.0.0.1:8000'
# ]

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('POSTGRES_DB', 'radio_mentions'),
        'USER': os.environ.get('POSTGRES_USER', 'postgres'),
        'PASSWORD': os.environ.get('POSTGRES_PASSWORD', 'SecureP@ssw0rd2025!'),
        'HOST': os.environ.get('POSTGRES_HOST', 'db'),
        'PORT': os.environ.get('POSTGRES_PORT', '5432'),
        'OPTIONS': {
            'sslmode': 'prefer',
            'connect_timeout': 60,
        },
        'CONN_MAX_AGE': 600,
        'CONN_HEALTH_CHECKS': True,
        'ATOMIC_REQUESTS': True,
        'AUTOCOMMIT': True,
    }
}
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://redis:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'IGNORE_EXCEPTIONS': True,
        },
        'KEY_PREFIX': 'radiomention',
        'TIMEOUT': 300,
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://redis:6379/2'),
        'OPTIONS': {'CLIENT_CLASS': 'django_redis.client.DefaultClient'},
        'KEY_PREFIX': 'session',
        'TIMEOUT': 86400,
    },
    'templates': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://redis:6379/3'),
        'OPTIONS': {'CLIENT_CLASS': 'django_redis.client.DefaultClient'},
        'KEY_PREFIX': 'template',
        'TIMEOUT': 3600,
    }
}

SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'sessions'
SESSION_COOKIE_AGE = 60 * 60 * 24 * 7
SESSION_SAVE_EVERY_REQUEST = False
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

# =============================================================================
# THIRD PARTY INTEGRATIONS
# =============================================================================
# Sentry - DISABLED
# try:
#     import sentry_sdk
#     from sentry_sdk.integrations.django import DjangoIntegration
#     from sentry_sdk.integrations.redis import RedisIntegration
#     from sentry_sdk.integrations.celery import CeleryIntegration

#     sentry_sdk.init(
#         dsn=os.environ.get('SENTRY_DSN', ''),
#         integrations=[
#             DjangoIntegration(middleware_spans=True),
#             RedisIntegration(),
#             CeleryIntegration(),
#         ],
#         traces_sample_rate=0.1,
#         sample_rate=1.0,
#         send_default_pii=True,
#         environment="production",
#         attach_stacktrace=True,
#     )
# except ImportError:
#     print(" sentry-sdk not installed")

print("ℹ Sentry SDK is disabled")

# django-csp
try:
    import csp
    if 'csp' not in INSTALLED_APPS:
        INSTALLED_APPS += ['csp']
    if 'csp.middleware.CSPMiddleware' not in MIDDLEWARE:
        MIDDLEWARE.insert(0, 'csp.middleware.CSPMiddleware')
    CSP_DEFAULT_SRC = ("'self'",)
    CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net")
    CSP_STYLE_SRC = ("'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://fonts.googleapis.com")
    CSP_FONT_SRC = ("'self'", "https://cdn.jsdelivr.net", "https://fonts.gstatic.com")
    CSP_IMG_SRC = ("'self'", "data:", "https:")
    CSP_CONNECT_SRC = ("'self'",)
except ImportError:
    print(" django-csp not installed")

# django-health-check
try:
    import health_check
    if 'health_check' not in INSTALLED_APPS:
        INSTALLED_APPS += [
            'health_check', 'health_check.db', 'health_check.cache',
            'health_check.storage', 'health_check.contrib.migrations'
        ]
except ImportError:
    print("django-health-check not installed")

# django-axes (disabled)
try:
    import axes
    print("django-axes installed but not enabled")
except ImportError:
    print(" django-axes not installed")

# =============================================================================
# MIDDLEWARE CONFIGURATION
# =============================================================================

MIDDLEWARE = [
    'apps.core.monitoring.PerformanceMiddleware',
    'apps.core.middleware.security_middleware.SecurityHeadersMiddleware',
    'apps.core.middleware.security_middleware.RateLimitMiddleware',
    'apps.core.middleware.security_middleware.SuspiciousActivityMiddleware',
    'apps.core.middleware.security_middleware.BruteForceProtectionMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'apps.core.middleware.cache_middleware.SmartCacheMiddleware',
    'apps.core.middleware.cache_middleware.ETagMiddleware',
    'apps.core.middleware.cache_middleware.CompressionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'apps.core.middleware.security_middleware.SessionSecurityMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'allauth.account.middleware.AccountMiddleware',
    'apps.organizations.middleware.OrganizationMiddleware',
    'apps.core.middleware.cache_middleware.DatabaseQueryCacheMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Note: CSP middleware is already inserted earlier if available

# =============================================================================
# STATIC AND MEDIA FILES
# =============================================================================

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'

STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = False
WHITENOISE_SKIP_COMPRESS_EXTENSIONS = [
    'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'ico',
    'zip', 'gz', 'tgz', 'bz2', 'tbz', 'xz', 'br',
    'woff', 'woff2', 'ttf', 'eot',
    'mp4', 'webm', 'mp3', 'wav'
]
WHITENOISE_MAX_AGE = 31536000
WHITENOISE_IMMUTABLE_FILE_TEST = lambda path, url: True

MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# =============================================================================
# FILE UPLOADS
# =============================================================================

FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000
FILE_UPLOAD_TEMP_DIR = '/tmp'
FILE_UPLOAD_PERMISSIONS = 0o644
FILE_UPLOAD_DIRECTORY_PERMISSIONS = 0o755

THUMBNAIL_PRESERVE_FORMAT = True
THUMBNAIL_QUALITY = 85

ALLOWED_UPLOAD_EXTENSIONS = [
    'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg',
    'pdf', 'doc', 'docx', 'xls', 'xlsx',
    'mp3', 'wav', 'mp4', 'webm'
]

CONTENT_TYPES = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
    'image/svg+xml': ['.svg'],
    'application/pdf': ['.pdf'],
    'audio/mpeg': ['.mp3'],
    'audio/wav': ['.wav'],
    'video/mp4': ['.mp4'],
    'video/webm': ['.webm'],
}

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_TIMEOUT = 30
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = "RadioMention <<EMAIL>>"
SERVER_EMAIL = DEFAULT_FROM_EMAIL

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {asctime} {message}',
            'style': '{',
        },
        'json': {
            'format': '{{"level": "{levelname}", "time": "{asctime}", "module": "{module}", "message": "{message}"}}',
            'style': '{',
        },
    },
    'filters': {
        'require_debug_false': {'()': 'django.utils.log.RequireDebugFalse'},
        'require_debug_true': {'()': 'django.utils.log.RequireDebugTrue'},
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
            'filters': ['require_debug_true'],
        },
        'console_prod': {
            'level': 'WARNING',
            'class': 'logging.StreamHandler',
            'formatter': 'json',
            'filters': ['require_debug_false'],
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'error.log'),
            'maxBytes': 1024*1024*10,
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'file_app': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'app.log'),
            'maxBytes': 1024*1024*10,
            'backupCount': 5,
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'console_prod', 'file_error'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'console_prod', 'file_error'],
            'level': 'INFO',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console', 'console_prod', 'file_app'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery': {
            'handlers': ['console', 'console_prod', 'file_app'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

CELERY_BROKER_URL = 'redis://redis:6379/0'
CELERY_RESULT_BACKEND = 'redis://redis:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Africa/Nairobi'
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 1800
CELERY_TASK_SOFT_TIME_LIMIT = 1500
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000

CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

CELERY_TASK_ROUTES = {
    'apps.core.tasks.*': {'queue': 'core'},
    'apps.mentions.tasks.*': {'queue': 'mentions'},
    'apps.reports.tasks.*': {'queue': 'reports'},
    'apps.activity_logs.tasks.*': {'queue': 'maintenance'},
}
CELERY_TASK_DEFAULT_QUEUE = 'default'

# =============================================================================
# TEMPLATE OPTIMIZATIONS
# =============================================================================

TEMPLATES[0]['APP_DIRS'] = False
TEMPLATES[0]['OPTIONS']['loaders'] = [
    ('django.template.loaders.cached.Loader', [
        'django.template.loaders.filesystem.Loader',
        'django.template.loaders.app_directories.Loader',
    ]),
]
TEMPLATES[0]['OPTIONS']['context_processors'] = [
    'django.template.context_processors.debug',
    'django.template.context_processors.request',
    'django.contrib.auth.context_processors.auth',
    'django.contrib.messages.context_processors.messages',
    'apps.core.context_processors.sidebar_context',
    'apps.core.context_processors.organization_context',
    'apps.core.context_processors.permissions_context',
]
TEMPLATES[0]['OPTIONS']['debug'] = False

# =============================================================================
# DIGITALOCEAN SPACES (OPTIONAL)
# =============================================================================

# Uncomment if needed
# DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
# AWS_ACCESS_KEY_ID = os.environ.get('DO_SPACES_ACCESS_KEY_ID')
# AWS_SECRET_ACCESS_KEY = os.environ.get('DO_SPACES_SECRET_ACCESS_KEY')
# AWS_STORAGE_BUCKET_NAME = os.environ.get('DO_SPACES_BUCKET_NAME')
# AWS_S3_ENDPOINT_URL = os.environ.get('DO_SPACES_ENDPOINT_URL')
# AWS_S3_REGION_NAME = os.environ.get('DO_SPACES_REGION', 'nyc3')
# AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.{AWS_S3_REGION_NAME}.digitaloceanspaces.com'
# AWS_DEFAULT_ACL = 'public-read'
# AWS_S3_OBJECT_PARAMETERS = {'CacheControl': 'max-age=86400'}

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================

ADMIN_URL = 'admin/'
