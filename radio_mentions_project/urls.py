"""
URL configuration for radio_mentions_project project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from django.db import connection
from django.utils import timezone
from apps.core.views import locked_out
from apps.core.error_handlers import health_check_view

def health_check(request):
    """Health check endpoint for Docker and load balancers"""
    try:
        # Check database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")

        return JsonResponse({
            'status': 'healthy',
            'database': 'connected',
            'timestamp': settings.USE_TZ and timezone.now().isoformat() or 'N/A'
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': settings.USE_TZ and timezone.now().isoformat() or 'N/A'
        }, status=503)

urlpatterns = [
    # Health check endpoint (must be first for Docker health checks)
    path('health/', health_check_view, name='health_check'),

    # Account lockout page (for django-axes)
    path('locked-out/', locked_out, name='locked_out'),

    path('admin/', admin.site.urls),

    # Django Allauth URLs (prioritized for authentication)
    path('accounts/', include('allauth.urls')),

    # Organization-specific URLs (includes signup wizard)
    path('organizations/', include('apps.organizations.urls')),

    # Main app URLs
    path('', include('apps.core.urls')),
    path('mentions/', include('apps.mentions.urls')),
    path('shows/', include('apps.shows.urls')),
    path('reports/', include('apps.reports.urls')),
    path('template-designer/', include('apps.template_designer.urls')),
    path('news-reader/', include('apps.news_reader.urls')),

    # User management (admin only - keep for user CRUD operations)
    path('users/', include('apps.authentication.urls')),

    path('settings/', include('apps.settings.urls')),
    path('activity/', include('apps.activity_logs.urls')),
    path('api/', include('rest_framework.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Custom error handlers
handler404 = 'apps.core.error_handlers.custom_404_view'
handler500 = 'apps.core.error_handlers.custom_500_view'
handler403 = 'apps.core.error_handlers.custom_403_view'
handler400 = 'apps.core.error_handlers.custom_400_view'
