# Production settings for Digital Ocean deployment
import os
import logging
from .settings import *

# Override development settings for production
# Use environment variable for secret key in production
SECRET_KEY = os.environ.get('SECRET_KEY', '2fs=w8ec-@2=2ix3g(n8=tq-1a7(1at#y3lf@3$y9k^b7ujkwa')

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Security - disable DEBUG in production
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

# Allowed hosts - restrict to known domains
ALLOWED_HOSTS = [
    'radiocity.ugapp.net',
    'www.radiocity.ugapp.net',
    '*************',  # Droplet IP
    # Always allow localhost for Docker containers
    'localhost',
    'localhost:8000',
    'localhost:8080',
    '127.0.0.1',
    '127.0.0.1:8000',
    '127.0.0.1:8080',
    '0.0.0.0',
]

# Security headers and HTTPS settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# SSL/HTTPS settings for production
USE_TLS = os.environ.get('USE_TLS', 'True').lower() == 'true'
if USE_TLS and not DEBUG:
    SECURE_SSL_REDIRECT = True
    SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
    SESSION_COOKIE_SECURE = True
    CSRF_COOKIE_SECURE = True
    CSRF_COOKIE_HTTPONLY = True
    SESSION_COOKIE_HTTPONLY = True
else:
    SECURE_SSL_REDIRECT = False
    SESSION_COOKIE_SECURE = False
    CSRF_COOKIE_SECURE = False

# Additional security settings
SECURE_CROSS_ORIGIN_OPENER_POLICY = 'same-origin'
SECURE_REDIRECT_EXEMPT = []  # Paths that don't require HTTPS redirect

# Session security
SESSION_COOKIE_AGE = 60 * 60 * 4  # 4 hours
SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_IP_CHECK = True  # Check IP consistency
SESSION_MAX_AGE = 24 * 60 * 60  # 24 hours max session age

# CSRF security
CSRF_COOKIE_AGE = 60 * 60 * 4  # 4 hours
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_USE_SESSIONS = False
CSRF_FAILURE_VIEW = 'apps.core.error_handlers.custom_csrf_failure_view'

# CSRF trusted origins
CSRF_TRUSTED_ORIGINS = [
    'https://radiocity.ugapp.net',
    'https://www.radiocity.ugapp.net',
    'https://mention.ugapp.net',
    'https://www.mention.ugapp.net',
    'http://localhost:8000',
    'http://localhost:8080',
    'http://localhost',
    'http://127.0.0.1:8000',
    'http://127.0.0.1:8080',
]

# Additional CSRF settings for local development
if os.environ.get('DEBUG', 'False').lower() == 'true':
    CSRF_COOKIE_DOMAIN = None
    CSRF_COOKIE_SAMESITE = 'Lax'
    CSRF_USE_SESSIONS = False
    # Temporarily disable CSRF for testing
    CSRF_COOKIE_SECURE = False

    # Remove CSRF middleware temporarily for testing
    MIDDLEWARE = [mw for mw in MIDDLEWARE if 'CsrfViewMiddleware' not in mw]

# Security headers
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'

# SSL/HTTPS settings
# Allow disabling SSL redirect for local testing
SECURE_SSL_REDIRECT = os.environ.get('DISABLE_SSL_REDIRECT', 'False').lower() != 'true'
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SESSION_COOKIE_SECURE = os.environ.get('DISABLE_SSL_REDIRECT', 'False').lower() != 'true'
CSRF_COOKIE_SECURE = os.environ.get('DISABLE_SSL_REDIRECT', 'False').lower() != 'true'

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL for production with optimized settings
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('POSTGRES_DB', 'radio_mentions'),
        'USER': os.environ.get('POSTGRES_USER', 'postgres'),
        'PASSWORD': os.environ.get('POSTGRES_PASSWORD', 'SecureP@ssw0rd2025!'),
        'HOST': os.environ.get('POSTGRES_HOST', 'db'),
        'PORT': os.environ.get('POSTGRES_PORT', '5432'),
        'OPTIONS': {
            'sslmode': 'prefer',
            'connect_timeout': 60,
            # Removed default_transaction_isolation to avoid Postgres errors
        },
        'CONN_MAX_AGE': 600,  # Connection pooling - 10 minutes
        'CONN_HEALTH_CHECKS': True,  # Enable connection health checks
        'ATOMIC_REQUESTS': True,  # Wrap each request in a transaction
        'AUTOCOMMIT': True,
    }
}

# Database query optimization
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Query optimization settings
DATABASE_QUERY_TIMEOUT = 30  # 30 seconds
DATABASE_POOL_SIZE = 20
DATABASE_MAX_OVERFLOW = 30

# Enable query logging in development
if DEBUG:
    LOGGING['loggers']['django.db.backends'] = {
        'handlers': ['console'],
        'level': 'DEBUG',
        'propagate': False,
    }

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================

# Redis cache for production with optimized settings
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://redis:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 100,  # Increased for production
                'retry_on_timeout': True,
                'socket_keepalive': True,
                'socket_keepalive_options': {},
            },
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
            'IGNORE_EXCEPTIONS': True,
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
        },
        'KEY_PREFIX': 'radiomention',
        'TIMEOUT': 300,  # 5 minutes default
        'VERSION': 1,
    },
    # Separate cache for sessions
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://redis:6379/2'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
            },
        },
        'KEY_PREFIX': 'session',
        'TIMEOUT': 60 * 60 * 24,  # 24 hours for sessions
    },
    # Cache for template fragments
    'templates': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.environ.get('REDIS_URL', 'redis://redis:6379/3'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 30,
                'retry_on_timeout': True,
            },
        },
        'KEY_PREFIX': 'template',
        'TIMEOUT': 60 * 60,  # 1 hour for templates
    }
}

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'sessions'
SESSION_COOKIE_AGE = 60 * 60 * 24 * 7  # 1 week
SESSION_SAVE_EVERY_REQUEST = False  # Only save when modified
SESSION_EXPIRE_AT_BROWSER_CLOSE = False

# Cache middleware settings
CACHE_MIDDLEWARE_SECONDS = 300  # 5 minutes
CACHE_MIDDLEWARE_KEY_PREFIX = 'radiomention'
CACHE_AUTHENTICATED_REQUESTS = False  # Don't cache authenticated requests by default
CACHE_API_RESPONSES = True  # Cache API responses
CACHE_MAX_RESPONSE_SIZE = 1024 * 1024  # 1MB max response size to cache

# Template caching
TEMPLATE_CACHE_TIMEOUT = 3600  # 1 hour

# =============================================================================
# THIRD PARTY INTEGRATIONS
# =============================================================================

# Sentry SDK for error monitoring
try:
    import sentry_sdk
    from sentry_sdk.integrations.django import DjangoIntegration
    from sentry_sdk.integrations.redis import RedisIntegration
    from sentry_sdk.integrations.celery import CeleryIntegration

    SENTRY_DSN = os.environ.get('SENTRY_DSN', 'https://<EMAIL>/****************')
    SENTRY_RELEASE = os.environ.get('SENTRY_RELEASE', 'v1.0.0')

    sentry_sdk.init(
        dsn=SENTRY_DSN,
        integrations=[
            DjangoIntegration(
                transaction_style='url',
                middleware_spans=True,
                signals_spans=True,
                cache_spans=True,
            ),
            RedisIntegration(),
            CeleryIntegration(monitor_beat_tasks=True),
        ],
        # Performance monitoring
        traces_sample_rate=0.1,  # 10% of transactions for performance monitoring

        # Error sampling
        sample_rate=1.0,  # 100% of errors

        # Add data like request headers and IP for users
        send_default_pii=True,

        # Environment and release tracking
        environment="production",
        release=SENTRY_RELEASE,

        # Additional options
        attach_stacktrace=True,
        max_breadcrumbs=50,

        # Filter out health check requests
        before_send=lambda event, hint: None if '/health/' in event.get('request', {}).get('url', '') else event,
    )

    print("✅ Sentry error monitoring enabled")
except ImportError:
    print("⚠️  Sentry SDK not installed. Error monitoring disabled.")

# Try to set up django-csp
try:
    import csp
    if 'csp' not in INSTALLED_APPS:
        INSTALLED_APPS += ['csp']

    MIDDLEWARE = ['csp.middleware.CSPMiddleware'] + MIDDLEWARE
    
    # Content Security Policy
    CSP_DEFAULT_SRC = ("'self'",)
    CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net")
    CSP_STYLE_SRC = ("'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://fonts.googleapis.com")
    CSP_FONT_SRC = ("'self'", "https://cdn.jsdelivr.net", "https://fonts.gstatic.com")
    CSP_IMG_SRC = ("'self'", "data:", "https:")
    CSP_CONNECT_SRC = ("'self'",)
    
    print("✅ Content Security Policy enabled")
except ImportError:
    print("⚠️  django-csp not installed. Content Security Policy disabled.")

# Try to set up django-axes (DISABLED for easier development)
try:
    import axes
    # Commenting out axes configuration to disable lockout functionality
    # if 'axes' not in INSTALLED_APPS:
    #     INSTALLED_APPS += ['axes']
    #
    # MIDDLEWARE += ['axes.middleware.AxesMiddleware']
    #
    # AUTHENTICATION_BACKENDS = [
    #     'axes.backends.AxesStandaloneBackend',
    #     'django.contrib.auth.backends.ModelBackend',
    # ]
    #
    # AXES_FAILURE_LIMIT = 5  # Number of login attempts before blocking
    # AXES_COOLOFF_TIME = 1  # Hours before an IP can try again
    # AXES_LOCKOUT_URL = '/locked-out/'  # Optional redirect URL
    # AXES_ENABLE_ADMIN = True  # Enable axes in the admin interface

    print("✅ django-axes is installed but disabled for development")
except ImportError:
    print("⚠️  django-axes not installed. Enhanced login security disabled.")

# Try to set up health checks
try:
    import health_check
    if 'health_check' not in INSTALLED_APPS:
        INSTALLED_APPS += [
            'health_check',
            'health_check.db',
            'health_check.cache',
            'health_check.storage',
            'health_check.contrib.migrations',
        ]
    print("✅ Health checks enabled")
except ImportError:
    print("⚠️  django-health-check not installed. Health checks will be limited.")

# =============================================================================
# STATIC AND MEDIA FILES
# =============================================================================

# Static files with WhiteNoise optimization
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATIC_URL = '/static/'

# Static files finders optimization
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# WhiteNoise configuration for optimal performance
WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = False  # Disable in production
WHITENOISE_SKIP_COMPRESS_EXTENSIONS = [
    'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'ico',
    'zip', 'gz', 'tgz', 'bz2', 'tbz', 'xz', 'br',
    'woff', 'woff2', 'ttf', 'eot',
    'mp4', 'webm', 'mp3', 'wav'
]
WHITENOISE_MAX_AGE = 31536000  # 1 year for static files
WHITENOISE_IMMUTABLE_FILE_TEST = lambda path, url: True  # All files are immutable

# Media files configuration
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
MEDIA_URL = '/media/'

# File upload settings
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
FILE_UPLOAD_TEMP_DIR = '/tmp'
FILE_UPLOAD_PERMISSIONS = 0o644
FILE_UPLOAD_DIRECTORY_PERMISSIONS = 0o755

# Image optimization settings
THUMBNAIL_PRESERVE_FORMAT = True
THUMBNAIL_QUALITY = 85

# Security for file uploads
ALLOWED_UPLOAD_EXTENSIONS = [
    'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg',
    'pdf', 'doc', 'docx', 'xls', 'xlsx',
    'mp3', 'wav', 'mp4', 'webm'
]

# Content type validation
CONTENT_TYPES = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
    'image/svg+xml': ['.svg'],
    'application/pdf': ['.pdf'],
    'audio/mpeg': ['.mp3'],
    'audio/wav': ['.wav'],
    'video/mp4': ['.mp4'],
    'video/webm': ['.webm'],
}

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================

# Email backend - Console for production (change as needed)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = ''
EMAIL_HOST_PASSWORD = ''
DEFAULT_FROM_EMAIL = "RadioMention <<EMAIL>>"
SERVER_EMAIL = DEFAULT_FROM_EMAIL

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {asctime} {message}',
            'style': '{',
        },
        'json': {
            'format': '{{"level": "{levelname}", "time": "{asctime}", "module": "{module}", "message": "{message}"}}',
            'style': '{',
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
            'filters': ['require_debug_true'],
        },
        'console_prod': {
            'level': 'WARNING',
            'class': 'logging.StreamHandler',
            'formatter': 'json',
            'filters': ['require_debug_false'],
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'error.log'),
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'file_app': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'app.log'),
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
    },
    'root': {
        'handlers': ['console', 'console_prod', 'file_error'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'console_prod', 'file_error'],
            'level': 'INFO',
            'propagate': False,
        },
        'django.request': {
            'handlers': ['console', 'console_prod', 'file_error'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['console', 'console_prod', 'file_error'],
            'level': 'WARNING',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['file_error'],
            'level': 'ERROR',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console', 'console_prod', 'file_app'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery': {
            'handlers': ['console', 'console_prod', 'file_app'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# =============================================================================
# CELERY CONFIGURATION
# =============================================================================

# Celery settings
CELERY_BROKER_URL = 'redis://redis:6379/0'
CELERY_RESULT_BACKEND = 'redis://redis:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 30 * 60  # 30 minutes
CELERY_TASK_SOFT_TIME_LIMIT = 25 * 60  # 25 minutes
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000

# Celery Beat settings
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'
CELERY_BEAT_SCHEDULE_FILENAME = 'celerybeat-schedule'

# Task routing
CELERY_TASK_ROUTES = {
    'apps.core.tasks.*': {'queue': 'core'},
    'apps.mentions.tasks.*': {'queue': 'mentions'},
    'apps.reports.tasks.*': {'queue': 'reports'},
    'apps.activity_logs.tasks.*': {'queue': 'maintenance'},
}

# Default queue configuration
CELERY_TASK_DEFAULT_QUEUE = 'default'
CELERY_TASK_QUEUES = {
    'default': {
        'exchange': 'default',
        'routing_key': 'default',
    },
    'core': {
        'exchange': 'core',
        'routing_key': 'core',
    },
    'mentions': {
        'exchange': 'mentions',
        'routing_key': 'mentions',
    },
    'reports': {
        'exchange': 'reports',
        'routing_key': 'reports',
    },
    'maintenance': {
        'exchange': 'maintenance',
        'routing_key': 'maintenance',
    },
}

# =============================================================================
# MONITORING AND ERROR TRACKING
# =============================================================================
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.redis import RedisIntegration
from sentry_sdk.integrations.celery import CeleryIntegration

SENTRY_DSN = os.environ.get('SENTRY_DSN', 'https://<EMAIL>/****************')
SENTRY_RELEASE = os.environ.get('SENTRY_RELEASE', 'v1.0.0')

sentry_sdk.init(
    dsn=SENTRY_DSN,
    integrations=[
        DjangoIntegration(
            transaction_style='url',
            middleware_spans=True,
            signals_spans=True,
            cache_spans=True,
        ),
        RedisIntegration(),
        CeleryIntegration(monitor_beat_tasks=True),
    ],
    traces_sample_rate=0.1,  # 10% of transactions for performance monitoring
    sample_rate=1.0,  # 100% of errors
    send_default_pii=True,
    environment="production",
    release=SENTRY_RELEASE,
    attach_stacktrace=True,
    max_breadcrumbs=50,
    before_send=lambda event, hint: None if '/health/' in event.get('request', {}).get('url', '') else event,
)

# =============================================================================
# PERFORMANCE OPTIMIZATIONS
# =============================================================================

# Database optimizations
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Middleware optimization - order matters for performance
MIDDLEWARE = [
    'apps.core.monitoring.PerformanceMiddleware',  # Performance monitoring first
    'apps.core.middleware.security_middleware.SecurityHeadersMiddleware',  # Security headers
    'apps.core.middleware.security_middleware.RateLimitMiddleware',  # Global rate limiting
    'apps.core.middleware.security_middleware.SuspiciousActivityMiddleware',  # Suspicious activity detection
    'apps.core.middleware.security_middleware.BruteForceProtectionMiddleware',  # Brute force protection
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',  # Static files
    'apps.core.middleware.cache_middleware.SmartCacheMiddleware',  # Page caching
    'apps.core.middleware.cache_middleware.ETagMiddleware',  # ETag support
    'apps.core.middleware.cache_middleware.CompressionMiddleware',  # Compression headers
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'apps.core.middleware.security_middleware.SessionSecurityMiddleware',  # Session security - MUST be after AuthenticationMiddleware
    'django.contrib.messages.middleware.MessageMiddleware',
    'allauth.account.middleware.AccountMiddleware',
    'apps.organizations.middleware.OrganizationMiddleware',
    'apps.core.middleware.cache_middleware.DatabaseQueryCacheMiddleware',  # Query caching
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Performance monitoring settings
SLOW_REQUEST_THRESHOLD = 2.0  # Log requests slower than 2 seconds
PERFORMANCE_MONITORING_ENABLED = True

# CSP middleware is already added above in the main CSP configuration section

# File upload optimization
FILE_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 5242880  # 5MB
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000

# Email timeout settings
EMAIL_TIMEOUT = 30

# Template caching - disable APP_DIRS when using custom loaders
TEMPLATES[0]['APP_DIRS'] = False
TEMPLATES[0]['OPTIONS']['loaders'] = [
    ('django.template.loaders.cached.Loader', [
        'django.template.loaders.filesystem.Loader',
        'django.template.loaders.app_directories.Loader',
    ]),
]

# Template optimization
TEMPLATES[0]['OPTIONS']['context_processors'] = [
    'django.template.context_processors.debug',
    'django.template.context_processors.request',
    'django.contrib.auth.context_processors.auth',
    'django.contrib.messages.context_processors.messages',
    'apps.core.context_processors.sidebar_context',
    'apps.core.context_processors.organization_context',
    'apps.core.context_processors.permissions_context',
]

# Disable template debugging in production
TEMPLATES[0]['OPTIONS']['debug'] = False

# =============================================================================
# DIGITAL OCEAN SPACES (OPTIONAL)
# =============================================================================

# Uncomment and configure if using Digital Ocean Spaces for media files
# DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
# AWS_ACCESS_KEY_ID = os.environ.get('DO_SPACES_ACCESS_KEY_ID')
# AWS_SECRET_ACCESS_KEY = os.environ.get('DO_SPACES_SECRET_ACCESS_KEY')
# AWS_STORAGE_BUCKET_NAME = os.environ.get('DO_SPACES_BUCKET_NAME')
# AWS_S3_ENDPOINT_URL = os.environ.get('DO_SPACES_ENDPOINT_URL')
# AWS_S3_REGION_NAME = os.environ.get('DO_SPACES_REGION', 'nyc3')
# AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.{AWS_S3_REGION_NAME}.digitaloceanspaces.com'
# AWS_DEFAULT_ACL = 'public-read'
# AWS_S3_OBJECT_PARAMETERS = {
#     'CacheControl': 'max-age=86400',
# }

# =============================================================================
# HEALTH CHECKS
# =============================================================================

# Health check apps are already configured above in the third party integrations section

# =============================================================================
# RATE LIMITING
# =============================================================================

# Rate limiting
# RATELIMIT_ENABLE = True
# RATELIMIT_USE_CACHE = 'default'

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================

# Admin security
ADMIN_URL = 'admin/'
