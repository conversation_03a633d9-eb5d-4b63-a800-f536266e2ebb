"""
WSGI config for radio_mentions_project project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

# Use production settings by default, but allow override for development
# This ensures Docker containers use production settings correctly
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'radio_mentions_project.settings_production')

application = get_wsgi_application()
