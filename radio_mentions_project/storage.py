"""
Custom storage classes for production optimization
"""
import os
from django.core.files.storage import FileSystemStorage
from django.conf import settings
from django.utils.deconstruct import deconstructible


@deconstructible
class OptimizedMediaStorage(FileSystemStorage):
    """
    Optimized media storage with better security and performance
    """
    
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('location', settings.MEDIA_ROOT)
        kwargs.setdefault('base_url', settings.MEDIA_URL)
        super().__init__(*args, **kwargs)
    
    def get_valid_name(self, name):
        """
        Return a filename that's suitable for use on the target storage system.
        """
        # Remove any path components and sanitize filename
        name = os.path.basename(name)
        # Replace spaces with underscores
        name = name.replace(' ', '_')
        # Remove any non-alphanumeric characters except dots, dashes, and underscores
        import re
        name = re.sub(r'[^\w\-_\.]', '', name)
        return super().get_valid_name(name)
    
    def get_available_name(self, name, max_length=None):
        """
        Return a filename that's free on the target storage system.
        """
        # Add timestamp to avoid conflicts
        import time
        name_parts = name.rsplit('.', 1)
        if len(name_parts) == 2:
            name_without_ext, ext = name_parts
            timestamp = str(int(time.time()))
            name = f"{name_without_ext}_{timestamp}.{ext}"
        else:
            timestamp = str(int(time.time()))
            name = f"{name}_{timestamp}"
        
        return super().get_available_name(name, max_length)


@deconstructible
class SecureMediaStorage(OptimizedMediaStorage):
    """
    Secure media storage that validates file types and sizes
    """
    
    def save(self, name, content, max_length=None):
        """
        Save file with security validation
        """
        # Validate file size
        if hasattr(content, 'size') and content.size > settings.FILE_UPLOAD_MAX_MEMORY_SIZE:
            raise ValueError(f"File too large. Maximum size is {settings.FILE_UPLOAD_MAX_MEMORY_SIZE} bytes")
        
        # Validate file extension
        if hasattr(settings, 'ALLOWED_UPLOAD_EXTENSIONS'):
            ext = os.path.splitext(name)[1].lower().lstrip('.')
            if ext not in settings.ALLOWED_UPLOAD_EXTENSIONS:
                raise ValueError(f"File type '{ext}' not allowed")
        
        return super().save(name, content, max_length)


# Custom storage for different media types
@deconstructible
class ImageStorage(SecureMediaStorage):
    """Storage specifically for images with additional validation"""
    
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('location', os.path.join(settings.MEDIA_ROOT, 'images'))
        kwargs.setdefault('base_url', f"{settings.MEDIA_URL}images/")
        super().__init__(*args, **kwargs)
    
    def save(self, name, content, max_length=None):
        # Additional image validation
        allowed_image_types = ['jpg', 'jpeg', 'png', 'gif', 'webp']
        ext = os.path.splitext(name)[1].lower().lstrip('.')
        if ext not in allowed_image_types:
            raise ValueError(f"Only image files are allowed: {', '.join(allowed_image_types)}")
        
        return super().save(name, content, max_length)


@deconstructible
class DocumentStorage(SecureMediaStorage):
    """Storage specifically for documents"""
    
    def __init__(self, *args, **kwargs):
        kwargs.setdefault('location', os.path.join(settings.MEDIA_ROOT, 'documents'))
        kwargs.setdefault('base_url', f"{settings.MEDIA_URL}documents/")
        super().__init__(*args, **kwargs)
    
    def save(self, name, content, max_length=None):
        # Additional document validation
        allowed_doc_types = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt']
        ext = os.path.splitext(name)[1].lower().lstrip('.')
        if ext not in allowed_doc_types:
            raise ValueError(f"Only document files are allowed: {', '.join(allowed_doc_types)}")
        
        return super().save(name, content, max_length)
