# Gunicorn configuration for production

# Server socket
bind = "0.0.0.0:8000"
backlog = 2048

# Worker processes - optimized for resource usage
workers = 2  # Reduced from 4 to save memory
worker_class = "sync"
worker_connections = 500  # Reduced from 1000
timeout = 120  # Increased for long-running requests
keepalive = 2

# Memory management
max_requests = 500  # Reduced to restart workers more frequently
max_requests_jitter = 50
preload_app = True  # Preload application for memory efficiency

# Logging
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = "radiomention"

# Server mechanics
daemon = False
pidfile = "logs/gunicorn.pid"
user = None
group = None
tmp_upload_dir = None

# SSL (uncomment if using HTTPS)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"

# Environment variables
raw_env = [
    'DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production',
]
