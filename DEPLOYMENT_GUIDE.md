# 🚀 Enhanced AppRadio Deployment Guide

## Overview

The enhanced `update-app.sh` script provides a production-ready deployment solution with automated migrations, backups, and rollback capabilities.

## Features

✅ **Automated Database Migrations** - Specify which Django apps need migrations  
✅ **Zero Data Loss** - Automatic database backups before any changes  
✅ **Custom Management Commands** - Run Django management commands during deployment  
✅ **Rollback Support** - Quick rollback to previous version if issues occur  
✅ **Safety Checks** - Comprehensive verification before and after deployment  
✅ **SSL Support** - Automatic detection and handling of SSL certificates  
✅ **Dry Run Mode** - Preview changes without executing them  

## Usage Examples

### Basic Deployment with Migrations
```bash
# Deploy with migrations for core and mentions apps
./update-app.sh --apps "core,mentions"
```

### Full Deployment with Custom Commands
```bash
# Deploy with migrations and run management commands
./update-app.sh --apps "core,mentions" --commands "populate_recurring_history,collectstatic"
```

### Preview Changes (Dry Run)
```bash
# See what would be done without executing
./update-app.sh --dry-run --apps "core,mentions" --commands "populate_recurring_history"
```

### Quick Deployment (No Rebuild)
```bash
# Deploy without rebuilding Docker images
./update-app.sh --skip-build --apps "mentions"
```

### Emergency Rollback
```bash
# Rollback to previous version
./update-app.sh --rollback
```

### Deployment Without Backup (Not Recommended)
```bash
# Skip backup (only use if you have external backups)
./update-app.sh --skip-backup --apps "core"
```

## Command Line Options

| Option | Description | Example |
|--------|-------------|---------|
| `--apps "app1,app2"` | Django apps to migrate | `--apps "core,mentions"` |
| `--commands "cmd1,cmd2"` | Management commands to run | `--commands "populate_recurring_history"` |
| `--skip-backup` | Skip database backup (NOT RECOMMENDED) | `--skip-backup` |
| `--skip-build` | Skip Docker image rebuild | `--skip-build` |
| `--rollback` | Rollback to previous version | `--rollback` |
| `--dry-run` | Show what would be done without executing | `--dry-run` |
| `--help` | Show help message | `--help` |

## Deployment Process

The script follows this safe deployment process:

1. **Prerequisites Check** - Verify Docker, compose files, and directories
2. **Configuration Detection** - Detect SSL certificates and compose setup
3. **Database Backup** - Create timestamped database backup
4. **Image Tagging** - Tag current images for potential rollback
5. **Service Deployment** - Build and deploy new services
6. **Database Migrations** - Run makemigrations and migrate for specified apps
7. **Static Files** - Collect static files
8. **Management Commands** - Execute custom Django commands
9. **Health Verification** - Verify all services are running correctly
10. **Success Confirmation** - Display deployment summary

## Safety Features

### Automatic Backups
- Database backups created before any changes
- Backups compressed and timestamped
- Old backups automatically cleaned up (7-day retention)
- Backup location stored for rollback reference

### Rollback Capability
- Current Docker images tagged before deployment
- Quick rollback command available
- Automatic verification after rollback

### Health Checks
- Service status verification
- Django deployment checks
- Timeout-based health monitoring

## File Structure

```
your-project/
├── update-app.sh              # Enhanced deployment script
├── docker-compose.yml         # Main compose file
├── docker-compose.ssl.yml     # SSL compose override
├── backups/                   # Database backups directory
│   ├── backup_20250107_143022.sql.gz
│   └── latest_backup.txt
└── logs/                      # Application logs
```

## Troubleshooting

### Deployment Fails
1. Check the error message in the script output
2. Verify all prerequisites are met
3. Check Docker service status: `docker ps`
4. Review application logs: `docker compose logs`

### Rollback if Needed
```bash
./update-app.sh --rollback
```

### Manual Recovery
If automatic rollback fails:
```bash
# Stop services
docker compose down

# Restore from backup
docker compose exec -T db psql -U postgres radio_mentions < backups/backup_TIMESTAMP.sql

# Start services with previous images
docker compose up -d
```

## Best Practices

1. **Always test in staging first** - Never deploy untested changes to production
2. **Use dry run mode** - Preview changes before executing
3. **Monitor after deployment** - Check logs and application functionality
4. **Keep backups** - Don't skip backups unless you have external backup systems
5. **Plan rollback** - Know how to rollback quickly if issues occur

## Migration Workflow

For your specific updates with recurring mentions:

```bash
# Deploy the recurring mentions status updates
./update-app.sh --apps "core,mentions" --commands "populate_recurring_history"
```

This will:
1. Create a database backup
2. Run `makemigrations core` and `migrate core`
3. Run `makemigrations mentions` and `migrate mentions`
4. Execute `populate_recurring_history` management command
5. Verify everything is working correctly

## Support

If you encounter issues:
1. Check the deployment logs
2. Use `--dry-run` to preview changes
3. Use `--rollback` if deployment fails
4. Review this guide for troubleshooting steps
