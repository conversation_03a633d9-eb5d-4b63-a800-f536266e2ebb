#!/bin/bash

# <PERSON><PERSON>t to update the app without destroying the database
# This script ensures SSL certificates are preserved and uses the correct compose files

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Updating AppRadio application...${NC}"

# Check if SSL certificates exist
if [ -d "docker/certbot/conf/live" ] && [ "$(ls -A docker/certbot/conf/live 2>/dev/null)" ]; then
    echo -e "${GREEN}✅ SSL certificates found - using SSL configuration${NC}"
    
    # Get the domain name from existing certificates
    DOMAIN_NAME=$(ls docker/certbot/conf/live/ | head -n 1)
    
    if [ -z "$DOMAIN_NAME" ]; then
        echo -e "${RED}❌ Could not determine domain name from certificates${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}📋 Domain: $DOMAIN_NAME${NC}"
    
    # Export environment variables
    export NGINX_SERVER_NAME=$DOMAIN_NAME
    export DOMAIN_NAME=$DOMAIN_NAME
    
    # Update with SSL configuration
    echo -e "${YELLOW}🔄 Updating services with SSL enabled...${NC}"
    DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml up -d --build
    
else
    echo -e "${YELLOW}⚠️  No SSL certificates found - using HTTP-only configuration${NC}"
    echo -e "${YELLOW}💡 Run './enable-https.sh your-domain.com' to enable HTTPS${NC}"
    
    # Check if HTTP-only config exists, if not create it
    if [ ! -f "docker/nginx/default.conf.http-only" ]; then
        echo -e "${RED}❌ HTTP-only nginx configuration not found${NC}"
        exit 1
    fi
    
    # Backup current config and use HTTP-only
    if [ -f "docker/nginx/default.conf" ]; then
        cp docker/nginx/default.conf docker/nginx/default.conf.backup
    fi
    cp docker/nginx/default.conf.http-only docker/nginx/default.conf
    
    # Update with HTTP-only configuration
    echo -e "${YELLOW}🔄 Updating services with HTTP-only configuration...${NC}"
    docker compose up -d --build
    
    # Restore original config
    if [ -f "docker/nginx/default.conf.backup" ]; then
        mv docker/nginx/default.conf.backup docker/nginx/default.conf
    fi
fi

# Wait for services to start
echo -e "${YELLOW}⏳ Waiting for services to start...${NC}"
sleep 10

# Check service status
echo -e "${GREEN}📊 Service Status:${NC}"
if [ -d "docker/certbot/conf/live" ] && [ "$(ls -A docker/certbot/conf/live 2>/dev/null)" ]; then
    DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml ps
else
    docker compose ps
fi

echo -e "${GREEN}✅ Update completed!${NC}"

# Show access information
if [ -d "docker/certbot/conf/live" ] && [ "$(ls -A docker/certbot/conf/live 2>/dev/null)" ]; then
    echo -e "${GREEN}🌐 Your app is available at: https://$DOMAIN_NAME${NC}"
else
    echo -e "${YELLOW}🌐 Your app is available at: http://localhost (or your server IP)${NC}"
fi
