Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3
psycopg2-binary==2.9.7  # PostgreSQL adapter
redis==5.0.1
celery==5.3.4
django-celery-beat==2.5.0
pillow==10.0.1
openpyxl==3.1.2
django-crispy-forms==2.1
crispy-bootstrap5==0.7
django-extensions==3.2.3
django-allauth==0.57.0
requests==2.32.3  # HTTP library for API calls and testing
django-csp==3.8.0  # Content Security Policy

# PDF Generation
fpdf2==2.8.3  # Lightweight PDF generation library
fonttools==4.58.4  # Font handling for PDF generation

# Configuration and Environment
python-decouple==3.8  # Environment configuration management

# Task Scheduling and Cron
python-crontab==3.2.0  # Cron job management
cron-descriptor==1.4.5  # Human-readable cron descriptions

# Data Processing and Validation
jsonschema==4.24.0  # JSON schema validation
PyYAML==6.0.2  # YAML parsing and generation

# Additional useful packages
python-dateutil==2.9.0.post0  # Enhanced date/time handling
pytz==2025.2  # Timezone support
six==1.17.0  # Python 2/3 compatibility utilities
