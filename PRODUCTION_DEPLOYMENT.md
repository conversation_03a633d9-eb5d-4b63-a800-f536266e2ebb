# Production Deployment Guide with HTTPS

This guide will help you deploy the RadioMention application to production with SSL/HTTPS enabled.

## Prerequisites

1. **Production Server**: Access to the server where `radiocity.ugapp.net` points to (IP: *************)
2. **Domain Configuration**: Ensure `radiocity.ugapp.net` DNS points to your production server
3. **Docker**: <PERSON><PERSON> and <PERSON>er Compose installed on the production server
4. **Ports**: Ports 80 and 443 open on the production server
5. **Email**: Valid email address for Let's Encrypt certificate registration

## Quick Deployment

### Option 1: Automated Deployment (Recommended)

```bash
# On the production server, run:
./scripts/deploy-production.sh
```

This script will:
- ✅ Verify you're on the correct production server
- ✅ Deploy the application using Docker
- ✅ Set up Let's Encrypt SSL certificates
- ✅ Configure HTTPS with security headers
- ✅ Verify everything is working

### Option 2: Manual Step-by-Step

If you prefer manual control:

```bash
# 1. Deploy the application
./scripts/docker-build.sh build --target production
./scripts/docker-build.sh deploy

# 2. Set up SSL certificates
./scripts/setup-ssl.sh

# 3. Verify deployment
curl -I https://radiocity.ugapp.net
```

## Deployment Steps Explained

### Step 1: Application Deployment

The `docker-build.sh` script will:
- Build production Docker images
- Start all services (web, database, redis, nginx, celery)
- Run health checks
- Configure production settings

### Step 2: SSL Certificate Setup

The `setup-ssl.sh` script will:
- Create necessary directories for certificates
- Configure nginx for ACME challenge
- Obtain Let's Encrypt SSL certificates
- Update nginx configuration for HTTPS
- Enable security headers (HSTS, CSP, etc.)

### Step 3: Verification

After deployment, verify:
- ✅ HTTP redirects to HTTPS: `curl -I http://radiocity.ugapp.net`
- ✅ HTTPS is working: `curl -I https://radiocity.ugapp.net`
- ✅ SSL certificate is valid: `openssl s_client -connect radiocity.ugapp.net:443`
- ✅ Application is responding: `https://radiocity.ugapp.net/health`

## Configuration Files

### Key Files Modified for Production:

1. **docker/nginx/default.conf**: Production nginx configuration with SSL
2. **docker/nginx/nginx.conf**: Security headers and SSL settings
3. **docker-compose.yml**: Production service configuration
4. **scripts/setup-ssl.sh**: SSL certificate automation

### SSL Certificate Locations:

- **Certificates**: `./docker/certbot/conf/live/radiocity.ugapp.net/`
- **ACME Challenge**: `./docker/certbot/www/.well-known/acme-challenge/`
- **Logs**: `./docker/certbot/logs/`

## Post-Deployment

### Accessing Your Application

- **Main Application**: https://radiocity.ugapp.net
- **Health Check**: https://radiocity.ugapp.net/health
- **Admin Panel**: https://radiocity.ugapp.net/admin/

### SSL Certificate Management

```bash
# Check certificate status
./scripts/ssl-check.sh

# Renew certificates (automatic via cron)
./scripts/ssl-renew.sh

# Monitor certificate expiration
./scripts/cert-monitor.sh
```

### Monitoring and Logs

```bash
# View all service logs
docker compose logs -f

# View specific service logs
docker compose logs -f nginx
docker compose logs -f web

# Check service status
docker compose ps

# Restart services
docker compose restart
```

## Security Features Enabled

✅ **SSL/TLS Encryption**: Let's Encrypt certificates with automatic renewal
✅ **HSTS**: HTTP Strict Transport Security headers
✅ **CSP**: Content Security Policy headers
✅ **Security Headers**: X-Frame-Options, X-Content-Type-Options, etc.
✅ **HTTP to HTTPS Redirect**: Automatic redirection
✅ **Rate Limiting**: API and login endpoint protection

## Troubleshooting

### Common Issues:

1. **SSL Certificate Failed**:
   ```bash
   # Check DNS resolution
   nslookup radiocity.ugapp.net
   
   # Check ACME challenge
   curl http://radiocity.ugapp.net/.well-known/acme-challenge/test
   ```

2. **Application Not Responding**:
   ```bash
   # Check service status
   docker compose ps
   
   # Check logs
   docker compose logs web
   ```

3. **Database Issues**:
   ```bash
   # Run migrations
   docker compose exec web python manage.py migrate
   
   # Check database connection
   docker compose exec web python manage.py dbshell
   ```

### Support Commands:

```bash
# Full system health check
./scripts/docker-build.sh health

# Database optimization
./scripts/docker-build.sh optimize

# Clean up resources
./scripts/docker-build.sh clean
```

## Maintenance

### Regular Tasks:

1. **SSL Certificate Renewal**: Automatic (cron job)
2. **Application Updates**: Re-run deployment script
3. **Database Backups**: Use `./scripts/backup.sh`
4. **Log Rotation**: Configured automatically
5. **Security Updates**: Regular Docker image updates

### Update Deployment:

```bash
# Pull latest code
git pull origin main

# Redeploy
./scripts/deploy-production.sh
```

## Success Indicators

After successful deployment, you should see:

✅ **HTTPS Working**: Green lock icon in browser
✅ **HTTP Redirects**: Automatic redirect from HTTP to HTTPS
✅ **Valid Certificate**: No browser warnings
✅ **Application Loading**: Login page accessible
✅ **Health Check**: `/health` endpoint responding
✅ **Security Headers**: Present in response headers

---

🎉 **Congratulations!** Your application is now deployed with production-grade HTTPS security.

For support or issues, check the logs and troubleshooting section above.
