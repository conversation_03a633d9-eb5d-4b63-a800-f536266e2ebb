#!/bin/bash

# Docker Cleanup and Restart Script
# This script fixes the ContainerConfig error and prepares for SSL setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Stop all containers
log_info "Stopping all containers..."
docker-compose down --remove-orphans

# Clean up problematic containers
log_info "Removing problematic containers..."
docker container prune -f

# Clean up dangling images
log_info "Cleaning up dangling images..."
docker image prune -f

# Remove specific containers that are causing issues
log_info "Removing specific problematic containers..."
docker rm -f 6dce7a4e0219_appradio-redis-1 2>/dev/null || true
docker rm -f e2ba8bcdde9a_appradio-db-1 2>/dev/null || true

# Clean up volumes (be careful with this - it will remove data)
log_warning "Cleaning up anonymous volumes..."
docker volume prune -f

# Pull latest images
log_info "Pulling latest images..."
docker-compose pull

# Restart Docker daemon if needed (uncomment if issues persist)
# log_warning "Restarting Docker daemon..."
# sudo systemctl restart docker
# sleep 10

log_success "Docker cleanup completed. Ready to restart services."