#!/bin/bash

# Docker Build and Deployment Script for RadioMention
# This script handles building, testing, and deploying the Docker containers
# Updated with production optimizations and monitoring

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="radiomention"
DOCKER_REGISTRY=${DOCKER_REGISTRY:-""}
VERSION=${VERSION:-"latest"}
BUILD_TARGET=${BUILD_TARGET:-"production"}
SENTRY_RELEASE=${SENTRY_RELEASE:-"v1.0.0"}
HEALTH_CHECK_ENABLED=${HEALTH_CHECK_ENABLED:-"true"}

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

success() {
    echo -e "${GREEN}[SUCCESS] $1${NC}"
}

debug() {
    if [[ "$VERBOSE" == "--verbose" ]]; then
        echo -e "${CYAN}[DEBUG] $1${NC}"
    fi
}

# Help function
show_help() {
    echo "Docker Build and Deployment Script for RadioMention"
    echo ""
    echo "Usage: $0 [OPTIONS] COMMAND"
    echo ""
    echo "Commands:"
    echo "  build       Build Docker images with production optimizations"
    echo "  test        Run tests in Docker container"
    echo "  deploy      Deploy using docker-compose with health checks"
    echo "  restart     Restart services"
    echo "  logs        Show logs"
    echo "  clean       Clean up Docker resources"
    echo "  health      Check system health"
    echo "  optimize    Run database optimization"
    echo "  monitor     Start monitoring dashboard"
    echo "  sentry      Test Sentry integration"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  --target TARGET     Build target (development|production) [default: production]"
    echo "  --version VERSION   Image version tag [default: latest]"
    echo "  --registry REGISTRY Docker registry URL"
    echo "  --no-cache          Build without cache"
    echo "  --verbose           Verbose output"
    echo "  --sentry-release    Sentry release version [default: v1.0.0]"
    echo "  --skip-health       Skip health checks during deployment"
    echo ""
    echo "Examples:"
    echo "  $0 build --target production --version v1.2.3"
    echo "  $0 deploy --sentry-release v1.2.3"
    echo "  $0 health --verbose"
    echo "  $0 optimize"
    echo "  $0 sentry --test-error"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --target)
                BUILD_TARGET="$2"
                shift 2
                ;;
            --version)
                VERSION="$2"
                shift 2
                ;;
            --registry)
                DOCKER_REGISTRY="$2"
                shift 2
                ;;
            --sentry-release)
                SENTRY_RELEASE="$2"
                shift 2
                ;;
            --no-cache)
                NO_CACHE="--no-cache"
                shift
                ;;
            --verbose)
                VERBOSE="--verbose"
                shift
                ;;
            --skip-health)
                SKIP_HEALTH="true"
                shift
                ;;
            --test-error)
                TEST_ERROR="--test-error"
                shift
                ;;
            build|test|deploy|restart|logs|clean|health|optimize|monitor|sentry|help)
                COMMAND="$1"
                shift
                ;;
            *)
                error "Unknown option: $1"
                ;;
        esac
    done
}

# Build Docker images
build_images() {
    log "Building Docker images for target: $BUILD_TARGET"

    # Pre-build checks
    info "Running pre-build optimizations..."

    # Pre-build optimizations (only if we have a working Python environment)
    if [[ "$BUILD_TARGET" == "production" ]] && command -v python >/dev/null 2>&1; then
        info "Running pre-build optimizations..."

        # Check if we're in a virtual environment or have Django available
        if python -c "import django" >/dev/null 2>&1; then
            # Install production requirements if possible
            if [[ -f "requirements-prod.txt" ]]; then
                info "Installing production requirements..."
                pip install -r requirements-prod.txt || warning "Could not install production requirements"
            fi

            # Run database migrations if possible
            info "Running database migrations..."
            python manage.py migrate || warning "Could not run migrations"

            # Collect static files if possible
            info "Collecting static files..."
            python manage.py collectstatic --noinput --settings=radio_mentions_project.settings_production || warning "Could not collect static files"
        else
            info "Django not available locally, skipping pre-build optimizations"
        fi
    fi

    # Build the main application image with build args
    docker build \
        $NO_CACHE \
        --target $BUILD_TARGET \
        --build-arg SENTRY_RELEASE=$SENTRY_RELEASE \
        --build-arg BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ') \
        --build-arg VERSION=$VERSION \
        --tag ${PROJECT_NAME}:${VERSION} \
        --tag ${PROJECT_NAME}:latest \
        .

    if [[ -n "$DOCKER_REGISTRY" ]]; then
        docker tag ${PROJECT_NAME}:${VERSION} ${DOCKER_REGISTRY}/${PROJECT_NAME}:${VERSION}
        docker tag ${PROJECT_NAME}:latest ${DOCKER_REGISTRY}/${PROJECT_NAME}:latest
        log "Tagged images for registry: $DOCKER_REGISTRY"
    fi

    # Simple validation
    info "Validating built image..."
    if docker run --rm \
        -e SKIP_DB_CHECK=true \
        -e SKIP_REDIS_CHECK=true \
        ${PROJECT_NAME}:${VERSION} \
        python --version; then
        success "Image validation passed"
    else
        warning "Image validation failed"
    fi

    log "Docker images built successfully!"
    docker images | grep $PROJECT_NAME
}

# Run tests
run_tests() {
    log "Running comprehensive tests in Docker container..."

    # Build test image if needed
    if ! docker images | grep -q "${PROJECT_NAME}:latest"; then
        warning "Application image not found, building first..."
        build_images
    fi

    # Run Django tests
    info "Running Django unit tests..."
    docker run --rm \
        -e DJANGO_SETTINGS_MODULE=radio_mentions_project.settings \
        ${PROJECT_NAME}:latest \
        python manage.py test $VERBOSE

    # Run system health check
    info "Running system health check..."
    docker run --rm \
        -e DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production \
        ${PROJECT_NAME}:latest \
        python manage.py system_health --check-only

    # Run database optimization check
    info "Running database optimization check..."
    docker run --rm \
        -e DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production \
        ${PROJECT_NAME}:latest \
        python manage.py optimize_database --connection-info

    # Test Sentry integration - DISABLED
    if [[ "$BUILD_TARGET" == "production" ]]; then
        info "Sentry integration is disabled - skipping test"
        # docker run --rm \
        #     -e DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production \
        #     ${PROJECT_NAME}:latest \
        #     python manage.py test_sentry --test-info || warning "Sentry test failed"
    fi

    log "All tests completed successfully!"
}

# Deploy using Docker Compose
deploy_services() {
    log "Deploying services using Docker Compose with health checks..."

    # Check if docker-compose.yml exists
    if [[ ! -f "docker-compose.yml" ]]; then
        error "docker-compose.yml not found in current directory"
        exit 1
    fi

    # Set environment variables for docker-compose
    export PROJECT_NAME
    export VERSION
    export BUILD_TARGET
    export SENTRY_RELEASE
    export HEALTH_CHECK_ENABLED

    # Check if image exists, build if needed
    if ! docker images | grep -q "${PROJECT_NAME}:${VERSION}"; then
        warning "Docker image ${PROJECT_NAME}:${VERSION} not found. Building first..."
        build_images
    fi

    # Skip pre-deployment health check - let docker-compose handle startup
    info "Skipping pre-deployment health check - services will validate on startup"

    # Pull latest images if using registry
    if [[ -n "$DOCKER_REGISTRY" ]]; then
        $COMPOSE_CMD pull
    fi

    # Deploy services
    info "Starting services..."
    $COMPOSE_CMD up -d --build

    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 15

    # Post-deployment health checks
    if [[ "$SKIP_HEALTH" != "true" ]]; then
        info "Running post-deployment health checks..."

        # Check if web service is responding
        if command -v curl >/dev/null 2>&1; then
            info "Testing web service response..."
            for i in {1..5}; do
                if curl -f http://localhost/health/ >/dev/null 2>&1; then
                    success "Web service health endpoint responding on port 80 (Nginx)"
                    break
                elif curl -f http://localhost:8000/health/ >/dev/null 2>&1; then
                    success "Web service health endpoint responding on port 8000 (Gunicorn)"
                    break
                elif [[ $i -eq 5 ]]; then
                    warning "Web service health endpoint not responding after 5 attempts"
                else
                    info "Attempt $i/5: Waiting for web service..."
                    sleep 5
                fi
            done
        fi
    fi

    log "Services deployed successfully!"
    $COMPOSE_CMD ps

    info "Application available at: http://localhost (Nginx) or http://localhost:8000 (Gunicorn)"
    info "Health check endpoint: http://localhost/health/ or http://localhost:8000/health/"
    info "Metrics endpoint: http://localhost/metrics/ or http://localhost:8000/metrics/"
}

# Restart services
restart_services() {
    log "Restarting services..."

    $COMPOSE_CMD restart

    log "Services restarted successfully!"
    $COMPOSE_CMD ps
}

# Show logs
show_logs() {
    log "Showing service logs..."

    $COMPOSE_CMD logs -f --tail=100
}

# Clean up Docker resources
clean_resources() {
    log "Cleaning up Docker resources..."

    # Stop and remove containers
    $COMPOSE_CMD down --remove-orphans

    # Remove unused images
    docker image prune -f

    # Remove unused volumes (with confirmation)
    read -p "Remove unused volumes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker volume prune -f
    fi

    log "Cleanup completed!"
}

# Detect Docker Compose command
detect_compose_command() {
    if command -v docker-compose >/dev/null 2>&1; then
        COMPOSE_CMD="docker-compose"
        info "Using docker-compose: $(docker-compose --version)"
    elif docker compose version >/dev/null 2>&1; then
        COMPOSE_CMD="docker compose"
        info "Using docker compose: $(docker compose version)"
    else
        error "Docker Compose is not available. Please install Docker Compose or Docker Desktop."
        exit 1
    fi
}

# Run health check
run_health_check() {
    log "Running comprehensive health check..."

    if docker images | grep -q "${PROJECT_NAME}:latest"; then
        # Run health check in container
        docker run --rm \
            -e DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production \
            ${PROJECT_NAME}:latest \
            python manage.py system_health --check-only $VERBOSE
    else
        # Run health check locally
        python manage.py system_health --check-only $VERBOSE
    fi

    log "Health check completed!"
}

# Run database optimization
run_optimization() {
    log "Running database optimization analysis..."

    if docker images | grep -q "${PROJECT_NAME}:latest"; then
        # Run optimization in container
        docker run --rm \
            -e DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production \
            ${PROJECT_NAME}:latest \
            python manage.py optimize_database --all $VERBOSE
    else
        # Run optimization locally
        python manage.py optimize_database --all $VERBOSE
    fi

    log "Database optimization completed!"
}

# Start monitoring dashboard
start_monitoring() {
    log "Starting monitoring dashboard..."

    info "Starting system monitoring (press Ctrl+C to stop)..."
    if docker images | grep -q "${PROJECT_NAME}:latest"; then
        # Run monitoring in container
        docker run --rm -it \
            -e DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production \
            ${PROJECT_NAME}:latest \
            python manage.py system_health --alert-email=admin@localhost
    else
        # Run monitoring locally
        python manage.py system_health --alert-email=admin@localhost
    fi
}

# Test Sentry integration - DISABLED
test_sentry() {
    log "Sentry integration is disabled"
    warning "Sentry SDK has been disabled in production settings"

    # if docker images | grep -q "${PROJECT_NAME}:latest"; then
    #     # Test Sentry in container
    #     docker run --rm \
    #         -e DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production \
    #         ${PROJECT_NAME}:latest \
    #         python manage.py test_sentry $TEST_ERROR $VERBOSE
    # else
    #     # Test Sentry locally
    #     python manage.py test_sentry $TEST_ERROR $VERBOSE
    # fi

    log "Sentry test skipped (disabled)!"
}

# Validate environment
validate_environment() {
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        error "Docker is not running or not accessible"
        error "Please start Docker Desktop or the Docker daemon"
        exit 1
    fi

    # Detect and set Docker Compose command
    detect_compose_command

    # Check if we're in the right directory
    if [[ ! -f "Dockerfile" ]]; then
        error "Dockerfile not found. Please run this script from the project root directory."
        exit 1
    fi

    # Check if production requirements exist
    if [[ "$BUILD_TARGET" == "production" && ! -f "requirements-prod.txt" ]]; then
        warning "requirements-prod.txt not found. Production optimizations may not be available."
    fi
}

# Main execution
main() {
    # Parse arguments
    parse_args "$@"
    
    # Show help if no command provided
    if [[ -z "$COMMAND" ]]; then
        show_help
        exit 0
    fi
    
    # Validate environment
    validate_environment
    
    # Execute command
    case $COMMAND in
        build)
            build_images
            ;;
        test)
            run_tests
            ;;
        deploy)
            deploy_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        clean)
            clean_resources
            ;;
        health)
            run_health_check
            ;;
        optimize)
            run_optimization
            ;;
        monitor)
            start_monitoring
            ;;
        sentry)
            test_sentry
            ;;
        help)
            show_help
            ;;
        *)
            error "Unknown command: $COMMAND"
            ;;
    esac
}

# Run main function with all arguments
main "$@"
