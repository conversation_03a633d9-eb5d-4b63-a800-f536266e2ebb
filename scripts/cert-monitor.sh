#!/bin/bash

# Certificate Monitoring Script
# Checks SSL certificate expiration and sends alerts

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CERT_DIR="$SCRIPT_DIR/docker/certbot/conf/live"
WARNING_DAYS=30
CRITICAL_DAYS=7

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Email settings (configure as needed)
ALERT_EMAIL="${ALERT_EMAIL:-admin@localhost}"
SEND_EMAIL="${SEND_EMAIL:-false}"

log_info() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log_warning() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${YELLOW}WARNING:${NC} $1"
}

log_error() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${RED}ERROR:${NC} $1"
}

log_success() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${GREEN}SUCCESS:${NC} $1"
}

send_alert() {
    local subject="$1"
    local message="$2"
    
    if [ "$SEND_EMAIL" = "true" ] && command -v mail &> /dev/null; then
        echo "$message" | mail -s "$subject" "$ALERT_EMAIL"
        log_info "Alert sent to $ALERT_EMAIL"
    else
        log_warning "Email alerts not configured or mail command not available"
    fi
}

check_certificate() {
    local domain="$1"
    local cert_file="$CERT_DIR/$domain/cert.pem"
    
    if [ ! -f "$cert_file" ]; then
        log_error "Certificate file not found: $cert_file"
        return 1
    fi
    
    # Get certificate expiration date
    local exp_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
    local exp_timestamp=$(date -d "$exp_date" +%s)
    local current_timestamp=$(date +%s)
    local days_until_expiry=$(( (exp_timestamp - current_timestamp) / 86400 ))
    
    log_info "Certificate for $domain expires in $days_until_expiry days ($exp_date)"
    
    # Check expiration status
    if [ $days_until_expiry -le $CRITICAL_DAYS ]; then
        log_error "CRITICAL: Certificate for $domain expires in $days_until_expiry days!"
        send_alert "CRITICAL: SSL Certificate Expiring Soon - $domain" \
                  "The SSL certificate for $domain will expire in $days_until_expiry days on $exp_date. Immediate action required!"
        return 2
    elif [ $days_until_expiry -le $WARNING_DAYS ]; then
        log_warning "Certificate for $domain expires in $days_until_expiry days"
        send_alert "WARNING: SSL Certificate Expiring Soon - $domain" \
                  "The SSL certificate for $domain will expire in $days_until_expiry days on $exp_date. Please renew soon."
        return 1
    else
        log_success "Certificate for $domain is valid for $days_until_expiry more days"
        return 0
    fi
}

check_all_certificates() {
    local exit_code=0
    
    if [ ! -d "$CERT_DIR" ]; then
        log_error "Certificate directory not found: $CERT_DIR"
        return 1
    fi
    
    log_info "Checking all SSL certificates..."
    
    for domain_dir in "$CERT_DIR"/*; do
        if [ -d "$domain_dir" ]; then
            local domain=$(basename "$domain_dir")
            check_certificate "$domain"
            local cert_status=$?
            
            if [ $cert_status -gt $exit_code ]; then
                exit_code=$cert_status
            fi
        fi
    done
    
    if [ $exit_code -eq 0 ]; then
        log_success "All certificates are valid"
    elif [ $exit_code -eq 1 ]; then
        log_warning "Some certificates need attention"
    else
        log_error "Some certificates are critically close to expiration"
    fi
    
    return $exit_code
}

show_certificate_info() {
    local domain="$1"
    
    if [ -z "$domain" ]; then
        log_error "Domain name required"
        return 1
    fi
    
    local cert_file="$CERT_DIR/$domain/cert.pem"
    
    if [ ! -f "$cert_file" ]; then
        log_error "Certificate file not found: $cert_file"
        return 1
    fi
    
    log_info "Certificate information for $domain:"
    openssl x509 -in "$cert_file" -noout -text | grep -E "(Subject:|Issuer:|Not Before:|Not After:|DNS:)"
}

auto_renew_check() {
    log_info "Checking if certificates need renewal..."
    
    # Check certificates and renew if needed
    check_all_certificates
    local check_result=$?
    
    if [ $check_result -ge 1 ]; then
        log_info "Some certificates need renewal. Running certbot renew..."
        
        if command -v docker-compose &> /dev/null; then
            docker-compose run --rm certbot renew --quiet
            
            if [ $? -eq 0 ]; then
                log_success "Certificate renewal completed"
                # Reload nginx
                docker-compose exec nginx nginx -s reload || true
            else
                log_error "Certificate renewal failed"
                send_alert "SSL Certificate Renewal Failed" \
                          "Automatic SSL certificate renewal failed. Manual intervention required."
            fi
        else
            log_error "docker-compose not found. Cannot perform automatic renewal."
        fi
    fi
}

# Main script logic
case "${1:-check}" in
    check)
        check_all_certificates
        ;;
    info)
        show_certificate_info "$2"
        ;;
    auto-renew)
        auto_renew_check
        ;;
    help|--help|-h)
        cat << EOF
Certificate Monitoring Script

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    check           Check all certificates (default)
    info DOMAIN     Show detailed certificate information
    auto-renew      Check and auto-renew certificates if needed
    help            Show this help message

Environment Variables:
    ALERT_EMAIL     Email address for alerts (default: admin@localhost)
    SEND_EMAIL      Enable email alerts (default: false)
    WARNING_DAYS    Days before expiry to warn (default: 30)
    CRITICAL_DAYS   Days before expiry for critical alert (default: 7)

Examples:
    $0 check
    $0 info example.com
    SEND_EMAIL=true ALERT_EMAIL=<EMAIL> $0 check
EOF
        ;;
    *)
        log_error "Unknown command: $1"
        log_info "Use '$0 help' for usage information"
        exit 1
        ;;
esac