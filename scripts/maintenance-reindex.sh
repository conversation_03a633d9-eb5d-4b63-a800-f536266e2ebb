#!/bin/bash

# =============================================================================
# SAFE DATABASE REINDEXING SCRIPT FOR PRODUCTION
# =============================================================================
# This script provides safe database reindexing during maintenance windows
# Use this script during low-traffic periods or scheduled maintenance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
COMPOSE_FILE="docker-compose.yml"
BACKUP_BEFORE_REINDEX=true
MAINTENANCE_MODE=false
AUTOMATED_MODE=false

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if services are running
check_services() {
    log_info "Checking service status..."
    
    if ! docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
        log_error "Services are not running. Please start them first."
        exit 1
    fi
    
    log_success "Services are running"
}

# Function to create backup before reindexing
create_backup() {
    if [ "$BACKUP_BEFORE_REINDEX" = true ]; then
        log_info "Creating backup before reindexing..."
        
        if [ -f "./scripts/backup.sh" ]; then
            ./scripts/backup.sh
            log_success "Backup completed"
        else
            log_warning "Backup script not found. Proceeding without backup."
        fi
    fi
}

# Function to enable maintenance mode (if implemented)
enable_maintenance_mode() {
    if [ "$MAINTENANCE_MODE" = true ]; then
        log_info "Enabling maintenance mode..."
        # Add your maintenance mode logic here
        # For example: touch /app/maintenance.flag
        log_warning "Maintenance mode not implemented. Consider implementing it for production."
    fi
}

# Function to disable maintenance mode
disable_maintenance_mode() {
    if [ "$MAINTENANCE_MODE" = true ]; then
        log_info "Disabling maintenance mode..."
        # Add your maintenance mode disable logic here
        # For example: rm -f /app/maintenance.flag
    fi
}

# Function to check database health before reindexing
check_database_health() {
    log_info "Checking database health..."
    
    # Check if database is accessible
    if ! docker-compose -f $COMPOSE_FILE exec -T db pg_isready -U postgres >/dev/null 2>&1; then
        log_error "Database is not accessible"
        exit 1
    fi
    
    # Check for active connections
    ACTIVE_CONNECTIONS=$(docker-compose -f $COMPOSE_FILE exec -T db psql -U postgres -d radio_mentions -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active' AND pid != pg_backend_pid();" | tr -d ' ')
    
    log_info "Active database connections: $ACTIVE_CONNECTIONS"
    
    if [ "$ACTIVE_CONNECTIONS" -gt 10 ]; then
        log_warning "High number of active connections ($ACTIVE_CONNECTIONS). Consider waiting for lower traffic."

        if [ "$AUTOMATED_MODE" = false ]; then
            read -p "Continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "Reindexing cancelled by user"
                exit 0
            fi
        else
            log_warning "Automated mode: Proceeding despite high connections"
        fi
    fi
    
    log_success "Database health check passed"
}

# Function to perform safe reindexing
perform_reindexing() {
    log_info "Starting database reindexing..."
    log_warning "This operation will lock tables and may cause temporary unavailability"
    
    # Estimate time based on database size
    DB_SIZE=$(docker-compose -f $COMPOSE_FILE exec -T db psql -U postgres -d radio_mentions -t -c "SELECT pg_size_pretty(pg_database_size('radio_mentions'));" | tr -d ' ')
    log_info "Database size: $DB_SIZE"
    
    # Confirm before proceeding (skip in automated mode)
    if [ "$AUTOMATED_MODE" = false ]; then
        echo
        log_warning "⚠️  IMPORTANT: This will lock database tables during reindexing"
        log_warning "⚠️  Users may experience temporary errors or timeouts"
        log_warning "⚠️  Estimated time: 1-5 minutes for small DB, 10+ minutes for large DB"
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Reindexing cancelled by user"
            exit 0
        fi
    else
        log_info "Automated mode: Proceeding with reindexing during maintenance window"
    fi
    
    # Record start time
    START_TIME=$(date +%s)
    
    # Perform reindexing
    log_info "Executing reindexing command..."
    if docker-compose -f $COMPOSE_FILE exec -T web python manage.py optimize_database --reindex; then
        # Calculate duration
        END_TIME=$(date +%s)
        DURATION=$((END_TIME - START_TIME))
        
        log_success "Reindexing completed successfully in ${DURATION} seconds"
        
        # Log the maintenance activity
        docker-compose -f $COMPOSE_FILE exec -T web python -c "
from apps.activity_logs.models import ActivityLog
from django.utils import timezone
ActivityLog.log_activity(
    user=None,
    organization=None,
    action='manual_database_reindex',
    description='Manual database reindexing completed during maintenance window',
    level='info',
    metadata={'duration_seconds': $DURATION, 'database_size': '$DB_SIZE'}
)
print('Maintenance activity logged')
"
    else
        log_error "Reindexing failed!"
        exit 1
    fi
}

# Function to verify reindexing results
verify_reindexing() {
    log_info "Verifying reindexing results..."
    
    # Check database connectivity
    if docker-compose -f $COMPOSE_FILE exec -T db pg_isready -U postgres >/dev/null 2>&1; then
        log_success "Database is accessible after reindexing"
    else
        log_error "Database connectivity issues after reindexing"
        exit 1
    fi
    
    # Run a simple query test
    if docker-compose -f $COMPOSE_FILE exec -T web python manage.py shell -c "from django.contrib.auth.models import User; print(f'User count: {User.objects.count()}')" >/dev/null 2>&1; then
        log_success "Database queries working correctly"
    else
        log_error "Database query issues after reindexing"
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "Safe Database Reindexing Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --no-backup         Skip backup before reindexing"
    echo "  --maintenance-mode  Enable maintenance mode during reindexing"
    echo "  --automated         Run in automated mode (skip user confirmations)"
    echo "  --help              Show this help message"
    echo ""
    echo "This script will:"
    echo "  1. Check service status and database health"
    echo "  2. Create a backup (optional)"
    echo "  3. Enable maintenance mode (optional)"
    echo "  4. Perform database reindexing with user confirmation"
    echo "  5. Verify results and disable maintenance mode"
    echo ""
    echo "⚠️  WARNING: This operation locks database tables"
    echo "⚠️  Use only during scheduled maintenance windows"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --no-backup)
            BACKUP_BEFORE_REINDEX=false
            shift
            ;;
        --maintenance-mode)
            MAINTENANCE_MODE=true
            shift
            ;;
        --automated)
            AUTOMATED_MODE=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    log_info "Starting safe database reindexing process..."
    echo
    
    # Pre-flight checks
    check_services
    check_database_health
    
    # Backup
    create_backup
    
    # Enable maintenance mode
    enable_maintenance_mode
    
    # Perform reindexing
    perform_reindexing
    
    # Verify results
    verify_reindexing
    
    # Disable maintenance mode
    disable_maintenance_mode
    
    echo
    log_success "✅ Database reindexing completed successfully!"
    log_info "Monitor your application for any issues"
    echo
}

# Trap to ensure maintenance mode is disabled on exit
trap 'disable_maintenance_mode' EXIT

# Run main function
main "$@"
