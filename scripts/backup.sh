#!/bin/bash

# =============================================================================
# Database Backup Script for RadioMention
# =============================================================================

set -e

# Configuration
DB_NAME="${DB_NAME:-radio_mentions}"
DB_USER="${DB_USER:-postgres}"
DB_HOST="${DB_HOST:-db}"
BACKUP_DIR="/backups"
RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/radio_mentions_backup_$TIMESTAMP.sql"
LOG_FILE="$BACKUP_DIR/backup.log"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    echo "[ERROR] $1" >> "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
    echo "[WARNING] $1" >> "$LOG_FILE"
}

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Function to create database backup
create_backup() {
    log "Starting database backup..."
    
    # Check if database is accessible
    if ! pg_isready -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
        error "Database is not accessible"
    fi
    
    # Create backup
    log "Creating backup: $BACKUP_FILE"
    if pg_dump -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" --no-password --verbose > "$BACKUP_FILE" 2>> "$LOG_FILE"; then
        log "Backup created successfully: $BACKUP_FILE"
        
        # Compress backup
        gzip "$BACKUP_FILE"
        log "Backup compressed: $BACKUP_FILE.gz"
        
        # Get file size
        BACKUP_SIZE=$(du -h "$BACKUP_FILE.gz" | cut -f1)
        log "Backup size: $BACKUP_SIZE"
        
    else
        error "Backup creation failed"
    fi
}

# Function to clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups (keeping last $RETENTION_DAYS days)..."
    
    # Find and delete old backups
    DELETED_COUNT=$(find "$BACKUP_DIR" -name "radio_mentions_backup_*.sql.gz" -type f -mtime +$RETENTION_DAYS -delete -print | wc -l)
    
    if [ "$DELETED_COUNT" -gt 0 ]; then
        log "Deleted $DELETED_COUNT old backup files"
    else
        log "No old backup files to delete"
    fi
}

# Function to verify backup
verify_backup() {
    log "Verifying backup integrity..."
    
    if [ -f "$BACKUP_FILE.gz" ]; then
        # Test gzip integrity
        if gzip -t "$BACKUP_FILE.gz"; then
            log "Backup file integrity verified"
        else
            error "Backup file is corrupted"
        fi
        
        # Check if backup contains data
        BACKUP_SIZE=$(stat -f%z "$BACKUP_FILE.gz" 2>/dev/null || stat -c%s "$BACKUP_FILE.gz" 2>/dev/null)
        if [ "$BACKUP_SIZE" -gt 1000 ]; then
            log "Backup file size looks reasonable: $BACKUP_SIZE bytes"
        else
            warning "Backup file seems too small: $BACKUP_SIZE bytes"
        fi
    else
        error "Backup file not found: $BACKUP_FILE.gz"
    fi
}

# Function to upload backup to Digital Ocean Spaces (optional)
upload_to_spaces() {
    if [ -n "$DO_SPACES_ACCESS_KEY_ID" ] && [ -n "$DO_SPACES_SECRET_ACCESS_KEY" ] && [ -n "$DO_SPACES_BUCKET_NAME" ]; then
        log "Uploading backup to Digital Ocean Spaces..."
        
        # Install s3cmd if not present
        if ! command -v s3cmd &> /dev/null; then
            log "Installing s3cmd..."
            apk add --no-cache s3cmd
        fi
        
        # Configure s3cmd
        cat > ~/.s3cfg << EOF
[default]
access_key = $DO_SPACES_ACCESS_KEY_ID
secret_key = $DO_SPACES_SECRET_ACCESS_KEY
host_base = ${DO_SPACES_ENDPOINT_URL#https://}
host_bucket = %(bucket)s.${DO_SPACES_ENDPOINT_URL#https://}
use_https = True
EOF
        
        # Upload backup
        SPACES_PATH="s3://$DO_SPACES_BUCKET_NAME/backups/$(basename $BACKUP_FILE.gz)"
        if s3cmd put "$BACKUP_FILE.gz" "$SPACES_PATH"; then
            log "Backup uploaded to Spaces: $SPACES_PATH"
        else
            warning "Failed to upload backup to Spaces"
        fi
    else
        log "Digital Ocean Spaces not configured, skipping upload"
    fi
}

# Function to send notification (optional)
send_notification() {
    if [ -n "$WEBHOOK_URL" ]; then
        log "Sending backup notification..."
        
        BACKUP_SIZE=$(du -h "$BACKUP_FILE.gz" | cut -f1)
        MESSAGE="✅ RadioMention database backup completed successfully
        
📊 **Backup Details:**
- File: $(basename $BACKUP_FILE.gz)
- Size: $BACKUP_SIZE
- Time: $(date)
- Server: $(hostname)"
        
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"$MESSAGE\"}" \
            > /dev/null 2>&1 || warning "Failed to send notification"
    fi
}

# Main backup process
main() {
    log "Starting backup process for $DB_NAME database"
    
    create_backup
    verify_backup
    cleanup_old_backups
    upload_to_spaces
    send_notification
    
    log "Backup process completed successfully"
    
    # Show backup summary
    echo ""
    echo "=== Backup Summary ==="
    echo "Database: $DB_NAME"
    echo "Backup file: $(basename $BACKUP_FILE.gz)"
    echo "Size: $(du -h $BACKUP_FILE.gz | cut -f1)"
    echo "Location: $BACKUP_FILE.gz"
    echo "Timestamp: $TIMESTAMP"
    echo "======================"
}

# Cron job setup (if called with 'cron' argument)
if [ "$1" = "cron" ]; then
    # Add cron job for daily backups at 2 AM
    echo "0 2 * * * /backup.sh" | crontab -
    log "Cron job added for daily backups at 2 AM"
    
    # Start cron daemon
    crond -f
else
    # Run backup immediately
    main
fi
