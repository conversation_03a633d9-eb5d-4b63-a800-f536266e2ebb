#!/bin/bash

# SSL Certificate Renewal Script for AppRadio
# Automatically renews Let's Encrypt certificates and restarts nginx

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CERTBOT_DIR="$PROJECT_ROOT/docker/certbot"
LOG_FILE="$PROJECT_ROOT/logs/ssl-renewal.log"

# Functions
log() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo -e "${GREEN}$message${NC}"
    echo "$message" >> "$LOG_FILE"
}

warn() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1"
    echo -e "${YELLOW}$message${NC}"
    echo "$message" >> "$LOG_FILE"
}

error() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1"
    echo -e "${RED}$message${NC}"
    echo "$message" >> "$LOG_FILE"
    exit 1
}

info() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1"
    echo -e "${BLUE}$message${NC}"
    echo "$message" >> "$LOG_FILE"
}

# Create log directory
mkdir -p "$(dirname "$LOG_FILE")"

# Check if certificates exist
check_certificates() {
    if [ ! -d "$CERTBOT_DIR/conf/live" ]; then
        error "No Let's Encrypt certificates found. Run './scripts/setup-ssl.sh letsencrypt' first."
    fi
    
    local cert_count=$(find "$CERTBOT_DIR/conf/live" -maxdepth 1 -type d | wc -l)
    if [ "$cert_count" -le 1 ]; then
        error "No certificate directories found in $CERTBOT_DIR/conf/live"
    fi
    
    log "Found certificates to check for renewal"
}

# Check certificate expiration
check_expiration() {
    log "Checking certificate expiration dates..."
    
    for cert_dir in "$CERTBOT_DIR/conf/live"/*; do
        if [ -d "$cert_dir" ] && [ "$(basename "$cert_dir")" != "README" ]; then
            local domain=$(basename "$cert_dir")
            local cert_file="$cert_dir/cert.pem"
            
            if [ -f "$cert_file" ]; then
                local expiry_date=$(openssl x509 -in "$cert_file" -noout -enddate | cut -d= -f2)
                local expiry_epoch=$(date -d "$expiry_date" +%s)
                local current_epoch=$(date +%s)
                local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
                
                info "Certificate for $domain expires in $days_until_expiry days ($expiry_date)"
                
                if [ "$days_until_expiry" -lt 30 ]; then
                    warn "Certificate for $domain expires in less than 30 days!"
                elif [ "$days_until_expiry" -lt 7 ]; then
                    error "Certificate for $domain expires in less than 7 days! Immediate renewal required."
                fi
            else
                warn "Certificate file not found for $domain"
            fi
        fi
    done
}

# Renew certificates
renew_certificates() {
    log "Starting certificate renewal process..."
    
    # Change to project directory
    cd "$PROJECT_ROOT"
    
    # Run certbot renewal
    log "Running certbot renew..."
    if docker-compose run --rm certbot renew --quiet; then
        log "Certificate renewal completed successfully"
        
        # Check if any certificates were actually renewed
        local renewal_log="/tmp/certbot-renewal.log"
        docker-compose run --rm certbot renew --dry-run > "$renewal_log" 2>&1 || true
        
        if grep -q "would be renewed" "$renewal_log"; then
            log "Certificates were renewed, restarting nginx..."
            restart_nginx
        else
            info "No certificates needed renewal"
        fi
        
        rm -f "$renewal_log"
    else
        error "Certificate renewal failed"
    fi
}

# Restart nginx
restart_nginx() {
    log "Restarting nginx to apply renewed certificates..."
    
    if docker-compose restart nginx; then
        log "Nginx restarted successfully"
        
        # Wait a moment for nginx to start
        sleep 5
        
        # Test nginx configuration
        if docker-compose exec nginx nginx -t; then
            log "Nginx configuration test passed"
        else
            error "Nginx configuration test failed"
        fi
    else
        error "Failed to restart nginx"
    fi
}

# Send notification (placeholder for future implementation)
send_notification() {
    local status=$1
    local message=$2
    
    # This is a placeholder for notification functionality
    # You can implement email, Slack, or other notifications here
    log "Notification: $status - $message"
}

# Dry run renewal
dry_run() {
    log "Performing dry run certificate renewal..."
    
    cd "$PROJECT_ROOT"
    
    if docker-compose run --rm certbot renew --dry-run; then
        log "Dry run completed successfully - renewal should work"
        return 0
    else
        error "Dry run failed - there may be issues with renewal"
        return 1
    fi
}

# Show certificate status
show_status() {
    log "SSL Certificate Status Report"
    echo "================================"
    
    check_expiration
    
    echo ""
    log "Certificate files:"
    for cert_dir in "$CERTBOT_DIR/conf/live"/*; do
        if [ -d "$cert_dir" ] && [ "$(basename "$cert_dir")" != "README" ]; then
            local domain=$(basename "$cert_dir")
            echo "  Domain: $domain"
            echo "    Certificate: $cert_dir/cert.pem"
            echo "    Private Key: $cert_dir/privkey.pem"
            echo "    Full Chain: $cert_dir/fullchain.pem"
            echo ""
        fi
    done
}

# Setup cron job for automatic renewal
setup_cron() {
    log "Setting up automatic certificate renewal..."
    
    local cron_script="$SCRIPT_DIR/ssl-renew.sh"
    local cron_entry="0 2 * * 0 $cron_script renew >> $LOG_FILE 2>&1"
    
    # Check if cron entry already exists
    if crontab -l 2>/dev/null | grep -q "$cron_script"; then
        warn "Cron job already exists for SSL renewal"
    else
        # Add cron job
        (crontab -l 2>/dev/null; echo "$cron_entry") | crontab -
        log "Cron job added: Certificate renewal will run every Sunday at 2 AM"
    fi
    
    info "Current cron jobs:"
    crontab -l | grep -E "(ssl-renew|certbot)" || echo "No SSL renewal cron jobs found"
}

# Remove cron job
remove_cron() {
    log "Removing automatic certificate renewal cron job..."
    
    local cron_script="$SCRIPT_DIR/ssl-renew.sh"
    
    # Remove cron job
    crontab -l 2>/dev/null | grep -v "$cron_script" | crontab -
    log "Cron job removed"
}

# Show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  renew          Renew Let's Encrypt certificates"
    echo "  dry-run        Test certificate renewal without actually renewing"
    echo "  status         Show certificate status and expiration dates"
    echo "  setup-cron     Setup automatic renewal cron job"
    echo "  remove-cron    Remove automatic renewal cron job"
    echo "  help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 renew       # Renew certificates"
    echo "  $0 dry-run     # Test renewal process"
    echo "  $0 status      # Check certificate status"
    echo "  $0 setup-cron  # Setup automatic renewal"
}

# Main script logic
main() {
    case "${1:-help}" in
        "renew")
            check_certificates
            renew_certificates
            send_notification "SUCCESS" "SSL certificates renewed successfully"
            ;;
        "dry-run")
            check_certificates
            dry_run
            ;;
        "status")
            show_status
            ;;
        "setup-cron")
            setup_cron
            ;;
        "remove-cron")
            remove_cron
            ;;
        "help"|*)
            show_usage
            ;;
    esac
}

# Run main function
main "$@"
