#!/bin/bash

# === Configuration ===
CONTAINER_NAME="db"                  # Change to your Postgres container name (e.g. postgres, db, pg14, etc)
DB_NAME="radio_mentions"            # Your Postgres DB name
DB_USER="postgres"                  # Your DB user (username stays postgres)
BACKUP_DIR="$HOME/postgres_backups" # Where to store the backup on host
DATE_STR=$(date +"%Y-%m-%d_%H-%M")
BACKUP_FILE="backup_${DB_NAME}_${DATE_STR}.dump"

# === Ensure backup directory exists ===
mkdir -p "$BACKUP_DIR"

# === Run pg_dump inside container ===
echo "🔄 Dumping database '$DB_NAME' from container '$CONTAINER_NAME'..."
docker exec -t "$CONTAINER_NAME" pg_dump -U "$DB_USER" -F c "$DB_NAME" -f "/tmp/$BACKUP_FILE"

# === Copy backup file from container to host ===
echo "📦 Copying backup to host: $BACKUP_DIR/$BACKUP_FILE"
docker cp "$CONTAINER_NAME:/tmp/$BACKUP_FILE" "$BACKUP_DIR/$BACKUP_FILE"

# === Clean up backup from container ===
echo "🧹 Cleaning up container temporary file"
docker exec "$CONTAINER_NAME" rm "/tmp/$BACKUP_FILE"

# === Done ===
echo "✅ Backup complete: $BACKUP_DIR/$BACKUP_FILE"
