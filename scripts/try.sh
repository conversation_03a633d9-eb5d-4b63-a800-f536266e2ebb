#!/bin/bash

# Database Connection Fix Script
# Run this on the production server to fix database connectivity issues

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check Docker Compose command
detect_compose_command() {
    if command -v docker-compose >/dev/null 2>&1; then
        COMPOSE_CMD="docker-compose"
    elif docker compose version >/dev/null 2>&1; then
        COMPOSE_CMD="docker compose"
    else
        log_error "Docker Compose is not available"
        exit 1
    fi
    log_info "Using: $COMPOSE_CMD"
}

# Function to check current status
check_status() {
    log_info "Checking current Docker services status..."
    
    if $COMPOSE_CMD ps | grep -q "Up"; then
        log_info "Some services are running:"
        $COMPOSE_CMD ps
    else
        log_warning "No services appear to be running"
    fi
}

# Function to fix database connection
fix_database_connection() {
    log_info "Fixing database connection issues..."
    
    # Step 1: Stop all services
    log_info "Stopping all services..."
    $COMPOSE_CMD down
    
    # Step 2: Clean up networks
    log_info "Cleaning up Docker networks..."
    docker network prune -f || true
    
    # Step 3: Start services in correct order
    log_info "Starting database and Redis first..."
    $COMPOSE_CMD up -d db redis
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    for i in {1..30}; do
        if $COMPOSE_CMD exec -T db pg_isready -U postgres >/dev/null 2>&1; then
            log_success "Database is ready!"
            break
        elif [[ $i -eq 30 ]]; then
            log_error "Database failed to start after 30 attempts"
            exit 1
        else
            echo -n "."
            sleep 2
        fi
    done
    
    # Step 4: Start web services
    log_info "Starting web application..."
    $COMPOSE_CMD up -d web
    
    # Wait for web service
    log_info "Waiting for web service to be ready..."
    sleep 10
    
    # Step 5: Run migrations
    log_info "Running database migrations..."
    $COMPOSE_CMD exec -T web python manage.py migrate
    
    # Step 6: Start remaining services
    log_info "Starting remaining services..."
    $COMPOSE_CMD up -d celery celery-beat nginx
    
    log_success "All services started successfully!"
}

# Function to verify connection
verify_connection() {
    log_info "Verifying database connection..."
    
    # Test database connection
    if $COMPOSE_CMD exec -T web python manage.py dbshell -c "\q" >/dev/null 2>&1; then
        log_success "Database connection is working!"
    else
        log_error "Database connection still failing"
        return 1
    fi
    
    # Test web application
    log_info "Testing web application..."
    sleep 5
    
    if curl -f http://localhost/health >/dev/null 2>&1; then
        log_success "Web application is responding!"
    elif curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log_success "Web application is responding on port 8000!"
    else
        log_warning "Web application may not be responding yet (this is normal, give it a few more minutes)"
    fi
}

# Function to show final status
show_final_status() {
    log_info "Final service status:"
    $COMPOSE_CMD ps
    
    echo
    log_info "If issues persist, check logs with:"
    echo "  $COMPOSE_CMD logs db"
    echo "  $COMPOSE_CMD logs web"
    echo "  $COMPOSE_CMD logs nginx"
    
    echo
    log_info "To restart a specific service:"
    echo "  $COMPOSE_CMD restart db"
    echo "  $COMPOSE_CMD restart web"
}

# Main function
main() {
    log_info "Starting database connection fix..."
    
    # Detect Docker Compose command
    detect_compose_command
    
    # Check current status
    check_status
    
    # Fix database connection
    fix_database_connection
    
    # Verify everything is working
    verify_connection
    
    # Show final status
    show_final_status
    
    log_success "Database connection fix completed! 🎉"
}

# Run main function
main "$@"
