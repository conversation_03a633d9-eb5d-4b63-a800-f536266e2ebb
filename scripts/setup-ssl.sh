#!/bin/bash

# Fixed SSL Setup Script for radiocity.ugapp.net
# This script handles the DNS and connectivity issues

set -e

# Configuration
DOMAIN="radiocity.ugapp.net"
EMAIL="<EMAIL>"  # Update with your actual email
STAGING=false  # Set to true for testing

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check DNS resolution
check_dns() {
    local domain="$1"
    log_info "Checking DNS resolution for $domain..."
    
    if nslookup "$domain" >/dev/null 2>&1; then
        local ip=$(nslookup "$domain" | grep -A1 "Name:" | tail -1 | awk '{print $2}')
        log_success "DNS resolved: $domain -> $ip"
        return 0
    else
        log_error "DNS resolution failed for $domain"
        return 1
    fi
}

# Function to check HTTP connectivity
check_http_connectivity() {
    local domain="$1"
    log_info "Checking HTTP connectivity to $domain..."
    
    if curl -s --connect-timeout 10 "http://$domain" >/dev/null 2>&1; then
        log_success "HTTP connectivity OK for $domain"
        return 0
    else
        log_warning "HTTP connectivity failed for $domain"
        return 1
    fi
}

# Function to create directories
ensure_directories() {
    log_info "Creating necessary directories..."
    mkdir -p ./docker/certbot/conf
    mkdir -p ./docker/certbot/www
    mkdir -p ./docker/certbot/logs
    mkdir -p ./logs/nginx
    log_success "Directories created"
}

# Function to create HTTP-only nginx config
create_http_only_config() {
    log_info "Creating HTTP-only nginx configuration..."
    
    # Backup existing config
    if [ -f "./docker/nginx/default.conf" ]; then
        cp "./docker/nginx/default.conf" "./docker/nginx/default.conf.ssl-backup"
        log_info "Backed up existing nginx config"
    fi
    
    # Create HTTP-only config for ACME challenge
    cat > "./docker/nginx/default.conf" << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN;

    # ACME challenge location for Let's Encrypt
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
        allow all;
    }

    # Health check
    location /health {
        return 200 "OK";
        add_header Content-Type text/plain;
    }

    # Temporary redirect for all other requests
    location / {
        return 200 "Server is running. SSL setup in progress.";
        add_header Content-Type text/plain;
    }
}
EOF
    
    log_success "HTTP-only configuration created"
}

# Function to restore SSL config
restore_ssl_config() {
    log_info "Restoring SSL configuration..."
    
    if [ -f "./docker/nginx/default.conf.ssl-backup" ]; then
        mv "./docker/nginx/default.conf.ssl-backup" "./docker/nginx/default.conf"
        log_success "SSL configuration restored"
    else
        log_warning "No SSL backup found, creating new SSL config..."
        create_ssl_config
    fi
}

# Function to create SSL config
create_ssl_config() {
    cat > "./docker/nginx/default.conf" << EOF
# HTTP server - redirects to HTTPS and handles ACME challenges
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN;

    # ACME challenge location for Let's Encrypt
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
    }

    # Redirect all other HTTP requests to HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name $DOMAIN;

    # SSL certificate configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/$DOMAIN/chain.pem;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Static files
    location /static/ {
        alias /app/staticfiles/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Media files
    location /media/ {
        alias /app/media/;
        expires 30d;
        add_header Cache-Control "public";
    }

    # Health check
    location /health {
        access_log off;
        return 200 "OK";
        add_header Content-Type text/plain;
    }

    # Main application
    location / {
        proxy_pass http://web:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$server_name;
        proxy_redirect off;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
EOF
    log_success "SSL configuration created"
}

# Main setup function
main() {
    log_info "Starting SSL setup for $DOMAIN"
    
    # Check prerequisites
    log_info "Checking prerequisites..."
    
    # Check DNS resolution for main domain only (skip www subdomain)
    if ! check_dns "$DOMAIN"; then
        log_error "DNS resolution failed. Please ensure $DOMAIN points to this server."
        exit 1
    fi
    
    # Create directories
    ensure_directories
    
    # Create HTTP-only config
    create_http_only_config
    
    # Stop any running containers
    log_info "Stopping existing containers..."
    docker-compose down 2>/dev/null || true
    
    # Start nginx with HTTP-only config
    log_info "Starting nginx with HTTP-only configuration..."
    docker-compose up -d nginx
    
    # Wait for nginx to start
    sleep 10
    
    # Check if nginx is responding
    if ! check_http_connectivity "$DOMAIN"; then
        log_error "Nginx is not responding on port 80. Please check firewall settings."
        log_info "Make sure port 80 is open: sudo ufw allow 80"
        exit 1
    fi
    
    # Test ACME challenge directory
    log_info "Testing ACME challenge directory..."
    echo "test" > ./docker/certbot/www/test.txt
    
    if curl -s "http://$DOMAIN/.well-known/acme-challenge/test.txt" | grep -q "test"; then
        log_success "ACME challenge directory is accessible"
        rm -f ./docker/certbot/www/test.txt
    else
        log_error "ACME challenge directory is not accessible"
        exit 1
    fi
    
    # Obtain SSL certificate (single domain only, no www)
    log_info "Obtaining SSL certificate for $DOMAIN..."
    
    local certbot_cmd="certbot certonly --webroot --webroot-path=/var/www/certbot"
    certbot_cmd="$certbot_cmd --email $EMAIL --agree-tos --no-eff-email"
    certbot_cmd="$certbot_cmd -d $DOMAIN"
    
    if [ "$STAGING" = "true" ]; then
        certbot_cmd="$certbot_cmd --staging"
        log_warning "Using Let's Encrypt staging environment"
    fi
    
    # Run certbot
    if docker-compose run --rm certbot $certbot_cmd; then
        log_success "SSL certificate obtained successfully!"
        
        # Restore SSL configuration
        restore_ssl_config
        
        # Restart nginx with SSL
        log_info "Restarting nginx with SSL configuration..."
        docker-compose restart nginx
        
        # Wait for nginx to restart
        sleep 5
        
        # Test HTTPS
        if curl -s --connect-timeout 10 "https://$DOMAIN/health" >/dev/null 2>&1; then
            log_success "HTTPS is working! SSL setup completed successfully."
            log_info "Your site is now available at: https://$DOMAIN"
        else
            log_warning "HTTPS test failed, but certificate was obtained. Check nginx logs."
        fi
        
    else
        log_error "Failed to obtain SSL certificate"
        
        # Show certbot logs for debugging
        log_info "Certbot logs:"
        docker-compose run --rm certbot logs || true
        
        exit 1
    fi
}

# Run main function
main "$@"