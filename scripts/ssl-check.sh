#!/bin/bash

# SSL Health Check Script for AppRadio
# Checks SSL certificate status, expiration, and configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_ROOT/.env.production"

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Get domain from environment
get_domain() {
    if [ -f "$ENV_FILE" ]; then
        DOMAIN=$(grep "^DOMAIN=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"' | tr -d "'")
    fi
    
    if [ -z "$DOMAIN" ] || [ "$DOMAIN" = "localhost" ]; then
        DOMAIN="localhost"
        warn "Using localhost for SSL check"
    fi
}

# Check if SSL is enabled
check_ssl_enabled() {
    log "Checking if SSL is enabled..."
    
    if curl -s -I "https://$DOMAIN" >/dev/null 2>&1; then
        log "SSL is enabled and responding"
        return 0
    else
        error "SSL is not responding on https://$DOMAIN"
        return 1
    fi
}

# Check certificate expiration
check_certificate_expiration() {
    log "Checking certificate expiration for $DOMAIN..."
    
    local cert_info
    if cert_info=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null); then
        local not_after=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
        local expiry_epoch=$(date -d "$not_after" +%s 2>/dev/null || date -j -f "%b %d %T %Y %Z" "$not_after" +%s 2>/dev/null)
        local current_epoch=$(date +%s)
        local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        if [ "$days_until_expiry" -gt 30 ]; then
            log "Certificate expires in $days_until_expiry days ($not_after) ✓"
        elif [ "$days_until_expiry" -gt 7 ]; then
            warn "Certificate expires in $days_until_expiry days ($not_after) - Consider renewal"
        else
            error "Certificate expires in $days_until_expiry days ($not_after) - URGENT RENEWAL NEEDED"
        fi
    else
        error "Could not retrieve certificate information"
    fi
}

# Check SSL configuration
check_ssl_configuration() {
    log "Checking SSL configuration..."
    
    # Check TLS version
    local tls_version
    if tls_version=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | grep "Protocol" | head -1); then
        info "TLS Protocol: $tls_version"
    fi
    
    # Check cipher
    local cipher
    if cipher=$(echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | grep "Cipher" | head -1); then
        info "Cipher: $cipher"
    fi
    
    # Check certificate chain
    log "Checking certificate chain..."
    if echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" -verify_return_error >/dev/null 2>&1; then
        log "Certificate chain is valid ✓"
    else
        warn "Certificate chain validation failed"
    fi
}

# Check security headers
check_security_headers() {
    log "Checking security headers..."
    
    local headers
    if headers=$(curl -s -I "https://$DOMAIN" 2>/dev/null); then
        # Check HSTS
        if echo "$headers" | grep -qi "strict-transport-security"; then
            log "HSTS header present ✓"
        else
            warn "HSTS header missing"
        fi
        
        # Check X-Frame-Options
        if echo "$headers" | grep -qi "x-frame-options"; then
            log "X-Frame-Options header present ✓"
        else
            warn "X-Frame-Options header missing"
        fi
        
        # Check X-Content-Type-Options
        if echo "$headers" | grep -qi "x-content-type-options"; then
            log "X-Content-Type-Options header present ✓"
        else
            warn "X-Content-Type-Options header missing"
        fi
        
        # Check CSP
        if echo "$headers" | grep -qi "content-security-policy"; then
            log "Content-Security-Policy header present ✓"
        else
            warn "Content-Security-Policy header missing"
        fi
    else
        error "Could not retrieve headers from https://$DOMAIN"
    fi
}

# Check HTTP to HTTPS redirect
check_http_redirect() {
    log "Checking HTTP to HTTPS redirect..."
    
    local redirect_status
    if redirect_status=$(curl -s -o /dev/null -w "%{http_code}" "http://$DOMAIN" 2>/dev/null); then
        if [ "$redirect_status" = "301" ] || [ "$redirect_status" = "302" ]; then
            log "HTTP to HTTPS redirect working ✓ (Status: $redirect_status)"
        else
            warn "HTTP to HTTPS redirect not working (Status: $redirect_status)"
        fi
    else
        error "Could not check HTTP redirect"
    fi
}

# Check SSL Labs rating (if available)
check_ssl_labs() {
    log "SSL Labs rating check (requires internet connection)..."
    
    # This is a simplified check - full SSL Labs API integration would be more complex
    info "For detailed SSL analysis, visit: https://www.ssllabs.com/ssltest/analyze.html?d=$DOMAIN"
}

# Generate SSL report
generate_report() {
    local report_file="$PROJECT_ROOT/logs/ssl-health-report-$(date +%Y%m%d-%H%M%S).txt"
    
    log "Generating SSL health report..."
    
    {
        echo "SSL Health Report for $DOMAIN"
        echo "Generated: $(date)"
        echo "================================"
        echo ""
        
        echo "Certificate Information:"
        echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | openssl x509 -noout -text 2>/dev/null | head -20
        
        echo ""
        echo "Security Headers:"
        curl -s -I "https://$DOMAIN" 2>/dev/null | grep -E "(strict-transport-security|x-frame-options|x-content-type-options|content-security-policy)" -i
        
        echo ""
        echo "TLS Configuration:"
        echo | openssl s_client -servername "$DOMAIN" -connect "$DOMAIN:443" 2>/dev/null | grep -E "(Protocol|Cipher|Verify)"
        
    } > "$report_file"
    
    log "Report saved to: $report_file"
}

# Quick check
quick_check() {
    get_domain
    
    log "Quick SSL check for $DOMAIN"
    echo "=========================="
    
    if check_ssl_enabled; then
        check_certificate_expiration
        log "Quick check completed ✓"
    else
        error "SSL is not working"
    fi
}

# Full check
full_check() {
    get_domain
    
    log "Full SSL health check for $DOMAIN"
    echo "================================="
    
    check_ssl_enabled
    check_certificate_expiration
    check_ssl_configuration
    check_security_headers
    check_http_redirect
    check_ssl_labs
    
    log "Full SSL health check completed"
}

# Show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  quick          Quick SSL check (certificate and basic connectivity)"
    echo "  full           Full SSL health check (comprehensive)"
    echo "  report         Generate detailed SSL report"
    echo "  expiry         Check certificate expiration only"
    echo "  headers        Check security headers only"
    echo "  help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 quick       # Quick SSL check"
    echo "  $0 full        # Full SSL health check"
    echo "  $0 expiry      # Check certificate expiration"
}

# Main script logic
main() {
    case "${1:-quick}" in
        "quick")
            quick_check
            ;;
        "full")
            full_check
            ;;
        "report")
            get_domain
            full_check
            generate_report
            ;;
        "expiry")
            get_domain
            check_certificate_expiration
            ;;
        "headers")
            get_domain
            check_security_headers
            ;;
        "help"|*)
            show_usage
            ;;
    esac
}

# Run main function
main "$@"
