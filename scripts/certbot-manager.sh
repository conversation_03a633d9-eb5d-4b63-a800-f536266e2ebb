#!/bin/bash

# Certbot Management Script
# Usage: ./certbot-manager.sh [command] [options]

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMPOSE_FILE="$SCRIPT_DIR/docker-compose.yml"
ENV_FILE="$SCRIPT_DIR/.env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Load environment variables
if [ -f "$ENV_FILE" ]; then
    source "$ENV_FILE"
fi

# Default values
DOMAIN=${DOMAIN:-"example.com"}
CERTBOT_EMAIL=${CERTBOT_EMAIL:-"<EMAIL>"}
WEBROOT_PATH="/var/www/certbot"
NGINX_SERVICE="nginx"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

show_help() {
    cat << EOF
Certbot Management Script

Usage: $0 [COMMAND] [OPTIONS]

Commands:
    obtain      Obtain a new SSL certificate
    renew       Renew existing certificates
    revoke      Revoke a certificate
    status      Show certificate status
    test        Test certificate renewal (dry run)
    setup       Initial setup for SSL certificates
    cleanup     Remove all certificates and data

Options:
    -d, --domain DOMAIN     Domain name (default: $DOMAIN)
    -e, --email EMAIL       Email address (default: $CERTBOT_EMAIL)
    -s, --staging           Use Let's Encrypt staging environment
    -f, --force             Force renewal even if not due
    -h, --help              Show this help message

Examples:
    $0 setup -d example.com -e <EMAIL>
    $0 obtain -d example.com,www.example.com
    $0 renew --force
    $0 status
EOF
}

check_prerequisites() {
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose is not installed or not in PATH"
        exit 1
    fi

    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "docker-compose.yml not found at $COMPOSE_FILE"
        exit 1
    fi
}

ensure_directories() {
    log_info "Creating necessary directories..."
    mkdir -p ./docker/certbot/conf
    mkdir -p ./docker/certbot/www
    mkdir -p ./docker/certbot/logs
    mkdir -p ./logs/nginx
}

obtain_certificate() {
    local domains="$1"
    local email="$2"
    local staging="$3"
    
    log_info "Obtaining SSL certificate for: $domains"
    
    # Prepare certbot command
    local certbot_cmd="certbot certonly --webroot --webroot-path=$WEBROOT_PATH"
    certbot_cmd="$certbot_cmd --email $email --agree-tos --no-eff-email"
    
    if [ "$staging" = "true" ]; then
        certbot_cmd="$certbot_cmd --staging"
        log_warning "Using Let's Encrypt staging environment"
    fi
    
    # Add domains
    IFS=',' read -ra DOMAIN_ARRAY <<< "$domains"
    for domain in "${DOMAIN_ARRAY[@]}"; do
        certbot_cmd="$certbot_cmd -d $(echo $domain | xargs)"
    done
    
    # Ensure nginx is running for webroot validation
    log_info "Starting nginx service..."
    docker-compose up -d $NGINX_SERVICE
    
    # Wait for nginx to be ready
    sleep 5
    
    # Run certbot
    log_info "Running certbot..."
    docker-compose run --rm certbot $certbot_cmd
    
    if [ $? -eq 0 ]; then
        log_success "Certificate obtained successfully!"
        log_info "Reloading nginx configuration..."
        docker-compose exec $NGINX_SERVICE nginx -s reload
    else
        log_error "Failed to obtain certificate"
        exit 1
    fi
}

renew_certificates() {
    local force="$1"
    
    log_info "Renewing SSL certificates..."
    
    local renew_cmd="certbot renew --quiet"
    if [ "$force" = "true" ]; then
        renew_cmd="$renew_cmd --force-renewal"
        log_warning "Force renewal enabled"
    fi
    
    docker-compose run --rm certbot $renew_cmd
    
    if [ $? -eq 0 ]; then
        log_success "Certificate renewal completed"
        log_info "Reloading nginx configuration..."
        docker-compose exec $NGINX_SERVICE nginx -s reload || true
    else
        log_error "Certificate renewal failed"
        exit 1
    fi
}

show_status() {
    log_info "Certificate status:"
    docker-compose run --rm certbot certificates
}

test_renewal() {
    log_info "Testing certificate renewal (dry run)..."
    docker-compose run --rm certbot renew --dry-run
    
    if [ $? -eq 0 ]; then
        log_success "Renewal test passed"
    else
        log_error "Renewal test failed"
        exit 1
    fi
}

revoke_certificate() {
    local domain="$1"
    
    if [ -z "$domain" ]; then
        log_error "Domain name required for revocation"
        exit 1
    fi
    
    log_warning "Revoking certificate for: $domain"
    read -p "Are you sure? This action cannot be undone. (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose run --rm certbot revoke --cert-path "/etc/letsencrypt/live/$domain/cert.pem"
        log_success "Certificate revoked for $domain"
    else
        log_info "Revocation cancelled"
    fi
}

setup_ssl() {
    local domain="$1"
    local email="$2"
    local staging="$3"
    
    log_info "Setting up SSL certificates for the first time..."
    
    ensure_directories
    
    # Create initial nginx config without SSL
    log_info "Creating initial HTTP-only nginx configuration..."
    
    # Backup existing config if it exists
    if [ -f "./docker/nginx/default.conf" ]; then
        cp "./docker/nginx/default.conf" "./docker/nginx/default.conf.backup"
        log_info "Backed up existing nginx config"
    fi
    
    # Create temporary HTTP-only config
    cat > "./docker/nginx/default.conf.temp" << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $domain www.$domain;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
    }

    location / {
        proxy_pass http://web:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    cp "./docker/nginx/default.conf.temp" "./docker/nginx/default.conf"
    
    # Start nginx with HTTP-only config
    log_info "Starting nginx with HTTP-only configuration..."
    docker-compose up -d $NGINX_SERVICE
    
    # Obtain certificate
    obtain_certificate "$domain,www.$domain" "$email" "$staging"
    
    # Restore full SSL config
    if [ -f "./docker/nginx/default.conf.backup" ]; then
        mv "./docker/nginx/default.conf.backup" "./docker/nginx/default.conf"
        log_info "Restored full SSL nginx configuration"
    fi
    
    # Restart nginx with SSL config
    docker-compose restart $NGINX_SERVICE
    
    log_success "SSL setup completed successfully!"
}

cleanup() {
    log_warning "This will remove all SSL certificates and certbot data!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Removing certbot data..."
        rm -rf ./docker/certbot/conf/*
        rm -rf ./docker/certbot/www/*
        rm -rf ./docker/certbot/logs/*
        log_success "Cleanup completed"
    else
        log_info "Cleanup cancelled"
    fi
}

# Parse command line arguments
COMMAND=""
DOMAIN_ARG=""
EMAIL_ARG=""
STAGING=false
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        obtain|renew|revoke|status|test|setup|cleanup)
            COMMAND="$1"
            shift
            ;;
        -d|--domain)
            DOMAIN_ARG="$2"
            shift 2
            ;;
        -e|--email)
            EMAIL_ARG="$2"
            shift 2
            ;;
        -s|--staging)
            STAGING=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Use provided arguments or defaults
DOMAIN="${DOMAIN_ARG:-$DOMAIN}"
CERTBOT_EMAIL="${EMAIL_ARG:-$CERTBOT_EMAIL}"

# Check prerequisites
check_prerequisites

# Execute command
case "$COMMAND" in
    obtain)
        obtain_certificate "$DOMAIN" "$CERTBOT_EMAIL" "$STAGING"
        ;;
    renew)
        renew_certificates "$FORCE"
        ;;
    revoke)
        revoke_certificate "$DOMAIN"
        ;;
    status)
        show_status
        ;;
    test)
        test_renewal
        ;;
    setup)
        setup_ssl "$DOMAIN" "$CERTBOT_EMAIL" "$STAGING"
        ;;
    cleanup)
        cleanup
        ;;
    "")
        log_error "No command specified"
        show_help
        exit 1
        ;;
    *)
        log_error "Unknown command: $COMMAND"
        show_help
        exit 1
        ;;
esac