#!/bin/bash

# Production Deployment Script with HTTPS Setup
# This script deploys the application to production and sets up SSL certificates

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
DOMAIN="radiocity.ugapp.net"
EMAIL="<EMAIL>"
STAGING=false  # Set to true for testing

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if we're on the production server
check_production_server() {
    log_info "Checking if this is the production server..."
    
    # Check if domain resolves to this server
    local server_ip=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo "unknown")
    local domain_ip=$(nslookup $DOMAIN | grep -A1 "Name:" | tail -1 | awk '{print $2}' || echo "unknown")
    
    log_info "Server IP: $server_ip"
    log_info "Domain IP: $domain_ip"
    
    if [[ "$server_ip" == "$domain_ip" ]]; then
        log_success "Confirmed: This is the production server for $DOMAIN"
        return 0
    else
        log_warning "This may not be the production server for $DOMAIN"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "Deployment cancelled"
            exit 1
        fi
    fi
}

# Function to deploy application
deploy_application() {
    log_info "Deploying application to production..."
    
    # Build and deploy using the existing script
    ./scripts/docker-build.sh build --target production
    ./scripts/docker-build.sh deploy
    
    log_success "Application deployed successfully"
}

# Function to setup SSL
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    # Run the SSL setup script
    ./scripts/setup-ssl.sh
    
    log_success "SSL setup completed"
}

# Function to verify deployment
verify_deployment() {
    log_info "Verifying production deployment..."
    
    # Wait for services to be ready
    sleep 10
    
    # Test HTTP redirect
    if curl -s -I "http://$DOMAIN" | grep -q "301"; then
        log_success "HTTP to HTTPS redirect working"
    else
        log_warning "HTTP redirect may not be working"
    fi
    
    # Test HTTPS
    if curl -s -k "https://$DOMAIN/health" >/dev/null 2>&1; then
        log_success "HTTPS endpoint responding"
    else
        log_warning "HTTPS endpoint may not be responding"
    fi
    
    # Test certificate
    if openssl s_client -connect $DOMAIN:443 -servername $DOMAIN </dev/null 2>/dev/null | grep -q "Verify return code: 0"; then
        log_success "SSL certificate is valid"
    else
        log_warning "SSL certificate verification failed (may be expected for new certificates)"
    fi
}

# Function to show final status
show_status() {
    log_info "Production deployment status:"
    echo
    echo "🌐 Application URL: https://$DOMAIN"
    echo "🔒 SSL Certificate: Let's Encrypt"
    echo "📊 Health Check: https://$DOMAIN/health"
    echo "📈 Metrics: https://$DOMAIN/metrics"
    echo
    log_info "To check logs: docker compose logs -f"
    log_info "To restart: docker compose restart"
    log_info "To update SSL: ./scripts/setup-ssl.sh"
}

# Main deployment function
main() {
    log_info "Starting production deployment for $DOMAIN"
    
    # Check if we're on the right server
    check_production_server
    
    # Deploy the application
    deploy_application
    
    # Setup SSL certificates
    setup_ssl
    
    # Verify everything is working
    verify_deployment
    
    # Show final status
    show_status
    
    log_success "Production deployment completed successfully! 🎉"
}

# Show help
show_help() {
    echo "Production Deployment Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --staging     Use Let's Encrypt staging environment"
    echo "  --help        Show this help message"
    echo ""
    echo "This script will:"
    echo "  1. Verify this is the production server"
    echo "  2. Deploy the application using docker-build.sh"
    echo "  3. Set up SSL certificates with Let's Encrypt"
    echo "  4. Verify the deployment is working"
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --staging)
            STAGING=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
