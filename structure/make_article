<html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>::-webkit-scrollbar { display: none;}
    
    .word-count-chart {
        height: 40px;
        width: 100%;
    }
    
    .highlight-word {
        background-color: rgba(59, 130, 246, 0.1);
        border-radius: 2px;
    }
    
    .time-marker {
        position: relative;
    }
    
    .time-marker:after {
        content: "";
        position: absolute;
        height: 12px;
        width: 1px;
        background-color: #d1d5db;
        bottom: -16px;
        left: 50%;
    }
    </style>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "primary": {
          "50": "#f0f9ff",
          "100": "#e0f2fe",
          "200": "#bae6fd",
          "300": "#7dd3fc",
          "400": "#38bdf8",
          "500": "#0ea5e9",
          "600": "#0284c7",
          "700": "#0369a1",
          "800": "#075985",
          "900": "#0c4a6e"
        },
        "neutral": {
          "50": "#f9fafb",
          "100": "#f3f4f6",
          "200": "#e5e7eb",
          "300": "#d1d5db",
          "400": "#9ca3af",
          "500": "#6b7280",
          "600": "#4b5563",
          "700": "#374151",
          "800": "#1f2937",
          "900": "#111827"
        }
      },
      "fontFamily": {
        "sans": [
          "Inter",
          "sans-serif"
        ],
        "mono": [
          "Roboto Mono",
          "monospace"
        ]
      }
    }
  }
};</script>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
      body {
        font-family: 'Inter', sans-serif !important;
      }
      
      /* Preserve Font Awesome icons */
      .fa, .fas, .far, .fal, .fab {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
      }
    </style><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style></head>
<body class="font-sans bg-neutral-50 text-neutral-900 h-screen flex flex-col">
    <!-- Header -->
    <header id="header" class="bg-white border-b border-neutral-200 shadow-sm">
        <div class="flex items-center justify-between px-4 py-2">
            <div class="flex items-center">
                <button class="p-2 text-neutral-600 hover:text-primary-600 mr-2">
                    <i class="fa-solid fa-bars"></i>
                </button>
                <div class="flex items-center">
                    <span class="font-semibold text-neutral-800 mr-1">NewsRadio</span>
                    <span class="text-neutral-400">|</span>
                    <span class="ml-1 text-neutral-600">Morning Brief</span>
                </div>
            </div>
            <div class="flex items-center">
                <div class="hidden md:flex items-center mr-4">
                    <span class="text-xs text-neutral-500 mr-2">Last saved: 2 minutes ago</span>
                    <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">Auto-saving</span>
                </div>
                <button class="p-2 text-neutral-600 hover:text-primary-600 mr-2">
                    <i class="fa-solid fa-eye"></i>
                </button>
                <button class="p-2 text-neutral-600 hover:text-primary-600 mr-2">
                    <i class="fa-solid fa-share-nodes"></i>
                </button>
                <button class="hidden md:flex items-center px-4 py-1.5 bg-primary-600 hover:bg-primary-700 text-white rounded text-sm font-medium">
                    <i class="fa-solid fa-microphone mr-2"></i>
                    On Air
                </button>
                <button class="md:hidden p-2 text-neutral-600 hover:text-primary-600">
                    <i class="fa-solid fa-microphone"></i>
                </button>
                <div class="ml-4 relative">
                    <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-2.jpg" alt="User avatar" class="w-8 h-8 rounded-full border border-neutral-200">
                </div>
            </div>
        </div>
        <div class="flex items-center px-4 py-1 bg-white border-t border-neutral-100 text-sm">
            <button class="flex items-center text-neutral-600 hover:text-primary-600 mr-4">
                <i class="fa-solid fa-file-lines mr-1.5"></i>
                File
            </button>
            <button class="flex items-center text-neutral-600 hover:text-primary-600 mr-4">
                <i class="fa-solid fa-pen-to-square mr-1.5"></i>
                Edit
            </button>
            <button class="flex items-center text-neutral-600 hover:text-primary-600 mr-4">
                <i class="fa-solid fa-font mr-1.5"></i>
                Format
            </button>
            <button class="flex items-center text-neutral-600 hover:text-primary-600 mr-4">
                <i class="fa-solid fa-clock mr-1.5"></i>
                Timing
            </button>
            <button class="flex items-center text-neutral-600 hover:text-primary-600">
                <i class="fa-solid fa-gear mr-1.5"></i>
                Settings
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="flex flex-1 overflow-hidden">
        <!-- Editor Area -->
        <div id="editor-area" class="flex-1 flex flex-col overflow-hidden">
            <!-- Toolbar -->
            <div id="editor-toolbar" class="flex items-center justify-between px-4 py-2 bg-white border-b border-neutral-200">
                <div class="flex items-center">
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1">
                        <i class="fa-solid fa-bold"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1">
                        <i class="fa-solid fa-italic"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1">
                        <i class="fa-solid fa-underline"></i>
                    </button>
                    <span class="mx-2 text-neutral-300">|</span>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1">
                        <i class="fa-solid fa-list"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1">
                        <i class="fa-solid fa-list-ol"></i>
                    </button>
                    <span class="mx-2 text-neutral-300">|</span>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1">
                        <i class="fa-solid fa-link"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1">
                        <i class="fa-solid fa-quote-left"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1">
                        <i class="fa-solid fa-music"></i>
                    </button>
                    <span class="mx-2 text-neutral-300">|</span>
                    <button class="flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200">
                        <i class="fa-solid fa-comment-dots mr-1"></i>
                        Add Note
                    </button>
                </div>
                <div class="flex items-center">
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1">
                        <i class="fa-solid fa-undo"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600">
                        <i class="fa-solid fa-redo"></i>
                    </button>
                </div>
            </div>

            <!-- Article Editor -->
            <div id="article-editor" class="flex-1 overflow-y-auto bg-white p-8 mx-auto max-w-3xl">
                <div class="mb-6">
                    <h1 contenteditable="true" class="text-3xl font-bold mb-2 outline-none">California Wildfires: Update on Containment Efforts</h1>
                    <div class="flex items-center text-sm text-neutral-500 mb-4">
                        <span class="mr-3">By John Smith</span>
                        <span>June 26, 2023</span>
                    </div>
                    <div class="flex items-center text-xs bg-neutral-100 rounded-md p-2 mb-4">
                        <span class="px-2 py-0.5 bg-blue-100 text-blue-800 rounded mr-2">BREAKING</span>
                        <span class="px-2 py-0.5 bg-red-100 text-red-800 rounded mr-2">URGENT</span>
                        <span class="text-neutral-600">Air at 07:30, 09:30, 12:30</span>
                    </div>
                </div>

                <div contenteditable="true" class="outline-none text-lg leading-relaxed">
                    <p class="mb-4">Firefighters are making <span class="highlight-word">significant</span> progress in containing the wildfires that have ravaged parts of Northern California for the past week. The largest blaze, dubbed the "Mountain Fire," is now 45% contained, according to state fire officials.</p>
                    
                    <p class="mb-4">Governor Newsom visited affected communities yesterday and announced additional emergency funding of $25 million to support evacuation centers and recovery efforts. <span class="time-marker" data-time="00:30">[30s]</span></p>
                    
                    <p class="mb-4">"We're mobilizing every resource available to ensure both the safety of our residents and the quickest possible containment of these fires," said Newsom during a press conference in Sacramento.</p>
                    
                    <p class="mb-4">The fires have burned over 75,000 acres and destroyed approximately 200 structures since they began last Thursday after a series of lightning strikes ignited dry vegetation. <span class="time-marker" data-time="01:00">[1m]</span></p>
                    
                    <p class="mb-4">Evacuation orders remain in effect for Sonoma and Napa counties, affecting an estimated 15,000 residents. Emergency shelters have been established at local schools and community centers.</p>
                    
                    <p class="mb-4">Meteorologists predict a cooling trend and increased humidity levels over the next 48 hours, which should aid firefighting efforts. However, officials warn that conditions remain dangerous. <span class="time-marker" data-time="01:30">[1m30s]</span></p>
                    
                    <p class="mb-4">Air quality in the Bay Area continues to be poor, with health officials recommending residents limit outdoor activities and wear N95 masks when outside.</p>
                    
                    <p class="mb-4">For NewsRadio, this is John Smith reporting.</p>
                </div>
            </div>

            <!-- Timing and Word Count Bar -->
            <div id="timing-bar" class="bg-white border-t border-neutral-200 py-2 px-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex items-center mr-6">
                            <i class="fa-solid fa-stopwatch text-neutral-500 mr-2"></i>
                            <span class="font-mono text-lg font-medium">01:42</span>
                            <span class="text-xs text-neutral-500 ml-1">/ 02:00 target</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fa-solid fa-font text-neutral-500 mr-2"></i>
                            <span class="font-mono text-lg font-medium">258</span>
                            <span class="text-xs text-neutral-500 ml-1">words</span>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <button class="flex items-center justify-center px-3 py-1.5 bg-neutral-100 hover:bg-neutral-200 text-neutral-700 rounded-l-md text-sm">
                            <i class="fa-solid fa-play mr-1.5"></i>
                            Test Read
                        </button>
                        <button class="flex items-center justify-center px-3 py-1.5 bg-neutral-100 hover:bg-neutral-200 text-neutral-700 border-l border-neutral-200 rounded-r-md text-sm">
                            <i class="fa-solid fa-microphone-lines"></i>
                        </button>
                    </div>
                </div>
                <div class="mt-2 word-count-chart" id="reading-pace-chart"></div>
            </div>
        </div>

        <!-- Right Sidebar -->
        <div id="right-sidebar" class="w-80 bg-white border-l border-neutral-200 overflow-y-auto">
            <div class="border-b border-neutral-200">
                <div class="p-4">
                    <h3 class="font-medium text-neutral-800 mb-3">Story Metadata</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs text-neutral-500 mb-1">Slug</label>
                            <input type="text" value="CA-WILDFIRES-UPDATE" class="w-full px-3 py-1.5 border border-neutral-300 rounded text-sm">
                        </div>
                        <div>
                            <label class="block text-xs text-neutral-500 mb-1">Category</label>
                            <select class="w-full px-3 py-1.5 border border-neutral-300 rounded text-sm bg-white">
                                <option>Breaking News</option>
                                <option>Weather</option>
                                <option>Politics</option>
                                <option>Business</option>
                                <option>Health</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-xs text-neutral-500 mb-1">Target Length</label>
                            <div class="flex">
                                <input type="text" value="2:00" class="w-1/2 px-3 py-1.5 border border-neutral-300 rounded-l text-sm">
                                <select class="w-1/2 px-3 py-1.5 border border-neutral-300 border-l-0 rounded-r text-sm bg-white">
                                    <option>minutes</option>
                                    <option>seconds</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label class="block text-xs text-neutral-500 mb-1">Air Times</label>
                            <div class="flex flex-wrap gap-2">
                                <span class="inline-block px-2 py-1 bg-neutral-100 text-neutral-700 text-xs rounded">07:30</span>
                                <span class="inline-block px-2 py-1 bg-neutral-100 text-neutral-700 text-xs rounded">09:30</span>
                                <span class="inline-block px-2 py-1 bg-neutral-100 text-neutral-700 text-xs rounded">12:30</span>
                                <button class="inline-block px-2 py-1 border border-dashed border-neutral-300 text-neutral-500 text-xs rounded hover:bg-neutral-50">
                                    <i class="fa-solid fa-plus text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-b border-neutral-200">
                <div class="p-4">
                    <h3 class="font-medium text-neutral-800 mb-3">Formatting Tools</h3>
                    <div class="space-y-3">
                        <div class="flex flex-wrap gap-2">
                            <button id="btn-emphasis" class="px-3 py-1.5 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200">
                                Emphasis
                            </button>
                            <button id="btn-pause" class="px-3 py-1.5 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200">
                                Pause
                            </button>
                            <button id="btn-breath" class="px-3 py-1.5 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200">
                                Breath
                            </button>
                            <button id="btn-time" class="px-3 py-1.5 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200">
                                Time Marker
                            </button>
                        </div>
                        <div class="mt-3">
                            <label class="block text-xs text-neutral-500 mb-1">Reading Speed</label>
                            <div class="flex items-center">
                                <span class="text-xs text-neutral-600 mr-2">Slow</span>
                                <input type="range" min="1" max="5" value="3" class="flex-1">
                                <span class="text-xs text-neutral-600 ml-2">Fast</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="block text-xs text-neutral-500 mb-1">Voice Tone</label>
                            <select class="w-full px-3 py-1.5 border border-neutral-300 rounded text-sm bg-white">
                                <option>Neutral</option>
                                <option>Formal</option>
                                <option>Urgent</option>
                                <option>Conversational</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-b border-neutral-200">
                <div class="p-4">
                    <h3 class="font-medium text-neutral-800 mb-3">Pronunciation Guide</h3>
                    <div class="mb-3">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium">Newsom</span>
                            <button class="text-xs text-primary-600 hover:text-primary-700">Edit</button>
                        </div>
                        <div class="text-sm text-neutral-600">NEW-sum</div>
                    </div>
                    <div class="mb-3">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium">Sacramento</span>
                            <button class="text-xs text-primary-600 hover:text-primary-700">Edit</button>
                        </div>
                        <div class="text-sm text-neutral-600">SAK-ruh-MEN-toh</div>
                    </div>
                    <div class="mb-3">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium">Sonoma</span>
                            <button class="text-xs text-primary-600 hover:text-primary-700">Edit</button>
                        </div>
                        <div class="text-sm text-neutral-600">suh-NOH-muh</div>
                    </div>
                    <div class="mb-3">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium">Napa</span>
                            <button class="text-xs text-primary-600 hover:text-primary-700">Edit</button>
                        </div>
                        <div class="text-sm text-neutral-600">NA-puh</div>
                    </div>
                    <button class="flex items-center text-sm text-primary-600 hover:text-primary-700">
                        <i class="fa-solid fa-plus mr-1.5"></i>
                        Add pronunciation
                    </button>
                </div>
            </div>

            <div class="border-b border-neutral-200">
                <div class="p-4">
                    <h3 class="font-medium text-neutral-800 mb-3">Audio Assets</h3>
                    <div class="mb-3 p-2 bg-neutral-50 rounded border border-neutral-200">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium">Newsom Statement</span>
                            <div class="text-xs text-neutral-500">0:12</div>
                        </div>
                        <div class="flex items-center">
                            <button class="p-1 text-neutral-600 hover:text-primary-600 mr-1">
                                <i class="fa-solid fa-play"></i>
                            </button>
                            <div class="flex-1 h-4 bg-neutral-200 rounded-full overflow-hidden">
                                <div class="bg-primary-500 h-full w-3/4"></div>
                            </div>
                        </div>
                        <div class="mt-1 text-xs text-neutral-600 italic">
                            "We're mobilizing every resource available..."
                        </div>
                    </div>
                    <div class="mb-3 p-2 bg-neutral-50 rounded border border-neutral-200">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium">Fire Chief Update</span>
                            <div class="text-xs text-neutral-500">0:18</div>
                        </div>
                        <div class="flex items-center">
                            <button class="p-1 text-neutral-600 hover:text-primary-600 mr-1">
                                <i class="fa-solid fa-play"></i>
                            </button>
                            <div class="flex-1 h-4 bg-neutral-200 rounded-full overflow-hidden">
                                <div class="bg-primary-500 h-full w-1/2"></div>
                            </div>
                        </div>
                        <div class="mt-1 text-xs text-neutral-600 italic">
                            "Containment efforts are progressing..."
                        </div>
                    </div>
                    <button class="flex items-center text-sm text-primary-600 hover:text-primary-700">
                        <i class="fa-solid fa-plus mr-1.5"></i>
                        Add audio clip
                    </button>
                </div>
            </div>

            <div>
                <div class="p-4">
                    <h3 class="font-medium text-neutral-800 mb-3">Export Options</h3>
                    <div class="space-y-3">
                        <button class="flex items-center justify-between w-full px-3 py-2 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200">
                            <span class="flex items-center">
                                <i class="fa-solid fa-file-pdf mr-2 text-red-500"></i>
                                PDF Format
                            </span>
                            <i class="fa-solid fa-arrow-up-right-from-square text-xs"></i>
                        </button>
                        <button class="flex items-center justify-between w-full px-3 py-2 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200">
                            <span class="flex items-center">
                                <i class="fa-solid fa-tv mr-2 text-blue-500"></i>
                                Teleprompter View
                            </span>
                            <i class="fa-solid fa-arrow-up-right-from-square text-xs"></i>
                        </button>
                        <button class="flex items-center justify-between w-full px-3 py-2 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200">
                            <span class="flex items-center">
                                <i class="fa-solid fa-mobile-screen mr-2 text-green-500"></i>
                                Mobile View
                            </span>
                            <i class="fa-solid fa-arrow-up-right-from-square text-xs"></i>
                        </button>
                        <button class="flex items-center justify-between w-full px-3 py-2 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200">
                            <span class="flex items-center">
                                <i class="fa-solid fa-print mr-2 text-purple-500"></i>
                                Print Version
                            </span>
                            <i class="fa-solid fa-arrow-up-right-from-square text-xs"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Bottom Navigation -->
    <div class="md:hidden flex items-center justify-around border-t border-neutral-200 bg-white py-2">
        <button class="flex flex-col items-center p-1">
            <i class="fa-solid fa-file-lines text-neutral-600"></i>
            <span class="text-xs text-neutral-600 mt-1">File</span>
        </button>
        <button class="flex flex-col items-center p-1">
            <i class="fa-solid fa-stopwatch text-neutral-600"></i>
            <span class="text-xs text-neutral-600 mt-1">Timing</span>
        </button>
        <button class="flex flex-col items-center p-1">
            <i class="fa-solid fa-microphone text-primary-600"></i>
            <span class="text-xs text-primary-600 mt-1">Record</span>
        </button>
        <button class="flex flex-col items-center p-1">
            <i class="fa-solid fa-font text-neutral-600"></i>
            <span class="text-xs text-neutral-600 mt-1">Format</span>
        </button>
        <button class="flex flex-col items-center p-1">
            <i class="fa-solid fa-sliders text-neutral-600"></i>
            <span class="text-xs text-neutral-600 mt-1">More</span>
        </button>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize buttons
            document.getElementById('btn-emphasis').addEventListener('click', function() {
                const selection = window.getSelection();
                if (selection.toString().length > 0) {
                    const range = selection.getRangeAt(0);
                    const span = document.createElement('span');
                    span.className = 'highlight-word';
                    range.surroundContents(span);
                }
            });

            document.getElementById('btn-time').addEventListener('click', function() {
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const timeMarker = document.createElement('span');
                    timeMarker.className = 'time-marker';
                    timeMarker.setAttribute('data-time', '00:00');
                    timeMarker.textContent = '[0s]';
                    range.collapse(false);
                    range.insertNode(timeMarker);
                }
            });
        });
    </script>

</body></html>