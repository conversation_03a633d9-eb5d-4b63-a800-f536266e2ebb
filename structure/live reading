<html><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <script> window.FontAwesomeConfig = { autoReplaceSvg: 'nest'};</script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        ::-webkit-scrollbar { display: none; }
        
        .teleprompter-text {
            line-height: 1.8;
            letter-spacing: 0.5px;
            word-spacing: 2px;
        }
        
        .current-line {
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.1) 100%);
            border-left: 4px solid #3b82f6;
            padding-left: 16px;
            margin-left: -20px;
        }
        
        .scroll-container {
            scroll-behavior: smooth;
        }
        
        .pronunciation {
            font-size: 0.75em;
            color: #9ca3af;
            font-style: italic;
        }
        
        .story-break {
            border-top: 3px solid #374151;
            margin: 40px 0;
            padding-top: 40px;
        }
        
        .control-button {
            transition: all 0.1s ease;
        }
        
        .control-button:active {
            transform: scale(0.95);
        }
        
        .live-indicator {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .emergency-stop {
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        }
    </style>
    <script>tailwind.config = {
  "theme": {
    "extend": {
      "colors": {
        "broadcast": {
          "bg": "#000000",
          "text": "#ffffff",
          "control": "#1f2937",
          "live": "#ef4444",
          "breaking": "#f59e0b",
          "local": "#3b82f6",
          "traffic": "#f97316",
          "weather": "#10b981",
          "sports": "#8b5cf6"
        }
      },
      "fontFamily": {
        "broadcast": [
          "Roboto",
          "Arial",
          "sans-serif"
        ],
        "sans": [
          "Inter",
          "sans-serif"
        ]
      }
    }
  }
};</script>
<link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin=""><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&amp;display=swap"><style>
      body {
        font-family: 'Inter', sans-serif !important;
      }
      
      /* Preserve Font Awesome icons */
      .fa, .fas, .far, .fal, .fab {
        font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands" !important;
      }
    </style><style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    display: none;
  }

  html, body {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  </style></head>
<body class="font-broadcast bg-broadcast-bg text-broadcast-text h-screen flex flex-col overflow-hidden">
    <!-- Status Bar -->
    <div id="status-bar" class="flex items-center justify-between px-6 py-2 bg-broadcast-control border-b border-gray-700">
        <div class="flex items-center">
            <div class="flex items-center mr-8">
                <div class="w-3 h-3 bg-broadcast-live rounded-full live-indicator mr-2"></div>
                <span class="text-lg font-semibold">WXYZ NewsRadio</span>
            </div>
            <span class="text-gray-300">Morning News Brief</span>
        </div>
        <div class="flex items-center space-x-6">
            <div class="text-right">
                <div class="text-sm text-gray-400">Segment Time</div>
                <div class="text-xl font-mono font-bold" id="segment-timer">04:30</div>
            </div>
            <div class="text-right">
                <div class="text-sm text-gray-400">Current Time</div>
                <div class="text-xl font-mono font-bold" id="current-time">7:15:42 AM</div>
            </div>
            <button class="p-2 text-gray-400 hover:text-white" id="settings-btn">
                <i class="fa-solid fa-cog text-lg"></i>
            </button>
        </div>
    </div>

    <!-- Main Teleprompter Area -->
    <div id="teleprompter-main" class="flex-1 relative overflow-hidden">
        <!-- Reading Content -->
        <div id="scroll-container" class="h-full overflow-y-auto scroll-container px-16 py-12">
            <div class="max-w-4xl mx-auto">
                <!-- Story 1 -->
                <div class="story-item" data-story="1">
                    <div class="flex items-center mb-6">
                        <span class="px-4 py-2 bg-broadcast-breaking text-black font-bold text-lg rounded-md mr-4">BREAKING NEWS</span>
                        <span class="text-2xl text-gray-300">Story 1 of 5</span>
                    </div>
                    <h1 class="text-5xl font-bold mb-8 teleprompter-text">California Wildfires: Emergency Evacuations Ordered</h1>
                    <div class="teleprompter-text text-3xl leading-relaxed space-y-6">
                        <p class="current-line">Good morning, this is breaking news from Northern California where emergency officials have ordered immediate evacuations for three counties as wildfires continue to spread rapidly overnight.</p>
                        <p>The largest blaze, now called the Mountain Fire, has burned over seventy-five thousand acres since lightning strikes ignited dry vegetation last Thursday. Governor Newsom <span class="pronunciation">(NEW-sum)</span> visited affected areas yesterday.</p>
                        <p>"We're mobilizing every resource available to ensure both the safety of our residents and the quickest possible containment of these fires," the Governor said during a press conference in Sacramento <span class="pronunciation">(SAK-ruh-MEN-toh)</span>.</p>
                        <p>Evacuation orders are now in effect for Sonoma <span class="pronunciation">(suh-NOH-muh)</span> and Napa <span class="pronunciation">(NA-puh)</span> counties, affecting an estimated fifteen thousand residents.</p>
                    </div>
                </div>

                <!-- Story 2 -->
                <div class="story-item story-break" data-story="2">
                    <div class="flex items-center mb-6">
                        <span class="px-4 py-2 bg-broadcast-traffic text-white font-bold text-lg rounded-md mr-4">TRAFFIC</span>
                        <span class="text-2xl text-gray-300">Story 2 of 5</span>
                    </div>
                    <h1 class="text-5xl font-bold mb-8 teleprompter-text">Highway 101 Closure Impacts Morning Commute</h1>
                    <div class="teleprompter-text text-3xl leading-relaxed space-y-6">
                        <p>Traffic update now - Highway 101 southbound remains closed between Mill Valley and San Rafael due to a multi-vehicle accident that occurred at six-thirty this morning.</p>
                        <p>The California Highway Patrol reports three vehicles were involved in the collision, with two people transported to Marin General Hospital with non-life-threatening injuries.</p>
                        <p>Commuters should use Highway 280 or surface streets as alternate routes. The closure is expected to last until nine AM while crews clear the scene and investigate.</p>
                    </div>
                </div>

                <!-- Story 3 -->
                <div class="story-item story-break" data-story="3">
                    <div class="flex items-center mb-6">
                        <span class="px-4 py-2 bg-broadcast-weather text-white font-bold text-lg rounded-md mr-4">WEATHER</span>
                        <span class="text-2xl text-gray-300">Story 3 of 5</span>
                    </div>
                    <h1 class="text-5xl font-bold mb-8 teleprompter-text">Heat Wave Continues Through Weekend</h1>
                    <div class="teleprompter-text text-3xl leading-relaxed space-y-6">
                        <p>The Bay Area heat wave shows no signs of letting up, with temperatures expected to reach one hundred degrees inland today and ninety-five degrees along the coast.</p>
                        <p>The National Weather Service has extended the excessive heat warning through Sunday evening. Cooling centers are open in San Francisco, Oakland, and San Jose.</p>
                        <p>Health officials remind residents to stay hydrated, avoid outdoor activities during peak hours, and check on elderly neighbors and relatives.</p>
                    </div>
                </div>

                <!-- Story 4 -->
                <div class="story-item story-break" data-story="4">
                    <div class="flex items-center mb-6">
                        <span class="px-4 py-2 bg-broadcast-local text-white font-bold text-lg rounded-md mr-4">LOCAL</span>
                        <span class="text-2xl text-gray-300">Story 4 of 5</span>
                    </div>
                    <h1 class="text-5xl font-bold mb-8 teleprompter-text">City Council Approves New Housing Development</h1>
                    <div class="teleprompter-text text-3xl leading-relaxed space-y-6">
                        <p>The San Francisco City Council voted seven to four last night to approve a controversial housing development in the Mission District.</p>
                        <p>The project will bring four hundred new affordable housing units to the area, but opponents say it will increase traffic and strain local resources.</p>
                        <p>Construction is expected to begin next spring and take approximately three years to complete.</p>
                    </div>
                </div>

                <!-- Story 5 -->
                <div class="story-item story-break" data-story="5">
                    <div class="flex items-center mb-6">
                        <span class="px-4 py-2 bg-broadcast-sports text-white font-bold text-lg rounded-md mr-4">SPORTS</span>
                        <span class="text-2xl text-gray-300">Story 5 of 5</span>
                    </div>
                    <h1 class="text-5xl font-bold mb-8 teleprompter-text">Giants Win Extends Winning Streak</h1>
                    <div class="teleprompter-text text-3xl leading-relaxed space-y-6">
                        <p>The San Francisco Giants defeated the Los Angeles Dodgers eight to five last night, extending their winning streak to seven games.</p>
                        <p>Third baseman Matt Chapman hit two home runs and drove in four RBIs in the victory at Oracle Park.</p>
                        <p>The Giants now sit just two games behind the Dodgers in the National League West standings with fifteen games remaining in the regular season.</p>
                        <p>That's your NewsRadio morning brief. I'm Sarah Johnson. We'll be back with traffic and weather on the eights.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reading Position Indicator -->
        <div id="position-indicator" class="absolute left-0 top-1/2 w-1 h-16 bg-broadcast-live opacity-80 transform -translate-y-1/2"></div>
    </div>

    <!-- Control Panel -->
    <div id="control-panel" class="bg-broadcast-control border-t border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
            <!-- Left Controls -->
            <div class="flex items-center space-x-4">
                <!-- Auto-Scroll Control -->
                <button id="scroll-toggle" class="control-button flex items-center justify-center w-16 h-16 bg-blue-600 hover:bg-blue-700 rounded-full text-white text-2xl">
                    <i class="fa-solid fa-play" id="scroll-icon"></i>
                </button>
                
                <!-- Story Navigation -->
                <div class="flex items-center space-x-2">
                    <button id="prev-story" class="control-button flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-500 rounded-lg text-white">
                        <i class="fa-solid fa-chevron-left text-lg"></i>
                    </button>
                    <div class="px-4 py-2 bg-gray-700 rounded-lg">
                        <span class="text-lg font-bold" id="story-counter">1 of 5</span>
                    </div>
                    <button id="next-story" class="control-button flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-500 rounded-lg text-white">
                        <i class="fa-solid fa-chevron-right text-lg"></i>
                    </button>
                </div>

                <!-- Speed Controls -->
                <div class="flex items-center space-x-2">
                    <button id="speed-down" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-minus"></i>
                    </button>
                    <div class="px-3 py-2 bg-gray-700 rounded">
                        <span class="text-sm font-mono" id="speed-display">1.0x</span>
                    </div>
                    <button id="speed-up" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-plus"></i>
                    </button>
                </div>
            </div>

            <!-- Center Status -->
            <div class="flex items-center space-x-8">
                <!-- Mic Status -->
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-broadcast-live rounded-full live-indicator mr-3"></div>
                    <span class="text-xl font-bold text-broadcast-live">ON AIR</span>
                </div>

                <!-- Story Timer -->
                <div class="text-center">
                    <div class="text-sm text-gray-400">Story Time</div>
                    <div class="text-2xl font-mono font-bold" id="story-timer">0:45</div>
                </div>
            </div>

            <!-- Right Controls -->
            <div class="flex items-center space-x-4">
                <!-- Font Size -->
                <div class="flex items-center space-x-2">
                    <button id="font-down" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-text-height"></i>
                    </button>
                    <div class="px-3 py-2 bg-gray-700 rounded">
                        <span class="text-sm" id="font-size">32px</span>
                    </div>
                    <button id="font-up" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-text-height"></i>
                    </button>
                </div>

                <!-- Emergency Stop -->
                <button id="emergency-stop" class="control-button emergency-stop flex items-center justify-center w-16 h-16 bg-red-600 hover:bg-red-700 rounded-full text-white text-2xl">
                    <i class="fa-solid fa-stop"></i>
                </button>

                <!-- Quick Story Jump -->
                <div class="flex space-x-1">
                    <button class="story-jump control-button w-8 h-8 bg-gray-600 hover:bg-gray-500 rounded text-sm font-bold" data-story="1">1</button>
                    <button class="story-jump control-button w-8 h-8 bg-gray-600 hover:bg-gray-500 rounded text-sm font-bold" data-story="2">2</button>
                    <button class="story-jump control-button w-8 h-8 bg-gray-600 hover:bg-gray-500 rounded text-sm font-bold" data-story="3">3</button>
                    <button class="story-jump control-button w-8 h-8 bg-gray-600 hover:bg-gray-500 rounded text-sm font-bold" data-story="4">4</button>
                    <button class="story-jump control-button w-8 h-8 bg-blue-600 hover:bg-blue-700 rounded text-sm font-bold" data-story="5">5</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Panel (Hidden by default) -->
    <div id="settings-panel" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="absolute right-0 top-0 h-full w-96 bg-broadcast-control border-l border-gray-700 p-6 overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold">Broadcast Settings</h2>
                <button id="close-settings" class="text-gray-400 hover:text-white text-xl">
                    <i class="fa-solid fa-times"></i>
                </button>
            </div>

            <div class="space-y-6">
                <!-- Theme -->
                <div>
                    <h3 class="text-lg font-semibold mb-3">Theme</h3>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="theme" value="dark" checked="" class="mr-3">
                            <span>Dark (Studio)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="theme" value="light" class="mr-3">
                            <span>Light (Backup)</span>
                        </label>
                    </div>
                </div>

                <!-- Font Settings -->
                <div>
                    <h3 class="text-lg font-semibold mb-3">Font Settings</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-400 mb-1">Base Font Size</label>
                            <input type="range" min="24" max="48" value="32" class="w-full" id="font-range">
                            <div class="text-sm text-gray-400 mt-1">Current: <span id="font-value">32px</span></div>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-400 mb-1">Line Height</label>
                            <input type="range" min="1.4" max="2.2" step="0.1" value="1.8" class="w-full" id="line-height-range">
                        </div>
                    </div>
                </div>

                <!-- Scroll Settings -->
                <div>
                    <h3 class="text-lg font-semibold mb-3">Auto-Scroll</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm text-gray-400 mb-1">Default Speed</label>
                            <input type="range" min="0.5" max="3" step="0.1" value="1" class="w-full" id="scroll-speed-range">
                            <div class="text-sm text-gray-400 mt-1">Current: <span id="scroll-speed-value">1.0x</span></div>
                        </div>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-3" id="smooth-scroll">
                            <span>Extra Smooth Scrolling</span>
                        </label>
                    </div>
                </div>

                <!-- Keyboard Shortcuts -->
                <div>
                    <h3 class="text-lg font-semibold mb-3">Keyboard Shortcuts</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Play/Pause</span>
                            <span class="font-mono bg-gray-700 px-2 py-1 rounded">SPACE</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Next Story</span>
                            <span class="font-mono bg-gray-700 px-2 py-1 rounded">→</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Previous Story</span>
                            <span class="font-mono bg-gray-700 px-2 py-1 rounded">←</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Emergency Stop</span>
                            <span class="font-mono bg-gray-700 px-2 py-1 rounded">ESC</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStory = 1;
        let totalStories = 5;
        let isScrolling = false;
        let scrollSpeed = 1;
        let fontSize = 32;
        let storyStartTime = Date.now();
        let segmentStartTime = Date.now();

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateStoryCounter();
            updateClock();
            setInterval(updateClock, 1000);
            setInterval(updateTimers, 100);
            setupKeyboardShortcuts();
        });

        // Auto-scroll functionality
        document.getElementById('scroll-toggle').addEventListener('click', function() {
            isScrolling = !isScrolling;
            const icon = document.getElementById('scroll-icon');
            
            if (isScrolling) {
                icon.className = 'fa-solid fa-pause';
                startAutoScroll();
            } else {
                icon.className = 'fa-solid fa-play';
                stopAutoScroll();
            }
        });

        function startAutoScroll() {
            const container = document.getElementById('scroll-container');
            const scrollInterval = setInterval(() => {
                if (!isScrolling) {
                    clearInterval(scrollInterval);
                    return;
                }
                container.scrollTop +=</script></body></html>