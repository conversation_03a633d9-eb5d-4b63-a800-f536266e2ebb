# Template Debugging Guide

This guide helps debug common Django template issues that can cause templates to display raw template syntax instead of rendered content.

## Common Symptoms

When templates are not rendering correctly, you might see:
- Raw template syntax like `{{ variable.method }}` displayed on the page
- Template tags like `{% if condition %}` showing as text
- Missing content or broken layouts

## Common Causes & Solutions

### 1. **Malformed Template Tags**

**Problem**: Missing `%` in template tags
```django
❌ WRONG: {% if condition %} content {% else %} other {% endif %}
✅ CORRECT: {% if condition %} content {% else %} other {% endif %}
```

**Problem**: Unclosed template tags
```django
❌ WRONG: {% if condition %} content
✅ CORRECT: {% if condition %} content {% endif %}
```

### 2. **Template Syntax Validation**

Use Django's template validation to catch errors:

```python
# In Django shell
from django.template import Template, Context
from django.template.loader import get_template

try:
    template = get_template('your_template.html')
    print("✅ Template syntax is valid")
except Exception as e:
    print(f"❌ Template error: {e}")
```

### 3. **Context Issues**

**Problem**: Missing variables in template context
```python
# Make sure view passes all required variables
context = {
    'users': users_list,
    'current_organization': org,
    # Don't forget any variables used in template
}
```

**Problem**: Incorrect variable access
```django
❌ WRONG: {{ user.membership.role }} (if membership doesn't exist)
✅ CORRECT: {{ user.membership.role|default:"No Role" }}
```

### 4. **Method Calls in Templates**

**Problem**: Using parentheses for method calls
```django
❌ WRONG: {{ user.get_full_name() }}
✅ CORRECT: {{ user.get_full_name }}
```

**Problem**: Missing fallbacks for method calls
```django
❌ RISKY: {{ user.membership.get_role_display }}
✅ SAFE: {{ user.membership.get_role_display|default:"Unknown" }}
```

## Debugging Steps

### Step 1: Check Template Syntax
1. Look for malformed template tags (`{%`, `%}` mismatches)
2. Ensure all `{% if %}` have matching `{% endif %}`
3. Check for proper `{% else %}` syntax

### Step 2: Validate Template Loading
```python
python manage.py shell -c "
from django.template.loader import get_template
try:
    template = get_template('your_template.html')
    print('Template loads successfully')
except Exception as e:
    print(f'Template error: {e}')
"
```

### Step 3: Test Context Variables
```python
# Add debug output to your view
def your_view(request):
    context = {'users': users}
    
    # Debug: Print context variables
    for user in users:
        if hasattr(user, 'organization_membership'):
            print(f"User {user.username}: {user.organization_membership.get_role_display()}")
    
    return render(request, 'template.html', context)
```

### Step 4: Check Server Logs
- Look for template errors in Django's debug output
- Check for 500 errors that might indicate template issues
- Watch for template reloading messages

## Prevention Tips

### 1. **Use Template Linting**
Consider using template linting tools to catch syntax errors early.

### 2. **Add Fallbacks**
Always provide fallbacks for optional data:
```django
{{ user.profile.bio|default:"No bio available" }}
{{ user.membership.get_role_display|default:"No role" }}
```

### 3. **Test Template Changes**
- Test templates in development before deploying
- Use automated tests for critical template functionality
- Check templates after making changes

### 4. **Use Safe Navigation**
```django
{% if user.membership %}
    {{ user.membership.get_role_display }}
{% else %}
    No membership
{% endif %}
```

## Example: User Role Display Fix

**Problem**: Roles showing as raw template syntax

**Root Cause**: Malformed template tag
```django
❌ BROKEN: {% if user.last_login %} date {% else %} Never {% endif %}
```

**Solution**: Fix template syntax
```django
✅ FIXED: {% if user.last_login %} date {% else %} Never {% endif %}
```

**Result**: Template renders correctly and shows actual role names

## Testing Template Fixes

```python
# Test template rendering
from django.test import Client
from django.contrib.auth.models import User

client = Client()
user = User.objects.first()
client.force_login(user)

response = client.get('/your-page/')
content = response.content.decode('utf-8')

# Check for raw template syntax
if '{{' in content or '{%' in content:
    print("❌ Raw template syntax found")
else:
    print("✅ Template rendering correctly")
```

## Quick Checklist

When templates aren't rendering:

- [ ] Check for malformed template tags (`{%`, `%}`)
- [ ] Verify all `{% if %}` have matching `{% endif %}`
- [ ] Ensure `{% else %}` tags are properly formed
- [ ] Check that all variables exist in template context
- [ ] Add fallbacks for optional data (`|default:"fallback"`)
- [ ] Test template loading in Django shell
- [ ] Check server logs for template errors
- [ ] Verify template file permissions and location

Following this guide should help quickly identify and fix template rendering issues.
