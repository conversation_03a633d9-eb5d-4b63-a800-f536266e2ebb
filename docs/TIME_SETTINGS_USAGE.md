# Time Settings Usage Guide

This guide explains how to use the new time formatting settings in the RadioMention application.

## Overview

The application now supports user-specific time and date formatting preferences, including:

- **Time Format**: 12-hour (AM/PM) or 24-hour format
- **Date Format**: Multiple formats including US, European, ISO, and descriptive formats
- **Timezone**: User-specific timezone settings
- **Language**: Interface language preference

## User Preferences Model

The `UserPreferences` model now includes these fields:

```python
# Time and localization preferences
timezone = models.CharField(max_length=50, default='UTC')
date_format = models.CharField(max_length=20, default='MM/DD/YYYY')
time_format = models.CharField(max_length=10, default='12')
language = models.Char<PERSON>ield(max_length=10, default='en')
```

## Available Options

### Time Format
- `'12'`: 12-hour format with AM/PM (e.g., "2:30 PM")
- `'24'`: 24-hour format (e.g., "14:30")

### Date Format
- `'MM/DD/YYYY'`: US format (e.g., "01/15/2024")
- `'DD/MM/YYYY'`: European format (e.g., "15/01/2024")
- `'YYYY-MM-DD'`: ISO format (e.g., "2024-01-15")
- `'MMM DD, YYYY'`: Descriptive format (e.g., "Jan 15, 2024")
- `'DD MMM YYYY'`: European descriptive (e.g., "15 Jan 2024")

### Timezone
- `'UTC'`: Coordinated Universal Time
- `'Europe/Moscow'`: Moscow Time (UTC+3)
- `'Europe/Istanbul'`: Turkey Time (UTC+3)
- `'Africa/Nairobi'`: East Africa Time (UTC+3)
- `'Asia/Riyadh'`: Arabia Standard Time (UTC+3)
- `'Europe/Athens'`: Eastern European Time (UTC+2/+3)
- `'Europe/London'`: Greenwich Mean Time (UTC+0/+1)
- `'Europe/Paris'`: Central European Time (UTC+1/+2)
- `'America/New_York'`: Eastern Time (UTC-5/-4)
- `'America/Chicago'`: Central Time (UTC-6/-5)
- `'America/Denver'`: Mountain Time (UTC-7/-6)
- `'America/Los_Angeles'`: Pacific Time (UTC-8/-7)
- `'America/Anchorage'`: Alaska Time (UTC-9/-8)
- `'Pacific/Honolulu'`: Hawaii Time (UTC-10)
- `'Asia/Tokyo'`: Japan Standard Time (UTC+9)
- `'Australia/Sydney'`: Australian Eastern Time (UTC+10/+11)

## Using in Templates

### Load the Template Tags

```django
{% load time_filters %}
```

### Format Time

```django
<!-- Format time according to user preference -->
{{ mention.scheduled_time|format_time_user:user }}

<!-- Format datetime -->
{{ mention.created_at|format_datetime_user:user }}

<!-- Format date only -->
{{ mention.scheduled_date|format_date_user:user }}
```

### Get User Preferences

```django
<!-- Get user's timezone -->
{% user_timezone as user_tz %}
<p>Your timezone: {{ user_tz }}</p>

<!-- Get user's time format -->
{% user_time_format as time_fmt %}
<p>Time format: {% if time_fmt == '12' %}12-hour{% else %}24-hour{% endif %}</p>

<!-- Get user's date format -->
{% user_date_format as date_fmt %}
<p>Date format: {{ date_fmt }}</p>
```

## Using in Python Code

### Import the Service

```python
from apps.settings.services import TimeFormattingService
```

### Format Time in Views

```python
def my_view(request):
    # Get current time
    current_time = timezone.now()
    
    # Format according to user preferences
    formatted_time = TimeFormattingService.format_time(
        current_time.time(), 
        user=request.user
    )
    
    formatted_datetime = TimeFormattingService.format_datetime(
        current_time, 
        user=request.user
    )
    
    formatted_date = TimeFormattingService.format_date(
        current_time.date(), 
        user=request.user
    )
    
    context = {
        'formatted_time': formatted_time,
        'formatted_datetime': formatted_datetime,
        'formatted_date': formatted_date,
    }
    return render(request, 'my_template.html', context)
```

### Convert to User Timezone

```python
def my_view(request):
    # Convert UTC datetime to user's timezone
    utc_datetime = timezone.now()
    user_datetime = TimeFormattingService.convert_to_user_timezone(
        utc_datetime, 
        user=request.user
    )
    
    # Get user's timezone string
    user_tz = TimeFormattingService.get_user_timezone(user=request.user)
    
    return render(request, 'template.html', {
        'user_datetime': user_datetime,
        'user_timezone': user_tz,
    })
```

## Settings Configuration

Users can configure their time preferences by visiting:
- **Settings Dashboard**: `/settings/`
- **User Preferences**: `/settings/preferences/`

The preferences are automatically saved and applied across the application.

## Migration

The new fields have been added via migration:
```bash
python manage.py makemigrations settings
python manage.py migrate settings
```

## Examples

### Template Example

```django
{% load time_filters %}

<div class="mention-card">
    <h3>{{ mention.client.name }}</h3>
    <p>Scheduled: {{ mention.scheduled_date|format_date_user:user }} at {{ mention.scheduled_time|format_time_user:user }}</p>
    <p>Created: {{ mention.created_at|format_datetime_user:user }}</p>
    
    {% user_timezone as tz %}
    <small>Times shown in {{ tz }}</small>
</div>
```

### View Example

```python
from apps.settings.services import TimeFormattingService

def mention_detail(request, pk):
    mention = get_object_or_404(Mention, pk=pk)
    
    # Format times according to user preferences
    scheduled_time_formatted = TimeFormattingService.format_time(
        mention.scheduled_time, 
        user=request.user
    )
    
    created_datetime_formatted = TimeFormattingService.format_datetime(
        mention.created_at, 
        user=request.user
    )
    
    context = {
        'mention': mention,
        'scheduled_time_formatted': scheduled_time_formatted,
        'created_datetime_formatted': created_datetime_formatted,
    }
    return render(request, 'mentions/detail.html', context)
```

This system ensures consistent time formatting across the entire application based on user preferences.
