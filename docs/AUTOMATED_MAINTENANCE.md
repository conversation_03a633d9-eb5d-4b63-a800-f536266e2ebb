# Automated Database Maintenance

This document describes the automated database maintenance system implemented for production safety and optimal performance.

## Overview

The system provides multiple layers of database maintenance:

1. **Safe Daily Operations** - Non-blocking statistics updates
2. **Automated Reindexing** - Smart scheduling during low-traffic hours
3. **Manual Maintenance** - Controlled reindexing during maintenance windows
4. **Monitoring & Alerts** - Comprehensive logging and notifications

## Automated Tasks Schedule

### Daily Tasks (Safe Operations)
- **Database ANALYZE**: Updates query statistics without table locks
- **Schedule**: Every day at various times
- **Duration**: 1-5 minutes
- **Impact**: None (no table locks)

### Weekly Tasks (Automated Reindexing)
- **Automated Reindex**: Full database reindexing with safety checks
- **Schedule**: Monday 3:00 AM
- **Duration**: 5-30 minutes depending on database size
- **Impact**: Temporary table locks during reindexing

### Monthly Tasks (Deep Maintenance)
- **Deep Reindex**: Comprehensive maintenance on 1st of each month
- **Schedule**: 1st day of month at 2:30 AM
- **Duration**: 10-60 minutes depending on database size
- **Impact**: Temporary table locks during reindexing

## Safety Features

### Time-Based Safety
- **Maintenance Window**: 2:00 AM - 6:00 AM server time
- **Load Checking**: Skips if >5 active database connections
- **Timeout Protection**: 1-hour maximum execution time

### Automatic Safeguards
- Database connectivity verification
- Active connection monitoring
- Resource usage checks
- Comprehensive error handling and logging

### Fallback Mechanisms
- Automatic retry logic with exponential backoff
- Graceful degradation if maintenance fails
- Detailed logging for troubleshooting

## Manual Operations

### Testing Automated Reindex
```bash
# Test during maintenance window
python manage.py test_automated_reindex

# Force test outside maintenance window
python manage.py test_automated_reindex --force

# Dry run to see what would happen
python manage.py test_automated_reindex --dry-run

# Run asynchronously
python manage.py test_automated_reindex --async
```

### Manual Maintenance Script
```bash
# Interactive mode with all safety checks
./scripts/maintenance-reindex.sh

# Automated mode (skip confirmations)
./scripts/maintenance-reindex.sh --automated

# Skip backup (if recent backup exists)
./scripts/maintenance-reindex.sh --no-backup

# Enable maintenance mode during reindex
./scripts/maintenance-reindex.sh --maintenance-mode
```

### Direct Database Commands
```bash
# Safe statistics update (no locks)
python manage.py optimize_database --analyze-queries

# Full reindexing (locks tables)
python manage.py optimize_database --reindex

# Comprehensive analysis
python manage.py optimize_database --all
```

## Monitoring

### Activity Logs
All maintenance operations are logged in the ActivityLog model:
- Execution times and durations
- Success/failure status
- Database load at execution time
- Detailed error messages if failures occur

### Log Levels
- **INFO**: Successful operations
- **WARNING**: Skipped operations (outside window, high load)
- **ERROR**: Failed operations with error details

### Celery Task Monitoring
```bash
# Check active tasks
docker-compose exec celery celery -A radio_mentions_project inspect active

# Check scheduled tasks
docker-compose exec celery celery -A radio_mentions_project inspect scheduled

# Check task history
docker-compose exec celery celery -A radio_mentions_project events
```

## Configuration

### Environment Variables
```bash
# Maintenance window (hours)
MAINTENANCE_WINDOW_START=2
MAINTENANCE_WINDOW_END=6

# Connection threshold for load checking
MAX_ACTIVE_CONNECTIONS=5

# Task timeouts
REINDEX_TIMEOUT=3600  # 1 hour
```

### Celery Beat Schedule
The schedule is defined in `radio_mentions_project/celery.py`:
- Modify timing by changing crontab expressions
- Adjust timeouts in task options
- Enable/disable tasks by commenting out entries

## Troubleshooting

### Common Issues

1. **Task Skipped - Outside Maintenance Window**
   - Check server timezone settings
   - Verify maintenance window configuration
   - Use `--force` flag for testing

2. **Task Skipped - High Database Load**
   - Check active connections: `SELECT * FROM pg_stat_activity;`
   - Wait for lower traffic period
   - Adjust `MAX_ACTIVE_CONNECTIONS` threshold

3. **Task Timeout**
   - Check database size and performance
   - Increase timeout in celery configuration
   - Consider manual maintenance during dedicated window

4. **Script Not Found**
   - Verify script path: `scripts/maintenance-reindex.sh`
   - Check file permissions: `chmod +x scripts/maintenance-reindex.sh`
   - Ensure script is in Docker container

### Debugging Commands
```bash
# Check task status
python manage.py test_automated_reindex --dry-run

# View recent activity logs
python manage.py shell -c "
from apps.activity_logs.models import ActivityLog
logs = ActivityLog.objects.filter(action__contains='reindex').order_by('-created_at')[:10]
for log in logs: print(f'{log.created_at}: {log.action} - {log.description}')
"

# Check database health
python manage.py optimize_database --connection-info --table-sizes
```

## Best Practices

1. **Monitor First Week**: Watch logs closely after deployment
2. **Backup Strategy**: Ensure regular backups before maintenance
3. **Load Testing**: Test during actual low-traffic periods
4. **Gradual Rollout**: Start with weekly, add monthly after validation
5. **Alert Setup**: Configure monitoring alerts for failed tasks

## Deployment

To deploy the automated maintenance system:

```bash
# Deploy code changes
git add .
git commit -m "Add automated database maintenance system"
git push origin main
./scripts/deploy-production.sh

# Restart Celery services
docker-compose restart celery celery-beat

# Verify schedule
docker-compose exec celery celery -A radio_mentions_project inspect scheduled
```

## Performance Impact

### Expected Performance Improvements
- **Query Speed**: 10-30% improvement after reindexing
- **Index Efficiency**: Reduced fragmentation
- **Statistics Accuracy**: Better query planning

### Monitoring Metrics
- Query execution times
- Index usage statistics
- Database connection counts
- Task execution durations

The automated maintenance system ensures optimal database performance while maintaining production safety and reliability.
