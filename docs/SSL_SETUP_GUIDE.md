# SSL Setup Guide for AppRadio

This guide covers SSL/TLS certificate setup for the AppRadio application using Docker and Nginx.

## Overview

The SSL configuration supports three modes:
1. **Let's Encrypt** (Recommended for production)
2. **Self-signed certificates** (For testing/development)
3. **HTTP-only mode** (Not recommended for production)

## Prerequisites

- Docker and Docker Compose installed
- Domain name pointing to your server (for Let's Encrypt)
- Ports 80 and 443 open on your server
- AppRadio application configured and running

## Quick Start

### 1. Production Setup with Let's Encrypt

```bash
# 1. Configure your domain
cp .env.production.example .env.production
# Edit .env.production and set your DOMAIN

# 2. Setup SSL certificates
./scripts/setup-ssl.sh letsencrypt

# 3. Verify SSL is working
curl -I https://your-domain.com
```

### 2. Development Setup with Self-Signed Certificates

```bash
# Setup self-signed certificates
./scripts/setup-ssl.sh self-signed

# Access with security warning
https://localhost
```

### 3. HTTP-Only Mode (Not Recommended)

```bash
# Disable SSL (for development only)
./scripts/setup-ssl.sh http-only
```

## Detailed Configuration

### Let's Encrypt Setup

Let's Encrypt provides free, automated SSL certificates that are trusted by all major browsers.

#### Requirements
- Valid domain name
- Domain DNS pointing to your server
- Ports 80 and 443 accessible from the internet

#### Setup Process

1. **Configure Domain**
   ```bash
   # Edit .env.production
   DOMAIN=your-domain.com
   SSL_EMAIL=<EMAIL>
   ```

2. **Run Setup Script**
   ```bash
   ./scripts/setup-ssl.sh letsencrypt
   ```

3. **Verify Certificate**
   ```bash
   ./scripts/setup-ssl.sh status
   ```

#### What the Script Does
- Creates necessary directories
- Starts nginx for domain verification
- Requests certificate from Let's Encrypt
- Updates nginx configuration
- Restarts services with SSL enabled

### Self-Signed Certificates

Self-signed certificates provide encryption but are not trusted by browsers (security warnings will appear).

#### Setup
```bash
./scripts/setup-ssl.sh self-signed
```

#### What the Script Does
- Generates RSA private key
- Creates self-signed certificate
- Generates Diffie-Hellman parameters
- Updates nginx configuration
- Restarts nginx with SSL

### Certificate Management

#### Check Certificate Status
```bash
./scripts/setup-ssl.sh status
```

#### Renew Let's Encrypt Certificates
```bash
./scripts/ssl-renew.sh renew
```

#### Test Renewal Process
```bash
./scripts/ssl-renew.sh dry-run
```

#### Setup Automatic Renewal
```bash
./scripts/ssl-renew.sh setup-cron
```

This creates a cron job that runs every Sunday at 2 AM to check and renew certificates.

## Nginx Configuration

The SSL configuration includes:

### Security Features
- Modern TLS protocols (1.2 and 1.3)
- Strong cipher suites
- HSTS headers
- Security headers (XSS protection, content type sniffing protection)
- OCSP stapling

### Performance Features
- HTTP/2 support
- SSL session caching
- Gzip compression
- Static file caching

### Configuration Files
- `docker/nginx/nginx.conf` - Main nginx configuration
- `docker/nginx/default.conf` - Server-specific SSL configuration

## Docker Configuration

### Volume Mounts
```yaml
volumes:
  # SSL certificates
  - ./docker/certbot/conf:/etc/letsencrypt:ro
  - ./docker/certbot/www:/var/www/certbot:ro
  - ./docker/ssl:/etc/ssl:ro
```

### Services
- **nginx**: Web server with SSL termination
- **certbot**: Let's Encrypt certificate management

## Troubleshooting

### Common Issues

#### 1. Certificate Not Found
```bash
# Check if certificates exist
ls -la docker/certbot/conf/live/

# Re-run setup if needed
./scripts/setup-ssl.sh letsencrypt
```

#### 2. Domain Verification Failed
- Ensure domain DNS points to your server
- Check firewall allows ports 80 and 443
- Verify nginx is accessible from internet

#### 3. Certificate Expired
```bash
# Check expiration
./scripts/ssl-renew.sh status

# Renew certificates
./scripts/ssl-renew.sh renew
```

#### 4. Nginx Configuration Error
```bash
# Test nginx configuration
docker-compose exec nginx nginx -t

# Check nginx logs
docker-compose logs nginx
```

### Debug Commands

```bash
# Check SSL certificate details
openssl x509 -in docker/certbot/conf/live/your-domain/cert.pem -text -noout

# Test SSL connection
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Check certificate chain
curl -I https://your-domain.com

# Verify HSTS headers
curl -I https://your-domain.com | grep -i strict
```

## Security Best Practices

### 1. Certificate Security
- Keep private keys secure (600 permissions)
- Use strong passwords for certificate generation
- Monitor certificate expiration dates
- Set up automatic renewal

### 2. Nginx Security
- Regular security updates
- Strong SSL configuration
- Security headers enabled
- Rate limiting configured

### 3. Monitoring
- Set up certificate expiration monitoring
- Monitor SSL/TLS configuration
- Regular security audits
- Log monitoring for SSL errors

## Maintenance

### Regular Tasks
1. **Monthly**: Check certificate status
2. **Quarterly**: Review SSL configuration
3. **Annually**: Update security settings

### Automated Tasks
- Certificate renewal (via cron)
- Log rotation
- Security monitoring

## Support

### Log Files
- Nginx: `logs/nginx/`
- SSL Renewal: `logs/ssl-renewal.log`
- Application: `logs/`

### Useful Commands
```bash
# View all SSL-related logs
tail -f logs/nginx/error.log logs/ssl-renewal.log

# Check Docker container status
docker-compose ps

# Restart SSL services
docker-compose restart nginx certbot
```

For additional support, check the main README.md file or create an issue in the project repository.
