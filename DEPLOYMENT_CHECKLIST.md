# 📋 AppRadio Deployment Checklist

## Pre-Deployment

- [ ] **Code Review** - All changes reviewed and approved
- [ ] **Testing** - Changes tested in development/staging environment
- [ ] **Backup Verification** - Ensure backup system is working
- [ ] **Maintenance Window** - Schedule deployment during low-traffic period
- [ ] **Rollback Plan** - Confirm rollback procedure is ready

## Deployment Commands

### For Your Current Recurring Mentions Update

```bash
# 1. Preview the deployment (recommended first step)
./update-app.sh --dry-run --apps "core,mentions" --commands "populate_recurring_history"

# 2. Execute the actual deployment
./update-app.sh --apps "core,mentions" --commands "populate_recurring_history"
```

### Alternative Commands

```bash
# If you want to skip the rebuild (faster, but use only if no code changes)
./update-app.sh --skip-build --apps "core,mentions" --commands "populate_recurring_history"

# If you have external backups and want to skip backup (not recommended)
./update-app.sh --skip-backup --apps "core,mentions" --commands "populate_recurring_history"
```

## During Deployment

- [ ] **Monitor Output** - Watch script output for any errors
- [ ] **Check Service Status** - Verify all containers are running
- [ ] **Database Backup** - Confirm backup was created successfully
- [ ] **Migration Success** - Verify migrations completed without errors
- [ ] **Management Commands** - Confirm custom commands executed successfully

## Post-Deployment Verification

- [ ] **Application Access** - Verify app is accessible via web browser
- [ ] **Core Functionality** - Test main application features
- [ ] **New Features** - Test the recurring mentions status management
- [ ] **Database Integrity** - Verify data is intact and new features work
- [ ] **Performance** - Check application response times
- [ ] **Logs** - Review application logs for any errors

### Specific Tests for Recurring Mentions Update

- [ ] **Recurring Mentions Page** - Visit `/mentions/recurring/` (should show only active/paused)
- [ ] **History Page** - Visit `/mentions/recurring/history/` (should show all statuses)
- [ ] **Status Management** - Test pause/end/cancel buttons on active mentions
- [ ] **Filtering** - Test status filters on history page
- [ ] **Statistics** - Verify status counts are correct

## If Issues Occur

### Minor Issues
1. Check application logs: `docker compose logs web`
2. Check database connectivity: `docker compose exec web python manage.py check`
3. Restart services if needed: `docker compose restart`

### Major Issues
1. **Immediate Rollback**: `./update-app.sh --rollback`
2. Verify rollback success
3. Investigate issues in development environment
4. Plan fix and re-deployment

## Emergency Contacts

- [ ] **Team Lead** - Notify of deployment status
- [ ] **Database Admin** - Available for database issues
- [ ] **Infrastructure Team** - Available for server/Docker issues

## Success Criteria

- [ ] All services running (web, db, redis, celery, nginx)
- [ ] Application accessible via web interface
- [ ] No error logs in application
- [ ] Database migrations applied successfully
- [ ] New recurring mentions features working
- [ ] Performance within acceptable limits

## Post-Deployment Tasks

- [ ] **Update Documentation** - Document any configuration changes
- [ ] **Monitor for 24 hours** - Watch for any delayed issues
- [ ] **Backup Verification** - Verify backup can be restored if needed
- [ ] **Team Notification** - Inform team of successful deployment
- [ ] **Cleanup** - Remove old Docker images if space is needed

## Rollback Procedure

If rollback is needed:

```bash
# Quick rollback to previous version
./update-app.sh --rollback

# Verify rollback success
docker compose ps
curl -I http://your-domain.com  # or https://your-domain.com
```

## Notes

- Deployment typically takes 2-5 minutes
- Database backup adds 30-60 seconds depending on data size
- Health checks run for up to 60 seconds
- Keep this checklist handy during deployment

## Command Reference

```bash
# Full deployment with migrations and commands
./update-app.sh --apps "core,mentions" --commands "populate_recurring_history"

# Dry run (preview only)
./update-app.sh --dry-run --apps "core,mentions" --commands "populate_recurring_history"

# Rollback
./update-app.sh --rollback

# Help
./update-app.sh --help
```
