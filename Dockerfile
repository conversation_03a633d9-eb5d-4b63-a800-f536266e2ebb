# Multi-stage build for Django Radio Mentions App - CPU Optimized
FROM python:3.11-slim AS base

# Set environment variables for CPU optimization
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app \
    NODE_VERSION=20.x \
    MAKEFLAGS="-j2" \
    PIP_COMPILE_JOBS=2

# Install minimal system dependencies with CPU limits
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    sqlite3 \
    netcat-openbsd \
    libpq-dev \
    ca-certificates \
    gnupg \
    && curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg \
    && echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_VERSION nodistro main" | tee /etc/apt/sources.list.d/nodesource.list \
    && apt-get update \
    && apt-get install -y nodejs \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* /var/tmp/*

# Install build dependencies only when needed
FROM base AS build-deps
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libssl-dev \
    libffi-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Python dependencies stage - CPU optimized
FROM build-deps AS python-deps

# Create app user with specific UID/GID for consistency
RUN groupadd -r -g 1000 appuser && useradd -r -u 1000 -g appuser appuser

# Set work directory
WORKDIR /app

# Copy requirements first for better layer caching
COPY requirements.txt .

# Install Python dependencies (optimized for speed)
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt

# Node.js dependencies stage - separate for better caching
FROM base AS node-deps
WORKDIR /app
COPY package*.json ./
COPY tailwind.config.js ./
# Install npm dependencies with limited CPU usage
# Use npm install if npm ci fails (fallback for compatibility)
RUN npm ci --omit=dev --no-audit --no-fund --maxsockets 2 || \
    npm install --omit=dev --no-audit --no-fund --maxsockets 2

# Development stage - CPU optimized
FROM python-deps AS development

# Copy Node.js dependencies from node-deps stage
COPY --from=node-deps /app/node_modules ./node_modules
COPY --from=node-deps /app/package*.json ./
COPY --from=node-deps /app/tailwind.config.js ./

# Install development dependencies (optimized for speed)
COPY requirements-dev.txt* ./
RUN if [ -f requirements-dev.txt ]; then \
        pip install --no-cache-dir -r requirements-dev.txt; \
    fi

# Copy project files
COPY --chown=appuser:appuser . .

# Build CSS with Tailwind (CPU limited)
RUN npm run build-css-prod

# Create necessary directories with proper permissions
RUN mkdir -p /app/staticfiles /app/media /app/logs && \
    chmod 755 /app/staticfiles /app/media /app/logs && \
    chown -R appuser:appuser /app

# Switch to app user
USER appuser

# Expose port
EXPOSE 8000

# Development healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# Run development server
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]

# Production base stage - CPU optimized
FROM python-deps AS prod-base

# Install production dependencies (optimized for speed)
COPY requirements-prod.txt ./
RUN pip install --no-cache-dir -r requirements-prod.txt && \
    pip install --no-cache-dir django-csp==3.8.0

# Copy Node.js dependencies from node-deps stage
COPY --from=node-deps /app/node_modules ./node_modules
COPY --from=node-deps /app/package*.json ./
COPY --from=node-deps /app/tailwind.config.js ./

# Clean up build dependencies to reduce image size
RUN apt-get update && apt-get remove -y build-essential gnupg && \
    apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Production stage
FROM prod-base AS production

# Copy project files with proper ownership
COPY --chown=appuser:appuser . .

# Build CSS with Tailwind for production
RUN npm run build-css-prod

# Create necessary directories
RUN mkdir -p /app/staticfiles /app/media /app/logs && \
    chmod 755 /app/staticfiles /app/media /app/logs && \
    chown -R appuser:appuser /app

# Copy and set up entrypoint script
COPY --chown=appuser:appuser docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Switch to app user before running Django commands
USER appuser

# Set production environment
ENV DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production

# Collect static files (will be overridden in entrypoint if needed)
RUN python manage.py collectstatic --noinput --clear || echo "Static collection will run in entrypoint"

# Expose port
EXPOSE 8000

# Enhanced health check with better parameters
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# Use entrypoint script
ENTRYPOINT ["/docker-entrypoint.sh"]

# Default command is Gunicorn, but allow override for celery/celery-beat
CMD ["sh", "-c", "if [ \"$CELERY_WORKER\" = '1' ]; then \
    exec celery -A radio_mentions_project worker --loglevel=info --concurrency=2 --max-tasks-per-child=100; \
elif [ \"$CELERY_BEAT\" = '1' ]; then \
    exec celery -A radio_mentions_project beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler; \
else \
    exec gunicorn \
     --bind 0.0.0.0:8000 \
     --workers ${GUNICORN_WORKERS:-4} \
     --worker-class ${GUNICORN_WORKER_CLASS:-sync} \
     --timeout ${GUNICORN_TIMEOUT:-120} \
     --max-requests ${GUNICORN_MAX_REQUESTS:-1000} \
     --max-requests-jitter ${GUNICORN_MAX_REQUESTS_JITTER:-100} \
     --preload \
     --access-logfile ${GUNICORN_ACCESS_LOG:-/app/logs/gunicorn_access.log} \
     --error-logfile ${GUNICORN_ERROR_LOG:-/app/logs/gunicorn_error.log} \
     --log-level ${GUNICORN_LOG_LEVEL:-info} \
     --capture-output \
     radio_mentions_project.wsgi:application; \
fi"]

# Metadata
LABEL maintainer="<EMAIL>" \
      version="1.0" \
      description="Django Radio Mentions Application" \
      org.opencontainers.image.title="Radio Mentions App" \
      org.opencontainers.image.description="Production-ready Django application for radio mentions tracking"