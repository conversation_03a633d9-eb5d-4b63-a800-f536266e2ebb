# Production requirements - includes all base requirements plus production-specific packages
-r requirements.txt

# Production WSGI server
gunicorn==21.2.0

# Redis cache backend
django-redis==5.4.0

# Production database optimizations (already included in requirements.txt)

# Security and monitoring
whitenoise==6.6.0
# sentry-sdk==1.39.1  # DISABLED - Updated to latest version as of June 2025
django-csp==3.8.0   # Content Security Policy

# Health checks
django-health-check==3.17.0

# System monitoring
psutil==5.9.6

# Performance optimization
django-extensions==3.2.3
django-debug-toolbar==4.2.0

# Security enhancements
django-axes==6.1.1

# File handling and compression
# Pillow already included in requirements.txt
django-compressor==4.4

# Additional Redis client
redis==5.0.1

# Rate limiting and security
django-ratelimit==4.1.0
django-axes==6.1.1  # Login attempt tracking

# Production-specific optimizations
django-timezone-field==7.1  # Enhanced timezone handling for production
