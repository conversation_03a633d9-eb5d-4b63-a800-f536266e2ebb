{% extends 'base.html' %}
{% load static %}

{% block title %}
  Conflict Detection - RadioMention
{% endblock %}

{% block page_title %}
  Conflict Detection
{% endblock %}

{% block header_actions %}
  <a href="{% url 'mentions:calendar' %}" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
    <i class="fa-solid fa-calendar mr-2"></i>
    View Calendar
  </a>
{% endblock %}

{% block content %}
  {% csrf_token %}
  <div class="space-y-6">
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-exclamation-triangle text-red-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Time Conflicts</p>
            <p class="text-2xl font-semibold text-gray-900">{{ conflict_types.time_overlap|length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-user-clock text-yellow-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Presenter Availability</p>
            <p class="text-2xl font-semibold text-gray-900">{{ conflict_types.presenter_availability|length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-chart-line text-orange-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Capacity Issues</p>
            <p class="text-2xl font-semibold text-gray-900">{{ conflict_types.show_capacity|length }}</p>
          </div>
        </div>
      </div>
    </div>

    {% if conflicts %}
      <!-- Time Overlap Conflicts -->
      {% if conflict_types.time_overlap %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
              <i class="fa-solid fa-exclamation-triangle text-red-500 mr-2"></i>
              Time Overlap Conflicts
            </h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              {% for conflict in conflict_types.time_overlap %}
                <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                  <div class="flex items-start justify-between">
                    <div>
                      <h4 class="font-medium text-gray-900">{{ conflict.reading1.mention.title }}</h4>
                      <p class="text-sm text-gray-600">{{ conflict.reading1.show.name }} - {{ conflict.reading1.scheduled_date }} at {{ conflict.reading1.scheduled_time }}</p>
                      <p class="text-sm text-red-600 mt-1">{{ conflict.description }}</p>
                      {% if conflict.reading2 %}
                        <p class="text-sm text-gray-500 mt-1">Conflicts with: {{ conflict.reading2.mention.title }}</p>
                      {% endif %}
                    </div>
                    <div class="flex space-x-2">
                      <button onclick="resolveConflict({{ conflict.reading1.id }})" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700" data-reading-id="{{ conflict.reading1.id }}">Auto-Resolve</button>
                      {% if conflict.reading1.id %}
                        <a href="{% url 'mentions:reading_detail' conflict.reading1.id %}" class="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700">View Details</a>
                      {% endif %}
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      {% endif %}

      <!-- Presenter Availability Issues -->
      {% if conflict_types.presenter_availability %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
              <i class="fa-solid fa-user-clock text-yellow-500 mr-2"></i>
              Presenter Availability Issues
            </h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              {% for conflict in conflict_types.presenter_availability %}
                <div class="border border-yellow-200 rounded-lg p-4 bg-yellow-50">
                  <div class="flex items-start justify-between">
                    <div>
                      <h4 class="font-medium text-gray-900">{{ conflict.reading1.mention.title }}</h4>
                      <p class="text-sm text-gray-600">{{ conflict.reading1.show.name }} • {{ conflict.reading1.scheduled_date|date:'M j, Y' }} at {{ conflict.reading1.scheduled_time|time:'g:i A' }}</p>
                      <p class="text-sm text-yellow-600 mt-1">{{ conflict.description }}</p>
                    </div>
                    <div class="flex space-x-2">
                      <button onclick="resolveConflict({{ conflict.reading1.id }})" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700" data-reading-id="{{ conflict.reading1.id }}">Auto-Resolve</button>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      {% endif %}

      <!-- Show Capacity Issues -->
      {% if conflict_types.show_capacity %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
              <i class="fa-solid fa-chart-line text-orange-500 mr-2"></i>
              Show Capacity Issues
            </h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              {% for conflict in conflict_types.show_capacity %}
                <div class="border border-orange-200 rounded-lg p-4 bg-orange-50">
                  <div class="flex items-start justify-between">
                    <div>
                      <h4 class="font-medium text-gray-900">{{ conflict.reading1.show.name }}</h4>
                      <p class="text-sm text-gray-600">{{ conflict.reading1.scheduled_date }} - Hour capacity exceeded</p>
                      <p class="text-sm text-orange-600 mt-1">{{ conflict.description }}</p>
                    </div>
                    <div class="flex space-x-2">
                      <button onclick="resolveConflict({{ conflict.reading1.id }})" class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700" data-reading-id="{{ conflict.reading1.id }}">Auto-Resolve</button>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      {% endif %}
    {% else %}
      <!-- No Conflicts -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i class="fa-solid fa-check text-green-600 text-2xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Conflicts Detected</h3>
        <p class="text-gray-600">All mentions are properly scheduled without conflicts.</p>
      </div>
    {% endif %}
  </div>

  <script>
    function resolveConflict(readingId) {
      console.log('resolveConflict called with readingId:', readingId)
    
      if (!readingId) {
        alert('Error: Invalid reading ID')
        return
      }
    
      if (confirm('Automatically resolve this conflict? This will reschedule the mention to the next available slot.')) {
        // Get CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')
        console.log('CSRF token found:', !!csrfToken)
    
        if (!csrfToken) {
          alert('Error: CSRF token not found. Please refresh the page and try again.')
          return
        }
    
        // Show loading state
        const button = event.target
        const originalText = button.textContent
        button.textContent = 'Resolving...'
        button.disabled = true
    
        const url = `/mentions/readings/${readingId}/auto-resolve/`
        console.log('Making request to:', url)
    
        fetch(url, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrfToken.value,
            'Content-Type': 'application/json'
          }
        })
          .then((response) => {
            console.log('Response status:', response.status)
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }
            return response.json()
          })
          .then((data) => {
            console.log('Response data:', data)
            if (data.success) {
              alert('Conflict resolved successfully! The page will reload to show updated conflicts.')
              location.reload()
            } else {
              alert('Failed to resolve conflict: ' + (data.error || 'Unknown error'))
              // Restore button state
              button.textContent = originalText
              button.disabled = false
            }
          })
          .catch((error) => {
            console.error('Error resolving conflict:', error)
            alert('Error resolving conflict: ' + error.message)
            // Restore button state
            button.textContent = originalText
            button.disabled = false
          })
      }
    }
    
    // Debug function to check if page is properly loaded
    document.addEventListener('DOMContentLoaded', function () {
      console.log('Conflict detection page loaded')
      const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')
      console.log('CSRF token on page load:', !!csrfToken)
    
      const buttons = document.querySelectorAll('button[data-reading-id]')
      console.log('Auto-resolve buttons found:', buttons.length)
    
      buttons.forEach((button, index) => {
        const readingId = button.getAttribute('data-reading-id')
        console.log(`Button ${index + 1} reading ID:`, readingId)
      })
    })
  </script>
{% endblock %}
