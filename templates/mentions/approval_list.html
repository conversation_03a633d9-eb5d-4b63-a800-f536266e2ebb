{% extends 'base.html' %}

{% block title %}
  Mention Approvals - RadioMention
{% endblock %}

{% block page_title %}
  Mention Approvals
{% endblock %}

{% block content %}
  <!-- Approval Notifications -->
  <div id="approval-notifications" class="mb-6">
    <!-- Real-time approval notifications will appear here -->
  </div>

  <div class="flex items-center justify-between mb-6">
    <div>
      <h3 class="text-xl font-semibold text-gray-800">Pending Approvals</h3>
      <p class="text-gray-600">Review and approve mentions before scheduling</p>
    </div>
    <div class="flex items-center space-x-4">
      <div class="text-sm text-gray-500">{{ pending_mentions|length }} mentions pending approval</div>
      {% if pending_mentions|length > 5 %}
        <div class="bg-yellow-100 border border-yellow-300 text-yellow-800 px-3 py-1 rounded-full text-xs font-medium">
          <i class="fas fa-exclamation-triangle mr-1"></i>
          High Volume
        </div>
      {% endif %}
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {% for mention in pending_mentions %}
      <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200" data-mention-id="{{ mention.pk }}">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h4 class="font-medium text-gray-900">{{ mention.title }}</h4>
            <p class="text-sm text-gray-500 mt-1">{{ mention.client.name }} - {{ mention.created_at|date:'M d, Y' }}</p>
          </div>
          <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">{{ mention.status_display }}</span>
        </div>
        <p class="text-sm text-gray-600 mb-4">{{ mention.content|truncatewords:20 }}</p>

        <div class="space-y-2 mb-4">
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">Priority:</span>
            <span class="font-medium {% if mention.priority == 4 %}
                
                
                
                text-red-600



              {% elif mention.priority == 3 %}
                
                
                
                text-orange-600



              {% else %}
                
                
                
                text-gray-600



              {% endif %}">
              {{ mention.priority_display }}
            </span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">Duration:</span>
            <span class="text-gray-900">{{ mention.duration_seconds }} seconds</span>
          </div>
          <div class="flex justify-between text-sm">
            <span class="text-gray-500">Created by:</span>
            <span class="text-gray-900">{{ mention.created_by.get_full_name|default:mention.created_by.username }}</span>
          </div>
        </div>

        <div class="flex items-center text-sm text-gray-500 mb-4">
          <i class="fa-regular fa-clock mr-1"></i>
          <span>Submitted {{ mention.created_at|timesince }} ago</span>
        </div>

        {% if mention.notes %}
          <div class="bg-gray-50 rounded p-3 mb-4">
            <p class="text-xs text-gray-500 mb-1">Notes:</p>
            <p class="text-sm text-gray-700">{{ mention.notes }}</p>
          </div>
        {% endif %}

        <div class="flex space-x-2">
          <button onclick="approveMention({{ mention.pk }})" class="flex-1 px-3 py-1.5 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700">
            <i class="fa-solid fa-check mr-1"></i>
            Approve
          </button>
          <a href="{% url 'mentions:mention_edit' mention.pk %}" class="px-3 py-1.5 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50"><i class="fa-solid fa-edit"></i></a>
          <button onclick="rejectMention({{ mention.pk }})" class="px-3 py-1.5 bg-white border border-red-300 text-red-700 text-sm font-medium rounded-md hover:bg-red-50"><i class="fa-solid fa-times"></i></button>
        </div>
      </div>
    {% empty %}
      <div class="col-span-3 text-center py-12">
        <i class="fa-solid fa-check-circle text-6xl text-green-300 mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
        <p class="text-gray-500 mb-4">No mentions pending approval at the moment.</p>
        <a href="{% url 'mentions:mention_create' %}" class="inline-flex items-center px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700">
          <i class="fa-solid fa-plus mr-2"></i>
          Create New Mention
        </a>
      </div>
    {% endfor %}
  </div>

  <!-- Bulk Actions -->
  {% if pending_mentions %}
    <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">Bulk Actions</h3>
      <div class="flex items-center space-x-4">
        <button onclick="bulkApprove()" class="px-4 py-2 bg-green-600 text-white font-medium rounded-md hover:bg-green-700">
          <i class="fa-solid fa-check mr-2"></i>
          Approve All
        </button>
        <button onclick="bulkReject()" class="px-4 py-2 bg-red-600 text-white font-medium rounded-md hover:bg-red-700">
          <i class="fa-solid fa-times mr-2"></i>
          Reject All
        </button>
        <select id="bulkPriority" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
          <option value="">Change Priority...</option>
          <option value="1">Low</option>
          <option value="2">Normal</option>
          <option value="3">High</option>
          <option value="4">Urgent</option>
        </select>
        <button onclick="bulkChangePriority()" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700">Apply</button>
      </div>
    </div>
  {% endif %}
{% endblock %}

{% block extra_js %}
  <script>
    // Add CSRF token to all AJAX requests
    function getCookie(name) {
      let cookieValue = null
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';')
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim()
          if (cookie.substring(0, name.length + 1) === name + '=') {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1))
            break
          }
        }
      }
      return cookieValue
    }
    
    const csrftoken = getCookie('csrftoken')
    
    function approveMention(mentionId) {
      if (confirm('Are you sure you want to approve this mention?')) {
        fetch(`/mentions/${mentionId}/approve/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrftoken,
            'Content-Type': 'application/json'
          }
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              // Show success notification
              if (window.notificationSystem) {
                window.notificationSystem.show('Mention approved successfully!', 'success')
              }
              // Remove the mention card from the page
              const mentionCard = document.querySelector(`[data-mention-id="${mentionId}"]`)
              if (mentionCard) {
                mentionCard.style.transition = 'all 0.3s ease'
                mentionCard.style.opacity = '0'
                mentionCard.style.transform = 'scale(0.95)'
                setTimeout(() => {
                  mentionCard.remove()
                  updateApprovalCount()
                }, 300)
              } else {
                location.reload()
              }
            } else {
              if (window.notificationSystem) {
                window.notificationSystem.show('Error approving mention: ' + (data.error || 'Unknown error'), 'error')
              } else {
                alert('Error approving mention: ' + (data.error || 'Unknown error'))
              }
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('Error approving mention')
          })
      }
    }
    
    function rejectMention(mentionId) {
      const reason = prompt('Please provide a reason for rejection:')
      if (reason) {
        fetch(`/mentions/${mentionId}/reject/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrftoken,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ reason: reason })
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              location.reload()
            } else {
              alert('Error rejecting mention: ' + (data.error || 'Unknown error'))
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('Error rejecting mention')
          })
      }
    }
    
    function bulkApprove() {
      if (confirm('Are you sure you want to approve all pending mentions?')) {
        // Implementation for bulk approve
        alert('Bulk approve functionality coming soon!')
      }
    }
    
    function bulkReject() {
      const reason = prompt('Please provide a reason for rejecting all mentions:')
      if (reason) {
        // Implementation for bulk reject
        alert('Bulk reject functionality coming soon!')
      }
    }
    
    function bulkChangePriority() {
      const priority = document.getElementById('bulkPriority').value
      if (priority && confirm(`Change priority of all pending mentions to ${priority}?`)) {
        // Implementation for bulk priority change
        alert('Bulk priority change functionality coming soon!')
      }
    }
    
    // Helper function to update approval count
    function updateApprovalCount() {
      const remainingCards = document.querySelectorAll('[data-mention-id]').length
      const countElement = document.querySelector('.text-sm.text-gray-500')
      if (countElement) {
        countElement.textContent = `${remainingCards} mentions pending approval`
      }
    
      // Show completion message if no more approvals
      if (remainingCards === 0) {
        const gridContainer = document.querySelector('.grid')
        gridContainer.innerHTML = `
                <div class="col-span-3 text-center py-12">
                    <i class="fa-solid fa-check-circle text-6xl text-green-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
                    <p class="text-gray-500 mb-4">No mentions pending approval at the moment.</p>
                    <a href="/mentions/create/" class="inline-flex items-center px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700">
                        <i class="fa-solid fa-plus mr-2"></i>
                        Create New Mention
                    </a>
                </div>
            `
    
        // Show success notification
        if (window.notificationSystem) {
          window.notificationSystem.show('All mentions have been processed! 🎉', 'success', 8000)
        }
      }
    }
  </script>
{% endblock %}
