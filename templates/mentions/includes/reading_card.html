{% load permission_tags %}

<div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
  <div class="flex items-start justify-between">
    <div class="flex-1">
      <div class="flex items-center space-x-3 mb-2">
        <h4 class="font-medium text-gray-900">{{ reading.show.name }}</h4>
        {% if reading.actual_read_time %}
          <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <i class="fa-solid fa-check mr-1"></i>
            Completed
          </span>
        {% else %}
          <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <i class="fa-solid fa-clock mr-1"></i>
            Scheduled
          </span>
        {% endif %}
      </div>

      <div class="space-y-1 text-sm text-gray-600">
        <div class="flex items-center">
          <i class="fa-solid fa-calendar mr-2 text-gray-400"></i>
          <span>{{ reading.scheduled_date|date:'l, F j, Y' }} at {{ reading.scheduled_time|time:'g:i A' }}</span>
        </div>

        {% if reading.presenter %}
          <div class="flex items-center">
            <i class="fa-solid fa-user mr-2 text-gray-400"></i>
            <span>{{ reading.presenter.display_name }}</span>
            {% has_permission 'manage_presenters' as can_manage_presenters %}
            {% if can_manage_presenters %}
              <a href="{% url 'core:presenter_calendar' reading.presenter.pk %}" class="ml-2 text-primary-600 hover:text-primary-800 text-xs">View Calendar</a>
            {% endif %}
          </div>
        {% endif %}

        {% if reading.actual_read_time %}
          <div class="flex items-center text-green-600">
            <i class="fa-solid fa-check-circle mr-2"></i>
            <span>Read on {{ reading.actual_read_time|date:'M j, Y' }} at {{ reading.actual_read_time|time:'g:i A' }}</span>
          </div>
          {% if reading.duration_seconds %}
            <div class="flex items-center text-gray-500">
              <i class="fa-solid fa-stopwatch mr-2"></i>
              <span>Duration: {{ reading.duration_seconds }}s</span>
            </div>
          {% endif %}
        {% endif %}

        {% if reading.notes %}
          <div class="mt-2 p-2 bg-gray-50 rounded text-xs">
            <strong>Notes:</strong> {{ reading.notes }}
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Actions -->
    {% if show_actions %}
      <div class="flex items-center space-x-2 ml-4">
        <!-- View Details -->
        <a href="{% url 'mentions:reading_detail' reading.pk %}" class="text-gray-400 hover:text-gray-600" title="View Details"><i class="fa-solid fa-eye"></i></a>

        {% has_permission 'schedule_mentions' as can_schedule %}
        {% if can_schedule and not reading.actual_read_time %}
          <!-- Reschedule -->
          <button onclick="rescheduleReading({{ reading.pk }})" class="text-blue-400 hover:text-blue-600" title="Reschedule"><i class="fa-solid fa-calendar-alt"></i></button>

          <!-- Reassign Presenter -->
          <button onclick="reassignPresenter({{ reading.pk }})" class="text-yellow-400 hover:text-yellow-600" title="Reassign Presenter"><i class="fa-solid fa-user-edit"></i></button>
        {% endif %}

        {% has_permission 'manage_mentions' as can_manage %}
        {% if can_manage and not reading.actual_read_time %}
          <!-- Delete Reading -->
          <button onclick="deleteReading({{ reading.pk }})" class="text-red-400 hover:text-red-600" title="Delete Reading"><i class="fa-solid fa-trash"></i></button>
        {% endif %}

        {% has_role 'presenter' as is_presenter %}
        {% if is_presenter and not reading.actual_read_time %}
          <!-- Check if presenter can access this show -->
          {% for show_presenter in request.user.presenter.showpresenter_set.all %}
            {% if show_presenter.show == reading.show and show_presenter.is_active %}
              <!-- Mark as Read (for presenters) -->
              <button onclick="markAsRead({{ reading.pk }})" class="bg-green-600 text-white px-2 py-1 rounded text-xs font-medium hover:bg-green-700">
                <i class="fa-solid fa-check mr-1"></i>
                Mark Read
              </button>
            {% endif %}
          {% endfor %}
        {% endif %}
      </div>
    {% endif %}
  </div>

  <!-- Conflict Warning -->
  {% if reading.has_conflict %}
    <div class="mt-3 p-2 bg-red-50 border border-red-200 rounded-md">
      <div class="flex items-center text-red-800 text-xs">
        <i class="fa-solid fa-exclamation-triangle mr-2"></i>
        <span>Scheduling conflict detected</span>
        {% has_permission 'manage_mentions' as can_manage %}
        {% if can_manage %}
          <button onclick="resolveConflict({{ reading.pk }})" class="ml-2 text-red-600 hover:text-red-800 underline">Resolve</button>
        {% endif %}
      </div>
    </div>
  {% endif %}
</div>
