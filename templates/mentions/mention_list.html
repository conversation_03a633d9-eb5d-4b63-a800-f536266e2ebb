{% extends 'base.html' %}

{% block title %}
  Mention Management - RadioMention
{% endblock %}

{% block page_title %}
  Mention Management - Enhanced
{% endblock %}

{% block header_actions %}
  <div class="flex space-x-2">
    <a href="{% url 'mentions:mention_create' %}" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
      <i class="fa-solid fa-plus mr-2"></i>
      Create Mention
    </a>
    <a href="{% url 'mentions:bulk_schedule' %}" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 flex items-center">
      <i class="fa-solid fa-calendar-plus mr-2"></i>
      Bulk Assign
    </a>
    <a href="{% url 'mentions:analytics' %}" class="px-4 py-2 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 flex items-center">
      <i class="fa-solid fa-chart-bar mr-2"></i>
      Analytics
    </a>
  </div>
{% endblock %}

{% block content %}
  <!-- Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
      <div class="text-2xl font-bold text-gray-900">{{ stats.total }}</div>
      <div class="text-sm text-gray-500">Total Mentions</div>
    </div>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
      <div class="text-2xl font-bold text-yellow-600">{{ stats.pending }}</div>
      <div class="text-sm text-gray-500">Pending Approval</div>
    </div>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
      <div class="text-2xl font-bold text-blue-600">{{ stats.scheduled }}</div>
      <div class="text-sm text-gray-500">Approved</div>
    </div>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
      <div class="text-2xl font-bold text-green-600">{{ stats.read }}</div>
      <div class="text-sm text-gray-500">Completed</div>
    </div>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 text-center">
      <div class="text-2xl font-bold text-red-600">{{ stats.cancelled }}</div>
      <div class="text-sm text-gray-500">Cancelled</div>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <!-- Filters -->
    <div class="p-4 border-b border-gray-200">
      <form method="GET" class="flex flex-wrap items-center gap-3">
        <div class="flex items-center gap-2">
          <label class="text-sm font-medium text-gray-700">Search:</label>
          {{ filter_form.search }}
        </div>
        <div class="flex items-center gap-2">
          <label class="text-sm font-medium text-gray-700">Status:</label>
          {{ filter_form.status }}
        </div>
        <div class="flex items-center gap-2">
          <label class="text-sm font-medium text-gray-700">Priority:</label>
          {{ filter_form.priority }}
        </div>
        <div class="flex items-center gap-2">
          <label class="text-sm font-medium text-gray-700">Client:</label>
          {{ filter_form.client }}
        </div>
        <div class="flex items-center gap-2 ml-4">
          <button type="submit" class="px-3 py-2 bg-primary-600 text-white text-sm rounded-md hover:bg-primary-700"><i class="fa-solid fa-filter mr-1"></i>Filter</button>
          <a href="{% url 'mentions:mention_list' %}" class="px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200">Clear</a>
        </div>
      </form>
    </div>

    <!-- Bulk Actions -->
    {% if page_obj %}
      <div class="p-4 border-b border-gray-200 bg-gray-50" id="bulkActions" style="display: none;">
        <form method="POST" id="bulkForm">
          {% csrf_token %}
          {{ bulk_form.action }}
          {{ bulk_form.new_priority }}
          {{ bulk_form.new_status }}
          {{ bulk_form.selected_mentions }}
          <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">Apply Action</button>
        </form>
      </div>
    {% endif %}

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <input type="checkbox" id="selectAll" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" />
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mention</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {% for mention in page_obj %}
            <tr>
              <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" name="mention_ids" value="{{ mention.id }}" class="mention-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" />
              </td>
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ mention.title }}</div>
                  <div class="text-sm text-gray-500">{{ mention.content|truncatewords:10 }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ mention.client.name }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                {% if mention.status == 'pending' %}
                    
                    
                    
                    
                    
                    bg-yellow-100 text-yellow-800






                  {% elif mention.status == 'scheduled' %}
                    
                    
                    
                    
                    
                    bg-blue-100 text-blue-800






                  {% elif mention.status == 'read' %}
                    
                    
                    
                    
                    
                    bg-green-100 text-green-800






                  {% elif mention.status == 'cancelled' %}
                    
                    
                    
                    
                    
                    bg-red-100 text-red-800






                  {% endif %}">
                  {{ mention.get_status_display }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                {% if mention.priority == 4 %}
                    
                    
                    
                    
                    
                    bg-red-100 text-red-800






                  {% elif mention.priority == 3 %}
                    
                    
                    
                    
                    
                    bg-orange-100 text-orange-800






                  {% elif mention.priority == 2 %}
                    
                    
                    
                    
                    
                    bg-yellow-100 text-yellow-800






                  {% else %}
                    
                    
                    
                    
                    
                    bg-green-100 text-green-800






                  {% endif %}">
                  {{ mention.get_priority_display }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ mention.duration_seconds }}s</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ mention.created_at|timesince }} ago</td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <a href="{% url 'mentions:mention_detail' mention.pk %}" class="text-primary-600 hover:text-primary-900 mr-3">View</a>
                <a href="{% url 'mentions:mention_edit' mention.pk %}" class="text-primary-600 hover:text-primary-900 mr-3">Edit</a>
                <a href="{% url 'mentions:mention_delete' mention.pk %}" class="text-red-600 hover:text-red-900">Delete</a>
              </td>
            </tr>
          {% empty %}
            <tr>
              <td colspan="8" class="px-6 py-12 text-center">
                <div class="text-gray-500">
                  <i class="fa-solid fa-bullhorn text-4xl mb-4"></i>
                  <p class="text-lg font-medium">No mentions found</p>
                  <p class="text-sm">Create your first mention to get started.</p>
                  <a href="{% url 'mentions:mention_create' %}" class="mt-4 inline-flex items-center px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700">
                    <i class="fa-solid fa-plus mr-2"></i>
                    Create Mention
                  </a>
                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
      <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
        <div class="text-sm text-gray-500">Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results</div>
        <div class="flex space-x-2">
          {% if page_obj.has_previous %}
            <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}
                {% if key != 'page' %}
                  
                  
                  
                  
                  &{{ key }}={{ value }}
                {% endif %}
              {% endfor %}"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
              Previous
            </a>
          {% endif %}

          {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
              <span class="px-3 py-1 bg-primary-600 text-white rounded-md text-sm">{{ num }}</span>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
              <a href="?page={{ num }}{% for key, value in request.GET.items %}
                  {% if key != 'page' %}
                    
                    
                    
                    
                    &{{ key }}={{ value }}
                  {% endif %}
                {% endfor %}"
                class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
                {{ num }}
              </a>
            {% endif %}
          {% endfor %}

          {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}
                {% if key != 'page' %}
                  
                  
                  
                  
                  &{{ key }}={{ value }}
                {% endif %}
              {% endfor %}"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">
              Next
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}
  </div>
{% endblock %}
