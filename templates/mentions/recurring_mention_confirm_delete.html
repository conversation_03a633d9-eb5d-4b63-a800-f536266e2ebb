{% extends 'base.html' %}

{% block title %}Delete Recurring Mention - RadioMention{% endblock %}

{% block page_title %}Delete Recurring Mention{% endblock %}

{% block header_actions %}
<a href="{% url 'mentions:recurring_mentions' %}" class="px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 flex items-center">
    <i class="fa-solid fa-arrow-left mr-2"></i>
    Back to Recurring Mentions
</a>
{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-exclamation-triangle text-red-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-medium text-gray-900">Confirm Deletion</h3>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <div class="mb-6">
                <p class="text-gray-700 mb-4">
                    Are you sure you want to delete this recurring mention? This action will:
                </p>
                <ul class="list-disc list-inside text-gray-600 space-y-1 mb-4">
                    <li>Delete the recurring mention pattern</li>
                    <li>Delete all future unread mentions generated from this pattern</li>
                    <li>Keep any mentions that have already been read</li>
                </ul>
                <p class="text-red-600 font-medium">This action cannot be undone.</p>
            </div>
            
            <!-- Recurring Mention Details -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 class="font-medium text-gray-900 mb-2">{{ recurring_mention.title }}</h4>
                <div class="text-sm text-gray-600 space-y-1">
                    <p><strong>Client:</strong> {{ recurring_mention.client.name }}</p>
                    <p><strong>Frequency:</strong> {{ recurring_mention.get_frequency_display }}</p>
                    <p><strong>Start Date:</strong> {{ recurring_mention.start_date }}</p>
                    {% if recurring_mention.end_date %}
                    <p><strong>End Date:</strong> {{ recurring_mention.end_date }}</p>
                    {% endif %}
                    <p><strong>Content:</strong> {{ recurring_mention.content|truncatewords:20 }}</p>
                </div>
            </div>
            
            <form method="post" class="flex justify-end space-x-3">
                {% csrf_token %}
                <a href="{% url 'mentions:recurring_mentions' %}" 
                   class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-red-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700">
                    <i class="fa-solid fa-trash mr-2"></i>
                    Delete Recurring Mention
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
