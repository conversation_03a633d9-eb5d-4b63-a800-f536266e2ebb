{% extends 'base.html' %} {% load static %} {% block title %}
  Create Recurring Mention - Preview
{% endblock %} {% block header_actions %}
  <div class="flex space-x-3">
    <a href="{% url 'mentions:recurring_wizard_clear' %}" class="px-4 py-2 bg-yellow-600 text-white font-medium rounded-md hover:bg-yellow-700 flex items-center">
      <i class="fa-solid fa-refresh mr-2"></i>
      Start Fresh
    </a>
    <a href="{% url 'mentions:recurring_mentions' %}" class="px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 flex items-center">
      <i class="fa-solid fa-arrow-left mr-2"></i>
      Back to Recurring Mentions
    </a>
  </div>
{% endblock %} {% block content %}
  <div class="max-w-6xl mx-auto">
    <!-- Progress Indicator -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full text-sm font-medium">
            <i class="fa-solid fa-check"></i>
          </div>
          <span class="ml-2 text-sm font-medium text-green-600">Basic Information</span>
        </div>
        <div class="flex-1 mx-4">
          <div class="h-1 bg-green-600 rounded"></div>
        </div>
        <div class="flex items-center">
          <div class="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full text-sm font-medium">
            <i class="fa-solid fa-check"></i>
          </div>
          <span class="ml-2 text-sm font-medium text-green-600">Schedule</span>
        </div>
        <div class="flex-1 mx-4">
          <div class="h-1 bg-green-600 rounded"></div>
        </div>
        <div class="flex items-center">
          <div class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-medium">3</div>
          <span class="ml-2 text-sm font-medium text-blue-600">Preview</span>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
      <div class="px-6 py-4 bg-blue-50 border-b border-blue-200">
        <h2 class="text-xl font-semibold text-blue-900">Step 3: Set Period & Preview</h2>
        <p class="text-sm text-blue-700 mt-1">Set the campaign duration and review the complete schedule</p>
      </div>

      <div class="p-6">
        <!-- Campaign Summary -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h4 class="text-lg font-medium text-blue-900 mb-2">📋 Campaign Summary</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span class="font-medium text-blue-800">Client:</span>
              <span class="text-blue-700">{{ client.name }}</span>
            </div>
            <div>
              <span class="font-medium text-blue-800">Mention:</span>
              <span class="text-blue-700">{{ wizard_data.title }}</span>
            </div>
            <div>
              <span class="font-medium text-blue-800">Duration:</span>
              <span class="text-blue-700">{{ wizard_data.duration_seconds|default:30 }} seconds</span>
            </div>
          </div>
        </div>

        <form method="post" action="{% url 'mentions:recurring_wizard_save' %}" class="space-y-6" id="step3-form">
          {% csrf_token %}
          <input type="hidden" name="submission_token" value="{{ wizard_data.submission_token|default:'' }}" />

          <!-- Period Settings -->
          <div class="bg-white border border-gray-200 rounded-lg p-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">📅 Campaign Period</h4>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Start Date *</label>
                <input type="date" name="start_date" value="{{ wizard_data.start_date|default:'' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required min="{{ today|date:'Y-m-d' }}" onchange="updatePreview()" />
                <p class="text-xs text-gray-500 mt-1">Campaign start date</p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">End Date *</label>
                <input type="date" name="end_date" value="{{ wizard_data.end_date|default:'' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required onchange="updatePreview()" id="end-date-input" />
                <p class="text-xs text-gray-500 mt-1">Campaign end date (inclusive)</p>
              </div>
            </div>

            <!-- Quick Duration Buttons -->
            <div class="mt-4">
              <p class="text-sm font-medium text-gray-700 mb-2">Quick Select:</p>
              <div class="flex flex-wrap gap-2">
                <button type="button" onclick="setDuration(7)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">1 Week</button>
                <button type="button" onclick="setDuration(14)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">2 Weeks</button>
                <button type="button" onclick="setDuration(30)" class="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-md">1 Month</button>
                <button type="button" onclick="setDuration(60)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">2 Months</button>
                <button type="button" onclick="setDuration(90)" class="px-3 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-800 rounded-md">3 Months</button>
                <button type="button" onclick="setDuration(180)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">6 Months</button>
              </div>
            </div>
          </div>

          <!-- Statistics -->
          <div class="bg-white border border-gray-200 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-lg font-medium text-gray-900">📊 Campaign Statistics</h4>
              <div id="session-status" class="text-xs text-green-600 opacity-0 transition-opacity">
                <i class="fa-solid fa-circle-check mr-1"></i>
                Session active
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div class="text-center">
                <div id="mentions-per-week" class="text-3xl font-bold text-blue-600">{{ mentions_per_week|default:0|floatformat:0 }}</div>
                <div class="text-sm text-gray-600">Mentions per week</div>
              </div>
              <div class="text-center">
                <div id="total-mentions" class="text-3xl font-bold text-green-600">{{ estimated_total_mentions|default:0 }}</div>
                <div class="text-sm text-gray-600">Total mentions</div>
              </div>
              <div class="text-center">
                <div id="duration-text" class="text-2xl font-bold text-purple-600">{{ duration_text|default:'0 days' }}</div>
                <div class="text-sm text-gray-600">Campaign duration</div>
              </div>
              <div class="text-center">
                {% if cost_estimate %}
                  <div id="cost-estimate" class="text-3xl font-bold text-orange-600" data-rate="{{ client.rate_per_mention|default:0 }}">${{ cost_estimate|floatformat:2 }}</div>
                  <div class="text-sm text-gray-600">Estimated cost</div>
                {% else %}
                  <div id="cost-estimate" class="text-3xl font-bold text-gray-400" data-rate="{{ client.rate_per_mention|default:0 }}">--</div>
                  <div class="text-sm text-gray-600">Cost estimate</div>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- Preview -->
          <div class="bg-white border border-gray-200 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-lg font-medium text-gray-900">🗓️ Schedule Preview</h4>
              <div class="text-right">
                <div class="text-sm text-gray-500" id="preview-status">Campaign will run for {{ duration_text|default:'0 days' }}</div>
                {% if wizard_data.start_date and wizard_data.end_date %}
                  <div class="text-xs text-gray-400 mt-1" id="date-range">{{ wizard_data.start_date|date:'M d, Y' }} - {{ wizard_data.end_date|date:'M d, Y' }}</div>
                {% endif %}
              </div>
            </div>

            <div id="schedule-preview-container">
              {% if preview_data %}
                {% include 'mentions/wizard_preview_partial.html' %}
              {% else %}
                <div class="text-center py-8">
                  <div class="text-gray-400 text-lg mb-2">📅</div>
                  <p class="text-gray-500">No schedule configured. Please complete Step 2 first.</p>
                </div>
              {% endif %}
            </div>
          </div>

          <!-- Final Review -->
          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 class="text-lg font-medium text-yellow-900 mb-2">⚠️ Final Review</h4>
            <div class="text-sm text-yellow-800 space-y-1">
              <p>• Please review all details above before creating the recurring mention</p>
              <p>• Once created, {{ estimated_total_mentions|default:0 }}
                individual mentions will be generated</p>
              <p>• You can modify or pause the recurring mention after creation</p>
              <p>• All mentions will be created in "pending" status and require approval</p>
            </div>
          </div>

          <!-- Navigation -->
          <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <a href="{% url 'mentions:recurring_wizard_step2' %}" class="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium border border-gray-300 rounded-lg hover:bg-gray-50">
              <i class="fa-solid fa-arrow-left mr-2"></i>
              Previous Step
            </a>

            <div class="flex space-x-3">
              <button type="button" onclick="debugStep3()" class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 font-medium text-sm">
                <i class="fa-solid fa-bug mr-1"></i>
                Debug
              </button>
              <button type="submit" onclick="return confirmCreation()" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium">
                <i class="fa-solid fa-save mr-2"></i>
                Create Recurring Mention
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script src="{% static 'js/wizard-step3.js' %}"></script>
{% endblock %}
