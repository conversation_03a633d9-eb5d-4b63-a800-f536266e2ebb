{% if preview_data %}
  <!-- Complete schedule indicator -->
  <div class="mb-4 text-sm text-gray-600">
    <i class="fa-solid fa-calendar-check mr-1"></i>
    Complete schedule for all campaign dates
  </div>

  <div class="overflow-x-auto max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50 sticky top-0 z-10">
        <tr>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Week</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Day</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
          <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        {% for week in preview_data %}
          {% for date_item in week.dates %}
            <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
              <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">Week {{ week.week }}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ date_item.date|date:'M d, Y' }}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">{{ date_item.weekday }}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{{ date_item.show }}</span>
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-mono">{{ date_item.time }}</td>
              <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">{{ date_item.duration }}s</td>
            </tr>
          {% endfor %}
        {% endfor %}
      </tbody>
    </table>
  </div>

  <!-- Weekly Summary -->
  <div class="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    {% for week in preview_data %}
      <div class="bg-gray-50 rounded-lg p-3">
        <div class="text-sm font-medium text-gray-900">Week {{ week.week }}</div>
        <div class="text-xs text-gray-600">{{ week.dates|length }} mention{{ week.dates|length|pluralize }}</div>
        <div class="text-xs text-gray-500">Starting {{ week.week_start|date:'M d' }}</div>
      </div>
    {% endfor %}
  </div>
{% else %}
  <div class="text-center py-8">
    <div class="text-gray-400 text-lg mb-2">📅</div>
    <p class="text-gray-500">No schedule configured. Please complete Step 2 first.</p>
  </div>
{% endif %}
