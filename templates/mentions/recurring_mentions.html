{% extends 'base.html' %}
{% load static %}

{% block title %}
  Recurring Mentions
{% endblock %}

{% block extra_css %}
  <style>
    .recurring-mention {
      position: relative;
      background: linear-gradient(45deg, #f0f9ff 25%, transparent 25%), linear-gradient(-45deg, #f0f9ff 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f9ff 75%), linear-gradient(-45deg, transparent 75%, #f0f9ff 75%);
      background-size: 4px 4px;
      background-position: 0 0, 0 2px, 2px -2px, -2px 0px;
    }
    
    .recurring-mention::before {
      content: '';
      position: absolute;
      top: -2px;
      right: -2px;
      width: 12px;
      height: 12px;
      background: #0ea5e9;
      border-radius: 50%;
      z-index: 20;
    }
    
    .recurring-mention::after {
      content: '∞';
      position: absolute;
      top: -2px;
      right: -2px;
      width: 12px;
      height: 12px;
      color: white;
      font-size: 8px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 21;
    }
    
    .frequency-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.375rem;
      font-weight: 500;
    }
    
    .frequency-daily {
      background-color: #dbeafe;
      color: #1e40af;
    }
    
    .frequency-weekly {
      background-color: #d1fae5;
      color: #065f46;
    }
    
    .frequency-monthly {
      background-color: #fef3c7;
      color: #92400e;
    }
    
    .frequency-custom {
      background-color: #f3e8ff;
      color: #6b21a8;
    }

    /* Fix dropdown z-index and positioning issues */
    .dropdown-container {
      position: relative;
      z-index: 9999;
    }

    .dropdown-menu {
      position: absolute;
      z-index: 9999 !important;
      transform: translateZ(0); /* Force hardware acceleration */
    }

    /* Ensure parent containers don't clip dropdowns */
    .max-w-7xl {
      overflow: visible !important;
    }

    .bg-white.rounded-lg.shadow-sm {
      overflow: visible !important;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto" style="overflow: visible;">
    <!-- Enhanced Header -->
    <div class="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-xl shadow-lg border border-blue-200 mb-6" style="overflow: visible;">
      <div class="px-8 py-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <!-- Left side - Enhanced title and description -->
          <div class="flex-1">
            <div class="flex items-center gap-4 mb-3">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                <i class="fa-solid fa-repeat text-white text-xl"></i>
              </div>
              <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-1">Recurring Mentions</h1>
                <p class="text-blue-700 font-semibold text-sm">Professional Radio Automation System</p>
              </div>
            </div>
            <p class="text-gray-700 ml-16 text-sm">Create and manage automated mention scheduling patterns with precision timing</p>
          </div>

          <!-- Right side - Enhanced action buttons -->
          <div class="flex flex-wrap items-center gap-3">
            <!-- Bulk Approve Pending Mentions -->
            <button onclick="bulkApprovePendingMentions()"
                    class="px-5 py-3 bg-gradient-to-r from-orange-600 to-red-600 text-white font-semibold rounded-lg hover:from-orange-700 hover:to-red-700 transition-all duration-200 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                    id="bulkApproveBtn"{% if pending_mentions_count == 0 %} style="display: none;"{% endif %}>
              <i class="fa-solid fa-check-double mr-2"></i>
              Approve All (<span id="pendingCount">{{ pending_mentions_count|default:0 }}</span>)
            </button>

            <!-- Quick Create Single Mention -->
            {% comment %} <button onclick="openQuickCreateModal()" class="px-5 py-3 bg-gradient-to-r from-emerald-600 to-green-600 text-white font-semibold rounded-lg hover:from-emerald-700 hover:to-green-700 transition-all duration-200 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
              <i class="fa-solid fa-bolt mr-2"></i>
              Quick Single
            </button> {% endcomment %}

            <!-- Create Recurring Pattern -->
            {% comment %} <a href="{% url 'mentions:recurring_mention_create' %}" class="px-5 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
              <i class="fa-solid fa-plus-circle mr-2"></i>
              Create Pattern
              <span class="ml-2 text-xs bg-green-400 text-green-900 px-2.5 py-1 rounded-full font-bold animate-pulse">NEW</span>
            </a> {% endcomment %}

            <!-- Templates Dropdown -->
            <div class="dropdown-container">
              {% comment %} <button onclick="toggleTemplatesDropdown()" class="px-5 py-3 bg-gradient-to-r from-purple-600 to-violet-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-violet-700 transition-all duration-200 flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                <i class="fa-solid fa-magic-wand-sparkles mr-2"></i>
                Templates
                <i class="fa-solid fa-chevron-down ml-2 text-sm transition-transform duration-200" id="templatesChevron"></i>
              </button> {% endcomment %}

              <!-- Enhanced Dropdown Menu -->
              <div id="templatesDropdown" class="dropdown-menu hidden absolute right-0 mt-3 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden">
                <div class="bg-gradient-to-r from-purple-600 to-violet-600 px-4 py-3">
                  <div class="flex items-center text-white">
                    <i class="fa-solid fa-sparkles mr-2"></i>
                    <span class="font-bold text-sm">Quick Start Templates</span>
                  </div>
                </div>
                <div class="py-2">
                  <button onclick="createFromTemplate('hourly_sponsor')" class="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 transition-colors flex items-center group">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors">
                      <i class="fa-solid fa-clock text-blue-600"></i>
                    </div>
                    <div class="flex-1">
                      <div class="font-semibold text-gray-900">Hourly Sponsor</div>
                      <div class="text-xs text-gray-500">Regular sponsor mentions throughout the day</div>
                    </div>
                    <i class="fa-solid fa-arrow-right text-gray-400 group-hover:text-blue-600 transition-colors"></i>
                  </button>
                  <button onclick="createFromTemplate('morning_drive')" class="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-yellow-50 transition-colors flex items-center group">
                    <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-yellow-200 transition-colors">
                      <i class="fa-solid fa-sun text-yellow-600"></i>
                    </div>
                    <div class="flex-1">
                      <div class="font-semibold text-gray-900">Morning Drive</div>
                      <div class="text-xs text-gray-500">Weekday morning package (Mon-Fri)</div>
                    </div>
                    <i class="fa-solid fa-arrow-right text-gray-400 group-hover:text-yellow-600 transition-colors"></i>
                  </button>
                  <button onclick="createFromTemplate('evening_commute')" class="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 transition-colors flex items-center group">
                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-orange-200 transition-colors">
                      <i class="fa-solid fa-car text-orange-600"></i>
                    </div>
                    <div class="flex-1">
                      <div class="font-semibold text-gray-900">Evening Commute</div>
                      <div class="text-xs text-gray-500">Rush hour targeting (Mon-Fri)</div>
                    </div>
                    <i class="fa-solid fa-arrow-right text-gray-400 group-hover:text-orange-600 transition-colors"></i>
                  </button>
                  <button onclick="createFromTemplate('weekend_special')" class="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-green-50 transition-colors flex items-center group">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors">
                      <i class="fa-solid fa-calendar-weekend text-green-600"></i>
                    </div>
                    <div class="flex-1">
                      <div class="font-semibold text-gray-900">Weekend Special</div>
                      <div class="text-xs text-gray-500">Saturday & Sunday events</div>
                    </div>
                    <i class="fa-solid fa-arrow-right text-gray-400 group-hover:text-green-600 transition-colors"></i>
                  </button>
                  <button onclick="createFromTemplate('campaign')" class="w-full text-left px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 transition-colors flex items-center group">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200 transition-colors">
                      <i class="fa-solid fa-bullhorn text-purple-600"></i>
                    </div>
                    <div class="flex-1">
                      <div class="font-semibold text-gray-900">Campaign</div>
                      <div class="text-xs text-gray-500">Promotional campaign with end date</div>
                    </div>
                    <i class="fa-solid fa-arrow-right text-gray-400 group-hover:text-purple-600 transition-colors"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    {% comment %} <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-8"> {% endcomment %}
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-repeat text-blue-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Active Patterns</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.total_patterns }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-calendar-day text-green-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Daily Rotations</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.daily_rotations }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-calendar-week text-purple-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Weekly Shows</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.weekly_shows }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-bullhorn text-yellow-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Campaigns</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.campaigns }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-clock text-red-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Hourly Spots</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.hourly_spots }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-link text-indigo-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Related Patterns</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.related_mentions }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="p-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Filter Recurring Patterns</h3>
        <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
            {{ filter_form.status }}
          </div>
          <div>
            <label for="frequency" class="block text-sm font-medium text-gray-700 mb-1">Frequency</label>
            {{ filter_form.frequency }}
          </div>
          <div>
            <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
            {{ filter_form.priority }}
          </div>
          <div>
            <label for="client" class="block text-sm font-medium text-gray-700 mb-1">Client</label>
            {{ filter_form.client }}
          </div>
          <div class="md:col-span-2">
            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            {{ filter_form.search }}
          </div>
          <div>
            <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">Start Date From</label>
            {{ filter_form.date_from }}
          </div>
          <div>
            <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">Start Date To</label>
            {{ filter_form.date_to }}
          </div>
          <div class="lg:col-span-4 flex justify-end space-x-3 mt-4">
            <a href="{% url 'mentions:recurring_mentions' %}" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
              Clear Filters
            </a>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
              <i class="fa-solid fa-filter mr-2"></i>
              Apply Filters
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Recurring Mentions List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-800">Active Recurring Patterns</h3>
            <p class="text-sm text-gray-500 mt-1">
              Showing active patterns only.
              <a href="{% url 'mentions:recurring_history' %}" class="text-blue-600 hover:text-blue-800">View history</a>
              for paused, ended, finished, or canceled patterns.
            </p>
          </div>
          <div class="flex items-center space-x-4 text-xs">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-200 border border-blue-300 rounded mr-1 recurring-mention" style="background-size: 2px 2px;"></div>
              <span class="text-gray-600">Recurring Pattern</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-200 border border-green-300 rounded mr-1"></div>
              <span class="text-gray-600">Active</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-gray-200 border border-gray-300 rounded mr-1"></div>
              <span class="text-gray-600">Inactive</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-200 border border-blue-300 rounded mr-1"></div>
              <span class="text-gray-600">Additional</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-purple-200 border border-purple-300 rounded mr-1"></div>
              <span class="text-gray-600">Has Related</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-orange-200 border border-orange-300 rounded mr-1"></div>
              <span class="text-gray-600">Replacement</span>
            </div>
          </div>
        </div>
      </div>

      {% if recurring_mentions %}
        <div class="divide-y divide-gray-200">
          {% for recurring in recurring_mentions %}
            <div class="p-6 {% if not recurring.is_active %}bg-gray-50 opacity-75{% endif %}">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <h4 class="text-lg font-medium {% if not recurring.is_active %}text-gray-500{% else %}text-gray-900{% endif %}">{{ recurring.title }}</h4>
                    <span class="ml-2 frequency-badge frequency-{{ recurring.frequency }}">{{ recurring.get_frequency_display }}</span>
                    {% if recurring.is_active %}
                      <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fa-solid fa-circle text-green-400 mr-1 text-xs"></i>
                        Active
                      </span>
                    {% else %}
                      <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <i class="fa-solid fa-circle text-gray-400 mr-1 text-xs"></i>
                        Inactive
                      </span>
                    {% endif %}

                    <!-- Relationship Badge -->
                    {% with relationship_type=recurring.get_relationship_type %}
                      {% if relationship_type != 'standalone' %}
                        {% if relationship_type == 'additional' %}
                          <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <i class="fa-solid fa-plus text-blue-600 mr-1 text-xs"></i>
                            Additional
                          </span>
                        {% elif relationship_type == 'replacement' %}
                          <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                            <i class="fa-solid fa-arrow-right text-orange-600 mr-1 text-xs"></i>
                            Replacement
                          </span>
                        {% elif relationship_type == 'base' %}
                          <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            <i class="fa-solid fa-layer-group text-purple-600 mr-1 text-xs"></i>
                            Has Related
                          </span>
                        {% endif %}
                      {% endif %}
                    {% endwith %}
                  </div>

                  <!-- Related Mentions Info -->
                  {% with related_mentions=recurring.get_related_mentions %}
                    {% if related_mentions %}
                      <div class="mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                        <p class="text-xs font-medium text-gray-600 mb-2">
                          <i class="fa-solid fa-link mr-1"></i>
                          Related Schedule Patterns
                        </p>
                        <div class="space-y-1">
                          {% for related in related_mentions %}
                            <div class="flex items-center justify-between text-xs">
                              <div class="flex items-center">
                                {% if related.relationship == 'additional' %}
                                  <i class="fa-solid fa-plus text-blue-500 mr-1"></i>
                                  <span class="text-blue-700">Additional:</span>
                                {% elif related.relationship == 'base' %}
                                  <i class="fa-solid fa-layer-group text-purple-500 mr-1"></i>
                                  <span class="text-purple-700">Base pattern:</span>
                                {% elif related.relationship == 'replaced' %}
                                  <i class="fa-solid fa-arrow-left text-orange-500 mr-1"></i>
                                  <span class="text-orange-700">Replaced:</span>
                                {% endif %}
                                <span class="ml-1 font-medium text-gray-900">{{ related.mention.title }}</span>
                                <i class="fa-solid fa-check-circle text-green-500 ml-1" title="Same schedule pattern"></i>
                              </div>
                              <span class="text-gray-500">{{ related.created_at|date:"M d" }}</span>
                            </div>
                          {% endfor %}
                        </div>
                      </div>
                    {% endif %}
                  {% endwith %}

                  <p class="text-sm {% if not recurring.is_active %}text-gray-400{% else %}text-gray-600{% endif %} mb-3">{{ recurring.content|truncatewords:20 }}</p>

                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                    <div>
                      <p class="text-xs font-medium {% if not recurring.is_active %}text-gray-400{% else %}text-gray-500{% endif %}">Client</p>
                      <p class="text-sm {% if not recurring.is_active %}text-gray-500{% else %}text-gray-900{% endif %}">{{ recurring.client.name }}</p>
                    </div>
                    <div>
                      <p class="text-xs font-medium {% if not recurring.is_active %}text-gray-400{% else %}text-gray-500{% endif %}">Duration</p>
                      <p class="text-sm {% if not recurring.is_active %}text-gray-500{% else %}text-gray-900{% endif %}">{{ recurring.duration_seconds }} seconds</p>
                    </div>
                    <div>
                      <p class="text-xs font-medium {% if not recurring.is_active %}text-gray-400{% else %}text-gray-500{% endif %}">Priority</p>
                      <p class="text-sm {% if not recurring.is_active %}text-gray-500{% else %}text-gray-900{% endif %}">{{ recurring.get_priority_display }}</p>
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                    <div>
                      <p class="text-xs font-medium {% if not recurring.is_active %}text-gray-400{% else %}text-gray-500{% endif %}">Schedule</p>
                      <p class="text-sm {% if not recurring.is_active %}text-gray-500{% else %}text-gray-900{% endif %}">
                        {% if recurring.frequency == 'daily' %}
                          Every {{ recurring.interval }} day{{ recurring.interval|pluralize }}
                        {% elif recurring.frequency == 'weekly' %}
                          Every {{ recurring.interval }} week{{ recurring.interval|pluralize }}
                          {% if recurring.weekdays %}
                            on {{ recurring.weekdays|join:', ' }}
                          {% endif %}
                        {% elif recurring.frequency == 'monthly' %}
                          Every {{ recurring.interval }} month{{ recurring.interval|pluralize }}
                        {% endif %}
                      </p>
                    </div>
                    <div>
                      <p class="text-xs font-medium {% if not recurring.is_active %}text-gray-400{% else %}text-gray-500{% endif %}">Date Range</p>
                      <p class="text-sm {% if not recurring.is_active %}text-gray-500{% else %}text-gray-900{% endif %}">
                        {{ recurring.start_date|date:'M d, Y' }}
                        {% if recurring.end_date %}
                          - {{ recurring.end_date|date:'M d, Y' }}
                        {% else %}
                          - Ongoing
                        {% endif %}
                      </p>
                    </div>
                  </div>

                  <!-- Shows Assignment -->
                  <div class="mb-3">
                    <p class="text-xs font-medium {% if not recurring.is_active %}text-gray-400{% else %}text-gray-500{% endif %} mb-1">Assigned Shows</p>
                    <div class="flex flex-wrap gap-2">
                      {% for show_assignment in recurring.recurringmentionshow_set.all %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if not recurring.is_active %}bg-gray-100 text-gray-600{% else %}bg-blue-100 text-blue-800{% endif %}">{{ show_assignment.show.name }} - {{ show_assignment.scheduled_time|time:'H:i' }}</span>
                      {% empty %}
                        <span class="text-xs {% if not recurring.is_active %}text-gray-400{% else %}text-gray-500{% endif %}">No shows assigned</span>
                      {% endfor %}
                    </div>
                  </div>

                  <!-- Generated Mentions Stats -->
                  <div class="mb-3">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
                      <div class="bg-blue-50 border border-blue-200 rounded-lg p-2">
                        <p class="text-blue-600 font-medium">Generated Mentions</p>
                        <p class="text-lg font-bold text-blue-800">{{ recurring.generated_mentions_count }}</p>
                      </div>
                      <div class="bg-green-50 border border-green-200 rounded-lg p-2">
                        <p class="text-green-600 font-medium">Active/Pending</p>
                        <p class="text-lg font-bold text-green-800">{{ recurring.active_mentions_count }}</p>
                      </div>
                      <div class="bg-purple-50 border border-purple-200 rounded-lg p-2">
                        <p class="text-purple-600 font-medium">Show Slots</p>
                        <p class="text-lg font-bold text-purple-800">{{ recurring.recurringmentionshow_set.count }}</p>
                      </div>
                    </div>
                  </div>

                  <div class="flex items-center text-xs text-gray-500">
                    <span>Created {{ recurring.created_at|timesince }} ago</span>
                    {% if recurring.created_by %}
                      <span class="mx-2">•</span>
                      <span>by {{ recurring.created_by.get_full_name|default:recurring.created_by.username }}</span>
                    {% endif %}
                    {% if recurring.max_occurrences %}
                      <span class="mx-2">•</span>
                      <span>Max {{ recurring.max_occurrences }} occurrences</span>
                    {% endif %}
                  </div>
                </div>

                <div class="ml-4 flex flex-col space-y-2">
                  <!-- Check if this recurring mention has pending mentions -->
                  {% with pending_count=recurring.pending_mentions_count %}
                    {% if pending_count > 0 %}
                      <button onclick="approveRecurringMentions({{ recurring.pk }})" class="px-3 py-1 bg-orange-600 text-white text-xs font-medium rounded-md hover:bg-orange-700 flex items-center justify-center" id="approveBtn-{{ recurring.pk }}">
                        <i class="fa-solid fa-check-double mr-1"></i>
                        Approve ({{ pending_count }})
                      </button>
                    {% endif %}
                  {% endwith %}

                  {% if recurring.is_active %}
                    <a href="{% url 'mentions:recurring_mention_edit' recurring.pk %}" class="px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 text-center">
                      <i class="fa-solid fa-edit mr-1"></i>
                      Edit
                    </a>

                    <!-- Update Button - Acts as end button for running recurring mention -->
                    <button onclick="openRescheduleModal({{ recurring.pk }})" class="px-3 py-1 bg-indigo-600 text-white text-xs font-medium rounded-md hover:bg-indigo-700 flex items-center justify-center" title="Update this recurring mention with new content">
                      <i class="fa-solid fa-calendar-plus mr-1"></i>
                      Update / New
                    </button>
 <button onclick="openAddMentionModal({{ recurring.pk }})" class="px-3 py-1 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 flex items-center justify-center" title="Add additional mention to the same schedule pattern">
                      <i class="fa-solid fa-plus-circle mr-1"></i>
                      Add More
                    </button>
                    <!-- New Mention Management Buttons - Commented out for later use -->
                    <!--
                    <button onclick="openReplaceMentionModal({{ recurring.pk }})" class="px-3 py-1 bg-orange-600 text-white text-xs font-medium rounded-md hover:bg-orange-700 flex items-center justify-center" title="Replace current mention with a new one using the same schedule">
                      <i class="fa-solid fa-exchange-alt mr-1"></i>
                      Replace
                    </button>
                   
                    <button onclick="openSplitScheduleModal({{ recurring.pk }})" class="px-3 py-1 bg-purple-600 text-white text-xs font-medium rounded-md hover:bg-purple-700 flex items-center justify-center" title="Split this schedule between two different mentions">
                      <i class="fa-solid fa-scissors mr-1"></i>
                      Split Schedule
                    </button>

                    <button onclick="toggleRecurring({{ recurring.pk }}, false)" class="px-3 py-1 bg-yellow-600 text-white text-xs font-medium rounded-md hover:bg-yellow-700">
                      <i class="fa-solid fa-pause mr-1"></i>
                      Pause
                    </button>
                    -->
                  {% endif %}
                  <a href="{% url 'mentions:recurring_mention_delete' recurring.pk %}" class="px-3 py-1 bg-red-600 text-white text-xs font-medium rounded-md hover:bg-red-700 text-center">
                    <i class="fa-solid fa-trash mr-1"></i>
                    Delete
                  </a>
                </div>
              </div>
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="p-12 text-center">
          <i class="fa-solid fa-repeat text-gray-400 text-4xl mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No recurring mentions</h3>
          <p class="text-gray-500 mb-4">Create your first recurring mention pattern to automate scheduling.</p>
          <a href="{% url 'mentions:recurring_mention_create' %}" class="inline-flex items-center px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700">
            <i class="fa-solid fa-plus mr-2"></i>
            Create Recurring Mention
          </a>
        </div>
      {% endif %}
    </div>
  </div>

  <!-- Quick Create Single Mention Modal -->
  <div id="quickCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Quick Create Single Mention</h3>
        <button onclick="closeQuickCreateModal()" class="text-gray-400 hover:text-gray-600"><i class="fa-solid fa-times"></i></button>
      </div>

      <form id="quickCreateForm" class="mt-4 space-y-4">
        {% csrf_token %}

        <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
          <p class="text-sm text-blue-800">
            <i class="fa-solid fa-info-circle mr-1"></i>
            The presenter will be automatically assigned when the mention is read during the show.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Client</label>
            <select name="client" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">Select a client</option>
              <!-- Will be populated by JavaScript -->
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Show</label>
            <select name="show" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">Select a show</option>
              <!-- Will be populated by JavaScript -->
            </select>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
          <input type="text" name="title" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Content</label>
          <textarea name="content" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
            <input type="date" name="date" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Time</label>
            <input type="time" name="time" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Duration (seconds)</label>
            <input type="number" name="duration" value="30" min="10" max="300" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button type="button" onclick="closeQuickCreateModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Cancel</button>
          <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">Create Mention</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Reschedule Modal -->
  <div id="rescheduleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
          <i class="fa-solid fa-calendar-plus mr-2 text-indigo-600"></i>
          Update Recurring Mention
        </h3>
        <button onclick="closeRescheduleModal()" class="text-gray-400 hover:text-gray-600">
          <i class="fa-solid fa-times"></i>
        </button>
      </div>

      <form id="rescheduleForm" class="mt-4 space-y-4">
        {% csrf_token %}
        <input type="hidden" id="rescheduleRecurringId" name="recurring_id" value="">

        <div class="bg-indigo-50 border border-indigo-200 rounded-md p-4">
          <div class="flex items-start">
            <i class="fa-solid fa-info-circle text-indigo-600 mt-0.5 mr-2"></i>
            <div class="text-sm text-indigo-800">
              <p class="font-semibold mb-1">Update Process:</p>
              <ul class="list-disc list-inside space-y-1 text-xs">
                <li>Original mention details will be saved for audit tracking</li>
                <li>All future instances will use the new content</li>
                <li>Schedule continues from today with the same end date</li>
                <li>Pattern frequency and show assignments remain unchanged</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Current Mention Details (Read-only) -->
        <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
          <h4 class="text-sm font-semibold text-gray-700 mb-2">Current Mention Details</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
            <div>
              <span class="font-medium text-gray-600">Title:</span>
              <span id="currentTitle" class="text-gray-900"></span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Client:</span>
              <span id="currentClient" class="text-gray-900"></span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Duration:</span>
              <span id="currentDuration" class="text-gray-900"></span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Frequency:</span>
              <span id="currentFrequency" class="text-gray-900"></span>
            </div>
            <div class="md:col-span-2">
              <span class="font-medium text-gray-600">Current Content:</span>
              <div id="currentContent" class="text-gray-900 mt-1 p-2 bg-white border rounded text-sm"></div>
            </div>
          </div>
        </div>

        <!-- New Title Input -->
        <div>
          <label for="newTitle" class="block text-sm font-medium text-gray-700 mb-1">
            New Mention Title <span class="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="newTitle"
            name="new_title"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Enter the new title for this recurring mention..."
          />
          <p class="text-xs text-gray-500 mt-1">This title will replace all future instances of this recurring mention.</p>
        </div>

        <!-- New Content Input -->
        <div>
          <label for="newContent" class="block text-sm font-medium text-gray-700 mb-1">
            New Mention Content <span class="text-red-500">*</span>
          </label>
          <textarea
            id="newContent"
            name="new_content"
            rows="4"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Enter the new content for this recurring mention..."
          ></textarea>
          <p class="text-xs text-gray-500 mt-1">This content will replace all future instances of this recurring mention.</p>
        </div>

        <!-- Reschedule Options -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
          <div class="flex items-center mb-2">
            <i class="fa-solid fa-calendar-check text-yellow-600 mr-2"></i>
            <span class="text-sm font-medium text-yellow-800">Schedule Continuation</span>
          </div>
          <div class="text-xs text-yellow-700 space-y-1">
            <p><strong>New Start Date:</strong> <span id="newStartDate"></span> (Today)</p>
            <p><strong>End Date:</strong> <span id="currentEndDate"></span> (Unchanged)</p>
            <p><strong>Pattern:</strong> All frequency and show settings will be preserved</p>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button type="button" onclick="closeRescheduleModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex items-center">
            <i class="fa-solid fa-calendar-plus mr-2"></i>
            Update Mention
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Add/Replace Mention Modal -->
  <div id="addMentionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white">
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900" id="addMentionModalTitle">
          <i class="fa-solid fa-plus-circle mr-2 text-emerald-600"></i>
          Add New Mention to Schedule
        </h3>
        <button onclick="closeAddMentionModal()" class="text-gray-400 hover:text-gray-600">
          <i class="fa-solid fa-times"></i>
        </button>
      </div>

      <form id="addMentionForm" class="mt-4 space-y-6">
        {% csrf_token %}
        <input type="hidden" id="addMentionRecurringId" name="recurring_id" value="">
        <input type="hidden" id="addMentionAction" name="action" value="">

        <!-- Scenario Selection -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 class="text-sm font-semibold text-blue-800 mb-3">
            <i class="fa-solid fa-route mr-1"></i>
            Choose Your Action
          </h4>
          <div class="space-y-3">
            <label class="flex items-start space-x-3 cursor-pointer">
              <input type="radio" name="scenario" value="replace" class="mt-1" required>
              <div>
                <div class="font-medium text-blue-900">Replace Current Mention</div>
                <div class="text-xs text-blue-700">End the current mention today and start a new one with the same schedule pattern</div>
              </div>
            </label>
            <label class="flex items-start space-x-3 cursor-pointer">
              <input type="radio" name="scenario" value="add" class="mt-1" required>
              <div>
                <div class="font-medium text-blue-900">Add Additional Mention</div>
                <div class="text-xs text-blue-700">Keep current mention running and add a new concurrent mention to the same schedule</div>
              </div>
            </label>
          </div>
        </div>

        <!-- Current Schedule Pattern (Read-only) -->
        <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
          <h4 class="text-sm font-semibold text-gray-700 mb-2">Current Schedule Pattern</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
            <div>
              <span class="font-medium text-gray-600">Current Title:</span>
              <span id="currentPatternTitle" class="text-gray-900"></span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Client:</span>
              <span id="currentPatternClient" class="text-gray-900"></span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Frequency:</span>
              <span id="currentPatternFrequency" class="text-gray-900"></span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Duration:</span>
              <span id="currentPatternDuration" class="text-gray-900"></span>
            </div>
            <div class="md:col-span-2">
              <span class="font-medium text-gray-600">Shows & Times:</span>
              <div id="currentPatternShows" class="text-gray-900 mt-1"></div>
            </div>
            <div class="md:col-span-2">
              <span class="font-medium text-gray-600">Current End Date:</span>
              <span id="currentPatternEndDate" class="text-gray-900"></span>
            </div>
          </div>
        </div>

        <!-- New Mention Details -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
          <h4 class="text-sm font-semibold text-gray-700 mb-3">New Mention Details</h4>

          <div class="space-y-4">
            <!-- Title -->
            <div>
              <label for="newMentionTitle" class="block text-sm font-medium text-gray-700 mb-1">
                Title <span class="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="newMentionTitle"
                name="title"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                placeholder="Enter title for the new mention..."
              />
            </div>

            <!-- Content -->
            <div>
              <label for="newMentionContent" class="block text-sm font-medium text-gray-700 mb-1">
                Content <span class="text-red-500">*</span>
              </label>
              <textarea
                id="newMentionContent"
                name="content"
                rows="4"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                placeholder="Enter content for the new mention..."
              ></textarea>
            </div>

            <!-- Duration -->
            <div>
              <label for="newMentionDuration" class="block text-sm font-medium text-gray-700 mb-1">
                Duration (seconds)
              </label>
              <input
                type="number"
                id="newMentionDuration"
                name="duration_seconds"
                min="10"
                max="300"
                value="30"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
              />
              <p class="text-xs text-gray-500 mt-1">Leave blank to use the same duration as current mention</p>
            </div>
          </div>
        </div>

        <!-- Schedule Modification Options (for Add scenario) -->
        <div id="scheduleModificationOptions" class="bg-yellow-50 border border-yellow-200 rounded-md p-4 hidden">
          <h4 class="text-sm font-semibold text-yellow-800 mb-3">
            <i class="fa-solid fa-clock mr-1"></i>
            Schedule Options (Optional)
          </h4>
          <div class="space-y-3">
            <label class="flex items-center space-x-2">
              <input type="checkbox" id="useExactSchedule" name="use_exact_schedule" checked>
              <span class="text-sm text-yellow-800">Use exact same schedule (may create overlapping mentions)</span>
            </label>
            <div id="customScheduleOptions" class="hidden pl-6 space-y-2">
              <p class="text-xs text-yellow-700">Custom scheduling options will be available in future updates</p>
            </div>
          </div>
        </div>

        <!-- Action Preview -->
        <div id="actionPreview" class="bg-green-50 border border-green-200 rounded-md p-4">
          <h4 class="text-sm font-semibold text-green-800 mb-2">
            <i class="fa-solid fa-eye mr-1"></i>
            What Will Happen
          </h4>
          <div id="actionPreviewContent" class="text-sm text-green-700">
            <!-- Content will be populated by JavaScript -->
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button type="button" onclick="closeAddMentionModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 flex items-center">
            <i class="fa-solid fa-check mr-2"></i>
            <span id="submitButtonText">Create Mention</span>
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Split Schedule Modal -->
  <div id="splitScheduleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-5 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-md bg-white">
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
          <i class="fa-solid fa-scissors mr-2 text-purple-600"></i>
          Split Schedule Between Two Mentions
        </h3>
        <button onclick="closeSplitScheduleModal()" class="text-gray-400 hover:text-gray-600">
          <i class="fa-solid fa-times"></i>
        </button>
      </div>

      <form id="splitScheduleForm" class="mt-4 space-y-6">
        {% csrf_token %}
        <input type="hidden" id="splitRecurringId" name="recurring_id" value="">

        <!-- Information Banner -->
        <div class="bg-purple-50 border border-purple-200 rounded-md p-4">
          <div class="flex items-start">
            <i class="fa-solid fa-info-circle text-purple-600 mt-0.5 mr-2"></i>
            <div class="text-sm text-purple-800">
              <p class="font-semibold mb-1">Split Schedule Process:</p>
              <ul class="list-disc list-inside space-y-1 text-xs">
                <li>The original recurring mention will be terminated or modified</li>
                <li>Two new mentions will be created with divided schedule patterns</li>
                <li>The split ensures complete coverage with no gaps or overlaps</li>
                <li>Both mentions maintain the same client and end date</li>
                <li>Full audit trail is maintained for all changes</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Current Schedule Overview -->
        <div class="bg-gray-50 border border-gray-200 rounded-md p-4">
          <h4 class="text-sm font-semibold text-gray-700 mb-2">Current Schedule to Split</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
            <div>
              <span class="font-medium text-gray-600">Title:</span>
              <span id="splitCurrentTitle" class="text-gray-900"></span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Client:</span>
              <span id="splitCurrentClient" class="text-gray-900"></span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Frequency:</span>
              <span id="splitCurrentFrequency" class="text-gray-900"></span>
            </div>
            <div>
              <span class="font-medium text-gray-600">Duration:</span>
              <span id="splitCurrentDuration" class="text-gray-900"></span>
            </div>
            <div class="md:col-span-2">
              <span class="font-medium text-gray-600">Shows & Times:</span>
              <div id="splitCurrentShows" class="text-gray-900 mt-1"></div>
            </div>
            <div class="md:col-span-2">
              <span class="font-medium text-gray-600">Current Schedule:</span>
              <div id="splitCurrentSchedule" class="text-gray-900 mt-1 text-xs"></div>
            </div>
          </div>
        </div>

        <!-- Split Method Selection -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h4 class="text-sm font-semibold text-blue-800 mb-3">
            <i class="fa-solid fa-cut mr-1"></i>
            Choose Split Method
          </h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <label class="flex items-start space-x-3 cursor-pointer p-3 border border-blue-200 rounded-md hover:bg-blue-100">
              <input type="radio" name="split_method" value="days" class="mt-1" required>
              <div>
                <div class="font-medium text-blue-900">Split by Days</div>
                <div class="text-xs text-blue-700">Mention A: Mon/Wed/Fri, Mention B: Tue/Thu</div>
              </div>
            </label>
            <label class="flex items-start space-x-3 cursor-pointer p-3 border border-blue-200 rounded-md hover:bg-blue-100">
              <input type="radio" name="split_method" value="times" class="mt-1" required>
              <div>
                <div class="font-medium text-blue-900">Split by Time Slots</div>
                <div class="text-xs text-blue-700">Mention A: Morning slots, Mention B: Afternoon slots</div>
              </div>
            </label>
            <label class="flex items-start space-x-3 cursor-pointer p-3 border border-blue-200 rounded-md hover:bg-blue-100">
              <input type="radio" name="split_method" value="shows" class="mt-1" required>
              <div>
                <div class="font-medium text-blue-900">Split by Shows</div>
                <div class="text-xs text-blue-700">Mention A: Show 1, Mention B: Show 2</div>
              </div>
            </label>
            <label class="flex items-start space-x-3 cursor-pointer p-3 border border-blue-200 rounded-md hover:bg-blue-100">
              <input type="radio" name="split_method" value="weeks" class="mt-1" required>
              <div>
                <div class="font-medium text-blue-900">Split by Weeks</div>
                <div class="text-xs text-blue-700">Mention A: Week 1, Mention B: Week 2 (alternating)</div>
              </div>
            </label>
          </div>
        </div>

        <!-- Split Preview -->
        <div id="splitPreview" class="bg-green-50 border border-green-200 rounded-md p-4 hidden">
          <h4 class="text-sm font-semibold text-green-800 mb-2">
            <i class="fa-solid fa-eye mr-1"></i>
            Split Preview
          </h4>
          <div id="splitPreviewContent" class="text-sm text-green-700">
            <!-- Content will be populated by JavaScript -->
          </div>
        </div>

        <!-- Mention A Details -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
          <h4 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
            <span class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2">A</span>
            First Mention Details
          </h4>

          <div class="space-y-4">
            <div>
              <label for="mentionATitle" class="block text-sm font-medium text-gray-700 mb-1">
                Title <span class="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="mentionATitle"
                name="mention_a_title"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter title for first mention..."
              />
            </div>

            <div>
              <label for="mentionAContent" class="block text-sm font-medium text-gray-700 mb-1">
                Content <span class="text-red-500">*</span>
              </label>
              <textarea
                id="mentionAContent"
                name="mention_a_content"
                rows="3"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter content for first mention..."
              ></textarea>
            </div>

            <div>
              <label for="mentionADuration" class="block text-sm font-medium text-gray-700 mb-1">
                Duration (seconds)
              </label>
              <input
                type="number"
                id="mentionADuration"
                name="mention_a_duration"
                min="10"
                max="300"
                value="30"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>
          </div>
        </div>

        <!-- Mention B Details -->
        <div class="bg-white border border-gray-200 rounded-md p-4">
          <h4 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
            <span class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs mr-2">B</span>
            Second Mention Details
          </h4>

          <div class="space-y-4">
            <div>
              <label for="mentionBTitle" class="block text-sm font-medium text-gray-700 mb-1">
                Title <span class="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="mentionBTitle"
                name="mention_b_title"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter title for second mention..."
              />
            </div>

            <div>
              <label for="mentionBContent" class="block text-sm font-medium text-gray-700 mb-1">
                Content <span class="text-red-500">*</span>
              </label>
              <textarea
                id="mentionBContent"
                name="mention_b_content"
                rows="3"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter content for second mention..."
              ></textarea>
            </div>

            <div>
              <label for="mentionBDuration" class="block text-sm font-medium text-gray-700 mb-1">
                Duration (seconds)
              </label>
              <input
                type="number"
                id="mentionBDuration"
                name="mention_b_duration"
                min="10"
                max="300"
                value="30"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button type="button" onclick="closeSplitScheduleModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center">
            <i class="fa-solid fa-scissors mr-2"></i>
            Split Schedule
          </button>
        </div>
      </form>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Enhanced template dropdown functionality
    function toggleTemplatesDropdown() {
      const dropdown = document.getElementById('templatesDropdown')
      const chevron = document.getElementById('templatesChevron')
    
      dropdown.classList.toggle('hidden')
    
      // Rotate chevron
      if (dropdown.classList.contains('hidden')) {
        chevron.style.transform = 'rotate(0deg)'
      } else {
        chevron.style.transform = 'rotate(180deg)'
      }
    }
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function (event) {
      const dropdown = document.getElementById('templatesDropdown')
      const button = event.target.closest('button[onclick="toggleTemplatesDropdown()"]')
      const chevron = document.getElementById('templatesChevron')
    
      if (!button && !dropdown.contains(event.target)) {
        dropdown.classList.add('hidden')
        chevron.style.transform = 'rotate(0deg)'
      }
    })
    
    // Individual recurring mention approval
    function approveRecurringMentions(recurringId) {
      const button = document.getElementById(`approveBtn-${recurringId}`)
      const pendingCount = button.textContent.match(/\((\d+)\)/)?.[1] || 0
    
      if (confirm(`Are you sure you want to approve all ${pendingCount} pending mentions from this recurring pattern?\n\nThis will move them to "Scheduled" status.`)) {
        const originalText = button.innerHTML
    
        // Show loading state
        button.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-1"></i>Approving...'
        button.disabled = true
    
        fetch(`/mentions/recurring/${recurringId}/approve-pending/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
          }
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              // Hide the approve button since there are no more pending mentions
              button.style.display = 'none'
    
              // Update the pending count in the stats
              updatePendingCount()
    
              // Show success message
              alert(`Successfully approved ${data.approved_count} mentions from this recurring pattern!`)
    
              // Optionally refresh the page to update stats
              if (data.approved_count > 0) {
                location.reload()
              }
            } else {
              alert('Error: ' + (data.error || 'Unknown error occurred'))
              // Restore button state
              button.innerHTML = originalText
              button.disabled = false
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('An error occurred while approving mentions. Please try again.')
            // Restore button state
            button.innerHTML = originalText
            button.disabled = false
          })
      }
    }
    
    // Quick Create Modal functions
    function openQuickCreateModal() {
      document.getElementById('quickCreateModal').classList.remove('hidden')
      document.body.style.overflow = 'hidden'
    
      // Set default date to today
      const today = new Date().toISOString().split('T')[0]
      document.querySelector('#quickCreateForm input[name="date"]').value = today
    
      // Load clients and shows
      loadClientsAndShows()
    }
    
    function closeQuickCreateModal() {
      document.getElementById('quickCreateModal').classList.add('hidden')
      document.body.style.overflow = 'auto'
      document.getElementById('quickCreateForm').reset()
    }
    
    function loadClientsAndShows() {
      // This would typically make AJAX calls to get current clients and shows
      // For now, we'll populate with placeholder data
      const clientSelect = document.querySelector('#quickCreateForm select[name="client"]')
      const showSelect = document.querySelector('#quickCreateForm select[name="show"]')
    
      // Clear existing options except the first one
      clientSelect.innerHTML = '<option value="">Select a client</option>'
      showSelect.innerHTML = '<option value="">Select a show</option>'
    
      // Add placeholder options (in real implementation, these would come from AJAX)
      clientSelect.innerHTML += '<option value="1">Sample Client 1</option>'
      clientSelect.innerHTML += '<option value="2">Sample Client 2</option>'
    
      showSelect.innerHTML += '<option value="1">Morning Drive</option>'
      showSelect.innerHTML += '<option value="2">Afternoon Show</option>'
    }
    
    // Handle quick create form submission
    document.getElementById('quickCreateForm').addEventListener('submit', function (e) {
      e.preventDefault()
    
      const formData = new FormData(this)
    
      // Here you would make an AJAX call to create the mention
      // For now, we'll just show a success message
      alert('Single mention created successfully!')
      closeQuickCreateModal()
    
      // Optionally redirect to mentions list
      // window.location.href = '{% url "mentions:mention_list" %}';
    })
    
    // Template creation functions
    function createFromTemplate(templateType) {
      const templates = {
        hourly_sponsor: {
          title: 'Hourly Sponsor Mention',
          frequency: 'custom',
          content: 'This hour is brought to you by [SPONSOR NAME]. [SPONSOR MESSAGE]',
          interval: 1,
          duration: 15
        },
        morning_drive: {
          title: 'Morning Drive Package',
          frequency: 'weekly',
          content: 'Start your morning right with [CLIENT NAME]. [PROMOTIONAL MESSAGE]',
          weekdays: [0, 1, 2, 3, 4], // Monday to Friday
          duration: 30
        },
        evening_commute: {
          title: 'Evening Commute Special',
          frequency: 'weekly',
          content: 'Beat the traffic with [CLIENT NAME]. [SPECIAL OFFER]',
          weekdays: [0, 1, 2, 3, 4], // Monday to Friday
          duration: 25
        },
        weekend_special: {
          title: 'Weekend Special Event',
          frequency: 'weekly',
          content: 'This weekend at [CLIENT NAME]: [EVENT DETAILS]',
          weekdays: [5, 6], // Saturday and Sunday
          duration: 45
        },
        campaign: {
          title: 'Promotional Campaign',
          frequency: 'daily',
          content: '[CAMPAIGN MESSAGE] - Limited time offer from [CLIENT NAME]',
          interval: 1,
          duration: 30
        }
      }
    
      const template = templates[templateType]
      if (template) {
        // Redirect to new simplified wizard
        window.location.href = `{% url 'mentions:recurring_wizard' %}?template=${templateType}`
      }
    
      // Hide dropdown
      document.getElementById('templatesDropdown').classList.add('hidden')
    }
    
    // Enhanced recurring mention functions
    function toggleRecurring(recurringId, activate) {
      const action = activate ? 'activate' : 'pause'
      if (confirm(`Are you sure you want to ${action} this recurring mention?`)) {
        // Make AJAX call to toggle status
        fetch(`/mentions/recurring/${recurringId}/toggle/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ active: activate })
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              alert(`Recurring mention ${action}d successfully!`)
              location.reload()
            } else {
              alert('Error: ' + data.error)
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('An error occurred while updating the recurring mention.')
          })
      }
    }
    
    function generateMentions(recurringId) {
      if (confirm('Generate mentions for the next 30 days?')) {
        fetch(`/mentions/recurring/${recurringId}/generate/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
          }
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              alert(`Generated ${data.count} mentions successfully!`)
              // Redirect to calendar to see the generated mentions
              window.location.href = '{% url "mentions:calendar" %}'
            } else {
              alert('Error: ' + data.error)
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('An error occurred while generating mentions.')
          })
      }
    }
    
    // Close modal when clicking outside
    document.getElementById('quickCreateModal').addEventListener('click', function (e) {
      if (e.target === this) {
        closeQuickCreateModal()
      }
    })
    
    // Bulk approve pending mentions from recurring patterns
    function bulkApprovePendingMentions() {
      const pendingCount = parseInt(document.getElementById('pendingCount').textContent) || 0
    
      if (pendingCount === 0) {
        alert('No pending mentions to approve.')
        return
      }
    
      if (confirm(`Are you sure you want to approve all ${pendingCount} pending mentions generated from recurring patterns?\n\nThis will move them to "Scheduled" status and make them available for scheduling.`)) {
        const button = document.getElementById('bulkApproveBtn')
        const originalText = button.innerHTML
    
        // Show loading state
        button.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Approving...'
        button.disabled = true
    
        fetch('{% url "mentions:bulk_approve_recurring" %}', {
          method: 'POST',
          headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
          }
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              // Update the pending count
              document.getElementById('pendingCount').textContent = '0'
    
              // Show success message
              alert(`Successfully approved ${data.approved_count} mentions!\n\nThey are now available in the scheduling workflow.`)
    
              // Optionally redirect to approval workflow or calendar
              if (confirm('Would you like to view the approved mentions in the calendar?')) {
                window.location.href = '{% url "mentions:calendar" %}'
              }
            } else {
              alert('Error: ' + (data.error || 'Unknown error occurred'))
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('An error occurred while approving mentions. Please try again.')
          })
          .finally(() => {
            // Restore button state
            button.innerHTML = originalText
            button.disabled = false
          })
      }
    }
    
    // Update pending count on page load
    document.addEventListener('DOMContentLoaded', function () {
      updatePendingCount()
    })
    
    function updatePendingCount() {
      fetch('{% url "mentions:get_pending_count" %}')
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            document.getElementById('pendingCount').textContent = data.count

            // Hide button if no pending mentions
            const button = document.getElementById('bulkApproveBtn')
            if (data.count === 0) {
              button.style.display = 'none'
            } else {
              button.style.display = 'flex'
            }
          }
        })
        .catch((error) => {
          console.error('Error fetching pending count:', error)
        })
    }

    // Reschedule functionality
    function openRescheduleModal(recurringId) {
      // First, fetch the current mention data
      fetch(`/mentions/recurring/${recurringId}/reschedule/`, {
        method: 'GET',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json'
        }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Populate the modal with current data
            document.getElementById('rescheduleRecurringId').value = recurringId
            document.getElementById('currentTitle').textContent = data.data.title
            document.getElementById('currentClient').textContent = data.data.client
            document.getElementById('currentDuration').textContent = data.data.duration
            document.getElementById('currentFrequency').textContent = data.data.frequency
            document.getElementById('currentContent').textContent = data.data.content
            document.getElementById('currentEndDate').textContent = data.data.end_date
            document.getElementById('newStartDate').textContent = data.data.current_date

            // Pre-populate the new title and content fields with current values for easy editing
            document.getElementById('newTitle').value = data.data.title
            document.getElementById('newContent').value = data.data.content

            // Show the modal
            document.getElementById('rescheduleModal').classList.remove('hidden')
            document.body.style.overflow = 'hidden'

            // Focus on the title field first
            document.getElementById('newTitle').focus()
            document.getElementById('newTitle').select()
          } else {
            alert('Error loading mention data: ' + (data.error || 'Unknown error'))
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert('An error occurred while loading the mention data.')
        })
    }

    function closeRescheduleModal() {
      document.getElementById('rescheduleModal').classList.add('hidden')
      document.body.style.overflow = 'auto'
      document.getElementById('rescheduleForm').reset()
    }

    // Handle reschedule form submission
    document.getElementById('rescheduleForm').addEventListener('submit', function(e) {
      e.preventDefault()

      const recurringId = document.getElementById('rescheduleRecurringId').value
      const newTitle = document.getElementById('newTitle').value.trim()
      const newContent = document.getElementById('newContent').value.trim()

      if (!newTitle) {
        alert('Please enter a new title for the mention.')
        return
      }

      if (!newContent) {
        alert('Please enter new content for the mention.')
        return
      }

      if (newTitle.length < 3) {
        alert('Title must be at least 3 characters long.')
        return
      }

      if (newContent.length < 10) {
        alert('Content must be at least 10 characters long.')
        return
      }

      // Confirm the reschedule action
      if (!confirm('Are you sure you want to reschedule this recurring mention?\n\nThis will:\n• Save the original mention details for audit tracking\n• Update all future instances with the new title and content\n• Set today as the new start date\n• Keep the same end date and pattern settings\n\nThis action cannot be undone.')) {
        return
      }

      // Show loading state
      const submitButton = this.querySelector('button[type="submit"]')
      const originalText = submitButton.innerHTML
      submitButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Rescheduling...'
      submitButton.disabled = true

      // Send the reschedule request
      fetch(`/mentions/recurring/${recurringId}/reschedule/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          new_title: newTitle,
          new_content: newContent
        })
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert(data.message || 'Recurring mention rescheduled successfully!')
            closeRescheduleModal()
            // Reload the page to show updated data
            location.reload()
          } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'))
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert('An error occurred while rescheduling the mention. Please try again.')
        })
        .finally(() => {
          // Restore button state
          submitButton.innerHTML = originalText
          submitButton.disabled = false
        })
    })

    // Close modal when clicking outside
    document.getElementById('rescheduleModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeRescheduleModal()
      }
    })

    // Add/Replace Mention functionality
    function openReplaceMentionModal(recurringId) {
      openAddMentionModal(recurringId, 'replace')
    }

    function openAddMentionModal(recurringId, action = 'add') {
      // First, fetch the current mention data
      const endpoint = action === 'replace' ? 'replace' : 'add-mention'

      fetch(`/mentions/recurring/${recurringId}/${endpoint}/`, {
        method: 'GET',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json'
        }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Set the action and recurring ID
            document.getElementById('addMentionRecurringId').value = recurringId
            document.getElementById('addMentionAction').value = action

            // Update modal title and content based on action
            const modalTitle = document.getElementById('addMentionModalTitle')
            const submitButtonText = document.getElementById('submitButtonText')

            if (action === 'replace') {
              modalTitle.innerHTML = '<i class="fa-solid fa-exchange-alt mr-2 text-orange-600"></i>Replace Recurring Mention'
              submitButtonText.textContent = 'Replace Mention'
              // Pre-select replace scenario
              document.querySelector('input[name="scenario"][value="replace"]').checked = true
              // Hide scenario selection for replace action
              document.querySelector('input[name="scenario"][value="replace"]').closest('.space-y-3').style.display = 'none'
            } else {
              modalTitle.innerHTML = '<i class="fa-solid fa-plus-circle mr-2 text-emerald-600"></i>Add Additional Mention'
              submitButtonText.textContent = 'Add Mention'
              // Pre-select add scenario
              document.querySelector('input[name="scenario"][value="add"]').checked = true
              // Show scenario selection for add action
              document.querySelector('input[name="scenario"][value="replace"]').closest('.space-y-3').style.display = 'block'
            }

            // Populate current pattern details
            document.getElementById('currentPatternTitle').textContent = data.data.title
            document.getElementById('currentPatternClient').textContent = data.data.client
            document.getElementById('currentPatternFrequency').textContent = data.data.frequency
            document.getElementById('currentPatternDuration').textContent = data.data.duration
            document.getElementById('currentPatternEndDate').textContent = data.data.end_date

            // Populate shows
            const showsContainer = document.getElementById('currentPatternShows')
            if (data.data.shows && data.data.shows.length > 0) {
              showsContainer.innerHTML = data.data.shows.map(show => `<div class="text-xs bg-gray-100 px-2 py-1 rounded inline-block mr-1 mb-1">${show}</div>`).join('')
            } else {
              showsContainer.textContent = 'No shows assigned'
            }

            // Clear form fields
            document.getElementById('newMentionTitle').value = ''
            document.getElementById('newMentionContent').value = ''
            document.getElementById('newMentionDuration').value = data.data.duration.split(' ')[0] // Extract number from "30 seconds"

            // Update action preview
            updateActionPreview()

            // Show the modal
            document.getElementById('addMentionModal').classList.remove('hidden')
            document.body.style.overflow = 'hidden'

            // Focus on the title field
            document.getElementById('newMentionTitle').focus()
          } else {
            alert('Error loading mention data: ' + (data.error || 'Unknown error'))
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert('An error occurred while loading the mention data.')
        })
    }

    function closeAddMentionModal() {
      document.getElementById('addMentionModal').classList.add('hidden')
      document.body.style.overflow = 'auto'
      document.getElementById('addMentionForm').reset()

      // Reset scenario selection visibility
      document.querySelector('input[name="scenario"][value="replace"]').closest('.space-y-3').style.display = 'block'
    }

    function updateActionPreview() {
      const scenario = document.querySelector('input[name="scenario"]:checked')?.value
      const previewContent = document.getElementById('actionPreviewContent')
      const scheduleOptions = document.getElementById('scheduleModificationOptions')

      if (scenario === 'replace') {
        previewContent.innerHTML = `
          <ul class="space-y-1">
            <li>• Current recurring mention will be <strong>terminated today</strong></li>
            <li>• New mention will be created with the <strong>same schedule pattern</strong></li>
            <li>• New mention starts <strong>today</strong> and continues until the original end date</li>
            <li>• All show assignments and timing will be preserved</li>
            <li>• Full audit trail will be maintained for both actions</li>
          </ul>
        `
        scheduleOptions.classList.add('hidden')
      } else if (scenario === 'add') {
        previewContent.innerHTML = `
          <ul class="space-y-1">
            <li>• Current recurring mention will <strong>continue running</strong> unchanged</li>
            <li>• New mention will be added with the <strong>same schedule pattern</strong></li>
            <li>• Both mentions will run <strong>concurrently</strong> on the same days/times</li>
            <li>• System will check for potential scheduling conflicts</li>
            <li>• New mention starts <strong>today</strong> and continues until the original end date</li>
          </ul>
        `
        scheduleOptions.classList.remove('hidden')
      }
    }

    // Add event listeners for scenario selection
    document.querySelectorAll('input[name="scenario"]').forEach(radio => {
      radio.addEventListener('change', updateActionPreview)
    })

    // Handle add/replace mention form submission
    document.getElementById('addMentionForm').addEventListener('submit', function(e) {
      e.preventDefault()

      const recurringId = document.getElementById('addMentionRecurringId').value
      const action = document.getElementById('addMentionAction').value
      const scenario = document.querySelector('input[name="scenario"]:checked')?.value
      const title = document.getElementById('newMentionTitle').value.trim()
      const content = document.getElementById('newMentionContent').value.trim()
      const duration = document.getElementById('newMentionDuration').value
      const useExactSchedule = document.getElementById('useExactSchedule').checked

      // Validation
      if (!title) {
        alert('Please enter a title for the new mention.')
        return
      }

      if (!content) {
        alert('Please enter content for the new mention.')
        return
      }

      if (title.length < 3) {
        alert('Title must be at least 3 characters long.')
        return
      }

      if (content.length < 10) {
        alert('Content must be at least 10 characters long.')
        return
      }

      // Confirm the action
      const actionText = scenario === 'replace' ? 'replace the current mention' : 'add an additional mention'
      const confirmMessage = `Are you sure you want to ${actionText}?\n\nThis will:\n${scenario === 'replace' ? '• Terminate the current mention today\n• Create a new mention with the same schedule' : '• Keep the current mention running\n• Add a new concurrent mention'}\n\nThis action cannot be undone.`

      if (!confirm(confirmMessage)) {
        return
      }

      // Show loading state
      const submitButton = this.querySelector('button[type="submit"]')
      const originalText = submitButton.innerHTML
      submitButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Processing...'
      submitButton.disabled = true

      // Determine endpoint
      const endpoint = scenario === 'replace' ? 'replace' : 'add-mention'

      // Send the request
      fetch(`/mentions/recurring/${recurringId}/${endpoint}/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: title,
          content: content,
          duration_seconds: duration ? parseInt(duration) : null,
          use_exact_schedule: useExactSchedule
        })
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            let message = data.message
            if (data.warnings && data.warnings.length > 0) {
              message += '\n\nWarnings:\n' + data.warnings.join('\n')
            }
            alert(message)
            closeAddMentionModal()
            // Reload the page to show updated data
            location.reload()
          } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'))
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert('An error occurred while processing the request. Please try again.')
        })
        .finally(() => {
          // Restore button state
          submitButton.innerHTML = originalText
          submitButton.disabled = false
        })
    })

    // Close modal when clicking outside
    document.getElementById('addMentionModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeAddMentionModal()
      }
    })

    // Split Schedule functionality
    function openSplitScheduleModal(recurringId) {
      // First, fetch the current mention data
      fetch(`/mentions/recurring/${recurringId}/split-schedule/`, {
        method: 'GET',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json'
        }
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Set the recurring ID
            document.getElementById('splitRecurringId').value = recurringId

            // Populate current schedule details
            document.getElementById('splitCurrentTitle').textContent = data.data.title
            document.getElementById('splitCurrentClient').textContent = data.data.client
            document.getElementById('splitCurrentFrequency').textContent = data.data.frequency
            document.getElementById('splitCurrentDuration').textContent = data.data.duration

            // Populate shows
            const showsContainer = document.getElementById('splitCurrentShows')
            if (data.data.shows && data.data.shows.length > 0) {
              showsContainer.innerHTML = data.data.shows.map(show => `<div class="text-xs bg-gray-100 px-2 py-1 rounded inline-block mr-1 mb-1">${show}</div>`).join('')
            } else {
              showsContainer.textContent = 'No shows assigned'
            }

            // Populate detailed schedule
            const scheduleContainer = document.getElementById('splitCurrentSchedule')
            if (data.data.schedule_details && data.data.schedule_details.length > 0) {
              scheduleContainer.innerHTML = data.data.schedule_details.map(detail => `<div class="text-xs bg-blue-100 px-2 py-1 rounded mb-1">${detail}</div>`).join('')
            } else {
              scheduleContainer.textContent = 'No schedule details available'
            }

            // Set default durations
            document.getElementById('mentionADuration').value = data.data.duration.split(' ')[0]
            document.getElementById('mentionBDuration').value = data.data.duration.split(' ')[0]

            // Clear form fields
            document.getElementById('mentionATitle').value = ''
            document.getElementById('mentionAContent').value = ''
            document.getElementById('mentionBTitle').value = ''
            document.getElementById('mentionBContent').value = ''

            // Clear split method selection
            document.querySelectorAll('input[name="split_method"]').forEach(radio => {
              radio.checked = false
            })

            // Hide preview initially
            document.getElementById('splitPreview').classList.add('hidden')

            // Store data for validation
            window.splitData = data.data

            // Show the modal
            document.getElementById('splitScheduleModal').classList.remove('hidden')
            document.body.style.overflow = 'hidden'

            // Focus on the first title field
            document.getElementById('mentionATitle').focus()
          } else {
            alert('Error loading mention data: ' + (data.error || 'Unknown error'))
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert('An error occurred while loading the mention data.')
        })
    }

    function closeSplitScheduleModal() {
      document.getElementById('splitScheduleModal').classList.add('hidden')
      document.body.style.overflow = 'auto'
      document.getElementById('splitScheduleForm').reset()
      document.getElementById('splitPreview').classList.add('hidden')
      window.splitData = null
    }

    function updateSplitPreview() {
      const splitMethod = document.querySelector('input[name="split_method"]:checked')?.value
      const previewContainer = document.getElementById('splitPreview')
      const previewContent = document.getElementById('splitPreviewContent')

      if (!splitMethod || !window.splitData) {
        previewContainer.classList.add('hidden')
        return
      }

      // Validate split method feasibility
      const data = window.splitData
      let preview = {}
      let isValid = true
      let errorMessage = ''

      if (splitMethod === 'days') {
        if (data.weekdays.length < 2) {
          isValid = false
          errorMessage = 'Cannot split by days: mention runs on fewer than 2 weekdays'
        } else {
          const weekdayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
          const sortedDays = data.weekdays.sort()
          const daysA = sortedDays.filter((_, index) => index % 2 === 0)
          const daysB = sortedDays.filter((_, index) => index % 2 === 1)

          preview = {
            method: 'Split by Days',
            mentionA: `Runs on: ${daysA.map(d => weekdayNames[d]).join(', ')}`,
            mentionB: `Runs on: ${daysB.map(d => weekdayNames[d]).join(', ')}`,
            description: 'Schedule divided by weekdays with alternating pattern'
          }
        }
      } else if (splitMethod === 'times') {
        if (data.time_slot_count < 2) {
          isValid = false
          errorMessage = 'Cannot split by times: mention has fewer than 2 time slots'
        } else {
          preview = {
            method: 'Split by Time Slots',
            mentionA: `Morning/First half: ~${Math.ceil(data.time_slot_count / 2)} slots`,
            mentionB: `Afternoon/Second half: ~${Math.floor(data.time_slot_count / 2)} slots`,
            description: 'Schedule divided by time of day or slot order'
          }
        }
      } else if (splitMethod === 'shows') {
        if (data.show_count < 2) {
          isValid = false
          errorMessage = 'Cannot split by shows: mention runs on fewer than 2 different shows'
        } else {
          preview = {
            method: 'Split by Shows',
            mentionA: `First half: ~${Math.ceil(data.show_count / 2)} shows`,
            mentionB: `Second half: ~${Math.floor(data.show_count / 2)} shows`,
            description: 'Schedule divided between different radio shows'
          }
        }
      } else if (splitMethod === 'weeks') {
        if (data.frequency !== 'weekly') {
          isValid = false
          errorMessage = 'Cannot split by weeks: mention is not a weekly pattern'
        } else {
          preview = {
            method: 'Split by Weeks',
            mentionA: 'Runs on odd weeks (Week 1, 3, 5...)',
            mentionB: 'Runs on even weeks (Week 2, 4, 6...)',
            description: 'Schedule alternates between mentions every week'
          }
        }
      }

      if (!isValid) {
        previewContent.innerHTML = `
          <div class="bg-red-100 border border-red-300 rounded p-3">
            <div class="text-red-800 font-medium">Split Not Possible</div>
            <div class="text-red-700 text-sm mt-1">${errorMessage}</div>
          </div>
        `
        previewContainer.classList.remove('hidden')
        return
      }

      previewContent.innerHTML = `
        <div class="space-y-3">
          <div class="font-medium text-green-800">${preview.method}</div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div class="bg-blue-100 border border-blue-200 rounded p-2">
              <div class="font-medium text-blue-800 text-sm flex items-center">
                <span class="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2">A</span>
                First Mention
              </div>
              <div class="text-blue-700 text-xs mt-1">${preview.mentionA}</div>
            </div>
            <div class="bg-green-100 border border-green-200 rounded p-2">
              <div class="font-medium text-green-800 text-sm flex items-center">
                <span class="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs mr-2">B</span>
                Second Mention
              </div>
              <div class="text-green-700 text-xs mt-1">${preview.mentionB}</div>
            </div>
          </div>
          <div class="text-green-600 text-sm italic">${preview.description}</div>
        </div>
      `

      previewContainer.classList.remove('hidden')
    }

    // Add event listeners for split method selection
    document.querySelectorAll('input[name="split_method"]').forEach(radio => {
      radio.addEventListener('change', updateSplitPreview)
    })

    // Handle split schedule form submission
    document.getElementById('splitScheduleForm').addEventListener('submit', function(e) {
      e.preventDefault()

      const recurringId = document.getElementById('splitRecurringId').value
      const splitMethod = document.querySelector('input[name="split_method"]:checked')?.value

      const mentionATitle = document.getElementById('mentionATitle').value.trim()
      const mentionAContent = document.getElementById('mentionAContent').value.trim()
      const mentionADuration = document.getElementById('mentionADuration').value

      const mentionBTitle = document.getElementById('mentionBTitle').value.trim()
      const mentionBContent = document.getElementById('mentionBContent').value.trim()
      const mentionBDuration = document.getElementById('mentionBDuration').value

      // Validation
      if (!splitMethod) {
        alert('Please select a split method.')
        return
      }

      if (!mentionATitle || !mentionAContent) {
        alert('Please enter title and content for the first mention.')
        return
      }

      if (!mentionBTitle || !mentionBContent) {
        alert('Please enter title and content for the second mention.')
        return
      }

      if (mentionATitle.length < 3 || mentionBTitle.length < 3) {
        alert('Both titles must be at least 3 characters long.')
        return
      }

      if (mentionAContent.length < 10 || mentionBContent.length < 10) {
        alert('Both content fields must be at least 10 characters long.')
        return
      }

      // Confirm the split action
      const confirmMessage = `Are you sure you want to split this recurring mention?\n\nThis will:\n• Terminate the current mention today\n• Create two new mentions with divided schedule\n• Use ${splitMethod} method for splitting\n\nThis action cannot be undone.`

      if (!confirm(confirmMessage)) {
        return
      }

      // Show loading state
      const submitButton = this.querySelector('button[type="submit"]')
      const originalText = submitButton.innerHTML
      submitButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Splitting...'
      submitButton.disabled = true

      // Send the split request
      fetch(`/mentions/recurring/${recurringId}/split-schedule/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          split_method: splitMethod,
          mention_a_title: mentionATitle,
          mention_a_content: mentionAContent,
          mention_a_duration: mentionADuration ? parseInt(mentionADuration) : null,
          mention_b_title: mentionBTitle,
          mention_b_content: mentionBContent,
          mention_b_duration: mentionBDuration ? parseInt(mentionBDuration) : null
        })
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            let message = data.message
            if (data.warnings && data.warnings.length > 0) {
              message += '\n\nWarnings:\n' + data.warnings.join('\n')
            }
            alert(message)
            closeSplitScheduleModal()
            // Reload the page to show updated data
            location.reload()
          } else {
            alert('Error: ' + (data.error || 'Unknown error occurred'))
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert('An error occurred while splitting the schedule. Please try again.')
        })
        .finally(() => {
          // Restore button state
          submitButton.innerHTML = originalText
          submitButton.disabled = false
        })
    })

    // Close modal when clicking outside
    document.getElementById('splitScheduleModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeSplitScheduleModal()
      }
    })
  </script>
{% endblock %}
