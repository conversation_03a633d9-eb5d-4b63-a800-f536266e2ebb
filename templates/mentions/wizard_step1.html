{% extends 'base.html' %}
{% load static %}

{% block title %}Create Recurring Mention - Basic Info{% endblock %}

{% block header_actions %}
<div class="flex space-x-3">
    {% if wizard_data %}
        <a href="{% url 'mentions:recurring_wizard_clear' %}" class="px-4 py-2 bg-yellow-600 text-white font-medium rounded-md hover:bg-yellow-700 flex items-center">
            <i class="fa-solid fa-refresh mr-2"></i>
            Start Fresh
        </a>
    {% endif %}
    <a href="{% url 'mentions:recurring_mentions' %}" class="px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 flex items-center">
        <i class="fa-solid fa-arrow-left mr-2"></i>
        Back to Recurring Mentions
    </a>
</div>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Progress Indicator -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-medium">
                    1
                </div>
                <span class="ml-2 text-sm font-medium text-blue-600">Basic Information</span>
            </div>
            <div class="flex-1 mx-4">
                <div class="h-1 bg-gray-200 rounded">
                    <div class="h-1 bg-blue-600 rounded" style="width: 33%"></div>
                </div>
            </div>
            <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-600 rounded-full text-sm font-medium">
                    2
                </div>
                <span class="ml-2 text-sm text-gray-500">Schedule</span>
            </div>
            <div class="flex-1 mx-4">
                <div class="h-1 bg-gray-200 rounded"></div>
            </div>
            <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-600 rounded-full text-sm font-medium">
                    3
                </div>
                <span class="ml-2 text-sm text-gray-500">Preview</span>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-blue-50 border-b border-blue-200">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-blue-900">Step 1: Basic Information</h2>
                    <p class="text-sm text-blue-700 mt-1">Enter the mention details and select which days it should air</p>
                </div>
                {% if wizard_data %}
                    <div class="text-right">
                        <a href="{% url 'mentions:recurring_wizard_clear' %}" class="inline-flex items-center px-3 py-2 text-sm bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 border border-yellow-300">
                            <i class="fa-solid fa-refresh mr-2"></i>
                            Clear & Start Fresh
                        </a>
                        <p class="text-xs text-gray-500 mt-1">Previous data detected</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- Basic Mention Information -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="lg:col-span-2">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                            Mention Title *
                        </label>
                        <input type="text" name="title" id="title" 
                               value="{{ wizard_data.title|default:'' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="e.g., Morning Coffee Special, Weekend Sale Announcement"
                               required maxlength="200">
                        <p class="text-xs text-gray-500 mt-1">A descriptive title for this recurring mention</p>
                    </div>

                    <div class="lg:col-span-2">
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                            Mention Content *
                        </label>
                        <textarea name="content" id="content" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                  placeholder="The actual content that will be read on air..."
                                  required maxlength="1000">{{ wizard_data.content|default:'' }}</textarea>
                        <p class="text-xs text-gray-500 mt-1">The script that presenters will read on air</p>
                    </div>

                    <div>
                        <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Client *
                        </label>
                        <select name="client_id" id="client_id" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required>
                            <option value="">Select Client</option>
                            {% for client in clients %}
                                <option value="{{ client.id }}" 
                                        {% if wizard_data.client_id == client.id %}selected{% endif %}>
                                    {{ client.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div>
                        <label for="duration_seconds" class="block text-sm font-medium text-gray-700 mb-2">
                            Duration (seconds)
                        </label>
                        <input type="number" name="duration_seconds" id="duration_seconds" 
                               value="{{ wizard_data.duration_seconds|default:30 }}"
                               min="5" max="300" step="5"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="text-xs text-gray-500 mt-1">Estimated reading time</p>
                    </div>

                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                            Priority Level
                        </label>
                        <select name="priority" id="priority" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1" {% if wizard_data.priority == 1 %}selected{% endif %}>High Priority</option>
                            <option value="2" {% if wizard_data.priority == 2 or not wizard_data.priority %}selected{% endif %}>Normal Priority</option>
                            <option value="3" {% if wizard_data.priority == 3 %}selected{% endif %}>Low Priority</option>
                        </select>
                    </div>
                </div>

                <!-- Days Selection -->
                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Select Days of the Week</h3>
                    <p class="text-sm text-gray-600 mb-4">Choose which days this mention should air</p>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
                        {% for day_num, day_name in weekdays %}
                            <label class="flex items-center p-3 border border-gray-300 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <input type="checkbox" name="selected_days" value="{{ day_num }}"
                                       {% if day_num in wizard_data.selected_days %}checked{% endif %}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm font-medium text-gray-700">{{ day_name }}</span>
                            </label>
                        {% endfor %}
                    </div>
                    <p class="text-xs text-gray-500 mt-2">Select at least one day</p>
                </div>

                <!-- Navigation -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <a href="{% url 'mentions:recurring_mentions' %}" class="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium">
                        <i class="fa-solid fa-arrow-left mr-2"></i>
                        Cancel
                    </a>
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium">
                        Next: Configure Schedule
                        <i class="fa-solid fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="{% static 'js/wizard-step1.js' %}"></script>
{% endblock %}
