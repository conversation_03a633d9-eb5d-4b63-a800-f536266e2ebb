{% extends 'base.html' %}
{% load static %}

{% block title %}Reading Details - {{ reading.mention.title }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="{% url 'mentions:calendar' %}" class="text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fa-solid fa-arrow-left"></i>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">Reading Details</h1>
                </div>
                <div class="flex space-x-2">
                    {% if not reading.actual_read_time %}
                        <button onclick="markAsRead({{ reading.pk }})" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
                            <i class="fa-solid fa-check mr-1"></i>
                            Mark as Read
                        </button>
                    {% endif %}
                    <a href="{% url 'mentions:mention_detail' reading.mention.pk %}" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-eye mr-1"></i>
                        View Mention
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Reading Status -->
        <div class="p-6">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h2 class="text-2xl font-bold text-gray-900">{{ reading.mention.title }}</h2>
                    <p class="text-lg text-gray-600 mt-1">{{ reading.mention.client.name }}</p>
                    <div class="flex items-center mt-3 space-x-4">
                        {% if reading.actual_read_time %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fa-solid fa-check mr-1"></i>
                                Completed
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fa-solid fa-clock mr-1"></i>
                                Scheduled
                            </span>
                        {% endif %}
                        <span class="text-sm text-gray-500">{{ reading.mention.duration_seconds }}s duration</span>
                    </div>
                </div>
                <div class="text-right">
                    {% if reading.actual_read_time %}
                        <p class="text-sm text-gray-500">Read on</p>
                        <p class="text-sm font-medium text-gray-900">{{ reading.actual_read_time|date:"M d, Y" }}</p>
                        <p class="text-xs text-gray-500">{{ reading.actual_read_time|time:"g:i A" }}</p>
                    {% else %}
                        <p class="text-sm text-gray-500">Scheduled for</p>
                        <p class="text-sm font-medium text-gray-900">{{ reading.scheduled_date|date:"M d, Y" }}</p>
                        <p class="text-xs text-gray-500">{{ reading.scheduled_time|time:"g:i A" }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Schedule Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Show</label>
                    <div class="flex items-center">
                        <i class="fa-solid fa-broadcast-tower text-gray-400 mr-2"></i>
                        <span class="text-gray-900">{{ reading.show.name }}</span>
                    </div>
                    {% if reading.show.description %}
                        <p class="text-sm text-gray-500 mt-1">{{ reading.show.description|truncatewords:15 }}</p>
                    {% endif %}
                </div>
                
                {% if reading.presenter %}
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Presenter</label>
                    <div class="flex items-center">
                        <i class="fa-solid fa-microphone text-gray-400 mr-2"></i>
                        <span class="text-gray-900">{{ reading.presenter.get_full_name }}</span>
                    </div>
                    {% if reading.presenter.stage_name %}
                        <p class="text-sm text-gray-500 mt-1">{{ reading.presenter.stage_name }}</p>
                    {% endif %}
                </div>
                {% endif %}
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Show Time</label>
                    <div class="flex items-center">
                        <i class="fa-solid fa-clock text-gray-400 mr-2"></i>
                        <span class="text-gray-900">{{ reading.show.start_time|time:"g:i A" }} - {{ reading.show.end_time|time:"g:i A" }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content to Read -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Content to Read</h3>
        </div>
        <div class="p-6">
            <div class="bg-gray-50 rounded-lg p-4 border-l-4 border-primary-500">
                <div class="prose max-w-none">
                    {{ reading.mention.content|linebreaks }}
                </div>
            </div>
            <div class="mt-4 flex items-center justify-between text-sm text-gray-500">
                <span>Estimated reading time: {{ reading.mention.duration_seconds }} seconds</span>
                <span>Priority: {{ reading.mention.get_priority_display }}</span>
            </div>
        </div>
    </div>

    <!-- Reading Notes -->
    {% if reading.notes or reading.mention.notes %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Notes</h3>
        </div>
        <div class="p-6 space-y-4">
            {% if reading.notes %}
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Reading Notes</h4>
                <p class="text-gray-700">{{ reading.notes|linebreaks }}</p>
            </div>
            {% endif %}
            
            {% if reading.mention.notes %}
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Mention Notes</h4>
                <p class="text-gray-700">{{ reading.mention.notes|linebreaks }}</p>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}

    <!-- Reading History -->
    {% if reading.actual_read_time %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Reading History</h3>
        </div>
        <div class="p-6">
            <div class="flex items-center space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-check text-green-600"></i>
                    </div>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">
                        Read on {{ reading.actual_read_time|date:"l, F d, Y" }} at {{ reading.actual_read_time|time:"g:i A" }}
                    </p>
                    {% if reading.presenter %}
                        <p class="text-sm text-gray-500">by {{ reading.presenter.get_full_name }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Quick Actions for Upcoming Reading -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="markAsRead({{ reading.pk }})" class="flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                    <i class="fa-solid fa-check mr-2"></i>
                    Mark as Read Now
                </button>
                <button onclick="rescheduleReading({{ reading.pk }})" class="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fa-solid fa-calendar mr-2"></i>
                    Reschedule
                </button>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Mark as Read Modal -->
<div id="markReadModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Mark as Read</h3>
            <p class="text-sm text-gray-600 mb-4">Confirm that this mention has been read on air.</p>
            <textarea id="readingNotes" placeholder="Optional notes about the reading..." class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500" rows="3"></textarea>
            <div class="flex justify-end space-x-3 mt-4">
                <button onclick="closeMarkReadModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
                    Cancel
                </button>
                <button onclick="confirmMarkRead()" class="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700">
                    Mark as Read
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentReadingId = null;

function markAsRead(readingId) {
    currentReadingId = readingId;
    document.getElementById('markReadModal').classList.remove('hidden');
}

function closeMarkReadModal() {
    document.getElementById('markReadModal').classList.add('hidden');
    document.getElementById('readingNotes').value = '';
    currentReadingId = null;
}

function confirmMarkRead() {
    const notes = document.getElementById('readingNotes').value;
    
    fetch(`/mentions/readings/${currentReadingId}/mark-read/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes: notes })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error marking reading as complete');
        }
    });
}

function rescheduleReading(readingId) {
    window.location.href = `/mentions/calendar/?reading=${readingId}`;
}
</script>
{% endblock %}
