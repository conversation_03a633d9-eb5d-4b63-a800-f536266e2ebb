{% extends 'base.html' %}
{% load static %}

{% block title %}Create Recurring Mention - RadioMention{% endblock %}

{% block page_title %}Create Recurring Mention{% endblock %}

{% block header_actions %}
<div class="flex space-x-3">
    {% if wizard_data %}
        <a href="{% url 'mentions:recurring_wizard_clear' %}" class="px-4 py-2 bg-yellow-600 text-white font-medium rounded-md hover:bg-yellow-700 flex items-center">
            <i class="fa-solid fa-refresh mr-2"></i>
            Start Fresh
        </a>
    {% endif %}
    <a href="{% url 'mentions:recurring_mentions' %}" class="px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 flex items-center">
        <i class="fa-solid fa-arrow-left mr-2"></i>
        Back to Recurring Mentions
    </a>
</div>
{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Progress Steps -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            {% for i in "123" %}
                <div class="flex items-center {% if not forloop.last %}flex-1{% endif %}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium
                        {% if step >= forloop.counter %}bg-blue-600 text-white{% else %}bg-gray-200 text-gray-500{% endif %}">
                        {{ forloop.counter }}
                    </div>
                    {% if not forloop.last %}
                        <div class="flex-1 h-1 mx-4 {% if step > forloop.counter %}bg-blue-600{% else %}bg-gray-200{% endif %}"></div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
        <div class="flex justify-between mt-2 text-sm">
            <span class="{% if step == 1 %}font-medium text-blue-600{% else %}text-gray-600{% endif %}">Basic Info</span>
            <span class="{% if step == 2 %}font-medium text-blue-600{% else %}text-gray-600{% endif %}">Schedule</span>
            <span class="{% if step == 3 %}font-medium text-blue-600{% else %}text-gray-600{% endif %}">Period & Save</span>
        </div>
    </div>

    <!-- Step Content -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {% if step == 1 %}
            <!-- Step 1: Basic Info -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Step 1: Basic Information</h3>
                        <p class="text-gray-600">Enter mention details and select days of the week</p>
                    </div>
                    {% if wizard_data %}
                        <div class="text-right">
                            <a href="{% url 'mentions:recurring_wizard_clear' %}" class="inline-flex items-center px-3 py-2 text-sm bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 border border-yellow-300">
                                <i class="fa-solid fa-refresh mr-2"></i>
                                Clear & Start Fresh
                            </a>
                            <p class="text-xs text-gray-500 mt-1">Previous data detected</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Mention Details -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Mention Title *</label>
                        <input type="text" name="title" value="{{ wizard_data.title|default:'' }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                               required>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Client *</label>
                        <select name="client_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <option value="">Select Client</option>
                            {% for client in clients %}
                                <option value="{{ client.id }}"{% if wizard_data.client_id == client.id %} selected{% endif %}>
                                    {{ client.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Mention Content *</label>
                    <textarea name="content" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                              required>{{ wizard_data.content|default:'' }}</textarea>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Duration (seconds)</label>
                        <input type="number" name="duration_seconds" value="{{ wizard_data.duration_seconds|default:30 }}" 
                               min="1" max="300" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                        <select name="priority" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1" {% if wizard_data.priority == 1 %}selected{% endif %}>High</option>
                            <option value="2" {% if wizard_data.priority == 2 or not wizard_data.priority %}selected{% endif %}>Medium</option>
                            <option value="3" {% if wizard_data.priority == 3 %}selected{% endif %}>Low</option>
                        </select>
                    </div>
                </div>

                <!-- Days Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Select Days of the Week *</label>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
                        {% for day_num, day_name in weekdays %}
                            <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="checkbox" name="selected_days" value="{{ day_num }}" 
                                       {% if day_num in wizard_data.selected_days %}checked{% endif %}
                                       class="mr-2 text-blue-600 focus:ring-blue-500">
                                <span class="text-sm font-medium">{{ day_name }}</span>
                            </label>
                        {% endfor %}
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex justify-end pt-6 border-t border-gray-200">
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium">
                        Next Step
                        <i class="fa-solid fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </form>

        {% elif step == 2 %}
            <!-- Step 2: Schedule -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Step 2: Schedule Shows & Times</h3>
                <p class="text-gray-600 mb-4">Set up show schedules for each selected day</p>

                <!-- Validation Guidelines -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">
                        <i class="fa-solid fa-info-circle mr-1"></i>
                        Scheduling Guidelines
                    </h4>
                    <ul class="text-sm text-blue-800 space-y-1">
                        <li>• Select a show and time for each day you want mentions to air</li>
                        <li>• Time slots are available in <strong>precise intervals</strong> (30 seconds for short shows, 1 minute for longer shows)</li>
                        <li>• Mention times should fall within the show's broadcast hours</li>
                        <li>• <span class="text-green-600 font-medium">✓ Available</span> slots can be selected</li>
                        <li>• <span class="text-gray-500 font-medium">(Unavailable)</span> slots are grayed out due to conflicts</li>
                        <li>• <span class="text-orange-600 font-medium">⚠️ Warning</span> slots have close timing with existing mentions</li>
                        <li>• Duplicate show/time combinations <strong>within the same day</strong> are prevented</li>
                        <li>• You can add multiple time slots per day using "Add another time slot"</li>
                    </ul>
                </div>
            </div>

            <form method="post" class="space-y-6" onsubmit="return validateStep2Form()">
                {% csrf_token %}
                
                <!-- Enhanced Schedule Cards with Validation -->
                <div class="space-y-6">
                    {% for day_data in selected_days_data %}
                        <div class="border border-gray-200 rounded-lg p-4 {% if not day_data.has_available_shows %}bg-red-50 border-red-200{% endif %}">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-medium text-gray-900">{{ day_data.name }}</h4>
                                {% if not day_data.has_available_shows %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <i class="fa-solid fa-ban mr-1"></i>
                                        No Shows Available
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fa-solid fa-check mr-1"></i>
                                        {{ day_data.available_shows|length }} Show{{ day_data.available_shows|length|pluralize }} Available
                                    </span>
                                {% endif %}
                            </div>

                            {% if not day_data.has_available_shows %}
                                <div class="mb-4 p-3 bg-red-100 border border-red-300 rounded-md">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <i class="fa-solid fa-ban text-red-400"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm text-red-700">
                                                <strong>No Shows Available:</strong> No shows are configured to air on {{ day_data.name }}s.
                                                Please configure show schedules or select a different day.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}

                            <div class="space-y-3" id="day-{{ day_data.number }}-schedules">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 schedule-row">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Show *
                                            <span class="text-xs text-gray-500">({{ day_data.available_shows|length }} available)</span>
                                        </label>
                                        <select name="day_{{ day_data.number }}_shows"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 show-select"
                                                data-day="{{ day_data.number }}" onchange="validateShowTime(this)">
                                            <option value="">Select Show</option>
                                            {% for show in day_data.available_shows %}
                                                <option value="{{ show.id }}"
                                                        data-start-time="{{ show.start_time|time:"H:i" }}"
                                                        data-end-time="{{ show.end_time|time:"H:i" }}">
                                                    {{ show.name }}
                                                    {% if show.start_time and show.end_time %}
                                                        ({{ show.start_time|time:"H:i" }} - {{ show.end_time|time:"H:i" }})
                                                    {% endif %}
                                                </option>
                                            {% endfor %}
                                            {% if day_data.unavailable_shows %}
                                                <optgroup label="🚫 Not available on {{ day_data.name }}s">
                                                    {% for show in day_data.unavailable_shows %}
                                                        <option value="" disabled
                                                                style="color: #9CA3AF !important; background-color: #F3F4F6 !important; font-style: italic;">
                                                            {{ show.name }} - Not scheduled for {{ day_data.name }}s
                                                        </option>
                                                    {% endfor %}
                                                </optgroup>
                                            {% endif %}
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Mention Time * <span class="text-xs text-gray-500">(precise intervals)</span>
                                            <span class="text-xs text-gray-500 time-hint" style="display: none;"></span>
                                        </label>
                                        <select name="day_{{ day_data.number }}_times"
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 time-select"
                                                data-day="{{ day_data.number }}" onchange="validateTimeSelection(this)" disabled>
                                            <option value="">Select a show first</option>
                                        </select>
                                        <div class="time-validation-message text-xs mt-1" style="display: none;"></div>
                                        <div class="conflict-indicator text-xs mt-1" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>

                            <button type="button" onclick="addScheduleRow({{ day_data.number }})"
                                    class="mt-3 text-sm text-blue-600 hover:text-blue-800 flex items-center">
                                <i class="fa-solid fa-plus mr-1"></i> Add another time slot
                            </button>

                            <!-- Show summary for this day -->
                            <div class="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600 day-summary" id="day-{{ day_data.number }}-summary" style="display: none;">
                                <strong>Scheduled:</strong> <span class="summary-text">No time slots configured</span>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Overall Schedule Summary -->
                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg" id="schedule-summary" style="display: none;">
                    <h5 class="text-sm font-medium text-blue-900 mb-2">
                        <i class="fa-solid fa-calendar-check mr-1"></i>
                        Schedule Summary
                    </h5>
                    <div class="text-sm text-blue-800" id="summary-content">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Advanced Options -->
                <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <h5 class="text-sm font-medium text-gray-900 mb-3">
                        <i class="fa-solid fa-cog mr-1"></i>
                        Advanced Options
                    </h5>
                    <div class="flex items-center">
                        <input type="checkbox" name="check_conflicts" id="check_conflicts" value="true"
                               {% if not allow_overlapping_mentions %}checked{% endif %}
                               class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="check_conflicts" class="ml-2 text-sm text-gray-700">
                            Check for conflicts with existing recurring mentions
                        </label>
                    </div>
                    {% if allow_overlapping_mentions %}
                    <p class="text-xs text-green-600 mt-1">
                        <i class="fa-solid fa-check-circle mr-1"></i>
                        Overlapping mentions are enabled for this organization. Conflicts will not be checked by default.
                    </p>
                    {% else %}
                    <p class="text-xs text-gray-500 mt-1">
                        Uncheck this if you want to allow overlapping schedules (e.g., different clients using the same show/time)
                    </p>
                    {% endif %}
                </div>

                <!-- Navigation -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <a href="{% url 'mentions:recurring_wizard_step' step=1 %}" class="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium">
                        <i class="fa-solid fa-arrow-left mr-2"></i>
                        Previous Step
                    </a>
                    <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium">
                        Next Step
                        <i class="fa-solid fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </form>

        {% elif step == 3 %}
            <!-- Step 3: Period & Save -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Step 3: Set Period & Preview</h3>
                <p class="text-gray-600">Set the campaign duration and review the complete schedule</p>
            </div>

            <!-- Campaign Summary -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h4 class="text-lg font-medium text-blue-900 mb-2">📋 Campaign Summary</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-blue-800">Client:</span>
                        <span class="text-blue-700">{{ client.name }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-blue-800">Mention:</span>
                        <span class="text-blue-700">{{ wizard_data.title }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-blue-800">Duration:</span>
                        <span class="text-blue-700">{{ wizard_data.duration_seconds|default:30 }} seconds</span>
                    </div>
                </div>
            </div>

            <form method="post" class="space-y-6" id="step3-form">
                {% csrf_token %}

                <!-- Period Settings -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">📅 Campaign Period</h4>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Start Date *</label>
                            <input type="date" name="start_date" value="{{ wizard_data.start_date|default:'' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   required min="{{ today|date:'Y-m-d' }}" onchange="updatePreview()">
                            <p class="text-xs text-gray-500 mt-1">Campaign start date</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Duration (weeks) *</label>
                            <input type="number" name="weeks" value="{{ wizard_data.weeks|default:4 }}"
                                   min="1" max="52" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   required onchange="updatePreview()" id="weeks-input">
                            <p class="text-xs text-gray-500 mt-1">Campaign duration in weeks</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">End Date (optional)</label>
                            <input type="date" name="end_date" value="{{ wizard_data.end_date|default:'' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   onchange="updateFromEndDate()" id="end-date-input">
                            <p class="text-xs text-gray-500 mt-1">Alternative to duration</p>
                        </div>
                    </div>

                    <!-- Quick Duration Buttons -->
                    <div class="mt-4">
                        <p class="text-sm font-medium text-gray-700 mb-2">Quick Select:</p>
                        <div class="flex flex-wrap gap-2">
                            <button type="button" onclick="setWeeks(1)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">1 Week</button>
                            <button type="button" onclick="setWeeks(2)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">2 Weeks</button>
                            <button type="button" onclick="setWeeks(4)" class="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-md">1 Month</button>
                            <button type="button" onclick="setWeeks(8)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">2 Months</button>
                            <button type="button" onclick="setWeeks(12)" class="px-3 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-800 rounded-md">3 Months</button>
                            <button type="button" onclick="setWeeks(26)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">6 Months</button>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-lg font-medium text-gray-900">📊 Campaign Statistics</h4>
                        <div id="session-status" class="text-xs text-green-600 opacity-0 transition-opacity">
                            <i class="fa-solid fa-circle-check mr-1"></i>
                            Session active
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-600">{{ mentions_per_week|default:0 }}</div>
                            <div class="text-sm text-gray-600">Mentions per week</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-600">{{ estimated_total_mentions|default:0 }}</div>
                            <div class="text-sm text-gray-600">Total mentions</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-purple-600">{{ total_weeks|default:0 }}</div>
                            <div class="text-sm text-gray-600">Total weeks</div>
                        </div>
                        <div class="text-center">
                            {% if cost_estimate %}
                                <div class="text-3xl font-bold text-orange-600" data-rate="{{ client.rate_per_mention|default:0 }}">${{ cost_estimate|floatformat:2 }}</div>
                                <div class="text-sm text-gray-600">Estimated cost</div>
                            {% else %}
                                <div class="text-3xl font-bold text-gray-400" data-rate="{{ client.rate_per_mention|default:0 }}">--</div>
                                <div class="text-sm text-gray-600">Cost estimate</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Preview -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-lg font-medium text-gray-900">🗓️ Schedule Preview</h4>
                        <span class="text-sm text-gray-500" id="preview-status">
                            {% if preview_weeks < total_weeks %}
                                Showing first {{ preview_weeks }} of {{ total_weeks }} weeks
                            {% else %}
                                Showing all {{ total_weeks }} weeks
                            {% endif %}
                        </span>
                    </div>

                    <div id="schedule-preview-container">
                        {% if preview_data %}
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Week</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Day</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        {% for week in preview_data %}
                                            {% for date_item in week.dates %}
                                                <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
                                                    <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                                                        Week {{ week.week }}
                                                    </td>
                                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                        {{ date_item.date|date:"M d, Y" }}
                                                    </td>
                                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                                                        {{ date_item.weekday }}
                                                    </td>
                                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                            {{ date_item.show }}
                                                        </span>
                                                    </td>
                                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-mono">
                                                        {{ date_item.time }}
                                                    </td>
                                                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">
                                                        {{ date_item.duration }}s
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- Weekly Summary -->
                            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                {% for week in preview_data %}
                                    <div class="bg-gray-50 rounded-lg p-3">
                                        <div class="text-sm font-medium text-gray-900">Week {{ week.week }}</div>
                                        <div class="text-xs text-gray-600">{{ week.dates|length }} mention{{ week.dates|length|pluralize }}</div>
                                        <div class="text-xs text-gray-500">Starting {{ week.week_start|date:"M d" }}</div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-8">
                                <div class="text-gray-400 text-lg mb-2">📅</div>
                                <p class="text-gray-500">No schedule configured. Please complete Step 2 first.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Final Review -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h4 class="text-lg font-medium text-yellow-900 mb-2">⚠️ Final Review</h4>
                    <div class="text-sm text-yellow-800 space-y-1">
                        <p>• Please review all details above before creating the recurring mention</p>
                        <p>• Once created, {{ estimated_total_mentions|default:0 }} individual mentions will be generated</p>
                        <p>• You can modify or pause the recurring mention after creation</p>
                        <p>• All mentions will be created in "pending" status and require approval</p>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <a href="{% url 'mentions:recurring_wizard_step' step=2 %}" class="px-6 py-3 text-gray-600 hover:text-gray-800 font-medium border border-gray-300 rounded-lg hover:bg-gray-50">
                        <i class="fa-solid fa-arrow-left mr-2"></i>
                        Previous Step
                    </a>

                    <div class="flex space-x-3">
                        <button type="button" onclick="validateAndPreview()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium">
                            <i class="fa-solid fa-eye mr-2"></i>
                            Update Preview
                        </button>
                        <button type="submit" onclick="return confirmCreation()" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium">
                            <i class="fa-solid fa-save mr-2"></i>
                            Create Recurring Mention
                        </button>
                    </div>
                </div>
            </form>
        {% endif %}
    </div>
</div>

<style>
/* Enhanced styling for disabled show options */
select option:disabled {
    color: #9CA3AF !important;
    background-color: #F3F4F6 !important;
    font-style: italic !important;
    text-decoration: line-through !important;
}

select optgroup[label*="🚫"] {
    color: #9CA3AF !important;
    font-style: italic !important;
}

/* Ensure disabled options are clearly distinguishable */
.show-select option[disabled] {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
}

/* Duplicate highlighting styles */
.duplicate-highlight {
    background-color: #FEF3C7 !important;
    border-color: #F59E0B !important;
    animation: pulse-warning 2s infinite;
}

/* Conflict highlighting styles */
.conflict-highlight {
    background-color: #FEE2E2 !important;
    border-color: #EF4444 !important;
    animation: pulse-error 2s infinite;
}

.conflict-indicator {
    font-size: 0.75rem;
    line-height: 1rem;
}

.border-orange-300 {
    border-color: #FDBA74 !important;
}

/* Time slot dropdown styling */
.time-select option.unavailable-slot {
    color: #9CA3AF !important;
    background-color: #F3F4F6 !important;
    font-style: italic;
}

.time-select option.warning-slot {
    color: #D97706 !important;
    background-color: #FEF3C7 !important;
}

.time-select option.available-slot {
    color: #059669 !important;
    background-color: #ECFDF5 !important;
}

@keyframes pulse-warning {
    0%, 100% {
        background-color: #FEF3C7;
    }
    50% {
        background-color: #FDE68A;
    }
}

@keyframes pulse-error {
    0%, 100% {
        background-color: #FEE2E2;
    }
    50% {
        background-color: #FECACA;
    }
}

/* Enhanced border colors for validation states */
.border-red-300 {
    border-color: #FCA5A5 !important;
    box-shadow: 0 0 0 1px #FCA5A5 !important;
}

.border-green-300 {
    border-color: #86EFAC !important;
    box-shadow: 0 0 0 1px #86EFAC !important;
}

.border-yellow-300 {
    border-color: #FDE047 !important;
    box-shadow: 0 0 0 1px #FDE047 !important;
}
</style>

<script src="{% static 'js/recurring-wizard.js' %}"></script>
<script>
// Initialize the recurring wizard with data from Django
document.addEventListener('DOMContentLoaded', function() {
    // Show data for validation
    const showsData = {
        {% for show in shows %}
        {{ show.id }}: {
            name: '{{ show.name|escapejs }}',
            startTime: '{{ show.start_time|time:"H:i" }}',
            endTime: '{{ show.end_time|time:"H:i" }}',
            timeFrame: '{{ show.get_time_frame_display|escapejs }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    };

    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    // Add mentions per week data for statistics
    const mentionsPerWeekElement = document.createElement('div');
    mentionsPerWeekElement.dataset.mentionsPerWeek = '{{ mentions_per_week|default:0 }}';
    mentionsPerWeekElement.style.display = 'none';
    document.body.appendChild(mentionsPerWeekElement);

    // Set up API URLs for wizard
    window.wizardApiUrls = {
        getAvailableTimeSlots: '{% url "mentions:get_available_time_slots" %}',
        checkTimeSlotConflicts: '{% url "mentions:check_time_slot_conflicts" %}'
    };

    // Pass organization settings to JavaScript
    window.organizationSettings = {
        allowOverlappingMentions: {{ allow_overlapping_mentions|yesno:"true,false" }}
    };

    // Initialize the recurring wizard functionality
    initializeRecurringWizard(showsData, csrfToken);
});

// Note: Functions moved to external file recurring-wizard.js
// Some wizard-specific functions that use Django template tags need to remain here

function loadTimeSlots(showId, dayNumber, timeSelect) {
    // Show loading state
    timeSelect.innerHTML = '<option value="">Loading time slots...</option>';
    timeSelect.disabled = true;

    // Make API call to get available time slots
    fetch('{% url "mentions:get_available_time_slots" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            show_id: showId,
            weekday: parseInt(dayNumber)  // dayNumber is already 0-based (Monday=0, Tuesday=1, etc.)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateTimeSlots(timeSelect, data.time_slots);
        } else {
            timeSelect.innerHTML = '<option value="">Error loading time slots</option>';
            console.error('Error loading time slots:', data.error);
        }
    })
    .catch(error => {
        console.error('Error loading time slots:', error);
        timeSelect.innerHTML = '<option value="">Error loading time slots</option>';
    });
}

function populateTimeSlots(timeSelect, timeSlots) {
    // Clear existing options
    timeSelect.innerHTML = '<option value="">Select time slot</option>';

    // Add time slot options
    timeSlots.forEach(slot => {
        const option = document.createElement('option');
        option.value = slot.time;

        // Create display text with availability indicator
        let displayText = slot.time_display;
        let optionClass = '';

        if (!slot.is_available) {
            displayText += ' (Unavailable)';
            option.disabled = true;
            optionClass = 'unavailable-slot';
        } else if (slot.warnings && slot.warnings.length > 0) {
            displayText += ' ⚠️';
            optionClass = 'warning-slot';
        } else {
            displayText += ' ✓';
            optionClass = 'available-slot';
        }

        option.textContent = displayText;
        option.className = optionClass;

        // Store conflict/warning data
        if (slot.conflicts) {
            option.dataset.conflicts = JSON.stringify(slot.conflicts);
        }
        if (slot.warnings) {
            option.dataset.warnings = JSON.stringify(slot.warnings);
        }

        timeSelect.appendChild(option);
    });

    // Enable the select
    timeSelect.disabled = false;
}

// Note: Most validation functions moved to external file recurring-wizard.js
// Keeping wizard-specific functions that use Django template tags

// Functions that need Django template tags - keeping minimal template-specific code

function updateSchedulePreview(startDate, weeks, endDate) {
    // Call the external function with template-specific URL handling
    updateSchedulePreviewTemplate(startDate, weeks, endDate);
}

// Step 3 specific functions - now using external functions with template data

// These functions are now in the external JS file, just keeping template-specific wrappers

// Note: Most functions moved to external file recurring-wizard.js
// Only keeping minimal template-specific wrappers

function confirmCreation() {
    // Call external function with template data
    return confirmCreationWithTemplate({{ mentions_per_week|default:0 }});
}

// Note: Most initialization moved to external file recurring-wizard.js
// The external file will handle the main initialization
</script>
{% endblock %}
