{% extends 'base.html' %}
{% load static %}

{% block title %}
  Create Recurring Mention - Schedule
{% endblock %}

{% block header_actions %}
  <div class="flex space-x-3">
    <a href="{% url 'mentions:recurring_wizard_clear' %}" class="px-4 py-2 bg-yellow-600 text-white font-medium rounded-md hover:bg-yellow-700 flex items-center">
      <i class="fa-solid fa-refresh mr-2"></i>
      Start Fresh
    </a>
    <a href="{% url 'mentions:recurring_mentions' %}" class="px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 flex items-center">
      <i class="fa-solid fa-arrow-left mr-2"></i>
      Back to Recurring Mentions
    </a>
  </div>
{% endblock %}

{% block content %}
  <div class="max-w-6xl mx-auto">
    <!-- Progress Indicator -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full text-sm font-medium">
            <i class="fa-solid fa-check"></i>
          </div>
          <span class="ml-2 text-sm font-medium text-green-600">Basic Information</span>
        </div>
        <div class="flex-1 mx-4">
          <div class="h-1 bg-green-600 rounded"></div>
        </div>
        <div class="flex items-center">
          <div class="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-medium">2</div>
          <span class="ml-2 text-sm font-medium text-blue-600">Schedule</span>
        </div>
        <div class="flex-1 mx-4">
          <div class="h-1 bg-gray-200 rounded">
            <div class="h-1 bg-blue-600 rounded" style="width: 66%"></div>
          </div>
        </div>
        <div class="flex items-center">
          <div class="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-600 rounded-full text-sm font-medium">3</div>
          <span class="ml-2 text-sm text-gray-500">Preview</span>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
      <div class="px-6 py-4 bg-blue-50 border-b border-blue-200">
        <h2 class="text-xl font-semibold text-blue-900">Step 2: Configure Schedule</h2>
        <p class="text-sm text-blue-700 mt-1">Set up shows and times for each selected day</p>
      </div>

      <div class="p-6">
        <!-- Campaign Summary -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
          <h4 class="text-lg font-medium text-gray-900 mb-2">📋 Campaign Details</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium text-gray-800">Title:</span>
              <span class="text-gray-700">{{ wizard_data.title }}</span>
            </div>
            <div>
              <span class="font-medium text-gray-800">Duration:</span>
              <span class="text-gray-700">{{ wizard_data.duration_seconds|default:30 }} seconds</span>
            </div>
          </div>
        </div>

        <form method="post" class="space-y-6" onsubmit="return validateStep2Form()">
          {% csrf_token %}

          <!-- Schedule Configuration -->
          <div class="space-y-6">
            {% for day_data in selected_days_data %}
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-lg font-medium text-gray-900">📅 {{ day_data.name }}</h4>
                  {% if not day_data.has_available_shows %}
                    <div class="text-sm text-red-600 bg-red-50 px-3 py-1 rounded-md">
                      <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                      No shows available for {{ day_data.name }}s
                    </div>
                  {% endif %}
                </div>

                <div class="space-y-3" id="day-{{ day_data.number }}-schedules">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 schedule-row">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        Show *
                        <span class="text-xs text-gray-500">({{ day_data.available_shows|length }} available)</span>
                      </label>
                      <select name="day_{{ day_data.number }}_shows" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 show-select" data-day="{{ day_data.number }}" onchange="validateShowTime(this)">
                        <option value="">Select Show</option>
                        {% for show in day_data.available_shows %}
                          <option value="{{ show.id }}" data-start-time="{{ show.start_time|time:'H:i' }}" data-end-time="{{ show.end_time|time:'H:i' }}">
                            {{ show.name }}
                            {% if show.start_time and show.end_time %}
                              ({{ show.start_time|time:'H:i' }} - {{ show.end_time|time:'H:i' }})
                            {% endif %}
                          </option>
                        {% endfor %}
                        {% if day_data.unavailable_shows %}
                          <optgroup label="🚫 Not available on {{ day_data.name }}s">
                            {% for show in day_data.unavailable_shows %}
                              <option value="" disabled style="color: #9CA3AF !important; background-color: #F3F4F6 !important; font-style: italic;">{{ show.name }} - Not scheduled for {{ day_data.name }}s</option>
                            {% endfor %}
                          </optgroup>
                        {% endif %}
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">
                        Mention Time * <span class="text-xs text-gray-500">(precise intervals)</span>
                        <span class="text-xs text-gray-500 time-hint" style="display: none;"></span>
                      </label>
                      <select name="day_{{ day_data.number }}_times" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 time-select" data-day="{{ day_data.number }}" onchange="validateTimeSelection(this)" disabled>
                        <option value="">Select a show first</option>
                      </select>
                      <div class="time-validation-message text-xs mt-1" style="display: none;"></div>
                      <div class="conflict-indicator text-xs mt-1" style="display: none;"></div>
                    </div>
                  </div>
                </div>

                <button type="button" onclick="addScheduleRow({{ day_data.number }})" class="mt-3 text-sm text-blue-600 hover:text-blue-800 flex items-center"><i class="fa-solid fa-plus mr-1"></i> Add another time slot</button>

                <!-- Show summary for this day -->
                <div class="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600 day-summary" id="day-{{ day_data.number }}-summary" style="display: none;">
                  <strong>Scheduled:</strong> <span class="summary-text">No time slots configured</span>
                </div>
              </div>
            {% endfor %}
          </div>

          <!-- Overall Schedule Summary -->
          <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg" id="schedule-summary" style="display: none;">
            <h5 class="text-sm font-medium text-blue-900 mb-2">
              <i class="fa-solid fa-calendar-check mr-1"></i>
              Schedule Summary
            </h5>
            <div class="text-sm text-blue-800" id="summary-content">
              <!-- Will be populated by JavaScript -->
            </div>
          </div>

          <!-- Advanced Options -->
          <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <h5 class="text-sm font-medium text-gray-900 mb-3">
              <i class="fa-solid fa-cog mr-1"></i>
              Advanced Options
            </h5>
            <div class="flex items-center">
              <input type="checkbox" name="check_conflicts" id="check_conflicts" value="true"
                     {% if not allow_overlapping_mentions %}checked{% endif %}
                     class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
              <label for="check_conflicts" class="ml-2 text-sm text-gray-700">Check for conflicts with existing recurring mentions</label>
            </div>
            {% if allow_overlapping_mentions %}
            <p class="text-xs text-green-600 mt-1">
              <i class="fa-solid fa-check-circle mr-1"></i>
              Overlapping mentions are enabled for this organization. Conflicts will not be checked by default.
            </p>
            {% else %}
            <p class="text-xs text-gray-500 mt-1">Uncheck this if you want to allow overlapping schedules (e.g., different clients using the same show/time)</p>
            {% endif %}
          </div>

          <!-- Navigation -->
          <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <a href="{% url 'mentions:recurring_wizard_step1' %}" class="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium">
              <i class="fa-solid fa-arrow-left mr-2"></i>
              Previous Step
            </a>
            <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium">
              Next: Preview & Save
              <i class="fa-solid fa-arrow-right ml-2"></i>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <style>
    /* Ensure select options are visible */
    .time-select option {
      display: block !important;
      visibility: visible !important;
      color: #374151 !important;
      background-color: #ffffff !important;
      padding: 4px 8px !important;
      font-size: 14px !important;
    }
    
    .time-select option.available-slot {
      color: #059669 !important;
      background-color: #ecfdf5 !important;
    }
    
    .time-select option.warning-slot {
      color: #d97706 !important;
      background-color: #fef3c7 !important;
    }
    
    .time-select option.unavailable-slot {
      color: #9ca3af !important;
      background-color: #f3f4f6 !important;
    }
    
    .time-select {
      min-height: 38px !important;
    }
    
    /* Manual input option styling */
    .time-select option[value='MANUAL_INPUT'] {
      color: #2563eb !important;
      background-color: #eff6ff !important;
      font-weight: bold !important;
    }
    
    /* Custom time option styling */
    .time-select option[data-is-custom='true'] {
      color: #2563eb !important;
      background-color: #eff6ff !important;
      font-weight: bold !important;
    }
    
    /* Blue border for custom time inputs */
    .border-blue-300 {
      border-color: #93c5fd !important;
    }
  </style>

  <script src="{% static 'js/wizard-step2.js' %}"></script>
  <script>
// Initialize the wizard step 2 with data from Django
document.addEventListener('DOMContentLoaded', function() {
    // Show data for validation
    const showsData = {
        {% for show in shows %}
        {{ show.id }}: {
            name: '{{ show.name|escapejs }}',
            startTime: '{{ show.start_time|time:"H:i" }}',
            endTime: '{{ show.end_time|time:"H:i" }}',
            timeFrame: '{{ show.get_time_frame_display|escapejs }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    };

    // Set up API URLs for wizard
    window.wizardApiUrls = {
        getAvailableTimeSlots: '{% url "mentions:get_available_time_slots" %}',
        checkTimeSlotConflicts: '{% url "mentions:check_time_slot_conflicts" %}'
    };

    // Pass organization settings to JavaScript
    window.organizationSettings = {
        allowOverlappingMentions: {{ allow_overlapping_mentions|yesno:"true,false" }}
    };

    // Pass show data to the global scope for validation
    window.showsData = showsData;

    // Add debug button to first day for testing
    setTimeout(function() {
        const firstDayContainer = document.querySelector('[id*="day-"][id*="-schedules"]');
        if (firstDayContainer) {
            const debugButton = document.createElement('button');
            debugButton.type = 'button';
            debugButton.className = 'mt-2 text-xs text-red-500 hover:text-red-700 border border-red-300 px-2 py-1 rounded';
            debugButton.innerHTML = '<i class="fa-solid fa-bug mr-1"></i> Debug Time Slots';
            debugButton.onclick = function() {
                const dayNumber = firstDayContainer.id.match(/day-(\d+)/)[1];
                testTimeSlotLoading(dayNumber);
            };
            firstDayContainer.appendChild(debugButton);
        }
    }, 1000);
});
</script>
{% endblock %}
