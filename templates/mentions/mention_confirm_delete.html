{% extends 'base.html' %}
{% load static %}

{% block title %}
  Delete Mention - {{ mention.title }}
{% endblock %}

{% block content %}
  <div class="max-w-md mx-auto mt-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center">
          <a href="{% url 'mentions:mention_detail' mention.pk %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
          <h1 class="text-xl font-semibold text-gray-900">Delete Mention</h1>
        </div>
      </div>

      <div class="p-6">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
          </div>

          <h3 class="text-lg font-medium text-gray-900 mb-2">Are you sure you want to delete this mention?</h3>

          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div class="text-left">
              <p class="font-medium text-gray-900">{{ mention.title }}</p>
              <p class="text-sm text-gray-600">Client: {{ mention.client.name }}</p>
              <p class="text-sm text-gray-500">Created: {{ mention.created_at|date:'M d, Y' }}</p>
              <div class="mt-2">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if mention.status == 'pending' %}
                    


                    bg-yellow-100 text-yellow-800




                  {% elif mention.status == 'scheduled' %}
                    


                    bg-blue-100 text-blue-800




                  {% elif mention.status == 'completed' %}
                    


                    bg-green-100 text-green-800




                  {% elif mention.status == 'cancelled' %}
                    


                    bg-red-100 text-red-800




                  {% endif %}">
                  {{ mention.get_status_display }}
                </span>
                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if mention.priority == 'high' %}
                    


                    bg-red-100 text-red-800




                  {% elif mention.priority == 'medium' %}
                    


                    bg-yellow-100 text-yellow-800




                  {% else %}
                    


                    bg-green-100 text-green-800




                  {% endif %}">
                  {{ mention.get_priority_display }} Priority
                </span>
              </div>
            </div>
          </div>

          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>This action cannot be undone. Deleting this mention will:</p>
                  <ul class="list-disc list-inside mt-2 space-y-1">
                    <li>Remove the mention permanently</li>
                    <li>Cancel all scheduled readings</li>
                    <li>Delete all associated data</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Show scheduled readings if any -->
          {% if mention.mentionreading.exists %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fa-solid fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Scheduled Readings Found</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <p>
                      This mention has <strong>{{ mention.mentionreading.count }}</strong> scheduled reading(s):
                    </p>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                      {% for reading in mention.mentionreading.all %}
                        <li>{{ reading.show.name }} - {{ reading.scheduled_date|date:'M d' }} at {{ reading.scheduled_time|time:'g:i A' }}</li>
                      {% endfor %}
                    </ul>
                    <p class="mt-2 font-medium">All these readings will be cancelled.</p>
                  </div>
                </div>
              </div>
            </div>
          {% endif %}

          <!-- Show content preview -->
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
            <h4 class="text-sm font-medium text-gray-800 mb-2">Content Preview:</h4>
            <p class="text-sm text-gray-600 text-left">
              {{ mention.content|truncatewords:20 }}
              {% if mention.content|wordcount > 20 %}...{% endif %}
            </p>
          </div>

          <form method="post" class="space-y-4">
            {% csrf_token %}
            <div class="flex justify-center space-x-3">
              <a href="{% url 'mentions:mention_detail' mention.pk %}" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</a>
              <button type="submit" class="bg-red-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <i class="fa-solid fa-trash mr-2"></i>
                Delete Mention
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
