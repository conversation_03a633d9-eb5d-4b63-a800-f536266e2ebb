{% extends 'base.html' %}

{% block title %}Mention Analytics - RadioMention{% endblock %}

{% block page_title %}Mention Analytics{% endblock %}

{% block header_actions %}
<div class="flex space-x-2">
    <a href="{% url 'mentions:mention_list' %}" class="px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 flex items-center">
        <i class="fa-solid fa-list mr-2"></i>
        View Mentions
    </a>
    <button onclick="exportData()" class="px-4 py-2 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 flex items-center">
        <i class="fa-solid fa-download mr-2"></i>
        Export Data
    </button>
</div>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Date Range Filter -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form method="get" class="flex items-end space-x-4">
            <div>
                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <input type="date" name="start_date" id="start_date" value="{{ start_date|date:'Y-m-d' }}" 
                       class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
            </div>
            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <input type="date" name="end_date" id="end_date" value="{{ end_date|date:'Y-m-d' }}" 
                       class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
            </div>
            <button type="submit" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700">
                Update Report
            </button>
        </form>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-bullhorn text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Mentions</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.total_mentions }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-clock text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Pending</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.pending }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-calendar text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Scheduled</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.scheduled }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-check text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Completed</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.read }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-times text-red-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Cancelled</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.cancelled }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top Clients -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Top Clients</h3>
            </div>
            <div class="p-6">
                {% if client_stats %}
                    <div class="space-y-3">
                        {% for client in client_stats %}
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-900">{{ client.client__name }}</span>
                            <span class="text-sm text-gray-500">{{ client.count }} mentions</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-4">No data available</p>
                {% endif %}
            </div>
        </div>

        <!-- Priority Breakdown -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Priority Breakdown</h3>
            </div>
            <div class="p-6">
                {% if priority_stats %}
                    <div class="space-y-3">
                        {% for priority in priority_stats %}
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-900">
                                {% if priority.priority == 1 %}Low Priority
                                {% elif priority.priority == 2 %}Normal Priority
                                {% elif priority.priority == 3 %}High Priority
                                {% elif priority.priority == 4 %}Urgent Priority
                                {% endif %}
                            </span>
                            <span class="text-sm text-gray-500">{{ priority.count }} mentions</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-4">No data available</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Show Performance -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Show Performance</h3>
        </div>
        <div class="p-6">
            {% if show_stats %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Readings</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Rate</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for show in show_stats %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ show.show__name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ show.total_readings }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ show.completed_readings }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% if show.total_readings > 0 %}
                                        {% widthratio show.completed_readings show.total_readings 100 %}%
                                    {% else %}
                                        0%
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p class="text-gray-500 text-center py-4">No show data available</p>
            {% endif %}
        </div>
    </div>

    <!-- Daily Activity Chart -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Daily Activity</h3>
        </div>
        <div class="p-6">
            <div id="dailyChart" style="height: 300px;">
                <!-- Chart will be rendered here with JavaScript -->
            </div>
        </div>
    </div>
</div>

<script>
function exportData() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    // Create a simple CSV export
    let csvContent = "Date,Total Mentions,Pending,Scheduled,Read,Cancelled\n";
    
    // Add summary row
    csvContent += `${startDate} to ${endDate},{{ stats.total_mentions }},{{ stats.pending }},{{ stats.scheduled }},{{ stats.read }},{{ stats.cancelled }}\n`;
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `mention_analytics_${startDate}_to_${endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Simple chart rendering (you could use Chart.js or similar for better charts)
document.addEventListener('DOMContentLoaded', function() {
    const dailyData = [
        {% for day in daily_stats %}
        { date: '{{ day.day }}', count: {{ day.count }} },
        {% endfor %}
    ];
    
    // Simple text-based chart for now
    const chartDiv = document.getElementById('dailyChart');
    if (dailyData.length > 0) {
        let chartHTML = '<div class="space-y-2">';
        dailyData.forEach(day => {
            const barWidth = Math.max(day.count * 10, 20); // Minimum 20px width
            chartHTML += `
                <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500 w-20">${day.date}</span>
                    <div class="bg-primary-500 h-4 rounded" style="width: ${barWidth}px;"></div>
                    <span class="text-xs text-gray-700">${day.count}</span>
                </div>
            `;
        });
        chartHTML += '</div>';
        chartDiv.innerHTML = chartHTML;
    } else {
        chartDiv.innerHTML = '<p class="text-gray-500 text-center py-8">No daily data available</p>';
    }
});
</script>
{% endblock %}
