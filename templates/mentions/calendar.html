{% extends 'base.html' %}
{% load time_filters %}

{% block title %}
  Calendar Schedule - RadioMention
{% endblock %}

{% block page_title %}
  Calendar Schedule
{% endblock %}

{% block header_actions %}
  <button onclick="openScheduleModal()" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
    <i class="fa-solid fa-plus mr-2"></i>
    Assign to Show
  </button>

{% endblock %}

{% block extra_css %}
  <style>
    .draggable {
      cursor: move;
      user-select: none;
    }
    
    .dragging {
      opacity: 0.5;
      transform: rotate(5deg);
    }
    
    .drop-zone {
      transition: all 0.2s ease;
    }
    
    .drop-zone.drag-over {
      background-color: #f0f9ff;
      border: 2px dashed #0ea5e9;
    }
    
    .time-slot {
      min-height: 60px;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      flex-direction: column;
      padding: 4px;
    }

    .time-slot.has-mentions {
      min-height: auto;
      padding: 8px 4px;
    }

    .mention-stack {
      display: flex;
      flex-direction: column;
      gap: 4px;
      width: 100%;
    }
    
    .mention-item {
      transition: all 0.2s ease;
    }
    
    .mention-item:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
  </style>
{% endblock %}

{% block content %}
  <!-- Campaign Progress Dashboard -->
  {% if active_campaigns %}
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">
      <i class="fa-solid fa-bullhorn text-blue-600 mr-2"></i>
      Active Campaigns
    </h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {% for campaign in active_campaigns %}
      <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <h3 class="font-medium text-gray-900 truncate">{{ campaign.campaign_name }}</h3>
          <span class="text-xs px-2 py-1 rounded-full
            {% if campaign.get_campaign_status == 'complete' %}bg-green-100 text-green-800
            {% elif campaign.get_campaign_status == 'on_track' %}bg-blue-100 text-blue-800
            {% elif campaign.get_campaign_status == 'behind' %}bg-yellow-100 text-yellow-800
            {% else %}bg-red-100 text-red-800{% endif %}">
            {{ campaign.get_campaign_status|title }}
          </span>
        </div>
        <p class="text-sm text-gray-600 mb-3">{{ campaign.client.name }}</p>

        <!-- Progress Bar -->
        <div class="mb-2">
          <div class="flex justify-between text-xs text-gray-600 mb-1">
            <span>{{ campaign.total_aired }}/{{ campaign.total_required }} mentions</span>
            <span>{{ campaign.get_completion_percentage|floatformat:0 }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="h-2 rounded-full
              {% if campaign.get_campaign_status == 'complete' %}bg-green-500
              {% elif campaign.get_campaign_status == 'on_track' %}bg-blue-500
              {% elif campaign.get_campaign_status == 'behind' %}bg-yellow-500
              {% else %}bg-red-500{% endif %}"
              style="width: {{ campaign.get_completion_percentage|floatformat:0 }}%"></div>
          </div>
        </div>

        <div class="flex justify-between text-xs text-gray-500">
          <span>{{ campaign.daily_frequency }}/day target</span>
          <span>{{ campaign.start_date|date:"M j" }} - {{ campaign.end_date|date:"M j" }}</span>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
  {% endif %}

  <div class="flex space-x-6 h-full">
    <!-- Calendar View -->
    <div id="calendar-container" class="flex-1 bg-white rounded-lg shadow-sm border border-gray-200">
      <!-- Calendar Header -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">{{ current_date|format_date_user:user }}</h3>
          <div class="flex items-center space-x-2">
            <a href="?date={{ prev_date|date:'Y-m-d' }}" class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"><i class="fa-solid fa-chevron-left"></i></a>
            <a href="?date={{ today|date:'Y-m-d' }}" class="px-3 py-1 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md">Today</a>
            <a href="?date={{ next_date|date:'Y-m-d' }}" class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"><i class="fa-solid fa-chevron-right"></i></a>
          </div>
        </div>
        <div class="flex space-x-2">
          <button onclick="switchView('day')" type="button"
            class="px-3 py-1 text-sm font-medium {% if view == 'day' %}bg-primary-600 text-white{% else %}text-gray-700 hover:bg-gray-100{% endif %} rounded-md">
            Day
          </button>
          <button onclick="switchView('week')" type="button"
            class="px-3 py-1 text-sm font-medium {% if view == 'week' %}bg-primary-600 text-white{% else %}text-gray-700 hover:bg-gray-100{% endif %} rounded-md">
            Week
          </button>
          <button onclick="switchView('month')" type="button"
            class="px-3 py-1 text-sm font-medium {% if view == 'month' %}bg-primary-600 text-white{% else %}text-gray-700 hover:bg-gray-100{% endif %} rounded-md">
            Month
          </button>
        </div>
      </div>

      <!-- Day View -->
      <div id="day-view"
        class="{% if view == 'day' %}
          
          
          block


        {% else %}
          
          
          hidden


        {% endif %} h-full overflow-y-auto">
        <div class="flex">
          <!-- Time Column -->
          <div class="w-20 border-r border-gray-200">
            <div class="h-12 border-b border-gray-200"></div>
            {% for hour in time_slots %}
              <div class="time-slot flex items-center justify-center text-xs text-gray-500 font-medium">{{ hour }}:00</div>
            {% endfor %}
          </div>

          <!-- Schedule Column -->
          <div class="flex-1">
            <!-- Header with Date -->
            <div class="h-12 border-b border-gray-200 flex items-center justify-center bg-gray-50">
              <span class="text-sm font-medium text-gray-700">{{ current_date|date:'l, F d' }}</span>
            </div>

            <!-- Time Slots -->
            <div class="relative">
              {% for hour in time_slots %}
                <div class="time-slot drop-zone" data-time="{{ hour }}:00" ondrop="drop(event)" ondragover="allowDrop(event)">
                  <div class="mention-stack">
                    {% for reading in schedule %}
                      {% if reading.scheduled_time.hour == hour %}
                        <div class="mention-item draggable bg-blue-100 border border-blue-300 rounded-md p-2 cursor-pointer hover:bg-blue-200 transition-colors" draggable="true" ondragstart="drag(event)" data-mention-id="{{ reading.id }}" onclick="showMentionDetails({{ reading.id }})">
                          <div class="flex items-center justify-between">
                            <div class="flex-1">
                              <div class="flex items-center">
                                <p class="text-xs font-semibold text-blue-800">{{ reading.show.name }}</p>
                                {% if reading.mention.recurring_mention %}
                                  <i class="fa-solid fa-repeat text-blue-600 text-xs ml-1" title="Recurring mention"></i>
                                {% endif %}
                              </div>
                              <p class="text-xs text-blue-600">{{ reading.mention.client.name }}</p>
                              <p class="text-xs text-blue-500">{{ reading.mention.title|truncatechars:30 }}</p>
                            </div>
                            <div class="text-xs text-blue-600">{{ reading.scheduled_time|format_time_user:user }}</div>
                          </div>
                          {% if reading.has_conflict %}
                            <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border border-white"></div>
                          {% endif %}
                        </div>
                      {% endif %}
                    {% endfor %}
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>

      <!-- Week View -->
      <div id="week-view"
        class="{% if view == 'week' %}
          
          
          block


        {% else %}
          
          
          hidden


        {% endif %} h-full overflow-y-auto">
        <div class="flex">
          <!-- Time Column -->
          <div class="w-20 border-r border-gray-200">
            <div class="h-12 border-b border-gray-200"></div>
            {% for hour in time_slots %}
              <div class="time-slot flex items-center justify-center text-xs text-gray-500 font-medium">{{ hour }}:00</div>
            {% endfor %}
          </div>

          <!-- Week Days -->
          {% for day in week_days %}
            <div class="flex-1 border-r border-gray-200 last:border-r-0">
              <!-- Day Header -->
              <div class="h-12 border-b border-gray-200 flex flex-col items-center justify-center bg-gray-50">
                <span class="text-xs font-medium text-gray-600">{{ day.date|date:'D' }}</span>
                <span class="text-sm font-semibold text-gray-800">{{ day.date|date:'j' }}</span>
              </div>

              <!-- Time Slots for this day -->
              <div class="relative">
                {% for hour in time_slots %}
                  <div class="time-slot drop-zone border-b border-gray-100" data-time="{{ hour }}:00" data-date="{{ day.date|date:'Y-m-d' }}" ondrop="drop(event)" ondragover="allowDrop(event)">
                    <div class="mention-stack">
                      {% for reading in day.schedule %}
                        {% if reading.scheduled_time.hour == hour %}
                          <div class="mention-item draggable bg-blue-100 border border-blue-300 rounded-md p-1 text-xs cursor-pointer hover:bg-blue-200 transition-colors" draggable="true" ondragstart="drag(event)" data-mention-id="{{ reading.id }}" onclick="showMentionDetails({{ reading.id }})">
                            <div class="flex items-center">
                              <p class="font-semibold text-blue-800 truncate">{{ reading.show.name }}</p>
                              {% if reading.mention.recurring_mention %}
                                <i class="fa-solid fa-repeat text-blue-600 text-xs ml-1" title="Recurring mention"></i>
                              {% endif %}
                            </div>
                            <p class="text-blue-600 truncate">{{ reading.mention.client.name }}</p>
                            <p class="text-blue-500 text-xs">{{ reading.scheduled_time|time:'g:i A' }}</p>
                          </div>
                        {% endif %}
                      {% endfor %}
                    </div>
                  </div>
                {% endfor %}
              </div>
            </div>
          {% endfor %}
        </div>
      </div>

      <!-- Month View -->
      <div id="month-view"
        class="{% if view == 'month' %}
          
          
          block


        {% else %}
          
          
          hidden


        {% endif %} h-full overflow-y-auto p-4">
        <div class="grid grid-cols-7 gap-1 h-full">
          <!-- Day Headers -->
          {% for day_name in day_names %}
            <div class="text-center text-sm font-medium text-gray-600 p-2 bg-gray-50 border border-gray-200">{{ day_name }}</div>
          {% endfor %}

          <!-- Calendar Days -->
          {% for week in month_weeks %}
            {% for day in week %}
              <div class="border border-gray-200 p-1 min-h-24 {% if day.is_other_month %}
                  
                  
                  bg-gray-50 text-gray-400


                {% elif day.is_today %}
                  
                  
                  bg-blue-50


                {% endif %}">
                <div class="text-sm font-medium mb-1">{{ day.day }}</div>
                <div class="space-y-1">
                  {% for reading in day.schedule %}
                    <div class="text-xs bg-blue-100 text-blue-800 px-1 py-0.5 rounded truncate flex items-center">
                      <span>{{ reading.scheduled_time|time:'g:i' }} {{ reading.mention.client.name }}</span>
                      {% if reading.mention.recurring_mention %}
                        <i class="fa-solid fa-repeat text-blue-600 text-xs ml-1" title="Recurring mention"></i>
                      {% endif %}
                    </div>
                  {% endfor %}
                </div>
              </div>
            {% endfor %}
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- Sidebar Panel -->
    <div id="sidebar-panel" class="w-80 space-y-4">
      <!-- Approved Mentions Ready for Assignment -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
          <i class="fa-solid fa-check-circle mr-2 text-green-500"></i>
          Ready to Assign
        </h4>
        <div class="space-y-2">
          {% for mention in unscheduled_mentions %}
            <div class="mention-item draggable bg-green-50 border border-green-200 rounded-md p-3 cursor-move" draggable="true" ondragstart="drag(event)" data-mention-id="{{ mention.id }}">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <p class="text-sm font-medium text-green-800">{{ mention.title }}</p>
                  <p class="text-xs text-green-600">{{ mention.client.name }}</p>
                  <div class="flex items-center text-xs text-green-500 mt-1">
                    <i class="fa-solid fa-clock mr-1"></i>
                    {{ mention.duration_seconds }} seconds
                  </div>
                  <div class="flex items-center text-xs text-green-500 mt-1">
                    <i class="fa-solid fa-check mr-1"></i>
                    Approved{% if mention.approved_by %} by {{ mention.approved_by.get_full_name|default:mention.approved_by.username }}{% endif %}
                  </div>
                </div>
                <i class="fa-solid fa-grip-dots-vertical text-green-400"></i>
              </div>
            </div>
          {% empty %}
            <div class="text-sm text-gray-500 text-center py-4">
              <i class="fa-solid fa-check-circle text-gray-400 mb-2 text-lg block"></i>
              No approved mentions ready to assign to shows
            </div>
          {% endfor %}
        </div>
      </div>

      <!-- Show Information -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
          <i class="fa-solid fa-broadcast-tower mr-2 text-gray-500"></i>
          Active Shows
        </h4>
        <div class="space-y-2">
          {% for show in active_shows %}
            <div class="flex items-center p-2 bg-blue-50 rounded-md">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <div class="flex-1">
                <p class="text-sm font-medium text-blue-800">{{ show.name }}</p>
                <p class="text-xs text-blue-600">{{ show.start_time|time:'g:i A' }} - {{ show.end_time|time:'g:i A' }}</p>
              </div>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>

  <!-- Schedule Mention Modal -->
  <div id="scheduleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Assign Mention to Show</h3>
          <button onclick="closeScheduleModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600"><i class="fa-solid fa-times"></i></button>
        </div>
        <form id="scheduleForm" class="p-6 space-y-4">
          {% csrf_token %}
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Mention</label>
            <select id="mentionSelect" name="mention_id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
              <option value="">Select a mention...</option>
              {% for mention in unscheduled_mentions %}
                <option value="{{ mention.id }}">{{ mention.title }} - {{ mention.client.name }}</option>
              {% empty %}
                <option value="" disabled>No approved mentions available</option>
              {% endfor %}
            </select>
            {% if not unscheduled_mentions %}
              <p class="text-sm text-gray-500 mt-1">
                <i class="fa-solid fa-info-circle mr-1"></i>
                No approved mentions ready to assign.
                <a href="{% url 'mentions:mention_create' %}" class="text-primary-600 hover:text-primary-700">Create a mention</a>
                or check <a href="{% url 'mentions:approval_workflow' %}" class="text-primary-600 hover:text-primary-700">pending approvals</a>.
              </p>
            {% endif %}
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Show</label>
            <select id="showSelect" name="show_id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
              <option value="">Select a show...</option>
              {% for show in active_shows %}
                <option value="{{ show.id }}">{{ show.name }} ({{ show.start_time|time:'g:i A' }} - {{ show.end_time|time:'g:i A' }})</option>
              {% empty %}
                <option value="" disabled>No active shows available</option>
              {% endfor %}
            </select>
            {% if not active_shows %}
              <p class="text-sm text-gray-500 mt-1">
                <i class="fa-solid fa-info-circle mr-1"></i>
                No active shows available.
                <a href="{% url 'shows:show_create' %}" class="text-primary-600 hover:text-primary-700">Create a show</a> first.
              </p>
            {% endif %}
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Presenter</label>
            <select id="presenterSelect" name="presenter_id" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
              <option value="">Select a presenter...</option>
              {% for presenter in presenters %}
                <option value="{{ presenter.id }}">{{ presenter.name }}</option>
              {% empty %}
                <option value="" disabled>No active presenters available</option>
              {% endfor %}
            </select>
            {% if not presenters %}
              <p class="text-sm text-gray-500 mt-1">
                <i class="fa-solid fa-info-circle mr-1"></i>
                No active presenters available.
                <a href="{% url 'core:presenter_list' %}" class="text-primary-600 hover:text-primary-700">View presenters</a> or assign users the "presenter" role.
              </p>
            {% endif %}
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
              <input type="date" id="scheduleDate" name="date" value="{{ current_date|date:'Y-m-d' }}" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Time</label>
              <input type="time" id="scheduleTime" name="time" class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500" />
            </div>
          </div>
          <div class="flex justify-end space-x-3 pt-4">
            <button type="button" onclick="closeScheduleModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">Cancel</button>
            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md">Assign to Show</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Mention Details Modal -->
  <div id="mentionDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Mention Details</h3>
          <button onclick="closeMentionDetailsModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600"><i class="fa-solid fa-times"></i></button>
        </div>
        <div id="mentionDetailsContent" class="p-6">
          <!-- Content will be loaded dynamically -->
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>


    // Drag and Drop functionality
    function allowDrop(ev) {
      ev.preventDefault()
      ev.currentTarget.classList.add('drag-over')
    }
    
    function drag(ev) {
      ev.dataTransfer.setData('text', ev.target.dataset.mentionId)
      ev.target.classList.add('dragging')
    }
    
    function drop(ev) {
      ev.preventDefault()
      ev.currentTarget.classList.remove('drag-over')
    
      const mentionId = ev.dataTransfer.getData('text')
      const timeSlot = ev.currentTarget.dataset.time
      const date = '{{ current_date|date:"Y-m-d" }}'
    
      // Schedule the mention
      scheduleMention(mentionId, date, timeSlot)
    }
    
    function scheduleMention(mentionId, date, time, showId = null, presenterId = null) {
      // Show loading state
      const loadingDiv = document.createElement('div')
      loadingDiv.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50'
      loadingDiv.innerHTML = '<div class="bg-white rounded-lg p-4"><i class="fa-solid fa-spinner fa-spin mr-2"></i>Scheduling...</div>'
      document.body.appendChild(loadingDiv)
    
      fetch('/mentions/schedule/', {
        method: 'POST',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          mention_id: mentionId,
          date: date,
          time: time,
          show_id: showId,
          presenter_id: presenterId
        })
      })
        .then((response) => response.json())
        .then((data) => {
          document.body.removeChild(loadingDiv)
          if (data.success) {
            location.reload()
          } else {
            alert('Error scheduling mention: ' + data.error)
          }
        })
        .catch((error) => {
          document.body.removeChild(loadingDiv)
          console.error('Error:', error)
          alert('Error scheduling mention')
        })
    }
    
    function switchView(view) {
      const currentDate = '{{ current_date|date:"Y-m-d" }}';
      window.location.href = `?view=${view}&date=${currentDate}`;
    }
    
    function openScheduleModal() {
      document.getElementById('scheduleModal').classList.remove('hidden')
    }

    function closeScheduleModal() {
      document.getElementById('scheduleModal').classList.add('hidden')
      document.getElementById('scheduleForm').reset()
    }

    function showMentionDetails(readingId) {
      fetch(`/mentions/readings/${readingId}/?format=json`)
        .then((response) => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          return response.json()
        })
        .then((data) => {
          if (data.success) {
            document.getElementById('mentionDetailsContent').innerHTML = `
                <div class="space-y-4">
                    <div>
                        <h4 class="font-medium text-gray-900">${data.reading.mention.title}</h4>
                        <p class="text-sm text-gray-600">${data.reading.mention.client.name}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-700">${data.reading.mention.content}</p>
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium">Show:</span> ${data.reading.show.name}
                        </div>
                        <div>
                            <span class="font-medium">Presenter:</span> ${data.reading.presenter.name}
                        </div>
                        <div>
                            <span class="font-medium">Duration:</span> ${data.reading.mention.duration_seconds}s
                        </div>
                        <div>
                            <span class="font-medium">Priority:</span> ${data.reading.mention.priority_display}
                        </div>
                        <div>
                            <span class="font-medium">Scheduled:</span> ${data.reading.scheduled_date} at ${data.reading.scheduled_time}
                        </div>
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button onclick="editReading(${readingId})" class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded">Edit</button>
                        <button onclick="deleteReading(${readingId})" class="px-3 py-1 text-sm bg-red-100 text-red-700 rounded">Delete</button>
                    </div>
                </div>
            `
            document.getElementById('mentionDetailsModal').classList.remove('hidden')
          } else {
            alert('Error loading mention details: ' + (data.error || 'Unknown error'))
          }
        })
        .catch((error) => {
          console.error('Error:', error)
          alert('Error loading mention details: ' + error.message)
        })
    }

    function closeMentionDetailsModal() {
      document.getElementById('mentionDetailsModal').classList.add('hidden')
    }

    function editReading(readingId) {
      // Close the details modal
      closeMentionDetailsModal()

      // For now, redirect to the reading detail page for editing
      // In the future, this could open an edit modal
      window.location.href = `/mentions/readings/${readingId}/`
    }

    function deleteReading(readingId) {
      if (confirm('Are you sure you want to delete this scheduled reading?')) {
        fetch(`/mentions/readings/${readingId}/delete/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json'
          }
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              closeMentionDetailsModal()
              location.reload()
            } else {
              alert('Error deleting reading: ' + (data.error || 'Unknown error'))
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('Error deleting reading: ' + error.message)
          })
      }
    }

    // Helper function to get CSRF token from cookies
    function getCookie(name) {
      let cookieValue = null
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';')
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim()
          if (cookie.substring(0, name.length + 1) === name + '=') {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1))
            break
          }
        }
      }
      return cookieValue
    }

    // Handle schedule form submission
    document.getElementById('scheduleForm').addEventListener('submit', function (e) {
      e.preventDefault()
      const formData = new FormData(this)
      const mentionId = formData.get('mention_id')
      const showId = formData.get('show_id')
      const presenterId = formData.get('presenter_id')
      const date = formData.get('date')
      const time = formData.get('time')

      if (!mentionId || !showId || !presenterId || !date || !time) {
        alert('Please fill in all required fields')
        return
      }

      scheduleMention(mentionId, date, time, showId, presenterId)
      closeScheduleModal()
    })

    // Remove dragging class when drag ends
    document.addEventListener('dragend', function (e) {
      e.target.classList.remove('dragging')
    })

    // Remove drag-over class when leaving drop zone
    document.addEventListener('dragleave', function (e) {
      if (e.target.classList.contains('drop-zone')) {
        e.target.classList.remove('drag-over')
      }
    })
  </script>
{% endblock %}
