{% extends 'base.html' %}

{% block title %}Bulk Schedule - RadioMention{% endblock %}

{% block page_title %}Bulk Schedule Mentions{% endblock %}

{% block header_actions %}
<a href="{% url 'mentions:mention_list' %}" class="px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 flex items-center">
    <i class="fa-solid fa-arrow-left mr-2"></i>
    Back to Mentions
</a>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Instructions -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fa-solid fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Bulk Scheduling</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p>Select multiple mentions and schedule them automatically with specified intervals. The system will check for conflicts and skip any mentions that would cause scheduling issues.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scheduling Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Schedule Settings</h3>
        </div>
        <div class="p-6">
            <form method="post" id="bulkScheduleForm">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Show Selection -->
                    <div>
                        <label for="show" class="block text-sm font-medium text-gray-700 mb-1">
                            Show <span class="text-red-500">*</span>
                        </label>
                        <select name="show" id="show" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select a show</option>
                            {% for show in shows %}
                            <option value="{{ show.id }}">{{ show.name }} ({{ show.start_time }} - {{ show.end_time }})</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Presenter Selection -->
                    <div>
                        <label for="presenter" class="block text-sm font-medium text-gray-700 mb-1">
                            Presenter <span class="text-red-500">*</span>
                        </label>
                        <select name="presenter" id="presenter" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select a presenter</option>
                            {% for presenter in presenters %}
                            <option value="{{ presenter.id }}">{{ presenter.display_name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Start Date -->
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">
                            Start Date <span class="text-red-500">*</span>
                        </label>
                        <input type="date" name="start_date" id="start_date" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <!-- Start Time -->
                    <div>
                        <label for="start_time" class="block text-sm font-medium text-gray-700 mb-1">
                            Start Time <span class="text-red-500">*</span>
                        </label>
                        <input type="time" name="start_time" id="start_time" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    </div>

                    <!-- Interval -->
                    <div class="md:col-span-2">
                        <label for="interval_minutes" class="block text-sm font-medium text-gray-700 mb-1">
                            Interval Between Mentions (minutes)
                        </label>
                        <input type="number" name="interval_minutes" id="interval_minutes" value="30" min="5" max="120" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <p class="mt-1 text-sm text-gray-500">Time gap between each mention (5-120 minutes)</p>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="mt-6 flex justify-end">
                    <button type="submit" id="scheduleButton" disabled 
                            class="px-6 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed">
                        <i class="fa-solid fa-calendar-plus mr-2"></i>
                        Schedule Selected Mentions
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Mention Selection -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Select Mentions to Schedule</h3>
                <div class="flex space-x-2">
                    <button onclick="selectAll()" class="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700">
                        Select All
                    </button>
                    <button onclick="selectNone()" class="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700">
                        Select None
                    </button>
                </div>
            </div>
        </div>
        <div class="p-6">
            {% if unscheduled_mentions %}
                <div class="space-y-3">
                    {% for mention in unscheduled_mentions %}
                    <div class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <input type="checkbox" name="mentions" value="{{ mention.id }}" 
                               class="mention-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                        <div class="ml-3 flex-1">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">{{ mention.title }}</h4>
                                    <p class="text-sm text-gray-600">{{ mention.client.name }}</p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                {% if mention.priority == 4 %}bg-red-100 text-red-800
                                                {% elif mention.priority == 3 %}bg-orange-100 text-orange-800
                                                {% elif mention.priority == 2 %}bg-yellow-100 text-yellow-800
                                                {% else %}bg-green-100 text-green-800{% endif %}">
                                        {{ mention.get_priority_display }}
                                    </span>
                                    <p class="text-xs text-gray-500 mt-1">{{ mention.duration_seconds }}s</p>
                                </div>
                            </div>
                            <p class="text-sm text-gray-500 mt-1 line-clamp-2">{{ mention.content|truncatewords:20 }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-calendar-check text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Unscheduled Mentions</h3>
                    <p class="text-gray-600">All mentions have been scheduled or there are no pending mentions.</p>
                    <a href="{% url 'mentions:mention_create' %}" 
                       class="mt-4 inline-flex items-center px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700">
                        <i class="fa-solid fa-plus mr-2"></i>
                        Create New Mention
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function selectAll() {
    document.querySelectorAll('.mention-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateScheduleButton();
}

function selectNone() {
    document.querySelectorAll('.mention-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateScheduleButton();
}

function updateScheduleButton() {
    const checkedBoxes = document.querySelectorAll('.mention-checkbox:checked');
    const scheduleButton = document.getElementById('scheduleButton');
    
    if (checkedBoxes.length > 0) {
        scheduleButton.disabled = false;
        scheduleButton.textContent = `Schedule ${checkedBoxes.length} Mention${checkedBoxes.length > 1 ? 's' : ''}`;
    } else {
        scheduleButton.disabled = true;
        scheduleButton.innerHTML = '<i class="fa-solid fa-calendar-plus mr-2"></i>Schedule Selected Mentions';
    }
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.mention-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateScheduleButton);
    });
    
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').min = today;
    document.getElementById('start_date').value = today;
});

// Form submission
document.getElementById('bulkScheduleForm').addEventListener('submit', function(e) {
    const checkedBoxes = document.querySelectorAll('.mention-checkbox:checked');
    if (checkedBoxes.length === 0) {
        e.preventDefault();
        alert('Please select at least one mention to schedule.');
        return;
    }
    
    if (!confirm(`Schedule ${checkedBoxes.length} mention${checkedBoxes.length > 1 ? 's' : ''}? This action cannot be undone.`)) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
