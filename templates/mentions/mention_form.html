{% extends 'base.html' %} {% load static %} {% block title %}
  {% if object %}
    Edit Mention
  {% else %}
    Create New Mention
  {% endif %}
{% endblock %} {% block content %}
  <div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center">
          <a href="{% url 'mentions:mention_list' %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
          <h1 class="text-xl font-semibold text-gray-900">
            {% if object %}
               Edit Mention
            {% else %}
              Create New Mention
            {% endif %}
          </h1>
        </div>
      </div>

      <!-- Form -->
      <div class="p-6">
        <form method="post" class="space-y-6">
          {% csrf_token %}

          <!-- Basic Information -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>

            <div class="space-y-4">
              <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Title <span class="text-red-500">*</span></label>
                {{ form.title }} {% if form.title.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.title.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.client.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Client <span class="text-red-500">*</span></label>
                {{ form.client }} {% if form.client.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.client.errors.0 }}</p>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- Content -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Content</h3>

            <div>
              <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Mention Content <span class="text-red-500">*</span></label>
              {{ form.content }} {% if form.content.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.content.errors.0 }}</p>
              {% endif %}
              <p class="mt-1 text-sm text-gray-500">This is the exact text that will be read on air</p>
            </div>
          </div>

          <!-- Settings -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Settings</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                {{ form.priority }} {% if form.priority.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.priority.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.duration_seconds.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Duration (seconds)</label>
                {{ form.duration_seconds }} {% if form.duration_seconds.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.duration_seconds.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">Estimated reading time</p>
              </div>
            </div>
          </div>

          <!-- Notes -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>

            <div>
              <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
              {{ form.notes }} {% if form.notes.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
              {% endif %}
              <p class="mt-1 text-sm text-gray-500">Internal notes (not read on air)</p>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <a href="{% url 'mentions:mention_list' %}" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</a>
            <button type="submit" class="bg-primary-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-primary-700">
              {% if object %}
                 Update Mention
              {% else %}
                Create Mention
              {% endif %}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Character Counter -->
  <div class="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3" id="characterCounter" style="display: none">
    <div class="text-sm">
      <span class="text-gray-600">Characters:</span>
      <span id="charCount" class="font-medium">0</span>
      <span class="text-gray-400">/ Est. reading time:</span>
      <span id="readingTime" class="font-medium text-primary-600">0s</span>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const contentTextarea = document.getElementById('id_content')
      const durationInput = document.getElementById('id_duration_seconds')
      const charCounter = document.getElementById('characterCounter')
      const charCount = document.getElementById('charCount')
      const readingTime = document.getElementById('readingTime')
    
      function updateCounter() {
        const text = contentTextarea.value
        const chars = text.length
        const words = text
          .trim()
          .split(/\s+/)
          .filter((word) => word.length > 0).length
        const estimatedSeconds = Math.ceil(words / 2.5) // Average reading speed
    
        charCount.textContent = chars
        readingTime.textContent = estimatedSeconds + 's'
    
        // Auto-update duration if it's still default or empty
        if (!durationInput.value || durationInput.value == '30') {
          durationInput.value = Math.max(estimatedSeconds, 5)
        }
    
        // Show/hide counter
        if (chars > 0) {
          charCounter.style.display = 'block'
        } else {
          charCounter.style.display = 'none'
        }
      }
    
      contentTextarea.addEventListener('input', updateCounter)
      updateCounter() // Initial call
    })
  </script>
{% endblock %}
