{% extends 'base.html' %}
{% load static %}

{% block title %}
  Calendar Schedule
{% endblock %}

{% block extra_css %}
  <style>
    .mention-card {
      transition: all 0.2s ease-in-out;
    }
    .mention-card:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    /* Enhanced month view styles */
    .calendar-day {
      transition: all 0.2s ease;
    }
    .calendar-day:hover {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }



    /* Tooltip positioning */
    .group:hover .group-hover\:block {
      display: block !important;
    }

    /* Animation for mention cards */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .mention-card-animated {
      animation: fadeIn 0.3s ease-out;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    {% csrf_token %}
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">
              <i class="fa-solid fa-calendar-days text-primary-600 mr-2"></i>
              Calendar Schedule
            </h1>
            <p class="text-gray-600">Manage and schedule mentions</p>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <!-- View Toggle -->
          <div class="flex bg-gray-100 rounded-lg p-1">
            <button onclick="switchView('day')" 
               class="px-3 py-1 text-sm font-medium rounded-md {% if view == 'day' %}bg-white text-gray-900 shadow-sm{% else %}text-gray-600 hover:text-gray-900{% endif %}">
              Day
            </button>
            <button onclick="switchView('week')" 
               class="px-3 py-1 text-sm font-medium rounded-md {% if view == 'week' %}bg-white text-gray-900 shadow-sm{% else %}text-gray-600 hover:text-gray-900{% endif %}">
              Week
            </button>
            <button onclick="switchView('month')" 
               class="px-3 py-1 text-sm font-medium rounded-md {% if view == 'month' %}bg-white text-gray-900 shadow-sm{% else %}text-gray-600 hover:text-gray-900{% endif %}">
              Month
            </button>
          </div>
          <!-- Quick create button -->
          <button onclick="openQuickCreateModal()" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors">
            <i class="fa-solid fa-plus mr-1"></i>
            Quick Add
          </button>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-4">
        <button onclick="previousPeriod()" class="p-2 text-gray-400 hover:text-gray-600">
          <i class="fa-solid fa-chevron-left"></i>
        </button>
        <h2 class="text-xl font-semibold text-gray-900">
          <span id="calendar-title">
            {% if view == 'day' %}
              {{ current_date|date:'l, F j, Y' }}
            {% elif view == 'week' %}
              Week of {{ current_date|date:'F j, Y' }}
            {% else %}
              {{ current_date|date:'F Y' }}
            {% endif %}
          </span>
        </h2>
        <button onclick="nextPeriod()" class="p-2 text-gray-400 hover:text-gray-600">
          <i class="fa-solid fa-chevron-right"></i>
        </button>
      </div>
      <div class="flex items-center space-x-3">
        <button onclick="goToToday()" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
          Today
        </button>
        <!-- Auto-refresh toggle -->
        <div class="flex items-center">
          <input type="checkbox" id="auto-refresh" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
          <label for="auto-refresh" class="ml-2 text-sm text-gray-600">Auto-refresh</label>
        </div>
      </div>
    </div>

    <!-- Filters Bar -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <!-- Search -->
            <div class="relative">
              <input type="text" id="search-input" placeholder="Search mentions, clients, shows..." class="pl-10 pr-4 py-2 w-80 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
              <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>

            <!-- Status Filter -->
            <div class="relative">
              <select id="status-filter" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Status</option>
                <option value="completed">Completed</option>
                <option value="scheduled">Scheduled</option>
                <option value="pending">Pending</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>

            <!-- Client Filter -->
            <div class="relative">
              <select id="client-filter" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Clients</option>
                {% for client in organization.client_set.all %}
                  <option value="{{ client.id }}">{{ client.name }}</option>
                {% endfor %}
              </select>
            </div>

            <!-- Show Filter -->
            <div class="relative">
              <select id="show-filter" class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Shows</option>
                {% for show in active_shows %}
                  <option value="{{ show.id }}">{{ show.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <!-- Status Legend -->
          <div class="flex items-center space-x-4 text-sm">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-100 border border-green-300 rounded mr-2"></div>
              <span class="text-gray-600">Completed</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-100 border border-blue-300 rounded mr-2"></div>
              <span class="text-gray-600">Approved</span>
            </div>
            <div class="flex items-center">
              <div class="w-3 h-3 bg-yellow-100 border border-yellow-300 rounded mr-2"></div>
              <span class="text-gray-600">Pending</span>
            </div>
            <div class="flex items-center">
              <i class="fa-solid fa-repeat text-blue-600 text-sm mr-2"></i>
              <span class="text-gray-600">Recurring</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Calendar Content -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      {% if view == 'day' %}
        <!-- Day View -->
        <div class="p-6">
          <div class="grid grid-cols-1 gap-4">
            <!-- Time slots -->
            {% for hour in time_slots %}
              <div class="flex border-b border-gray-100 pb-4">
                <div class="w-20 text-sm text-gray-500 font-medium">
                  {{ hour }}:00
                </div>
                <div class="flex-1">
                  {% for reading in scheduled_readings %}
                    {% if reading.scheduled_time.hour == hour %}
                      <div class="rounded-lg p-3 mb-2 cursor-pointer transition-colors
                                  {% if reading.actual_read_time %}
                                    bg-green-100 border border-green-200 hover:bg-green-200
                                  {% elif reading.mention.status == 'approved' %}
                                    bg-blue-100 border border-blue-200 hover:bg-blue-200
                                  {% else %}
                                    bg-yellow-100 border border-yellow-200 hover:bg-yellow-200
                                  {% endif %}"
                           onclick="showMentionDetails({{ reading.id }})">
                        <div class="flex items-center justify-between">
                          <div>
                            <h4 class="text-sm font-medium
                                       {% if reading.actual_read_time %}
                                         text-green-900
                                       {% elif reading.mention.status == 'approved' %}
                                         text-blue-900
                                       {% else %}
                                         text-yellow-900
                                       {% endif %}">{{ reading.mention.title }}</h4>
                            <p class="text-xs
                                      {% if reading.actual_read_time %}
                                        text-green-700
                                      {% elif reading.mention.status == 'approved' %}
                                        text-blue-700
                                      {% else %}
                                        text-yellow-700
                                      {% endif %}">{{ reading.mention.client.name }} • {{ reading.show.name }}</p>
                            <p class="text-xs
                                      {% if reading.actual_read_time %}
                                        text-green-600
                                      {% elif reading.mention.status == 'approved' %}
                                        text-blue-600
                                      {% else %}
                                        text-yellow-600
                                      {% endif %}">{{ reading.scheduled_time|time:'g:i A' }} • {{ reading.mention.duration_seconds }}s</p>
                            {% if reading.mention.recurring_mention %}
                              <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-200 text-blue-800 mt-1">
                                <i class="fa-solid fa-repeat mr-1"></i>
                                Recurring
                              </span>
                            {% endif %}
                          </div>
                          <div class="flex items-center space-x-2">
                            {% if reading.actual_read_time %}
                              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-200 text-green-800">
                                <i class="fa-solid fa-check mr-1"></i>
                                Completed
                              </span>
                            {% elif reading.mention.status == 'approved' %}
                              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-200 text-blue-800">
                                <i class="fa-solid fa-calendar-check mr-1"></i>
                                Approved
                              </span>
                            {% else %}
                              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-200 text-yellow-800">
                                <i class="fa-solid fa-clock mr-1"></i>
                                Pending
                              </span>
                            {% endif %}
                            <!-- Quick actions -->
                            <div class="flex space-x-1">
                              <button onclick="editMention({{ reading.id }})" class="p-1 text-gray-400 hover:text-blue-600" title="Edit">
                                <i class="fa-solid fa-edit text-xs"></i>
                              </button>
                              <button onclick="deleteMention({{ reading.id }})" class="p-1 text-gray-400 hover:text-red-600" title="Delete">
                                <i class="fa-solid fa-trash text-xs"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    {% endif %}
                  {% endfor %}
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      {% elif view == 'week' %}
        <!-- Week View -->
        <div class="overflow-x-auto">
          <div class="min-w-full">
            <!-- Week header -->
            <div class="grid grid-cols-8 border-b border-gray-200">
              <div class="p-4 text-sm font-medium text-gray-500">Time</div>
              {% for day in week_days %}
                <div class="p-4 text-center border-l border-gray-200">
                  <div class="text-sm font-medium text-gray-900">{{ day.date|date:'D' }}</div>
                  <div class="text-lg font-semibold {% if day.is_today %}text-blue-600{% else %}text-gray-900{% endif %}">
                    {{ day.date|date:'j' }}
                  </div>
                </div>
              {% endfor %}
            </div>

            <!-- Week content -->
            {% for hour in time_slots %}
              <div class="grid grid-cols-8 border-b border-gray-100">
                <div class="p-2 text-sm text-gray-500 border-r border-gray-200">
                  {{ hour }}:00
                </div>
                {% for day in week_days %}
                  <div class="p-2 border-l border-gray-200 min-h-16 flex flex-col gap-1">
                    {% for reading in day.schedule %}
                      {% if reading.scheduled_time.hour == hour %}
                        <div class="rounded p-1 text-xs cursor-pointer transition-colors
                                    {% if reading.actual_read_time %}
                                      bg-green-100 border border-green-300 hover:bg-green-200
                                    {% elif reading.mention.status == 'approved' %}
                                      bg-blue-100 border border-blue-300 hover:bg-blue-200
                                    {% else %}
                                      bg-yellow-100 border border-yellow-300 hover:bg-yellow-200
                                    {% endif %}"
                             onclick="showMentionDetails({{ reading.id }})"
                             title="{{ reading.mention.title }} - {{ reading.mention.client.name }} at {{ reading.scheduled_time|time:'g:i A' }}">

                          <div class="font-semibold truncate
                                      {% if reading.actual_read_time %}
                                        text-green-800
                                      {% elif reading.mention.status == 'approved' %}
                                        text-blue-800
                                      {% else %}
                                        text-yellow-800
                                      {% endif %}">{{ reading.mention.title|truncatechars:20 }}</div>

                          <div class="truncate
                                      {% if reading.actual_read_time %}
                                        text-green-700
                                      {% elif reading.mention.status == 'approved' %}
                                        text-blue-700
                                      {% else %}
                                        text-yellow-700
                                      {% endif %}">{{ reading.show.name }}</div>

                          <div class="flex items-center justify-between">
                            <span class="{% if reading.actual_read_time %}
                                           text-green-600
                                         {% elif reading.mention.status == 'approved' %}
                                           text-blue-600
                                         {% else %}
                                           text-yellow-600
                                         {% endif %}">{{ reading.scheduled_time|time:'g:i' }}</span>

                            <div class="flex items-center space-x-1">
                              {% if reading.actual_read_time %}
                                <i class="fa-solid fa-check text-green-600 text-xs"></i>
                              {% elif reading.mention.recurring_mention %}
                                <i class="fa-solid fa-repeat text-blue-600 text-xs"></i>
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      {% endif %}
                    {% endfor %}
                  </div>
                {% endfor %}
              </div>
            {% endfor %}
          </div>
        </div>
      {% else %}
        <!-- Month View -->
        <div class="p-6">
          <!-- Month header -->
          <div class="grid grid-cols-7 gap-px mb-4">
            {% for day_name in day_names %}
              <div class="p-2 text-center text-sm font-medium text-gray-500">{{ day_name }}</div>
            {% endfor %}
          </div>

          <!-- Month calendar -->
          <div class="grid grid-cols-7 gap-px bg-gray-200 rounded-lg overflow-hidden">
            <!-- Calendar days -->
            {% for week in month_weeks %}
              {% for day in week %}
                <div class="calendar-day bg-white p-2 relative {% if day.is_other_month %}bg-gray-50 text-gray-400{% elif day.is_today %}bg-blue-50 ring-2 ring-blue-200{% endif %} hover:bg-gray-50 transition-colors">
                  <!-- Day number -->
                  <div class="flex items-center justify-between mb-2">
                    <div class="text-sm font-semibold {% if day.is_today %}text-blue-600{% elif day.is_other_month %}text-gray-400{% else %}text-gray-900{% endif %}">
                      {{ day.day }}
                    </div>
                    {% if day.schedule|length > 0 %}
                      <div class="text-xs bg-gray-200 text-gray-600 px-2 py-0.5 rounded-full font-medium">
                        {{ day.schedule|length }}
                      </div>
                    {% endif %}
                  </div>

                  <!-- Mentions for this day -->
                  <div class="space-y-1">
                    {% for reading in day.schedule %}
                      <div class="group relative">
                        <div class="text-xs px-2 py-1 rounded cursor-pointer transition-all duration-200
                                    {% if reading.actual_read_time %}
                                      bg-green-100 text-green-800 border border-green-200 hover:bg-green-200
                                    {% elif reading.mention.status == 'approved' %}
                                      bg-blue-100 text-blue-800 border border-blue-200 hover:bg-blue-200
                                    {% else %}
                                      bg-yellow-100 text-yellow-800 border border-yellow-200 hover:bg-yellow-200
                                    {% endif %}"
                             onclick="showMentionDetails({{ reading.id }})"
                             title="{{ reading.mention.title }} - {{ reading.mention.client.name }} at {{ reading.scheduled_time|time:'g:i A' }}">

                          <!-- Time and client -->
                          <div class="flex items-center justify-between">
                            <span class="font-medium">{{ reading.scheduled_time|time:'g:i' }}</span>
                            {% if reading.actual_read_time %}
                              <i class="fa-solid fa-check text-green-600 text-xs"></i>
                            {% elif reading.mention.recurring_mention %}
                              <i class="fa-solid fa-repeat text-blue-600 text-xs"></i>
                            {% endif %}
                          </div>

                          <!-- Client name -->
                          <div class="truncate font-medium">
                            {{ reading.mention.client.name|truncatechars:12 }}
                          </div>

                          <!-- Show name -->
                          {% if reading.show %}
                            <div class="truncate text-xs opacity-75">
                              {{ reading.show.name|truncatechars:15 }}
                            </div>
                          {% endif %}
                        </div>

                        <!-- Hover tooltip -->
                        <div class="absolute bottom-full left-0 mb-2 hidden group-hover:block z-10 bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap shadow-lg">
                          {{ reading.mention.title|truncatechars:30 }}
                          <div class="text-xs opacity-75">{{ reading.mention.client.name }} • {{ reading.scheduled_time|time:'g:i A' }}</div>
                        </div>
                      </div>
                    {% endfor %}
                  </div>
                </div>
              {% endfor %}
            {% endfor %}
          </div>
        </div>
      {% endif %}
    </div>

    <!-- Enhanced Legend -->
    <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <h4 class="text-sm font-semibold text-gray-700 mb-3">Legend</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div class="flex items-center">
          <div class="w-4 h-4 bg-green-100 border border-green-300 rounded mr-2"></div>
          <span class="text-gray-600">Completed</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-100 border border-blue-300 rounded mr-2"></div>
          <span class="text-gray-600">Approved</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-yellow-100 border border-yellow-300 rounded mr-2"></div>
          <span class="text-gray-600">Pending</span>
        </div>
        <div class="flex items-center">
          <i class="fa-solid fa-repeat text-blue-600 text-sm mr-2"></i>
          <span class="text-gray-600">Recurring</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced JavaScript -->
  <script>
    let currentDate = '{{ current_date|date:"Y-m-d" }}'
    let currentView = '{{ view }}'
    let autoRefreshInterval = null

    // View switching functions
    function switchView(view) {
      window.location.href = `?view=${view}&date=${currentDate}`
    }

    function previousPeriod() {
      const date = new Date(currentDate)
      if (currentView === 'day') {
        date.setDate(date.getDate() - 1)
      } else if (currentView === 'week') {
        date.setDate(date.getDate() - 7)
      } else if (currentView === 'month') {
        date.setMonth(date.getMonth() - 1)
      }
      const newDate = date.toISOString().split('T')[0]
      window.location.href = `?view=${currentView}&date=${newDate}`
    }

    function nextPeriod() {
      const date = new Date(currentDate)
      if (currentView === 'day') {
        date.setDate(date.getDate() + 1)
      } else if (currentView === 'week') {
        date.setDate(date.getDate() + 7)
      } else if (currentView === 'month') {
        date.setMonth(date.getMonth() + 1)
      }
      const newDate = date.toISOString().split('T')[0]
      window.location.href = `?view=${currentView}&date=${newDate}`
    }

    function goToToday() {
      const today = new Date().toISOString().split('T')[0]
      window.location.href = `?view=${currentView}&date=${today}`
    }

    // Auto-refresh functionality
    function toggleAutoRefresh() {
      const checkbox = document.getElementById('auto-refresh')
      if (checkbox.checked) {
        // Show notification that auto-refresh is enabled
        if (window.RadioMention && window.RadioMention.showNotification) {
          window.RadioMention.showNotification('Auto-refresh enabled (30 seconds)', 'info');
        }
        autoRefreshInterval = setInterval(() => {
          // Only refresh if user is still on the page and no modals are open
          if (document.visibilityState === 'visible' && !document.querySelector('.modal:not(.hidden)')) {
            location.reload()
          }
        }, 30000) // Refresh every 30 seconds
      } else {
        if (autoRefreshInterval) {
          clearInterval(autoRefreshInterval)
          autoRefreshInterval = null
        }
        // Show notification that auto-refresh is disabled
        if (window.RadioMention && window.RadioMention.showNotification) {
          window.RadioMention.showNotification('Auto-refresh disabled', 'info');
        }
      }
    }

    // Mention details modal
    function showMentionDetails(readingId) {
      const modalHTML = `
        <div id="mention-detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-900">Mention Details</h3>
              <button onclick="closeMentionModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fa-solid fa-times text-xl"></i>
              </button>
            </div>
            <div class="mention-detail-content">
              <iframe src="/mentions/reading/${readingId}/" class="w-full h-96 border-0"></iframe>
            </div>
            <div class="flex justify-end mt-4">
              <button onclick="closeMentionModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Close</button>
            </div>
          </div>
        </div>
      `
      document.body.insertAdjacentHTML('beforeend', modalHTML)
    }

    function closeMentionModal() {
      const modal = document.getElementById('mention-detail-modal')
      if (modal) {
        modal.remove()
      }
    }

    // Show day details function
    function showDayDetails(date) {
      window.location.href = `?view=day&date=${date}`
    }

    // Quick create modal
    function openQuickCreateModal() {
      const modalHTML = `
        <div id="quick-create-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Create Mention</h3>
              <form id="quick-create-form">
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                  <input type="text" name="title" required class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">Client</label>
                  <select name="client_id" required class="w-full border border-gray-300 rounded-md px-3 py-2">
                    <option value="">Select Client</option>
                    {% for client in organization.client_set.all %}
                      <option value="{{ client.id }}">{{ client.name }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                  <textarea name="content" required rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                  <button type="button" onclick="closeQuickCreateModal()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">Cancel</button>
                  <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700">Create</button>
                </div>
              </form>
            </div>
          </div>
        </div>
      `
      document.body.insertAdjacentHTML('beforeend', modalHTML)

      // Handle form submission
      document.getElementById('quick-create-form').addEventListener('submit', function(e) {
        e.preventDefault()
        const formData = new FormData(this)

        fetch('/mentions/create/', {
          method: 'POST',
          headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
          },
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            closeQuickCreateModal()
            location.reload()
          } else {
            alert('Error creating mention: ' + data.error)
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert('Error creating mention')
        })
      })
    }

    function closeQuickCreateModal() {
      const modal = document.getElementById('quick-create-modal')
      if (modal) {
        modal.remove()
      }
    }

    // Edit and delete functions
    function editMention(readingId) {
      window.location.href = `/mentions/reading/${readingId}/edit/`
    }

    function deleteMention(readingId) {
      if (confirm('Are you sure you want to delete this mention?')) {
        fetch(`/mentions/reading/${readingId}/delete/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
          }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            location.reload()
          } else {
            alert('Error deleting mention: ' + data.error)
          }
        })
        .catch(error => {
          console.error('Error:', error)
          alert('Error deleting mention')
        })
      }
    }

    // Search and filter functionality
    function performSearch() {
      const searchTerm = document.getElementById('search-input').value.toLowerCase()
      const statusFilter = document.getElementById('status-filter').value
      const clientFilter = document.getElementById('client-filter').value
      const showFilter = document.getElementById('show-filter').value

      // Build URL with filters
      const params = new URLSearchParams(window.location.search)
      params.set('view', currentView)
      params.set('date', currentDate)

      if (searchTerm) params.set('search', searchTerm)
      else params.delete('search')

      if (statusFilter) params.set('status', statusFilter)
      else params.delete('status')

      if (clientFilter) params.set('client', clientFilter)
      else params.delete('client')

      if (showFilter) params.set('show', showFilter)
      else params.delete('show')

      window.location.search = params.toString()
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
      // Auto-refresh toggle
      const autoRefreshCheckbox = document.getElementById('auto-refresh')
      if (autoRefreshCheckbox) {
        autoRefreshCheckbox.addEventListener('change', toggleAutoRefresh)
      }

      // Search functionality with debounce
      const searchInput = document.getElementById('search-input')
      if (searchInput) {
        searchInput.addEventListener('input', debounce(performSearch, 500))
      }

      // Filter change handlers
      const statusFilter = document.getElementById('status-filter')
      if (statusFilter) {
        statusFilter.addEventListener('change', performSearch)
      }

      const clientFilter = document.getElementById('client-filter')
      if (clientFilter) {
        clientFilter.addEventListener('change', performSearch)
      }

      const showFilter = document.getElementById('show-filter')
      if (showFilter) {
        showFilter.addEventListener('change', performSearch)
      }

      // Close modal when clicking outside
      document.addEventListener('click', function(e) {
        if (e.target.id === 'quick-create-modal' || e.target.id === 'mention-detail-modal') {
          if (e.target.id === 'quick-create-modal') closeQuickCreateModal()
          if (e.target.id === 'mention-detail-modal') closeMentionModal()
        }
      })
    })

    // Utility function for debouncing
    function debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    }
  </script>
{% endblock %}
