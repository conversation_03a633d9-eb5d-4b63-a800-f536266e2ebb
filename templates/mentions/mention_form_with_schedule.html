{% extends 'base.html' %} {% load static %} 
{% block title %}
 {% if object %}
Edit Mention 
{% else %} 
Create New Mention 
{% endif %} 
{% endblock %} 
{% block content %}
<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center">
        <a
          href="{% url 'mentions:mention_list' %}"
          class="text-gray-500 hover:text-gray-700 mr-4"
          ><i class="fa-solid fa-arrow-left"></i
        ></a>
        <h1 class="text-xl font-semibold text-gray-900">
          {% if object %} Edit Mention {% else %} Create New Mention {% endif %}
        </h1>
      </div>
    </div>

    <!-- Form -->
    <div class="p-6">
      <form method="post" class="space-y-8" id="mentionForm">
        {% csrf_token %}

        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Basic Information
          </h3>

          <div class="space-y-4">
            <div>
              <label
                for="{{ form.title.id_for_label }}"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Title <span class="text-red-500">*</span></label
              >
              {{ form.title }} {% if form.title.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.title.errors.0 }}</p>
              {% endif %}
            </div>

            <div>
              <label
                for="{{ form.client.id_for_label }}"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Client <span class="text-red-500">*</span></label
              >
              {{ form.client }} {% if form.client.errors %}
              <p class="mt-1 text-sm text-red-600">
                {{ form.client.errors.0 }}
              </p>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Content -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Content</h3>

          <div>
            <label
              for="{{ form.content.id_for_label }}"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Mention Content <span class="text-red-500">*</span></label
            >
            {{ form.content }} {% if form.content.errors %}
            <p class="mt-1 text-sm text-red-600">{{ form.content.errors.0 }}</p>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">
              This is the exact text that will be read on air
            </p>
          </div>
        </div>

        <!-- Settings -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Settings</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                for="{{ form.priority.id_for_label }}"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Priority</label
              >
              {{ form.priority }} {% if form.priority.errors %}
              <p class="mt-1 text-sm text-red-600">
                {{ form.priority.errors.0 }}
              </p>
              {% endif %}
            </div>

            <div>
              <label
                for="{{ form.duration_seconds.id_for_label }}"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Duration (seconds)</label
              >
              {{ form.duration_seconds }} {% if form.duration_seconds.errors %}
              <p class="mt-1 text-sm text-red-600">
                {{ form.duration_seconds.errors.0 }}
              </p>
              {% endif %}
              <p class="mt-1 text-sm text-gray-500">Estimated reading time</p>
            </div>
          </div>
        </div>

        <!-- Scheduling Section -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Scheduling</h3>

          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <i class="fa-solid fa-info-circle text-blue-400 mt-0.5"></i>
              </div>
              <div class="ml-3">
                <h4 class="text-sm font-medium text-blue-800">
                  Schedule to Shows
                </h4>
                <p class="text-sm text-blue-700 mt-1">
                  You can schedule this mention to specific shows and times. If
                  you skip this step, you can schedule it later from the
                  calendar.
                </p>
              </div>
            </div>
          </div>

          <div class="space-y-4">
            <div class="flex items-center">
              {{ form.schedule_immediately }}
              <label
                for="{{ form.schedule_immediately.id_for_label }}"
                class="ml-2 text-sm font-medium text-gray-700"
              >
                Schedule this mention to shows now
              </label>
            </div>

            <div id="schedulingSection" class="space-y-4" style="display: none">
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-sm font-medium text-gray-900">
                    Show Assignments
                  </h4>
                  <button
                    type="button"
                    onclick="addShowAssignment()"
                    class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200"
                  >
                    <i class="fa-solid fa-plus mr-1"></i>
                    Add Show
                  </button>
                </div>

                <div id="showAssignments" class="space-y-3">
                  <!-- Show assignments will be added here dynamically -->
                </div>

                <div class="mt-4 text-sm text-gray-500">
                  <i class="fa-solid fa-lightbulb mr-1"></i>
                  <strong>Tip:</strong> Select shows and times when this mention
                  should be read. The system will automatically assign the
                  primary presenter for each show.
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Notes -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            Additional Information
          </h3>

          <div>
            <label
              for="{{ form.notes.id_for_label }}"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Notes</label
            >
            {{ form.notes }} {% if form.notes.errors %}
            <p class="mt-1 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">
              Internal notes (not read on air)
            </p>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <a
            href="{% url 'mentions:mention_list' %}"
            class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
            >Cancel</a
          >
          <button
            type="submit"
            class="bg-primary-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-primary-700"
          >
            {% if object %} Update Mention {% else %} Create Mention {% endif %}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Character Counter -->
<div
  class="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-3"
  id="characterCounter"
  style="display: none"
>
  <div class="text-sm">
    <span class="text-gray-600">Characters:</span>
    <span id="charCount" class="font-medium">0</span>
    <span class="text-gray-400">/ Est. reading time:</span>
    <span id="readingTime" class="font-medium text-primary-600">0s</span>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  let assignmentCounter = 0;

  // Show data for validation
  const showsData = {
    {% for show in shows %}
      {{ show.id }}: {
        name: "{{ show.name }}",
        startTime: "{{ show.start_time|time:'H:i' }}",
        endTime: "{{ show.end_time|time:'H:i' }}",
        daysOfWeek: [{{ show.days_of_week|join:',' }}],
        timeDisplay: "{{ show.get_time_frame_display }}"
      }{% if not forloop.last %},{% endif %}
    {% endfor %}
  };

  document.addEventListener('DOMContentLoaded', function () {
    const contentTextarea = document.getElementById('id_content');
    const durationInput = document.getElementById('id_duration_seconds');
    const charCounter = document.getElementById('characterCounter');
    const charCount = document.getElementById('charCount');
    const readingTime = document.getElementById('readingTime');
    const scheduleCheckbox = document.getElementById('id_schedule_immediately');
    const schedulingSection = document.getElementById('schedulingSection');

    // Character counter functionality
    function updateCounter() {
      const text = contentTextarea.value;
      const chars = text.length;
      const words = text.trim().split(/\s+/).filter(word => word.length > 0).length;
      const estimatedSeconds = Math.ceil(words / 2.5); // Average reading speed

      charCount.textContent = chars;
      readingTime.textContent = estimatedSeconds + 's';

      // Auto-update duration if it's still default or empty
      if (!durationInput.value || durationInput.value == '30') {
        durationInput.value = Math.max(estimatedSeconds, 5);
      }

      // Show/hide counter
      if (chars > 0) {
        charCounter.style.display = 'block';
      } else {
        charCounter.style.display = 'none';
      }
    }

    // Toggle scheduling section
    function toggleScheduling() {
      if (scheduleCheckbox.checked) {
        schedulingSection.style.display = 'block';
        if (document.getElementById('showAssignments').children.length === 0) {
          addShowAssignment(); // Add first assignment
        }
      } else {
        schedulingSection.style.display = 'none';
      }
    }

    contentTextarea.addEventListener('input', updateCounter);
    scheduleCheckbox.addEventListener('change', toggleScheduling);

    updateCounter(); // Initial call
    toggleScheduling(); // Initial call
  });

  function addShowAssignment() {
    const container = document.getElementById('showAssignments');
    const assignmentId = assignmentCounter++;

    // Get tomorrow's date as default
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const defaultDate = tomorrow.toISOString().split('T')[0];

    // Create the assignment element
    const assignmentDiv = document.createElement('div');
    assignmentDiv.className = 'show-assignment border border-gray-200 rounded-lg p-4 bg-gray-50';
    assignmentDiv.setAttribute('data-assignment', assignmentId);

    assignmentDiv.innerHTML = `
      <div class="flex items-center justify-between mb-3">
        <h5 class="text-sm font-medium text-gray-900">Show Assignment #${assignmentId + 1}</h5>
        <button type="button" onclick="removeShowAssignment(${assignmentId})" class="text-red-600 hover:text-red-800">
          <i class="fa-solid fa-trash"></i>
        </button>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Show</label>
          <select name="shows" class="show-select w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" required>
            <option value="">Select a show...</option>
          </select>
          <div class="show-info mt-2 text-sm text-gray-600" style="display: none;">
            <i class="fa-solid fa-info-circle mr-1 text-blue-500"></i>
            <span class="info-text"></span>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Date</label>
          <input type="date" name="scheduled_dates" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" required>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Time</label>
          <input type="time" name="scheduled_times" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" required>
        </div>
      </div>
    `;

    container.appendChild(assignmentDiv);

    // Set the default date value
    const dateInput = assignmentDiv.querySelector('input[name="scheduled_dates"]');
    if (dateInput) {
      dateInput.value = defaultDate;
    }

    // Populate show options
    const showSelect = assignmentDiv.querySelector('.show-select');
    populateShowOptions(showSelect);

    // Add event listeners for validation
    showSelect.addEventListener('change', function() {
      updateTimeConstraints(this);
    });

    const timeInput = assignmentDiv.querySelector('input[name="scheduled_times"]');
    const dateInputElement = assignmentDiv.querySelector('input[name="scheduled_dates"]');

    timeInput.addEventListener('change', function() {
      validateTimeInput(this);
    });

    dateInputElement.addEventListener('change', function() {
      validateDateInput(this);
    });
  }

  function populateShowOptions(selectElement) {
    // Clear existing options except the first one
    while (selectElement.children.length > 1) {
      selectElement.removeChild(selectElement.lastChild);
    }

    // Add show options
    Object.keys(showsData).forEach(showId => {
      const show = showsData[showId];
      const option = document.createElement('option');
      option.value = showId;
      option.textContent = `${show.name} (${show.timeDisplay})`;
      option.setAttribute('data-start-time', show.startTime);
      option.setAttribute('data-end-time', show.endTime);
      option.setAttribute('data-days', show.daysOfWeek.join(','));
      selectElement.appendChild(option);
    });
  }

  function updateTimeConstraints(selectElement) {
    const assignment = selectElement.closest('.show-assignment');
    const showInfo = assignment.querySelector('.show-info');
    const infoText = assignment.querySelector('.info-text');
    const timeInput = assignment.querySelector('input[name="scheduled_times"]');

    if (selectElement.value) {
      const show = showsData[selectElement.value];
      showInfo.style.display = 'block';
      infoText.textContent = `This show airs ${show.timeDisplay}. Please select a time within this range.`;

      // Set time input constraints
      timeInput.setAttribute('min', show.startTime);
      timeInput.setAttribute('max', show.endTime);
    } else {
      showInfo.style.display = 'none';
      timeInput.removeAttribute('min');
      timeInput.removeAttribute('max');
    }
  }

  function validateTimeInput(timeInput) {
    const assignment = timeInput.closest('.show-assignment');
    const showSelect = assignment.querySelector('.show-select');

    if (showSelect.value && timeInput.value) {
      const show = showsData[showSelect.value];
      const selectedTime = timeInput.value;

      // Check if time is within show range
      if (!isTimeInRange(selectedTime, show.startTime, show.endTime)) {
        timeInput.setCustomValidity(`Time must be between ${show.startTime} and ${show.endTime}`);
        timeInput.classList.add('border-red-500');
      } else {
        timeInput.setCustomValidity('');
        timeInput.classList.remove('border-red-500');
      }
    }
  }

  function validateDateInput(dateInput) {
    const assignment = dateInput.closest('.show-assignment');
    const showSelect = assignment.querySelector('.show-select');

    if (showSelect.value && dateInput.value) {
      const show = showsData[showSelect.value];
      const selectedDate = new Date(dateInput.value);
      const dayOfWeek = selectedDate.getDay(); // 0=Sunday, 1=Monday, etc.

      // Convert to our format (0=Monday)
      const adjustedDay = dayOfWeek === 0 ? 6 : dayOfWeek - 1;

      if (!show.daysOfWeek.includes(adjustedDay)) {
        const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        const validDays = show.daysOfWeek.map(day => dayNames[day]).join(', ');
        dateInput.setCustomValidity(`This show only airs on: ${validDays}`);
        dateInput.classList.add('border-red-500');
      } else {
        dateInput.setCustomValidity('');
        dateInput.classList.remove('border-red-500');
      }
    }
  }

  function isTimeInRange(time, startTime, endTime) {
    // Handle shows that cross midnight
    if (endTime < startTime) {
      return time >= startTime || time <= endTime;
    } else {
      return time >= startTime && time <= endTime;
    }
  }

  function removeShowAssignment(assignmentId) {
    const assignment = document.querySelector(`[data-assignment="${assignmentId}"]`);
    if (assignment) {
      assignment.remove();
    }
  }
</script>
{% endblock %}
