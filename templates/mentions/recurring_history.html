{% extends 'base.html' %}
{% load static %}

{% block title %}Recurring History - {{ block.super }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    {% csrf_token %}
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <div class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                <a href="{% url 'mentions:recurring_mentions' %}" class="hover:text-blue-600">Recurring Patterns</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span>History</span>
            </div>
            <h1 class="text-2xl font-bold text-gray-900">Recurring History</h1>
            <p class="text-gray-600 mt-1">Track the lifecycle of recurring mention schedules</p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'mentions:recurring_mentions' %}" 
               class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Active
            </a>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-6">
        <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-list-alt text-2xl text-gray-400"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Total</p>
                    <p class="text-lg font-semibold text-gray-900">{{ stats.total }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-play-circle text-2xl text-green-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Active</p>
                    <p class="text-lg font-semibold text-green-600">{{ stats.active }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-pause-circle text-2xl text-orange-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Paused</p>
                    <p class="text-lg font-semibold text-orange-600">{{ stats.paused }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-stop-circle text-2xl text-gray-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Ended</p>
                    <p class="text-lg font-semibold text-gray-600">{{ stats.ended }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-2xl text-blue-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Finished</p>
                    <p class="text-lg font-semibold text-blue-600">{{ stats.finished }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-times-circle text-2xl text-red-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Canceled</p>
                    <p class="text-lg font-semibold text-red-600">{{ stats.canceled }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exchange-alt text-2xl text-orange-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Replaced</p>
                    <p class="text-lg font-semibold text-orange-600">{{ stats.replaced }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-code-branch text-2xl text-purple-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Split</p>
                    <p class="text-lg font-semibold text-purple-600">{{ stats.split }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Filter Recurring History</h3>
        <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Status Filter -->
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" id="status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Status</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Frequency Filter -->
            <div>
                <label for="frequency" class="block text-sm font-medium text-gray-700 mb-1">Frequency</label>
                <select name="frequency" id="frequency"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Frequencies</option>
                    <option value="daily" {% if request.GET.frequency == 'daily' %}selected{% endif %}>Daily</option>
                    <option value="weekly" {% if request.GET.frequency == 'weekly' %}selected{% endif %}>Weekly</option>
                    <option value="monthly" {% if request.GET.frequency == 'monthly' %}selected{% endif %}>Monthly</option>
                    <option value="custom" {% if request.GET.frequency == 'custom' %}selected{% endif %}>Custom</option>
                </select>
            </div>

            <!-- Priority Filter -->
            <div>
                <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                <select name="priority" id="priority"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Priorities</option>
                    <option value="1" {% if request.GET.priority == '1' %}selected{% endif %}>Low</option>
                    <option value="2" {% if request.GET.priority == '2' %}selected{% endif %}>Normal</option>
                    <option value="3" {% if request.GET.priority == '3' %}selected{% endif %}>High</option>
                    <option value="4" {% if request.GET.priority == '4' %}selected{% endif %}>Urgent</option>
                </select>
            </div>

            <!-- Client Filter -->
            <div>
                <label for="client" class="block text-sm font-medium text-gray-700 mb-1">Client</label>
                <select name="client" id="client"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Clients</option>
                    {% for client in clients %}
                        <option value="{{ client.id }}" {% if client_filter == client.id|stringformat:"s" %}selected{% endif %}>{{ client.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Search -->
            <div class="md:col-span-2">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" id="search" value="{{ search_query }}"
                       placeholder="Search by title, content, or campaign name..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Date From -->
            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">Start Date From</label>
                <input type="date" name="date_from" id="date_from" value="{{ date_from }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Date To -->
            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">Start Date To</label>
                <input type="date" name="date_to" id="date_to" value="{{ date_to }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>

            <!-- Actions -->
            <div class="lg:col-span-4 flex justify-end space-x-3 mt-4">
                <a href="{% url 'mentions:recurring_history' %}"
                   class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
                    Clear Filters
                </a>
                <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                    <i class="fa-solid fa-filter mr-2"></i>
                    Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Results Table -->
    <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
        {% if page_obj %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Campaign / Title
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Client
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date Range
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Progress
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                            </th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for recurring_mention in page_obj %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">
                                        {% if recurring_mention.campaign_name %}
                                            {{ recurring_mention.campaign_name }}
                                        {% else %}
                                            {{ recurring_mention.title|truncatechars:40 }}
                                        {% endif %}
                                    </div>
                                    {% if recurring_mention.campaign_name and recurring_mention.title != recurring_mention.campaign_name %}
                                        <div class="text-sm text-gray-500">{{ recurring_mention.title|truncatechars:50 }}</div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ recurring_mention.client.name }}</div>
                                <div class="text-sm text-gray-500">{{ recurring_mention.get_frequency_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {{ recurring_mention.start_date|date:"M d, Y" }}
                                    {% if recurring_mention.end_date %}
                                        - {{ recurring_mention.end_date|date:"M d, Y" }}
                                    {% else %}
                                        - Ongoing
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if recurring_mention.status == 'active' %}bg-green-100 text-green-800
                                    {% elif recurring_mention.status == 'paused' %}bg-orange-100 text-orange-800
                                    {% elif recurring_mention.status == 'ended' %}bg-gray-100 text-gray-800
                                    {% elif recurring_mention.status == 'finished' %}bg-blue-100 text-blue-800
                                    {% elif recurring_mention.status == 'canceled' %}bg-red-100 text-red-800
                                    {% elif recurring_mention.status == 'replaced' %}bg-orange-100 text-orange-800
                                    {% elif recurring_mention.status == 'split' %}bg-purple-100 text-purple-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {% if recurring_mention.status == 'active' %}<i class="fas fa-play-circle mr-1"></i>
                                    {% elif recurring_mention.status == 'paused' %}<i class="fas fa-pause-circle mr-1"></i>
                                    {% elif recurring_mention.status == 'ended' %}<i class="fas fa-stop-circle mr-1"></i>
                                    {% elif recurring_mention.status == 'finished' %}<i class="fas fa-check-circle mr-1"></i>
                                    {% elif recurring_mention.status == 'canceled' %}<i class="fas fa-times-circle mr-1"></i>
                                    {% elif recurring_mention.status == 'replaced' %}<i class="fas fa-exchange-alt mr-1"></i>
                                    {% elif recurring_mention.status == 'split' %}<i class="fas fa-code-branch mr-1"></i>
                                    {% endif %}
                                    {{ recurring_mention.get_status_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {{ recurring_mention.total_actual }}{% if recurring_mention.total_planned > 0 %} / {{ recurring_mention.total_planned }}{% endif %}
                                </div>
                                {% if recurring_mention.total_planned > 0 %}
                                    <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ recurring_mention.completion_percentage }}%"></div>
                                    </div>
                                    <div class="text-xs text-gray-500 mt-1">{{ recurring_mention.completion_percentage|floatformat:0 }}%</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div>{{ recurring_mention.created_at|date:"M d, Y" }}</div>
                                {% if recurring_mention.created_by %}
                                    <div class="text-xs">by {{ recurring_mention.created_by.get_full_name|default:recurring_mention.created_by.username }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    {% if recurring_mention.status == 'active' %}
                                        <a href="{% url 'mentions:recurring_mention_edit' recurring_mention.pk %}"
                                           class="text-blue-600 hover:text-blue-900" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button onclick="pauseRecurring({{ recurring_mention.pk }})"
                                                class="text-orange-600 hover:text-orange-900" title="Pause">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button onclick="endRecurring({{ recurring_mention.pk }})"
                                                class="text-gray-600 hover:text-gray-900" title="End">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                        <button onclick="cancelRecurring({{ recurring_mention.pk }})"
                                                class="text-red-600 hover:text-red-900" title="Cancel">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    {% elif recurring_mention.status == 'paused' %}
                                        <button onclick="resumeRecurring({{ recurring_mention.pk }})"
                                                class="text-green-600 hover:text-green-900" title="Resume">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <button onclick="endRecurring({{ recurring_mention.pk }})"
                                                class="text-gray-600 hover:text-gray-900" title="End">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                        <button onclick="cancelRecurring({{ recurring_mention.pk }})"
                                                class="text-red-600 hover:text-red-900" title="Cancel">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    {% else %}
                                        <span class="text-gray-400" title="No actions available for {{ recurring_mention.get_status_display|lower }} schedules">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" 
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" 
                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                                            {{ num }}
                                        </span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" 
                                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ num }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-history text-2xl text-gray-400"></i>
                </div>
                <h4 class="text-lg font-medium text-gray-900 mb-2">No recurring history found</h4>
                <p class="text-gray-500 mb-4">
                    {% if status_filter or search_query or date_from or date_to %}
                        Try adjusting your filters to see more results.
                    {% else %}
                        No recurring mention schedules have been created yet.
                    {% endif %}
                </p>
                {% if status_filter or search_query or date_from or date_to %}
                    <a href="{% url 'mentions:recurring_history' %}" 
                       class="text-blue-600 hover:text-blue-500 font-medium">
                        Clear all filters
                    </a>
                {% else %}
                    <a href="{% url 'mentions:recurring_mentions' %}" 
                       class="text-blue-600 hover:text-blue-500 font-medium">
                        View active recurring patterns
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<script>
// Status management functions
function updateRecurringStatus(recurringId, newStatus, confirmMessage, successMessage) {
    if (confirm(confirmMessage)) {
        fetch(`/mentions/recurring/${recurringId}/update-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status: newStatus,
                cancel_future_mentions: newStatus === 'canceled'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(successMessage);
                location.reload(); // Refresh to show updated status
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the status.');
        });
    }
}

function pauseRecurring(recurringId) {
    updateRecurringStatus(
        recurringId,
        'paused',
        'Are you sure you want to pause this recurring mention? It will stop generating new mentions until resumed.',
        'Recurring mention has been paused successfully.'
    );
}

function resumeRecurring(recurringId) {
    updateRecurringStatus(
        recurringId,
        'active',
        'Are you sure you want to resume this recurring mention? It will start generating mentions again.',
        'Recurring mention has been resumed successfully.'
    );
}

function endRecurring(recurringId) {
    updateRecurringStatus(
        recurringId,
        'ended',
        'Are you sure you want to end this recurring mention? This will mark it as completed and stop all future mentions.',
        'Recurring mention has been ended successfully.'
    );
}

function cancelRecurring(recurringId) {
    updateRecurringStatus(
        recurringId,
        'canceled',
        'Are you sure you want to cancel this recurring mention? This will cancel all future unread mentions and mark the pattern as canceled.',
        'Recurring mention has been canceled successfully.'
    );
}
</script>

{% endblock %}
