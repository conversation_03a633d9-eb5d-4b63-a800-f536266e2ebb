{% extends 'base.html' %}
{% load static %}
{% load report_extras %}

{% block title %}{% if recurring_mention %}Edit Recurring Mention{% else %}Create Recurring Mention{% endif %} - RadioMention{% endblock %}

{% block page_title %}{% if recurring_mention %}Edit Recurring Mention{% else %}Create Recurring Mention{% endif %}{% endblock %}

{% block header_actions %}
<a href="{% url 'mentions:recurring_mentions' %}" class="px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 flex items-center">
    <i class="fa-solid fa-arrow-left mr-2"></i>
    Back to Recurring Mentions
</a>
{% endblock %}

{% block content %}
{% if not recurring_mention %}
<!-- Redirect to new simplified wizard for new recurring mentions -->
<script>
    window.location.href = "{% url 'mentions:recurring_wizard' %}";
</script>
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
        <div class="mb-4">
            <i class="fa-solid fa-spinner fa-spin text-blue-600 text-3xl"></i>
        </div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">Redirecting to Wizard</h3>
        <p class="text-gray-600">You're being redirected to the new step-by-step wizard for creating recurring mentions.</p>
        <div class="mt-4">
            <a href="{% url 'mentions:recurring_wizard' %}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                Go to New Wizard
            </a>
        </div>
    </div>
</div>
{% else %}
<!-- Keep the edit form for existing recurring mentions -->
<div class="max-w-6xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6">
            <!-- Edit Warning -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fa-solid fa-exclamation-triangle text-yellow-600"></i>
                    </div>
                    <div class="ml-3">
                        <h4 class="text-sm font-medium text-yellow-800">Editing Recurring Mention</h4>
                        <div class="mt-1 text-sm text-yellow-700">
                            <p>• Changes to schedule patterns will regenerate future mentions</p>
                            <p>• Content changes will update existing unread mentions</p>
                            <p>• Already read mentions will not be affected</p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="post" class="space-y-6" id="edit-form">
                {% csrf_token %}

                <!-- Basic Information -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fa-solid fa-info-circle mr-2 text-blue-600"></i>
                        Basic Information
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-1">
                                Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="title" id="title" required
                                   value="{% if recurring_mention %}{{ recurring_mention.title }}{% endif %}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label for="client" class="block text-sm font-medium text-gray-700 mb-1">
                                Client <span class="text-red-500">*</span>
                            </label>
                            <select name="client" id="client" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Select a client</option>
                                {% for client in clients %}
                                <option value="{{ client.id }}"{% if recurring_mention and recurring_mention.client_id == client.id %} selected{% endif %}>
                                    {{ client.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <div class="mt-4">
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-1">
                            Content <span class="text-red-500">*</span>
                        </label>
                        <textarea name="content" id="content" rows="4" required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">{% if recurring_mention %}{{ recurring_mention.content }}{% endif %}</textarea>
                        <p class="text-xs text-gray-500 mt-1">
                            Use placeholders like [CLIENT NAME], [SPONSOR NAME], [PROMOTIONAL MESSAGE] for dynamic content
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label for="duration_seconds" class="block text-sm font-medium text-gray-700 mb-1">
                                Duration (seconds) <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="duration_seconds" id="duration_seconds" min="10" max="300" required
                                   value="{% if recurring_mention %}{{ recurring_mention.duration_seconds }}{% else %}30{% endif %}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Typical radio spots: 15s, 30s, 60s</p>
                        </div>

                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">
                                Priority
                            </label>
                            <select name="priority" id="priority"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="1" {% if recurring_mention and recurring_mention.priority == 1 %}selected{% endif %}>Low</option>
                                <option value="2" {% if recurring_mention and recurring_mention.priority == 2 or not recurring_mention %}selected{% endif %}>Normal</option>
                                <option value="3" {% if recurring_mention and recurring_mention.priority == 3 %}selected{% endif %}>High</option>
                                <option value="4" {% if recurring_mention and recurring_mention.priority == 4 %}selected{% endif %}>Urgent</option>
                            </select>
                        </div>
                    </div>

                    <!-- Campaign Tracking Fields -->
                    <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h4 class="text-sm font-medium text-blue-800 mb-3">
                            <i class="fa-solid fa-chart-line mr-1"></i>
                            Campaign Tracking (Optional)
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="campaign_name" class="block text-sm font-medium text-gray-700 mb-1">
                                    Campaign Name
                                </label>
                                <input type="text" name="campaign_name" id="campaign_name"
                                       value="{% if recurring_mention %}{{ recurring_mention.campaign_name }}{% endif %}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <p class="text-xs text-gray-500 mt-1">For tracking and reporting purposes</p>
                            </div>

                            <div>
                                <label for="daily_frequency" class="block text-sm font-medium text-gray-700 mb-1">
                                    Target Mentions/Day
                                </label>
                                <input type="number" name="daily_frequency" id="daily_frequency" min="1"
                                       value="{% if recurring_mention %}{{ recurring_mention.daily_frequency }}{% else %}1{% endif %}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <p class="text-xs text-gray-500 mt-1">How many times per day</p>
                            </div>

                            <div>
                                <label for="total_required" class="block text-sm font-medium text-gray-700 mb-1">
                                    Total Required
                                </label>
                                <input type="number" name="total_required" id="total_required" min="1"
                                       value="{% if recurring_mention and recurring_mention.total_required %}{{ recurring_mention.total_required }}{% endif %}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <p class="text-xs text-gray-500 mt-1">Total mentions needed for campaign</p>
                            </div>
                        </div>

                        {% if recurring_mention and recurring_mention.total_aired > 0 %}
                        <div class="mt-3 p-3 bg-green-50 border border-green-200 rounded-md">
                            <div class="flex items-center">
                                <i class="fa-solid fa-check-circle text-green-600 mr-2"></i>
                                <span class="text-sm text-green-800">
                                    <strong>{{ recurring_mention.total_aired }}</strong> mentions have been aired so far
                                    {% if recurring_mention.total_required %}
                                        ({{ recurring_mention.total_aired }}/{{ recurring_mention.total_required }} - {{ recurring_mention.total_aired|percentage:recurring_mention.total_required }}% complete)
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Recurrence Settings -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fa-solid fa-repeat mr-2 text-green-600"></i>
                        Recurrence Pattern
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="frequency" class="block text-sm font-medium text-gray-700 mb-1">
                                Frequency <span class="text-red-500">*</span>
                            </label>
                            <select name="frequency" id="frequency" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="daily" {% if recurring_mention and recurring_mention.frequency == 'daily' %}selected{% endif %}>Daily Rotation</option>
                                <option value="weekly" {% if recurring_mention and recurring_mention.frequency == 'weekly' %}selected{% endif %}>Weekly Schedule</option>
                                <option value="monthly" {% if recurring_mention and recurring_mention.frequency == 'monthly' %}selected{% endif %}>Monthly Campaign</option>
                                <option value="custom" {% if recurring_mention and recurring_mention.frequency == 'custom' %}selected{% endif %}>Hourly/Custom Pattern</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">Choose the pattern that fits your radio programming</p>
                        </div>

                        <div>
                            <label for="interval" class="block text-sm font-medium text-gray-700 mb-1">
                                Interval
                            </label>
                            <input type="number" name="interval" id="interval" min="1"
                                   value="{% if recurring_mention %}{{ recurring_mention.interval }}{% else %}1{% endif %}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Every X days/weeks/months</p>
                        </div>
                    </div>

                    <!-- Weekdays (for weekly frequency) -->
                    <div id="weekdaysSection" class="mt-4" style="{% if recurring_mention.frequency == 'weekly' %}display: block;{% else %}display: none;{% endif %}">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Days of Week <span class="text-red-500">*</span>
                        </label>
                        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
                            {% for day_num, day_name in weekday_choices %}
                            <label class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                                <input type="checkbox" name="weekdays" value="{{ day_num }}"
                                       {% if recurring_mention and day_num in recurring_mention.weekdays %}checked{% endif %}
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">{{ day_name }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        <p class="text-xs text-gray-500 mt-2">Select the days when this mention should air</p>
                    </div>
                </div>

                <!-- Date Range -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fa-solid fa-calendar-days mr-2 text-purple-600"></i>
                        Campaign Period
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">
                                Start Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" name="start_date" id="start_date" required
                                   value="{% if recurring_mention %}{{ recurring_mention.start_date|date:'Y-m-d' }}{% endif %}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <p class="text-xs text-gray-500 mt-1">When the campaign should begin</p>
                        </div>

                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">
                                End Date
                            </label>
                            <input type="date" name="end_date" id="end_date"
                                   value="{% if recurring_mention and recurring_mention.end_date %}{{ recurring_mention.end_date|date:'Y-m-d' }}{% endif %}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Leave blank for no end date</p>
                        </div>

                        <div>
                            <label for="max_occurrences" class="block text-sm font-medium text-gray-700 mb-1">
                                Max Occurrences
                            </label>
                            <input type="number" name="max_occurrences" id="max_occurrences" min="1"
                                   value="{% if recurring_mention and recurring_mention.max_occurrences %}{{ recurring_mention.max_occurrences }}{% endif %}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <p class="text-xs text-gray-500 mt-1">Maximum number of mentions to generate</p>
                        </div>
                    </div>

                    <!-- Quick Duration Buttons -->
                    {% comment %} <div class="mt-4">
                        <p class="text-sm font-medium text-gray-700 mb-2">Quick Duration:</p>
                        <div class="flex flex-wrap gap-2">
                            <button type="button" onclick="setDuration(7)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">1 Week</button>
                            <button type="button" onclick="setDuration(14)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">2 Weeks</button>
                            <button type="button" onclick="setDuration(30)" class="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-800 rounded-md">1 Month</button>
                            <button type="button" onclick="setDuration(60)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">2 Months</button>
                            <button type="button" onclick="setDuration(90)" class="px-3 py-1 text-xs bg-green-100 hover:bg-green-200 text-green-800 rounded-md">3 Months</button>
                            <button type="button" onclick="setDuration(180)" class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md">6 Months</button>
                        </div>
                    </div> {% endcomment %}
                </div>

                <!-- Show Assignments -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fa-solid fa-radio mr-2 text-orange-600"></i>
                        Show Schedule & Time Slots
                    </h3>

                    {% if recurring_mention and existing_assignments %}
                    <!-- Schedule Summary -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <h4 class="text-sm font-medium text-green-800 mb-3">
                            <i class="fa-solid fa-calendar-check mr-1"></i>
                            Current Schedule Summary
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 text-xs">
                            {% for day_num, day_name in weekday_choices %}
                                {% if day_num in recurring_mention.weekdays %}
                                <div class="bg-white border border-green-300 rounded p-2">
                                    <div class="font-medium text-green-800">{{ day_name }}</div>
                                    {% for assignment in existing_assignments %}
                                        {% if day_num in assignment.scheduled_days %}
                                        <div class="text-green-700 mt-1">
                                            {{ assignment.scheduled_time|time:"H:i" }} - {{ assignment.show.name }}
                                        </div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <p class="text-xs text-green-600 mt-3">
                            <i class="fa-solid fa-info-circle mr-1"></i>
                            This shows your current schedule. Edit individual assignments below to make changes.
                        </p>
                    </div>
                    {% endif %}

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fa-solid fa-info-circle text-blue-600"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-blue-800">Advanced Time Slot Management</h4>
                                <div class="mt-1 text-sm text-blue-700">
                                    <p>• Time slots are generated in 30-second intervals for precise scheduling</p>
                                    <p>• Conflict detection will warn about overlapping mentions</p>
                                    <p>• Presenters are assigned automatically when mentions are read</p>
                                    <p>• Each assignment can be scheduled for multiple days of the week</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="showAssignments">
                        {% if recurring_mention and existing_assignments %}
                            <!-- Show existing assignments for editing -->
                            {% for assignment in existing_assignments %}
                            <div class="show-assignment border border-gray-200 rounded-lg p-4 mb-4 schedule-row">
                                <!-- Assignment Header -->
                                <div class="mb-3 pb-2 border-b border-gray-200">
                                    <h5 class="text-sm font-medium text-gray-900">
                                        Assignment {{ forloop.counter }}: {{ assignment.show.name }} at {{ assignment.scheduled_time|time:"H:i" }}
                                    </h5>
                                    <p class="text-xs text-gray-600 mt-1">
                                        Currently scheduled for:
                                        {% for day_num in assignment.scheduled_days %}
                                            {% for day_number, day_name in weekday_choices %}
                                                {% if day_number == day_num %}{{ day_name }}{% if not forloop.last %}, {% endif %}{% endif %}
                                            {% endfor %}
                                        {% endfor %}
                                    </p>
                                </div>
                                <!-- Hidden field to track assignment ID for updates -->
                                <input type="hidden" name="assignment_ids" value="{{ assignment.id }}">

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Show <span class="text-red-500">*</span>
                                        </label>
                                        <select name="shows" required
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 show-select"
                                                data-assignment-id="{{ assignment.id }}" onchange="validateShowTimeEdit(this)">
                                            <option value="">Select a show</option>
                                            {% for show in shows %}
                                            <option value="{{ show.id }}"
                                                    data-start-time="{{ show.start_time|time:"H:i" }}"
                                                    data-end-time="{{ show.end_time|time:"H:i" }}"
                                                    {% if show.id == assignment.show.id %}selected{% endif %}>
                                                {{ show.name }}
                                                {% if show.start_time and show.end_time %}
                                                    ({{ show.start_time|time:"H:i" }} - {{ show.end_time|time:"H:i" }})
                                                {% endif %}
                                            </option>
                                            {% endfor %}
                                        </select>
                                        <p class="text-xs text-gray-500 mt-1">Choose which show will air this mention</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Mention Time <span class="text-red-500">*</span>
                                            <span class="text-xs text-gray-500 time-hint" style="display: none;"></span>
                                        </label>
                                        <select name="scheduled_times" required
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 time-select"
                                                data-assignment-id="{{ assignment.id }}"
                                                data-current-time="{{ assignment.scheduled_time|time:'H:i:s' }}"
                                                onchange="validateTimeSelectionEdit(this)">
                                            <option value="{{ assignment.scheduled_time|time:'H:i:s' }}" selected>{{ assignment.scheduled_time|time:'H:i' }}</option>
                                        </select>
                                        <div class="time-validation-message text-xs mt-1" style="display: none;"></div>
                                        <div class="conflict-indicator text-xs mt-1" style="display: none;"></div>
                                    </div>
                                </div>

                                <!-- Scheduled Days for this Assignment -->
                                <div class="mt-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        Scheduled Days for this Show/Time <span class="text-red-500">*</span>
                                    </label>
                                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
                                        {% for day_num, day_name in weekday_choices %}
                                        <label class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                                            <input type="checkbox" name="scheduled_days_{{ assignment.id }}" value="{{ day_num }}"
                                                   {% if day_num in assignment.scheduled_days %}checked{% endif %}
                                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <span class="ml-2 text-sm text-gray-700">{{ day_name }}</span>
                                        </label>
                                        {% endfor %}
                                    </div>
                                    <p class="text-xs text-gray-500 mt-2">Select which days this specific show/time combination should air</p>
                                </div>

                                {% if not forloop.first %}
                                <div class="mt-3">
                                    <button type="button" onclick="removeShowAssignment(this)"
                                            class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                                        <i class="fa-solid fa-trash mr-1"></i>
                                        Remove Assignment
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        {% else %}
                            <!-- Default empty assignment for new recurring mentions -->
                            <div class="show-assignment border border-gray-200 rounded-lg p-4 schedule-row">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Show <span class="text-red-500">*</span>
                                        </label>
                                        <select name="shows" required
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 show-select"
                                                onchange="validateShowTimeEdit(this)">
                                            <option value="">Select a show</option>
                                            {% for show in shows %}
                                            <option value="{{ show.id }}"
                                                    data-start-time="{{ show.start_time|time:"H:i" }}"
                                                    data-end-time="{{ show.end_time|time:"H:i" }}">
                                                {{ show.name }}
                                                {% if show.start_time and show.end_time %}
                                                    ({{ show.start_time|time:"H:i" }} - {{ show.end_time|time:"H:i" }})
                                                {% endif %}
                                            </option>
                                            {% endfor %}
                                        </select>
                                        <p class="text-xs text-gray-500 mt-1">Choose which show will air this mention</p>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">
                                            Mention Time <span class="text-red-500">*</span>
                                            <span class="text-xs text-gray-500 time-hint" style="display: none;"></span>
                                        </label>
                                        <select name="scheduled_times" required
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 time-select"
                                                onchange="validateTimeSelectionEdit(this)" disabled>
                                            <option value="">Select a show first</option>
                                        </select>
                                        <div class="time-validation-message text-xs mt-1" style="display: none;"></div>
                                        <div class="conflict-indicator text-xs mt-1" style="display: none;"></div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>

                    <button type="button" onclick="addShowAssignmentEdit()"
                            class="mt-4 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 flex items-center">
                        <i class="fa-solid fa-plus mr-2"></i>
                        Add Another Show Assignment
                    </button>

                    <!-- Advanced Options -->
                    <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <h5 class="text-sm font-medium text-gray-900 mb-3">
                            <i class="fa-solid fa-cog mr-1"></i>
                            Advanced Options
                        </h5>
                        <div class="flex items-center">
                            <input type="checkbox" name="check_conflicts" id="check_conflicts" value="true" checked
                                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label for="check_conflicts" class="ml-2 text-sm text-gray-700">
                                Check for conflicts with existing recurring mentions
                            </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">
                            Uncheck this if you want to allow overlapping schedules (e.g., different clients using the same show/time)
                        </p>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <i class="fa-solid fa-clock mr-1"></i>
                            Changes will be applied to future mentions only
                        </div>
                        <div class="flex space-x-3">
                            <a href="{% url 'mentions:recurring_mentions' %}"
                               class="px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 flex items-center">
                                <i class="fa-solid fa-arrow-left mr-2"></i>
                                Cancel
                            </a>
                            <button type="button" onclick="validateFormAndPreview()"
                                    class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm font-medium flex items-center">
                                <i class="fa-solid fa-eye mr-2"></i>
                                Preview Changes
                            </button>
                            <button type="submit" onclick="return confirmUpdate()"
                                    class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 text-sm font-medium flex items-center">
                                <i class="fa-solid fa-save mr-2"></i>
                                Update Recurring Mention
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Enhanced styling for the edit form -->
<style>
/* Enhanced styling for disabled show options */
select option:disabled {
    color: #9CA3AF !important;
    background-color: #F3F4F6 !important;
    font-style: italic !important;
    text-decoration: line-through !important;
}

select optgroup[label*="🚫"] {
    color: #9CA3AF !important;
    font-style: italic !important;
}

/* Ensure disabled options are clearly distinguishable */
.show-select option[disabled] {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
}

/* Duplicate highlighting styles */
.duplicate-highlight {
    background-color: #FEF3C7 !important;
    border-color: #F59E0B !important;
    animation: pulse-warning 2s infinite;
}

/* Conflict highlighting styles */
.conflict-highlight {
    background-color: #FEE2E2 !important;
    border-color: #EF4444 !important;
    animation: pulse-error 2s infinite;
}

.conflict-indicator {
    font-size: 0.75rem;
    line-height: 1rem;
}

.border-orange-300 {
    border-color: #FDBA74 !important;
}

/* Time slot dropdown styling */
.time-select option.unavailable-slot {
    color: #9CA3AF !important;
    background-color: #F3F4F6 !important;
    font-style: italic;
}

.time-select option.warning-slot {
    color: #D97706 !important;
    background-color: #FEF3C7 !important;
}

.time-select option.available-slot {
    color: #059669 !important;
    background-color: #ECFDF5 !important;
}

@keyframes pulse-warning {
    0%, 100% {
        background-color: #FEF3C7;
    }
    50% {
        background-color: #FDE68A;
    }
}

@keyframes pulse-error {
    0%, 100% {
        background-color: #FEE2E2;
    }
    50% {
        background-color: #FECACA;
    }
}

/* Enhanced border colors for validation states */
.border-red-300 {
    border-color: #FCA5A5 !important;
    box-shadow: 0 0 0 1px #FCA5A5 !important;
}

.border-green-300 {
    border-color: #86EFAC !important;
    box-shadow: 0 0 0 1px #86EFAC !important;
}

.border-yellow-300 {
    border-color: #FDE047 !important;
    box-shadow: 0 0 0 1px #FDE047 !important;
}
</style>

<script src="{% static 'js/recurring-mentions.js' %}"></script>
<script>
// Initialize the recurring mentions form with data from Django
document.addEventListener('DOMContentLoaded', function() {
    // Show data for validation
    const showsData = {
        {% for show in shows %}
        {{ show.id }}: {
            name: '{{ show.name|escapejs }}',
            startTime: '{{ show.start_time|time:"H:i" }}',
            endTime: '{{ show.end_time|time:"H:i" }}',
            timeFrame: '{{ show.get_time_frame_display|escapejs }}'
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    };

    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    // Initialize the recurring mentions functionality
    initializeRecurringMentions(showsData, csrfToken);
});




</script>
{% endif %}
{% endblock %}
