{% extends 'base.html' %}
{% load static %}
{% load permission_tags %}

{% block title %}
  {{ mention.title }} - Mention Details
{% endblock %}

{% block content %}
  {% csrf_token %}
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <a href="{% url 'mentions:mention_list' %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
            <h1 class="text-xl font-semibold text-gray-900">Mention Details</h1>
          </div>
          <div class="flex space-x-2">
            {% if mention.status == 'pending' %}
              <button onclick="approveMention({{ mention.pk }})" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
                <i class="fa-solid fa-check mr-1"></i>
                Approve
              </button>
              <button onclick="rejectMention({{ mention.pk }})" class="bg-red-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-red-700">
                <i class="fa-solid fa-times mr-1"></i>
                Reject
              </button>
            {% endif %}
            <a href="{% url 'mentions:mention_edit' mention.pk %}" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
              <i class="fa-solid fa-edit mr-1"></i>
              Edit
            </a>
            <button onclick="deleteMention({{ mention.pk }})" class="bg-red-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-red-700">
              <i class="fa-solid fa-trash mr-1"></i>
              Delete
            </button>
          </div>
        </div>
      </div>

      <!-- Mention Info -->
      <div class="p-6">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h2 class="text-2xl font-bold text-gray-900">{{ mention.title }}</h2>
            <p class="text-lg text-gray-600 mt-1">{{ mention.client.name }}</p>
            <div class="flex items-center mt-3 space-x-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if mention.status == 'pending' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-yellow-100 text-yellow-800











                {% elif mention.status == 'scheduled' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-blue-100 text-blue-800











                {% elif mention.status == 'completed' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-green-100 text-green-800











                {% elif mention.status == 'cancelled' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-red-100 text-red-800











                {% endif %}">
                {{ mention.get_status_display }}
              </span>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if mention.priority == 'high' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-red-100 text-red-800











                {% elif mention.priority == 'medium' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-yellow-100 text-yellow-800











                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-green-100 text-green-800











                {% endif %}">
                {{ mention.get_priority_display }} Priority
              </span>
              <span class="text-sm text-gray-500">{{ mention.duration_seconds }}s duration</span>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm text-gray-500">Created</p>
            <p class="text-sm font-medium text-gray-900">{{ mention.created_at|date:'M d, Y' }}</p>
            <p class="text-xs text-gray-500">{{ mention.created_at|time:'g:i A' }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Content</h3>
      </div>
      <div class="p-6">
        <div class="prose max-w-none">{{ mention.content|linebreaks }}</div>
      </div>
    </div>

    <!-- Approval Information -->
    {% if mention.approved_by or mention.approved_at %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Approval Information</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% if mention.approved_by %}
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Approved By</label>
                <p class="text-gray-900">{{ mention.approved_by.get_full_name|default:mention.approved_by.username }}</p>
              </div>
            {% endif %}
            {% if mention.approved_at %}
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Approved At</label>
                <p class="text-gray-900">{{ mention.approved_at|date:'M d, Y g:i A' }}</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Notes -->
    {% if mention.notes %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Notes</h3>
        </div>
        <div class="p-6">
          <p class="text-gray-700">{{ mention.notes|linebreaks }}</p>
        </div>
      </div>
    {% endif %}

    <!-- Enhanced Scheduled Readings -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <h3 class="text-lg font-medium text-gray-900">
              {% has_role 'presenter' as is_presenter %}
              {% if is_presenter %}
                My Scheduled Readings
              {% else %}
                Scheduled Readings
              {% endif %}
            </h3>
            {% if has_conflicts %}
              <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                Conflicts Detected
              </span>
            {% endif %}
          </div>
          <div class="flex items-center space-x-2">
            {% if can_schedule and mention.status == 'scheduled' %}
              <a href="{% url 'mentions:calendar' %}?mention={{ mention.pk }}" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
                <i class="fa-solid fa-calendar-plus mr-1"></i>
                Schedule Reading
              </a>
            {% endif %}
            {% if readings.exists and can_manage %}
              <button onclick="showBulkActions()" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
                <i class="fa-solid fa-cog mr-1"></i>
                Bulk Actions
              </button>
            {% endif %}
          </div>
        </div>
      </div>
      <div class="p-6">
        {% if readings.exists %}
          <!-- Role-based reading display -->
          {% has_role 'presenter' as is_presenter %}
          {% if is_presenter %}
            <!-- Presenter View: Show only their readings -->
            {% if user_readings.exists %}
              <div class="space-y-4">
                {% for reading in user_readings %}
                  {% include 'mentions/includes/reading_card.html' with reading=reading show_actions=True %}
                {% endfor %}
              </div>
              <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-center">
                  <i class="fa-solid fa-info-circle text-blue-600 mr-2"></i>
                  <span class="text-sm text-blue-800">
                    Showing only readings assigned to you.{% if current_membership.user.presenter_profile %}
                      <a href="{% url 'core:presenter_calendar' current_membership.user.presenter_profile.pk %}" class="font-medium underline hover:no-underline">View your full calendar →</a>
                    {% else %}
                      <span class="font-medium">Calendar not available</span>
                    {% endif %}
                  </span>
                </div>
              </div>
            {% else %}
              <div class="text-center py-8">
                <i class="fa-solid fa-calendar-times text-4xl text-gray-300 mb-4"></i>
                <h4 class="text-lg font-medium text-gray-900 mb-2">No readings assigned to you</h4>
                <p class="text-gray-600">This mention has other scheduled readings, but none are assigned to you.</p>
              </div>
            {% endif %}
          {% else %}
            <!-- Admin/Manager View: Show all readings -->
            <div class="space-y-4">
              {% for reading in readings %}
                {% include 'mentions/includes/reading_card.html' with reading=reading show_actions=True %}
              {% endfor %}
            </div>

            <!-- Summary Statistics -->
            {% if readings.count > 1 %}
              <div class="mt-6 bg-gray-50 rounded-lg p-4">
                <div class="text-sm text-gray-600">
                  <strong>{{ readings.count }}</strong> total readings scheduled • <span class="text-green-600">{{ readings.completed_count|default:0 }} completed</span>
                  • <span class="text-blue-600">{{ readings.pending_count|default:readings.count }} pending</span>
                </div>
              </div>
            {% endif %}
          {% endif %}
        {% else %}
          <!-- No readings scheduled -->
          <div class="text-center py-8">
            <i class="fa-solid fa-calendar-plus text-4xl text-gray-300 mb-4"></i>
            <h4 class="text-lg font-medium text-gray-900 mb-2">No readings scheduled</h4>
            <p class="text-gray-600 mb-4">This mention hasn't been scheduled for any shows yet.</p>
            {% if can_schedule and mention.status == 'scheduled' %}
              <a href="{% url 'mentions:calendar' %}?mention={{ mention.pk }}" class="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md text-sm font-medium hover:bg-primary-700">
                <i class="fa-solid fa-calendar-plus mr-2"></i>
                Schedule First Reading
              </a>
            {% endif %}
          </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Rejection Modal -->
  <div id="rejectModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Reject Mention</h3>
        <textarea id="rejectReason" placeholder="Reason for rejection..." class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500" rows="4"></textarea>
        <div class="flex justify-end space-x-3 mt-4">
          <button onclick="closeRejectModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">Cancel</button>
          <button onclick="confirmReject()" class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700">Reject</button>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    let currentMentionId = null
    
    // Helper function to get cookie value
    function getCookie(name) {
      let cookieValue = null
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';')
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim()
          if (cookie.substring(0, name.length + 1) === name + '=') {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1))
            break
          }
        }
      }
      return cookieValue
    }
    
    function approveMention(mentionId) {
      if (confirm('Are you sure you want to approve this mention?')) {
        // Get CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || getCookie('csrftoken')
    
        fetch(`/mentions/${mentionId}/approve/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
          }
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              location.reload()
            } else {
              alert('Error approving mention: ' + (data.error || 'Unknown error'))
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('Network error occurred while approving mention')
          })
      }
    }
    
    function rejectMention(mentionId) {
      currentMentionId = mentionId
      document.getElementById('rejectModal').classList.remove('hidden')
    }
    
    function closeRejectModal() {
      document.getElementById('rejectModal').classList.add('hidden')
      document.getElementById('rejectReason').value = ''
      currentMentionId = null
    }
    
    function confirmReject() {
      const reason = document.getElementById('rejectReason').value
      if (!reason.trim()) {
        alert('Please provide a reason for rejection')
        return
      }
    
      // Get CSRF token
      const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || getCookie('csrftoken')
    
      fetch(`/mentions/${currentMentionId}/reject/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason: reason })
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            closeRejectModal()
            location.reload()
          } else {
            alert('Error rejecting mention: ' + (data.error || 'Unknown error'))
          }
        })
        .catch((error) => {
          console.error('Error:', error)
          alert('Network error occurred while rejecting mention')
        })
    }
    
    function deleteMention(mentionId) {
      if (confirm('Are you sure you want to delete this mention? This action cannot be undone.')) {
        window.location.href = `/mentions/${mentionId}/delete/`
      }
    }
    
    // Enhanced reading management functions
    function rescheduleReading(readingId) {
      window.location.href = `/mentions/calendar/?reading=${readingId}`
    }
    
    function reassignPresenter(readingId) {
      // This would open a modal or redirect to reassignment page
      // For now, redirect to calendar with reading selected
      window.location.href = `/mentions/calendar/?reading=${readingId}&action=reassign`
    }
    
    function deleteReading(readingId) {
      if (confirm('Are you sure you want to delete this scheduled reading?')) {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || getCookie('csrftoken')
    
        fetch(`/mentions/readings/${readingId}/delete/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
          }
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              location.reload()
            } else {
              alert('Error deleting reading: ' + (data.error || 'Unknown error'))
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('Network error occurred while deleting reading')
          })
      }
    }
    
    function markAsRead(readingId) {
      const notes = prompt('Add any notes about this reading (optional):')
      if (notes !== null) {
        // User didn't cancel
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || getCookie('csrftoken')
    
        fetch(`/mentions/readings/${readingId}/mark-read/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': csrfToken,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ notes: notes })
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              location.reload()
            } else {
              alert('Error marking reading as complete: ' + (data.error || 'Unknown error'))
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('Network error occurred while marking reading as complete')
          })
      }
    }
    
    function resolveConflict(readingId) {
      window.location.href = `/mentions/conflicts/?reading=${readingId}`
    }
    
    function showBulkActions() {
      // This would show a modal with bulk action options
      alert('Bulk actions feature coming soon!')
    }
  </script>
{% endblock %}
