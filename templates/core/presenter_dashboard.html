{% extends 'base.html' %}
{% load static %}
{% load dict_extras %}

{% block title %}
  {{ presenter.display_name }} Dashboard
{% endblock %}

{% block extra_css %}
  <style>
    .priority-urgent {
      @apply bg-red-100 text-red-800;
    }
    .priority-high {
      @apply bg-yellow-100 text-yellow-800;
    }
    .priority-normal {
      @apply bg-blue-100 text-blue-800;
    }
    .priority-low {
      @apply bg-gray-100 text-gray-800;
    }
    
    .dashboard-card {
      @apply bg-white rounded-lg shadow-sm border border-gray-200 transition-all duration-200 hover:shadow-md;
    }
    
    .quick-action-btn {
      @apply inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200;
    }
    
    .mention-card {
      @apply p-3 rounded-lg border transition-all duration-200 hover:shadow-sm;
    }
    
    .mention-card.completed {
      @apply bg-green-50 border-green-200;
    }
    
    .mention-card.upcoming {
      @apply bg-blue-50 border-blue-200;
    }
    
    .mention-card.next {
      @apply bg-yellow-50 border-yellow-300 ring-2 ring-yellow-200;
    }
    
    .stat-card {
      @apply dashboard-card p-4 hover:scale-105 transform transition-transform duration-200;
    }
  </style>
{% endblock %}

{% block content %}
  <meta name="csrf-token" content="{{ csrf_token }}" />
  {% csrf_token %}
  <div class="max-w-7xl mx-auto space-y-6">
    <!-- Header -->
    <div class="dashboard-card p-4">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div class="flex items-center">
          {% if presenter.profile_picture %}
            <img src="{{ presenter.profile_picture.url }}" alt="{{ presenter.display_name }}" class="w-10 h-10 rounded-full object-cover mr-3" />
          {% else %}
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-3">
              <i class="fa-solid fa-user text-white text-sm"></i>
            </div>
          {% endif %}
          <div>
            <h1 class="text-xl font-bold text-gray-900">{{ presenter.display_name }}</h1>
            <p class="text-sm text-gray-600 flex items-center">
              <i class="fa-solid fa-microphone mr-1 text-blue-500"></i>
              Presenter Dashboard
              <span class="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Online</span>
            </p>
          </div>
        </div>

        <div class="flex flex-wrap gap-2">
          <button onclick="refreshDashboard()" class="quick-action-btn bg-gray-600 text-white hover:bg-gray-700" title="Refresh"><i class="fa-solid fa-refresh"></i></button>
        </div>
      </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-clock text-orange-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.scheduled_today|default:0 }}</p>
            <p class="text-xs text-gray-500">Remaining Today</p>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-check-circle text-green-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.completion_rate|default:0 }}%</p>
            <p class="text-xs text-gray-500">Completion Rate</p>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-radio text-purple-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.shows_count|default:0 }}</p>
            <p class="text-xs text-gray-500">Active Shows</p>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-microphone text-yellow-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.total_readings|default:0 }}</p>
            <p class="text-xs text-gray-500">Total Readings</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="grid grid-cols-2 lg:grid-cols-3 gap-4">
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-tv text-emerald-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.completed_shows_today }}</p>
            <p class="text-xs text-gray-500">Shows Remaining Today</p>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-calendar-week text-indigo-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.completed_shows_week }}</p>
            <p class="text-xs text-gray-500">Shows Remaining This Week</p>
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-trophy text-orange-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.total_completed_shows }}</p>
            <p class="text-xs text-gray-500">Pending Shows</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
      <!-- Today's Schedule -->
      <div class="lg:col-span-3">
        <div class="dashboard-card">
          <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
              <i class="fa-solid fa-clock mr-2 text-blue-600"></i>
              Today's Schedule
            </h3>
            <div class="flex items-center space-x-2">
              <span class="text-xs text-gray-500" id="current-time"></span>
              <button onclick="refreshSchedule()" class="text-gray-400 hover:text-gray-600" title="Refresh"><i class="fa-solid fa-refresh text-sm"></i></button>
            </div>
          </div>
          <div class="p-4">
            {% if todays_schedule %}
              <div class="space-y-3" id="todays-schedule">
                {% for reading in todays_schedule %}
                  <div class="mention-card {% if reading.actual_read_time %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      completed



























                    {% elif forloop.first and not reading.actual_read_time %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      next



























                    {% else %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      upcoming



























                    {% endif %}"
                    data-reading-id="{{ reading.pk }}">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3 flex-1">
                        <!-- Time -->
                        <div class="text-center min-w-[60px]">
                          <div class="text-sm font-bold text-gray-900">{{ reading.scheduled_time|time:'H:i' }}</div>
                          <div class="text-xs text-gray-500">{{ reading.show.name|truncatechars:8 }}</div>
                        </div>

                        <!-- Content -->
                        <div class="flex-1 min-w-0">
                          <h4 class="text-sm font-medium text-gray-900 truncate">{{ reading.mention.title }}</h4>
                          <p class="text-xs text-gray-600">{{ reading.mention.client.name }}</p>
                          <div class="flex items-center mt-1 space-x-2">
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium priority-{% if reading.mention.priority == 4 %}
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                urgent



























                              {% elif reading.mention.priority == 3 %}
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                high



























                              {% elif reading.mention.priority == 2 %}
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                normal



























                              {% else %}
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                low



























                              {% endif %}">
                              {{ reading.mention.priority_display }}
                            </span>
                            <span class="text-xs text-gray-500">{{ reading.mention.duration_seconds }}s</span>
                          </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center space-x-2">
                          {% if reading.actual_read_time %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <i class="fa-solid fa-check mr-1"></i>
                              Done
                            </span>
                          {% else %}
                            <button onclick="markComplete({{ reading.pk }})" class="quick-action-btn bg-green-600 text-white hover:bg-green-700 text-xs px-2 py-1">
                              <i class="fa-solid fa-check mr-1"></i>
                              Mark Done
                            </button>
                            <button onclick="viewMention({{ reading.pk }}, '{{ reading.mention.title|escapejs }}', '{{ reading.mention.content|escapejs }}')" class="quick-action-btn bg-blue-600 text-white hover:bg-blue-700 text-xs px-2 py-1"><i class="fa-solid fa-eye"></i></button>
                          {% endif %}
                          <!-- Show Notes Button (always visible) -->
                          <button onclick="openShowNotes({{ reading.show.pk }}, '{{ reading.scheduled_date|date:'Y-m-d' }}', '{{ reading.show.name|escapejs }}')" class="quick-action-btn bg-purple-600 text-white hover:bg-purple-700 text-xs px-2 py-1 relative" title="Show Notes">
                            <i class="fa-solid fa-note-sticky"></i>
                            {% with note_key=reading.show.pk|add:'_'|add:reading.scheduled_date|date:'Y-m-d' %}
                              {% if show_note_counts|get_item:note_key %}
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">{{ show_note_counts|get_item:note_key }}</span>
                              {% endif %}
                            {% endwith %}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                {% endfor %}
              </div>
            {% else %}
              <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i class="fa-solid fa-calendar-check text-2xl text-gray-400"></i>
                </div>
                <h4 class="text-lg font-medium text-gray-900 mb-2">No mentions scheduled today</h4>
                <p class="text-gray-600">Enjoy your day off!</p>
              </div>
            {% endif %}
          </div>
        </div>

        <!-- Completed Shows -->
        <div class="dashboard-card mt-6">
          <div class="px-4 py-3 border-b border-gray-200 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
              <i class="fa-solid fa-check-circle mr-2 text-green-600"></i>
              Recently Completed Shows
            </h3>
            <div class="flex items-center space-x-2">
              <span class="text-xs text-gray-500">Last 30 days</span>
              <button onclick="refreshCompletedShows()" class="text-gray-400 hover:text-gray-600" title="Refresh"><i class="fa-solid fa-refresh text-sm"></i></button>
            </div>
          </div>
          <div class="p-4">
            {% if recent_completed_shows %}
              <div class="space-y-3" id="completed-shows">
                {% for session in recent_completed_shows %}
                  <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center space-x-3 flex-1">
                      <!-- Date -->
                      <div class="text-center min-w-[80px]">
                        <div class="text-sm font-bold text-gray-900">{{ session.date|date:'M d' }}</div>
                        <div class="text-xs text-gray-500">{{ session.date|date:'D' }}</div>
                      </div>

                      <!-- Show Info -->
                      <div class="flex-1 min-w-0">
                        <h4 class="text-sm font-medium text-gray-900">{{ session.show.name }}</h4>
                        <p class="text-xs text-gray-600">
                          {{ session.actual_start_time|time:'H:i' }} - {{ session.actual_end_time|time:'H:i' }}
                          {% if session.duration_minutes %}
                            ({{ session.duration_minutes }} min)
                          {% endif %}
                        </p>
                        <div class="flex items-center mt-1 space-x-2">
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">{{ session.get_end_reason_display }}</span>
                          {% if session.ended_early %}
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                              <i class="fa-solid fa-clock mr-1"></i>
                              Early
                            </span>
                          {% endif %}
                          {% if session.notes %}
                            <button onclick="showSessionNotes('{{ session.show.name|escapejs }}', '{{ session.notes|escapejs }}')" class="text-gray-400 hover:text-gray-600" title="View notes"><i class="fa-solid fa-sticky-note text-xs"></i></button>
                          {% endif %}
                        </div>
                      </div>

                      <!-- Status -->
                      <div class="flex items-center">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <i class="fa-solid fa-check mr-1"></i>
                          Completed
                        </span>
                      </div>
                    </div>
                  </div>
                {% endfor %}
              </div>
            {% else %}
              <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i class="fa-solid fa-tv text-2xl text-gray-400"></i>
                </div>
                <h4 class="text-lg font-medium text-gray-900 mb-2">No completed shows yet</h4>
                <p class="text-gray-600">Your completed shows will appear here once you finish hosting shows.</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-4">
        <!-- Quick Stats -->
        <div class="dashboard-card p-4">
          <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
            <i class="fa-solid fa-chart-simple mr-2 text-green-600"></i>
            Quick Stats
          </h3>
          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-600">Completed Today</span>
              <span class="text-sm font-bold text-green-600">{{ stats.completed_today|default:0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-600">Remaining Today</span>
              <span class="text-sm font-bold text-blue-600">{{ stats.remaining_today|default:0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-600">Remaining This Week</span>
              <span class="text-sm font-bold text-purple-600">{{ stats.week_total|default:0 }}</span>
            </div>
            <hr class="my-2 border-gray-200" />
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-600">Shows Remaining Today</span>
              <span class="text-sm font-bold text-emerald-600">{{ stats.completed_shows_today|default:0 }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-xs text-gray-600">Shows Remaining This Week</span>
              <span class="text-sm font-bold text-indigo-600">{{ stats.completed_shows_week|default:0 }}</span>
            </div>
          </div>
        </div>

        <!-- Upcoming Mentions -->
        <div class="dashboard-card p-4">
          <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
            <i class="fa-solid fa-calendar-plus mr-2 text-orange-600"></i>
            Upcoming This Week
          </h3>
          {% if upcoming_schedule %}
            <div class="space-y-2 max-h-48 overflow-y-auto">
              {% for reading in upcoming_schedule %}
                <div class="p-2 bg-blue-50 rounded-lg border border-blue-200">
                  <div class="text-xs font-medium text-gray-900">{{ reading.mention.title|truncatechars:25 }}</div>
                  <div class="text-xs text-gray-600">{{ reading.show.name }}</div>
                  <div class="text-xs text-blue-600 mt-1">{{ reading.scheduled_date|date:'M d' }} at {{ reading.scheduled_time|time:'H:i' }}</div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <p class="text-xs text-gray-500 text-center py-4">No upcoming mentions</p>
          {% endif %}
        </div>

        <!-- My Notes -->
        <div class="dashboard-card p-4">
          <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
            <i class="fa-solid fa-note-sticky mr-2 text-purple-600"></i>
            My Notes
          </h3>
          <div class="space-y-2">
            <a href="{% url 'core:personal_notes_list' %}" class="block w-full text-center quick-action-btn bg-purple-600 text-white hover:bg-purple-700 text-xs">
              <i class="fa-solid fa-list mr-2"></i>
              View All Notes
            </a>
            <a href="{% url 'core:quick_note_create' %}" class="block w-full text-center quick-action-btn bg-green-600 text-white hover:bg-green-700 text-xs">
              <i class="fa-solid fa-bolt mr-2"></i>
              Quick Note
            </a>
            <a href="{% url 'core:personal_note_create' %}" class="block w-full text-center quick-action-btn bg-blue-600 text-white hover:bg-blue-700 text-xs">
              <i class="fa-solid fa-plus mr-2"></i>
              New Note
            </a>
          </div>
        </div>

        <!-- My Shows -->
        <div class="dashboard-card p-4">
          <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
            <i class="fa-solid fa-radio mr-2 text-purple-600"></i>
            My Shows
          </h3>
          {% if presenter_shows %}
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3">
              {% for show_presenter in presenter_shows %}
                <div class="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <div class="text-sm font-medium text-gray-900 mb-1">{{ show_presenter.show.name }}</div>
                  <div class="text-xs text-gray-600 mb-2">{{ show_presenter.show.start_time|time:'H:i' }} - {{ show_presenter.show.end_time|time:'H:i' }}</div>
                  <div class="text-xs text-gray-500 mb-2">
                    {% if show_presenter.show.days_of_week %}
                      {% for day in show_presenter.show.days_of_week %}
                        {% if day == 0 %}
                          Mon
                        {% elif day == 1 %}
                          Tue
                        {% elif day == 2 %}
                          Wed
                        {% elif day == 3 %}
                          Thu
                        {% elif day == 4 %}
                          Fri
                        {% elif day == 5 %}
                          Sat
                        {% elif day == 6 %}
                          Sun
                        {% endif %}
                        {% if not forloop.last %}, {% endif %}
                      {% endfor %}
                    {% endif %}
                  </div>
                  <div class="flex items-center justify-start">
                    {% if show_presenter.is_primary %}
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">Primary</span>
                    {% else %}
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">Secondary</span>
                    {% endif %}
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <p class="text-xs text-gray-500 text-center py-4">No shows assigned</p>
          {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="dashboard-card p-4">
          <h3 class="text-sm font-semibold text-gray-900 mb-3 flex items-center">
            <i class="fa-solid fa-bolt mr-2 text-yellow-600"></i>
            Quick Actions
          </h3>
          <div class="space-y-2">
            <a href="{% url 'account_change_password' %}" class="block w-full text-center quick-action-btn bg-blue-600 text-white hover:bg-blue-700 text-xs">
              <i class="fa-solid fa-key mr-2"></i>
              Change Password
            </a>
            <button onclick="showHelp()" class="block w-full text-center quick-action-btn bg-gray-600 text-white hover:bg-gray-700 text-xs">
              <i class="fa-solid fa-question-circle mr-2"></i>
              Help & Tips
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Mention Detail Modal -->
  <div id="mentionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">Mention Details</h3>
        <button onclick="closeMentionModal()" class="text-gray-400 hover:text-gray-600"><i class="fa-solid fa-times text-xl"></i></button>
      </div>
      <div class="mt-4">
        <div class="bg-gray-50 rounded-lg p-4">
          <div id="modalContent" class="text-gray-900 leading-relaxed whitespace-pre-wrap"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Show Notes Modal -->
  {% include 'core/show_notes_modal.html' %}
{% endblock %}

{% block extra_js %}
  <script src="{% load static %}{% static 'js/show_notes.js' %}"></script>
  <script>
    // Update current time
    function updateCurrentTime() {
      const now = new Date()
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      })
      const timeElement = document.getElementById('current-time')
      if (timeElement) {
        timeElement.textContent = timeString
      }
    }
    
    // Refresh dashboard
    function refreshDashboard() {
      location.reload()
    }
    
    // Refresh schedule
    function refreshSchedule() {
      location.reload()
    }
    
    // Refresh completed shows
    function refreshCompletedShows() {
      location.reload()
    }
    
    // Mark mention as complete
    function markComplete(readingId) {
      if (confirm('Mark this mention as completed?')) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || document.querySelector('[name=csrfmiddlewaretoken]')?.value
    
        if (!csrfToken) {
          alert('CSRF token not found. Please refresh the page and try again.')
          return
        }
    
        // Show loading state
        const mentionCard = document.querySelector(`[data-reading-id="${readingId}"]`)
        const actionButtons = mentionCard?.querySelector('.flex.items-center.space-x-2:last-child')
        if (actionButtons) {
          actionButtons.innerHTML = `
                                                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                                                              <i class="fa-solid fa-spinner fa-spin mr-1"></i>
                                                                                              Processing...
                                                                                            </span>
                                                                                          `
        }
    
        fetch(`/mentions/readings/${readingId}/mark-read/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
          },
          body: JSON.stringify({
            notes: ''
          })
        })
          .then((response) => {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }
            return response.json()
          })
          .then((data) => {
            if (data.success) {
              // Update the UI immediately
              if (mentionCard) {
                mentionCard.className = 'mention-card completed'
                if (actionButtons) {
                  actionButtons.innerHTML = `
                                                                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                                                      <i class="fa-solid fa-check mr-1"></i>
                                                                                                      Done
                                                                                                    </span>
                                                                                                  `
                }
              }
              showNotification('Mention marked as complete!', 'success')
    
              // Update statistics
              setTimeout(() => {
                location.reload()
              }, 1500)
            } else {
              // Handle validation errors
              let errorMessage = 'Unknown error occurred'
              if (data.error) {
                if (typeof data.error === 'object') {
                  // Handle Django form validation errors
                  errorMessage = Object.values(data.error).flat().join(', ')
                } else {
                  errorMessage = data.error
                }
              }
              showNotification('Error: ' + errorMessage, 'error')
    
              // Restore original button
              if (actionButtons) {
                actionButtons.innerHTML =
                  `
                                                                                                  <button onclick="markComplete(` +
                  readingId +
                  `)" class="quick-action-btn bg-green-600 text-white hover:bg-green-700 text-xs px-2 py-1">
                                                                                                    <i class="fa-solid fa-check mr-1"></i>
                                                                                                    Mark Done
                                                                                                  </button>
                                                                                                `
              }
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            showNotification('Network error: ' + error.message, 'error')
    
            // Restore original button
            if (actionButtons) {
              actionButtons.innerHTML =
                `
                                                                                                <button onclick="markComplete(` +
                readingId +
                `)" class="quick-action-btn bg-green-600 text-white hover:bg-green-700 text-xs px-2 py-1">
                                                                                                  <i class="fa-solid fa-check mr-1"></i>
                                                                                                  Mark Done
                                                                                                </button>
                                                                                              `
            }
          })
      }
    }
    
    // View mention details
    function viewMention(readingId, title, content) {
      document.getElementById('modalTitle').textContent = title
      document.getElementById('modalContent').textContent = content
      document.getElementById('mentionModal').classList.remove('hidden')
      document.body.style.overflow = 'hidden'
    }
    
    // Show session notes
    function showSessionNotes(showName, notes) {
      document.getElementById('modalTitle').textContent = showName + ' - Session Notes'
      document.getElementById('modalContent').textContent = notes
      document.getElementById('mentionModal').classList.remove('hidden')
      document.body.style.overflow = 'hidden'
    }
    
    // Close mention modal
    function closeMentionModal() {
      document.getElementById('mentionModal').classList.add('hidden')
      document.body.style.overflow = 'auto'
    }
    
    // Show help
    function showHelp() {
      alert(`Presenter Dashboard Help:
                                                                                                                
                                                                                                                • Use "Mark Done" to complete mentions
                                                                                                                • Click the eye icon to view full mention content
                                                                                                                • Your completion rate is calculated automatically
                                                                                                                • Use the calendar to view your full schedule
                                                                                                                
                                                                                                                Keyboard shortcuts in Live Show:
                                                                                                                • Space: Mark current mention as read
                                                                                                                • Arrow keys: Navigate between mentions
                                                                                                                • Enter: View mention details`)
    }
    
    // Show notification
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div')
      let bgClass = 'bg-blue-100 text-blue-800 border border-blue-200'
      let iconClass = 'info'
    
      if (type === 'success') {
        bgClass = 'bg-green-100 text-green-800 border border-green-200'
        iconClass = 'check'
      } else if (type === 'error') {
        bgClass = 'bg-red-100 text-red-800 border border-red-200'
        iconClass = 'exclamation-triangle'
      }
    
      notification.className = 'fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ' + bgClass
      notification.innerHTML = '<div class="flex items-center">' + '<i class="fa-solid fa-' + iconClass + ' mr-2"></i>' + '<span>' + message + '</span>' + '<button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-500 hover:text-gray-700">' + '<i class="fa-solid fa-times"></i>' + '</button>' + '</div>'
    
      document.body.appendChild(notification)
    
      // Auto-remove after 5 seconds
      setTimeout(function () {
        if (notification.parentElement) {
          notification.remove()
        }
      }, 5000)
    }
    
    // Close modal when clicking outside
    document.getElementById('mentionModal').addEventListener('click', function (e) {
      if (e.target === this) {
        closeMentionModal()
      }
    })
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function () {
      updateCurrentTime()
      setInterval(updateCurrentTime, 1000) // Update every second
    
      // Add keyboard shortcuts
      document.addEventListener('keydown', function (e) {
        // Escape key to close modal
        if (e.key === 'Escape') {
          closeMentionModal()
        }
    
        // Ctrl+R to refresh (prevent default and use our refresh)
        if (e.ctrlKey && e.key === 'r') {
          e.preventDefault()
          refreshDashboard()
        }
      })
    })
  </script>
{% endblock %}
