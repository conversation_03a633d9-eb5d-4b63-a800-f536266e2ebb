{% extends 'base.html' %}
{% load static %}

{% block title %}
  Live Show - {{ presenter.display_name }}
{% endblock %}

{% block extra_css %}
  <style>
    .live-indicator {
      animation: pulse-red 2s infinite;
    }
    
    @keyframes pulse-red {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
    
    .digital-clock {
      font-family: 'Courier New', monospace;
      text-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
    }
    
    /* Chat-style mention cards */
    .mention-card {
      @apply transition-all duration-300 hover:shadow-lg transform hover:scale-102;
      max-width: 85%;
      position: relative;
    }
    
    .mention-card.completed {
      @apply bg-green-50 border-green-200;
    }
    
    .mention-card.next {
      @apply bg-red-50 border-red-300 ring-2 ring-red-200;
      animation: pulse-glow 2s infinite;
    }
    
    .mention-card.upcoming {
      @apply bg-blue-50 border-blue-200;
    }
    
    /* Chat-style alignment based on industry */
    .mention-left {
      @apply ml-0 mr-auto rounded-r-2xl rounded-tl-2xl rounded-bl-md;
      background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
      border-left: 4px solid #0288d1;
    }
    
    .mention-right {
      @apply ml-auto mr-0 rounded-l-2xl rounded-tr-2xl rounded-br-md;
      background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
      border-right: 4px solid #8e24aa;
    }
    
    .mention-center {
      @apply mx-auto rounded-2xl;
      background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
      border: 2px solid #ff9800;
    }
    
    /* Industry-specific colors */
    .industry-technology { border-color: #2196f3 !important; }
    .industry-healthcare { border-color: #4caf50 !important; }
    .industry-finance { border-color: #ff9800 !important; }
    .industry-entertainment { border-color: #e91e63 !important; }
    .industry-government { border-color: #607d8b !important; }
    .industry-retail { border-color: #9c27b0 !important; }
    .industry-automotive { border-color: #f44336 !important; }
    .industry-education { border-color: #3f51b5 !important; }
    
    @keyframes pulse-glow {
      0%, 100% { box-shadow: 0 0 5px rgba(239, 68, 68, 0.3); }
      50% { box-shadow: 0 0 20px rgba(239, 68, 68, 0.6), 0 0 30px rgba(239, 68, 68, 0.4); }
    }
    
    .quick-action-btn {
      @apply inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 shadow-sm border border-transparent focus:outline-none focus:ring-2 focus:ring-offset-2;
    }
    
    /* Enhanced Drag and Drop Styles */
    .draggable-mention {
      @apply transition-all duration-300 cursor-grab;
      position: relative;
    }

    .draggable-mention:active {
      @apply cursor-grabbing;
    }

    .draggable-mention:hover {
      @apply shadow-xl transform scale-105;
      z-index: 10;
    }

    .draggable-mention.drag-over {
      @apply border-blue-400 bg-blue-50 transform scale-105;
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    }

    .draggable-mention.dragging {
      @apply opacity-50 transform rotate-2 scale-105;
      z-index: 1000;
    }

    .drag-handle {
      @apply transition-all duration-200 p-2 rounded-lg;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(4px);
    }

    .drag-handle:hover {
      @apply text-blue-600 bg-blue-50;
      transform: scale(1.1);
    }

    /* Drop zones */
    .drop-zone {
      @apply border-2 border-dashed border-gray-300 rounded-lg p-4 transition-all duration-200;
      min-height: 60px;
    }

    .drop-zone.active {
      @apply border-blue-500 bg-blue-50;
      animation: pulse-border 1s infinite;
    }

    @keyframes pulse-border {
      0%, 100% { border-color: #3b82f6; }
      50% { border-color: #60a5fa; }
    }

    /* Industry category indicators */
    .industry-indicator {
      @apply absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg;
    }

    /* Mobile optimizations */
    @media (max-width: 640px) {
      .digital-clock {
        font-size: 2.5rem !important;
      }

      .mention-card {
        @apply p-3;
        max-width: 95%;
      }

      .quick-action-btn {
        @apply px-3 py-2 text-xs;
      }

      .draggable-mention:hover {
        @apply transform-none scale-100;
      }

      .mention-left, .mention-right {
        max-width: 90%;
      }
    }

    /* Animation for new mentions */
    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .mention-card.new {
      animation: slideIn 0.5s ease-out;
    }
    
    .stat-card {
      @apply bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow duration-200;
    }
    
    .modal-enter {
      animation: modalEnter 0.3s ease-out;
    }
    
    @keyframes modalEnter {
      from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }
  </style>
{% endblock %}

{% block content %}
  {% csrf_token %}
  <div class="max-w-7xl mx-auto space-y-6">
    <!-- Live Header -->
    <div class="bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg p-6 shadow-lg">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
            <i class="fa-solid fa-microphone text-white text-xl"></i>
          </div>
          <div>
            <h1 class="text-2xl font-bold">Live Show Dashboard</h1>
            <p class="text-red-100 flex items-center">
              {% if show_ended_early %}
                <span class="text-red-200 mr-2">●</span>
                <span>SHOW ENDED</span>
                {% if current_show %}
                  <span class="mx-2">•</span>
                  <span>{{ current_show.name }}</span>
                {% endif %}
              {% elif is_live %}
                <span class="live-indicator text-red-200 mr-2">●</span>
                <span>LIVE</span>
                {% if current_show %}
                  <span class="mx-2">•</span>
                  <span>{{ current_show.name }}</span>
                {% endif %}
              {% else %}
                <span class="text-red-200 mr-2">●</span>
                <span>OFF AIR</span>
              {% endif %}
            </p>
          </div>
        </div>
        
        <div class="flex flex-col sm:flex-row items-center gap-4">
          <!-- Digital Clock -->
          <div class="text-center">
            <div class="digital-clock text-4xl font-mono font-bold" id="current-time">--:--:--</div>
            <div class="text-sm text-red-100" id="current-date">Loading...</div>
          </div>
          
          <!-- Action Buttons -->
          <div class="flex gap-2">
            <button onclick="refreshLiveShow()" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
              <i class="fa-solid fa-refresh mr-2"></i>
              Refresh
            </button>
            {% if is_live and not show_ended_early %}
              <button onclick="endShow()" class="bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center">
                <i class="fa-solid fa-stop mr-2"></i>
                End Show
              </button>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <!-- Show Status -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-broadcast-tower text-red-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">
              {% if is_live and not show_ended_early %}
                ON AIR
              {% elif show_ended_early %}
                ENDED
              {% else %}
                OFF AIR
              {% endif %}
            </p>
            <p class="text-xs text-gray-500">Status</p>
          </div>
        </div>
      </div>

      <!-- Show Duration -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-clock text-blue-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">
              {% if current_show %}
                {{ show_duration_hours }}h
              {% else %}
                --
              {% endif %}
            </p>
            <p class="text-xs text-gray-500">Duration</p>
          </div>
        </div>
      </div>

      <!-- Time Remaining -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-hourglass-half text-orange-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900" id="time-remaining">
              {% if current_show %}
                --:--
              {% else %}
                --
              {% endif %}
            </p>
            <p class="text-xs text-gray-500">Time Left</p>
          </div>
        </div>
      </div>

      <!-- Mentions Progress -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-clock text-orange-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ mentions_remaining }}</p>
            <p class="text-xs text-gray-500">Remaining</p>
          </div>
        </div>
      </div>
    </div>
