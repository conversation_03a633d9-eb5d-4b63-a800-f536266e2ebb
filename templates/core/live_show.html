{% extends 'base.html' %} {% load static %} {% load dict_extras %} {% block title %}
  Live Show - {{ presenter.display_name }}
{% endblock %} {% block extra_css %}
  <style>
    .live-indicator {
      animation: pulse-red 2s infinite;
    }
    
    @keyframes pulse-red {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }
    
    .digital-clock {
      font-family: 'Courier New', monospace;
      text-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
    }
    
    /* Clean mention cards with alternating alignment */
    .mention-card {
      @apply transition-all duration-200 hover:shadow-md;
      width: 100%;
      max-width: none;
      position: relative;
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      min-height: auto;
      overflow: visible;
    }
    
    .mention-card.completed {
      @apply bg-green-50 border-green-200;
    }
    
    .mention-card.next {
      @apply bg-red-50 border-red-300 ring-2 ring-red-200;
      animation: pulse-glow 2s infinite;
    }
    
    .mention-card.upcoming {
      @apply bg-white border-gray-200;
    }
    
    /* Clean alternating alignment - no chat bubbles */
    .mention-left {
      margin-left: 0;
      margin-right: 15%;
      border-left: 4px solid #3b82f6;
    }
    
    .mention-right {
      margin-left: 15%;
      margin-right: 0;
      border-right: 4px solid #8b5cf6;
    }
    
    /* Dynamic industry colors will be applied inline */
    
    @keyframes pulse-glow {
      0%,
      100% {
        box-shadow: 0 0 5px rgba(239, 68, 68, 0.3);
      }
      50% {
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.6), 0 0 30px rgba(239, 68, 68, 0.4);
      }
    }
    
    .quick-action-btn {
      @apply inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 shadow-sm border border-transparent focus:outline-none focus:ring-2 focus:ring-offset-2;
    }
    
    .quick-action-btn:hover {
      @apply transform -translate-y-0.5 shadow-md;
    }
    
    .quick-action-btn:active {
      @apply transform translate-y-0 shadow-sm;
    }
    
    /* Button variants */
    .btn-primary {
      @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-300;
    }
    
    .btn-success {
      @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-300;
    }
    
    .btn-danger {
      @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-300;
    }
    
    .btn-warning {
      @apply bg-amber-600 text-white hover:bg-amber-700 focus:ring-amber-300;
    }
    
    .btn-secondary {
      @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-300;
    }
    
    .btn-outline {
      @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-gray-300;
    }
    
    .stat-card {
      @apply bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow duration-200;
    }
    
    .modal-enter {
      animation: modalEnter 0.3s ease-out;
    }
    
    @keyframes modalEnter {
      from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }
    
    /* Enhanced Drag and Drop Styles */
    .draggable-mention {
      @apply transition-all duration-300 cursor-grab;
      position: relative;
    }
    
    .draggable-mention:active {
      @apply cursor-grabbing;
    }
    
    .draggable-mention:hover {
      @apply shadow-xl transform scale-105;
      z-index: 10;
    }
    
    .draggable-mention.drag-over {
      @apply border-blue-400 bg-blue-50 transform scale-105;
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    }
    
    .draggable-mention.dragging {
      @apply opacity-50 transform rotate-2 scale-105;
      z-index: 1000;
    }
    
    .drag-handle {
      @apply transition-all duration-200 p-2 rounded-lg;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(4px);
    }
    
    .drag-handle:hover {
      @apply text-blue-600 bg-blue-50;
      transform: scale(1.1);
    }
    
    /* Drop zones */
    .drop-zone {
      @apply border-2 border-dashed border-gray-300 rounded-lg p-4 transition-all duration-200;
      min-height: 60px;
    }
    
    .drop-zone.active {
      @apply border-blue-500 bg-blue-50;
      animation: pulse-border 1s infinite;
    }
    
    @keyframes pulse-border {
      0%,
      100% {
        border-color: #3b82f6;
      }
      50% {
        border-color: #60a5fa;
      }
    }
    
    /* Industry category indicators */
    .industry-indicator {
      @apply absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg;
    }
    
    /* Mobile optimizations */
    @media (max-width: 640px) {
      .digital-clock {
        font-size: 2.5rem !important;
      }
    
      .mention-card {
        @apply p-3;
      }
    
      .quick-action-btn {
        @apply px-3 py-2 text-xs;
      }
    
      .draggable-mention:hover {
        @apply transform-none scale-100;
      }
    
      .mention-left {
        margin-right: 5%;
      }
    
      .mention-right {
        margin-left: 5%;
      }
    }
    
    /* Animation for new mentions */
    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .mention-card.new {
      animation: slideIn 0.5s ease-out;
    }
  </style>
{% endblock %} {% block content %}
  {% csrf_token %}
  <div class="max-w-7xl mx-auto space-y-6">
    <!-- Live Header -->
    <div class="bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg p-6 shadow-lg">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
            <i class="fa-solid fa-microphone text-white text-xl"></i>
          </div>
          <div>
            <h1 class="text-2xl font-bold">Live Show Dashboard</h1>
            <p class="text-red-100 flex items-center">
              {% if show_ended_early %}
                <span class="text-red-200 mr-2">●</span>
                <span>SHOW ENDED</span>
                {% if current_show %}
                  <span class="mx-2">•</span>
                  <span>{{ current_show.name }}</span>
                {% endif %}
              {% elif is_live %}
                <span class="live-indicator mr-2">●</span>
                <span>ON AIR</span>
                {% if current_show %}
                  <span class="mx-2">•</span>
                  <span>{{ current_show.name }}</span>
                {% endif %}
              {% else %}
                <i class="fa-solid fa-pause mr-2"></i>
                <span>OFF AIR</span>
              {% endif %}
            </p>
          </div>
        </div>

        <!-- Live Actions -->
        <div class="flex flex-wrap gap-3">
          {% if current_show and not show_ended_early %}
            <button class="quick-action-btn bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 focus:ring-white focus:ring-opacity-50 backdrop-blur-sm">
              <i class="fa-solid fa-pause mr-2"></i>
              Break
            </button>
            <button onclick="endShow()" class="quick-action-btn bg-red-500 bg-opacity-90 text-white hover:bg-red-600 hover:bg-opacity-100 focus:ring-red-300 backdrop-blur-sm">
              <i class="fa-solid fa-stop mr-2"></i>
              End Show
            </button>
          {% elif show_ended_early %}
            <div class="quick-action-btn bg-gray-500 bg-opacity-50 text-white cursor-not-allowed">
              <i class="fa-solid fa-check mr-2"></i>
              Show Ended
            </div>
          {% endif %}
          <button onclick="refreshLiveShow()" class="quick-action-btn bg-white bg-opacity-20 text-white hover:bg-white hover:bg-opacity-30 focus:ring-white focus:ring-opacity-50 backdrop-blur-sm" title="Refresh"><i class="fa-solid fa-refresh"></i></button>
        </div>
      </div>
    </div>

    <!-- Live Stats Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
      <!-- Current Time -->
      <div class="stat-card text-center lg:col-span-2">
        <div class="digital-clock text-4xl font-bold text-red-600 mb-2" id="current-time">--:--:--</div>
        <div class="text-sm text-gray-600" id="current-date">Loading...</div>
      </div>

      <!-- Show Duration -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-clock text-blue-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">
              {% if current_show %}
                {{ show_duration_hours|floatformat:1 }}h
              {% else %}
                --
              {% endif %}
            </p>
            <p class="text-xs text-gray-500">Show Duration</p>
          </div>
        </div>
      </div>

      <!-- Time Remaining -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-hourglass-half text-orange-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900" id="time-remaining">
              {% if current_show %}
                --:--
              {% else %}
                --
              {% endif %}
            </p>
            <p class="text-xs text-gray-500">Time Left</p>
          </div>
        </div>
      </div>

      <!-- Mentions Progress -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-clock text-orange-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ mentions_remaining }}</p>
            <p class="text-xs text-gray-500">Remaining</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Weather Widget (separate row) -->
    {% if weather_data %}
      <div class="stat-card" id="weather-widget">
        <!-- Weather Location Selector -->
        <div class="flex items-center justify-between mb-3 pb-3 border-b border-gray-200">
          <h3 class="text-sm font-semibold text-gray-700">Weather Information</h3>
          <div class="flex items-center space-x-2">
            <div class="relative">
              <input type="text" id="weather-location-input" placeholder="Enter city, country (e.g., London, UK)" class="text-xs px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500" style="width: 200px" list="popular-cities" />
              <datalist id="popular-cities">
                <option value="Nairobi, KE">Nairobi, Kenya</option>
                <option value="Kampala, UG">Kampala, Uganda</option>
                <option value="Dar es Salaam, TZ">Dar es Salaam, Tanzania</option>
                <option value="Kigali, RW">Kigali, Rwanda</option>
                <option value="Addis Ababa, ET">Addis Ababa, Ethiopia</option>
                <option value="Mombasa, KE">Mombasa, Kenya</option>
                <option value="Kisumu, KE">Kisumu, Kenya</option>
                <option value="London, UK">London, United Kingdom</option>
                <option value="New York, US">New York, United States</option>
                <option value="Paris, FR">Paris, France</option>
                <option value="Tokyo, JP">Tokyo, Japan</option>
                <option value="Sydney, AU">Sydney, Australia</option>
                <option value="Dubai, AE">Dubai, UAE</option>
                <option value="Mumbai, IN">Mumbai, India</option>
                <option value="Lagos, NG">Lagos, Nigeria</option>
                <option value="Cairo, EG">Cairo, Egypt</option>
                <option value="Cape Town, ZA">Cape Town, South Africa</option>
              </datalist>
            </div>
            <button id="update-weather-btn" class="text-xs px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500">Update</button>
          </div>
        </div>

        <!-- Weather Display -->
        <div id="weather-display">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-3 bg-blue-50">
                <img src="https://openweathermap.org/img/w/{{ weather_data.icon }}.png" alt="{{ weather_data.description }}" class="w-10 h-10" />
              </div>
              <div>
                <p class="text-xl font-bold text-gray-900">{{ weather_data.temperature }}°C</p>
                <p class="text-sm text-gray-600 capitalize">{{ weather_data.description }}</p>
                <p class="text-xs text-gray-500">
                  {{ weather_data.location }}{% if weather_data.country %}
                    , {{ weather_data.country }}
                  {% endif %}
                </p>
              </div>
            </div>
            <div class="text-right">
              <div class="grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-gray-600">
                <div class="text-right">
                  <span class="text-gray-500">Feels like:</span>
                  <span class="font-medium">{{ weather_data.feels_like }}°C</span>
                </div>
                <div class="text-right">
                  <span class="text-gray-500">Humidity:</span>
                  <span class="font-medium">{{ weather_data.humidity }}%</span>
                </div>
                {% if weather_data.pressure %}
                  <div class="text-right">
                    <span class="text-gray-500">Pressure:</span>
                    <span class="font-medium">{{ weather_data.pressure }} hPa</span>
                  </div>
                {% endif %} {% if weather_data.wind_speed %}
                  <div class="text-right">
                    <span class="text-gray-500">Wind:</span>
                    <span class="font-medium">{{ weather_data.wind_speed }} m/s</span>
                  </div>
                {% endif %} {% if weather_data.visibility %}
                  <div class="text-right">
                    <span class="text-gray-500">Visibility:</span>
                    <span class="font-medium">{{ weather_data.visibility }} km</span>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>

        <!-- Loading indicator -->
        <div id="weather-loading" class="hidden">
          <div class="flex items-center justify-center py-4">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-sm text-gray-600">Loading weather data...</span>
          </div>
        </div>

        <!-- Error display -->
        <div id="weather-error" class="hidden">
          <div class="flex items-center justify-center py-4 text-red-600">
            <i class="fa-solid fa-exclamation-triangle mr-2"></i>
            <span class="text-sm" id="weather-error-message">Error loading weather data</span>
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Quick Actions -->
    {% comment %}
    <div class="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
        <div class="flex flex-wrap gap-3">
          <button class="quick-action-btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-300 shadow-green-200">
            <i class="fa-solid fa-play mr-2"></i>
            Jingle
          </button>
          <button class="quick-action-btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-300 shadow-blue-200">
            <i class="fa-solid fa-music mr-2"></i>
            Music
          </button>
          <button class="quick-action-btn bg-amber-600 text-white hover:bg-amber-700 focus:ring-amber-300 shadow-amber-200">
            <i class="fa-solid fa-phone mr-2"></i>
            Calls
          </button>
          <button class="quick-action-btn bg-purple-600 text-white hover:bg-purple-700 focus:ring-purple-300 shadow-purple-200">
            <i class="fa-solid fa-bullhorn mr-2"></i>
            Live Ad
          </button>
        </div>
      </div>
    </div>
    {% endcomment %}

    <!-- Today's Mentions -->
    <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
          <i class="fa-solid fa-list mr-2 text-blue-600"></i>
          Today's Mentions
        </h3>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">{{ mentions_remaining }} remaining</span>
          <div class="bg-gray-100 rounded-lg px-3 py-1">
            <div class="flex items-center space-x-2 text-xs">
              <kbd class="px-2 py-1 bg-white border border-gray-300 rounded">Space</kbd>
              <span class="text-gray-600">Mark Read</span>
              <kbd class="px-2 py-1 bg-white border border-gray-300 rounded ml-2">Enter</kbd>
              <span class="text-gray-600">View</span>
            </div>
          </div>
        </div>
      </div>

      <div class="p-6">
        <!-- Debug: Show industry colors -->
        {% if industry_colors %}
          <div class="mb-4 p-2 bg-gray-100 rounded text-xs">
            <strong>Debug - Industry Colors:</strong>
            {% for code, color in industry_colors.items %}
              <span class="inline-block mr-2">{{ code }}: {{ color }}</span>
            {% endfor %}
          </div>
        {% endif %}

        {% if todays_mentions %}
          <div class="space-y-4" id="mentions-list">
            {% for reading in todays_mentions %}
              {% comment %}Mention alignment based on organization settings{% endcomment %} {% comment %}Mention alignment based on organization settings{% endcomment %} {% with industry=reading.mention.client.industry|default:'other' %}
                <div class="mention-wrapper flex {% if alignment_mode == 'alternating' and forloop.counter0|divisibleby:2 %}
                    
                    
                    
                    
                    
                    
                    
                     justify-end







                  {% elif alignment_mode == 'alternating' %}
                    
                    
                    
                    
                    
                    
                    
                     justify-start







                  {% elif alignment_mode == 'industry_based' and industry_alignments|default_if_none:''|get_item:industry == 'right' %}
                    
                    
                    
                    
                    
                    
                    
                     justify-end







                  {% elif alignment_mode == 'industry_based' %}
                    
                    
                    
                    
                    
                    
                    
                     justify-start







                  {% elif alignment_mode == 'all_right' %}
                    
                    
                    
                    
                    
                    
                    
                     justify-end







                  {% else %}
                    
                    
                    
                    
                    
                    
                    
                     justify-start







                  {% endif %}">
                  <div class="mention-card {% if reading.actual_read_time %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
completed

















                    {% elif reading == next_mention %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                                     
next

















                    {% else %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
upcoming

















                    {% endif %} {% if alignment_mode == 'alternating' and forloop.counter0|divisibleby:2 %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
mention-right

















                    {% elif alignment_mode == 'alternating' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                                          
mention-left

















                    {% elif alignment_mode == 'industry_based' and industry_alignments|default_if_none:''|get_item:industry == 'right' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                                       
 mention-right

















                    {% elif alignment_mode == 'industry_based' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                                  
mention-left

















                    {% elif alignment_mode == 'all_right' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
mention-right

















                    {% else %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
  mention-left

















                    {% endif %} {% if not reading.actual_read_time and is_live %}draggable-mention cursor-move{% endif %} p-4 border shadow-sm relative"
                    data-reading-id="{{ reading.id }}"
                    data-industry="{{ industry }}"
                    title="Debug: Industry={{ industry }}, Color={{ industry_colors|default_if_none:''|get_item:industry|default:'NOT_FOUND' }}"
                    style="{% if alignment_mode == 'alternating' and forloop.counter0|divisibleby:2 %}
                      
                      
                      
                      
                      border-right-color: {{ industry_colors|default_if_none:''|get_item:industry|default:'#8b5cf6' }} !important;




                    {% elif alignment_mode == 'alternating' %}
                      
                      
                      
                      
                      border-left-color: {{ industry_colors|default_if_none:''|get_item:industry|default:'#3b82f6' }} !important;




                    {% elif alignment_mode == 'industry_based' and industry_alignments|default_if_none:''|get_item:industry == 'right' %}
                      
                      
                      
                      
                      border-right-color: {{ industry_colors|default_if_none:''|get_item:industry|default:'#8b5cf6' }} !important;




                    {% elif alignment_mode == 'industry_based' %}
                      
                      
                      
                      
                      border-left-color: {{ industry_colors|default_if_none:''|get_item:industry|default:'#3b82f6' }} !important;




                    {% elif alignment_mode == 'all_right' %}
                      
                      
                      
                      
                      border-right-color: {{ industry_colors|default_if_none:''|get_item:industry|default:'#8b5cf6' }} !important;




                    {% else %}
                      
                      
                      
                      
                      border-left-color: {{ industry_colors|default_if_none:''|get_item:industry|default:'#3b82f6' }} !important;




                    {% endif %}"
                    data-alignment="{% if alignment_mode == 'alternating' and forloop.counter0|divisibleby:2 %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                     
right

















                    {% elif alignment_mode == 'alternating' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
left

















                    {% elif alignment_mode == 'industry_based' and industry_alignments|default_if_none:''|get_item:industry == 'right' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                     
 right

















                    {% elif alignment_mode == 'industry_based' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                                         
left

















                    {% elif alignment_mode == 'all_right' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
 right

















                    {% else %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                      
                                         
left

















                    {% endif %}"
                    tabindex="0">
                    {% comment %}
                    <!-- Industry indicator -->
                    <div class="industry-indicator" title="{{ reading.mention.client.get_industry_display }}">
                      {% if industry == 'technology' %}
                        🔧
                      {% elif industry == 'healthcare' %}
                        🏥
                      {% elif industry == 'education' %}
                        🎓
                      {% elif industry == 'entertainment' %}
                        🎭
                      {% elif industry == 'retail' or industry == 'fashion_retail' %}
                        🛍️
                      {% elif industry == 'automotive' %}
                        🚗
                      {% elif industry == 'food_beverage' %}
                        🍽️
                      {% elif industry == 'government' %}
                        🏛️
                      {% elif industry == 'banking_finance' %}
                        🏦
                      {% elif industry == 'construction' %}
                        🏗️
                      {% elif industry == 'media_advertising' %}
                        📺
                      {% elif industry == 'hospitality_tourism' %}
                        🏨
                      {% elif industry == 'insurance' %}
                        🛡️
                      {% elif industry == 'legal_services' %}
                        ⚖️
                      {% elif industry == 'manufacturing' %}
                        🏭
                      {% elif industry == 'non_profit' %}
                        ❤️
                      {% elif industry == 'real_estate' %}
                        🏠
                      {% elif industry == 'sports_recreation' %}
                        ⚽
                      {% elif industry == 'telecommunications' %}
                        📡
                      {% elif industry == 'transportation' %}
                        🚛
                      {% elif industry == 'utilities' %}
                        ⚡
                      {% else %}
                        📢
                      {% endif %}
                    </div>
                    {% endcomment %}
                    <!-- Clean content layout -->
                    <div class="flex items-start space-x-3">
                      <!-- Time badge -->
                      <div class="flex-shrink-0 text-center">
                        <div class="text-sm font-bold text-gray-900 bg-white bg-opacity-80 rounded-full px-2 py-1 shadow-sm">{{ reading.scheduled_time|time:'H:i' }}</div>
                        {% if reading == next_mention %}
                          <div class="text-xs text-red-600 font-medium mt-1">NEXT</div>
                        {% endif %}
                      </div>

                      <!-- Main content -->
                      <div class="flex-1 min-w-0">
                        <!-- Header with client name and drag handle -->
                        <div class="flex items-center justify-between mb-2">
                          <h4 class="text-lg font-semibold text-gray-900">{{ reading.mention.client.name }}</h4>
                          {% if not reading.actual_read_time and is_live %}
                            <div class="drag-handle ml-2" title="Drag to reorder">
                              <i class="fa-solid fa-grip-vertical"></i>
                            </div>
                          {% endif %}
                        </div>

                        <!-- Mention title -->
                        <p class="text-sm font-medium text-gray-700 mb-2">{{ reading.mention.title }}</p>

                        <!-- Content bubble -->
                        <div class="bg-white bg-opacity-60 rounded-lg p-4 text-sm text-gray-800 leading-relaxed shadow-sm whitespace-pre-wrap">{{ reading.mention.content }}</div>

                        <!-- Meta information -->
                        <div class="flex items-center justify-between mt-3">
                          <div class="flex items-center space-x-2">
                            <span class="text-xs bg-white bg-opacity-80 text-gray-700 px-2 py-1 rounded-full shadow-sm">{{ reading.mention.duration_seconds }}s</span>
                            <span class="text-xs bg-white bg-opacity-80 text-gray-700 px-2 py-1 rounded-full shadow-sm">Priority: {{ reading.mention.get_priority_display }}</span>
                          </div>

                          <!-- Actions -->
                          <div class="flex items-center space-x-2">
                            {% if reading.actual_read_time %}
                              <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 shadow-sm">
                                <i class="fa-solid fa-check mr-2"></i>
                                Done
                              </span>
                            {% else %}
                              <button onclick="markComplete({{ reading.id }})" class="quick-action-btn px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 mr-2 text-xs">
                                <i class="fa-solid fa-check mr-1"></i>
                                Read
                              </button>
                              <button onclick="viewMention({{ reading.id }}, '{{ reading.mention.title|escapejs }}', '{{ reading.mention.content|escapejs }}', '{{ reading.mention.client.name|escapejs }}')" class="quick-action-btn bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-blue-300 shadow-sm px-3" title="View Full Content"><i class="fa-solid fa-eye"></i></button>
                            {% endif %}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              {% endwith %}
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-12">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fa-solid fa-calendar-check text-2xl text-gray-400"></i>
            </div>
            <h4 class="text-lg font-medium text-gray-900 mb-2">No mentions scheduled today</h4>
            <p class="text-gray-600">Enjoy your show!</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Mention Detail Modal -->
  <div id="mentionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white modal-enter">
      <div class="flex items-center justify-between pb-4 border-b border-gray-200">
        <h3 class="text-xl font-semibold text-gray-900" id="modalTitle">Mention Details</h3>
        <button onclick="closeMentionModal()" class="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300"><i class="fa-solid fa-times text-lg"></i></button>
      </div>
      <div class="mt-6">
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-500 mb-1">Client</h4>
          <p class="text-lg font-semibold text-gray-900" id="modalClient"></p>
        </div>
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-sm font-medium text-gray-500 mb-2">Content</h4>
          <div id="modalContent" class="text-gray-900 leading-relaxed whitespace-pre-wrap"></div>
        </div>
      </div>
    </div>
  </div>
{% endblock %} {% block extra_js %}
  <!-- Show data from Django -->
  <script>
  var isLive = {% if is_live %}true{% else %}false{% endif %};
  var showEndedEarly = {% if show_ended_early %}true{% else %}false{% endif %};
  {% if current_show %}
  var showStartTime = "{{ current_show.start_time|time:'H:i' }}";
  var showEndTime = "{{ current_show.end_time|time:'H:i' }}";
  var showName = "{{ current_show.name|escapejs }}";
  {% else %}
  var showStartTime = null;
  var showEndTime = null;
  var showName = null;
  {% endif %}

  // Make variables available globally for enhanced script
  window.isLive = isLive;
  window.showEndedEarly = showEndedEarly;
</script>

  <!-- Enhanced Live Show JavaScript -->
  <script src="{% static 'js/live-show-enhanced.js' %}"></script>

  <script>
  // Remove duplicate variables - they are defined in enhanced JS file

  // Update current time and time remaining
  function updateClock() {
    const now = new Date();
    const timeString = now.toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
    const dateString = now.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    const timeElement = document.getElementById("current-time");
    const dateElement = document.getElementById("current-date");
    const timeRemainingElement = document.getElementById("time-remaining");

    if (timeElement) {
      timeElement.textContent = timeString;
    }
    if (dateElement) {
      dateElement.textContent = dateString;
    }

    // Update time remaining if show is live and not ended early
    if (showEndedEarly && timeRemainingElement) {
      timeRemainingElement.textContent = "ENDED EARLY";
      timeRemainingElement.className = "text-lg font-bold text-red-600";
    } else if (isLive && showEndTime && timeRemainingElement) {
      const currentTime = now.getHours() * 60 + now.getMinutes();
      const [endHour, endMinute] = showEndTime.split(":").map(Number);
      let endTime = endHour * 60 + endMinute;

      // Handle shows that cross midnight
      if (endTime < currentTime) {
        endTime += 24 * 60; // Add 24 hours
      }

      const remainingMinutes = endTime - currentTime;

      if (remainingMinutes > 0) {
        const hours = Math.floor(remainingMinutes / 60);
        const minutes = remainingMinutes % 60;
        timeRemainingElement.textContent = `${hours}:${minutes
          .toString()
          .padStart(2, "0")}`;

        // Change color based on time remaining
        if (remainingMinutes <= 15) {
          timeRemainingElement.className = "text-lg font-bold text-red-600";
        } else if (remainingMinutes <= 30) {
          timeRemainingElement.className = "text-lg font-bold text-orange-600";
        } else {
          timeRemainingElement.className = "text-lg font-bold text-gray-900";
        }
      } else {
        timeRemainingElement.textContent = "ENDED";
        timeRemainingElement.className = "text-lg font-bold text-red-600";
      }
    }
  }

  // Refresh live show
  function refreshLiveShow() {
    location.reload();
  }

  // End show function
  function endShow() {
    if (!confirm("Are you sure you want to end the show early?")) {
      return;
    }

    fetch('{% url "core:end_show" %}', {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": document.querySelector("[name=csrfmiddlewaretoken]")
          .value,
      },
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.success) {
          showNotification(data.message, "success");
          // Refresh the page after a short delay to show the updated state
          setTimeout(() => {
            location.reload();
          }, 1500);
        } else {
          showNotification(
            "Error ending show: " + (data.error || "Unknown error"),
            "error"
          );
        }
      })
      .catch((error) => {
        console.error("Error:", error);
        showNotification("Error ending show: " + error.message, "error");
      });
  }

  // Mark mention as complete
  function markComplete(readingId) {
    if (confirm("Mark this mention as completed?")) {
      const csrfToken =
        document
          .querySelector('meta[name="csrf-token"]')
          ?.getAttribute("content") ||
        document.querySelector("[name=csrfmiddlewaretoken]")?.value;

      if (!csrfToken) {
        showNotification(
          "CSRF token not found. Please refresh the page and try again.",
          "error"
        );
        return;
      }

      fetch(`/mentions/readings/${readingId}/mark-read/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": csrfToken,
        },
        body: JSON.stringify({
          notes: "",
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            // Update the UI immediately
            const mentionCard = document.querySelector(
              `[data-reading-id="${readingId}"]`
            );
            if (mentionCard) {
              mentionCard.className =
                "mention-card completed p-4 rounded-lg border";
              const actionButtons = mentionCard.querySelector(
                ".flex.items-center.space-x-2:last-child"
              );
              if (actionButtons) {
                actionButtons.innerHTML = `
                        <span class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                          <i class="fa-solid fa-check mr-2"></i>
                          Completed
                        </span>
                      `;
              }
            }

            showNotification("Mention marked as complete!", "success");

            // Auto-select next mention using enhanced JS function
            setTimeout(() => {
              if (typeof selectNextMention === "function") {
                selectNextMention();
              }
            }, 500);
          } else {
            showNotification(
              "Error marking mention as complete: " +
                (data.error || "Unknown error"),
              "error"
            );
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          showNotification(
            "Error marking mention as complete: " + error.message,
            "error"
          );
        });
    }
  }

  // View mention details
  function viewMention(readingId, title, content, client) {
    document.getElementById("modalTitle").textContent = title;
    document.getElementById("modalClient").textContent = client;
    document.getElementById("modalContent").textContent = content;
    document.getElementById("mentionModal").classList.remove("hidden");
    document.body.style.overflow = "hidden";
  }

  // Close mention modal
  function closeMentionModal() {
    document.getElementById("mentionModal").classList.add("hidden");
    document.body.style.overflow = "auto";
  }

  // showNotification function is defined in enhanced JS file

  // selectNextMention and navigateMentions functions are defined in enhanced JS file

  // Close modal when clicking outside
  document
    .getElementById("mentionModal")
    .addEventListener("click", function (e) {
      if (e.target === this) {
        closeMentionModal();
      }
    });

  // Keyboard shortcuts are defined in enhanced JS file

  // Drag and Drop functionality is defined in enhanced JS file

  // Initialize
  document.addEventListener("DOMContentLoaded", function () {
    console.log("Live Show page initializing...");

    updateClock();
    setInterval(updateClock, 1000); // Update every second

    // Auto-select the next mention if available - using enhanced JS function
    setTimeout(() => {
      if (typeof selectNextMention === "function") {
        console.log("Auto-selecting next mention...");
        selectNextMention();
      } else {
        console.warn("selectNextMention function not available");
      }
    }, 500);

    // Test basic functionality
    console.log("Testing basic functions...");
    console.log(
      "viewMention function available:",
      typeof viewMention === "function"
    );
    console.log(
      "closeMentionModal function available:",
      typeof closeMentionModal === "function"
    );
    console.log(
      "markComplete function available:",
      typeof markComplete === "function"
    );
    console.log(
      "showNotification function available:",
      typeof showNotification === "function"
    );

    console.log("Live Show page initialized successfully");

    // Initialize weather functionality
    initializeWeatherWidget();
  });

  // Helper function to get temperature unit symbol
  function getTemperatureUnit(units) {
    switch (units) {
      case "metric":
        return "C";
      case "imperial":
        return "F";
      case "kelvin":
        return "K";
      default:
        return "C";
    }
  }

  // Weather widget functionality
  function initializeWeatherWidget() {
    const updateBtn = document.getElementById("update-weather-btn");
    const locationInput = document.getElementById("weather-location-input");
    const weatherDisplay = document.getElementById("weather-display");
    const weatherLoading = document.getElementById("weather-loading");
    const weatherError = document.getElementById("weather-error");
    const weatherErrorMessage = document.getElementById(
      "weather-error-message"
    );

    if (!updateBtn || !locationInput) {
      return; // Weather widget not available
    }

    // Handle update button click
    updateBtn.addEventListener("click", function () {
      updateWeatherData();
    });

    // Handle Enter key in input field
    locationInput.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        updateWeatherData();
      }
    });

    function updateWeatherData() {
      const location = locationInput.value.trim();
      if (!location) {
        showWeatherError("Please enter a location");
        return;
      }

      // Show loading state
      showWeatherLoading();

      // Make AJAX request
      fetch('{% url "core:get_weather_data" %}', {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": document.querySelector("[name=csrfmiddlewaretoken]")
            .value,
        },
        body: JSON.stringify({
          location: location,
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            updateWeatherDisplay(data.weather_data);
            hideWeatherLoading();
            hideWeatherError();
          } else {
            showWeatherError(data.error || "Failed to fetch weather data");
            hideWeatherLoading();
          }
        })
        .catch((error) => {
          console.error("Weather API error:", error);
          showWeatherError("Network error occurred");
          hideWeatherLoading();
        });
    }

    function updateWeatherDisplay(weatherData) {
      if (!weatherData) return;

      // Update weather icon and basic info
      const iconImg = weatherDisplay.querySelector("img");
      const tempElement = weatherDisplay.querySelector(".text-xl");
      const descElement = weatherDisplay.querySelector(
        ".text-sm.text-gray-600"
      );
      const locationElement = weatherDisplay.querySelector(
        ".text-xs.text-gray-500"
      );

      if (iconImg) {
        iconImg.src = `https://openweathermap.org/img/w/${weatherData.icon}.png`;
        iconImg.alt = weatherData.description;
      }

      if (tempElement) {
        const tempUnit = getTemperatureUnit(weatherData.units);
        tempElement.textContent = `${weatherData.temperature}°${tempUnit}`;
      }

      if (descElement) {
        descElement.textContent = weatherData.description;
      }

      if (locationElement) {
        locationElement.textContent = `${weatherData.location}${
          weatherData.country ? ", " + weatherData.country : ""
        }`;
      }

      // Update detailed weather info
      const detailsGrid = weatherDisplay.querySelector(".grid");
      if (detailsGrid) {
        detailsGrid.innerHTML = `
          <div class="text-right">
            <span class="text-gray-500">Feels like:</span>
            <span class="font-medium">${
              weatherData.feels_like
            }°${getTemperatureUnit(weatherData.units)}</span>
          </div>
          <div class="text-right">
            <span class="text-gray-500">Humidity:</span>
            <span class="font-medium">${weatherData.humidity}%</span>
          </div>
          ${
            weatherData.pressure
              ? `
          <div class="text-right">
            <span class="text-gray-500">Pressure:</span>
            <span class="font-medium">${weatherData.pressure} hPa</span>
          </div>
          `
              : ""
          }
          ${
            weatherData.wind_speed
              ? `
          <div class="text-right">
            <span class="text-gray-500">Wind:</span>
            <span class="font-medium">${weatherData.wind_speed} m/s</span>
          </div>
          `
              : ""
          }
          ${
            weatherData.visibility
              ? `
          <div class="text-right">
            <span class="text-gray-500">Visibility:</span>
            <span class="font-medium">${weatherData.visibility} km</span>
          </div>
          `
              : ""
          }
        `;
      }

      // Clear the input field
      locationInput.value = "";
    }

    function showWeatherLoading() {
      if (weatherDisplay) weatherDisplay.classList.add("hidden");
      if (weatherError) weatherError.classList.add("hidden");
      if (weatherLoading) weatherLoading.classList.remove("hidden");
    }

    function hideWeatherLoading() {
      if (weatherLoading) weatherLoading.classList.add("hidden");
      if (weatherDisplay) weatherDisplay.classList.remove("hidden");
    }

    function showWeatherError(message) {
      if (weatherDisplay) weatherDisplay.classList.add("hidden");
      if (weatherLoading) weatherLoading.classList.add("hidden");
      if (weatherError) weatherError.classList.remove("hidden");
      if (weatherErrorMessage) weatherErrorMessage.textContent = message;
    }

    function hideWeatherError() {
      if (weatherError) weatherError.classList.add("hidden");
    }
  }
</script>
{% endblock %}
