{% extends 'base.html' %}
{% load static %}

{% block title %}Account Temporarily Locked{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-red-100">
                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Account Temporarily Locked
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Your account has been temporarily locked due to too many failed login attempts.
            </p>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="space-y-4">
                {% if username %}
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Account: <span class="font-medium text-gray-900">{{ username }}</span>
                    </p>
                </div>
                {% endif %}

                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Security Notice
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>For security reasons, your account has been temporarily locked after multiple failed login attempts.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-3">
                    <h4 class="text-sm font-medium text-gray-900">What you can do:</h4>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li class="flex items-start">
                            <span class="flex-shrink-0 h-1.5 w-1.5 rounded-full bg-gray-400 mt-2 mr-3"></span>
                            <span>Wait for the lockout period to expire (typically 1 hour)</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 h-1.5 w-1.5 rounded-full bg-gray-400 mt-2 mr-3"></span>
                            <span>Contact your system administrator if you need immediate access</span>
                        </li>
                        <li class="flex items-start">
                            <span class="flex-shrink-0 h-1.5 w-1.5 rounded-full bg-gray-400 mt-2 mr-3"></span>
                            <span>Make sure you're using the correct username and password</span>
                        </li>
                    </ul>
                </div>

                <div class="pt-4 border-t border-gray-200">
                    <div class="flex space-x-3">
                        <a href="{% url 'account_login' %}" 
                           class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white text-center py-2 px-4 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                            Try Again Later
                        </a>
                        <a href="{% url 'organizations:signup' %}" 
                           class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 text-center py-2 px-4 rounded-md text-sm font-medium transition duration-150 ease-in-out">
                            Create Account
                        </a>
                    </div>
                </div>

                <div class="text-center">
                    <p class="text-xs text-gray-500">
                        If you continue to experience issues, please contact support.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
