{% extends 'base.html' %}
{% load static %}

{% block title %}
  Delete Client - {{ client.name }}
{% endblock %}

{% block content %}
  <div class="max-w-md mx-auto mt-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center">
          <a href="{% url 'core:client_detail' client.pk %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
          <h1 class="text-xl font-semibold text-gray-900">Delete Client</h1>
        </div>
      </div>

      <div class="p-6">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
          </div>

          <h3 class="text-lg font-medium text-gray-900 mb-2">Are you sure you want to delete this client?</h3>

          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div class="text-left">
              <p class="font-medium text-gray-900">{{ client.name }}</p>
              {% if client.contact_person %}
                <p class="text-sm text-gray-600">Contact: {{ client.contact_person }}</p>
              {% endif %}
              {% if client.email %}
                <p class="text-sm text-gray-500">{{ client.email }}</p>
              {% endif %}
              {% if client.industry %}
                <p class="text-sm text-gray-500">Industry: {{ client.industry }}</p>
              {% endif %}
            </div>
          </div>

          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>This action cannot be undone. Deleting this client will:</p>
                  <ul class="list-disc list-inside mt-2 space-y-1">
                    <li>Delete all mentions associated with this client</li>
                    <li>Remove all scheduled readings for this client</li>
                    <li>Delete client information permanently</li>
                    <li>Remove all historical data and reports</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Show mention count if any -->
          {% if client.mention.count > 0 %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fa-solid fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Active Mentions Found</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <p>
                      This client has <strong>{{ client.mention.count }}</strong> mention(s) that will also be deleted.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          {% endif %}

          <form method="post" class="space-y-4">
            {% csrf_token %}
            <div class="flex justify-center space-x-3">
              <a href="{% url 'core:client_detail' client.pk %}" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</a>
              <button type="submit" class="bg-red-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <i class="fa-solid fa-trash mr-2"></i>
                Delete Client
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
