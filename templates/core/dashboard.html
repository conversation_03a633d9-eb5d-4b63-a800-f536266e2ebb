{% extends 'base.html' %} {% load static %} {% load time_filters %} {% block title %}
  Dashboard
{% endblock %} {% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p class="text-gray-600">Welcome back! Here's what's happening with your radio mentions today.</p>
        </div>
        <div class="flex items-center space-x-3">
          <span class="text-sm text-gray-500">{{ current_organization.name }}</span>
          <div class="flex items-center space-x-2">
            <a href="{% url 'mentions:mention_create' %}" class="bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors"><i class="fa-solid fa-plus mr-2"></i>New Mention</a>
            <a href="{% url 'mentions:calendar_interface' %}" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"><i class="fa-solid fa-calendar mr-2"></i>Calendar</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Dashboard Filters -->
    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center space-x-2">
          <label for="date-range" class="text-sm font-medium text-gray-700">Time Period:</label>
          <select id="date-range" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
            <option value="today">Today</option>
            <option value="7days" selected>Last 7 Days</option>
            <option value="30days">Last 30 Days</option>
            <option value="90days">Last 90 Days</option>
            <option value="custom">Custom Range</option>
          </select>
        </div>

        <div class="flex items-center space-x-2">
          <label for="show-filter" class="text-sm font-medium text-gray-700">Show:</label>
          <select id="show-filter" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
            <option value="">All Shows</option>
            {% for show in current_organization.shows.all %}
              <option value="{{ show.id }}">{{ show.name }}</option>
            {% endfor %}
          </select>
        </div>

        <div class="flex items-center space-x-2">
          <label for="industry-filter" class="text-sm font-medium text-gray-700">Industry:</label>
          <select id="industry-filter" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
            <option value="">All Industries</option>
            {% for item in industry_analytics %}
              <option value="{{ item.industry.code }}">{{ item.industry.name }}</option>
            {% endfor %}
          </select>
        </div>

        <div class="flex items-center space-x-2">
          <input type="date" id="start-date" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" style="display: none;" />
          <input type="date" id="end-date" class="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" style="display: none;" />
        </div>

        <button id="apply-filters" class="bg-primary-600 text-white px-4 py-1 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors"><i class="fa-solid fa-filter mr-1"></i>Apply Filters</button>

        <button id="reset-filters" class="bg-gray-100 text-gray-700 px-4 py-1 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors"><i class="fa-solid fa-refresh mr-1"></i>Reset</button>

        <div class="ml-auto">
          <span class="text-xs text-gray-500">Last updated: <span id="last-updated">{{ 'now'|date:'M d, Y H:i' }}</span></span>
        </div>
      </div>
    </div>

    <!-- System Notifications -->
    <div id="dashboard-notifications" class="mb-6 space-y-3">
      <!-- Real-time notifications will be inserted here -->
    </div>

    <!-- Alerts Section -->
    {% if alerts %}
      <div class="mb-6 space-y-3">
        {% for alert in alerts %}
          <div class="p-4 rounded-lg border flex items-center {% if alert.type == 'error' %}
              
              
              
              
              
              
              
              
              
              
               bg-red-50 border-red-200 text-red-800










            {% elif alert.type == 'warning' %}
              
              
              
              
              
              
              
              
              
              
               bg-yellow-50 border-yellow-200 text-yellow-800










            {% else %}
              
              
              
              
              
              
              
              
              
              
               bg-blue-50 border-blue-200 text-blue-800










            {% endif %}">
            <i class="fas {% if alert.type == 'error' %}
                
                
                
                
                
                
                
                
                
                
                 fa-exclamation-circle










              {% elif alert.type == 'warning' %}
                
                
                
                
                
                
                
                
                
                
                 fa-exclamation-triangle










              {% else %}
                
                
                
                
                
                
                
                
                
                
                 fa-info-circle










              {% endif %} mr-3">

            </i>
            <div class="flex-1">
              <h4 class="font-medium">{{ alert.title }}</h4>
              <p class="text-sm opacity-90">{{ alert.message }}</p>
            </div>
            {% if alert.action_url %}
              <a href="{{ alert.action_url }}" class="text-sm font-medium hover:underline ml-4">{{ alert.action_text }}</a>
            {% endif %}
            <button onclick="this.parentElement.remove()" class="ml-2 text-gray-500 hover:text-gray-700"><i class="fas fa-times"></i></button>
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- Enhanced Performance Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-8">
      <!-- Total Shows -->
      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-xs font-medium text-gray-500 uppercase">Total Shows</h3>
          <i class="fa-solid fa-broadcast-tower text-blue-500"></i>
        </div>
        <p class="text-xl font-bold text-gray-800">{{ stats.total_shows }}</p>
        <p class="text-xs text-gray-500 mt-1">{{ stats.active_shows }} active</p>
      </div>

      <!-- Active Recurring -->
      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-xs font-medium text-gray-500 uppercase">Recurring Active</h3>
          <i class="fa-solid fa-repeat text-green-500"></i>
        </div>
        <p class="text-xl font-bold text-gray-800">{{ stats.active_recurring_mentions }}</p>
        <p class="text-xs text-gray-500 mt-1">{{ recurring_stats.paused_count }} paused</p>
      </div>

      <!-- 30-Day Completed -->
      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-xs font-medium text-gray-500 uppercase">30-Day Completed</h3>
          <i class="fa-solid fa-check-circle text-green-500"></i>
        </div>
        <p class="text-xl font-bold text-gray-800">{{ stats.completed_mentions_30_days }}</p>
        <p class="text-xs text-gray-500 mt-1">{{ stats.avg_mentions_per_show|floatformat:1 }} avg/show</p>
      </div>

      <!-- Active Presenters -->
      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-xs font-medium text-gray-500 uppercase">Active Presenters</h3>
          <i class="fa-solid fa-users text-purple-500"></i>
        </div>
        <p class="text-xl font-bold text-gray-800">{{ stats.active_presenters }}</p>
        <p class="text-xs text-gray-500 mt-1">across {{ stats.active_shows }} shows</p>
      </div>

      <!-- Active Clients -->
      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-xs font-medium text-gray-500 uppercase">Active Clients</h3>
          <i class="fa-solid fa-building text-indigo-500"></i>
        </div>
        <p class="text-xl font-bold text-gray-800">{{ stats.active_clients }}</p>
        <div class="flex items-center text-xs text-gray-500 mt-1">
          <span class="{% if stats.clients_growth >= 0 %}
              
              
              
              
              
              
              
              text-green-500







            {% else %}
              
              
              
              
              
              
              
              text-red-500







            {% endif %}">
            {{ stats.clients_growth|floatformat:1 }}%
          </span>
          <span class="ml-1">vs last month</span>
        </div>
      </div>

      <!-- Today's Progress -->
      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-xs font-medium text-gray-500 uppercase">Today's Progress</h3>
          <i class="fa-solid fa-chart-line text-blue-500"></i>
        </div>
        <p class="text-xl font-bold text-gray-800">{{ stats.completed_today }}/{{ stats.scheduled_today }}</p>
        <div class="mt-2">
          <div class="w-full bg-gray-200 rounded-full h-1.5">
            <div class="bg-blue-500 h-1.5 rounded-full" style="width: {{ stats.completion_rate }}%"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1">{{ stats.completion_rate }}% complete</p>
        </div>
      </div>
    </div>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-sm font-medium text-gray-500">Total Mentions</h3>
          <span class="{% if stats.mentions_growth >= 0 %}
              
              
              
              
              
              
              
              
              
              
               text-green-500










            {% else %}
              
              
              
              
              
              
              
              
              
              
               text-red-500










            {% endif %} text-xs font-medium flex items-center">
            <i class="fa-solid fa-arrow-{% if stats.mentions_growth >= 0 %}
                
                
                
                
                
                
                
                
                
                
                 up










              {% else %}
                
                
                
                
                
                
                
                
                
                
                 down










              {% endif %} mr-1">

            </i>
            {{ stats.mentions_growth|floatformat:1 }}%
          </span>
        </div>
        <p class="text-2xl font-bold text-gray-800">{{ stats.total_mentions }}</p>
        <div class="mt-2 text-xs text-gray-500">
          <span>vs. {{ stats.last_month_mentions }} last month</span>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-sm font-medium text-gray-500">Pending Approvals</h3>
          <span class="{% if stats.pending_growth >= 0 %}
              
              
              
              
              
              
              
              
              
              
               text-red-500










            {% else %}
              
              
              
              
              
              
              
              
              
              
               text-green-500










            {% endif %} text-xs font-medium flex items-center">
            <i class="fa-solid fa-arrow-{% if stats.pending_growth >= 0 %}
                
                
                
                
                
                
                
                
                
                
                 up










              {% else %}
                
                
                
                
                
                
                
                
                
                
                 down










              {% endif %} mr-1">

            </i>
            {{ stats.pending_growth|floatformat:1 }}%
          </span>
        </div>
        <p class="text-2xl font-bold text-gray-800">{{ stats.pending_mentions }}</p>
        <div class="mt-2 text-xs text-gray-500">
          <span>vs. {{ stats.last_week_pending }} last week</span>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-sm font-medium text-gray-500">Active Clients</h3>
          <span class="{% if stats.clients_growth >= 0 %}
              
              
              
              
              
              
              
              
              
              
               text-green-500










            {% else %}
              
              
              
              
              
              
              
              
              
              
               text-red-500










            {% endif %} text-xs font-medium flex items-center">
            <i class="fa-solid fa-arrow-{% if stats.clients_growth >= 0 %}
                
                
                
                
                
                
                
                
                
                
                 up










              {% else %}
                
                
                
                
                
                
                
                
                
                
                 down










              {% endif %} mr-1">

            </i>
            {{ stats.clients_growth|floatformat:1 }}%
          </span>
        </div>
        <p class="text-2xl font-bold text-gray-800">{{ stats.active_clients }}</p>
        <div class="mt-2 text-xs text-gray-500">
          <span>vs. {{ stats.last_month_clients }} last month</span>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-sm font-medium text-gray-500">Today's Progress</h3>
          <span class="text-blue-500 text-xs font-medium">{{ stats.completion_rate }}%</span>
        </div>
        <p class="text-2xl font-bold text-gray-800">{{ stats.completed_today }}/{{ stats.scheduled_today }}</p>
        <div class="mt-2 text-xs text-gray-500">
          <span>across {{ stats.shows_today }} shows</span>
        </div>
        <div class="mt-3">
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-500 h-2 rounded-full" style="width: {{ stats.completion_rate }}%"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Industry Analytics and Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
      <!-- Industry Analytics -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Industry Distribution</h3>
          <i class="fa-solid fa-chart-pie text-gray-400"></i>
        </div>
        <div class="space-y-3">
          {% for item in industry_analytics|slice:':6' %}
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 rounded-full" style="background-color: {{ item.color }}"></div>
                <span class="text-sm text-gray-700 truncate">{{ item.industry.name }}</span>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-gray-900">{{ item.mention_count }}</div>
                <div class="text-xs text-gray-500">{{ item.percentage }}%</div>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-4">
              <i class="fa-solid fa-industry text-gray-300 text-2xl mb-2"></i>
              <p class="text-sm text-gray-500">No industry data</p>
            </div>
          {% endfor %}
        </div>
        {% if industry_analytics|length > 6 %}
          <div class="mt-4 pt-3 border-t border-gray-200">
            <a href="#" class="text-primary-600 text-sm hover:text-primary-700">View all industries →</a>
          </div>
        {% endif %}
      </div>

      <!-- Charts Section -->
      <div class="lg:col-span-3 grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-medium text-gray-800">Weekly Activity</h3>
            <i class="fa-solid fa-chart-line text-gray-400"></i>
          </div>
          <div class="h-[250px]">
            <canvas id="weeklyChart"></canvas>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-medium text-gray-800">Status Distribution</h3>
            <i class="fa-solid fa-chart-pie text-gray-400"></i>
          </div>
          <div class="h-[250px]">
            <canvas id="statusChart"></canvas>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-medium text-gray-800">Top Clients</h3>
            <i class="fa-solid fa-chart-bar text-gray-400"></i>
          </div>
          <div class="h-[250px]">
            <canvas id="clientChart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Show Management Updates and Mention Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Show Management Updates -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Recent Show Updates</h3>
          <a href="{% url 'shows:show_list' %}" class="text-primary-600 text-sm hover:text-primary-700">View All</a>
        </div>
        <div class="space-y-3">
          {% for update in show_updates|slice:':5' %}
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fa-solid fa-broadcast-tower text-orange-600 text-xs"></i>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">{{ update.description }}</p>
                <div class="flex items-center text-xs text-gray-500 mt-1">
                  <span>{{ update.user.get_full_name|default:update.user.username }}</span>
                  <span class="mx-1">•</span>
                  <span>{{ update.created_at|timesince }} ago</span>
                </div>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-4">
              <i class="fa-solid fa-broadcast-tower text-gray-300 text-2xl mb-2"></i>
              <p class="text-sm text-gray-500">No recent show updates</p>
            </div>
          {% endfor %}
        </div>
      </div>

      <!-- Mention Activity Summary -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Today's Mention Activity</h3>
          <a href="{% url 'mentions:mention_list' %}" class="text-primary-600 text-sm hover:text-primary-700">View All</a>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center p-3 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{ mention_activity.created_today }}</div>
            <div class="text-xs text-green-700 mt-1">Created</div>
          </div>
          <div class="text-center p-3 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{ mention_activity.approved_today }}</div>
            <div class="text-xs text-blue-700 mt-1">Approved</div>
          </div>
          <div class="text-center p-3 bg-purple-50 rounded-lg">
            <div class="text-2xl font-bold text-purple-600">{{ mention_activity.completed_today }}</div>
            <div class="text-xs text-purple-700 mt-1">Completed</div>
          </div>
          <div class="text-center p-3 bg-orange-50 rounded-lg">
            <div class="text-2xl font-bold text-orange-600">{{ mention_activity.edited_today }}</div>
            <div class="text-xs text-orange-700 mt-1">Edited</div>
          </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-200">
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600">Status changes (7 days)</span>
            <span class="font-medium text-gray-900">{{ mention_activity.status_changes_7_days }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Recurring Schedule Status -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-8">
      <div class="flex items-center justify-between mb-4">
        <h3 class="font-medium text-gray-800">Recurring Schedule Status</h3>
        <a href="{% url 'mentions:recurring_mentions' %}" class="text-primary-600 text-sm hover:text-primary-700">View All</a>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="text-3xl font-bold text-green-600 mb-2">{{ recurring_stats.active_count }}</div>
          <div class="text-sm text-gray-600">Active Campaigns</div>
          <div class="text-xs text-gray-500 mt-1">Currently running</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-yellow-600 mb-2">{{ recurring_stats.paused_count }}</div>
          <div class="text-sm text-gray-600">Paused</div>
          <div class="text-xs text-gray-500 mt-1">Temporarily stopped</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-red-600 mb-2">{{ recurring_stats.ended_recently }}</div>
          <div class="text-sm text-gray-600">Recently Ended</div>
          <div class="text-xs text-gray-500 mt-1">Last 7 days</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-blue-600 mb-2">{{ recurring_stats.total_aired_30_days }}</div>
          <div class="text-sm text-gray-600">Total Aired</div>
          <div class="text-xs text-gray-500 mt-1">Last 30 days</div>
        </div>
      </div>
    </div>

    <!-- Activity and Schedule Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Comprehensive Recent Activity Feed -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Recent Activity Feed</h3>
          <a href="{% url 'activity_logs:activity_logs' %}" class="text-primary-600 text-sm hover:text-primary-700">View All</a>
        </div>
        <div class="space-y-3 max-h-96 overflow-y-auto">
          {% for activity in recent_activity_feed|slice:':10' %}
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fa-solid {{ activity.icon }} {{ activity.color }} text-xs"></i>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900">{{ activity.description }}</p>
                <div class="flex items-center text-xs text-gray-500 mt-1">
                  <span>{{ activity.user }}</span>
                  <span class="mx-1">•</span>
                  <span>{{ activity.timestamp|timesince }} ago</span>
                </div>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-4">
              <i class="fa-solid fa-clock text-gray-300 text-2xl mb-2"></i>
              <p class="text-sm text-gray-500">No recent activity</p>
            </div>
          {% endfor %}
        </div>
      </div>

      <!-- Upcoming Schedule -->
      <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Upcoming Schedule</h3>
          <a href="{% url 'mentions:calendar_interface' %}" class="text-primary-600 text-sm hover:text-primary-700">View Calendar</a>
        </div>
        <div class="space-y-3">
          {% for reading in upcoming_schedule|slice:':6' %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="text-center">
                  <div class="text-xs text-gray-500">{{ reading.scheduled_date|format_date_user:user }}</div>
                  <div class="text-sm font-medium text-gray-900">{{ reading.scheduled_date|date:'d' }}</div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ reading.mention.title }}</p>
                  <p class="text-xs text-gray-500">{{ reading.show.name }} • {{ reading.scheduled_time|format_time_user:user }}</p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">{{ reading.mention.client.name }}</span>
                <a href="{% url 'mentions:reading_detail' reading.pk %}" class="text-primary-600 hover:text-primary-700"><i class="fa-solid fa-arrow-right text-xs"></i></a>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-8">
              <i class="fa-solid fa-calendar text-gray-300 text-3xl mb-3"></i>
              <p class="text-sm text-gray-500">No upcoming schedule</p>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>
    <!-- Today's Schedule -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-800">Today's Schedule</h3>
        <a href="{% url 'mentions:calendar' %}" class="text-primary-600 text-sm font-medium hover:text-primary-700">View Calendar</a>
      </div>
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Presenter</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for reading in todays_schedule|default:''|slice:':5' %}
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reading.scheduled_time|format_time_user:user|default:'07:15 AM' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reading.show.name|default:'Morning Drive' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center mr-2">
                        <i class="fa-solid fa-user text-gray-400 text-xs"></i>
                      </div>
                      <span class="text-sm text-gray-900">{{ reading.presenter.get_full_name|default:'Mike Johnson' }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reading.mention.client.name|default:'Spotify Premium' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if reading.actual_read_time %}
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                    {% else %}
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Upcoming</span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <a href="{% url 'mentions:reading_detail' reading.pk %}" class="text-primary-600 hover:text-primary-900">View</a>
                  </td>
                </tr>
              {% empty %}
                <tr>
                  <td colspan="6" class="px-6 py-8 text-center">
                    <div class="flex flex-col items-center">
                      <i class="fa-solid fa-calendar text-gray-300 text-3xl mb-3"></i>
                      <p class="text-sm text-gray-500">No scheduled mentions for today</p>
                      <a href="{% url 'mentions:mention_create' %}" class="mt-2 text-primary-600 text-sm font-medium hover:text-primary-700">Create your first mention →</a>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Pending Approvals -->
    <div>
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-800">Pending Approvals</h3>
        <a href="{% url 'mentions:approval_workflow' %}" class="text-primary-600 text-sm font-medium hover:text-primary-700">View All</a>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {% for mention in pending_mentions|default:''|slice:':3' %}
          <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">
            <div class="flex justify-between items-start mb-4">
              <div>
                <h4 class="font-medium text-gray-900">{{ mention.title }}</h4>
                <p class="text-sm text-gray-500 mt-1">{{ mention.show.name }} - {{ mention.created_at|format_date_user:user }}</p>
              </div>
              <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Waiting</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">{{ mention.content|truncatewords:15 }}</p>
            <div class="flex items-center text-sm text-gray-500 mb-4">
              <i class="fa-regular fa-clock mr-1"></i>
              <span>Submitted {{ mention.created_at|timesince }} ago</span>
            </div>
            <div class="flex space-x-2">
              <button onclick="approveMention({{ mention.pk }})" class="px-3 py-1.5 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">Approve</button>
              <a href="{% url 'mentions:mention_edit' mention.pk %}" class="px-3 py-1.5 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50">Edit</a>
              <button onclick="rejectMention({{ mention.pk }})" class="px-3 py-1.5 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50">Reject</button>
            </div>
          </div>
        {% empty %}
          <div class="col-span-full">
            <div class="text-center py-8">
              <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                <i class="fa-solid fa-check-circle text-gray-400 text-xl"></i>
              </div>
              <p class="text-gray-500">No pending approvals</p>
              <p class="text-sm text-gray-400 mt-1">All mentions are up to date!</p>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
{% endblock %} {% block extra_js %}
  <script src="{% static 'js/chart.min.js' %}"></script>
  <script>
  // Chart data from backend
  const weeklyData = {
    labels: {{ chart_data.weekly_labels|safe }},
    datasets: [{
      label: 'Completed Mentions',
      data: {{ chart_data.weekly_data|safe }},
      backgroundColor: 'rgba(14, 165, 233, 0.1)',
      borderColor: 'rgba(14, 165, 233, 1)',
      borderWidth: 2,
      fill: true,
      tension: 0.4
    }]
  };

  const statusData = {
    labels: {{ chart_data.status_labels|safe }},
    datasets: [{
      data: {{ chart_data.status_data|safe }},
      backgroundColor: [
        'rgba(34, 197, 94, 0.8)',
        'rgba(234, 179, 8, 0.8)',
        'rgba(59, 130, 246, 0.8)',
        'rgba(239, 68, 68, 0.8)'
      ],
      borderWidth: 0
    }]
  };

  const clientData = {
    labels: {{ chart_data.client_labels|safe }},
    datasets: [{
      data: {{ chart_data.client_data|safe }},
      backgroundColor: [
        'rgba(14, 165, 233, 0.8)',
        'rgba(34, 197, 94, 0.8)',
        'rgba(234, 179, 8, 0.8)',
        'rgba(239, 68, 68, 0.8)',
        'rgba(168, 85, 247, 0.8)'
      ],
      borderWidth: 0
    }]
  };

  // Initialize charts
  const weeklyCtx = document.getElementById('weeklyChart').getContext('2d');
  new Chart(weeklyCtx, {
    type: 'line',
    data: weeklyData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  const statusCtx = document.getElementById('statusChart').getContext('2d');
  new Chart(statusCtx, {
    type: 'doughnut',
    data: statusData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    }
  });

  const clientCtx = document.getElementById('clientChart').getContext('2d');
  new Chart(clientCtx, {
    type: 'bar',
    data: clientData,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Approval functions
  function approveMention(mentionId) {
    if (confirm('Are you sure you want to approve this mention?')) {
      fetch(`/mentions/${mentionId}/approve/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json',
        },
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          location.reload();
        } else {
          alert('Error approving mention: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error approving mention');
      });
    }
  }

  function rejectMention(mentionId) {
    const reason = prompt('Please provide a reason for rejection:');
    if (reason) {
      fetch(`/mentions/${mentionId}/reject/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({reason: reason})
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          location.reload();
        } else {
          alert('Error rejecting mention: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error rejecting mention');
      });
    }
  }

  // Dashboard filtering functionality
  document.addEventListener('DOMContentLoaded', function() {
    const dateRangeSelect = document.getElementById('date-range');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const resetFiltersBtn = document.getElementById('reset-filters');

    // Show/hide custom date inputs
    dateRangeSelect.addEventListener('change', function() {
      if (this.value === 'custom') {
        startDateInput.style.display = 'block';
        endDateInput.style.display = 'block';
      } else {
        startDateInput.style.display = 'none';
        endDateInput.style.display = 'none';
      }
    });

    // Apply filters
    applyFiltersBtn.addEventListener('click', function() {
      const filters = {
        dateRange: dateRangeSelect.value,
        startDate: startDateInput.value,
        endDate: endDateInput.value,
        show: document.getElementById('show-filter').value,
        industry: document.getElementById('industry-filter').value
      };

      // Show loading state
      this.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-1"></i>Applying...';
      this.disabled = true;

      // Apply filters via AJAX
      fetch(window.location.href, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        },
        body: JSON.stringify({
          action: 'apply_filters',
          filters: filters
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Reload the page with new filters
          window.location.reload();
        } else {
          alert('Error applying filters: ' + data.error);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error applying filters');
      })
      .finally(() => {
        this.innerHTML = '<i class="fa-solid fa-filter mr-1"></i>Apply Filters';
        this.disabled = false;
      });
    });

    // Reset filters
    resetFiltersBtn.addEventListener('click', function() {
      dateRangeSelect.value = '7days';
      document.getElementById('show-filter').value = '';
      document.getElementById('industry-filter').value = '';
      startDateInput.style.display = 'none';
      endDateInput.style.display = 'none';
      startDateInput.value = '';
      endDateInput.value = '';

      // Reload page without filters
      window.location.href = window.location.pathname;
    });

    // Auto-refresh dashboard every 5 minutes
    setInterval(function() {
      document.getElementById('last-updated').textContent = new Date().toLocaleString();
    }, 300000);
  });
</script>
{% endblock %}
