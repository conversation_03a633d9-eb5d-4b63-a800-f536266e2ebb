{% extends 'base.html' %}
{% load static %}

{% block title %}System Performance Monitor{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .metric-label {
        color: #6b7280;
        font-size: 0.9rem;
    }
    
    .health-score {
        text-align: center;
        padding: 30px;
    }
    
    .health-score.excellent { color: #10b981; }
    .health-score.good { color: #f59e0b; }
    .health-score.poor { color: #ef4444; }
    
    .performance-chart {
        height: 300px;
        background: #f8fafc;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-online { background-color: #10b981; }
    .status-warning { background-color: #f59e0b; }
    .status-offline { background-color: #ef4444; }
    
    .refresh-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #3b82f6;
        color: white;
        padding: 10px 15px;
        border-radius: 6px;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>📊 System Performance Monitor</h1>
        <div class="d-flex align-items-center">
            <span class="status-indicator status-online"></span>
            <span>Live Monitoring</span>
            <button id="toggleMonitoring" class="btn btn-sm btn-outline-primary ms-3">Pause</button>
        </div>
    </div>

    <!-- Performance Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value text-primary" id="completionRate">--</div>
                <div class="metric-label">Completion Rate</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value text-success" id="activeSessions">--</div>
                <div class="metric-label">Active Sessions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value text-warning" id="pendingMentions">--</div>
                <div class="metric-label">Pending Mentions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-card">
                <div class="metric-value text-danger" id="errorCount">--</div>
                <div class="metric-label">Errors (Last Hour)</div>
            </div>
        </div>
    </div>

    <!-- Health Score -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="metric-card">
                <div class="health-score" id="healthScore">
                    <div class="metric-value">--</div>
                    <div class="metric-label">System Health Score</div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="metric-card">
                <h5>System Status</h5>
                <div id="systemStatus">
                    <div class="d-flex align-items-center mb-2">
                        <span class="status-indicator status-online"></span>
                        <span>Celery Workers: Online</span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <span class="status-indicator status-online"></span>
                        <span>Database: Connected</span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <span class="status-indicator status-online"></span>
                        <span>Redis: Connected</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Chart -->
    <div class="row">
        <div class="col-12">
            <div class="metric-card">
                <h5>Performance Trends</h5>
                <div class="performance-chart">
                    <canvas id="performanceChart" width="800" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="metric-card">
                <h5>Recent Activity</h5>
                <div id="recentActivity">
                    <div class="text-muted">Loading activity...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="refresh-indicator" id="refreshIndicator" style="display: none;">
    Updating...
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let monitoringActive = true;
let performanceChart;
let performanceData = [];

// Initialize chart
function initChart() {
    const ctx = document.getElementById('performanceChart').getContext('2d');
    performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Health Score',
                data: [],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }, {
                label: 'Completion Rate',
                data: [],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
}

// Update metrics
function updateMetrics() {
    if (!monitoringActive) return;
    
    document.getElementById('refreshIndicator').style.display = 'block';
    
    fetch('/system-performance/api/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const metrics = data.metrics;
                
                // Update metric cards
                document.getElementById('completionRate').textContent = 
                    metrics.performance.completion_rate + '%';
                document.getElementById('activeSessions').textContent = 
                    metrics.shows.active_sessions;
                document.getElementById('pendingMentions').textContent = 
                    metrics.mentions.pending_today;
                document.getElementById('errorCount').textContent = 
                    metrics.activity.errors_last_hour;
                
                // Update health score
                const healthScore = metrics.performance.health_score;
                const healthElement = document.getElementById('healthScore');
                healthElement.querySelector('.metric-value').textContent = healthScore;
                
                // Update health score color
                healthElement.className = 'health-score';
                if (healthScore >= 90) {
                    healthElement.classList.add('excellent');
                } else if (healthScore >= 70) {
                    healthElement.classList.add('good');
                } else {
                    healthElement.classList.add('poor');
                }
                
                // Update chart
                updateChart(metrics);
                
                console.log('Metrics updated:', metrics);
            }
        })
        .catch(error => {
            console.error('Error fetching metrics:', error);
        })
        .finally(() => {
            document.getElementById('refreshIndicator').style.display = 'none';
        });
}

// Update chart with new data
function updateChart(metrics) {
    const now = new Date().toLocaleTimeString();
    
    // Add new data point
    performanceChart.data.labels.push(now);
    performanceChart.data.datasets[0].data.push(metrics.performance.health_score);
    performanceChart.data.datasets[1].data.push(metrics.performance.completion_rate);
    
    // Keep only last 20 data points
    if (performanceChart.data.labels.length > 20) {
        performanceChart.data.labels.shift();
        performanceChart.data.datasets[0].data.shift();
        performanceChart.data.datasets[1].data.shift();
    }
    
    performanceChart.update('none');
}

// Toggle monitoring
document.getElementById('toggleMonitoring').addEventListener('click', function() {
    monitoringActive = !monitoringActive;
    this.textContent = monitoringActive ? 'Pause' : 'Resume';
    this.className = monitoringActive ? 
        'btn btn-sm btn-outline-primary ms-3' : 
        'btn btn-sm btn-outline-danger ms-3';
});

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    initChart();
    updateMetrics();
    
    // Update every 30 seconds
    setInterval(updateMetrics, 30000);
});
</script>
{% endblock %}
