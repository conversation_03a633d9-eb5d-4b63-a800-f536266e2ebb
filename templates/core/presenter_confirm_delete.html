{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Presenter - {{ presenter.get_full_name }}{% endblock %}

{% block content %}
<div class="max-w-md mx-auto mt-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <a href="{% url 'core:presenter_detail' presenter.pk %}" class="text-gray-500 hover:text-gray-700 mr-4">
                    <i class="fa-solid fa-arrow-left"></i>
                </a>
                <h1 class="text-xl font-semibold text-gray-900">Delete Presenter</h1>
            </div>
        </div>
        
        <div class="p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                    Are you sure you want to delete this presenter?
                </h3>
                
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <div class="flex items-center space-x-3">
                        {% if presenter.profile_picture %}
                            <img src="{{ presenter.profile_picture.url }}" alt="{{ presenter.get_full_name }}" class="w-12 h-12 rounded-full object-cover">
                        {% else %}
                            <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                                <i class="fa-solid fa-user text-gray-400"></i>
                            </div>
                        {% endif %}
                        <div class="text-left">
                            <p class="font-medium text-gray-900">{{ presenter.get_full_name }}</p>
                            {% if presenter.stage_name %}
                                <p class="text-sm text-gray-600">{{ presenter.stage_name }}</p>
                            {% endif %}
                            {% if presenter.email %}
                                <p class="text-sm text-gray-500">{{ presenter.email }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Warning
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>This action cannot be undone. Deleting this presenter will:</p>
                                <ul class="list-disc list-inside mt-2 space-y-1">
                                    <li>Remove them from all associated shows</li>
                                    <li>Remove them from any scheduled mentions</li>
                                    <li>Delete their profile information permanently</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form method="post" class="space-y-4">
                    {% csrf_token %}
                    <div class="flex justify-center space-x-3">
                        <a href="{% url 'core:presenter_detail' presenter.pk %}" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Cancel
                        </a>
                        <button type="submit" class="bg-red-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            <i class="fa-solid fa-trash mr-2"></i>
                            Delete Presenter
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
