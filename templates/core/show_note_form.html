<!-- Show Note Form Template -->
<form id="showNoteForm" method="post">
  {% csrf_token %}
  
  <!-- Show Information -->
  <div class="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
    <div class="flex items-center">
      <i class="fa-solid fa-radio mr-2 text-blue-600"></i>
      <div>
        <div class="font-medium text-blue-900">{{ show.name }}</div>
        <div class="text-sm text-blue-700">{{ show_date|date:'l, F j, Y' }}</div>
      </div>
    </div>
  </div>

  <!-- Form Fields -->
  <div class="space-y-4">
    <!-- Title -->
    <div>
      <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
        {{ form.title.label }}
      </label>
      {{ form.title }}
      {% if form.title.errors %}
        <div class="mt-1 text-sm text-red-600">
          {% for error in form.title.errors %}
            <div>{{ error }}</div>
          {% endfor %}
        </div>
      {% endif %}
    </div>

    <!-- Content -->
    <div>
      <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
        {{ form.content.label }}
      </label>
      {{ form.content }}
      {% if form.content.errors %}
        <div class="mt-1 text-sm text-red-600">
          {% for error in form.content.errors %}
            <div>{{ error }}</div>
          {% endfor %}
        </div>
      {% endif %}
    </div>

    <!-- Category and Priority Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Category -->
      <div>
        <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
          {{ form.category.label }}
        </label>
        {{ form.category }}
        {% if form.category.errors %}
          <div class="mt-1 text-sm text-red-600">
            {% for error in form.category.errors %}
              <div>{{ error }}</div>
            {% endfor %}
          </div>
        {% endif %}
      </div>

      <!-- Priority -->
      <div>
        <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
          {{ form.priority.label }}
        </label>
        {{ form.priority }}
        {% if form.priority.errors %}
          <div class="mt-1 text-sm text-red-600">
            {% for error in form.priority.errors %}
              <div>{{ error }}</div>
            {% endfor %}
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Status and Tags Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Status -->
      <div>
        <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
          {{ form.status.label }}
        </label>
        {{ form.status }}
        {% if form.status.errors %}
          <div class="mt-1 text-sm text-red-600">
            {% for error in form.status.errors %}
              <div>{{ error }}</div>
            {% endfor %}
          </div>
        {% endif %}
      </div>

      <!-- Tags -->
      <div>
        <label for="{{ form.tags_input.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
          Tags
        </label>
        {{ form.tags_input }}
        {% if form.tags_input.errors %}
          <div class="mt-1 text-sm text-red-600">
            {% for error in form.tags_input.errors %}
              <div>{{ error }}</div>
            {% endfor %}
          </div>
        {% endif %}
        <div class="mt-1 text-xs text-gray-500">{{ form.tags_input.help_text }}</div>
      </div>
    </div>

    <!-- Pin Checkbox -->
    <div class="flex items-center">
      {{ form.is_pinned }}
      <label for="{{ form.is_pinned.id_for_label }}" class="ml-2 text-sm text-gray-700">
        {{ form.is_pinned.label }}
      </label>
      {% if form.is_pinned.errors %}
        <div class="ml-2 text-sm text-red-600">
          {% for error in form.is_pinned.errors %}
            <div>{{ error }}</div>
          {% endfor %}
        </div>
      {% endif %}
    </div>
  </div>

  <!-- Form Actions -->
  <div class="mt-6 flex justify-end space-x-3">
    <button type="button" onclick="closeShowNoteModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
      Cancel
    </button>
    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
      {% if action == 'edit' %}Update Note{% else %}Create Note{% endif %}
    </button>
  </div>

  <!-- General Form Errors -->
  {% if form.non_field_errors %}
    <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
      {% for error in form.non_field_errors %}
        <div class="text-sm text-red-600">{{ error }}</div>
      {% endfor %}
    </div>
  {% endif %}
</form>
