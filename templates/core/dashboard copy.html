{% extends 'base.html' %}
{% load static %}

{% block title %}
  Dashboard
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p class="text-gray-600">Welcome back! Here's what's happening with your radio mentions today.</p>
        </div>
        <div class="flex items-center space-x-3">
          <span class="text-sm text-gray-500">{{ current_organization.name }}</span>
          <div class="flex items-center space-x-2">
            <a href="{% url 'mentions:mention_create' %}" class="bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors"><i class="fa-solid fa-plus mr-2"></i>New Mention</a>
            <a href="{% url 'mentions:calendar_interface' %}" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors"><i class="fa-solid fa-calendar mr-2"></i>Calendar</a>
          </div>
        </div>
      </div>
    </div>

    <!-- System Notifications -->
    <div id="dashboard-notifications" class="mb-6 space-y-3">
      <!-- Real-time notifications will be inserted here -->
    </div>

    <!-- Alerts Section -->
    {% if alerts %}
      <div class="mb-6 space-y-3">
        {% for alert in alerts %}
          <div class="p-4 rounded-lg border flex items-center {% if alert.type == 'error' %}bg-red-50 border-red-200 text-red-800{% elif alert.type == 'warning' %}bg-yellow-50 border-yellow-200 text-yellow-800{% else %}bg-blue-50 border-blue-200 text-blue-800{% endif %}">
            <i class="fas {% if alert.type == 'error' %}fa-exclamation-circle{% elif alert.type == 'warning' %}fa-exclamation-triangle{% else %}fa-info-circle{% endif %} mr-3"></i>
            <div class="flex-1">
              <h4 class="font-medium">{{ alert.title }}</h4>
              <p class="text-sm opacity-90">{{ alert.message }}</p>
            </div>
            {% if alert.action_url %}
              <a href="{{ alert.action_url }}" class="text-sm font-medium hover:underline ml-4">{{ alert.action_text }}</a>
            {% endif %}
            <button onclick="this.parentElement.remove()" class="ml-2 text-gray-500 hover:text-gray-700">
              <i class="fas fa-times"></i>
            </button>
          </div>
        {% endfor %}
      </div>
    {% endif %}
              
              
              
              
              
              
              
              
              
              
              bg-red-50 border-red-200 text-red-800










            {% elif alert.type == 'warning' %}
              
              
              
              
              
              
              
              
              
              
              bg-yellow-50 border-yellow-200 text-yellow-800










            {% else %}
              
              
              
              
              
              
              
              
              
              
              bg-blue-50 border-blue-200 text-blue-800










            {% endif %}">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <i class="fa-solid {% if alert.type == 'error' %}
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    fa-exclamation-circle










                  {% elif alert.type == 'warning' %}
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    fa-exclamation-triangle










                  {% else %}
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    fa-info-circle










                  {% endif %} mr-3">

                </i>
                <div>
                  <h4 class="font-medium">{{ alert.title }}</h4>
                  <p class="text-sm opacity-90">{{ alert.message }}</p>
                </div>
              </div>
              {% if alert.action_url %}
                <a href="{{ alert.action_url }}" class="text-sm font-medium hover:underline">{{ alert.action_text }}</a>
              {% endif %}
            </div>
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-sm font-medium text-gray-500">Total Mentions</h3>
          <span class="{% if stats.mentions_growth >= 0 %}
              
              
              
              
              
              
              
              
              
              text-green-500









            {% else %}
              
              
              
              
              
              
              
              
              
              text-red-500









            {% endif %} text-xs font-medium flex items-center">
            <i class="fa-solid fa-arrow-{% if stats.mentions_growth >= 0 %}
                
                
                
                
                
                
                
                
                
                up









              {% else %}
                
                
                
                
                
                
                
                
                
                down









              {% endif %} mr-1">

            </i>
            {{ stats.mentions_growth|floatformat:1 }}%
          </span>
        </div>
        <p class="text-2xl font-bold text-gray-800">{{ stats.total_mentions }}</p>
        <div class="mt-2 text-xs text-gray-500">
          <span>vs. {{ stats.last_month_mentions }} last month</span>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-sm font-medium text-gray-500">Pending Approvals</h3>
          <span class="{% if stats.pending_growth >= 0 %}
              
              
              
              
              
              
              
              
              text-red-500








            {% else %}
              
              
              
              
              
              
              
              
              text-green-500








            {% endif %} text-xs font-medium flex items-center">
            <i class="fa-solid fa-arrow-{% if stats.pending_growth >= 0 %}
                
                
                
                
                
                
                
                
                up








              {% else %}
                
                
                
                
                
                
                
                
                down








              {% endif %} mr-1">

            </i>
            {{ stats.pending_growth|floatformat:1 }}%
          </span>
        </div>
        <p class="text-2xl font-bold text-gray-800">{{ stats.pending_mentions }}</p>
        <div class="mt-2 text-xs text-gray-500">
          <span>vs. {{ stats.last_week_pending }} last week</span>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-sm font-medium text-gray-500">Active Clients</h3>
          <span class="{% if stats.clients_growth >= 0 %}
              
              
              
              
              
              
              
              text-green-500







            {% else %}
              
              
              
              
              
              
              
              text-red-500







            {% endif %} text-xs font-medium flex items-center">
            <i class="fa-solid fa-arrow-{% if stats.clients_growth >= 0 %}
                
                
                
                
                
                
                
                up







              {% else %}
                
                
                
                
                
                
                
                down







              {% endif %} mr-1">

            </i>
            {{ stats.clients_growth|floatformat:1 }}%
          </span>
        </div>
        <p class="text-2xl font-bold text-gray-800">{{ stats.active_clients }}</p>
        <div class="mt-2 text-xs text-gray-500">
          <span>vs. {{ stats.last_month_clients }} last month</span>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-sm font-medium text-gray-500">Today's Progress</h3>
          <span class="text-blue-500 text-xs font-medium">{{ stats.completion_rate }}%</span>
        </div>
        <p class="text-2xl font-bold text-gray-800">{{ stats.completed_today }}/{{ stats.scheduled_today }}</p>
        <div class="mt-2 text-xs text-gray-500">
          <span>across {{ stats.shows_today }} shows</span>
        </div>
        <div class="mt-3">
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-500 h-2 rounded-full" style="width: {{ stats.completion_rate }}%"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Weekly Activity</h3>
          <i class="fa-solid fa-chart-line text-gray-400"></i>
        </div>
        <div class="h-[250px]">
          <canvas id="weeklyChart"></canvas>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Status Distribution</h3>
          <i class="fa-solid fa-chart-pie text-gray-400"></i>
        </div>
        <div class="h-[250px]">
          <canvas id="statusChart"></canvas>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Top Clients</h3>
          <i class="fa-solid fa-chart-bar text-gray-400"></i>
        </div>
        <div class="h-[250px]">
          <canvas id="clientChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Activity and Schedule Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Recent Activity -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Recent Activity</h3>
          <a href="{% url 'mentions:mention_list' %}" class="text-primary-600 text-sm hover:text-primary-700">View All</a>
        </div>
        <div class="space-y-3">
          {% for activity in recent_activity|slice:':5' %}
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <i class="fa-solid fa-check text-green-600 text-xs"></i>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-900 truncate">{{ activity.mention.title }}</p>
                <p class="text-xs text-gray-500">{{ activity.show.name }} • {{ activity.actual_read_time|timesince }} ago</p>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-4">
              <i class="fa-solid fa-clock text-gray-300 text-2xl mb-2"></i>
              <p class="text-sm text-gray-500">No recent activity</p>
            </div>
          {% endfor %}
        </div>
      </div>

      <!-- Upcoming Schedule -->
      <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Upcoming Schedule</h3>
          <a href="{% url 'mentions:calendar_interface' %}" class="text-primary-600 text-sm hover:text-primary-700">View Calendar</a>
        </div>
        <div class="space-y-3">
          {% for reading in upcoming_schedule|slice:':6' %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="text-center">
                  <div class="text-xs text-gray-500">{{ reading.scheduled_date|date:'M' }}</div>
                  <div class="text-sm font-medium text-gray-900">{{ reading.scheduled_date|date:'d' }}</div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ reading.mention.title }}</p>
                  <p class="text-xs text-gray-500">{{ reading.show.name }} • {{ reading.scheduled_time|time:'g:i A' }}</p>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">{{ reading.mention.client.name }}</span>
                <a href="{% url 'mentions:reading_detail' reading.pk %}" class="text-primary-600 hover:text-primary-700"><i class="fa-solid fa-arrow-right text-xs"></i></a>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-8">
              <i class="fa-solid fa-calendar text-gray-300 text-3xl mb-3"></i>
              <p class="text-sm text-gray-500">No upcoming schedule</p>
            </div>
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- Today's Schedule -->
    <div class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-800">Today's Schedule</h3>
        <a href="{% url 'mentions:calendar' %}" class="text-primary-600 text-sm font-medium hover:text-primary-700">View Calendar</a>
      </div>
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Presenter</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for reading in todays_schedule|default:''|slice:':5' %}
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reading.scheduled_time|time:'g:i A'|default:'07:15 AM' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reading.show.name|default:'Morning Drive' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center mr-2">
                        <i class="fa-solid fa-user text-gray-400 text-xs"></i>
                      </div>
                      <span class="text-sm text-gray-900">{{ reading.presenter.get_full_name|default:'Mike Johnson' }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reading.mention.client.name|default:'Spotify Premium' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if reading.actual_read_time %}
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                    {% else %}
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Upcoming</span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <a href="{% url 'mentions:reading_detail' reading.pk %}" class="text-primary-600 hover:text-primary-900">View</a>
                  </td>
                </tr>
              {% empty %}
                <tr>
                  <td colspan="6" class="px-6 py-8 text-center">
                    <div class="flex flex-col items-center">
                      <i class="fa-solid fa-calendar text-gray-300 text-3xl mb-3"></i>
                      <p class="text-sm text-gray-500">No scheduled mentions for today</p>
                      <a href="{% url 'mentions:mention_create' %}" class="mt-2 text-primary-600 text-sm font-medium hover:text-primary-700">Create your first mention →</a>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Pending Approvals -->
    <div>
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-800">Pending Approvals</h3>
        <a href="{% url 'mentions:approval_workflow' %}" class="text-primary-600 text-sm font-medium hover:text-primary-700">View All</a>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {% for mention in pending_mentions|default:''|slice:':3' %}
          <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">
            <div class="flex justify-between items-start mb-4">
              <div>
                <h4 class="font-medium text-gray-900">{{ mention.title }}</h4>
                <p class="text-sm text-gray-500 mt-1">{{ mention.show.name }} - {{ mention.created_at|date:'m/d/Y' }}</p>
              </div>
              <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Waiting</span>
            </div>
            <p class="text-sm text-gray-600 mb-4">{{ mention.content|truncatewords:15 }}</p>
            <div class="flex items-center text-sm text-gray-500 mb-4">
              <i class="fa-regular fa-clock mr-1"></i>
              <span>Submitted {{ mention.created_at|timesince }} ago</span>
            </div>
            <div class="flex space-x-2">
              <button onclick="approveMention({{ mention.pk }})" class="px-3 py-1.5 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">Approve</button>
              <a href="{% url 'mentions:mention_edit' mention.pk %}" class="px-3 py-1.5 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50">Edit</a>
              <button onclick="rejectMention({{ mention.pk }})" class="px-3 py-1.5 bg-white border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50">Reject</button>
            </div>
          </div>
        {% empty %}
          <div class="col-span-full">
            <div class="text-center py-8">
              <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                <i class="fa-solid fa-check-circle text-gray-400 text-xl"></i>
              </div>
              <p class="text-gray-500">No pending approvals</p>
              <p class="text-sm text-gray-400 mt-1">All mentions are up to date!</p>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/chart.min.js' %}"></script>
  <script>
    // Chart data from backend
    const weeklyData = {
      labels: {{ chart_data.weekly_labels|safe }},
      datasets: [{
        label: 'Completed Mentions',
        data: {{ chart_data.weekly_data|safe }},
        backgroundColor: 'rgba(14, 165, 233, 0.1)',
        borderColor: 'rgba(14, 165, 233, 1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4
      }]
    };

    const statusData = {
      labels: {{ chart_data.status_labels|safe }},
      datasets: [{
        data: {{ chart_data.status_data|safe }},
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(234, 179, 8, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(239, 68, 68, 0.8)'
        ],
        borderWidth: 0
      }]
    };

    const clientData = {
      labels: {{ chart_data.client_labels|safe }},
      datasets: [{
        label: 'Mentions',
        data: {{ chart_data.client_data|safe }},
        backgroundColor: 'rgba(99, 102, 241, 0.8)',
        borderColor: 'rgba(99, 102, 241, 1)',
        borderWidth: 1
      }]
    };

    // Initialize charts
    const weeklyChart = new Chart(document.getElementById('weeklyChart'), {
      type: 'line',
      data: weeklyData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: { display: false }
          },
          x: {
            grid: { display: false }
          }
        }
      }
    });

    const statusChart = new Chart(document.getElementById('statusChart'), {
      type: 'doughnut',
      data: statusData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: { usePointStyle: true, padding: 15 }
          }
        },
        cutout: '60%'
      }
    });

    const clientChart = new Chart(document.getElementById('clientChart'), {
      type: 'bar',
      data: clientData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: { display: false }
          },
          x: {
            grid: { display: false }
          }
        }
      }
    });

    // Approval functionality
    function approveMention(mentionId) {
      if (confirm('Approve this mention?')) {
        fetch(`/mentions/${mentionId}/approve/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
          },
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            location.reload();
          } else {
            alert('Error: ' + data.error);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('An error occurred while approving the mention.');
        });
      }
    }

    function rejectMention(mentionId) {
      const reason = prompt('Reason for rejection (optional):');
      if (reason !== null) {
        fetch(`/mentions/${mentionId}/reject/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ reason: reason })
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            location.reload();
          } else {
            alert('Error: ' + data.error);
          }
        })
        .catch(error => {
          console.error('Error:', error);
          alert('An error occurred while rejecting the mention.');
        });
      }
    }

    // Auto-refresh dashboard every 5 minutes
    setInterval(() => {
      // Only refresh if user is still on the page
      if (document.visibilityState === 'visible') {
        location.reload();
      }
    }, 300000); // 5 minutes
  </script>
{% endblock %}
