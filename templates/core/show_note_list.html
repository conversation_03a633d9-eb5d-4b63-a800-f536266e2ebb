<!-- Show Notes List Template -->
<div id="showNotesList" class="space-y-3">
  <!-- Notes will be populated by JavaScript -->
</div>

<!-- Show Note Item Template (Hidden, used by JavaScript) -->
<template id="showNoteItemTemplate">
  <div class="show-note-item bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow" data-note-id="">
    <!-- Note Header -->
    <div class="flex items-start justify-between mb-2">
      <div class="flex-1">
        <div class="flex items-center space-x-2">
          <h4 class="note-title font-medium text-gray-900 truncate"></h4>
          <span class="note-pin-indicator hidden">
            <i class="fa-solid fa-thumbtack text-primary-600 text-xs" title="Pinned"></i>
          </span>
        </div>
        <div class="flex items-center space-x-3 mt-1">
          <span class="note-category text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full"></span>
          <span class="note-priority text-xs px-2 py-1 rounded-full"></span>
          <span class="note-status text-xs px-2 py-1 rounded-full"></span>
        </div>
      </div>
      
      <!-- Note Actions -->
      <div class="flex items-center space-x-1 ml-4">
        <button onclick="viewShowNote(this)" class="text-gray-400 hover:text-blue-600" title="View">
          <i class="fa-solid fa-eye text-sm"></i>
        </button>
        <button onclick="editShowNote(this)" class="text-gray-400 hover:text-green-600" title="Edit">
          <i class="fa-solid fa-edit text-sm"></i>
        </button>
        <button onclick="toggleShowNotePin(this)" class="text-gray-400 hover:text-primary-600" title="Toggle Pin">
          <i class="fa-solid fa-thumbtack text-sm"></i>
        </button>
        <button onclick="toggleShowNoteStatus(this)" class="text-gray-400 hover:text-orange-600" title="Toggle Status">
          <i class="fa-solid fa-check-circle text-sm"></i>
        </button>
        <button onclick="deleteShowNote(this)" class="text-gray-400 hover:text-red-600" title="Delete">
          <i class="fa-solid fa-trash text-sm"></i>
        </button>
      </div>
    </div>

    <!-- Note Content Preview -->
    <div class="note-content text-sm text-gray-600 line-clamp-2 mb-2"></div>

    <!-- Note Tags -->
    <div class="note-tags flex flex-wrap gap-1 mb-2"></div>

    <!-- Note Footer -->
    <div class="flex items-center justify-between text-xs text-gray-500">
      <span class="note-created-at"></span>
      <span class="note-updated-at"></span>
    </div>
  </div>
</template>

<!-- Empty State Template -->
<template id="showNotesEmptyTemplate">
  <div class="text-center py-8">
    <i class="fa-solid fa-note-sticky text-4xl text-gray-300 mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No notes for this show</h3>
    <p class="text-gray-500 mb-4">Create your first note to get started with show preparation.</p>
    <button onclick="createShowNote()" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
      <i class="fa-solid fa-plus mr-2"></i>
      Create Note
    </button>
  </div>
</template>

<style>
/* Priority color classes */
.priority-1 { @apply bg-gray-100 text-gray-800; }
.priority-2 { @apply bg-blue-100 text-blue-800; }
.priority-3 { @apply bg-orange-100 text-orange-800; }
.priority-4 { @apply bg-red-100 text-red-800; }

/* Status color classes */
.status-active { @apply bg-green-100 text-green-800; }
.status-completed { @apply bg-blue-100 text-blue-800; }
.status-archived { @apply bg-gray-100 text-gray-800; }

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
