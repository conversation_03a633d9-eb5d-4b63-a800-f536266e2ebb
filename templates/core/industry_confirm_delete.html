{% extends 'base.html' %}
{% load static %}

{% block title %}
  Delete Industry - {{ block.super }}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
        <a href="{% url 'core:industry_list' %}" class="hover:text-blue-600">Industries</a>
        <i class="fas fa-chevron-right text-xs"></i>
        <a href="{% url 'core:industry_detail' object.pk %}" class="hover:text-blue-600">{{ object.name }}</a>
        <i class="fas fa-chevron-right text-xs"></i>
        <span>Delete</span>
      </div>
      <h1 class="text-2xl font-bold text-gray-900">Delete Industry</h1>
    </div>

    <!-- Confirmation Form -->
    <div class="max-w-2xl">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <!-- Warning Icon -->
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
          </div>
          <div class="ml-4">
            <h2 class="text-lg font-semibold text-gray-900">Confirm Deletion</h2>
            <p class="text-sm text-gray-600">This action cannot be undone.</p>
          </div>
        </div>

        <!-- Industry Information -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 class="text-sm font-medium text-gray-900 mb-2">Industry to be deleted:</h3>
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <i class="fas fa-industry text-red-600 text-sm"></i>
              </div>
            </div>
            <div>
              <div class="text-sm font-medium text-gray-900">{{ object.name }}</div>
              <div class="text-sm text-gray-500">Code: {{ object.code }}</div>
            </div>
          </div>
        </div>

        <!-- Client Count Check -->
        {% if client_count > 0 %}
          <!-- Cannot Delete - Has Clients -->
          <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-times-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Cannot Delete Industry</h3>
                <div class="mt-2 text-sm text-red-700">
                  <p>This industry cannot be deleted because it is currently assigned to {{ client_count }} client{{ client_count|pluralize }}.</p>
                  <p class="mt-2">To delete this industry, you must first:</p>
                  <ul class="list-disc list-inside mt-1 space-y-1">
                    <li>Reassign all clients to other industries, or</li>
                    <li>Delete the clients that use this industry</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Show Affected Clients -->
          <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Clients using this industry:</h4>
            <div class="space-y-2 max-h-48 overflow-y-auto">
              {% for client in clients %}
                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div class="flex items-center space-x-2">
                    <i class="fas fa-building text-gray-400 text-sm"></i>
                    <span class="text-sm text-gray-900">{{ client.name }}</span>
                  </div>
                  <a href="{% url 'core:client_edit' client.pk %}" class="text-blue-600 hover:text-blue-800 text-sm">Edit <i class="fas fa-external-link-alt ml-1"></i></a>
                </div>
              {% endfor %}
            </div>
          </div>

          <!-- Actions for Cannot Delete -->
          <div class="flex justify-end space-x-3">
            <a href="{% url 'core:industry_detail' object.pk %}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium transition-colors">Back to Industry</a>
            <a href="{% url 'core:client_list' %}?industry={{ object.pk }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors"><i class="fas fa-users mr-2"></i>Manage Clients</a>
          </div>
        {% else %}
          <!-- Can Delete - No Clients -->
          <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-yellow-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>Are you sure you want to delete the industry "{{ object.name }}"?</p>
                  <p class="mt-1">This action cannot be undone, but since no clients are currently using this industry, it's safe to delete.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Deletion Form -->
          <form method="post">
            {% csrf_token %}
            <div class="flex justify-end space-x-3">
              <a href="{% url 'core:industry_detail' object.pk %}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium transition-colors">Cancel</a>
              <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium transition-colors"><i class="fas fa-trash mr-2"></i>Delete Industry</button>
            </div>
          </form>
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}
