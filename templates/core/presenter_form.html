{% extends 'base.html' %}

{% block title %}
    {% if object %}Edit Presenter{% else %}Add Presenter{% endif %} - RadioMention
{% endblock %}

{% block page_title %}
    {% if object %}Edit Presenter{% else %}Add Presenter{% endif %}
{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-800">
                {% if object %}Edit Presenter: {{ object.get_full_name }}{% else %}Add New Presenter{% endif %}
            </h3>
            <p class="text-gray-600">
                {% if object %}Update presenter information{% else %}Enter presenter details{% endif %}
            </p>
        </div>
        
        <form method="post" enctype="multipart/form-data" class="space-y-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        First Name *
                    </label>
                    {{ form.first_name }}
                    {% if form.first_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.first_name.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Last Name *
                    </label>
                    {{ form.last_name }}
                    {% if form.last_name.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.last_name.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div>
                <label for="{{ form.stage_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Stage Name
                </label>
                {{ form.stage_name }}
                {% if form.stage_name.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.stage_name.errors.0 }}
                    </div>
                {% endif %}
                <p class="text-xs text-gray-500 mt-1">The name used on-air (optional)</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Email *
                    </label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.email.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Phone
                    </label>
                    {{ form.phone }}
                    {% if form.phone.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {{ form.phone.errors.0 }}
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div>
                <label for="{{ form.experience_years.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Years of Experience
                </label>
                {{ form.experience_years }}
                {% if form.experience_years.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.experience_years.errors.0 }}
                    </div>
                {% endif %}
                <p class="text-xs text-gray-500 mt-1">Years of broadcasting experience</p>
            </div>
            
            <div>
                <label for="{{ form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Bio
                </label>
                {{ form.bio }}
                {% if form.bio.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.bio.errors.0 }}
                    </div>
                {% endif %}
                <p class="text-xs text-gray-500 mt-1">Brief biography or description</p>
            </div>
            
            <div>
                <label for="{{ form.profile_picture.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Profile Picture
                </label>
                {{ form.profile_picture }}
                {% if form.profile_picture.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.profile_picture.errors.0 }}
                    </div>
                {% endif %}
                {% if object.profile_picture %}
                    <div class="mt-2">
                        <img src="{{ object.profile_picture.url }}" alt="Current profile picture" class="w-16 h-16 rounded-full object-cover">
                        <p class="text-xs text-gray-500 mt-1">Current profile picture</p>
                    </div>
                {% endif %}
            </div>
            
            <div class="flex items-center">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-sm text-gray-700">
                    Active Presenter
                </label>
                {% if form.is_active.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.is_active.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="{% url 'core:presenter_list' %}" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md text-sm font-medium hover:bg-primary-700">
                    {% if object %}Update Presenter{% else %}Create Presenter{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>

<style>
/* Style form inputs */
input[type="text"], input[type="email"], input[type="number"], textarea, select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
}

input[type="text"]:focus, input[type="email"]:focus, input[type="number"]:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    color: #0ea5e9;
    border-radius: 0.25rem;
}

input[type="file"] {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    background-color: #f9fafb;
}

textarea {
    min-height: 4rem;
    resize: vertical;
}
</style>
{% endblock %}
