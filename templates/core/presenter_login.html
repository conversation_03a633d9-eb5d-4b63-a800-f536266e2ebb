{% load static %}
{% load account %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Presenter Sign In - RadioMention</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <script>
      window.FontAwesomeConfig = { autoReplaceSvg: 'nest' }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <!-- Custom Tailwind Config -->
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                50: '#eff6ff',
                100: '#dbeafe',
                200: '#bfdbfe',
                300: '#93c5fd',
                400: '#60a5fa',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a',
              }
            }
          }
        }
      }
    </script>

    <style>
      .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      .glass-effect {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.1);
      }
    </style>
  </head>

  <body class="min-h-screen gradient-bg">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
      <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
    </div>

    <div class="relative min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <!-- Login Container -->
      <div class="w-full max-w-6xl mx-auto">
        <div class="grid lg:grid-cols-2 gap-8 items-center">
          <!-- Left Side - Branding -->
          <div class="text-center lg:text-left text-white">
            <div class="mb-8">
              <div class="flex items-center justify-center lg:justify-start mb-6">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                  <i class="fa-solid fa-microphone text-3xl text-white"></i>
                </div>
                <div class="ml-4">
                  <h1 class="text-3xl font-bold">RadioMention</h1>
                  <p class="text-lg opacity-90">Presenter Dashboard</p>
                </div>
              </div>
              <h2 class="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
                Welcome Back<br />
                <span class="text-yellow-300">Presenter</span>
              </h2>
              <p class="text-xl opacity-90 mb-8">
                Sign in to access your presenter dashboard, manage your shows, and mark mentions during broadcasts.
              </p>
            </div>

            <!-- Features -->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                  <i class="fa-solid fa-calendar-check text-white"></i>
                </div>
                <span class="text-sm">View Your Schedule</span>
              </div>
              <div class="flex items-center">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                  <i class="fa-solid fa-check-circle text-white"></i>
                </div>
                <span class="text-sm">Mark Mentions Complete</span>
              </div>
              <div class="flex items-center">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                  <i class="fa-solid fa-chart-line text-white"></i>
                </div>
                <span class="text-sm">Track Performance</span>
              </div>
              <div class="flex items-center">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                  <i class="fa-solid fa-users text-white"></i>
                </div>
                <span class="text-sm">Manage Shows</span>
              </div>
            </div>
          </div>

          <!-- Right Side - Login Form -->
          <div class="w-full max-w-md mx-auto lg:mx-0">
            <div class="glass-effect rounded-2xl p-8 shadow-2xl border border-white border-opacity-20">
              <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-white mb-2">Presenter Sign In</h3>
                <p class="text-white text-opacity-80">Access your presenter dashboard</p>
              </div>

              <!-- Login Form -->
              <form method="post" action="{% url 'account_login' %}" class="space-y-6">
                {% csrf_token %}
                
                <!-- Hidden field to redirect to presenter dashboard after login -->
                <input type="hidden" name="next" value="{% url 'core:my_presenter_dashboard' %}" />

                <!-- Username Field -->
                <div>
                  <label for="login" class="block text-sm font-medium text-white mb-2">Username</label>
                  <div class="relative">
                    <input type="text" id="login" name="login" required class="w-full px-4 py-3 pl-11 bg-white bg-opacity-10 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-60 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent backdrop-blur-sm" placeholder="Enter your username" />
                    <i class="fa-solid fa-user absolute left-3 top-1/2 transform -translate-y-1/2 text-white text-opacity-60"></i>
                  </div>
                </div>

                <!-- Password Field -->
                <div>
                  <label for="password" class="block text-sm font-medium text-white mb-2">Password</label>
                  <div class="relative">
                    <input type="password" id="password" name="password" required class="w-full px-4 py-3 pl-11 bg-white bg-opacity-10 border border-white border-opacity-30 rounded-lg text-white placeholder-white placeholder-opacity-60 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 focus:border-transparent backdrop-blur-sm" placeholder="Enter your password" />
                    <i class="fa-solid fa-lock absolute left-3 top-1/2 transform -translate-y-1/2 text-white text-opacity-60"></i>
                  </div>
                </div>

                <!-- Remember Me & Forgot Password -->
                <div class="flex items-center justify-between">
                  <label class="flex items-center">
                    <input type="checkbox" name="remember" class="w-4 h-4 text-white border-white border-opacity-30 rounded focus:ring-white focus:ring-opacity-50 bg-white bg-opacity-10" />
                    <span class="ml-2 text-sm text-white text-opacity-80">Remember me</span>
                  </label>
                  <a href="{% url 'account_reset_password' %}" class="text-sm text-white text-opacity-80 hover:text-white">Forgot password?</a>
                </div>

                <!-- Sign In Button -->
                <button type="submit" class="w-full bg-white bg-opacity-20 text-white py-3 px-4 rounded-lg font-medium hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-all duration-200 backdrop-blur-sm border border-white border-opacity-30">
                  <i class="fa-solid fa-sign-in-alt mr-2"></i>
                  Sign In to Dashboard
                </button>
              </form>

              <!-- Error Messages -->
              {% if form.errors %}
                <div class="mt-4 p-4 bg-red-500 bg-opacity-20 border border-red-400 border-opacity-30 rounded-lg">
                  {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                      <p class="text-red-200 text-sm">{{ error }}</p>
                    {% endfor %}
                  {% endfor %}
                </div>
              {% endif %}

              <!-- Additional Links -->
              <div class="mt-6 text-center">
                <p class="text-white text-opacity-60 text-sm">
                  Need help? 
                  <a href="mailto:<EMAIL>" class="text-white text-opacity-80 hover:text-white">Contact Support</a>
                </p>
                <div class="mt-4 pt-4 border-t border-white border-opacity-20">
                  <a href="{% url 'account_login' %}" class="text-white text-opacity-80 hover:text-white text-sm">
                    <i class="fa-solid fa-arrow-left mr-1"></i>
                    Back to Main Login
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Background Animation -->
    <script>
      // Add subtle floating animation to background elements
      document.addEventListener('DOMContentLoaded', function() {
        const elements = document.querySelectorAll('.glass-effect');
        elements.forEach((el, index) => {
          el.style.animation = `float ${3 + index}s ease-in-out infinite`;
        });
      });
    </script>

    <style>
      @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }
    </style>
  </body>
</html>
