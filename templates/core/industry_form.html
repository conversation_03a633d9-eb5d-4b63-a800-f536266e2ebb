{% extends 'base.html' %}
{% load static %}

{% block title %}
  {% if object %}
    Edit Industry
  {% else %}
    Add Industry
  {% endif %}- {{ block.super }}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
        <a href="{% url 'core:industry_list' %}" class="hover:text-blue-600">Industries</a>
        <i class="fas fa-chevron-right text-xs"></i>
        <span>
          {% if object %}
            Edit Industry
          {% else %}
            Add Industry
          {% endif %}
        </span>
      </div>
      <h1 class="text-2xl font-bold text-gray-900">
        {% if object %}
          Edit Industry
        {% else %}
          Add New Industry
        {% endif %}
      </h1>
      <p class="text-gray-600 mt-1">
        {% if object %}
          Update the industry information below.
        {% else %}
          Create a new industry category for your clients.
        {% endif %}
      </p>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <form method="post" class="p-6">
        {% csrf_token %}

        {% if form.non_field_errors %}
          <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc list-inside space-y-1">
                    {% for error in form.non_field_errors %}
                      <li>{{ error }}</li>
                    {% endfor %}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        {% endif %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Industry Name -->
          <div class="md:col-span-2">
            <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Industry Name <span class="text-red-500">*</span></label>
            {{ form.name }}
            {% if form.name.errors %}
              <div class="mt-1 text-sm text-red-600">
                {% for error in form.name.errors %}
                  <p>{{ error }}</p>
                {% endfor %}
              </div>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">The display name for this industry (e.g., "Healthcare", "Technology")</p>
          </div>

          <!-- Industry Code -->
          <div class="md:col-span-2">
            <label for="{{ form.code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Industry Code <span class="text-red-500">*</span></label>
            {{ form.code }}
            {% if form.code.errors %}
              <div class="mt-1 text-sm text-red-600">
                {% for error in form.code.errors %}
                  <p>{{ error }}</p>
                {% endfor %}
              </div>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">A unique code for this industry (e.g., "healthcare", "technology"). Will be automatically formatted.</p>
          </div>

          <!-- Description -->
          <div class="md:col-span-2">
            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
            {{ form.description }}
            {% if form.description.errors %}
              <div class="mt-1 text-sm text-red-600">
                {% for error in form.description.errors %}
                  <p>{{ error }}</p>
                {% endfor %}
              </div>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">Optional description of this industry category</p>
          </div>

          <!-- Color -->
          <div class="md:col-span-1">
            <label for="{{ form.color.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Industry Color</label>
            <div class="flex items-center space-x-3">
              {{ form.color }}
              <span class="text-sm text-gray-500">Choose a color to represent this industry</span>
            </div>
            {% if form.color.errors %}
              <div class="mt-1 text-sm text-red-600">
                {% for error in form.color.errors %}
                  <p>{{ error }}</p>
                {% endfor %}
              </div>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">This color will be used to highlight mentions from clients in this industry</p>
          </div>

          <!-- Status -->
          <div class="md:col-span-2">
            <div class="flex items-center">
              {{ form.is_active }}
              <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-sm font-medium text-gray-700">Active</label>
            </div>
            {% if form.is_active.errors %}
              <div class="mt-1 text-sm text-red-600">
                {% for error in form.is_active.errors %}
                  <p>{{ error }}</p>
                {% endfor %}
              </div>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">Inactive industries won't be available when creating or editing clients</p>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 mt-6">
          <a href="{% url 'core:industry_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md font-medium transition-colors">Cancel</a>
          <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
            <i class="fas fa-save mr-2"></i>
            {% if object %}
              Update Industry
            {% else %}
              Create Industry
            {% endif %}
          </button>
        </div>
      </form>
    </div>

    <!-- Help Section -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">Industry Management Tips</h3>
          <div class="mt-2 text-sm text-blue-700">
            <ul class="list-disc list-inside space-y-1">
              <li>Industry codes should be unique and descriptive (e.g., "healthcare", "automotive")</li>
              <li>Use clear, professional names that your team will easily recognize</li>
              <li>Inactive industries won't appear in client forms but existing assignments remain</li>
              <li>You can't delete industries that are currently assigned to clients</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Auto-format the code field as user types
      const nameField = document.getElementById('{{ form.name.id_for_label }}')
      const codeField = document.getElementById('{{ form.code.id_for_label }}')
    
      if (nameField && codeField && !codeField.value) {
        nameField.addEventListener('input', function () {
          // Auto-generate code from name if code is empty
          const name = this.value
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '') // Remove special characters
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .replace(/_+/g, '_') // Replace multiple underscores with single
            .replace(/^_|_$/g, '') // Remove leading/trailing underscores
    
          if (!codeField.value || codeField.dataset.autoGenerated === 'true') {
            codeField.value = name
            codeField.dataset.autoGenerated = 'true'
          }
        })
    
        codeField.addEventListener('input', function () {
          // Mark as manually edited if user types in code field
          if (this.value) {
            this.dataset.autoGenerated = 'false'
          }
        })
      }
    })
  </script>
{% endblock %}
