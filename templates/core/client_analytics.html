{% extends 'base.html' %}
{% load static %}

{% block title %}
  Client Analytics
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Client Analytics</h1>
            <p class="text-gray-600 mt-1">Detailed analytics and insights for client performance</p>
          </div>
          <div class="flex space-x-3">
            <select class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
              <option value="365">Last year</option>
            </select>
            <button class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 flex items-center">
              <i class="fa-solid fa-download mr-2"></i>
              Export Report
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Clients</p>
            <p class="text-2xl font-bold text-gray-800">{{ total_clients|default:86 }}</p>
          </div>
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-building text-blue-600"></i>
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500">
          <span class="text-green-500">+5%</span> from last month
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Active Clients</p>
            <p class="text-2xl font-bold text-gray-800">{{ active_clients|default:72 }}</p>
          </div>
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-check-circle text-green-600"></i>
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500">
          <span class="text-green-500">+8%</span> from last month
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Total Mentions</p>
            <p class="text-2xl font-bold text-gray-800">{{ total_mentions|default:1248 }}</p>
          </div>
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-microphone text-purple-600"></i>
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500">
          <span class="text-green-500">+12%</span> from last month
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">Avg. Mentions/Client</p>
            <p class="text-2xl font-bold text-gray-800">{{ avg_mentions|default:14.5 }}</p>
          </div>
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-chart-line text-orange-600"></i>
          </div>
        </div>
        <div class="mt-2 text-xs text-gray-500">
          <span class="text-green-500">+3%</span> from last month
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Client Activity Chart -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Client Activity Over Time</h3>
          <select class="text-sm border-gray-300 rounded-md text-gray-500">
            <option>Last 30 days</option>
            <option>Last 90 days</option>
            <option>Last year</option>
          </select>
        </div>
        <div class="h-[300px]">
          <canvas id="activityChart"></canvas>
        </div>
      </div>

      <!-- Industry Distribution -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Clients by Industry</h3>
          <button class="text-sm text-gray-500 hover:text-gray-700"><i class="fa-solid fa-expand"></i></button>
        </div>
        <div class="h-[300px]">
          <canvas id="industryChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Top Clients Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-800">Top Performing Clients</h3>
          <div class="flex items-center space-x-2">
            <select class="text-sm border-gray-300 rounded-md text-gray-500">
              <option>By Mentions</option>
              <option>By Revenue</option>
              <option>By Frequency</option>
            </select>
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rank</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Industry</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Mentions</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">This Month</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Growth</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {% for client in top_clients|default:''|slice:':10' %}
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-900">#{{ forloop.counter }}</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                      <i class="fa-solid fa-building text-gray-400 text-xs"></i>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ client.name|default:'Tech Solutions Inc.' }}</div>
                      <div class="text-sm text-gray-500">{{ client.contact_person|default:'John Smith' }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{{ client.industry|default:'Technology' }}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ client.mention_count|default:45 }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ client.this_month_count|default:8 }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="text-green-600 text-sm font-medium">
                    <i class="fa-solid fa-arrow-up mr-1"></i>
                    {{ client.growth|default:'+15%' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <a href="{% url 'core:client_detail' client.pk %}" class="text-primary-600 hover:text-primary-900 mr-3">View</a>
                  <button onclick="viewAnalytics({{ client.pk }})" class="text-gray-600 hover:text-gray-900">Analytics</button>
                </td>
              </tr>
            {% empty %}
              <!-- Sample data -->
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="text-sm font-medium text-gray-900">#1</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                      <i class="fa-solid fa-building text-gray-400 text-xs"></i>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">Tech Solutions Inc.</div>
                      <div class="text-sm text-gray-500">John Smith</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Technology</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">45</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="text-green-600 text-sm font-medium">
                    <i class="fa-solid fa-arrow-up mr-1"></i>
                    +15%
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button class="text-primary-600 hover:text-primary-900 mr-3">View</button>
                  <button onclick="viewAnalytics(1)" class="text-gray-600 hover:text-gray-900">Analytics</button>
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Client Insights -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Recent Activity -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 class="font-medium text-gray-800 mb-4">Recent Client Activity</h3>
        <div class="space-y-3">
          <div class="flex items-center">
            <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
            <div class="flex-1">
              <p class="text-sm text-gray-900">New client registered</p>
              <p class="text-xs text-gray-500">2 hours ago</p>
            </div>
          </div>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
            <div class="flex-1">
              <p class="text-sm text-gray-900">Mention scheduled</p>
              <p class="text-xs text-gray-500">4 hours ago</p>
            </div>
          </div>
          <div class="flex items-center">
            <div class="w-2 h-2 bg-orange-500 rounded-full mr-3"></div>
            <div class="flex-1">
              <p class="text-sm text-gray-900">Client updated profile</p>
              <p class="text-xs text-gray-500">1 day ago</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Industries -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 class="font-medium text-gray-800 mb-4">Top Industries</h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Technology</span>
            <span class="text-sm font-medium text-gray-900">24 clients</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Healthcare</span>
            <span class="text-sm font-medium text-gray-900">18 clients</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Retail</span>
            <span class="text-sm font-medium text-gray-900">15 clients</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Finance</span>
            <span class="text-sm font-medium text-gray-900">12 clients</span>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 class="font-medium text-gray-800 mb-4">Quick Actions</h3>
        <div class="space-y-3">
          <a href="{% url 'core:client_create' %}" class="block w-full px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 text-center">
            <i class="fa-solid fa-plus mr-2"></i>
            Add New Client
          </a>
          <button class="block w-full px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50">
            <i class="fa-solid fa-download mr-2"></i>
            Export Client List
          </button>
          <button class="block w-full px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50">
            <i class="fa-solid fa-envelope mr-2"></i>
            Send Newsletter
          </button>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/chart.min.js' %}"></script>
  <script>
    // Sample data for charts
    const activityData = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          label: 'New Clients',
          data: [12, 19, 8, 15, 22, 18],
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4
        },
        {
          label: 'Active Clients',
          data: [65, 68, 70, 72, 75, 78],
          borderColor: 'rgb(34, 197, 94)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          tension: 0.4
        }
      ]
    }
    
    const industryData = {
      labels: ['Technology', 'Healthcare', 'Retail', 'Finance', 'Education', 'Other'],
      datasets: [
        {
          data: [24, 18, 15, 12, 8, 9],
          backgroundColor: ['rgba(59, 130, 246, 0.8)', 'rgba(34, 197, 94, 0.8)', 'rgba(234, 179, 8, 0.8)', 'rgba(239, 68, 68, 0.8)', 'rgba(168, 85, 247, 0.8)', 'rgba(156, 163, 175, 0.8)']
        }
      ]
    }
    
    // Initialize charts
    const activityChart = new Chart(document.getElementById('activityChart'), {
      type: 'line',
      data: activityData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: { y: { beginAtZero: true } }
      }
    })
    
    const industryChart = new Chart(document.getElementById('industryChart'), {
      type: 'doughnut',
      data: industryData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { position: 'bottom' } }
      }
    })
    
    function viewAnalytics(clientId) {
      // Navigate to detailed client analytics
      window.location.href = `/clients/${clientId}/analytics/`
    }
  </script>
{% endblock %}
