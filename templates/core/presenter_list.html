{% extends 'base.html' %}
{% load static %}

{% block title %}Presenters{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">Presenters</h1>
                    <p class="text-sm text-gray-500 mt-1">Manage radio show presenters and their profiles</p>
                </div>
                {% if user.is_staff %}
                <div class="flex space-x-3">
                    <p class="text-sm text-gray-600 px-4 py-2 bg-blue-50 rounded-md border border-blue-200">
                        <i class="fa-solid fa-info-circle mr-2"></i>
                        Presenters are created automatically when users are assigned the "presenter" role
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" id="search" name="search" value="{{ search_query }}" 
                           placeholder="Search presenters..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
                
                <div>
                    <label for="organization" class="block text-sm font-medium text-gray-700 mb-1">Organization</label>
                    <select id="organization" name="organization" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Organizations</option>
                        {% for org in organizations %}
                            <option value="{{ org.id }}" {% if org.id|stringformat:'s' == org_filter %}selected{% endif %}>
                                {{ org.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Status</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactive</option>
                        <option value="available" {% if status_filter == 'available' %}selected{% endif %}>Available</option>
                    </select>
                </div>
                
                <div>
                    <label for="contract" class="block text-sm font-medium text-gray-700 mb-1">Contract Type</label>
                    <select id="contract" name="contract" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Types</option>
                        {% for value, label in contract_types %}
                            <option value="{{ value }}" {% if value == contract_filter %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="w-full px-4 py-2 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700">
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Presenters List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Presenter</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organization</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contract</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shows</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hire Date</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for presenter in page_obj %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        {% if presenter.profile_image %}
                                            <img class="h-10 w-10 rounded-full object-cover" src="{{ presenter.profile_image.url }}" alt="{{ presenter.stage_name }}">
                                        {% else %}
                                            <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                                <i class="fa-solid fa-microphone text-gray-400"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ presenter.stage_name }}</div>
                                        <div class="text-sm text-gray-500">{{ presenter.full_name }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ presenter.organization.name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    {% if presenter.contract_type == 'full_time' %}
                                        bg-green-100 text-green-800
                                    {% elif presenter.contract_type == 'part_time' %}
                                        bg-blue-100 text-blue-800
                                    {% elif presenter.contract_type == 'freelance' %}
                                        bg-yellow-100 text-yellow-800
                                    {% else %}
                                        bg-gray-100 text-gray-800
                                    {% endif %}">
                                    {{ presenter.get_contract_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ presenter.shows_count }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex space-x-1">
                                    {% if presenter.is_active %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Inactive
                                        </span>
                                    {% endif %}
                                    {% if presenter.is_available %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Available
                                        </span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if presenter.hire_date %}
                                    {{ presenter.hire_date|date:"M d, Y" }}
                                {% else %}
                                    Not set
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{% url 'core:presenter_detail' presenter.pk %}" class="text-primary-600 hover:text-primary-900" title="View Presenter">
                                        <i class="fa-solid fa-eye"></i>
                                    </a>
                                    {% if user.is_staff %}
                                        <a href="{% url 'core:presenter_edit' presenter.pk %}" class="text-gray-600 hover:text-gray-900" title="Edit Presenter">
                                            <i class="fa-solid fa-edit"></i>
                                        </a>
                                        <button onclick="deletePresenter({{ presenter.pk }})" class="text-red-600 hover:text-red-900" title="Delete Presenter">
                                            <i class="fa-solid fa-trash"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fa-solid fa-microphone text-4xl mb-4"></i>
                                    <p class="text-lg font-medium">No presenters found</p>
                                    <p class="text-sm">{% if user.is_staff %}Presenters are created automatically when users are assigned the "presenter" role.{% else %}No presenters match your search criteria.{% endif %}</p>
                                    {% if user.is_staff %}
                                        <div class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                                            <p class="text-sm text-blue-800">
                                                <i class="fa-solid fa-info-circle mr-2"></i>
                                                To add presenters, go to <strong>User Management</strong> and assign users the "presenter" role.
                                            </p>
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fa-solid fa-chevron-left"></i>
                                </a>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">
                                        {{ num }}
                                    </span>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                        {{ num }}
                                    </a>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <i class="fa-solid fa-chevron-right"></i>
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
function deletePresenter(presenterId) {
    if (confirm('Are you sure you want to delete this presenter? This action cannot be undone.')) {
        fetch(`/presenters/${presenterId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error deleting presenter: ' + error);
        });
    }
}
</script>
{% endblock %}
