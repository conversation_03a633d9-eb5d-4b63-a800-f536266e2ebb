{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
                <p class="text-gray-600">Quickly create a simple note with minimal details.</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'core:personal_notes_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Notes
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Note Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fa-solid fa-bolt text-yellow-500 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">Quick Note</h3>
                    <p class="text-sm text-gray-600">Create a note with just the essentials</p>
                </div>
            </div>
        </div>
        
        <form method="post" class="p-6 space-y-6" id="quickNoteForm">
            {% csrf_token %}
            
            <!-- Title -->
            <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Title
                    <span class="text-red-500">*</span>
                </label>
                {{ form.title }}
                {% if form.title.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.title.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Content -->
            <div>
                <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Content
                    <span class="text-red-500">*</span>
                </label>
                {{ form.content }}
                {% if form.content.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.content.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Priority -->
            <div>
                <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Priority
                </label>
                {{ form.priority }}
                {% if form.priority.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.priority.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa-solid fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc list-inside space-y-1">
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Submit Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="text-sm text-gray-500">
                    <i class="fa-solid fa-info-circle mr-1"></i>
                    This will create a note with default settings (General category, Active status)
                </div>
                
                <div class="flex items-center space-x-3">
                    <a href="{% url 'core:personal_notes_list' %}" 
                       class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium">
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
                            id="submitBtn">
                        <i class="fa-solid fa-bolt mr-2"></i>
                        Create Quick Note
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Quick Tips -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fa-solid fa-lightbulb text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">
                    Quick Note Tips
                </h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Quick notes are created with default settings for speed</li>
                        <li>You can always edit the note later to add more details</li>
                        <li>Use keyboard shortcuts: Ctrl+Enter to submit, Escape to cancel</li>
                        <li>For more detailed notes, use the <a href="{% url 'core:personal_note_create' %}" class="underline hover:no-underline">full note form</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('quickNoteForm');
    const titleInput = document.querySelector('input[name="title"]');
    const contentTextarea = document.querySelector('textarea[name="content"]');
    const submitBtn = document.getElementById('submitBtn');
    
    // Auto-resize textarea
    if (contentTextarea) {
        contentTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
        
        // Initial resize
        contentTextarea.style.height = contentTextarea.scrollHeight + 'px';
    }
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+Enter to submit
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            form.submit();
        }
        
        // Escape to cancel
        if (e.key === 'Escape') {
            window.location.href = "{% url 'core:personal_notes_list' %}";
        }
    });
    
    // Focus on title input
    if (titleInput) {
        titleInput.focus();
    }
    
    // Form validation feedback
    form.addEventListener('submit', function(e) {
        const title = titleInput.value.trim();
        const content = contentTextarea.value.trim();
        
        if (!title || !content) {
            e.preventDefault();
            
            // Show validation messages
            if (!title) {
                showFieldError(titleInput, 'Title is required');
            }
            if (!content) {
                showFieldError(contentTextarea, 'Content is required');
            }
            
            return false;
        }
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Creating...';
    });
    
    function showFieldError(field, message) {
        // Remove existing error
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error mt-1 text-sm text-red-600';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
        
        // Add error styling to field
        field.classList.add('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
        
        // Remove error styling when user starts typing
        field.addEventListener('input', function() {
            field.classList.remove('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
            if (existingError) {
                existingError.remove();
            }
        }, { once: true });
    }
});
</script>
{% endblock %}
