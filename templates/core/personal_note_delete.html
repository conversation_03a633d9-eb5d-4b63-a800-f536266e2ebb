{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Note - {{ note.title }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Delete Note</h1>
                <p class="text-gray-600">This action cannot be undone.</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'core:personal_note_detail' note.pk %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Note
                </a>
            </div>
        </div>
    </div>

    <!-- Confirmation Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 bg-red-50">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fa-solid fa-exclamation-triangle text-red-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-red-800">
                        Confirm Deletion
                    </h3>
                    <p class="text-sm text-red-700">
                        Are you sure you want to delete this note? This action cannot be undone.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            <!-- Note Preview -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="flex items-start justify-between mb-3">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            {% if note.is_pinned %}
                                <i class="fa-solid fa-thumbtack text-yellow-500" title="Pinned"></i>
                            {% endif %}
                            
                            <h4 class="text-lg font-medium text-gray-900">{{ note.title }}</h4>
                            
                            <!-- Priority Badge -->
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ note.priority_display.class }}">
                                {{ note.priority_display.label }}
                            </span>
                            
                            <!-- Category Badge -->
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {{ note.get_category_display }}
                            </span>
                        </div>
                        
                        <p class="text-gray-600 mb-3">{{ note.content|truncatechars:200 }}</p>
                        
                        <div class="flex items-center text-sm text-gray-500 space-x-4">
                            <span>
                                <i class="fa-solid fa-calendar mr-1"></i>
                                Created {{ note.created_at|date:"M d, Y" }}
                            </span>
                            
                            {% if note.due_date %}
                                <span>
                                    <i class="fa-solid fa-clock mr-1"></i>
                                    Due: {{ note.due_date|date:"M d, Y H:i" }}
                                </span>
                            {% endif %}
                            
                            {% if note.tags %}
                                <div class="flex items-center space-x-1">
                                    <i class="fa-solid fa-tags mr-1"></i>
                                    {% for tag in note.tags %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ tag }}
                                        </span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warning Message -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">
                            Warning
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <ul class="list-disc list-inside space-y-1">
                                <li>This note will be permanently deleted from your account</li>
                                <li>All content, tags, and metadata will be lost</li>
                                <li>This action cannot be undone or recovered</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Confirmation Form -->
            <form method="post">
                {% csrf_token %}
                
                <div class="flex items-center justify-end space-x-3">
                    <a href="{% url 'core:personal_note_detail' note.pk %}" 
                       class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium">
                        <i class="fa-solid fa-times mr-2"></i>
                        Cancel
                    </a>
                    
                    <button type="submit" 
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
                            onclick="return confirm('Are you absolutely sure you want to delete this note? This action cannot be undone.')">
                        <i class="fa-solid fa-trash mr-2"></i>
                        Delete Note
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Alternative Actions -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fa-solid fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">
                    Alternative Actions
                </h3>
                <div class="mt-2 text-sm text-blue-700">
                    <p class="mb-2">Instead of deleting, you might want to:</p>
                    <ul class="list-disc list-inside space-y-1">
                        <li>
                            <a href="{% url 'core:personal_note_edit' note.pk %}" class="underline hover:no-underline">
                                Edit the note
                            </a> to update its content
                        </li>
                        <li>
                            <a href="{% url 'core:toggle_note_status' note.pk %}" class="underline hover:no-underline">
                                Archive the note
                            </a> to hide it from active view
                        </li>
                        <li>
                            <a href="{% url 'core:personal_note_edit' note.pk %}" class="underline hover:no-underline">
                                Change its category or priority
                            </a> for better organization
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcut for cancel (Escape key)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            window.location.href = "{% url 'core:personal_note_detail' note.pk %}";
        }
    });
    
    // Focus on cancel button by default for safety
    const cancelButton = document.querySelector('a[href*="personal_note_detail"]');
    if (cancelButton) {
        cancelButton.focus();
    }
});
</script>
{% endblock %}
