{% extends 'base.html' %}

{% block title %}
  {{ client.name }} - Client Details - RadioMention
{% endblock %}

{% block page_title %}
  <div class="flex items-center">
    <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
      <i class="fa-solid fa-building text-primary-600 text-xl"></i>
    </div>
    <div>
      <h1 class="text-2xl font-bold text-gray-900">{{ client.name }}</h1>
      <p class="text-sm text-gray-500">{{ client.industry|default:'Client' }} • Member since {{ client.created_at|date:'M Y' }}</p>
    </div>
  </div>
{% endblock %}

{% block header_actions %}
  <div class="flex space-x-3">
    <a href="{% url 'mentions:mention_create' %}?client={{ client.pk }}" class="px-4 py-2 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 flex items-center">
      <i class="fa-solid fa-plus mr-2"></i>
      New Mention
    </a>
    <a href="{% url 'core:client_edit' client.pk %}" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
      <i class="fa-solid fa-edit mr-2"></i>
      Edit Client
    </a>
    <a href="{% url 'core:client_list' %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 flex items-center">
      <i class="fa-solid fa-arrow-left mr-2"></i>
      Back to List
    </a>
  </div>
{% endblock %}

{% block content %}
  <!-- Statistics Overview -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-bullhorn text-blue-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Total Mentions</p>
          <p class="text-2xl font-bold text-gray-900">{{ mention_stats.total }}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-check-circle text-green-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Completed</p>
          <p class="text-2xl font-bold text-gray-900">{{ mention_stats.completed }}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-clock text-yellow-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">Pending</p>
          <p class="text-2xl font-bold text-gray-900">{{ mention_stats.pending }}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-calendar text-purple-600"></i>
          </div>
        </div>
        <div class="ml-4">
          <p class="text-sm font-medium text-gray-500">This Month</p>
          <p class="text-2xl font-bold text-gray-900">{{ mention_stats.this_month }}</p>
        </div>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Client Information -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-800 flex items-center">
            <i class="fa-solid fa-info-circle text-gray-400 mr-2"></i>
            Client Information
          </h3>
          {% if client.is_active %}
            <span class="px-3 py-1 text-sm font-medium rounded-full bg-green-100 text-green-800"><i class="fa-solid fa-check mr-1"></i>Active</span>
          {% else %}
            <span class="px-3 py-1 text-sm font-medium rounded-full bg-red-100 text-red-800"><i class="fa-solid fa-times mr-1"></i>Inactive</span>
          {% endif %}
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-500 mb-1">Client Name</label>
              <p class="text-lg font-semibold text-gray-900">{{ client.name }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-500 mb-1">Industry</label>
              <p class="text-gray-900">
                {% if client.industry %}
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{{ client.get_industry_display }}</span>
                {% else %}
                  <span class="text-gray-400">Not specified</span>
                {% endif %}
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-500 mb-1">Contact Person</label>
              <p class="text-gray-900 flex items-center">
                <i class="fa-solid fa-user text-gray-400 mr-2"></i>
                {{ client.contact_person }}
              </p>
            </div>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-500 mb-1">Email</label>
              <p class="text-gray-900">
                <a href="mailto:{{ client.email }}" class="text-primary-600 hover:text-primary-700 flex items-center">
                  <i class="fa-solid fa-envelope text-gray-400 mr-2"></i>
                  {{ client.email }}
                </a>
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-500 mb-1">Phone</label>
              <p class="text-gray-900">
                <a href="tel:{{ client.phone }}" class="text-primary-600 hover:text-primary-700 flex items-center">
                  <i class="fa-solid fa-phone text-gray-400 mr-2"></i>
                  {{ client.phone }}
                </a>
              </p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-500 mb-1">Member Since</label>
              <p class="text-gray-900 flex items-center">
                <i class="fa-solid fa-calendar-plus text-gray-400 mr-2"></i>
                {{ client.created_at|date:'F d, Y' }}
              </p>
            </div>
          </div>
        </div>

        {% if client.address %}
          <div class="mt-6 pt-6 border-t border-gray-200">
            <label class="block text-sm font-medium text-gray-500 mb-2">Address</label>
            <p class="text-gray-900 flex items-start">
              <i class="fa-solid fa-map-marker-alt text-gray-400 mr-2 mt-1"></i>
              {{ client.address }}
            </p>
          </div>
        {% endif %}
      </div>

      <!-- Recent Mentions -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-800 flex items-center">
            <i class="fa-solid fa-bullhorn text-gray-400 mr-2"></i>
            Recent Mentions
          </h3>
          <div class="flex items-center space-x-3">
            <span class="text-sm text-gray-500">{{ mention_stats.total }} total</span>
            <a href="{% url 'mentions:mention_create' %}?client={{ client.pk }}" class="px-3 py-1 bg-green-100 text-green-700 text-sm font-medium rounded-md hover:bg-green-200"><i class="fa-solid fa-plus mr-1"></i>New</a>
          </div>
        </div>

        <div class="space-y-3">
          {% for mention in recent_mentions %}
            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center mb-2">
                    <h4 class="font-medium text-gray-900 mr-3">{{ mention.title }}</h4>
                    {% if mention.status == 'pending' %}
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800"><i class="fa-solid fa-clock mr-1"></i>Pending</span>
                    {% elif mention.status == 'scheduled' %}
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800"><i class="fa-solid fa-calendar-check mr-1"></i>Scheduled</span>
                    {% elif mention.status == 'read' %}
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800"><i class="fa-solid fa-check-circle mr-1"></i>Completed</span>
                    {% else %}
                      <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800"><i class="fa-solid fa-times-circle mr-1"></i>Cancelled</span>
                    {% endif %}
                  </div>
                  <p class="text-sm text-gray-600 mb-2">{{ mention.content|truncatewords:25 }}</p>
                  <div class="flex items-center text-xs text-gray-500">
                    <i class="fa-solid fa-calendar mr-1"></i>
                    <span>{{ mention.created_at|date:'M d, Y' }}</span>
                    <span class="mx-2">•</span>
                    <i class="fa-solid fa-clock mr-1"></i>
                    <span>{{ mention.duration_seconds }}s</span>
                    {% if mention.priority > 2 %}
                      <span class="mx-2">•</span>
                      <span class="text-red-600 font-medium"><i class="fa-solid fa-exclamation mr-1"></i>{{ mention.get_priority_display }}</span>
                    {% endif %}
                  </div>
                </div>
                <div class="ml-4 flex flex-col space-y-1">
                  <a href="{% url 'mentions:mention_detail' mention.pk %}" class="text-primary-600 hover:text-primary-700 text-xs"><i class="fa-solid fa-eye mr-1"></i>View</a>
                  <a href="{% url 'mentions:mention_edit' mention.pk %}" class="text-gray-600 hover:text-gray-700 text-xs"><i class="fa-solid fa-edit mr-1"></i>Edit</a>
                </div>
              </div>
            </div>
          {% empty %}
            <div class="text-center py-12 text-gray-500">
              <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fa-solid fa-bullhorn text-2xl text-gray-400"></i>
              </div>
              <h4 class="text-lg font-medium text-gray-900 mb-2">No mentions yet</h4>
              <p class="text-sm text-gray-500 mb-4">Get started by creating your first mention for this client.</p>
              <a href="{% url 'mentions:mention_create' %}?client={{ client.pk }}" class="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700"><i class="fa-solid fa-plus mr-2"></i>Create First Mention</a>
            </div>
          {% endfor %}
        </div>

        {% if recent_mentions and mention_stats.total > 10 %}
          <div class="mt-6 pt-4 border-t border-gray-200 text-center">
            <a href="{% url 'mentions:mention_list' %}?client={{ client.pk }}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">View all {{ mention_stats.total }} mentions <i class="fa-solid fa-arrow-right ml-1"></i></a>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Statistics Sidebar -->
    <div class="space-y-6">
      <!-- Performance Stats -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <i class="fa-solid fa-chart-bar text-gray-400 mr-2"></i>
          Performance
        </h3>

        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Completion Rate</span>
            <span class="text-lg font-semibold text-green-600">
              {% if mention_stats.total > 0 %}
                {% widthratio mention_stats.completed mention_stats.total 100 %}%
              {% else %}
                0%
              {% endif %}
            </span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Avg Duration</span>
            <span class="text-lg font-semibold text-gray-900">{{ mention_stats.avg_duration|floatformat:0 }}s</span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Total Airtime</span>
            <span class="text-lg font-semibold text-gray-900">
              {% if mention_stats.total_airtime %}
                {{ mention_stats.total_airtime|floatformat:0 }}s
              {% else %}
                0s
              {% endif %}
            </span>
          </div>

          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Active Mentions</span>
            <span class="text-lg font-semibold text-blue-600">{{ mention_stats.pending|add:mention_stats.scheduled }}</span>
          </div>
        </div>
      </div>

      <!-- Status Breakdown -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <i class="fa-solid fa-pie-chart text-gray-400 mr-2"></i>
          Status Breakdown
        </h3>

        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600">Pending</span>
            </div>
            <span class="text-sm font-semibold text-gray-900">{{ mention_stats.pending }}</span>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-blue-400 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600">Scheduled</span>
            </div>
            <span class="text-sm font-semibold text-gray-900">{{ mention_stats.scheduled }}</span>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600">Completed</span>
            </div>
            <span class="text-sm font-semibold text-gray-900">{{ mention_stats.completed }}</span>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
              <span class="text-sm text-gray-600">Cancelled</span>
            </div>
            <span class="text-sm font-semibold text-gray-900">{{ mention_stats.cancelled }}</span>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <i class="fa-solid fa-bolt text-gray-400 mr-2"></i>
          Quick Actions
        </h3>

        <div class="space-y-3">
          <a href="{% url 'mentions:mention_create' %}?client={{ client.pk }}" class="w-full flex items-center px-4 py-3 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 transition-colors">
            <i class="fa-solid fa-plus mr-3"></i>
            Create New Mention
          </a>

          <a href="{% url 'core:client_edit' client.pk %}" class="w-full flex items-center px-4 py-3 text-sm font-medium text-primary-700 bg-primary-50 rounded-md hover:bg-primary-100 transition-colors">
            <i class="fa-solid fa-edit mr-3"></i>
            Edit Client Details
          </a>

          <a href="mailto:{{ client.email }}" class="w-full flex items-center px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors">
            <i class="fa-solid fa-envelope mr-3"></i>
            Send Email
          </a>

          <a href="tel:{{ client.phone }}" class="w-full flex items-center px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors">
            <i class="fa-solid fa-phone mr-3"></i>
            Call Client
          </a>

          {% if mention_stats.total > 0 %}
            <a href="{% url 'mentions:mention_list' %}?client={{ client.pk }}" class="w-full flex items-center px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors">
              <i class="fa-solid fa-list mr-3"></i>
              View All Mentions
            </a>
          {% endif %}
        </div>
      </div>

      <!-- Contact Summary -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <i class="fa-solid fa-address-card text-gray-400 mr-2"></i>
          Contact Summary
        </h3>

        <div class="space-y-4">
          <div class="flex items-center p-3 bg-gray-50 rounded-lg">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                <i class="fa-solid fa-user text-primary-600"></i>
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-gray-900">{{ client.contact_person }}</p>
              <p class="text-xs text-gray-500">Primary Contact</p>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex items-center">
              <i class="fa-solid fa-envelope text-gray-400 w-4 mr-3"></i>
              <a href="mailto:{{ client.email }}" class="text-sm text-primary-600 hover:text-primary-700">{{ client.email }}</a>
            </div>

            <div class="flex items-center">
              <i class="fa-solid fa-phone text-gray-400 w-4 mr-3"></i>
              <a href="tel:{{ client.phone }}" class="text-sm text-primary-600 hover:text-primary-700">{{ client.phone }}</a>
            </div>

            {% if client.address %}
              <div class="flex items-start">
                <i class="fa-solid fa-map-marker-alt text-gray-400 w-4 mr-3 mt-0.5"></i>
                <p class="text-sm text-gray-600">{{ client.address }}</p>
              </div>
            {% endif %}

            <div class="flex items-center">
              <i class="fa-solid fa-calendar-plus text-gray-400 w-4 mr-3"></i>
              <p class="text-sm text-gray-600">Member since {{ client.created_at|date:'M Y' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
