{% extends 'base.html' %}
{% load static %}

{% block title %}{{ note.title }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                    {% if note.is_pinned %}
                        <i class="fa-solid fa-thumbtack text-yellow-500" title="Pinned"></i>
                    {% endif %}
                    
                    <h1 class="text-2xl font-bold text-gray-900">{{ note.title }}</h1>
                    
                    <!-- Priority Badge -->
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ note.priority_display.class }}">
                        {{ note.priority_display.label }}
                    </span>
                    
                    <!-- Status Badge -->
                    {% if note.status == 'completed' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <i class="fa-solid fa-check mr-1"></i>
                            Completed
                        </span>
                    {% elif note.status == 'archived' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                            <i class="fa-solid fa-archive mr-1"></i>
                            Archived
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            <i class="fa-solid fa-circle mr-1"></i>
                            Active
                        </span>
                    {% endif %}
                    
                    <!-- Overdue indicator -->
                    {% if note.is_overdue %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                            Overdue
                        </span>
                    {% endif %}
                </div>
                
                <p class="text-gray-600">
                    Category: {{ note.get_category_display }} • 
                    Created {{ note.created_at|date:"M d, Y \a\t H:i" }}
                    {% if note.updated_at != note.created_at %}
                        • Updated {{ note.updated_at|date:"M d, Y \a\t H:i" }}
                    {% endif %}
                </p>
            </div>
            
            <div class="flex items-center space-x-3">
                <a href="{% url 'core:toggle_note_pin' note.pk %}" 
                   class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fa-solid fa-thumbtack mr-2"></i>
                    {% if note.is_pinned %}Unpin{% else %}Pin{% endif %}
                </a>
                
                <a href="{% url 'core:toggle_note_status' note.pk %}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fa-solid fa-check mr-2"></i>
                    {% if note.status == 'active' %}Mark Complete{% else %}Mark Active{% endif %}
                </a>
                
                <a href="{% url 'core:personal_note_edit' note.pk %}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fa-solid fa-edit mr-2"></i>
                    Edit
                </a>
                
                <a href="{% url 'core:personal_notes_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Notes
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Note Content</h3>
                </div>
                <div class="p-6">
                    <div class="prose max-w-none">
                        {{ note.content|linebreaks }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Note Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Details</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Category</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ note.get_category_display }}</dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Priority</dt>
                        <dd class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ note.priority_display.class }}">
                                {{ note.priority_display.label }}
                            </span>
                        </dd>
                    </div>
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                        <dd class="mt-1">
                            {% if note.status == 'completed' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fa-solid fa-check mr-1"></i>
                                    Completed
                                </span>
                            {% elif note.status == 'archived' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fa-solid fa-archive mr-1"></i>
                                    Archived
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fa-solid fa-circle mr-1"></i>
                                    Active
                                </span>
                            {% endif %}
                        </dd>
                    </div>
                    
                    {% if note.due_date %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Due Date</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {{ note.due_date|date:"M d, Y \a\t H:i" }}
                                {% if note.is_overdue %}
                                    <span class="text-red-600 font-medium">(Overdue)</span>
                                {% endif %}
                            </dd>
                        </div>
                    {% endif %}
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ note.created_at|date:"M d, Y \a\t H:i" }}</dd>
                    </div>
                    
                    {% if note.updated_at != note.created_at %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ note.updated_at|date:"M d, Y \a\t H:i" }}</dd>
                        </div>
                    {% endif %}
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Privacy</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            {% if note.is_private %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    <i class="fa-solid fa-lock mr-1"></i>
                                    Private
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fa-solid fa-globe mr-1"></i>
                                    Shared
                                </span>
                            {% endif %}
                        </dd>
                    </div>
                </div>
            </div>

            <!-- Tags -->
            {% if note.tags %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Tags</h3>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-wrap gap-2">
                            {% for tag in note.tags %}
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    <i class="fa-solid fa-tag mr-1"></i>
                                    {{ tag }}
                                </span>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="{% url 'core:personal_note_edit' note.pk %}" 
                       class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center justify-center">
                        <i class="fa-solid fa-edit mr-2"></i>
                        Edit Note
                    </a>
                    
                    <a href="{% url 'core:toggle_note_pin' note.pk %}" 
                       class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center justify-center">
                        <i class="fa-solid fa-thumbtack mr-2"></i>
                        {% if note.is_pinned %}Unpin Note{% else %}Pin Note{% endif %}
                    </a>
                    
                    <a href="{% url 'core:toggle_note_status' note.pk %}" 
                       class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center justify-center">
                        <i class="fa-solid fa-check mr-2"></i>
                        {% if note.status == 'active' %}Mark Complete{% else %}Mark Active{% endif %}
                    </a>
                    
                    <a href="{% url 'core:personal_note_delete' note.pk %}" 
                       class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center justify-center">
                        <i class="fa-solid fa-trash mr-2"></i>
                        Delete Note
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
