{% extends 'base.html' %} {% load static %} {% block title %}
  Live Show Monitor - Administration
{% endblock %} {% block extra_css %}
  <style>
    .live-indicator {
      animation: pulse-green 2s infinite;
    }
    
    .ending-soon-indicator {
      animation: pulse-orange 2s infinite;
    }
    
    @keyframes pulse-green {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }
    
    @keyframes pulse-orange {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }
    
    .digital-clock {
      font-family: 'Courier New', monospace;
      text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    
    .show-card {
      @apply transition-all duration-200 hover:shadow-lg;
    }
    
    .show-card.live {
      @apply bg-green-50 border-green-200 ring-2 ring-green-300;
    }
    
    .show-card.upcoming {
      @apply bg-blue-50 border-blue-200;
    }
    
    .show-card.completed {
      @apply bg-gray-50 border-gray-200;
    }
    
    .show-card.ended-early {
      @apply bg-red-50 border-red-200;
    }
    
    .presenter-card {
      @apply transition-all duration-200 hover:shadow-md;
    }
    
    .presenter-card.active {
      @apply bg-green-50 border-green-200;
    }
    
    .alert-card {
      @apply transition-all duration-200 hover:shadow-md;
    }
    
    .progress-bar {
      @apply transition-all duration-500 ease-in-out;
    }
    
    .stat-card {
      @apply bg-white rounded-lg border border-gray-200 p-4 shadow-sm hover:shadow-md transition-shadow duration-200;
    }
    
    .activity-item {
      @apply transition-all duration-200 hover:bg-gray-50;
    }
    
    /* Status indicators */
    .status-live {
      @apply bg-green-500;
    }
    .status-upcoming {
      @apply bg-blue-500;
    }
    .status-completed {
      @apply bg-gray-500;
    }
    .status-ended-early {
      @apply bg-red-500;
    }
    
    /* Auto-refresh indicator */
    .refresh-indicator {
      animation: spin 2s linear infinite;
    }
    
    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  </style>
{% endblock %} {% block content %}
  <div class="max-w-7xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg p-6 shadow-lg">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-4">
            <i class="fa-solid fa-tv text-white text-xl"></i>
          </div>
          <div>
            <h1 class="text-2xl font-bold">Live Show Monitor</h1>
            <p class="text-blue-100">Real-time monitoring dashboard for administrators</p>
          </div>
        </div>

        <div class="flex items-center space-x-4">
          <!-- Current Time -->
          <div class="text-center">
            <div class="digital-clock text-2xl font-bold" id="current-time">--:--:--</div>
            <div class="text-xs text-blue-200" id="current-date">Loading...</div>
          </div>

          <!-- Auto-refresh toggle -->
          <button id="auto-refresh-toggle" class="bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-white hover:bg-opacity-30 transition-all duration-200 flex items-center">
            <i class="fa-solid fa-sync-alt mr-2" id="refresh-icon"></i>
            <span id="refresh-text">Auto-refresh ON</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Statistics Overview -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
      <!-- Live Shows -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-broadcast-tower text-green-600"></i>
          </div>
          <div>
            <p class="text-2xl font-bold text-gray-900" data-stat="live_shows">{{ live_shows }}</p>
            <p class="text-xs text-gray-500">Live Shows</p>
          </div>
        </div>
      </div>

      <!-- Total Shows Today -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-calendar-day text-blue-600"></i>
          </div>
          <div>
            <p class="text-2xl font-bold text-gray-900" data-stat="total_shows">{{ total_shows_today }}</p>
            <p class="text-xs text-gray-500">Shows Today</p>
          </div>
        </div>
      </div>

      <!-- Mentions Progress -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-bullhorn text-purple-600"></i>
          </div>
          <div>
            <p class="text-2xl font-bold text-gray-900" data-stat="mentions">{{ completed_mentions_today }}/{{ total_mentions_today }}</p>
            <p class="text-xs text-gray-500">Mentions</p>
          </div>
        </div>
      </div>

      <!-- Pending Mentions -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-clock text-orange-600"></i>
          </div>
          <div>
            <p class="text-2xl font-bold text-gray-900" data-stat="pending">{{ pending_mentions_today }}</p>
            <p class="text-xs text-gray-500">Pending</p>
          </div>
        </div>
      </div>

      <!-- Active Presenters -->
      <div class="stat-card">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-users text-indigo-600"></i>
          </div>
          <div>
            <p class="text-2xl font-bold text-gray-900">{{ active_presenters.count }}</p>
            <p class="text-xs text-gray-500">Presenters</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Alerts Section -->
    {% if alerts %}
      <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fa-solid fa-exclamation-triangle mr-2 text-amber-600"></i>
            Alerts & Issues
          </h3>
        </div>
        <div class="p-6">
          <div class="space-y-3">
            {% for alert in alerts %}
              <div class="alert-card p-4 rounded-lg border {% if alert.type == 'error' %}
                   bg-red-50 border-red-200
                {% elif alert.type == 'warning' %}
                   bg-amber-50 border-amber-200
                {% else %}
                   bg-blue-50 border-blue-200
                {% endif %}">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center mr-3 {% if alert.type == 'error' %}
                         bg-red-100
                      {% elif alert.type == 'warning' %}
                         bg-amber-100
                      {% else %}
                         bg-blue-100
                      {% endif %}">
                      <i class="fa-solid {% if alert.type == 'error' %}
                           fa-times text-red-600
                        {% elif alert.type == 'warning' %}
                           fa-exclamation text-amber-600
                        {% else %}
                           fa-info text-blue-600
                        {% endif %}">

                      </i>
                    </div>
                    <div>
                      <h4 class="font-medium {% if alert.type == 'error' %}
                           text-red-900
                        {% elif alert.type == 'warning' %}
                           text-amber-900
                        {% else %}
                           text-blue-900
                        {% endif %}">
                        {{ alert.title }}
                      </h4>
                      <p class="text-sm {% if alert.type == 'error' %}
                           text-red-700
                        {% elif alert.type == 'warning' %}
                           text-amber-700
                        {% else %}
                           text-blue-700
                        {% endif %}">{{ alert.message }}</p>
                    </div>
                  </div>
                  <div class="text-right">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if alert.type == 'error' %}
                         bg-red-100 text-red-800
                      {% elif alert.type == 'warning' %}
                         bg-amber-100 text-amber-800
                      {% else %}
                         bg-blue-100 text-blue-800
                      {% endif %}">
                      {{ alert.count }}
                    </span>
                  </div>
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Shows Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Shows Status -->
      <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fa-solid fa-radio mr-2 text-blue-600"></i>
            Today's Shows
          </h3>
        </div>
        <div class="p-6">
          {% if show_stats %}
            <div class="space-y-4">
              {% for stat in show_stats %}
                <div class="show-card {{ stat.status }} p-4 rounded-lg border">
                  <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                      <div class="w-3 h-3 rounded-full status-{{ stat.status }} mr-3"></div>
                      <div>
                        <h4 class="font-semibold text-gray-900">{{ stat.show.name }}</h4>
                        <p class="text-sm text-gray-600">{{ stat.show.start_time|time:'H:i' }} - {{ stat.show.end_time|time:'H:i' }}</p>
                      </div>
                    </div>
                    <div class="text-right">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if stat.status == 'live' %}
                           bg-green-100 text-green-800
                        {% elif stat.status == 'upcoming' %}
                           bg-blue-100 text-blue-800
                        {% elif stat.status == 'ended_early' %}
                           bg-red-100 text-red-800
                        {% else %}
                           bg-gray-100 text-gray-800
                        {% endif %}">
                        {% if stat.is_live %}
                          <span class="live-indicator mr-1">●</span>
                        {% endif %} {{ stat.status_display }}
                      </span>
                    </div>
                  </div>

                  <!-- Progress Bar -->
                  <div class="mb-3">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Mentions Progress</span>
                      <span>{{ stat.completed_mentions }}/{{ stat.total_mentions }}</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div class="progress-bar bg-blue-600 h-2 rounded-full" style="width: {{ stat.progress_percentage }}%"></div>
                    </div>
                  </div>

                  <!-- Presenters -->
                  <div class="flex items-center justify-between text-sm">
                    <div class="flex items-center">
                      <i class="fa-solid fa-user mr-2 text-gray-400"></i>
                      {% if stat.active_presenters.exists %}
                        {% for presenter in stat.active_presenters.all %}
                          <span class="mr-2">{{ presenter.presenter.display_name }}</span>
                        {% endfor %}
                      {% else %}
                        <span class="text-red-600">No active presenters</span>
                      {% endif %}
                    </div>
                    {% if stat.next_mention %}
                      <div class="text-gray-600">Next: {{ stat.next_mention.scheduled_time|time:'H:i' }}</div>
                    {% endif %}
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-8">
              <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fa-solid fa-calendar-times text-2xl text-gray-400"></i>
              </div>
              <h4 class="text-lg font-medium text-gray-900 mb-2">No shows scheduled today</h4>
              <p class="text-gray-600">All quiet on the airwaves!</p>
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Active Presenters -->
      <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fa-solid fa-microphone mr-2 text-green-600"></i>
            Active Presenters
          </h3>
        </div>
        <div class="p-6">
          {% if active_presenters %}
            <div class="space-y-3">
              {% for presenter in active_presenters %}
                <div class="presenter-card {% if presenter.current_show_count > 0 %}active{% endif %} p-3 rounded-lg border">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      {% if presenter.profile_picture %}
                        <img src="{{ presenter.profile_picture.url }}" alt="{{ presenter.display_name }}" class="w-8 h-8 rounded-full mr-3" />
                      {% else %}
                        <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                          <i class="fa-solid fa-user text-gray-600 text-sm"></i>
                        </div>
                      {% endif %}
                      <div>
                        <h4 class="font-medium text-gray-900">{{ presenter.display_name }}</h4>
                        <p class="text-sm text-gray-600">{{ presenter.specialties|default:'General' }}</p>
                      </div>
                    </div>
                    <div class="text-right text-sm">
                      {% if presenter.current_show_count > 0 %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <span class="live-indicator mr-1">●</span>
                          ON AIR
                        </span>
                      {% else %}
                        <span class="text-gray-500">Off Air</span>
                      {% endif %}
                      <div class="text-xs text-gray-500 mt-1">{{ presenter.completed_mentions }}/{{ presenter.todays_mentions }} mentions</div>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-8">
              <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fa-solid fa-user-slash text-2xl text-gray-400"></i>
              </div>
              <h4 class="text-lg font-medium text-gray-900 mb-2">No active presenters</h4>
              <p class="text-gray-600">No presenters are currently scheduled.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
          <i class="fa-solid fa-history mr-2 text-purple-600"></i>
          Recent Activity
          <span class="ml-2 text-sm text-gray-500">(Last 2 hours)</span>
        </h3>
      </div>
      <div class="p-6">
        {% if recent_activity %}
          <div class="space-y-3 max-h-96 overflow-y-auto">
            {% for activity in recent_activity %}
              <div class="activity-item p-3 rounded-lg border border-gray-100">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                      <i class="fa-solid fa-check text-green-600 text-sm"></i>
                    </div>
                    <div>
                      <h4 class="font-medium text-gray-900">{{ activity.mention.client.name }}</h4>
                      <p class="text-sm text-gray-600">{{ activity.mention.title|truncatechars:50 }}</p>
                      <div class="flex items-center text-xs text-gray-500 mt-1">
                        <i class="fa-solid fa-user mr-1"></i>
                        <span>{{ activity.presenter.display_name }}</span>
                        <span class="mx-2">•</span>
                        <i class="fa-solid fa-radio mr-1"></i>
                        <span>{{ activity.show.name }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="text-right text-sm text-gray-500">
                    <div>{{ activity.actual_read_time|time:'H:i:s' }}</div>
                    <div class="text-xs">{{ activity.actual_read_time|timesince }} ago</div>
                  </div>
                </div>
              </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-8">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fa-solid fa-clock text-2xl text-gray-400"></i>
            </div>
            <h4 class="text-lg font-medium text-gray-900 mb-2">No recent activity</h4>
            <p class="text-gray-600">Activity will appear here as mentions are completed.</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %} {% block extra_js %}
  <script>
    let autoRefreshEnabled = true
    let refreshInterval
    
    // Update current time
    function updateClock() {
      const now = new Date()
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      const dateString = now.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    
      const timeElement = document.getElementById('current-time')
      const dateElement = document.getElementById('current-date')
    
      if (timeElement) {
        timeElement.textContent = timeString
      }
      if (dateElement) {
        dateElement.textContent = dateString
      }
    }
    
    // Auto-refresh functionality
    function toggleAutoRefresh() {
      const button = document.getElementById('auto-refresh-toggle')
      const icon = document.getElementById('refresh-icon')
      const text = document.getElementById('refresh-text')
    
      autoRefreshEnabled = !autoRefreshEnabled
    
      if (autoRefreshEnabled) {
        text.textContent = 'Auto-refresh ON'
        button.classList.remove('bg-red-500')
        button.classList.add('bg-white', 'bg-opacity-20')
        icon.classList.add('refresh-indicator')
    
        // Start auto-refresh every 15 seconds with API calls
        refreshInterval = setInterval(() => {
          updateDashboardData()
        }, 15000)
      } else {
        text.textContent = 'Auto-refresh OFF'
        button.classList.remove('bg-white', 'bg-opacity-20')
        button.classList.add('bg-red-500')
        icon.classList.remove('refresh-indicator')
    
        // Stop auto-refresh
        if (refreshInterval) {
          clearInterval(refreshInterval)
        }
      }
    }
    
    // Update dashboard data via API
    function updateDashboardData() {
      fetch('{% url "core:live_show_monitor_api" %}', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            updateStatistics(data.data)
    
            // Show notification if there are new alerts
            if (data.data.alerts_count > 0) {
              showNotification(data.data.alerts_count + ' active alerts require attention', 'warning')
            }
          } else {
            console.error('API Error:', data.error)
          }
        })
        .catch((error) => {
          console.error('Network Error:', error)
          // Fall back to full page refresh on API failure
          location.reload()
        })
    }
    
    // Update statistics on the page
    function updateStatistics(data) {
      // Update stat cards
      const liveShowsElement = document.querySelector('[data-stat="live_shows"]')
      if (liveShowsElement) liveShowsElement.textContent = data.live_shows
    
      const totalShowsElement = document.querySelector('[data-stat="total_shows"]')
      if (totalShowsElement) totalShowsElement.textContent = data.total_shows_today
    
      const mentionsElement = document.querySelector('[data-stat="mentions"]')
      if (mentionsElement) mentionsElement.textContent = data.completed_mentions_today + '/' + data.total_mentions_today
    
      const pendingElement = document.querySelector('[data-stat="pending"]')
      if (pendingElement) pendingElement.textContent = data.pending_mentions_today
    
      // Add a subtle indicator that data was updated
      const refreshIcon = document.getElementById('refresh-icon')
      if (refreshIcon) {
        refreshIcon.classList.add('text-green-400')
        setTimeout(() => {
          refreshIcon.classList.remove('text-green-400')
        }, 1000)
      }
    }
    
    // Manual refresh
    function manualRefresh() {
      const icon = document.getElementById('refresh-icon')
      icon.classList.add('refresh-indicator')
    
      setTimeout(() => {
        location.reload()
      }, 500)
    }
    
    // Show notification
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div')
      let bgClass = 'bg-blue-100 text-blue-800 border border-blue-200'
      let iconClass = 'info'
    
      if (type === 'success') {
        bgClass = 'bg-green-100 text-green-800 border border-green-200'
        iconClass = 'check'
      } else if (type === 'error') {
        bgClass = 'bg-red-100 text-red-800 border border-red-200'
        iconClass = 'exclamation-triangle'
      } else if (type === 'warning') {
        bgClass = 'bg-amber-100 text-amber-800 border border-amber-200'
        iconClass = 'exclamation'
      }
    
      notification.className = 'fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ' + bgClass
      notification.innerHTML = '<div class="flex items-center">' + '<i class="fa-solid fa-' + iconClass + ' mr-2"></i>' + '<span>' + message + '</span>' + '<button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-500 hover:text-gray-700">' + '<i class="fa-solid fa-times"></i>' + '</button>' + '</div>'
    
      document.body.appendChild(notification)
    
      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (notification.parentElement) {
          notification.remove()
        }
      }, 5000)
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function () {
      updateClock()
      setInterval(updateClock, 1000) // Update every second
    
      // Set up auto-refresh toggle
      document.getElementById('auto-refresh-toggle').addEventListener('click', toggleAutoRefresh)
    
      // Start auto-refresh by default
      if (autoRefreshEnabled) {
        refreshInterval = setInterval(() => {
          updateDashboardData()
        }, 15000)
        document.getElementById('refresh-icon').classList.add('refresh-indicator')
      }
    
      // Add keyboard shortcuts
      document.addEventListener('keydown', function (e) {
        // R key for manual refresh
        if (e.key === 'r' || e.key === 'R') {
          if (!e.ctrlKey && !e.metaKey) {
            e.preventDefault()
            manualRefresh()
          }
        }
    
        // Space key to toggle auto-refresh
        if (e.key === ' ') {
          if (!e.target.matches('input, textarea, select')) {
            e.preventDefault()
            toggleAutoRefresh()
          }
        }
      })
    
      // Show initial notification
      showNotification('Live Show Monitor loaded. Auto-refresh is enabled.', 'success')
    })
  </script>
{% endblock %}
