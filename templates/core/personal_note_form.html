{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ title }}</h1>
                <p class="text-gray-600">
                    {% if note %}
                        Update your personal note details.
                    {% else %}
                        Create a new personal note to keep track of important information.
                    {% endif %}
                </p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'core:personal_notes_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Notes
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <form method="post" class="p-6 space-y-6">
            {% csrf_token %}
            
            <!-- Title -->
            <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.title.label }}
                    <span class="text-red-500">*</span>
                </label>
                {{ form.title }}
                {% if form.title.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.title.help_text }}</p>
                {% endif %}
                {% if form.title.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.title.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Content -->
            <div>
                <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.content.label }}
                    <span class="text-red-500">*</span>
                </label>
                {{ form.content }}
                {% if form.content.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.content.help_text }}</p>
                {% endif %}
                {% if form.content.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.content.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Category and Priority Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.category.label }}
                    </label>
                    {{ form.category }}
                    {% if form.category.help_text %}
                        <p class="mt-1 text-sm text-gray-500">{{ form.category.help_text }}</p>
                    {% endif %}
                    {% if form.category.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.category.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.priority.label }}
                    </label>
                    {{ form.priority }}
                    {% if form.priority.help_text %}
                        <p class="mt-1 text-sm text-gray-500">{{ form.priority.help_text }}</p>
                    {% endif %}
                    {% if form.priority.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.priority.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Status and Due Date Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.status.label }}
                    </label>
                    {{ form.status }}
                    {% if form.status.help_text %}
                        <p class="mt-1 text-sm text-gray-500">{{ form.status.help_text }}</p>
                    {% endif %}
                    {% if form.status.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.status.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.due_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.due_date.label }}
                    </label>
                    {{ form.due_date }}
                    {% if form.due_date.help_text %}
                        <p class="mt-1 text-sm text-gray-500">{{ form.due_date.help_text }}</p>
                    {% endif %}
                    {% if form.due_date.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.due_date.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Tags -->
            <div>
                <label for="{{ form.tags_input.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                </label>
                {{ form.tags_input }}
                {% if form.tags_input.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.tags_input.help_text }}</p>
                {% endif %}
                {% if form.tags_input.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.tags_input.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Checkboxes -->
            <div class="space-y-4">
                <div class="flex items-center">
                    {{ form.is_pinned }}
                    <label for="{{ form.is_pinned.id_for_label }}" class="ml-2 text-sm text-gray-700">
                        {{ form.is_pinned.label }}
                    </label>
                    {% if form.is_pinned.help_text %}
                        <p class="ml-2 text-sm text-gray-500">{{ form.is_pinned.help_text }}</p>
                    {% endif %}
                </div>

                <div class="flex items-center">
                    {{ form.is_private }}
                    <label for="{{ form.is_private.id_for_label }}" class="ml-2 text-sm text-gray-700">
                        {{ form.is_private.label }}
                    </label>
                    {% if form.is_private.help_text %}
                        <p class="ml-2 text-sm text-gray-500">{{ form.is_private.help_text }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa-solid fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Please correct the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc list-inside space-y-1">
                                    {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'core:personal_notes_list' %}" 
                   class="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    {% if note %}
                        <i class="fa-solid fa-save mr-2"></i>
                        Update Note
                    {% else %}
                        <i class="fa-solid fa-plus mr-2"></i>
                        Create Note
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textarea
    const textarea = document.querySelector('textarea[name="content"]');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
        
        // Initial resize
        textarea.style.height = textarea.scrollHeight + 'px';
    }
    
    // Tag input enhancement
    const tagInput = document.querySelector('input[name="tags_input"]');
    if (tagInput) {
        tagInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // Add comma if not present
                if (!this.value.endsWith(',') && this.value.trim()) {
                    this.value += ', ';
                }
            }
        });
    }
});
</script>
{% endblock %}
