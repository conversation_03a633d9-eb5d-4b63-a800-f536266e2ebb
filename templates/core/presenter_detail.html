{% extends 'base.html' %} {% load static %} {% block title %}
  {{ presenter.stage_name }} - Presenter Details
{% endblock %} {% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <a href="{% url 'core:presenter_list' %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
            <div class="flex items-center">
              {% if presenter.profile_image %}
                <img class="h-16 w-16 rounded-full object-cover mr-4" src="{{ presenter.profile_image.url }}" alt="{{ presenter.stage_name }}" />
              {% else %}
                <div class="h-16 w-16 rounded-full bg-gray-200 flex items-center justify-center mr-4">
                  <i class="fa-solid fa-microphone text-gray-400 text-xl"></i>
                </div>
              {% endif %}
              <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ presenter.stage_name }}</h1>
                <p class="text-gray-600">{{ presenter.full_name }}</p>
                <div class="flex items-center mt-1 space-x-2">
                  {% if presenter.is_active %}
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                  {% else %}
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                  {% endif %} {% if presenter.is_available %}
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Available</span>
                  {% endif %}
                  <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">{{ presenter.get_contract_type_display }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="flex space-x-3">
            {% if user.is_staff %}
              <a href="{% url 'core:presenter_edit' presenter.pk %}" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
                <i class="fa-solid fa-edit mr-2"></i>
                Edit
              </a>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
          </div>
          <div class="p-6">
            <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Organization</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ presenter.organization.name }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Contract Type</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ presenter.get_contract_type_display }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Hire Date</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {% if presenter.hire_date %}
                    {{ presenter.hire_date|date:'F d,
                Y' }}
                  {% else %}
                    Not set
                  {% endif %}
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Specialties</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {% if presenter.specialties %}
                    {{ presenter.specialties }}
                  {% else %}
                    Not specified
                  {% endif %}
                </dd>
              </div>
              {% if presenter.phone %}
                <div>
                  <dt class="text-sm font-medium text-gray-500">Phone</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ presenter.phone }}</dd>
                </div>
              {% endif %} {% if presenter.emergency_contact %}
                <div>
                  <dt class="text-sm font-medium text-gray-500">Emergency Contact</dt>
                  <dd class="mt-1 text-sm text-gray-900">{{ presenter.emergency_contact }}</dd>
                </div>
              {% endif %}
            </dl>

            {% if presenter.bio %}
              <div class="mt-6">
                <dt class="text-sm font-medium text-gray-500">Biography</dt>
                <dd class="mt-2 text-sm text-gray-900">{{ presenter.bio|linebreaks }}</dd>
              </div>
            {% endif %}
          </div>
        </div>

        <!-- Shows -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Shows</h3>
          </div>
          <div class="p-6">
            {% if shows %}
              <div class="space-y-4">
                {% for show_presenter in shows %}
                  <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 class="font-medium text-gray-900">{{ show_presenter.show.name }}</h4>
                      <p class="text-sm text-gray-500">{{ show_presenter.show.description|truncatewords:20 }}</p>
                      <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                        <span><i class="fa-solid fa-clock mr-1"></i>{{ show_presenter.show.start_time }} - {{ show_presenter.show.end_time }}</span>
                        <span><i class="fa-solid fa-calendar mr-1"></i>{{ show_presenter.show.get_frequency_display }}</span>
                        {% if show_presenter.is_primary %}
                          <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Primary</span>
                        {% endif %}
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      {% if show_presenter.show.is_active %}
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                      {% else %}
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>
                      {% endif %}
                      <a href="{% url 'shows:show_detail' show_presenter.show.pk %}" class="text-primary-600 hover:text-primary-900"><i class="fa-solid fa-external-link-alt"></i></a>
                    </div>
                  </div>
                {% endfor %}
              </div>
              {% if presenter.active_shows_count > 10 %}
                <div class="mt-4 text-center">
                  <a href="{% url 'shows:show_list' %}?presenter={{ presenter.pk }}" class="text-primary-600 hover:text-primary-900 text-sm">View all {{ presenter.active_shows_count }} shows</a>
                </div>
              {% endif %}
            {% else %}
              <div class="text-center py-8">
                <i class="fa-solid fa-radio text-gray-400 text-3xl mb-4"></i>
                <p class="text-gray-500">No shows assigned yet</p>
              </div>
            {% endif %}
          </div>
        </div>

        <!-- Recent Mentions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Mentions</h3>
          </div>
          <div class="p-6">
            {% if recent_mentions %}
              <div class="space-y-4">
                {% for mention in recent_mentions %}
                  <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 class="font-medium text-gray-900">{{ mention.mention.title }}</h4>
                      <p class="text-sm text-gray-500">{{ mention.mention.content|truncatewords:15 }}</p>
                      <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                        <span><i class="fa-solid fa-radio mr-1"></i>{{ mention.show.name }}</span>
                        <span><i class="fa-solid fa-calendar mr-1"></i>{{ mention.scheduled_date|date:'M d, Y' }}</span>
                        {% if mention.scheduled_time %}
                          <span><i class="fa-solid fa-clock mr-1"></i>{{ mention.scheduled_time|time:'g:i A' }}</span>
                        {% endif %}
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {% if mention.status == 'completed' %}
                           bg-green-100 text-green-800
                        {% elif mention.status == 'scheduled' %}
                           bg-blue-100 text-blue-800
                        {% elif mention.status == 'cancelled' %}
                           bg-red-100 text-red-800
                        {% else %}
                           bg-gray-100 text-gray-800
                        {% endif %}">
                        {{ mention.get_status_display }}
                      </span>
                      <a href="{% url 'mentions:mention_detail' mention.pk %}" class="text-primary-600 hover:text-primary-900"><i class="fa-solid fa-external-link-alt"></i></a>
                    </div>
                  </div>
                {% endfor %}
              </div>
              {% if presenter.total_mentions_count > 10 %}
                <div class="mt-4 text-center">
                  <a href="{% url 'mentions:mention_list' %}?presenter={{ presenter.pk }}" class="text-primary-600 hover:text-primary-900 text-sm">View all {{ presenter.total_mentions_count }} mentions</a>
                </div>
              {% endif %}
            {% else %}
              <div class="text-center py-8">
                <i class="fa-solid fa-microphone text-gray-400 text-3xl mb-4"></i>
                <p class="text-gray-500">No mentions yet</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Quick Stats -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Quick Stats</h3>
          </div>
          <div class="p-6">
            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-500">Active Shows</span>
                <span class="text-sm font-medium text-gray-900">{{ presenter.active_shows_count }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-500">Total Mentions</span>
                <span class="text-sm font-medium text-gray-900">{{ presenter.total_mentions_count }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-500">Skills</span>
                <span class="text-sm font-medium text-gray-900">{{ skills.count }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-500">Member Since</span>
                <span class="text-sm font-medium text-gray-900">{{ presenter.created_at|date:'M Y' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Social Media -->
        {% if presenter.twitter_handle or presenter.instagram_handle or presenter.facebook_page %}
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Social Media</h3>
            </div>
            <div class="p-6">
              <div class="space-y-3">
                {% if presenter.twitter_handle %}
                  <a href="https://twitter.com/{{ presenter.twitter_handle }}" target="_blank" class="flex items-center text-blue-600 hover:text-blue-800">
                    <i class="fa-brands fa-twitter mr-2"></i>
                    @{{ presenter.twitter_handle }}
                  </a>
                {% endif %} {% if presenter.instagram_handle %}
                  <a href="https://instagram.com/{{ presenter.instagram_handle }}" target="_blank" class="flex items-center text-pink-600 hover:text-pink-800">
                    <i class="fa-brands fa-instagram mr-2"></i>
                    @{{ presenter.instagram_handle }}
                  </a>
                {% endif %} {% if presenter.facebook_page %}
                  <a href="{{ presenter.facebook_page }}" target="_blank" class="flex items-center text-blue-700 hover:text-blue-900">
                    <i class="fa-brands fa-facebook mr-2"></i>
                    Facebook Page
                  </a>
                {% endif %}
              </div>
            </div>
          </div>
        {% endif %}

        <!-- Skills -->
        {% if skills %}
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Skills</h3>
            </div>
            <div class="p-6">
              <div class="space-y-3">
                {% for skill in skills|slice:':5' %}
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-900">{{ skill.name }}</span>
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium {% if skill.proficiency_level == 'expert' %}
                         bg-green-100 text-green-800
                      {% elif skill.proficiency_level == 'advanced' %}
                         bg-blue-100 text-blue-800
                      {% elif skill.proficiency_level == 'intermediate' %}
                         bg-yellow-100 text-yellow-800
                      {% else %}
                         bg-gray-100 text-gray-800
                      {% endif %}">
                      {{ skill.get_proficiency_level_display }}
                    </span>
                  </div>
                {% endfor %} {% if skills.count > 5 %}
                  <div class="text-center pt-2">
                    <span class="text-primary-600 text-sm">{{ skills.count }} skills total</span>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
        {% endif %}

        <!-- Availability Summary -->
        {% if availability %}
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h3 class="text-lg font-medium text-gray-900">Availability</h3>
            </div>
            <div class="p-6">
              <div class="space-y-2">
                {% for avail in availability|slice:':3' %}
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-900">{{ avail.get_day_of_week_display }}</span>
                    <span class="text-gray-500">{{ avail.start_time|time:'g:i A' }} - {{ avail.end_time|time:'g:i A' }}</span>
                  </div>
                {% endfor %} {% if availability.count > 3 %}
                  <div class="text-center pt-2">
                    <span class="text-primary-600 text-sm">Full schedule available</span>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}
