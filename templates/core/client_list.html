{% extends 'base.html' %}

{% block title %}Client Management - RadioMention{% endblock %}

{% block page_title %}Client Management{% endblock %}

{% block header_actions %}
<a href="{% url 'core:client_create' %}" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
    <i class="fa-solid fa-plus mr-2"></i>
    Add Client
</a>
{% endblock %}

{% block content %}
<div class="flex items-center justify-between mb-4">
    <div>
        <h3 class="text-lg font-semibold text-gray-800">Clients</h3>
        <p class="text-sm text-gray-600">Manage your client database and information</p>
    </div>
</div>

<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <form method="GET" class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" name="search" value="{{ request.GET.search }}" placeholder="Search clients..." class="pl-9 pr-4 py-2 border border-gray-300 rounded-md text-sm">
                        <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <select name="status" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="">All Status</option>
                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                    </select>
                    <select name="industry" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option value="">All Industries</option>
                        {% for value, label in industry_choices %}
                            <option value="{{ value }}" {% if request.GET.industry == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                    <button type="submit" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">Filter</button>
                </form>
            </div>
            <div class="text-sm text-gray-500">
                {{ clients|length }} total clients
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Industry</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mentions</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for client in clients %}
                <tr>
                    <td class="px-4 py-3 whitespace-nowrap">
                        <div>
                            <div class="text-sm font-medium text-gray-900">{{ client.name }}</div>
                            <div class="text-xs text-gray-500">{{ client.industry|default:"No industry specified" }}</div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ client.email }}</div>
                        <div class="text-sm text-gray-500">{{ client.phone }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ client.industry|default:"-" }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ client.total_mentions }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if client.is_active %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                        {% else %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a href="{% url 'core:client_detail' client.pk %}" class="text-primary-600 hover:text-primary-900 mr-3">View</a>
                        <a href="{% url 'core:client_edit' client.pk %}" class="text-primary-600 hover:text-primary-900 mr-3">Edit</a>
                        <a href="{% url 'core:client_delete' client.pk %}" class="text-red-600 hover:text-red-900">Delete</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">No clients found</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {% if is_paginated %}
    <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
        <div class="text-sm text-gray-500">
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
        </div>
        <div class="flex space-x-2">
            {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Previous</a>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <span class="px-3 py-1 bg-primary-600 text-white rounded-md text-sm">{{ num }}</span>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <a href="?page={{ num }}" class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">{{ num }}</a>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Next</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
