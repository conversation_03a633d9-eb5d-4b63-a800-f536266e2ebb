{% extends 'base.html' %}
{% load static %}

{% block title %}{{ industry.name }} - Industries - {{ block.super }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{% url 'core:industry_list' %}" class="hover:text-blue-600">Industries</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>{{ industry.name }}</span>
        </div>
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ industry.name }}</h1>
                <div class="flex items-center space-x-4 mt-2">
                    <span class="text-sm text-gray-500 font-mono bg-gray-100 px-2 py-1 rounded">
                        {{ industry.code }}
                    </span>
                    {% if industry.is_active %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>Active
                        </span>
                    {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <i class="fas fa-times-circle mr-1"></i>Inactive
                        </span>
                    {% endif %}
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{% url 'core:industry_edit' industry.pk %}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                    <i class="fas fa-edit mr-2"></i>Edit
                </a>
                <a href="{% url 'core:industry_delete' industry.pk %}" 
                   class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                   onclick="return confirm('Are you sure you want to delete this industry?')">
                    <i class="fas fa-trash mr-2"></i>Delete
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Industry Information -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Industry Information</h2>
                
                <dl class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ industry.name }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Code</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded inline-block">
                            {{ industry.code }}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                        <dd class="mt-1">
                            {% if industry.is_active %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>Active
                                </span>
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i>Inactive
                                </span>
                            {% endif %}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Created</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ industry.created_at|date:"F d, Y" }}</dd>
                    </div>
                    {% if industry.description %}
                    <div class="sm:col-span-2">
                        <dt class="text-sm font-medium text-gray-500">Description</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ industry.description }}</dd>
                    </div>
                    {% endif %}
                </dl>
            </div>

            <!-- Clients Using This Industry -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">
                        Clients Using This Industry
                        <span class="text-sm font-normal text-gray-500">({{ client_count }})</span>
                    </h2>
                    {% if clients %}
                    <a href="{% url 'core:client_list' %}?industry={{ industry.pk }}" 
                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        View All <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                    {% endif %}
                </div>

                {% if clients %}
                    <div class="space-y-3">
                        {% for client in clients|slice:":10" %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-building text-blue-600 text-sm"></i>
                                    </div>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">
                                        <a href="{% url 'core:client_detail' client.pk %}" 
                                           class="hover:text-blue-600">{{ client.name }}</a>
                                    </div>
                                    <div class="text-sm text-gray-500">{{ client.contact_person }}</div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500">{{ client.total_mentions }} mentions</span>
                                {% if client.is_active %}
                                    <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                                {% else %}
                                    <span class="w-2 h-2 bg-red-400 rounded-full"></span>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                        
                        {% if clients.count > 10 %}
                        <div class="text-center pt-3">
                            <a href="{% url 'core:client_list' %}?industry={{ industry.pk }}" 
                               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                View {{ clients.count|add:"-10" }} more clients <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i class="fas fa-building text-gray-400 text-3xl mb-3"></i>
                        <h3 class="text-sm font-medium text-gray-900 mb-1">No clients yet</h3>
                        <p class="text-sm text-gray-500">No clients are currently assigned to this industry.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Statistics Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Total Clients</span>
                        <span class="text-lg font-semibold text-gray-900">{{ client_count }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Active Clients</span>
                        <span class="text-lg font-semibold text-green-600">
                            {{ clients|length }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Total Mentions</span>
                        <span class="text-lg font-semibold text-blue-600">
                            {% with total_mentions=0 %}
                                {% for client in clients %}
                                    {% with total_mentions=total_mentions|add:client.total_mentions %}{% endwith %}
                                {% endfor %}
                                {{ total_mentions }}
                            {% endwith %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="{% url 'core:industry_edit' industry.pk %}" 
                       class="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-edit mr-2"></i>Edit Industry
                    </a>
                    
                    <a href="{% url 'core:client_create' %}" 
                       class="w-full bg-green-50 hover:bg-green-100 text-green-700 px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-plus mr-2"></i>Add Client
                    </a>
                    
                    {% if clients %}
                    <a href="{% url 'core:client_list' %}?industry={{ industry.pk }}" 
                       class="w-full bg-gray-50 hover:bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors flex items-center">
                        <i class="fas fa-list mr-2"></i>View All Clients
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
