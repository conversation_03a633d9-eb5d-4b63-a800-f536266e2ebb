{% extends 'base.html' %}
{% load static %}

{% block title %}My Notes{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Notes</h1>
                <p class="text-gray-600">Manage your personal notes and reminders.</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'core:quick_note_create' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                    <i class="fa-solid fa-bolt mr-2"></i>
                    Quick Note
                </a>
                <a href="{% url 'core:personal_note_create' %}" 
                   class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                    <i class="fa-solid fa-plus mr-2"></i>
                    New Note
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <i class="fa-solid fa-note-sticky text-blue-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Total Notes</p>
                    <p class="text-lg font-semibold text-gray-900">{{ stats.total_notes }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <i class="fa-solid fa-circle-check text-green-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Active</p>
                    <p class="text-lg font-semibold text-gray-900">{{ stats.active_notes }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center">
                <div class="p-2 bg-gray-100 rounded-lg">
                    <i class="fa-solid fa-check-double text-gray-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Completed</p>
                    <p class="text-lg font-semibold text-gray-900">{{ stats.completed_notes }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <i class="fa-solid fa-thumbtack text-yellow-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Pinned</p>
                    <p class="text-lg font-semibold text-gray-900">{{ stats.pinned_notes }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <i class="fa-solid fa-clock text-red-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-500">Overdue</p>
                    <p class="text-lg font-semibold text-gray-900">{{ stats.overdue_notes }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Filter Notes</h3>
        </div>
        <div class="p-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="{{ filter_form.search.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Search
                    </label>
                    {{ filter_form.search }}
                </div>
                
                <div>
                    <label for="{{ filter_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Category
                    </label>
                    {{ filter_form.category }}
                </div>
                
                <div>
                    <label for="{{ filter_form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Priority
                    </label>
                    {{ filter_form.priority }}
                </div>
                
                <div>
                    <label for="{{ filter_form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Status
                    </label>
                    {{ filter_form.status }}
                </div>
                
                <div class="md:col-span-4 flex items-center space-x-3">
                    <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        <i class="fa-solid fa-filter mr-2"></i>
                        Apply Filters
                    </button>
                    <a href="{% url 'core:personal_notes_list' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        <i class="fa-solid fa-times mr-2"></i>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Notes List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        {% if page_obj %}
            <div class="divide-y divide-gray-200">
                {% for note in page_obj %}
                    <div class="p-6 hover:bg-gray-50 transition-colors">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-2">
                                    {% if note.is_pinned %}
                                        <i class="fa-solid fa-thumbtack text-yellow-500" title="Pinned"></i>
                                    {% endif %}
                                    
                                    <h3 class="text-lg font-medium text-gray-900">
                                        <a href="{% url 'core:personal_note_detail' note.pk %}" class="hover:text-primary-600">
                                            {{ note.title }}
                                        </a>
                                    </h3>
                                    
                                    <!-- Priority Badge -->
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ note.priority_display.class }}">
                                        {{ note.priority_display.label }}
                                    </span>
                                    
                                    <!-- Category Badge -->
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        {{ note.get_category_display }}
                                    </span>
                                    
                                    <!-- Status Badge -->
                                    {% if note.status == 'completed' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fa-solid fa-check mr-1"></i>
                                            Completed
                                        </span>
                                    {% elif note.status == 'archived' %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            <i class="fa-solid fa-archive mr-1"></i>
                                            Archived
                                        </span>
                                    {% endif %}
                                    
                                    <!-- Overdue indicator -->
                                    {% if note.is_overdue %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                                            Overdue
                                        </span>
                                    {% endif %}
                                </div>
                                
                                <p class="text-gray-600 mb-3 line-clamp-2">{{ note.content|truncatechars:150 }}</p>
                                
                                <div class="flex items-center text-sm text-gray-500 space-x-4">
                                    <span>
                                        <i class="fa-solid fa-calendar mr-1"></i>
                                        {{ note.created_at|date:"M d, Y" }}
                                    </span>
                                    
                                    {% if note.due_date %}
                                        <span>
                                            <i class="fa-solid fa-clock mr-1"></i>
                                            Due: {{ note.due_date|date:"M d, Y H:i" }}
                                        </span>
                                    {% endif %}
                                    
                                    {% if note.tags %}
                                        <div class="flex items-center space-x-1">
                                            <i class="fa-solid fa-tags mr-1"></i>
                                            {% for tag in note.tags %}
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                    {{ tag }}
                                                </span>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="flex items-center space-x-2 ml-4">
                                <a href="{% url 'core:toggle_note_pin' note.pk %}" 
                                   class="text-gray-400 hover:text-yellow-600" 
                                   title="{% if note.is_pinned %}Unpin{% else %}Pin{% endif %}">
                                    <i class="fa-solid fa-thumbtack"></i>
                                </a>
                                
                                <a href="{% url 'core:toggle_note_status' note.pk %}" 
                                   class="text-gray-400 hover:text-green-600" 
                                   title="{% if note.status == 'active' %}Mark Complete{% else %}Mark Active{% endif %}">
                                    <i class="fa-solid fa-check"></i>
                                </a>
                                
                                <a href="{% url 'core:personal_note_edit' note.pk %}" 
                                   class="text-gray-400 hover:text-blue-600" 
                                   title="Edit">
                                    <i class="fa-solid fa-edit"></i>
                                </a>
                                
                                <a href="{% url 'core:personal_note_delete' note.pk %}" 
                                   class="text-gray-400 hover:text-red-600" 
                                   title="Delete">
                                    <i class="fa-solid fa-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} notes
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                                   class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                    Previous
                                </a>
                            {% endif %}
                            
                            <span class="px-3 py-2 text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 rounded-md">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                            
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.priority %}&priority={{ request.GET.priority }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}" 
                                   class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                    Next
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <i class="fa-solid fa-note-sticky text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No notes found</h3>
                <p class="text-gray-600 mb-4">Get started by creating your first personal note.</p>
                <a href="{% url 'core:personal_note_create' %}" 
                   class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium inline-flex items-center">
                    <i class="fa-solid fa-plus mr-2"></i>
                    Create Note
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter changes
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form');
    const filterInputs = filterForm.querySelectorAll('select, input[type="text"]');
    
    filterInputs.forEach(input => {
        if (input.type === 'text') {
            // For search input, add debounce
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    filterForm.submit();
                }, 500);
            });
        } else {
            // For select inputs, submit immediately
            input.addEventListener('change', function() {
                filterForm.submit();
            });
        }
    });
});
</script>
{% endblock %}
