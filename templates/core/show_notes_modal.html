<!-- Show Notes Modal -->
<div id="showNotesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
  <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-md bg-white max-h-[90vh] overflow-hidden flex flex-col">
    
    <!-- Modal Header -->
    <div class="flex items-center justify-between pb-4 border-b border-gray-200">
      <div class="flex items-center space-x-3">
        <i class="fa-solid fa-note-sticky text-primary-600"></i>
        <div>
          <h3 class="text-lg font-semibold text-gray-900" id="showNotesModalTitle">Show Notes</h3>
          <div class="text-sm text-gray-600" id="showNotesModalSubtitle"></div>
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <button onclick="createShowNote()" class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
          <i class="fa-solid fa-plus mr-1"></i>
          New Note
        </button>
        <button onclick="closeShowNotesModal()" class="text-gray-400 hover:text-gray-600">
          <i class="fa-solid fa-times text-xl"></i>
        </button>
      </div>
    </div>

    <!-- Modal Content -->
    <div class="flex-1 overflow-hidden flex flex-col mt-4">
      
      <!-- Loading State -->
      <div id="showNotesLoading" class="hidden flex items-center justify-center py-8">
        <div class="flex items-center space-x-2 text-gray-600">
          <i class="fa-solid fa-spinner fa-spin"></i>
          <span>Loading notes...</span>
        </div>
      </div>

      <!-- Notes List Container -->
      <div id="showNotesContainer" class="flex-1 overflow-y-auto">
        {% include 'core/show_note_list.html' %}
      </div>
    </div>
  </div>
</div>

<!-- Show Note Form Modal -->
<div id="showNoteFormModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-60 hidden">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    
    <!-- Form Modal Header -->
    <div class="flex items-center justify-between pb-4 border-b border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900" id="showNoteFormModalTitle">Create Note</h3>
      <button onclick="closeShowNoteFormModal()" class="text-gray-400 hover:text-gray-600">
        <i class="fa-solid fa-times text-xl"></i>
      </button>
    </div>

    <!-- Form Container -->
    <div class="mt-4" id="showNoteFormContainer">
      <!-- Form will be loaded here via AJAX -->
    </div>
  </div>
</div>

<!-- Show Note View Modal -->
<div id="showNoteViewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-60 hidden">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    
    <!-- View Modal Header -->
    <div class="flex items-center justify-between pb-4 border-b border-gray-200">
      <div class="flex items-center space-x-3">
        <i class="fa-solid fa-eye text-blue-600"></i>
        <h3 class="text-lg font-semibold text-gray-900" id="showNoteViewTitle">Note Details</h3>
      </div>
      <div class="flex items-center space-x-2">
        <button onclick="editShowNoteFromView()" class="text-gray-400 hover:text-green-600" title="Edit">
          <i class="fa-solid fa-edit"></i>
        </button>
        <button onclick="closeShowNoteViewModal()" class="text-gray-400 hover:text-gray-600">
          <i class="fa-solid fa-times text-xl"></i>
        </button>
      </div>
    </div>

    <!-- View Content -->
    <div class="mt-4">
      <!-- Note Info -->
      <div class="mb-4 p-3 bg-gray-50 rounded-lg">
        <div class="flex items-center justify-between mb-2">
          <div class="flex items-center space-x-2">
            <span id="showNoteViewCategory" class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full"></span>
            <span id="showNoteViewPriority" class="text-xs px-2 py-1 rounded-full"></span>
            <span id="showNoteViewStatus" class="text-xs px-2 py-1 rounded-full"></span>
          </div>
          <span id="showNoteViewPin" class="hidden">
            <i class="fa-solid fa-thumbtack text-primary-600" title="Pinned"></i>
          </span>
        </div>
        <div class="text-xs text-gray-500">
          <span>Created: <span id="showNoteViewCreated"></span></span>
          <span class="ml-4">Updated: <span id="showNoteViewUpdated"></span></span>
        </div>
      </div>

      <!-- Note Content -->
      <div class="mb-4">
        <div id="showNoteViewContent" class="text-gray-900 leading-relaxed whitespace-pre-wrap bg-white border border-gray-200 rounded-lg p-4"></div>
      </div>

      <!-- Note Tags -->
      <div id="showNoteViewTagsContainer" class="mb-4 hidden">
        <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
        <div id="showNoteViewTags" class="flex flex-wrap gap-1"></div>
      </div>
    </div>
  </div>
</div>

<style>
/* Modal z-index hierarchy */
#showNotesModal { z-index: 50; }
#showNoteFormModal, #showNoteViewModal { z-index: 60; }

/* Tag styles */
.note-tag {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full;
}
</style>
