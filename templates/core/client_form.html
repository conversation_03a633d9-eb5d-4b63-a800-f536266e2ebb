{% extends 'base.html' %}

{% block title %}
  {% if object %}
    Edit Client
  {% else %}
    Add Client
  {% endif %}- RadioMention
{% endblock %}

{% block page_title %}
  {% if object %}
    Edit Client
  {% else %}
    Add Client
  {% endif %}
{% endblock %}

{% block content %}
  <div class="max-w-2xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="mb-6">
        <h3 class="text-lg font-semibold text-gray-800">
          {% if object %}
            Edit Client: {{ object.name }}
          {% else %}
            Add New Client
          {% endif %}
        </h3>
        <p class="text-gray-600">
          {% if object %}
            Update client information
          {% else %}
            Enter client details
          {% endif %}
        </p>
      </div>

      <form method="post" class="space-y-6">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Client Name *</label>
            {{ form.name }}
            {% if form.name.errors %}
              <div class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</div>
            {% endif %}
          </div>

          <div>
            <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Contact Person *</label>
            {{ form.contact_person }}
            {% if form.contact_person.errors %}
              <div class="mt-1 text-sm text-red-600">{{ form.contact_person.errors.0 }}</div>
            {% endif %}
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
            {{ form.email }}
            {% if form.email.errors %}
              <div class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</div>
            {% endif %}
          </div>

          <div>
            <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Phone *</label>
            {{ form.phone }}
            {% if form.phone.errors %}
              <div class="mt-1 text-sm text-red-600">{{ form.phone.errors.0 }}</div>
            {% endif %}
          </div>
        </div>

        <div>
          <label for="{{ form.industry.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Industry</label>
          {{ form.industry }}
          {% if form.industry.errors %}
            <div class="mt-1 text-sm text-red-600">{{ form.industry.errors.0 }}</div>
          {% endif %}
        </div>

        <div>
          <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
          {{ form.address }}
          {% if form.address.errors %}
            <div class="mt-1 text-sm text-red-600">{{ form.address.errors.0 }}</div>
          {% endif %}
        </div>

        <div class="flex items-center">
          {{ form.is_active }}
          <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-sm text-gray-700">Active Client</label>
          {% if form.is_active.errors %}
            <div class="mt-1 text-sm text-red-600">{{ form.is_active.errors.0 }}</div>
          {% endif %}
        </div>

        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <a href="{% url 'core:client_list' %}" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</a>
          <button type="submit" class="px-4 py-2 bg-primary-600 text-white rounded-md text-sm font-medium hover:bg-primary-700">
            {% if object %}
              Update Client
            {% else %}
              Create Client
            {% endif %}
          </button>
        </div>
      </form>
    </div>
  </div>

  <style>
    /* Style form inputs */
    input[type='text'],
    input[type='email'],
    textarea,
    select {
      width: 100%;
      padding: 0.5rem 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
    
    input[type='text']:focus,
    input[type='email']:focus,
    textarea:focus,
    select:focus {
      outline: none;
      border-color: #0ea5e9;
      box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
    }
    
    input[type='checkbox'] {
      width: 1rem;
      height: 1rem;
      color: #0ea5e9;
      border-radius: 0.25rem;
    }
    
    textarea {
      min-height: 4rem;
      resize: vertical;
    }
  </style>
{% endblock %}
