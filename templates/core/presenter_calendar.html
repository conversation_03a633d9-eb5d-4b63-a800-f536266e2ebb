{% extends 'base.html' %} {% load static %} {% block title %}
  {{ presenter.display_name }} Calendar
{% endblock %} {% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <a href="{% url 'core:presenter_dashboard' presenter.pk %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
          <div class="flex items-center">
            {% if presenter.profile_picture %}
              <img src="{{ presenter.profile_picture.url }}" alt="{{ presenter.display_name }}" class="w-12 h-12 rounded-full object-cover mr-4" />
            {% else %}
              <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                <i class="fa-solid fa-user text-gray-400 text-lg"></i>
              </div>
            {% endif %}
            <div>
              <h1 class="text-2xl font-bold text-gray-900">{{ presenter.display_name }} Calendar</h1>
              <p class="text-gray-600">Your scheduled mentions</p>
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <!-- View Toggle -->
          <div class="flex bg-gray-100 rounded-lg p-1">
            <a href="?view=day&date={{ current_date|date:'Y-m-d' }}"
              class="px-3 py-1 text-sm font-medium rounded-md {% if view == 'day' %}
                 bg-white text-gray-900 shadow-sm
              {% else %}
                 text-gray-600 hover:text-gray-900
              {% endif %}">
              Day
            </a>
            <a href="?view=week&date={{ current_date|date:'Y-m-d' }}"
              class="px-3 py-1 text-sm font-medium rounded-md {% if view == 'week' %}
                 bg-white text-gray-900 shadow-sm
              {% else %}
                 text-gray-600 hover:text-gray-900
              {% endif %}">
              Week
            </a>
            <a href="?view=month&date={{ current_date|date:'Y-m-d' }}"
              class="px-3 py-1 text-sm font-medium rounded-md {% if view == 'month' %}
                 bg-white text-gray-900 shadow-sm
              {% else %}
                 text-gray-600 hover:text-gray-900
              {% endif %}">
              Month
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-4">
        <a href="?view={{ view }}&date={{ prev_date|date:'Y-m-d' }}" class="p-2 text-gray-400 hover:text-gray-600"><i class="fa-solid fa-chevron-left"></i></a>
        <h2 class="text-xl font-semibold text-gray-900">
          {% if view == 'day' %}
            {{ current_date|date:'l, F j, Y' }}
          {% elif view == 'week' %}
            Week of {{ current_date|date:'F j, Y' }}
          {% else %}
            {{ current_date|date:'F Y' }}
          {% endif %}
        </h2>
        <a href="?view={{ view }}&date={{ next_date|date:'Y-m-d' }}" class="p-2 text-gray-400 hover:text-gray-600"><i class="fa-solid fa-chevron-right"></i></a>
      </div>
      <a href="?view={{ view }}&date={{ today|date:'Y-m-d' }}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">Today</a>
    </div>

    <!-- Calendar Content -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      {% if view == 'day' %}
        <!-- Day View -->
        <div class="p-6">
          <div class="grid grid-cols-1 gap-4">
            <!-- Time slots -->
            {% for hour in time_slots %}
              <div class="flex border-b border-gray-100 pb-4">
                <div class="w-20 text-sm text-gray-500 font-medium">{{ hour }}:00</div>
                <div class="flex-1">
                  {% for reading in schedule %}
                    {% if reading.scheduled_time.hour == hour %}
                      <div class="rounded-lg p-3 mb-2 transition-colors {% if reading.actual_read_time %}
                           bg-green-100 border border-green-200 hover:bg-green-200
                        {% elif reading.mention.status == 'approved' %}
                           bg-blue-100 border border-blue-200 hover:bg-blue-200
                        {% else %}
                           bg-yellow-100 border border-yellow-200 hover:bg-yellow-200
                        {% endif %}">
                        <div class="flex items-center justify-between">
                          <div>
                            <h4 class="text-sm font-medium {% if reading.actual_read_time %}
                                 text-green-900
                              {% elif reading.mention.status == 'approved' %}
                                 text-blue-900
                              {% else %}
                                 text-yellow-900
                              {% endif %}">
                              {{ reading.mention.title }}
                            </h4>
                            <p class="text-xs {% if reading.actual_read_time %}
                                 text-green-700
                              {% elif reading.mention.status == 'approved' %}
                                 text-blue-700
                              {% else %}
                                 text-yellow-700
                              {% endif %}">{{ reading.mention.client.name }} • {{ reading.show.name }}</p>
                            <p class="text-xs {% if reading.actual_read_time %}
                                 text-green-600
                              {% elif reading.mention.status == 'approved' %}
                                 text-blue-600
                              {% else %}
                                 text-yellow-600
                              {% endif %}">{{ reading.scheduled_time|time:'g:i A' }} • {{ reading.mention.duration_seconds }}s</p>
                            {% if reading.mention.recurring_mention %}
                              <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-200 text-blue-800 mt-1">
                                <i class="fa-solid fa-repeat mr-1"></i>
                                Recurring
                              </span>
                            {% endif %}
                          </div>
                          <div>
                            {% if reading.actual_read_time %}
                              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-200 text-green-800">
                                <i class="fa-solid fa-check mr-1"></i>
                                Completed
                              </span>
                            {% elif reading.mention.status == 'approved' %}
                              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-200 text-blue-800">
                                <i class="fa-solid fa-calendar-check mr-1"></i>
                                Approved
                              </span>
                            {% else %}
                              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-200 text-yellow-800">
                                <i class="fa-solid fa-clock mr-1"></i>
                                Pending
                              </span>
                            {% endif %}
                          </div>
                        </div>
                      </div>
                    {% endif %}
                  {% endfor %}
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      {% elif view == 'week' %}
        <!-- Week View -->
        <div class="overflow-x-auto">
          <div class="min-w-full">
            <!-- Week header -->
            <div class="grid grid-cols-8 border-b border-gray-200">
              <div class="p-4 text-sm font-medium text-gray-500">Time</div>
              {% for day in week_days %}
                <div class="p-4 text-center border-l border-gray-200">
                  <div class="text-sm font-medium text-gray-900">{{ day.date|date:'D' }}</div>
                  <div class="text-lg font-semibold {% if day.is_today %}
                       text-blue-600
                    {% else %}
                       text-gray-900
                    {% endif %}">{{ day.date|date:'j' }}</div>
                </div>
              {% endfor %}
            </div>

            <!-- Week content -->
            {% for hour in time_slots %}
              <div class="grid grid-cols-8 border-b border-gray-100">
                <div class="p-2 text-sm text-gray-500 border-r border-gray-200">{{ hour }}:00</div>
                {% for day in week_days %}
                  <div class="p-2 border-l border-gray-200 min-h-16 flex flex-col gap-1">
                    {% for reading in day.schedule %}
                      {% if reading.scheduled_time.hour == hour %}
                        <div class="rounded p-1 text-xs cursor-pointer transition-colors {% if reading.actual_read_time %}
                             bg-green-100 border border-green-300 hover:bg-green-200
                          {% elif reading.mention.status == 'approved' %}
                             bg-blue-100 border border-blue-300 hover:bg-blue-200
                          {% else %}
                             bg-yellow-100 border border-yellow-300 hover:bg-yellow-200
                          {% endif %}"
                          title="{{ reading.mention.title }} - {{ reading.mention.client.name }} at {{ reading.scheduled_time|time:'g:i A' }}">
                          <div class="font-semibold truncate {% if reading.actual_read_time %}
                               text-green-800
                            {% elif reading.mention.status == 'approved' %}
                               text-blue-800
                            {% else %}
                               text-yellow-800
                            {% endif %}">{{ reading.mention.title|truncatechars:20 }}</div>

                          <div class="truncate {% if reading.actual_read_time %}
                               text-green-700
                            {% elif reading.mention.status == 'approved' %}
                               text-blue-700
                            {% else %}
                               text-yellow-700
                            {% endif %}">{{ reading.show.name }}</div>

                          <div class="flex items-center justify-between">
                            <span class="{% if reading.actual_read_time %}
                                 text-green-600
                              {% elif reading.mention.status == 'approved' %}
                                 text-blue-600
                              {% else %}
                                 text-yellow-600
                              {% endif %}">
                              {{ reading.scheduled_time|time:'g:i' }}
                            </span>

                            <div class="flex items-center space-x-1">
                              {% if reading.actual_read_time %}
                                <i class="fa-solid fa-check text-green-600 text-xs"></i>
                              {% elif reading.mention.recurring_mention %}
                                <i class="fa-solid fa-repeat text-blue-600 text-xs"></i>
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      {% endif %}
                    {% endfor %}
                  </div>
                {% endfor %}
              </div>
            {% endfor %}
          </div>
        </div>
      {% else %}
        <!-- Month View -->
        <div class="p-6">
          <!-- Month header -->
          <div class="grid grid-cols-7 gap-px mb-4">
            {% for day_name in day_names %}
              <div class="p-2 text-center text-sm font-medium text-gray-500">{{ day_name }}</div>
            {% endfor %}
          </div>

          <!-- Month calendar -->
          <div class="grid grid-cols-7 gap-px bg-gray-200 rounded-lg overflow-hidden">
            <!-- Calendar days -->
            {% for week in month_weeks %}
              {% for day in week %}
                <div class="bg-white p-2 relative {% if day.is_other_month %}
                     bg-gray-50 text-gray-400
                  {% elif day.is_today %}
                     bg-blue-50 ring-2 ring-blue-200
                  {% endif %} hover:bg-gray-50 transition-colors">
                  <!-- Day number -->
                  <div class="flex items-center justify-between mb-2">
                    <div class="text-sm font-semibold {% if day.is_today %}
                         text-blue-600
                      {% elif day.is_other_month %}
                         text-gray-400
                      {% else %}
                         text-gray-900
                      {% endif %}">{{ day.day }}</div>
                    {% if day.schedule|length > 0 %}
                      <div class="text-xs bg-gray-200 text-gray-600 px-2 py-0.5 rounded-full font-medium">{{ day.schedule|length }}</div>
                    {% endif %}
                  </div>

                  <!-- Mentions for this day -->
                  <div class="space-y-1">
                    {% for reading in day.schedule %}
                      <div class="group relative">
                        <div class="text-xs px-2 py-1 rounded cursor-pointer transition-all duration-200 {% if reading.actual_read_time %}
                             bg-green-100 text-green-800 border border-green-200 hover:bg-green-200
                          {% elif reading.mention.status == 'approved' %}
                             bg-blue-100 text-blue-800 border border-blue-200 hover:bg-blue-200
                          {% else %}
                             bg-yellow-100 text-yellow-800 border border-yellow-200 hover:bg-yellow-200
                          {% endif %}"
                          title="{{ reading.mention.title }} - {{ reading.mention.client.name }} at {{ reading.scheduled_time|time:'g:i A' }}">
                          <!-- Time and client -->
                          <div class="flex items-center justify-between">
                            <span class="font-medium">{{ reading.scheduled_time|time:'g:i' }}</span>
                            {% if reading.actual_read_time %}
                              <i class="fa-solid fa-check text-green-600 text-xs"></i>
                            {% elif reading.mention.recurring_mention %}
                              <i class="fa-solid fa-repeat text-blue-600 text-xs"></i>
                            {% endif %}
                          </div>

                          <!-- Client name -->
                          <div class="truncate font-medium">{{ reading.mention.client.name|truncatechars:12 }}</div>

                          <!-- Show name -->
                          {% if reading.show %}
                            <div class="truncate text-xs opacity-75">{{ reading.show.name|truncatechars:15 }}</div>
                          {% endif %}
                        </div>

                        <!-- Hover tooltip -->
                        <div class="absolute bottom-full left-0 mb-2 hidden group-hover:block z-10 bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap shadow-lg">
                          {{ reading.mention.title|truncatechars:30 }}
                          <div class="text-xs opacity-75">{{ reading.mention.client.name }} • {{ reading.scheduled_time|time:'g:i A' }}</div>
                        </div>
                      </div>
                    {% endfor %}
                  </div>
                </div>
              {% endfor %}
            {% endfor %}
          </div>
        </div>
      {% endif %}
    </div>

    <!-- Enhanced Legend -->
    <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <h4 class="text-sm font-semibold text-gray-700 mb-3">Legend</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div class="flex items-center">
          <div class="w-4 h-4 bg-green-100 border border-green-300 rounded mr-2"></div>
          <span class="text-gray-600">Completed</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-100 border border-blue-300 rounded mr-2"></div>
          <span class="text-gray-600">Approved</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-yellow-100 border border-yellow-300 rounded mr-2"></div>
          <span class="text-gray-600">Pending</span>
        </div>
        <div class="flex items-center">
          <i class="fa-solid fa-repeat text-blue-600 text-sm mr-2"></i>
          <span class="text-gray-600">Recurring</span>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
