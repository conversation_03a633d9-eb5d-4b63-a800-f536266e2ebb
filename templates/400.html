{% extends 'base.html' %}
{% load static %}

{% block title %}Bad Request - RadioMention{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-orange-100">
            <svg class="h-16 w-16 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
        </div>

        <!-- Error Content -->
        <div>
            <h1 class="text-6xl font-bold text-gray-900 mb-4">400</h1>
            <h2 class="text-2xl font-semibold text-gray-700 mb-4">Bad Request</h2>
            <p class="text-gray-600 mb-8">
                The request could not be understood by the server. This usually happens when the request is malformed or contains invalid data.
            </p>
        </div>

        <!-- Common Causes -->
        <div class="bg-yellow-50 rounded-lg p-4 mb-6">
            <h3 class="font-medium text-yellow-800 mb-2">Common causes:</h3>
            <ul class="text-sm text-yellow-700 text-left space-y-1">
                <li>• Invalid form data or missing required fields</li>
                <li>• Corrupted or expired session</li>
                <li>• Invalid file upload or size limit exceeded</li>
                <li>• Malformed URL parameters</li>
            </ul>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
            <button onclick="history.back()" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Go Back and Try Again
            </button>
            
            <button onclick="location.reload()" class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh Page
            </button>

            <a href="{% url 'core:dashboard' %}" class="w-full flex justify-center py-3 px-4 border border-green-300 rounded-md shadow-sm text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                Go to Dashboard
            </a>
        </div>

        <!-- Troubleshooting Tips -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Troubleshooting Tips</h3>
            <div class="text-sm text-gray-600 text-left space-y-3">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <strong>Check your input:</strong> Make sure all required fields are filled out correctly
                    </div>
                </div>
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <strong>Clear your browser cache:</strong> Sometimes cached data can cause issues
                    </div>
                </div>
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <strong>Try a different browser:</strong> Some browsers may handle requests differently
                    </div>
                </div>
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <strong>Check file sizes:</strong> If uploading files, ensure they're within size limits
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="mt-6 p-4 bg-gray-100 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">Still Having Issues?</h4>
            <p class="text-sm text-gray-600 mb-2">
                If the problem persists, please contact support with the following information:
            </p>
            <div class="text-xs text-gray-500 bg-white p-2 rounded border">
                <div><strong>Time:</strong> <span id="error-time"></span></div>
                <div><strong>URL:</strong> <span id="current-url"></span></div>
                <div><strong>Browser:</strong> <span id="user-agent"></span></div>
            </div>
            <p class="text-sm text-gray-600 mt-2">
                <strong>Email:</strong> 
                <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-500">
                    <EMAIL>
                </a>
            </p>
        </div>
    </div>
</div>

<script>
// Populate error information
document.getElementById('error-time').textContent = new Date().toLocaleString();
document.getElementById('current-url').textContent = window.location.href;
document.getElementById('user-agent').textContent = navigator.userAgent.substring(0, 50) + '...';
</script>
{% endblock %}
