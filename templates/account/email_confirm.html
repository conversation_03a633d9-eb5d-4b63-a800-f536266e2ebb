{% extends 'base.html' %}
{% load static %}
{% load account %}

{% block title %}Confirm Email - RadioMention{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-12 bg-primary-600 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-envelope text-white text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Confirm Email Address
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Please confirm your email address to complete your registration
            </p>
        </div>
        
        {% if confirmation %}
        <div class="mt-8 space-y-6">
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fa-solid fa-info-circle text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">
                            Confirm Email Address
                        </h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p>Please confirm that <strong>{{ confirmation.email_address.email }}</strong> is an email address for user {{ confirmation.email_address.user.get_username }}.</p>
                        </div>
                    </div>
                </div>
            </div>

            <form method="post">
                {% csrf_token %}
                <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <i class="fa-solid fa-check text-primary-500 group-hover:text-primary-400"></i>
                    </span>
                    Confirm Email Address
                </button>
            </form>
        </div>
        {% else %}
        <div class="mt-8 space-y-6">
            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fa-solid fa-exclamation-triangle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            Invalid Confirmation Link
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>This email confirmation link is invalid or has already been used.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <a href="{% url 'account_login' %}" class="font-medium text-primary-600 hover:text-primary-500">
                    Return to Login
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
