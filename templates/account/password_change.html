{% extends 'base.html' %}
{% load static %}

{% block title %}Change Password{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
  <!-- Header -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center">
        <a href="{% url 'core:dashboard' %}" class="text-gray-500 hover:text-gray-700 mr-4">
          <i class="fa-solid fa-arrow-left"></i>
        </a>
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            <i class="fa-solid fa-key text-blue-600"></i>
          </div>
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Change Password</h1>
            <p class="text-sm text-gray-500">Update your account password for security</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Form -->
    <div class="p-6">
      <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Display form errors -->
        {% if form.non_field_errors %}
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fa-solid fa-exclamation-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Please correct the following errors:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc list-inside space-y-1">
                    {% for error in form.non_field_errors %}
                      <li>{{ error }}</li>
                    {% endfor %}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        {% endif %}

        <!-- Current Password -->
        <div>
          <label for="{{ form.oldpassword.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
            Current Password <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            {{ form.oldpassword }}
            <button type="button" onclick="togglePasswordVisibility('{{ form.oldpassword.id_for_label }}')" 
                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <i id="{{ form.oldpassword.id_for_label }}-toggle" class="fa-solid fa-eye text-gray-400 hover:text-gray-600"></i>
            </button>
          </div>
          {% if form.oldpassword.errors %}
            <p class="mt-1 text-sm text-red-600">{{ form.oldpassword.errors.0 }}</p>
          {% endif %}
          <p class="text-xs text-gray-500 mt-1">Enter your current password to confirm your identity</p>
        </div>

        <!-- New Password -->
        <div>
          <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
            New Password <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            {{ form.password1 }}
            <button type="button" onclick="togglePasswordVisibility('{{ form.password1.id_for_label }}')" 
                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <i id="{{ form.password1.id_for_label }}-toggle" class="fa-solid fa-eye text-gray-400 hover:text-gray-600"></i>
            </button>
          </div>
          {% if form.password1.errors %}
            <p class="mt-1 text-sm text-red-600">{{ form.password1.errors.0 }}</p>
          {% endif %}
          
          <!-- Password Strength Indicator -->
          <div class="mt-2">
            <div class="flex items-center space-x-2">
              <span class="text-xs text-gray-500">Password strength:</span>
              <div class="flex space-x-1">
                <div id="strength-1" class="w-2 h-2 rounded-full bg-gray-200"></div>
                <div id="strength-2" class="w-2 h-2 rounded-full bg-gray-200"></div>
                <div id="strength-3" class="w-2 h-2 rounded-full bg-gray-200"></div>
                <div id="strength-4" class="w-2 h-2 rounded-full bg-gray-200"></div>
                <div id="strength-5" class="w-2 h-2 rounded-full bg-gray-200"></div>
              </div>
              <span id="strength-text" class="text-xs text-gray-500">Enter password</span>
            </div>
          </div>
          
          <!-- Password Requirements -->
          <div class="mt-2 text-xs text-gray-500">
            <p class="font-medium mb-1">Password must contain:</p>
            <ul class="space-y-1">
              <li id="req-length" class="flex items-center">
                <i class="fa-solid fa-circle text-gray-300 mr-2 text-xs"></i>
                At least 8 characters
              </li>
              <li id="req-lowercase" class="flex items-center">
                <i class="fa-solid fa-circle text-gray-300 mr-2 text-xs"></i>
                One lowercase letter
              </li>
              <li id="req-uppercase" class="flex items-center">
                <i class="fa-solid fa-circle text-gray-300 mr-2 text-xs"></i>
                One uppercase letter
              </li>
              <li id="req-number" class="flex items-center">
                <i class="fa-solid fa-circle text-gray-300 mr-2 text-xs"></i>
                One number
              </li>
              <li id="req-special" class="flex items-center">
                <i class="fa-solid fa-circle text-gray-300 mr-2 text-xs"></i>
                One special character
              </li>
            </ul>
          </div>
        </div>

        <!-- Confirm New Password -->
        <div>
          <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
            Confirm New Password <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            {{ form.password2 }}
            <button type="button" onclick="togglePasswordVisibility('{{ form.password2.id_for_label }}')" 
                    class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <i id="{{ form.password2.id_for_label }}-toggle" class="fa-solid fa-eye text-gray-400 hover:text-gray-600"></i>
            </button>
          </div>
          {% if form.password2.errors %}
            <p class="mt-1 text-sm text-red-600">{{ form.password2.errors.0 }}</p>
          {% endif %}
          <div id="password-match" class="mt-1 text-xs"></div>
        </div>

        <!-- Security Notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fa-solid fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">Security Notice</h3>
              <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                  <li>You will be logged out after changing your password</li>
                  <li>Use a unique password that you don't use elsewhere</li>
                  <li>Consider using a password manager for better security</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
          <a href="{% url 'core:dashboard' %}" 
             class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            Cancel
          </a>
          <button type="submit" id="submit-btn" disabled
                  class="px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-300 disabled:cursor-not-allowed">
            <i class="fa-solid fa-key mr-2"></i>
            Change Password
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  /* Custom styles for form fields */
  input[type="password"] {
    @apply w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }
  
  .strength-weak { @apply bg-red-400; }
  .strength-fair { @apply bg-yellow-400; }
  .strength-good { @apply bg-blue-400; }
  .strength-strong { @apply bg-green-400; }
  
  .requirement-met i { @apply text-green-500; }
  .requirement-met { @apply text-green-600; }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Password visibility toggle
function togglePasswordVisibility(fieldId) {
  const passwordInput = document.getElementById(fieldId);
  const toggleIcon = document.getElementById(fieldId + '-toggle');

  if (passwordInput.type === 'password') {
    passwordInput.type = 'text';
    toggleIcon.classList.remove('fa-eye');
    toggleIcon.classList.add('fa-eye-slash');
  } else {
    passwordInput.type = 'password';
    toggleIcon.classList.remove('fa-eye-slash');
    toggleIcon.classList.add('fa-eye');
  }
}

// Password strength calculation
function calculatePasswordStrength(password) {
  let strength = 0;
  const requirements = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /[0-9]/.test(password),
    special: /[^A-Za-z0-9]/.test(password)
  };

  // Update requirement indicators
  Object.keys(requirements).forEach(req => {
    const element = document.getElementById(`req-${req}`);
    const icon = element.querySelector('i');
    
    if (requirements[req]) {
      element.classList.add('requirement-met');
      icon.classList.remove('fa-circle', 'text-gray-300');
      icon.classList.add('fa-check-circle', 'text-green-500');
      strength++;
    } else {
      element.classList.remove('requirement-met');
      icon.classList.remove('fa-check-circle', 'text-green-500');
      icon.classList.add('fa-circle', 'text-gray-300');
    }
  });

  return strength;
}

// Update password strength indicator
function updatePasswordStrength(password) {
  const strength = calculatePasswordStrength(password);
  const indicators = document.querySelectorAll('[id^="strength-"]');
  const strengthText = document.getElementById('strength-text');

  // Reset all indicators
  indicators.forEach(indicator => {
    indicator.className = 'w-2 h-2 rounded-full bg-gray-200';
  });

  // Update based on strength
  let strengthClass = '';
  let strengthLabel = '';

  if (strength === 0) {
    strengthLabel = 'Enter password';
  } else if (strength <= 2) {
    strengthClass = 'strength-weak';
    strengthLabel = 'Weak';
  } else if (strength <= 3) {
    strengthClass = 'strength-fair';
    strengthLabel = 'Fair';
  } else if (strength <= 4) {
    strengthClass = 'strength-good';
    strengthLabel = 'Good';
  } else {
    strengthClass = 'strength-strong';
    strengthLabel = 'Strong';
  }

  // Apply strength styling
  for (let i = 0; i < strength; i++) {
    indicators[i].classList.remove('bg-gray-200');
    indicators[i].classList.add(strengthClass);
  }

  strengthText.textContent = strengthLabel;
  strengthText.className = `text-xs ${strength >= 4 ? 'text-green-600' : strength >= 3 ? 'text-blue-600' : strength >= 2 ? 'text-yellow-600' : 'text-red-600'}`;
}

// Password confirmation check
function checkPasswordMatch() {
  const password1 = document.getElementById('{{ form.password1.id_for_label }}').value;
  const password2 = document.getElementById('{{ form.password2.id_for_label }}').value;
  const matchIndicator = document.getElementById('password-match');
  const submitBtn = document.getElementById('submit-btn');

  if (password2) {
    if (password1 === password2) {
      matchIndicator.innerHTML = '<i class="fa-solid fa-check text-green-500 mr-1"></i><span class="text-green-600">Passwords match</span>';
      submitBtn.disabled = false;
    } else {
      matchIndicator.innerHTML = '<i class="fa-solid fa-times text-red-500 mr-1"></i><span class="text-red-600">Passwords do not match</span>';
      submitBtn.disabled = true;
    }
  } else {
    matchIndicator.innerHTML = '';
    submitBtn.disabled = true;
  }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
  const password1Field = document.getElementById('{{ form.password1.id_for_label }}');
  const password2Field = document.getElementById('{{ form.password2.id_for_label }}');

  password1Field.addEventListener('input', function() {
    updatePasswordStrength(this.value);
    checkPasswordMatch();
  });

  password2Field.addEventListener('input', checkPasswordMatch);
});
</script>
{% endblock %}
