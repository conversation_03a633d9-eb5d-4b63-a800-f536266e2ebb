{% extends 'base.html' %}
{% load static %}

{% block title %}Password Changed Successfully{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
  <!-- Success Message -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-8 text-center">
      <!-- Success Icon -->
      <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fa-solid fa-check text-green-600 text-2xl"></i>
      </div>
      
      <!-- Success Title -->
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Password Changed Successfully!</h1>
      <p class="text-gray-600 mb-6">Your password has been updated and you're now more secure.</p>
      
      <!-- Security Information -->
      <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6 text-left">
        <div class="flex">
          <div class="flex-shrink-0">
            <i class="fa-solid fa-shield-alt text-blue-400"></i>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Security Notice</h3>
            <div class="mt-2 text-sm text-blue-700">
              <ul class="list-disc list-inside space-y-1">
                <li>Your password was changed on {{ user.last_login|date:"F j, Y \a\t g:i A" }}</li>
                <li>You will be automatically logged out for security</li>
                <li>Please log in again with your new password</li>
                <li>If you didn't make this change, contact your administrator immediately</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <a href="{% url 'account_login' %}" 
           class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          <i class="fa-solid fa-sign-in-alt mr-2"></i>
          Log In Again
        </a>
        <a href="{% url 'core:dashboard' %}" 
           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          <i class="fa-solid fa-home mr-2"></i>
          Go to Dashboard
        </a>
      </div>
    </div>
  </div>
  
  <!-- Security Tips -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">
        <i class="fa-solid fa-lightbulb text-yellow-500 mr-2"></i>
        Security Tips
      </h3>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-key text-green-600 text-sm"></i>
            </div>
          </div>
          <div class="ml-3">
            <h4 class="text-sm font-medium text-gray-900">Use Strong Passwords</h4>
            <p class="text-sm text-gray-500 mt-1">Create unique passwords with a mix of letters, numbers, and symbols.</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-user-shield text-blue-600 text-sm"></i>
            </div>
          </div>
          <div class="ml-3">
            <h4 class="text-sm font-medium text-gray-900">Password Manager</h4>
            <p class="text-sm text-gray-500 mt-1">Consider using a password manager to generate and store secure passwords.</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-clock text-purple-600 text-sm"></i>
            </div>
          </div>
          <div class="ml-3">
            <h4 class="text-sm font-medium text-gray-900">Regular Updates</h4>
            <p class="text-sm text-gray-500 mt-1">Change your password regularly, especially if you suspect it may be compromised.</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-exclamation-triangle text-red-600 text-sm"></i>
            </div>
          </div>
          <div class="ml-3">
            <h4 class="text-sm font-medium text-gray-900">Stay Alert</h4>
            <p class="text-sm text-gray-500 mt-1">Never share your password and be cautious of phishing attempts.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Auto-logout countdown (optional) -->
<div id="logout-countdown" class="fixed bottom-4 right-4 bg-yellow-100 border border-yellow-300 rounded-lg p-4 shadow-lg hidden">
  <div class="flex items-center">
    <div class="flex-shrink-0">
      <i class="fa-solid fa-clock text-yellow-600"></i>
    </div>
    <div class="ml-3">
      <p class="text-sm font-medium text-yellow-800">
        Automatic logout in <span id="countdown-timer">30</span> seconds
      </p>
      <button onclick="cancelLogout()" class="text-xs text-yellow-600 hover:text-yellow-800 underline">
        Cancel
      </button>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Optional: Auto-logout countdown
let logoutTimer;
let countdownTimer;
let timeLeft = 30;

function startLogoutCountdown() {
  const countdownElement = document.getElementById('logout-countdown');
  const timerElement = document.getElementById('countdown-timer');
  
  countdownElement.classList.remove('hidden');
  
  countdownTimer = setInterval(() => {
    timeLeft--;
    timerElement.textContent = timeLeft;
    
    if (timeLeft <= 0) {
      clearInterval(countdownTimer);
      window.location.href = '{% url "account_logout" %}';
    }
  }, 1000);
  
  logoutTimer = setTimeout(() => {
    window.location.href = '{% url "account_logout" %}';
  }, 30000);
}

function cancelLogout() {
  clearTimeout(logoutTimer);
  clearInterval(countdownTimer);
  document.getElementById('logout-countdown').classList.add('hidden');
}

// Start countdown after 5 seconds
setTimeout(startLogoutCountdown, 5000);
</script>
{% endblock %}
