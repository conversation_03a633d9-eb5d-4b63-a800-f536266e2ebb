{% load static %}
{% load account %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sign Up - RadioMention</title>

    <!-- Tailwind CSS -->
    <!-- Offline Font Awesome -->
    <link rel="stylesheet" href="{% static 'css/fontawesome.min.css' %}" />
    <script>
      window.FontAwesomeConfig = { autoReplaceSvg: 'nest' }
    </script>
    <script src="{% static 'js/fontawesome.min.js' %}"></script>

    <!-- Offline Inter Font -->
    <link rel="stylesheet" href="{% static 'css/inter-font.css' %}" />

    <!-- Tailwind CSS - Local Build -->
    <link rel="stylesheet" href="{% static 'css/tailwind.css' %}?v=3" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/compact.css' %}" />
    <link rel="stylesheet" href="{% static 'css/print.css' %}" media="print" />
    <!-- Font Awesome -->
    <script>
      window.FontAwesomeConfig = { autoReplaceSvg: 'nest' }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&display=swap" />

    <style>
      ::-webkit-scrollbar {
        display: none;
      }
      
      body {
        font-family: 'Inter', sans-serif;
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      
      .login-card {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
      
      .input-group {
        position: relative;
      }
      
      .input-group input:focus + label,
      .input-group input:not(:placeholder-shown) + label {
        transform: translateY(-1.5rem) scale(0.875);
        color: #3b82f6;
      }
      
      .input-group label {
        position: absolute;
        left: 0.75rem;
        top: 0.75rem;
        color: #6b7280;
        transition: all 0.2s ease;
        pointer-events: none;
        background: white;
        padding: 0 0.25rem;
      }
      
      .floating-element {
        animation: float 6s ease-in-out infinite;
      }
      
      .floating-element:nth-child(2) {
        animation-delay: -2s;
      }
      
      .floating-element:nth-child(3) {
        animation-delay: -4s;
      }
      
      @keyframes float {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-20px) rotate(180deg);
        }
      }
      
      .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
    </style>

    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                50: '#f0f9ff',
                100: '#e0f2fe',
                200: '#bae6fd',
                300: '#7dd3fc',
                400: '#38bdf8',
                500: '#0ea5e9',
                600: '#0284c7',
                700: '#0369a1',
                800: '#075985',
                900: '#0c4a6e'
              }
            },
            fontFamily: {
              sans: ['Inter', 'sans-serif']
            }
          }
        }
      }
    </script>
  </head>
  <body>
    <div class="gradient-bg min-h-screen flex items-center justify-center p-4 relative overflow-hidden">
      <!-- Floating Background Elements -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="floating-element absolute top-20 left-20 w-16 h-16 bg-white bg-opacity-10 rounded-full"></div>
        <div class="floating-element absolute top-40 right-32 w-12 h-12 bg-white bg-opacity-10 rounded-full"></div>
        <div class="floating-element absolute bottom-32 left-40 w-20 h-20 bg-white bg-opacity-10 rounded-full"></div>
        <div class="floating-element absolute bottom-20 right-20 w-8 h-8 bg-white bg-opacity-10 rounded-full"></div>
        <div class="floating-element absolute top-1/2 left-10 w-6 h-6 bg-white bg-opacity-10 rounded-full"></div>
        <div class="floating-element absolute top-1/3 right-10 w-14 h-14 bg-white bg-opacity-10 rounded-full"></div>
      </div>

      <!-- Signup Container -->
      <div class="w-full max-w-6xl mx-auto">
        <div class="grid lg:grid-cols-2 gap-8 items-center">
          <!-- Left Side - Branding -->
          <div class="text-center lg:text-left text-white">
            <div class="mb-8">
              <div class="flex items-center justify-center lg:justify-start mb-6">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                  <i class="fa-solid fa-radio text-3xl text-white"></i>
                </div>
                <div class="ml-4">
                  <h1 class="text-3xl font-bold">RadioMention</h1>
                  <p class="text-lg opacity-90">Management System</p>
                </div>
              </div>
              <h2 class="text-4xl lg:text-5xl font-bold mb-4 leading-tight">
                Join Our<br />
                <span class="text-yellow-300">Radio Network</span>
              </h2>
              <p class="text-xl opacity-90 mb-8">Create your account and start managing radio mentions with our powerful platform</p>
            </div>

            <!-- Feature Highlights -->
            <div class="space-y-4">
              <div class="flex items-center justify-center lg:justify-start">
                <div class="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center mr-4">
                  <i class="fa-solid fa-check text-white text-sm"></i>
                </div>
                <span class="text-lg">Free account setup</span>
              </div>
              <div class="flex items-center justify-center lg:justify-start">
                <div class="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center mr-4">
                  <i class="fa-solid fa-check text-white text-sm"></i>
                </div>
                <span class="text-lg">Instant access to features</span>
              </div>
              <div class="flex items-center justify-center lg:justify-start">
                <div class="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center mr-4">
                  <i class="fa-solid fa-check text-white text-sm"></i>
                </div>
                <span class="text-lg">24/7 customer support</span>
              </div>
            </div>
          </div>

          <!-- Right Side - Signup Form -->
          <div class="w-full max-w-md mx-auto">
            <div class="login-card rounded-2xl shadow-2xl p-8">
              <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-2">Create Account</h3>
                <p class="text-gray-600">Join RadioMention and get started today</p>
              </div>

              <!-- Signup Form -->
              <form method="post" class="space-y-6">
                {% csrf_token %}

                {% if form.errors or form.non_field_errors %}
                  <div class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <i class="fa-solid fa-exclamation-circle text-red-400"></i>
                      </div>
                      <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                        <div class="mt-2 text-sm text-red-700">
                          <ul class="list-disc pl-5 space-y-1">
                            {% for field, errors in form.errors.items %}
                              {% for error in errors %}
                                <li>{{ error }}</li>
                              {% endfor %}
                            {% endfor %}
                            {% for error in form.non_field_errors %}
                              <li>{{ error }}</li>
                            {% endfor %}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                {% endif %}

                <div class="input-group">
                  <input type="text" id="{{ form.username.id_for_label }}" name="{{ form.username.name }}" placeholder=" " value="{{ form.username.value|default:'' }}" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors" required />
                  <label for="{{ form.username.id_for_label }}">Username</label>
                </div>

                <div class="input-group">
                  <input type="email" id="{{ form.email.id_for_label }}" name="{{ form.email.name }}" placeholder=" " value="{{ form.email.value|default:'' }}" class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors" required />
                  <label for="{{ form.email.id_for_label }}">Email Address</label>
                </div>

                <div class="input-group">
                  <input type="password" id="{{ form.password1.id_for_label }}" name="{{ form.password1.name }}" placeholder=" " class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors" required />
                  <label for="{{ form.password1.id_for_label }}">Password</label>
                  <button type="button" class="absolute right-3 top-3 text-gray-500 hover:text-gray-700" onclick="togglePassword('{{ form.password1.id_for_label }}')"><i class="fa-solid fa-eye" id="password1-toggle"></i></button>
                </div>

                <div class="input-group">
                  <input type="password" id="{{ form.password2.id_for_label }}" name="{{ form.password2.name }}" placeholder=" " class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors" required />
                  <label for="{{ form.password2.id_for_label }}">Confirm Password</label>
                  <button type="button" class="absolute right-3 top-3 text-gray-500 hover:text-gray-700" onclick="togglePassword('{{ form.password2.id_for_label }}')"><i class="fa-solid fa-eye" id="password2-toggle"></i></button>
                </div>

                <button type="submit" class="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors">Create Account</button>
              </form>

              <!-- Footer -->
              <div class="mt-8 text-center">
                <p class="text-sm text-gray-600">
                  Already have an account?
                  <a href="{% url 'account_login' %}" class="text-primary-600 hover:text-primary-700 font-medium">Sign in</a>
                </p>
                <div class="mt-4 text-xs text-gray-500 text-center">
                  By creating an account, you agree to our
                  <a href="#" class="text-primary-600 hover:text-primary-500">Terms of Service</a>
                  and
                  <a href="#" class="text-primary-600 hover:text-primary-500">Privacy Policy</a>.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      function togglePassword(fieldId) {
        const passwordInput = document.getElementById(fieldId)
        const toggleIcon = document.getElementById(fieldId.includes('password1') ? 'password1-toggle' : 'password2-toggle')
      
        if (passwordInput.type === 'password') {
          passwordInput.type = 'text'
          toggleIcon.classList.remove('fa-eye')
          toggleIcon.classList.add('fa-eye-slash')
        } else {
          passwordInput.type = 'password'
          toggleIcon.classList.remove('fa-eye-slash')
          toggleIcon.classList.add('fa-eye')
        }
      }
    </script>
  </body>
</html>
