<!-- Notification Widget -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i class="fas fa-bell mr-2 text-blue-600"></i>
            Recent Notifications
        </h3>
        <div class="flex items-center space-x-2">
            <span id="notification-count" class="text-sm text-gray-500">Loading...</span>
            <button onclick="refreshNotifications()" class="text-sm text-blue-600 hover:text-blue-800" title="Refresh">
                <i class="fas fa-refresh"></i>
            </button>
        </div>
    </div>
    <div class="p-6">
        <div id="notification-list" class="space-y-3">
            <!-- Notifications will be loaded here -->
            <div class="text-center text-gray-500 py-4">
                <i class="fas fa-spinner fa-spin mr-2"></i>
                Loading notifications...
            </div>
        </div>
        
        <!-- View All Link -->
        <div class="mt-4 pt-4 border-t border-gray-200 text-center">
            <a href="{% url 'settings:user_preferences' %}" class="text-sm text-blue-600 hover:text-blue-800">
                <i class="fas fa-cog mr-1"></i>
                Notification Settings
            </a>
        </div>
    </div>
</div>

<script>
// Notification Widget Functions
function refreshNotifications() {
    const notificationList = document.getElementById('notification-list');
    const notificationCount = document.getElementById('notification-count');
    
    // Show loading state
    notificationList.innerHTML = `
        <div class="text-center text-gray-500 py-4">
            <i class="fas fa-spinner fa-spin mr-2"></i>
            Loading notifications...
        </div>
    `;
    
    // Fetch recent notifications
    fetch('/api/notifications/recent/')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.notifications) {
                displayNotifications(data.notifications);
                notificationCount.textContent = `${data.notifications.length} recent`;
            } else {
                showNoNotifications();
                notificationCount.textContent = '0 notifications';
            }
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
            showErrorState();
            notificationCount.textContent = 'Error loading';
        });
}

function displayNotifications(notifications) {
    const notificationList = document.getElementById('notification-list');
    
    if (notifications.length === 0) {
        showNoNotifications();
        return;
    }
    
    notificationList.innerHTML = notifications.map(notification => `
        <div class="flex items-start space-x-3 p-3 rounded-lg ${notification.read ? 'bg-gray-50' : 'bg-blue-50'} hover:bg-gray-100 transition-colors">
            <div class="flex-shrink-0">
                <i class="fas ${getNotificationIcon(notification.type)} text-${getNotificationColor(notification.type)}-500"></i>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                <p class="text-sm text-gray-600 truncate">${notification.message}</p>
                <p class="text-xs text-gray-500 mt-1">${formatNotificationTime(notification.created_at)}</p>
            </div>
            ${!notification.read ? '<div class="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-2"></div>' : ''}
        </div>
    `).join('');
}

function showNoNotifications() {
    const notificationList = document.getElementById('notification-list');
    notificationList.innerHTML = `
        <div class="text-center text-gray-500 py-8">
            <i class="fas fa-bell-slash text-3xl mb-2"></i>
            <p class="text-sm">No recent notifications</p>
            <p class="text-xs mt-1">You're all caught up!</p>
        </div>
    `;
}

function showErrorState() {
    const notificationList = document.getElementById('notification-list');
    notificationList.innerHTML = `
        <div class="text-center text-red-500 py-4">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <p class="text-sm">Error loading notifications</p>
            <button onclick="refreshNotifications()" class="text-xs text-blue-600 hover:text-blue-800 mt-2">
                Try again
            </button>
        </div>
    `;
}

function getNotificationIcon(type) {
    const icons = {
        'show_alert': 'fa-broadcast-tower',
        'approval_reminder': 'fa-clock',
        'system_alert': 'fa-exclamation-triangle',
        'mention_update': 'fa-bullhorn',
        'schedule_change': 'fa-calendar-alt',
        'conflict_alert': 'fa-exclamation-circle',
        'deadline_alert': 'fa-hourglass-half',
        'performance_report': 'fa-chart-line'
    };
    return icons[type] || 'fa-info-circle';
}

function getNotificationColor(type) {
    const colors = {
        'show_alert': 'red',
        'approval_reminder': 'yellow',
        'system_alert': 'red',
        'mention_update': 'blue',
        'schedule_change': 'green',
        'conflict_alert': 'orange',
        'deadline_alert': 'purple',
        'performance_report': 'indigo'
    };
    return colors[type] || 'blue';
}

function formatNotificationTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    if (diff < 604800000) return `${Math.floor(diff / 86400000)}d ago`;
    return date.toLocaleDateString();
}

// Auto-refresh notifications every 30 seconds
setInterval(refreshNotifications, 30000);

// Load notifications when the widget is first displayed
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure the page is fully loaded
    setTimeout(refreshNotifications, 1000);
});
</script>
