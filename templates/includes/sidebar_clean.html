{% load permission_tags %}
<!-- Clean Radio Station Sidebar -->
<div id="sidebar" class="w-64 bg-white border-r border-gray-200 flex flex-col">
  <!-- Header -->
  <div class="p-4 border-b border-gray-200">
    <div class="flex items-center">
      <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white shadow-sm">
        <i class="fa-solid fa-radio text-sm"></i>
      </div>
      <h1 class="ml-3 font-bold text-gray-900 text-base">RadioMention</h1>
    </div>
  </div>

  <!-- Navigation -->
  <nav class="flex-1 overflow-y-auto py-4">
    <!-- Dashboard -->
    <div class="px-4">
      <a href="{% url 'core:dashboard' %}" 
         class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'dashboard' %}bg-primary-50 text-primary-700 border-r-2 border-primary-500{% else %}text-gray-700 hover:bg-primary-50 hover:text-primary-900{% endif %}">
        <i class="fa-solid fa-home w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'dashboard' %}text-primary-600{% else %}text-gray-400 group-hover:text-primary-600{% endif %}"></i>
        <span>Dashboard</span>
      </a>
    </div>

    <!-- Live Show -->
    {% if current_membership.role == 'presenter' or can_manage_mentions %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-red-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-broadcast-tower mr-2"></i>
          Live Show
        </h3>
        <div class="space-y-1">
          <a href="{% url 'shows:live_show' %}" 
             class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'live_show' %}bg-red-50 text-red-700 border-r-2 border-red-500{% else %}text-gray-700 hover:bg-red-50 hover:text-red-900{% endif %}">
            <i class="fa-solid fa-microphone w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'live_show' %}text-red-600{% else %}text-gray-400 group-hover:text-red-600{% endif %}"></i>
            <span>On-Air Interface</span>
            <span class="ml-auto w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
          </a>
        </div>
      </div>
    {% endif %}

    <!-- Scheduling -->
    {% if can_manage_mentions or current_membership.role == 'presenter' %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-blue-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-calendar mr-2"></i>
          Scheduling
        </h3>
        <div class="space-y-1">
          <a href="{% url 'mentions:calendar_interface' %}" 
             class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'calendar_interface' %}bg-blue-50 text-blue-700 border-r-2 border-blue-500{% else %}text-gray-700 hover:bg-blue-50 hover:text-blue-900{% endif %}">
            <i class="fa-solid fa-calendar-days w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'calendar_interface' %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}"></i>
            <span>Calendar & Schedule</span>
          </a>
          
          {% if current_membership.role == 'owner' or current_membership.role == 'admin' or current_membership.role == 'manager' %}
            <a href="{% url 'mentions:recurring_mentions' %}" 
               class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'recurring' in request.resolver_match.url_name %}bg-blue-50 text-blue-700 border-r-2 border-blue-500{% else %}text-gray-700 hover:bg-blue-50 hover:text-blue-900{% endif %}">
              <i class="fa-solid fa-repeat w-5 h-5 mr-3 {% if 'recurring' in request.resolver_match.url_name %}text-blue-600{% else %}text-gray-400 group-hover:text-blue-600{% endif %}"></i>
              <span>Recurring Patterns</span>
              <span class="ml-auto text-xs text-green-600 font-medium bg-green-100 px-2 py-0.5 rounded-full">NEW</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- Workflow & Approvals -->
    {% if can_approve_mentions %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-orange-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-clipboard-check mr-2"></i>
          Workflow
        </h3>
        <div class="space-y-1">
          <a href="{% url 'mentions:approval_workflow' %}" 
             class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'approval_workflow' %}bg-orange-50 text-orange-700 border-r-2 border-orange-500{% else %}text-gray-700 hover:bg-orange-50 hover:text-orange-900{% endif %}">
            <i class="fa-solid fa-check-to-slot w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'approval_workflow' %}text-orange-600{% else %}text-gray-400 group-hover:text-orange-600{% endif %}"></i>
            <span>Pending Approvals</span>
            {% if pending_approvals_count %}
              <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center font-semibold">{{ pending_approvals_count }}</span>
            {% endif %}
          </a>
        </div>
      </div>
    {% endif %}

    <!-- Content Management -->
    {% if can_manage_mentions or can_manage_shows or can_manage_clients or can_manage_presenters %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-folder mr-2"></i>
          Content
        </h3>
        <div class="space-y-1">
          <!-- Mentions -->
          {% if can_manage_mentions %}
            <a href="{% url 'mentions:mention_list' %}" 
               class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'mention' in request.resolver_match.url_name and 'approval' not in request.resolver_match.url_name and 'recurring' not in request.resolver_match.url_name %}bg-primary-50 text-primary-700 border-r-2 border-primary-500{% else %}text-gray-700 hover:bg-primary-50 hover:text-primary-900{% endif %}">
              <i class="fa-solid fa-bullhorn w-5 h-5 mr-3 {% if 'mention' in request.resolver_match.url_name and 'approval' not in request.resolver_match.url_name and 'recurring' not in request.resolver_match.url_name %}text-primary-600{% else %}text-gray-400 group-hover:text-primary-600{% endif %}"></i>
              <span>Mentions</span>
            </a>
          {% endif %}

          <!-- Shows -->
          {% if can_manage_shows %}
            <a href="{% url 'shows:show_list' %}" 
               class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'show' in request.resolver_match.url_name and 'live' not in request.resolver_match.url_name %}bg-primary-50 text-primary-700 border-r-2 border-primary-500{% else %}text-gray-700 hover:bg-primary-50 hover:text-primary-900{% endif %}">
              <i class="fa-solid fa-radio w-5 h-5 mr-3 {% if 'show' in request.resolver_match.url_name and 'live' not in request.resolver_match.url_name %}text-primary-600{% else %}text-gray-400 group-hover:text-primary-600{% endif %}"></i>
              <span>Shows</span>
            </a>
          {% endif %}

          <!-- Clients -->
          {% if can_manage_clients %}
            <a href="{% url 'clients:client_list' %}" 
               class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'client' in request.resolver_match.url_name %}bg-primary-50 text-primary-700 border-r-2 border-primary-500{% else %}text-gray-700 hover:bg-primary-50 hover:text-primary-900{% endif %}">
              <i class="fa-solid fa-building w-5 h-5 mr-3 {% if 'client' in request.resolver_match.url_name %}text-primary-600{% else %}text-gray-400 group-hover:text-primary-600{% endif %}"></i>
              <span>Clients</span>
            </a>
          {% endif %}

          <!-- Presenters -->
          {% if can_manage_presenters %}
            <a href="{% url 'authentication:user_list' %}" 
               class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'user' in request.resolver_match.url_name %}bg-primary-50 text-primary-700 border-r-2 border-primary-500{% else %}text-gray-700 hover:bg-primary-50 hover:text-primary-900{% endif %}">
              <i class="fa-solid fa-users w-5 h-5 mr-3 {% if 'user' in request.resolver_match.url_name %}text-primary-600{% else %}text-gray-400 group-hover:text-primary-600{% endif %}"></i>
              <span>Users & Presenters</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- Reports & Analytics -->
    {% if can_view_reports %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-purple-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-chart-bar mr-2"></i>
          Reports
        </h3>
        <div class="space-y-1">
          <a href="{% url 'reports:report_list' %}" 
             class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'report' in request.resolver_match.url_name %}bg-purple-50 text-purple-700 border-r-2 border-purple-500{% else %}text-gray-700 hover:bg-purple-50 hover:text-purple-900{% endif %}">
            <i class="fa-solid fa-file-chart-line w-5 h-5 mr-3 {% if 'report' in request.resolver_match.url_name %}text-purple-600{% else %}text-gray-400 group-hover:text-purple-600{% endif %}"></i>
            <span>Analytics & Reports</span>
          </a>
        </div>
      </div>
    {% endif %}

    <!-- Settings -->
    {% if current_membership.role == 'owner' or current_membership.role == 'admin' %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-cog mr-2"></i>
          Settings
        </h3>
        <div class="space-y-1">
          <a href="{% url 'core:organization_settings' %}" 
             class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'settings' in request.resolver_match.url_name %}bg-gray-50 text-gray-700 border-r-2 border-gray-500{% else %}text-gray-700 hover:bg-gray-50 hover:text-gray-900{% endif %}">
            <i class="fa-solid fa-building-gear w-5 h-5 mr-3 {% if 'settings' in request.resolver_match.url_name %}text-gray-600{% else %}text-gray-400 group-hover:text-gray-600{% endif %}"></i>
            <span>Organization</span>
          </a>
        </div>
      </div>
    {% endif %}
  </nav>

  <!-- User Profile -->
  <div class="p-4 border-t border-gray-200">
    <div class="flex items-center">
      <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
        <i class="fa-solid fa-user text-gray-600 text-sm"></i>
      </div>
      <div class="ml-3 flex-1 min-w-0">
        <p class="text-sm font-medium text-gray-900 truncate">{{ user.username }}</p>
        <p class="text-xs text-gray-500 truncate">{{ current_membership.get_role_display }}</p>
      </div>
      <a href="{% url 'authentication:logout' %}" class="ml-2 text-gray-400 hover:text-gray-600">
        <i class="fa-solid fa-sign-out-alt"></i>
      </a>
    </div>
  </div>
</div>
