{% load permission_tags %}
<!-- Modern Sidebar -->
<div id="sidebar" class="w-64 bg-white border-r border-gray-200 flex flex-col">
  <!-- Header -->
  <div class="p-4 border-b border-gray-200">
    <div class="flex items-center">
      <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white shadow-sm">
        <i class="fa-solid fa-radio text-sm"></i>
      </div>
      <h1 class="ml-3 font-bold text-gray-900 text-base">RadioMention</h1>
    </div>
  </div>

  <!-- Organization Switcher -->
  {% if current_organization %}
    <div class="p-4 border-b border-gray-100">
      <div class="relative">
        <button onclick="toggleOrgSwitcher()" class="w-full flex items-center justify-between p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-all duration-200 group">
          <div class="flex items-center min-w-0">
            {% if current_organization.logo %}
              <img src="{{ current_organization.logo.url }}" alt="{{ current_organization.name }}" class="w-7 h-7 rounded-lg object-cover flex-shrink-0 shadow-sm" />
            {% else %}
              <div class="w-7 h-7 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center flex-shrink-0">
                <i class="fa-solid fa-building text-primary-600 text-sm"></i>
              </div>
            {% endif %}
            <div class="ml-3 min-w-0">
              <p class="text-sm font-semibold text-gray-900 truncate">{{ current_organization.name }}</p>
              <p class="text-xs text-gray-500 capitalize">{{ current_membership.get_role_display }}</p>
            </div>
          </div>
          <i class="fa-solid fa-chevron-down text-gray-400 text-xs flex-shrink-0 group-hover:text-gray-600 transition-colors"></i>
        </button>

        <div id="orgSwitcher" class="hidden absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-xl border border-gray-200 z-50 overflow-hidden">
          <div class="py-2">
            {% for org in user_organizations %}
              <a href="{% url 'organizations:switch' slug=org.slug %}" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors {% if org == current_organization %}bg-primary-50 text-primary-700 border-r-2 border-primary-500{% endif %}">
                {% if org.logo %}
                  <img src="{{ org.logo.url }}" alt="{{ org.name }}" class="w-6 h-6 rounded-md object-cover shadow-sm" />
                {% else %}
                  <div class="w-6 h-6 bg-gradient-to-br from-primary-100 to-primary-200 rounded-md flex items-center justify-center">
                    <i class="fa-solid fa-building text-primary-600 text-xs"></i>
                  </div>
                {% endif %}
                <span class="ml-3 truncate font-medium">{{ org.name }}</span>
                {% if org == current_organization %}
                  <i class="fa-solid fa-check text-primary-600 ml-auto text-sm"></i>
                {% endif %}
              </a>
            {% endfor %}
            {% if user_organizations|length > 1 %}
              <div class="border-t border-gray-100 mt-2 pt-2">
                <a href="{% url 'organizations:list' %}" class="flex items-center px-4 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-colors">
                  <i class="fa-solid fa-cog w-5 h-5 text-gray-400"></i>
                  <span class="ml-3">Manage Organizations</span>
                </a>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  {% endif %}

  <nav class="flex-1 overflow-y-auto py-4">
    <!-- Primary Actions -->
    <div class="px-4 space-y-1">
      <!-- Dashboard -->
      <a href="{% url 'core:dashboard' %}"
        class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'dashboard' %}
          
          
          
          

          bg-primary-50 text-primary-700 border-r-2 border-primary-500





        {% else %}
          
          
          
          

          text-gray-700 hover:bg-gray-50 hover:text-gray-900





        {% endif %}">
        <i class="fa-solid fa-chart-line w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'dashboard' %}
            
            
            
            

            text-primary-600





          {% else %}
            
            
            
            

            text-gray-400 group-hover:text-gray-600





          {% endif %}">

        </i>
        <span>Dashboard</span>
      </a>

      <!-- Presenter Dashboard (only for presenters) -->
      {% if current_membership.role == 'presenter' %}
        <a href="{% url 'core:my_presenter_dashboard' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'presenter_dashboard' in request.resolver_match.url_name %}
            
            
            
            

            bg-orange-50 text-orange-700 border-r-2 border-orange-500





          {% else %}
            
            
            
            

            text-gray-700 hover:bg-orange-50 hover:text-orange-900





          {% endif %}">
          <i class="fa-solid fa-microphone w-5 h-5 mr-3 {% if 'presenter_dashboard' in request.resolver_match.url_name %}
              
              
              
              

              text-orange-600





            {% else %}
              
              
              
              

              text-gray-400 group-hover:text-orange-600





            {% endif %}">

          </i>
          <span>My Presenter Dashboard</span>
          <span class="ml-auto text-xs text-orange-600 font-medium bg-orange-100 px-2 py-0.5 rounded-full">LIVE</span>
        </a>
      {% endif %}

      <!-- Calendar -->
      <a href="{% url 'mentions:calendar_interface' %}"
        class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'calendar_interface' %}
          
          
          
          
          
          bg-primary-50 text-primary-700 border-r-2 border-primary-500





        {% else %}
          
          
          
          
          
          text-gray-700 hover:bg-gray-50 hover:text-gray-900





        {% endif %}">
        <i class="fa-solid fa-calendar-days w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'calendar_interface' %}
            
            
            
            
            
            text-primary-600





          {% else %}
            
            
            
            
            
            text-gray-400 group-hover:text-gray-600





          {% endif %}">

        </i>
        <span>Calendar</span>
      </a>

      <!-- Approvals with badge -->
      <a href="{% url 'mentions:approval_workflow' %}"
        class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'approval_workflow' %}
          
          
          
          
          
          bg-primary-50 text-primary-700 border-r-2 border-primary-500





        {% else %}
          
          
          
          
          
          text-gray-700 hover:bg-gray-50 hover:text-gray-900





        {% endif %}">
        <i class="fa-solid fa-check-to-slot w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'approval_workflow' %}
            
            
            
            
            
            text-primary-600





          {% else %}
            
            
            
            
            
            text-gray-400 group-hover:text-gray-600





          {% endif %}">

        </i>
        <span>Approvals</span>
        {% if pending_approvals_count %}
          <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center font-semibold">{{ pending_approvals_count }}</span>
        {% endif %}
      </a>
    </div>

    <!-- Content Management -->
    <div class="px-4 mt-6">
      <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Content</h3>
      <div class="space-y-1">
        <!-- Mentions -->
        <a href="{% url 'mentions:mention_list' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'mention' in request.resolver_match.url_name and 'approval' not in request.resolver_match.url_name %}
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500





          {% else %}
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900





          {% endif %}">
          <i class="fa-solid fa-bullhorn w-5 h-5 mr-3 {% if 'mention' in request.resolver_match.url_name and 'approval' not in request.resolver_match.url_name %}
              
              
              
              
              
              text-primary-600





            {% else %}
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600





            {% endif %}">

          </i>
          <span>Mentions</span>
        </a>

        <!-- Shows -->
        <a href="{% url 'shows:show_list' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'show' in request.resolver_match.url_name %}
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500





          {% else %}
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900





          {% endif %}">
          <i class="fa-solid fa-broadcast-tower w-5 h-5 mr-3 {% if 'show' in request.resolver_match.url_name %}
              
              
              
              
              
              text-primary-600





            {% else %}
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600





            {% endif %}">

          </i>
          <span>Shows</span>
        </a>

        <!-- Clients -->
        <a href="{% url 'core:client_list' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'client' in request.resolver_match.url_name %}
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500





          {% else %}
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900





          {% endif %}">
          <i class="fa-solid fa-building w-5 h-5 mr-3 {% if 'client' in request.resolver_match.url_name %}
              
              
              
              
              
              text-primary-600





            {% else %}
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600





            {% endif %}">

          </i>
          <span>Clients</span>
        </a>

        <!-- Presenters -->
        <a href="{% url 'core:presenter_list' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'presenter' in request.resolver_match.url_name %}
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500





          {% else %}
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900





          {% endif %}">
          <i class="fa-solid fa-microphone w-5 h-5 mr-3 {% if 'presenter' in request.resolver_match.url_name %}
              
              
              
              
              
              text-primary-600





            {% else %}
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600





            {% endif %}">

          </i>
          <span>Presenters</span>
        </a>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="px-4 mt-6">
      <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Quick Actions</h3>
      <div class="space-y-1">
        <a href="{% url 'mentions:mention_create' %}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 group">
          <i class="fa-solid fa-plus w-5 h-5 mr-3 text-gray-400 group-hover:text-gray-600"></i>
          <span>New Mention</span>
        </a>
        <a href="{% url 'mentions:recurring_mention_create' %}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 group">
          <i class="fa-solid fa-repeat w-5 h-5 mr-3 text-gray-400 group-hover:text-gray-600"></i>
          <span>New Recurring Mention</span>
        </a>
        <a href="{% url 'core:client_create' %}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 group">
          <i class="fa-solid fa-building-user w-5 h-5 mr-3 text-gray-400 group-hover:text-gray-600"></i>
          <span>New Client</span>
        </a>
        <a href="{% url 'shows:show_create' %}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 group">
          <i class="fa-solid fa-broadcast-tower w-5 h-5 mr-3 text-gray-400 group-hover:text-gray-600"></i>
          <span>New Show</span>
        </a>
      </div>
    </div>

    <!-- Analytics & Reports -->
    <div class="px-4 mt-6">
      <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Analytics</h3>
      <div class="space-y-1">
        <a href="{% url 'reports:report_list' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'report' in request.resolver_match.url_name %}
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500





          {% else %}
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900





          {% endif %}">
          <i class="fa-solid fa-file-lines w-5 h-5 mr-3 {% if 'report' in request.resolver_match.url_name %}
              
              
              
              
              
              text-primary-600





            {% else %}
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600





            {% endif %}">

          </i>
          <span>Reports</span>
        </a>
        <a href="{% url 'reports:analytics' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'analytics' %}
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500





          {% else %}
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900





          {% endif %}">
          <i class="fa-solid fa-chart-pie w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'analytics' %}
              
              
              
              
              
              text-primary-600





            {% else %}
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600





            {% endif %}">

          </i>
          <span>Analytics</span>
        </a>
      </div>
    </div>

    <!-- Advanced Features -->
    <div class="px-4 mt-6">
      <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Advanced</h3>
      <div class="space-y-1">
        <a href="{% url 'mentions:recurring_mentions' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'recurring' in request.resolver_match.url_name %}
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500





          {% else %}
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900





          {% endif %}">
          <i class="fa-solid fa-repeat w-5 h-5 mr-3 {% if 'recurring' in request.resolver_match.url_name %}
              
              
              
              
              
              text-primary-600





            {% else %}
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600





            {% endif %}">

          </i>
          <span>Recurring Mentions</span>
          <span class="ml-auto text-xs text-green-600 font-medium bg-green-100 px-2 py-0.5 rounded-full">NEW</span>
        </a>
        <a href="{% url 'activity_logs:conflict_logs' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'conflict' in request.resolver_match.url_name %}
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500





          {% else %}
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900





          {% endif %}">
          <i class="fa-solid fa-exclamation-triangle w-5 h-5 mr-3 {% if 'conflict' in request.resolver_match.url_name %}
              
              
              
              
              
              text-primary-600





            {% else %}
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600





            {% endif %}">

          </i>
          <span>Conflict Detection</span>
        </a>
      </div>
    </div>

    <!-- Admin Section (for staff users) -->
    {% if user.is_staff %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Administration</h3>
        <div class="space-y-1">
          <a href="{% url 'authentication:user_manager' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'user' in request.resolver_match.url_name %}
              
              
              
              
              
              bg-primary-50 text-primary-700 border-r-2 border-primary-500





            {% else %}
              
              
              
              
              
              text-gray-700 hover:bg-gray-50 hover:text-gray-900





            {% endif %}">
            <i class="fa-solid fa-users w-5 h-5 mr-3 {% if 'user' in request.resolver_match.url_name %}
                
                
                
                
                
                text-primary-600





              {% else %}
                
                
                
                
                
                text-gray-400 group-hover:text-gray-600





              {% endif %}">

            </i>
            <span>User Management</span>
          </a>
          <a href="{% url 'settings:settings_dashboard' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'settings' in request.resolver_match.url_name %}
              
              
              
              
              
              bg-primary-50 text-primary-700 border-r-2 border-primary-500





            {% else %}
              
              
              
              
              
              text-gray-700 hover:bg-gray-50 hover:text-gray-900





            {% endif %}">
            <i class="fa-solid fa-cog w-5 h-5 mr-3 {% if 'settings' in request.resolver_match.url_name %}
                
                
                
                
                
                text-primary-600





              {% else %}
                
                
                
                
                
                text-gray-400 group-hover:text-gray-600





              {% endif %}">

            </i>
            <span>Settings</span>
          </a>
          <a href="{% url 'activity_logs:activity_logs' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'activity' in request.resolver_match.url_name %}
              
              
              
              
              
              bg-primary-50 text-primary-700 border-r-2 border-primary-500





            {% else %}
              
              
              
              
              
              text-gray-700 hover:bg-gray-50 hover:text-gray-900





            {% endif %}">
            <i class="fa-solid fa-history w-5 h-5 mr-3 {% if 'activity' in request.resolver_match.url_name %}
                
                
                
                
                
                text-primary-600





              {% else %}
                
                
                
                
                
                text-gray-400 group-hover:text-gray-600





              {% endif %}">

            </i>
            <span>Activity Logs</span>
          </a>
        </div>
      </div>
    {% endif %}
  </nav>

  <!-- User Profile Section -->
  <div class="p-4 border-t border-gray-200 bg-gray-50">
    <div class="flex items-center">
      {% if user.is_authenticated %}
        <img src="https://storage.googleapis.com/uxpilot-auth.appspot.com/avatars/avatar-2.jpg" alt="User" class="w-9 h-9 rounded-full object-cover shadow-sm" />
        <div class="ml-3 min-w-0 flex-1">
          <p class="text-sm font-semibold text-gray-900 truncate">{{ user.get_full_name|default:user.username }}</p>
          <p class="text-xs text-gray-500">
            {% if user.is_superuser %}
              Super Admin
            {% elif current_membership.is_organization_superuser %}
              {{ current_membership.get_role_display }} (Org Superuser)
            {% elif user.is_staff %}
              Administrator
            {% else %}
              {{ current_membership.get_role_display|default:'User' }}
            {% endif %}
          </p>
        </div>
        <div class="ml-2 relative">
          <button class="text-gray-400 hover:text-gray-600 p-1 rounded-md hover:bg-gray-100 transition-colors" onclick="toggleUserMenu()"><i class="fa-solid fa-ellipsis-vertical text-sm"></i></button>
          <div id="userMenu" class="hidden absolute bottom-full right-0 mb-2 w-56 bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden">
            <div class="px-4 py-3 border-b border-gray-100 bg-gray-50">
              <p class="text-xs text-gray-500">Signed in as</p>
              <p class="text-sm font-semibold text-gray-900 truncate">{{ user.get_full_name|default:user.username }}</p>
            </div>
            <div class="py-2">
              <a href="{% url 'authentication:profile' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                <i class="fa-solid fa-user w-4 h-4 mr-3 text-gray-400"></i>
                <span>Profile Settings</span>
              </a>
              <a href="{% url 'settings:user_preferences' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                <i class="fa-solid fa-sliders-h w-4 h-4 mr-3 text-gray-400"></i>
                <span>Preferences</span>
              </a>
              {% if user.is_staff %}
                <div class="border-t border-gray-100 mt-2 pt-2">
                  <a href="{% url 'authentication:user_manager' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fa-solid fa-users-cog w-4 h-4 mr-3 text-gray-400"></i>
                    <span>Admin Panel</span>
                  </a>
                  <a href="{% url 'activity_logs:dashboard' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fa-solid fa-chart-line w-4 h-4 mr-3 text-gray-400"></i>
                    <span>Activity Dashboard</span>
                  </a>
                </div>
              {% endif %}
              <div class="border-t border-gray-100 mt-2 pt-2">
                <a href="#" onclick="showComingSoon('Help & Support')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                  <i class="fa-solid fa-question-circle w-4 h-4 mr-3 text-gray-400"></i>
                  <span>Help & Support</span>
                </a>
                <a href="{% url 'account_logout' %}" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                  <i class="fa-solid fa-sign-out-alt w-4 h-4 mr-3 text-red-500"></i>
                  <span>Sign Out</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      {% else %}
        <a href="{% url 'account_login' %}" class="text-primary-600 hover:text-primary-700 font-semibold text-sm">Login</a>
      {% endif %}
    </div>
  </div>
</div>

<script>
  function toggleUserMenu() {
    const menu = document.getElementById('userMenu')
    menu.classList.toggle('hidden')
  }
  
  function toggleOrgSwitcher() {
    const switcher = document.getElementById('orgSwitcher')
    switcher.classList.toggle('hidden')
  }
  
  // Close menus when clicking outside
  document.addEventListener('click', function (event) {
    const userMenu = document.getElementById('userMenu')
    const orgSwitcher = document.getElementById('orgSwitcher')
    const button = event.target.closest('button')
  
    if (!button || !button.onclick || !button.onclick.toString().includes('toggleUserMenu')) {
      userMenu.classList.add('hidden')
    }
  
    if (!button || !button.onclick || !button.onclick.toString().includes('toggleOrgSwitcher')) {
      if (orgSwitcher) {
        orgSwitcher.classList.add('hidden')
      }
    }
  })
  
  // Show coming soon notification
  function showComingSoon(featureName) {
    // Create notification element
    const notification = document.createElement('div')
    notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm'
    notification.innerHTML = `
                                    <div class="flex items-center">
                                      <i class="fa-solid fa-info-circle mr-2"></i>
                                      <div>
                                        <p class="font-medium">${featureName}</p>
                                        <p class="text-sm opacity-90">This feature is coming soon!</p>
                                      </div>
                                      <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                                        <i class="fa-solid fa-times"></i>
                                      </button>
                                    </div>
                                  `
  
    // Add to page
    document.body.appendChild(notification)
  
    // Auto remove after 3 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove()
      }
    }, 3000)
  }
</script>
