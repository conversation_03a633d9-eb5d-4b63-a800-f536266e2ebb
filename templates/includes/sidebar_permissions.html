{% load permission_tags %}
<!-- Permission-Based Sidebar -->
<div id="sidebar" class="w-64 bg-white border-r border-gray-200 flex flex-col">
  <!-- Header -->
  <div class="p-4 border-b border-gray-200">
    <div class="flex items-center">
      <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white shadow-sm">
        <i class="fa-solid fa-radio text-sm"></i>
      </div>
      <h1 class="ml-3 font-bold text-gray-900 text-base">RadioMention</h1>
    </div>
  </div>

  <!-- Organization Switcher -->
  {% if current_organization %}
    <div class="p-4 border-b border-gray-100">
      <div class="relative">
        <button onclick="toggleOrgSwitcher()" class="w-full flex items-center justify-between p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-all duration-200 group">
          <div class="flex items-center min-w-0">
            {% if current_organization.logo %}
              <img src="{{ current_organization.logo.url }}" alt="{{ current_organization.name }}" class="w-7 h-7 rounded-lg object-cover flex-shrink-0 shadow-sm" />
            {% else %}
              <div class="w-7 h-7 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center flex-shrink-0">
                <i class="fa-solid fa-building text-primary-600 text-sm"></i>
              </div>
            {% endif %}
            <div class="ml-3 min-w-0">
              <p class="text-sm font-semibold text-gray-900 truncate">{{ current_organization.name }}</p>
              <p class="text-xs text-gray-500 capitalize">{{ current_membership.get_role_display }}</p>
            </div>
          </div>
          <i class="fa-solid fa-chevron-down text-gray-400 text-xs flex-shrink-0 group-hover:text-gray-600 transition-colors"></i>
        </button>

        <div id="orgSwitcher" class="hidden absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-xl border border-gray-200 z-50 overflow-hidden">
          <div class="py-2">
            {% for org in user_organizations %}
              <a href="{% url 'organizations:switch' slug=org.slug %}" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors {% if org == current_organization %}bg-primary-50 text-primary-700 border-r-2 border-primary-500{% endif %}">
                {% if org.logo %}
                  <img src="{{ org.logo.url }}" alt="{{ org.name }}" class="w-6 h-6 rounded-md object-cover shadow-sm" />
                {% else %}
                  <div class="w-6 h-6 bg-gradient-to-br from-primary-100 to-primary-200 rounded-md flex items-center justify-center">
                    <i class="fa-solid fa-building text-primary-600 text-xs"></i>
                  </div>
                {% endif %}
                <span class="ml-3 truncate font-medium">{{ org.name }}</span>
                {% if org == current_organization %}
                  <i class="fa-solid fa-check text-primary-600 ml-auto text-sm"></i>
                {% endif %}
              </a>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  {% endif %}

  <nav class="flex-1 overflow-y-auto py-4">
    <!-- Primary Actions -->
    <div class="px-4 space-y-1">
      <!-- Dashboard (always visible) -->
      <a href="{% url 'core:dashboard' %}"
        class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'dashboard' %}
          
          
          
          
          
          
          
          
          
          
          
          
          bg-primary-50 text-primary-700 border-r-2 border-primary-500












        {% else %}
          
          
          
          
          
          
          
          
          
          
          
          
          text-gray-700 hover:bg-gray-50 hover:text-gray-900












        {% endif %}">
        <i class="fa-solid fa-chart-line w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'dashboard' %}
            
            
            
            
            
            
            
            
            
            
            
            
            text-primary-600












          {% else %}
            
            
            
            
            
            
            
            
            
            
            
            
            text-gray-400 group-hover:text-gray-600












          {% endif %}">

        </i>
        <span>Dashboard</span>
      </a>

      <!-- Presenter Dashboard (only for presenters) -->
      {% has_role 'presenter' as is_presenter %}
      {% if is_presenter %}
        <a href="{% url 'core:my_presenter_dashboard' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'presenter_dashboard' in request.resolver_match.url_name %}
            
            
            
            
            
            
            
            
            
            
            
            
            bg-orange-50 text-orange-700 border-r-2 border-orange-500












          {% else %}
            
            
            
            
            
            
            
            
            
            
            
            
            text-gray-700 hover:bg-orange-50 hover:text-orange-900












          {% endif %}">
          <i class="fa-solid fa-microphone w-5 h-5 mr-3 {% if 'presenter_dashboard' in request.resolver_match.url_name %}
              
              
              
              
              
              
              
              
              
              
              
              
              text-orange-600












            {% else %}
              
              
              
              
              
              
              
              
              
              
              
              
              text-gray-400 group-hover:text-orange-600












            {% endif %}">

          </i>
          <span>My Presenter Dashboard</span>
          <span class="ml-auto text-xs text-orange-600 font-medium bg-orange-100 px-2 py-0.5 rounded-full">LIVE</span>
        </a>

        <!-- Live Show Page (only for presenters) -->
        <a href="{% url 'core:live_show_page' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'live_show_page' %}
            
            
            
            
            
            
            
            bg-red-50 text-red-700 border-r-2 border-red-500







          {% else %}
            
            
            
            
            
            
            
            text-gray-700 hover:bg-red-50 hover:text-red-900







          {% endif %}">
          <i class="fa-solid fa-broadcast-tower w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'live_show_page' %}
              
              
              
              
              
              
              
              text-red-600







            {% else %}
              
              
              
              
              
              
              
              text-gray-400 group-hover:text-red-600







            {% endif %}">

          </i>
          <span>Live Show</span>
          <span class="ml-auto text-xs text-red-600 font-medium bg-red-100 px-2 py-0.5 rounded-full">LIVE</span>
        </a>

        <!-- Change Password (only for presenters) -->
        <a href="{% url 'account_change_password' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'account_change_password' %}
            
            
            
            
            
            
            bg-blue-50 text-blue-700 border-r-2 border-blue-500






          {% else %}
            
            
            
            
            
            
            text-gray-700 hover:bg-blue-50 hover:text-blue-900






          {% endif %}">
          <i class="fa-solid fa-key w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'account_change_password' %}
              
              
              
              
              
              
              text-blue-600






            {% else %}
              
              
              
              
              
              
              text-gray-400 group-hover:text-blue-600






            {% endif %}">

          </i>
          <span>Change Password</span>
        </a>
      {% endif %}

      <!-- Calendar (if can view mentions) -->
      {% has_any_permission 'view' 'manage_mentions' as can_view_calendar %}
      {% if can_view_calendar %}
        <a href="{% url 'mentions:calendar_interface' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'calendar_interface' %}
            
            
            
            
            
            
            
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500












          {% else %}
            
            
            
            
            
            
            
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900












          {% endif %}">
          <i class="fa-solid fa-calendar-days w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'calendar_interface' %}
              
              
              
              
              
              
              
              
              
              
              
              
              text-primary-600












            {% else %}
              
              
              
              
              
              
              
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600












            {% endif %}">

          </i>
          <span>Calendar</span>
        </a>
      {% endif %}

      <!-- Approvals (if can approve mentions) -->
      {% has_permission 'approve_mentions' as can_approve %}
      {% if can_approve %}
        <a href="{% url 'mentions:approval_workflow' %}"
          class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'approval_workflow' %}
            
            
            
            
            
            
            
            
            
            
            
            
            bg-primary-50 text-primary-700 border-r-2 border-primary-500












          {% else %}
            
            
            
            
            
            
            
            
            
            
            
            
            text-gray-700 hover:bg-gray-50 hover:text-gray-900












          {% endif %}">
          <i class="fa-solid fa-check-to-slot w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'approval_workflow' %}
              
              
              
              
              
              
              
              
              
              
              
              
              text-primary-600












            {% else %}
              
              
              
              
              
              
              
              
              
              
              
              
              text-gray-400 group-hover:text-gray-600












            {% endif %}">

          </i>
          <span>Approvals</span>
          {% if pending_approvals_count %}
            <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center font-semibold">{{ pending_approvals_count }}</span>
          {% endif %}
        </a>
      {% endif %}
    </div>

    <!-- Content Management -->
    {% has_any_permission 'view' 'manage_mentions' 'manage_shows' 'manage_clients' 'manage_presenters' as has_content_access %}
    {% if has_content_access %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Content</h3>
        <div class="space-y-1">
          <!-- Mentions -->
          {% has_any_permission 'view' 'manage_mentions' as can_view_mentions %}
          {% if can_view_mentions %}
            <a href="{% url 'mentions:mention_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'mention' in request.resolver_match.url_name and 'approval' not in request.resolver_match.url_name %}
                
                
                
                
                
                
                
                
                
                
                
                
                bg-primary-50 text-primary-700 border-r-2 border-primary-500












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-700 hover:bg-gray-50 hover:text-gray-900












              {% endif %}">
              <i class="fa-solid fa-bullhorn w-5 h-5 mr-3 {% if 'mention' in request.resolver_match.url_name and 'approval' not in request.resolver_match.url_name %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-primary-600












                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-gray-400 group-hover:text-gray-600












                {% endif %}">

              </i>
              <span>Mentions</span>
            </a>
          {% endif %}

          <!-- Shows -->
          {% has_any_permission 'view' 'manage_shows' as can_view_shows %}
          {% if can_view_shows %}
            <a href="{% url 'shows:show_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'show' in request.resolver_match.url_name %}
                
                
                
                
                
                
                
                
                
                
                
                
                bg-primary-50 text-primary-700 border-r-2 border-primary-500












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-700 hover:bg-gray-50 hover:text-gray-900












              {% endif %}">
              <i class="fa-solid fa-broadcast-tower w-5 h-5 mr-3 {% if 'show' in request.resolver_match.url_name %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-primary-600












                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-gray-400 group-hover:text-gray-600












                {% endif %}">

              </i>
              <span>Shows</span>
            </a>
          {% endif %}

          <!-- Clients -->
          {% has_any_permission 'view' 'manage_clients' as can_view_clients %}
          {% if can_view_clients %}
            <a href="{% url 'core:client_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'client' in request.resolver_match.url_name %}
                
                
                
                
                
                
                
                
                
                
                
                
                bg-primary-50 text-primary-700 border-r-2 border-primary-500












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-700 hover:bg-gray-50 hover:text-gray-900












              {% endif %}">
              <i class="fa-solid fa-building w-5 h-5 mr-3 {% if 'client' in request.resolver_match.url_name %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-primary-600












                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-gray-400 group-hover:text-gray-600












                {% endif %}">

              </i>
              <span>Clients</span>
            </a>
          {% endif %}

          <!-- Industries -->
          {% has_permission 'manage_settings' as can_manage_industries %}
          {% if can_manage_industries %}
            <a href="{% url 'core:industry_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'industry' in request.resolver_match.url_name %}
                
                
                bg-primary-50 text-primary-700 border-r-2 border-primary-500


              {% else %}
                
                
                text-gray-700 hover:bg-gray-50 hover:text-gray-900


              {% endif %}">
              <i class="fa-solid fa-industry w-5 h-5 mr-3 {% if 'industry' in request.resolver_match.url_name %}
                  
                  
                  text-primary-600


                {% else %}
                  
                  
                  text-gray-400 group-hover:text-gray-600


                {% endif %}">

              </i>
              <span>Industries</span>
            </a>
          {% endif %}

          <!-- Presenters -->
          {% has_any_permission 'view' 'manage_presenters' as can_view_presenters %}
          {% if can_view_presenters %}
            <a href="{% url 'core:presenter_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'presenter' in request.resolver_match.url_name %}
                
                
                
                
                
                
                
                
                
                
                
                
                bg-primary-50 text-primary-700 border-r-2 border-primary-500












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-700 hover:bg-gray-50 hover:text-gray-900












              {% endif %}">
              <i class="fa-solid fa-microphone w-5 h-5 mr-3 {% if 'presenter' in request.resolver_match.url_name %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-primary-600












                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-gray-400 group-hover:text-gray-600












                {% endif %}">

              </i>
              <span>Presenters</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- Quick Actions -->
    {% has_any_permission 'manage_mentions' 'manage_clients' 'manage_shows' as can_create_content %}
    {% if can_create_content %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Quick Actions</h3>
        <div class="space-y-1">
          {% has_permission 'manage_mentions' as can_create_mentions %}
          {% if can_create_mentions %}
            <a href="{% url 'mentions:mention_create' %}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 group">
              <i class="fa-solid fa-plus w-5 h-5 mr-3 text-gray-400 group-hover:text-gray-600"></i>
              <span>New Mention</span>
            </a>
            <a href="{% url 'mentions:recurring_mention_create' %}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 group">
              <i class="fa-solid fa-repeat w-5 h-5 mr-3 text-gray-400 group-hover:text-gray-600"></i>
              <span>New Recurring Mention</span>
            </a>
          {% endif %}

          {% has_permission 'manage_clients' as can_create_clients %}
          {% if can_create_clients %}
            <a href="{% url 'core:client_create' %}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 group">
              <i class="fa-solid fa-building-user w-5 h-5 mr-3 text-gray-400 group-hover:text-gray-600"></i>
              <span>New Client</span>
            </a>
          {% endif %}

          {% has_permission 'manage_settings' as can_create_industries %}
          {% if can_create_industries %}
            <a href="{% url 'core:industry_create' %}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 group">
              <i class="fa-solid fa-industry w-5 h-5 mr-3 text-gray-400 group-hover:text-gray-600"></i>
              <span>New Industry</span>
            </a>
          {% endif %}

          {% has_permission 'manage_shows' as can_create_shows %}
          {% if can_create_shows %}
            <a href="{% url 'shows:show_create' %}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-lg transition-all duration-200 group">
              <i class="fa-solid fa-broadcast-tower w-5 h-5 mr-3 text-gray-400 group-hover:text-gray-600"></i>
              <span>New Show</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- Analytics & Reports -->
    {% has_any_permission 'view_reports' 'view_analytics' as can_view_analytics %}
    {% if can_view_analytics %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Analytics</h3>
        <div class="space-y-1">
          {% has_permission 'view_reports' as can_view_reports %}
          {% if can_view_reports %}
            <a href="{% url 'reports:report_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'report' in request.resolver_match.url_name %}
                
                
                
                
                
                
                
                
                
                
                
                
                bg-primary-50 text-primary-700 border-r-2 border-primary-500












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-700 hover:bg-gray-50 hover:text-gray-900












              {% endif %}">
              <i class="fa-solid fa-file-lines w-5 h-5 mr-3 {% if 'report' in request.resolver_match.url_name %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-primary-600












                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-gray-400 group-hover:text-gray-600












                {% endif %}">

              </i>
              <span>Reports</span>
            </a>
          {% endif %}

          {% has_permission 'view_analytics' as can_view_analytics_detail %}
          {% if can_view_analytics_detail %}
            <a href="{% url 'reports:analytics' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'analytics' %}
                
                
                
                
                
                
                
                
                
                
                
                
                bg-primary-50 text-primary-700 border-r-2 border-primary-500












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-700 hover:bg-gray-50 hover:text-gray-900












              {% endif %}">
              <i class="fa-solid fa-chart-pie w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'analytics' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-primary-600












                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-gray-400 group-hover:text-gray-600












                {% endif %}">

              </i>
              <span>Analytics</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- Advanced Features -->
    {% has_any_role 'owner' 'admin' 'manager' as can_access_advanced %}
    {% if can_access_advanced %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Advanced</h3>
        <div class="space-y-1">
          <a href="{% url 'mentions:recurring_mentions' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'recurring' in request.resolver_match.url_name %}
              
              
              
              
              
              
              
              
              
              
              
              
              bg-primary-50 text-primary-700 border-r-2 border-primary-500












            {% else %}
              
              
              
              
              
              
              
              
              
              
              
              
              text-gray-700 hover:bg-gray-50 hover:text-gray-900












            {% endif %}">
            <i class="fa-solid fa-repeat w-5 h-5 mr-3 {% if 'recurring' in request.resolver_match.url_name %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-primary-600












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-400 group-hover:text-gray-600












              {% endif %}">

            </i>
            <span>Recurring Mentions</span>
            <span class="ml-auto text-xs text-green-600 font-medium bg-green-100 px-2 py-0.5 rounded-full">NEW</span>
          </a>

          <a href="{% url 'activity_logs:conflict_logs' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'conflict' in request.resolver_match.url_name %}
              
              
              
              
              
              
              
              
              
              
              
              
              bg-primary-50 text-primary-700 border-r-2 border-primary-500












            {% else %}
              
              
              
              
              
              
              
              
              
              
              
              
              text-gray-700 hover:bg-gray-50 hover:text-gray-900












            {% endif %}">
            <i class="fa-solid fa-exclamation-triangle w-5 h-5 mr-3 {% if 'conflict' in request.resolver_match.url_name %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-primary-600












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-400 group-hover:text-gray-600












              {% endif %}">

            </i>
            <span>Conflict Detection</span>
          </a>
        </div>
      </div>
    {% endif %}

    <!-- Admin Section -->
    {% has_any_role 'owner' 'admin' as is_admin %}
    {% if is_admin %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">Administration</h3>
        <div class="space-y-1">
          {% has_permission 'manage_users' as can_manage_users %}
          {% if can_manage_users %}
            <a href="{% url 'authentication:user_manager' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'user' in request.resolver_match.url_name %}
                
                
                
                
                
                
                
                
                
                
                
                
                bg-primary-50 text-primary-700 border-r-2 border-primary-500












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-700 hover:bg-gray-50 hover:text-gray-900












              {% endif %}">
              <i class="fa-solid fa-users w-5 h-5 mr-3 {% if 'user' in request.resolver_match.url_name %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-primary-600












                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-gray-400 group-hover:text-gray-600












                {% endif %}">

              </i>
              <span>User Management</span>
            </a>
          {% endif %}

          {% has_permission 'manage_settings' as can_manage_settings %}
          {% if can_manage_settings %}
            <a href="{% url 'settings:settings_dashboard' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'settings' in request.resolver_match.url_name %}
                
                
                
                
                
                
                
                
                
                
                
                
                bg-primary-50 text-primary-700 border-r-2 border-primary-500












              {% else %}
                
                
                
                
                
                
                
                
                
                
                
                
                text-gray-700 hover:bg-gray-50 hover:text-gray-900












              {% endif %}">
              <i class="fa-solid fa-cog w-5 h-5 mr-3 {% if 'settings' in request.resolver_match.url_name %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-primary-600












                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  text-gray-400 group-hover:text-gray-600












                {% endif %}">

              </i>
              <span>Settings</span>
            </a>
          {% endif %}

          {% has_permission 'manage_api_keys' as can_manage_api_keys %}
          {% if can_manage_api_keys %}
            <a href="{% url 'settings:api_settings' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'api_settings' %}
                
                
                
                
                
                bg-blue-50 text-blue-700 border-r-2 border-blue-500





              {% else %}
                
                
                
                
                
                text-gray-700 hover:bg-blue-50 hover:text-blue-900





              {% endif %}">
              <i class="fa-solid fa-plug w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'api_settings' %}
                  
                  
                  
                  
                  
                  text-blue-600





                {% else %}
                  
                  
                  
                  
                  
                  text-gray-400 group-hover:text-blue-600





                {% endif %}">

              </i>
              <span>API Settings</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}
  </nav>

  <!-- Footer -->
  <div class="p-4 border-t border-gray-200">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
          <i class="fa-solid fa-user text-gray-500 text-sm"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-gray-900">{{ user.get_full_name|default:user.username }}</p>
          <p class="text-xs text-gray-500">{{ current_membership.get_role_display }}</p>
        </div>
      </div>
      <a href="{% url 'account_logout' %}" class="text-gray-400 hover:text-gray-600 transition-colors" title="Sign Out"><i class="fa-solid fa-sign-out-alt"></i></a>
    </div>
  </div>
</div>

<script>
  function toggleOrgSwitcher() {
    const switcher = document.getElementById('orgSwitcher')
    switcher.classList.toggle('hidden')
  }
  
  // Close org switcher when clicking outside
  document.addEventListener('click', function (event) {
    const switcher = document.getElementById('orgSwitcher')
    const button = event.target.closest('button[onclick="toggleOrgSwitcher()"]')
  
    if (!button && !switcher.contains(event.target)) {
      switcher.classList.add('hidden')
    }
  })
</script>
