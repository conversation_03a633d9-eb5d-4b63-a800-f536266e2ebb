{% load permission_tags %}
<!-- Sidebar -->
<div id="sidebar" class="w-64 bg-white border-r border-gray-200 flex flex-col">
  <!-- Header -->
  <div class="p-4 border-b border-gray-200">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white shadow-sm">
          <i class="fa-solid fa-radio text-sm"></i>
        </div>
        <h1 class="ml-3 font-bold text-gray-900 text-base">RadioMention</h1>
      </div>
    </div>
  </div>

  <!-- Organization Switcher -->
  {% if organizations.count > 1 %}
    <div class="p-4 border-b border-gray-200 bg-gray-50">
      <div class="relative">
        <button onclick="toggleOrgSwitcher()" class="w-full flex items-center justify-between px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500" title="{{ current_organization.name|default:'Select Organization' }}">
          <div class="flex items-center">
            <i class="fa-solid fa-building w-4 h-4 text-gray-400"></i>
            <span class="ml-2 text-gray-700 truncate">{{ current_organization.name|default:'Select Organization' }}</span>
          </div>
          <i class="fa-solid fa-chevron-down w-4 h-4 text-gray-400"></i>
        </button>

        <div id="orgSwitcher" class="hidden absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto">
          {% for org in organizations %}
            <a href="?organization={{ org.id }}" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 {% if org == current_organization %}bg-primary-50 text-primary-700{% endif %}">
              <i class="fa-solid fa-building w-4 h-4 mr-2"></i>
              {{ org.name }}
            </a>
          {% endfor %}
        </div>
      </div>
    </div>
  {% endif %}

  <nav class="flex-1 overflow-y-auto py-4">
    <!-- 🎯 MAIN DASHBOARD -->
    <div class="px-4 space-y-1">
      <a href="{% url 'core:dashboard' %}"
        title="Dashboard"
        class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'dashboard' %}
          
          
          
           bg-primary-50 text-primary-700 border-r-2 border-primary-500



        {% else %}
          
          
          
           text-gray-700 hover:bg-gray-50 hover:text-gray-900



        {% endif %}">
        <i class="fa-solid fa-chart-line w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'dashboard' %}
            
            
            
             text-primary-600



          {% else %}
            
            
            
             text-gray-400 group-hover:text-gray-600



          {% endif %}">

        </i>
        <span class="ml-3">Dashboard</span>
      </a>
    </div>

    <!-- 🔴 LIVE OPERATIONS -->
    <div class="px-4 mt-6">
      <h3 class="px-3 text-xs font-semibold text-red-600 uppercase tracking-wider mb-3 flex items-center">
        <i class="fa-solid fa-circle text-red-500 animate-pulse"></i>
        <span class="ml-2">Live Operations</span>
      </h3>
      <div class="space-y-1">
        <!-- Live Show Interface (for presenters) -->
        {% if current_membership.role == 'presenter' %}
          <a href="/live-show/"
            title="My Live Show"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'presenter_dashboard' in request.resolver_match.url_name %}
              
              
              
               bg-red-50 text-red-700 border-r-2 border-red-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-red-50 hover:text-red-900



            {% endif %}">
            <i class="fa-solid fa-microphone w-5 h-5 mr-3 {% if 'presenter_dashboard' in request.resolver_match.url_name %}
                
                
                
                 text-red-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-red-600



              {% endif %}">

            </i>
            <span class="">My Live Show</span>
            <span class="ml-auto text-xs text-red-600 font-medium bg-red-100 px-2 py-0.5 rounded-full">LIVE</span>
          </a>
        {% endif %}

        <!-- Live Show Monitor (for admins/managers) -->
        {% if current_membership.role == 'owner' or current_membership.role == 'admin' %}
          <a href="{% url 'core:live_show_monitor' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'live_show_monitor' %}
              
              
              
               bg-red-50 text-red-700 border-r-2 border-red-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-red-50 hover:text-red-900



            {% endif %}">
            <i class="fa-solid fa-tower-broadcast w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'live_show_monitor' %}
                
                
                
                 text-red-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-red-600



              {% endif %}">

            </i>
            <span>Live Show Monitor</span>
            <span class="ml-auto text-xs text-red-600 font-medium bg-red-100 px-2 py-0.5 rounded-full">LIVE</span>
          </a>
        {% endif %}
      </div>
    </div>

    <!-- 📋 PRESENTER TOOLS (for presenters only) -->
    {% if current_membership.role == 'presenter' %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-purple-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-user-tie mr-2"></i>
          My Tools
        </h3>
        <div class="space-y-1">
          <a href="{% url 'core:presenter_calendar' current_membership.user.presenter_profile.pk %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'presenter_calendar' in request.resolver_match.url_name %}
              


               bg-purple-50 text-purple-700 border-r-2 border-purple-500



            {% else %}
              


               text-gray-700 hover:bg-purple-50 hover:text-purple-900



            {% endif %}">
            <i class="fa-solid fa-calendar-days w-5 h-5 mr-3 {% if 'presenter_calendar' in request.resolver_match.url_name %}
                


                 text-purple-600



              {% else %}
                


                 text-gray-400 group-hover:text-purple-600



              {% endif %}">

            </i>
            <span>My Schedule</span>
          </a>

          <a href="{% url 'core:personal_notes_list' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'personal_note' in request.resolver_match.url_name %}
              


               bg-purple-50 text-purple-700 border-r-2 border-purple-500



            {% else %}
              


               text-gray-700 hover:bg-purple-50 hover:text-purple-900



            {% endif %}">
            <i class="fa-solid fa-note-sticky w-5 h-5 mr-3 {% if 'personal_note' in request.resolver_match.url_name %}
                


                 text-purple-600



              {% else %}
                


                 text-gray-400 group-hover:text-purple-600



              {% endif %}">

            </i>
            <span>My Notes</span>
          </a>

          <a href="{% url 'account_change_password' %}" class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group text-gray-700 hover:bg-purple-50 hover:text-purple-900">
            <i class="fa-solid fa-key w-5 h-5 mr-3 text-gray-400 group-hover:text-purple-600"></i>
            <span>Change Password</span>
          </a>
        </div>
      </div>
    {% endif %}

    <!-- 📰 NEWS READER TOOLS (for news readers only) -->
    {% if current_membership.role == 'news_reader' %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-indigo-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-newspaper mr-2"></i>
          News Reader
        </h3>
        <div class="space-y-1">
          <a href="{% url 'news_reader:dashboard' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'dashboard' %}
              
              
              
               bg-indigo-50 text-indigo-700 border-r-2 border-indigo-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-indigo-50 hover:text-indigo-900



            {% endif %}">
            <i class="fa-solid fa-tachometer-alt w-5 h-5 mr-3 {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'dashboard' %}
                
                
                
                 text-indigo-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-indigo-600



              {% endif %}">

            </i>
            <span>Dashboard</span>
          </a>

          <a href="{% url 'news_reader:live_reading_interface' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'live_reading_interface' %}
              
              
              
               bg-red-50 text-red-700 border-r-2 border-red-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-red-50 hover:text-red-900



            {% endif %}">
            <i class="fa-solid fa-broadcast-tower w-5 h-5 mr-3 {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'live_reading_interface' %}
                
                
                
                 text-red-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-red-600



              {% endif %}">

            </i>
            <span>Live Reading</span>
            <span class="ml-auto text-xs text-red-600 font-medium bg-red-100 px-2 py-0.5 rounded-full">LIVE</span>
          </a>

          <a href="{% url 'news_reader:note_list' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'note_list' %}
              
              
              
               bg-indigo-50 text-indigo-700 border-r-2 border-indigo-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-indigo-50 hover:text-indigo-900



            {% endif %}">
            <i class="fa-solid fa-sticky-note w-5 h-5 mr-3 {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'note_list' %}
                
                
                
                 text-indigo-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-indigo-600



              {% endif %}">

            </i>
            <span>My Notes</span>
          </a>

          <a href="{% url 'news_reader:assignment_list' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'assignment_list' %}
              
              
              
               bg-indigo-50 text-indigo-700 border-r-2 border-indigo-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-indigo-50 hover:text-indigo-900



            {% endif %}">
            <i class="fa-solid fa-tasks w-5 h-5 mr-3 {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'assignment_list' %}
                
                
                
                 text-indigo-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-indigo-600



              {% endif %}">

            </i>
            <span>Assignments</span>
          </a>

          <a href="{% url 'news_reader:session_list' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'session_list' %}
              
              
              
               bg-indigo-50 text-indigo-700 border-r-2 border-indigo-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-indigo-50 hover:text-indigo-900



            {% endif %}">
            <i class="fa-solid fa-clock w-5 h-5 mr-3 {% if 'news_reader' in request.resolver_match.namespace and request.resolver_match.url_name == 'session_list' %}
                
                
                
                 text-indigo-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-indigo-600



              {% endif %}">

            </i>
            <span>Sessions</span>
          </a>

          <a href="{% url 'news_reader:preparation_schedule_report' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'news_reader' in request.resolver_match.namespace and 'report' in request.resolver_match.url_name %}
              
              
              
               bg-indigo-50 text-indigo-700 border-r-2 border-indigo-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-indigo-50 hover:text-indigo-900



            {% endif %}">
            <i class="fa-solid fa-file-pdf w-5 h-5 mr-3 {% if 'news_reader' in request.resolver_match.namespace and 'report' in request.resolver_match.url_name %}
                
                
                
                 text-indigo-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-indigo-600



              {% endif %}">

            </i>
            <span>Reports</span>
          </a>
        </div>
      </div>
    {% endif %}

    <!-- 📅 SCHEDULING & PLANNING -->
    {% if can_manage_mentions %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-blue-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-calendar mr-2"></i>
          Scheduling
        </h3>
        <div class="space-y-1">
          <a href="{% url 'mentions:calendar_interface' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'calendar_interface' %}
              
              
              
               bg-blue-50 text-blue-700 border-r-2 border-blue-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-blue-50 hover:text-blue-900



            {% endif %}">
            <i class="fa-solid fa-calendar-days w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'calendar_interface' %}
                
                
                
                 text-blue-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-blue-600



              {% endif %}">

            </i>
            <span>Calendar & Schedule</span>
          </a>

          {% if current_membership.role == 'owner' or current_membership.role == 'admin' or current_membership.role == 'manager' %}
            <a href="{% url 'mentions:recurring_mentions' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'recurring' in request.resolver_match.url_name %}
                
                
                
                 bg-blue-50 text-blue-700 border-r-2 border-blue-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-blue-50 hover:text-blue-900



              {% endif %}">
              <i class="fa-solid fa-repeat w-5 h-5 mr-3 {% if 'recurring' in request.resolver_match.url_name %}
                  
                  
                  
                   text-blue-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-blue-600



                {% endif %}">

              </i>
              <span>Recurring Patterns</span>
              <span class="ml-auto text-xs text-green-600 font-medium bg-green-100 px-2 py-0.5 rounded-full">NEW</span>
            </a>

            <a href="{% url 'mentions:recurring_history' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'recurring_history' in request.resolver_match.url_name %}
                
                
                
                 bg-blue-50 text-blue-700 border-r-2 border-blue-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-blue-50 hover:text-blue-900



              {% endif %}">
              <i class="fa-solid fa-history w-5 h-5 mr-3 {% if 'recurring_history' in request.resolver_match.url_name %}
                  
                  
                  
                   text-blue-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-blue-600



                {% endif %}">

              </i>
              <span>Recurring History</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- ✅ WORKFLOW & APPROVALS -->
    {% if can_approve_mentions %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-orange-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-clipboard-check mr-2"></i>
          Workflow
        </h3>
        <div class="space-y-1">
          <a href="{% url 'mentions:approval_workflow' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'approval_workflow' %}
              
              
              
               bg-orange-50 text-orange-700 border-r-2 border-orange-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-orange-50 hover:text-orange-900



            {% endif %}">
            <i class="fa-solid fa-check-to-slot w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'approval_workflow' %}
                
                
                
                 text-orange-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-orange-600



              {% endif %}">

            </i>
            <span>Pending Approvals</span>
            {% if pending_approvals_count %}
              <span class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center font-semibold">{{ pending_approvals_count }}</span>
            {% endif %}
          </a>
        </div>
      </div>
    {% endif %}

    <!-- 📝 CONTENT MANAGEMENT -->
    {% if can_manage_mentions or can_manage_shows or can_manage_clients or can_manage_presenters %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-folder mr-2"></i>
          Content
        </h3>
        <div class="space-y-1">
          <!-- Mentions -->
          {% if can_manage_mentions %}
            <a href="{% url 'mentions:mention_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'mention' in request.resolver_match.url_name and 'approval' not in request.resolver_match.url_name and 'recurring' not in request.resolver_match.url_name %}
                
                
                
                 bg-primary-50 text-primary-700 border-r-2 border-primary-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-gray-50 hover:text-gray-900



              {% endif %}">
              <i class="fa-solid fa-bullhorn w-5 h-5 mr-3 {% if 'mention' in request.resolver_match.url_name and 'approval' not in request.resolver_match.url_name and 'recurring' not in request.resolver_match.url_name %}
                  
                  
                  
                   text-primary-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-gray-600



                {% endif %}">

              </i>
              <span>All Mentions</span>
            </a>
          {% endif %}

          <!-- Shows -->
          {% if can_manage_shows %}
            <a href="{% url 'shows:show_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'show' in request.resolver_match.url_name %}
                
                
                
                 bg-primary-50 text-primary-700 border-r-2 border-primary-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-gray-50 hover:text-gray-900



              {% endif %}">
              <i class="fa-solid fa-broadcast-tower w-5 h-5 mr-3 {% if 'show' in request.resolver_match.url_name %}
                  
                  
                  
                   text-primary-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-gray-600



                {% endif %}">

              </i>
              <span>Radio Shows</span>
            </a>
          {% endif %}

          <!-- Clients -->
          {% if can_manage_clients %}
            <a href="{% url 'core:client_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'client' in request.resolver_match.url_name %}
                
                
                
                 bg-primary-50 text-primary-700 border-r-2 border-primary-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-gray-50 hover:text-gray-900



              {% endif %}">
              <i class="fa-solid fa-building w-5 h-5 mr-3 {% if 'client' in request.resolver_match.url_name %}
                  
                  
                  
                   text-primary-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-gray-600



                {% endif %}">

              </i>
              <span>Clients & Sponsors</span>
            </a>
          {% endif %}

          <!-- Presenters -->
          {% if can_manage_presenters %}
            <a href="{% url 'core:presenter_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'presenter' in request.resolver_match.url_name %}
                
                
                
                 bg-primary-50 text-primary-700 border-r-2 border-primary-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-gray-50 hover:text-gray-900



              {% endif %}">
              <i class="fa-solid fa-microphone w-5 h-5 mr-3 {% if 'presenter' in request.resolver_match.url_name %}
                  
                  
                  
                   text-primary-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-gray-600



                {% endif %}">

              </i>
              <span>Presenters & DJs</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- 📊 ANALYTICS & REPORTS -->
    {% if can_view_reports or can_view_analytics %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-purple-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-chart-pie mr-2"></i>
          Analytics
        </h3>
        <div class="space-y-1">
          {% if can_view_reports %}
            <a href="{% url 'reports:report_list' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'report' in request.resolver_match.url_name %}
                
                
                
                 bg-purple-50 text-purple-700 border-r-2 border-purple-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-purple-50 hover:text-purple-900



              {% endif %}">
              <i class="fa-solid fa-file-lines w-5 h-5 mr-3 {% if 'report' in request.resolver_match.url_name %}
                  
                  
                  
                   text-purple-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-purple-600



                {% endif %}">

              </i>
              <span>Reports</span>
            </a>
          {% endif %} {% if can_view_analytics %}
            <a href="{% url 'reports:analytics' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if request.resolver_match.url_name == 'analytics' %}
                
                
                
                 bg-purple-50 text-purple-700 border-r-2 border-purple-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-purple-50 hover:text-purple-900



              {% endif %}">
              <i class="fa-solid fa-chart-line w-5 h-5 mr-3 {% if request.resolver_match.url_name == 'analytics' %}
                  
                  
                  
                   text-purple-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-purple-600



                {% endif %}">

              </i>
              <span>Analytics Dashboard</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- ⚙️ SYSTEM TOOLS -->
    {% if current_membership.role == 'owner' or current_membership.role == 'admin' or current_membership.role == 'manager' %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-yellow-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-tools mr-2"></i>
          System Tools
        </h3>
        <div class="space-y-1">
          <a href="{% url 'activity_logs:conflict_logs' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'conflict' in request.resolver_match.url_name %}
              
              
              
               bg-yellow-50 text-yellow-700 border-r-2 border-yellow-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-yellow-50 hover:text-yellow-900



            {% endif %}">
            <i class="fa-solid fa-exclamation-triangle w-5 h-5 mr-3 {% if 'conflict' in request.resolver_match.url_name %}
                
                
                
                 text-yellow-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-yellow-600



              {% endif %}">

            </i>
            <span>Conflict Detection</span>
          </a>

          {% comment %}
          <a href="{% url 'mentions:bulk_schedule' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'bulk' in request.resolver_match.url_name %}
              
              
              
               bg-yellow-50 text-yellow-700 border-r-2 border-yellow-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-yellow-50 hover:text-yellow-900



            {% endif %}">
            <i class="fa-solid fa-layer-group w-5 h-5 mr-3 {% if 'bulk' in request.resolver_match.url_name %}
                
                
                
                 text-yellow-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-yellow-600



              {% endif %}">

            </i>
            <span>Bulk Scheduling</span>
          </a>
          {% endcomment %} {% comment %}
          <a href="{% url 'template_designer:dashboard' %}"
            class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'template_designer' in request.resolver_match.app_name %}
              
              
              
               bg-yellow-50 text-yellow-700 border-r-2 border-yellow-500



            {% else %}
              
              
              
               text-gray-700 hover:bg-yellow-50 hover:text-yellow-900



            {% endif %}">
            <i class="fa-solid fa-paint-brush w-5 h-5 mr-3 {% if 'template_designer' in request.resolver_match.app_name %}
                
                
                
                 text-yellow-600



              {% else %}
                
                
                
                 text-gray-400 group-hover:text-yellow-600



              {% endif %}">

            </i>
            <span>Template Designer</span>
          </a>
          {% endcomment %}
        </div>
      </div>
    {% endif %}

    <!-- 🔧 ADMINISTRATION -->
    {% if current_membership.role == 'owner' or current_membership.role == 'admin' %}
      <div class="px-4 mt-6">
        <h3 class="px-3 text-xs font-semibold text-gray-600 uppercase tracking-wider mb-3 flex items-center">
          <i class="fa-solid fa-cog mr-2"></i>
          Administration
        </h3>
        <div class="space-y-1">
          {% if can_manage_users %}
            <a href="{% url 'authentication:user_manager' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'user' in request.resolver_match.url_name %}
                
                
                
                 bg-gray-50 text-gray-700 border-r-2 border-gray-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-gray-50 hover:text-gray-900



              {% endif %}">
              <i class="fa-solid fa-users w-5 h-5 mr-3 {% if 'user' in request.resolver_match.url_name %}
                  
                  
                  
                   text-gray-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-gray-600



                {% endif %}">

              </i>
              <span>User Management</span>
            </a>
          {% endif %} {% if can_manage_settings %}
            <a href="{% url 'settings:settings_dashboard' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'settings' in request.resolver_match.url_name %}
                
                
                
                 bg-gray-50 text-gray-700 border-r-2 border-gray-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-gray-50 hover:text-gray-900



              {% endif %}">
              <i class="fa-solid fa-cog w-5 h-5 mr-3 {% if 'settings' in request.resolver_match.url_name %}
                  
                  
                  
                   text-gray-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-gray-600



                {% endif %}">

              </i>
              <span>System Settings</span>
            </a>
          {% endif %} {% if can_view_activity_logs %}
            <a href="{% url 'activity_logs:activity_logs' %}"
              class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 group {% if 'activity' in request.resolver_match.url_name %}
                
                
                
                 bg-gray-50 text-gray-700 border-r-2 border-gray-500



              {% else %}
                
                
                
                 text-gray-700 hover:bg-gray-50 hover:text-gray-900



              {% endif %}">
              <i class="fa-solid fa-history w-5 h-5 mr-3 {% if 'activity' in request.resolver_match.url_name %}
                  
                  
                  
                   text-gray-600



                {% else %}
                  
                  
                  
                   text-gray-400 group-hover:text-gray-600



                {% endif %}">

              </i>
              <span>Activity Logs</span>
            </a>
          {% endif %}
        </div>
      </div>
    {% endif %}
  </nav>

  <!-- User Menu -->
  <div class="p-4 border-t border-gray-200">
    <div class="relative">
      <button onclick="toggleUserMenu()" class="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-all duration-200">
        <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3">
          <i class="fa-solid fa-user text-primary-600 text-sm"></i>
        </div>
        <div class="flex-1 text-left">
          <p class="font-medium">{{ user.get_full_name|default:user.username }}</p>
          <p class="text-xs text-gray-500">{{ current_membership.get_role_display|default:'User' }}</p>
        </div>
        <i class="fa-solid fa-chevron-up w-4 h-4 text-gray-400"></i>
      </button>

      <div id="userMenu" class="hidden absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg">
        <div class="py-1">
          <a href="{% url 'authentication:profile' %}" class="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
            <i class="fa-solid fa-user-edit w-4 h-4 mr-2"></i>
            Profile Settings
          </a>
          <a href="{% url 'account_logout' %}" class="block px-3 py-2 text-sm text-red-600 hover:bg-red-50">
            <i class="fa-solid fa-sign-out-alt w-4 h-4 mr-2"></i>
            Sign Out
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function toggleOrgSwitcher() {
    const switcher = document.getElementById('orgSwitcher')
    switcher.classList.toggle('hidden')
  }
  
  function toggleUserMenu() {
    const menu = document.getElementById('userMenu')
    menu.classList.toggle('hidden')
  }
  
  // Close menus when clicking outside
  document.addEventListener('click', function (event) {
    const userMenu = document.getElementById('userMenu')
    const orgSwitcher = document.getElementById('orgSwitcher')
    const button = event.target.closest('button')
  
    if (!button || !button.onclick || !button.onclick.toString().includes('toggleUserMenu')) {
      if (userMenu) {
        userMenu.classList.add('hidden')
      }
    }
  
    if (!button || !button.onclick || !button.onclick.toString().includes('toggleOrgSwitcher')) {
      if (orgSwitcher) {
        orgSwitcher.classList.add('hidden')
      }
    }
  })
</script>
