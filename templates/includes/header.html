<!-- Header -->
<header id="header" class="bg-white border-b border-gray-200 py-3 px-4 flex items-center justify-between">
  <div class="flex items-center">
    <h2 id="page-title" class="text-base font-semibold text-gray-800">
      {% block page_title %}
        {% if request.resolver_match.url_name == 'dashboard' %}
          Dashboard
        {% elif request.resolver_match.url_name == 'calendar' %}
          Calendar Schedule
        {% elif request.resolver_match.url_name == 'client_list' or request.resolver_match.url_name == 'client_detail' or request.resolver_match.url_name == 'client_create' or request.resolver_match.url_name == 'client_edit' %}
          Client Management
        {% elif request.resolver_match.url_name == 'presenter_list' or request.resolver_match.url_name == 'presenter_detail' or request.resolver_match.url_name == 'presenter_edit' %}
          Presenter Management
        {% elif request.resolver_match.url_name == 'show_list' or request.resolver_match.url_name == 'show_detail' or request.resolver_match.url_name == 'show_create' or request.resolver_match.url_name == 'show_edit' %}
          Show Management
        {% elif request.resolver_match.url_name == 'mention_list' or request.resolver_match.url_name == 'mention_detail' or request.resolver_match.url_name == 'mention_create' or request.resolver_match.url_name == 'mention_edit' or request.resolver_match.url_name == 'approval_list' %}
          Mention Management
        {% elif request.resolver_match.url_name == 'report_list' %}
          Reports
        {% elif request.resolver_match.url_name == 'analytics' %}
          Analytics
        {% else %}
          RadioMention
        {% endif %}
      {% endblock %}
    </h2>
  </div>
  <div class="flex items-center space-x-4">
    <div class="relative">
      <input type="text" placeholder="Search..." class="pl-9 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" id="globalSearch" />
      <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
    </div>

    <!-- Quick Create Dropdown -->
    {% load permission_tags %}
    {% if can_manage_mentions or can_manage_clients or can_manage_shows %}
      <div class="relative">
        <button class="flex items-center px-3 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200" onclick="toggleQuickCreate()">
          <i class="fa-solid fa-plus w-4 h-4 mr-2"></i>
          <span>Create</span>
          <i class="fa-solid fa-chevron-down w-3 h-3 ml-2"></i>
        </button>
        <div id="quickCreateDropdown" class="hidden absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg border border-gray-200 z-50">
          <div class="py-1">
            {% if can_manage_mentions %}
              <a href="{% url 'mentions:mention_create' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">
                <i class="fa-solid fa-bullhorn w-4 h-4 mr-3 text-gray-400"></i>
                <span>New Mention</span>
              </a>
              <a href="{% url 'mentions:recurring_mention_create' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">
                <i class="fa-solid fa-repeat w-4 h-4 mr-3 text-gray-400"></i>
                <span>New Recurring Pattern</span>
              </a>
            {% endif %}
            {% if can_manage_clients %}
              <a href="{% url 'core:client_create' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">
                <i class="fa-solid fa-building w-4 h-4 mr-3 text-gray-400"></i>
                <span>New Client</span>
              </a>
            {% endif %}
            {% if can_manage_shows %}
              <a href="{% url 'shows:show_create' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150">
                <i class="fa-solid fa-broadcast-tower w-4 h-4 mr-3 text-gray-400"></i>
                <span>New Show</span>
              </a>
            {% endif %}
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Notification Bell -->
    <div class="relative">
      <button class="relative p-2 text-gray-500 hover:text-gray-700 transition-colors" onclick="toggleNotifications()">
        <i class="fa-solid fa-bell text-lg"></i>
        <span id="notificationBadge" class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center hidden notification-badge">0</span>
      </button>
      <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
        <div class="p-4 border-b border-gray-200 flex items-center justify-between">
          <h3 class="text-sm font-semibold text-gray-800">Recent Notifications</h3>
          <button onclick="markAllAsRead()" class="text-xs text-blue-600 hover:text-blue-800">Mark all read</button>
        </div>
        <div class="max-h-80 overflow-y-auto">
          <div id="notificationList" class="divide-y divide-gray-100">
            <div class="p-4 text-sm text-gray-500 text-center">Loading notifications...</div>
          </div>
        </div>
        <div class="p-3 border-t border-gray-200 text-center">
          <a href="{% url 'settings:user_preferences' %}" class="text-xs text-blue-600 hover:text-blue-800">Notification Settings</a>
        </div>
      </div>
    </div>

    <!-- System Status Indicator -->
    <div class="relative">
      <button class="relative p-2 text-gray-500 hover:text-gray-700 transition-colors" onclick="toggleSystemStatus()" title="System Status"><i id="systemStatusIcon" class="fa-solid fa-circle text-green-500 text-sm"></i></button>
      <div id="systemStatusDropdown" class="hidden absolute right-0 mt-2 w-72 bg-white rounded-lg shadow-xl border border-gray-200 z-50">
        <div class="p-4 border-b border-gray-200">
          <h3 class="text-sm font-semibold text-gray-800">System Status</h3>
        </div>
        <div id="systemStatusContent" class="p-4">
          <div class="text-sm text-gray-500">Loading system status...</div>
        </div>
      </div>
    </div>
    <button class="p-2 text-gray-500 hover:text-gray-700" onclick="showHelp()"><i class="fa-solid fa-question-circle"></i></button>
    {% block header_actions %}

    {% endblock %}
  </div>
</header>

<script>
  function toggleQuickCreate() {
    const dropdown = document.getElementById('quickCreateDropdown')
    dropdown.classList.toggle('hidden')
  }
  
  function toggleNotifications() {
    const dropdown = document.getElementById('notificationDropdown')
    dropdown.classList.toggle('hidden')
  
    // Load notifications if opening
    if (!dropdown.classList.contains('hidden')) {
      loadNotifications()
    }
  }
  
  function loadNotifications() {
    // This would typically fetch from an API endpoint
    const notificationList = document.getElementById('notificationList')
    notificationList.innerHTML = `
                      <div class="space-y-2">
                          <div class="p-2 hover:bg-gray-50 rounded">
                              <p class="text-sm text-gray-800">New mention pending approval</p>
                              <p class="text-xs text-gray-500">2 minutes ago</p>
                          </div>
                          <div class="p-2 hover:bg-gray-50 rounded">
                              <p class="text-sm text-gray-800">Show schedule updated</p>
                              <p class="text-xs text-gray-500">1 hour ago</p>
                          </div>
                      </div>
                  `
  }
  
  function showHelp() {
    alert('Help documentation coming soon!')
  }

  function toggleSystemStatus() {
    const dropdown = document.getElementById('systemStatusDropdown')
    dropdown.classList.toggle('hidden')

    // Load system status if opening
    if (!dropdown.classList.contains('hidden')) {
      loadSystemStatus()
    }
  }

  function loadSystemStatus() {
    const statusContent = document.getElementById('systemStatusContent')
    const statusIcon = document.getElementById('systemStatusIcon')

    // Show loading state
    statusContent.innerHTML = '<div class="text-sm text-gray-500">Loading system status...</div>'

    // Fetch system status
    fetch('/system-performance/api/')
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const metrics = data.metrics
          const healthScore = metrics.performance.health_score

          // Update status icon
          if (healthScore >= 90) {
            statusIcon.className = 'fa-solid fa-circle text-green-500 text-sm'
          } else if (healthScore >= 70) {
            statusIcon.className = 'fa-solid fa-circle text-yellow-500 text-sm'
          } else {
            statusIcon.className = 'fa-solid fa-circle text-red-500 text-sm'
          }

          // Update status content
          statusContent.innerHTML = `
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Health Score</span>
                <span class="text-sm font-medium ${healthScore >= 90 ? 'text-green-600' : healthScore >= 70 ? 'text-yellow-600' : 'text-red-600'}">${healthScore}/100</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Active Shows</span>
                <span class="text-sm font-medium">${metrics.shows.active_sessions}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Pending Mentions</span>
                <span class="text-sm font-medium">${metrics.mentions.pending_today}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">Completion Rate</span>
                <span class="text-sm font-medium">${metrics.performance.completion_rate}%</span>
              </div>
              ${metrics.activity.errors_last_hour > 0 ? `
                <div class="flex justify-between items-center">
                  <span class="text-sm text-red-600">Recent Errors</span>
                  <span class="text-sm font-medium text-red-600">${metrics.activity.errors_last_hour}</span>
                </div>
              ` : ''}
            </div>
          `
        } else {
          statusContent.innerHTML = '<div class="text-sm text-red-500">Error loading system status</div>'
          statusIcon.className = 'fa-solid fa-circle text-gray-400 text-sm'
        }
      })
      .catch(error => {
        console.error('Error loading system status:', error)
        statusContent.innerHTML = '<div class="text-sm text-red-500">Error loading system status</div>'
        statusIcon.className = 'fa-solid fa-circle text-gray-400 text-sm'
      })
  }

  function updateNotificationBadge(count) {
    const badge = document.getElementById('notificationBadge')
    if (count > 0) {
      badge.textContent = count > 99 ? '99+' : count
      badge.classList.remove('hidden')
    } else {
      badge.classList.add('hidden')
    }
  }

  function markAllAsRead() {
    // Implementation for marking all notifications as read using optimized AJAX
    if (window.ajax) {
      window.ajax.post('/api/notifications/mark-all-read/', {})
        .then(() => {
          loadNotifications()
        })
        .catch(error => {
          console.error('Error marking all notifications as read:', error);
        });
    } else {
      // Fallback to regular fetch
      fetch('/api/notifications/mark-all-read/', {
        method: 'POST',
        headers: {
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
          'Content-Type': 'application/json'
        }
      }).then(() => {
        loadNotifications()
      })
    }
  }

  function getNotificationIcon(type) {
    const icons = {
      'show_alert': 'fa-broadcast-tower',
      'approval_reminder': 'fa-clock',
      'system_alert': 'fa-exclamation-triangle',
      'mention_update': 'fa-bullhorn',
      'schedule_change': 'fa-calendar-alt'
    }
    return icons[type] || 'fa-info-circle'
  }

  function getNotificationColor(type) {
    const colors = {
      'show_alert': 'red',
      'approval_reminder': 'yellow',
      'system_alert': 'red',
      'mention_update': 'blue',
      'schedule_change': 'green'
    }
    return colors[type] || 'blue'
  }

  function formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date

    if (diff < 60000) return 'Just now'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`
    return date.toLocaleDateString()
  }
  
  // Global search functionality
  document.getElementById('globalSearch').addEventListener('keypress', function (e) {
    if (e.key === 'Enter') {
      const query = this.value.trim()
      if (query) {
        // Implement global search
        window.location.href = `/search/?q=${encodeURIComponent(query)}`
      }
    }
  })
  
  // Close dropdowns when clicking outside
  document.addEventListener('click', function (event) {
    const notificationDropdown = document.getElementById('notificationDropdown')
    const quickCreateDropdown = document.getElementById('quickCreateDropdown')
    const systemStatusDropdown = document.getElementById('systemStatusDropdown')

    const notificationButton = event.target.closest('button[onclick="toggleNotifications()"]')
    const quickCreateButton = event.target.closest('button[onclick="toggleQuickCreate()"]')
    const systemStatusButton = event.target.closest('button[onclick="toggleSystemStatus()"]')

    if (!notificationButton && notificationDropdown) {
      notificationDropdown.classList.add('hidden')
    }

    if (!quickCreateButton && quickCreateDropdown) {
      quickCreateDropdown.classList.add('hidden')
    }

    if (!systemStatusButton && systemStatusDropdown) {
      systemStatusDropdown.classList.add('hidden')
    }
  })

  // Auto-refresh system status every 30 seconds
  setInterval(() => {
    const statusDropdown = document.getElementById('systemStatusDropdown')
    if (!statusDropdown.classList.contains('hidden')) {
      loadSystemStatus()
    }
  }, 30000)

  // Load initial system status
  document.addEventListener('DOMContentLoaded', function() {
    loadSystemStatus()
  })
</script>
