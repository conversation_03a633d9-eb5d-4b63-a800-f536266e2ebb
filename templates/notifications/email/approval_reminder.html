<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mention Approval Reminder</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: {% if urgency == 'urgent' %}#ef4444{% else %}#f59e0b{% endif %};
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 20px;
            border: 1px solid #e2e8f0;
        }
        .footer {
            background-color: #64748b;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            border-radius: 0 0 8px 8px;
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            border-left: 4px solid {% if urgency == 'urgent' %}#ef4444{% else %}#f59e0b{% endif %};
            background-color: {% if urgency == 'urgent' %}#fee2e2{% else %}#fef3c7{% endif %};
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: {% if urgency == 'urgent' %}#ef4444{% else %}#f59e0b{% endif %};
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
        }
        .mention-list {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border: 1px solid #e2e8f0;
        }
        .mention-item {
            padding: 10px;
            border-bottom: 1px solid #f1f5f9;
            margin-bottom: 10px;
        }
        .mention-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .priority-high {
            border-left: 3px solid #ef4444;
            padding-left: 12px;
        }
        .priority-urgent {
            border-left: 3px solid #dc2626;
            padding-left: 12px;
            background-color: #fef2f2;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{% if urgency == 'urgent' %}🚨 URGENT{% else %}⏰{% endif %} Approval Reminder</h1>
        <p>{{ count }} Mention{{ count|pluralize }} Pending Approval</p>
    </div>
    
    <div class="content">
        <h2>Hello {{ user.first_name|default:user.username }},</h2>
        
        <div class="alert">
            <p><strong>{% if urgency == 'urgent' %}URGENT:{% else %}Reminder:{% endif %}</strong> 
            You have {{ count }} mention{{ count|pluralize }} that {{ count|pluralize:"has,have" }} been pending approval for over {{ threshold_hours }} hour{{ threshold_hours|pluralize }}.</p>
            <p><strong>Organization:</strong> {{ organization.name }}</p>
        </div>
        
        {% if urgency == 'urgent' %}
        <p><strong>These mentions require immediate attention as they have been pending for over 24 hours.</strong></p>
        {% else %}
        <p>Please review and approve or reject these mentions to keep the workflow moving smoothly.</p>
        {% endif %}
        
        <div class="mention-list">
            <h3>Pending Mentions:</h3>
            {% for mention in mentions %}
            <div class="mention-item {% if mention.priority >= 3 %}priority-high{% endif %} {% if mention.priority == 4 %}priority-urgent{% endif %}">
                <h4>{{ mention.title }}</h4>
                <ul>
                    <li><strong>Client:</strong> {{ mention.client.name }}</li>
                    <li><strong>Priority:</strong> {{ mention.get_priority_display }}</li>
                    <li><strong>Created:</strong> {{ mention.created_at|date:"M j, Y g:i A" }}</li>
                    <li><strong>Created by:</strong> {{ mention.created_by.get_full_name|default:mention.created_by.username }}</li>
                    <li><strong>Duration:</strong> {{ mention.duration_seconds }} seconds</li>
                </ul>
                {% if mention.notes %}
                <p><strong>Notes:</strong> {{ mention.notes|truncatewords:20 }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <h3>Next Steps:</h3>
        <ul>
            <li>Review each mention for content and accuracy</li>
            <li>Approve mentions that meet quality standards</li>
            <li>Reject mentions that need revision with clear feedback</li>
            <li>Schedule approved mentions to appropriate shows</li>
        </ul>
        
        <a href="{{ dashboard_url|default:'#' }}" class="button">Review Pending Mentions</a>
    </div>
    
    <div class="footer">
        <p>This is an automated reminder from RadioMention.</p>
        <p>Organization: {{ organization.name }}</p>
    </div>
</body>
</html>
