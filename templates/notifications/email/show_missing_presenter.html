<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Show Alert - Missing Presenter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #ef4444;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 20px;
            border: 1px solid #e2e8f0;
        }
        .footer {
            background-color: #64748b;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            border-radius: 0 0 8px 8px;
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            border-left: 4px solid #ef4444;
            background-color: #fee2e2;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #ef4444;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
        }
        .show-details {
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚨 URGENT: Show Alert</h1>
        <p>Missing Presenter</p>
    </div>
    
    <div class="content">
        <h2>Hello {{ user.first_name|default:user.username }},</h2>
        
        <div class="alert">
            <p><strong>URGENT:</strong> A show that should be on air is missing a presenter or has no active session.</p>
            <p><strong>Issue:</strong> {{ issue }}</p>
            <p><strong>Time:</strong> {{ time|date:"F j, Y g:i A" }}</p>
        </div>
        
        <div class="show-details">
            <h3>Show Details:</h3>
            <ul>
                <li><strong>Show Name:</strong> {{ show.name }}</li>
                <li><strong>Scheduled Time:</strong> {{ show.start_time }} - {{ show.end_time }}</li>
                <li><strong>Organization:</strong> {{ show.organization.name }}</li>
                {% if show.description %}
                <li><strong>Description:</strong> {{ show.description }}</li>
                {% endif %}
            </ul>
            
            {% if session %}
            <h4>Session Information:</h4>
            <ul>
                <li><strong>Session Started:</strong> {{ session.actual_start_time|date:"g:i A" }}</li>
                {% if session.presenter %}
                <li><strong>Assigned Presenter:</strong> {{ session.presenter.display_name }}</li>
                {% else %}
                <li><strong>Presenter:</strong> <span style="color: #ef4444;">None Assigned</span></li>
                {% endif %}
            </ul>
            {% endif %}
        </div>
        
        <h3>Immediate Action Required:</h3>
        <ul>
            <li>Check if a presenter is available to start the show</li>
            <li>Contact the assigned presenter if they haven't started</li>
            <li>Consider starting the show manually if needed</li>
            <li>Update the show session status in the system</li>
        </ul>
        
        <p><strong>This alert was generated automatically by the RadioMention monitoring system.</strong></p>
        
        <a href="{{ dashboard_url|default:'#' }}" class="button">View Live Monitor</a>
    </div>
    
    <div class="footer">
        <p>This is an automated alert from RadioMention Show Monitoring.</p>
        <p>Organization: {{ show.organization.name }}</p>
    </div>
</body>
</html>
