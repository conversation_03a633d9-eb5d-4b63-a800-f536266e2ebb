<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Daily Summary Report</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        background-color: #3b82f6;
        color: white;
        padding: 20px;
        text-align: center;
        border-radius: 8px 8px 0 0;
      }
      .content {
        background-color: #f8fafc;
        padding: 20px;
        border: 1px solid #e2e8f0;
      }
      .footer {
        background-color: #64748b;
        color: white;
        padding: 15px;
        text-align: center;
        font-size: 12px;
        border-radius: 0 0 8px 8px;
      }
      .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin: 20px 0;
      }
      .stat-card {
        background-color: white;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        text-align: center;
      }
      .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #3b82f6;
      }
      .stat-label {
        color: #64748b;
        font-size: 0.9em;
      }
      .performance-indicator {
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
        text-align: center;
      }
      .performance-good {
        background-color: #dcfce7;
        border: 1px solid #16a34a;
        color: #15803d;
      }
      .performance-warning {
        background-color: #fef3c7;
        border: 1px solid #f59e0b;
        color: #d97706;
      }
      .performance-poor {
        background-color: #fee2e2;
        border: 1px solid #ef4444;
        color: #dc2626;
      }
      .button {
        display: inline-block;
        padding: 12px 24px;
        background-color: #3b82f6;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>📊 Daily Summary</h1>
      <p>{{ date|date:'F j, Y' }} - {{ organization.name }}</p>
    </div>

    <div class="content">
      <h2>Hello {{ user.first_name|default:user.username }},</h2>

      <p>Here's your daily summary for {{ organization.name }} on {{ date|date:'F j, Y' }}:</p>

      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number">{{ stats.shows_scheduled }}</div>
          <div class="stat-label">Shows Scheduled</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ stats.mentions_completed }}</div>
          <div class="stat-label">Mentions Completed</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ stats.mentions_missed }}</div>
          <div class="stat-label">Mentions Missed</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ stats.active_presenters }}</div>
          <div class="stat-label">Active Presenters</div>
        </div>
      </div>

      {% if stats.mentions_completed > 0 or stats.mentions_missed > 0 %}
        {% with total_mentions=stats.mentions_completed|add:stats.mentions_missed %}
          {% if total_mentions > 0 %}
            <div class="performance-indicator {% if stats.mentions_completed >= 90 %}
                performance-good
              {% elif stats.mentions_completed >= 70 %}
                performance-warning
              {% else %}
                performance-poor
              {% endif %}">
              <strong>Completion Summary</strong>
              <br />
              {{ stats.mentions_completed }} completed out of {{ total_mentions }} total mentions
            </div>
          {% endif %}
        {% endwith %}
      {% endif %}

      <h3>Key Highlights:</h3>
      <ul>
        {% if stats.shows_scheduled > 0 %}
          <li>{{ stats.shows_scheduled }} show{{ stats.shows_scheduled|pluralize }} were scheduled for the day</li>
        {% endif %}

        {% if stats.mentions_completed > 0 %}
          <li>{{ stats.mentions_completed }} mention{{ stats.mentions_completed|pluralize }} {{ stats.mentions_completed|pluralize:'was,were' }} successfully completed</li>
        {% endif %}

        {% if stats.mentions_missed > 0 %}
          <li>
            <strong>{{ stats.mentions_missed }} mention{{ stats.mentions_missed|pluralize }} {{ stats.mentions_missed|pluralize:'was,were' }} missed</strong> - may need follow-up
          </li>
        {% endif %}

        {% if stats.active_presenters > 0 %}
          <li>{{ stats.active_presenters }} presenter{{ stats.active_presenters|pluralize }} {{ stats.active_presenters|pluralize:'was,were' }} active during the day</li>
        {% endif %}
      </ul>

      {% if stats.mentions_missed > 0 %}
        <h3>⚠️ Action Items:</h3>
        <ul>
          <li>Review missed mentions and determine if they need to be rescheduled</li>
          <li>Contact presenters who may have missed their scheduled mentions</li>
          <li>Check for any technical issues that may have caused missed mentions</li>
        </ul>
      {% endif %}

      <p>For detailed reports and analytics, visit your dashboard.</p>

      <a href="{{ dashboard_url|default:'#' }}" class="button">View Full Dashboard</a>
    </div>

    <div class="footer">
      <p>This is your automated daily summary from RadioMention.</p>
      <p>Generated on {{ 'now'|date:'F j, Y g:i A' }}</p>
    </div>
  </body>
</html>
