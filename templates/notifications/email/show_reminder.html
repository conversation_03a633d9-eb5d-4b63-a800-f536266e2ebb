<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Show Reminder</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #10b981;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 20px;
            border: 1px solid #e2e8f0;
        }
        .footer {
            background-color: #64748b;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            border-radius: 0 0 8px 8px;
        }
        .show-details {
            background-color: white;
            padding: 20px;
            border-radius: 6px;
            margin: 15px 0;
            border: 1px solid #e2e8f0;
            border-left: 4px solid #10b981;
        }
        .time-highlight {
            background-color: #dcfce7;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
            margin: 15px 0;
            border: 1px solid #16a34a;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #10b981;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
        }
        .checklist {
            background-color: #f0f9ff;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #0ea5e9;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📻 Show Reminder</h1>
        <p>Your show starts in 30 minutes!</p>
    </div>
    
    <div class="content">
        <h2>Hello {{ presenter.display_name }},</h2>
        
        <p>This is a friendly reminder that your show is starting soon!</p>
        
        <div class="time-highlight">
            <h3>⏰ {{ show.start_time|time:"g:i A" }}</h3>
            <p>Your show starts in approximately 30 minutes</p>
        </div>
        
        <div class="show-details">
            <h3>Show Details:</h3>
            <ul>
                <li><strong>Show Name:</strong> {{ show.name }}</li>
                <li><strong>Time:</strong> {{ show.start_time|time:"g:i A" }} - {{ show.end_time|time:"g:i A" }}</li>
                <li><strong>Duration:</strong> {{ show.duration_minutes }} minutes</li>
                {% if show.description %}
                <li><strong>Description:</strong> {{ show.description }}</li>
                {% endif %}
            </ul>
        </div>
        
        <div class="checklist">
            <h3>📋 Pre-Show Checklist:</h3>
            <ul>
                <li>☐ Review today's mentions and content</li>
                <li>☐ Check technical equipment and audio levels</li>
                <li>☐ Prepare any notes or talking points</li>
                <li>☐ Start your show session in the system</li>
                <li>☐ Mark mentions as read during the show</li>
            </ul>
        </div>
        
        <h3>Quick Actions:</h3>
        <ul>
            <li>View your mentions for today</li>
            <li>Check the live show interface</li>
            <li>Review any last-minute updates</li>
        </ul>
        
        <p><strong>Remember:</strong> Don't forget to start your show session in the RadioMention system when you begin broadcasting!</p>
        
        <a href="{{ live_show_url|default:'#' }}" class="button">Go to Live Show Interface</a>
    </div>
    
    <div class="footer">
        <p>This is an automated reminder from RadioMention.</p>
        <p>Good luck with your show!</p>
    </div>
</body>
</html>
