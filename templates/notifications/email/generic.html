<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RadioMention Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #3b82f6;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8fafc;
            padding: 20px;
            border: 1px solid #e2e8f0;
        }
        .footer {
            background-color: #64748b;
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            border-radius: 0 0 8px 8px;
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            border-left: 4px solid #3b82f6;
            background-color: #dbeafe;
        }
        .alert.warning {
            border-left-color: #f59e0b;
            background-color: #fef3c7;
        }
        .alert.error {
            border-left-color: #ef4444;
            background-color: #fee2e2;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>RadioMention</h1>
        <p>{{ notification_type }}</p>
    </div>
    
    <div class="content">
        <h2>Hello {{ user.first_name|default:user.username }},</h2>
        
        <div class="alert">
            <p><strong>Notification:</strong> {{ notification_type }}</p>
            {% if organization %}
            <p><strong>Organization:</strong> {{ organization.name }}</p>
            {% endif %}
            <p><strong>Time:</strong> {{ time|default:"now"|date:"F j, Y g:i A" }}</p>
        </div>
        
        {% if message %}
        <p>{{ message }}</p>
        {% endif %}
        
        {% if show %}
        <h3>Show Details:</h3>
        <ul>
            <li><strong>Show:</strong> {{ show.name }}</li>
            <li><strong>Time:</strong> {{ show.start_time }} - {{ show.end_time }}</li>
            {% if presenter %}
            <li><strong>Presenter:</strong> {{ presenter.display_name }}</li>
            {% endif %}
        </ul>
        {% endif %}
        
        {% if mention %}
        <h3>Mention Details:</h3>
        <ul>
            <li><strong>Title:</strong> {{ mention.title }}</li>
            <li><strong>Client:</strong> {{ mention.client.name }}</li>
            <li><strong>Priority:</strong> {{ mention.get_priority_display }}</li>
        </ul>
        {% endif %}
        
        <p>Please log in to your RadioMention dashboard for more details.</p>
        
        <a href="{{ dashboard_url|default:'#' }}" class="button">View Dashboard</a>
    </div>
    
    <div class="footer">
        <p>This is an automated notification from RadioMention.</p>
        <p>If you no longer wish to receive these notifications, please update your preferences in your dashboard.</p>
    </div>
</body>
</html>
