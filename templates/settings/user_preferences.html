{% extends 'base.html' %}
{% load static %}

{% block title %}User Preferences{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="{% url 'settings:settings_dashboard' %}" class="text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fa-solid fa-arrow-left"></i>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">User Preferences</h1>
                </div>
            </div>
        </div>
    </div>

    <!-- Preferences Form -->
    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Display Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Display Preferences</h3>
                <p class="text-sm text-gray-500 mt-1">Customize how the interface appears to you</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="theme" class="block text-sm font-medium text-gray-700 mb-1">Theme</label>
                        <select id="theme" name="theme"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="light" {% if preferences.theme == 'light' %}selected{% endif %}>Light</option>
                            <option value="dark" {% if preferences.theme == 'dark' %}selected{% endif %}>Dark</option>
                            <option value="auto" {% if preferences.theme == 'auto' %}selected{% endif %}>Auto (System)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="language" class="block text-sm font-medium text-gray-700 mb-1">Language</label>
                        <select id="language" name="language"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="en" {% if preferences.language == 'en' %}selected{% endif %}>English</option>
                            <option value="es" {% if preferences.language == 'es' %}selected{% endif %}>Spanish</option>
                            <option value="fr" {% if preferences.language == 'fr' %}selected{% endif %}>French</option>
                            <option value="de" {% if preferences.language == 'de' %}selected{% endif %}>German</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="timezone" class="block text-sm font-medium text-gray-700 mb-1">Timezone</label>
                        <select id="timezone" name="timezone"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="UTC" {% if preferences.timezone == 'UTC' %}selected{% endif %}>UTC</option>
                            <option value="Europe/Moscow" {% if preferences.timezone == 'Europe/Moscow' %}selected{% endif %}>Moscow Time (UTC+3)</option>
                            <option value="Europe/Istanbul" {% if preferences.timezone == 'Europe/Istanbul' %}selected{% endif %}>Turkey Time (UTC+3)</option>
                            <option value="Africa/Nairobi" {% if preferences.timezone == 'Africa/Nairobi' %}selected{% endif %}>East Africa Time (UTC+3)</option>
                            <option value="Asia/Riyadh" {% if preferences.timezone == 'Asia/Riyadh' %}selected{% endif %}>Arabia Standard Time (UTC+3)</option>
                            <option value="Europe/Athens" {% if preferences.timezone == 'Europe/Athens' %}selected{% endif %}>Eastern European Time (UTC+2/+3)</option>
                            <option value="Europe/London" {% if preferences.timezone == 'Europe/London' %}selected{% endif %}>Greenwich Mean Time (UTC+0/+1)</option>
                            <option value="Europe/Paris" {% if preferences.timezone == 'Europe/Paris' %}selected{% endif %}>Central European Time (UTC+1/+2)</option>
                            <option value="America/New_York" {% if preferences.timezone == 'America/New_York' %}selected{% endif %}>Eastern Time (UTC-5/-4)</option>
                            <option value="America/Chicago" {% if preferences.timezone == 'America/Chicago' %}selected{% endif %}>Central Time (UTC-6/-5)</option>
                            <option value="America/Denver" {% if preferences.timezone == 'America/Denver' %}selected{% endif %}>Mountain Time (UTC-7/-6)</option>
                            <option value="America/Los_Angeles" {% if preferences.timezone == 'America/Los_Angeles' %}selected{% endif %}>Pacific Time (UTC-8/-7)</option>
                            <option value="America/Anchorage" {% if preferences.timezone == 'America/Anchorage' %}selected{% endif %}>Alaska Time (UTC-9/-8)</option>
                            <option value="Pacific/Honolulu" {% if preferences.timezone == 'Pacific/Honolulu' %}selected{% endif %}>Hawaii Time (UTC-10)</option>
                            <option value="Asia/Tokyo" {% if preferences.timezone == 'Asia/Tokyo' %}selected{% endif %}>Japan Standard Time (UTC+9)</option>
                            <option value="Australia/Sydney" {% if preferences.timezone == 'Australia/Sydney' %}selected{% endif %}>Australian Eastern Time (UTC+10/+11)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="date_format" class="block text-sm font-medium text-gray-700 mb-1">Date Format</label>
                        <select id="date_format" name="date_format"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="MM/DD/YYYY" {% if preferences.date_format == 'MM/DD/YYYY' %}selected{% endif %}>MM/DD/YYYY (US Format)</option>
                            <option value="DD/MM/YYYY" {% if preferences.date_format == 'DD/MM/YYYY' %}selected{% endif %}>DD/MM/YYYY (European Format)</option>
                            <option value="YYYY-MM-DD" {% if preferences.date_format == 'YYYY-MM-DD' %}selected{% endif %}>YYYY-MM-DD (ISO Format)</option>
                            <option value="MMM DD, YYYY" {% if preferences.date_format == 'MMM DD, YYYY' %}selected{% endif %}>MMM DD, YYYY (e.g., Jan 15, 2024)</option>
                            <option value="DD MMM YYYY" {% if preferences.date_format == 'DD MMM YYYY' %}selected{% endif %}>DD MMM YYYY (e.g., 15 Jan 2024)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="time_format" class="block text-sm font-medium text-gray-700 mb-1">Time Format</label>
                        <select id="time_format" name="time_format"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="12" {% if preferences.time_format == '12' %}selected{% endif %}>12-hour (AM/PM)</option>
                            <option value="24" {% if preferences.time_format == '24' %}selected{% endif %}>24-hour</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="items_per_page" class="block text-sm font-medium text-gray-700 mb-1">Items per Page</label>
                        <select id="items_per_page" name="items_per_page"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="10" {% if preferences.items_per_page == 10 %}selected{% endif %}>10</option>
                            <option value="25" {% if preferences.items_per_page == 25 %}selected{% endif %}>25</option>
                            <option value="50" {% if preferences.items_per_page == 50 %}selected{% endif %}>50</option>
                            <option value="100" {% if preferences.items_per_page == 100 %}selected{% endif %}>100</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 flex items-center">
                        <i class="fas fa-bell mr-2 text-blue-600"></i>
                        Notification Preferences
                    </h3>
                    <p class="text-sm text-gray-500 mt-1">Choose how and when you want to be notified</p>
                </div>
                <button type="button" onclick="testNotifications()" class="text-sm text-blue-600 hover:text-blue-800">
                    <i class="fas fa-vial mr-1"></i>
                    Test Notifications
                </button>
            </div>
            <div class="p-6">
                <!-- Notification Delivery Methods -->
                <div class="mb-8">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Delivery Methods</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg">
                            <input type="checkbox" id="email_notifications" name="email_notifications"
                                   {% if preferences.email_notifications %}checked{% endif %}
                                   class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                            <label for="email_notifications" class="ml-3 block text-sm text-gray-900">
                                <div class="flex items-center">
                                    <i class="fas fa-envelope mr-2 text-blue-500"></i>
                                    Email Notifications
                                </div>
                                <span class="text-gray-500 text-xs block mt-1">Receive notifications via email</span>
                            </label>
                        </div>

                        <div class="flex items-center p-4 border border-gray-200 rounded-lg">
                            <input type="checkbox" id="browser_notifications" name="browser_notifications"
                                   {% if preferences.browser_notifications %}checked{% endif %}
                                   class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                            <label for="browser_notifications" class="ml-3 block text-sm text-gray-900">
                                <div class="flex items-center">
                                    <i class="fas fa-desktop mr-2 text-green-500"></i>
                                    Browser Notifications
                                </div>
                                <span class="text-gray-500 text-xs block mt-1">Show notifications in your browser</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Notification Types -->
                <div class="mb-8">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Notification Types</h4>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <input type="checkbox" id="show_alerts" name="show_alerts"
                                       {% if preferences.show_alerts %}checked{% endif %}
                                       class="h-4 w-4 text-red-600 border-gray-300 rounded focus:ring-red-500">
                                <label for="show_alerts" class="ml-3 block text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <i class="fas fa-exclamation-triangle mr-2 text-red-500"></i>
                                        Show Alerts
                                    </div>
                                    <span class="text-gray-500 text-xs block mt-1">Critical system alerts and missing presenters</span>
                                </label>
                            </div>
                            <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">Critical</span>
                        </div>

                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <input type="checkbox" id="mention_notifications" name="mention_notifications"
                                       {% if preferences.mention_notifications %}checked{% endif %}
                                       class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label for="mention_notifications" class="ml-3 block text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <i class="fas fa-bullhorn mr-2 text-blue-500"></i>
                                        Mention Updates
                                    </div>
                                    <span class="text-gray-500 text-xs block mt-1">New mentions assigned and status changes</span>
                                </label>
                            </div>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Important</span>
                        </div>

                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <input type="checkbox" id="approval_notifications" name="approval_notifications"
                                       {% if preferences.approval_notifications %}checked{% endif %}
                                       class="h-4 w-4 text-yellow-600 border-gray-300 rounded focus:ring-yellow-500">
                                <label for="approval_notifications" class="ml-3 block text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <i class="fas fa-clock mr-2 text-yellow-500"></i>
                                        Approval Reminders
                                    </div>
                                    <span class="text-gray-500 text-xs block mt-1">Mentions pending approval and deadlines</span>
                                </label>
                            </div>
                            <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">Workflow</span>
                        </div>

                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <input type="checkbox" id="conflict_notifications" name="conflict_notifications"
                                       {% if preferences.conflict_notifications %}checked{% endif %}
                                       class="h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                <label for="conflict_notifications" class="ml-3 block text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <i class="fas fa-exclamation-circle mr-2 text-orange-500"></i>
                                        Conflict Alerts
                                    </div>
                                    <span class="text-gray-500 text-xs block mt-1">Scheduling conflicts and resolution needed</span>
                                </label>
                            </div>
                            <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full">Warning</span>
                        </div>

                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <input type="checkbox" id="schedule_notifications" name="schedule_notifications"
                                       {% if preferences.schedule_notifications %}checked{% endif %}
                                       class="h-4 w-4 text-green-600 border-gray-300 rounded focus:ring-green-500">
                                <label for="schedule_notifications" class="ml-3 block text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar-alt mr-2 text-green-500"></i>
                                        Schedule Changes
                                    </div>
                                    <span class="text-gray-500 text-xs block mt-1">Show schedule updates and time changes</span>
                                </label>
                            </div>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Updates</span>
                        </div>

                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                            <div class="flex items-center">
                                <input type="checkbox" id="deadline_notifications" name="deadline_notifications"
                                       {% if preferences.deadline_notifications %}checked{% endif %}
                                       class="h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                                <label for="deadline_notifications" class="ml-3 block text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <i class="fas fa-hourglass-half mr-2 text-purple-500"></i>
                                        Deadline Alerts
                                    </div>
                                    <span class="text-gray-500 text-xs block mt-1">Upcoming deadlines and time-sensitive tasks</span>
                                </label>
                            </div>
                            <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">Reminders</span>
                        </div>
                    </div>
                </div>

                <!-- Notification Frequency -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">Notification Frequency</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="email_frequency" class="block text-sm font-medium text-gray-700 mb-1">Email Frequency</label>
                            <select id="email_frequency" name="email_frequency"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                <option value="immediate" {% if preferences.email_frequency == 'immediate' %}selected{% endif %}>Immediate</option>
                                <option value="hourly" {% if preferences.email_frequency == 'hourly' %}selected{% endif %}>Hourly Digest</option>
                                <option value="daily" {% if preferences.email_frequency == 'daily' %}selected{% endif %}>Daily Digest</option>
                                <option value="weekly" {% if preferences.email_frequency == 'weekly' %}selected{% endif %}>Weekly Summary</option>
                            </select>
                        </div>

                        <div>
                            <label for="quiet_hours_start" class="block text-sm font-medium text-gray-700 mb-1">Quiet Hours</label>
                            <div class="flex space-x-2">
                                <input type="time" id="quiet_hours_start" name="quiet_hours_start"
                                       value="{{ preferences.quiet_hours_start|default:'22:00' }}"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                <span class="self-center text-gray-500">to</span>
                                <input type="time" id="quiet_hours_end" name="quiet_hours_end"
                                       value="{{ preferences.quiet_hours_end|default:'08:00' }}"
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            </div>
                            <p class="text-xs text-gray-500 mt-1">No notifications during these hours</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calendar Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Calendar Preferences</h3>
                <p class="text-sm text-gray-500 mt-1">Customize your calendar view and behavior</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="default_calendar_view" class="block text-sm font-medium text-gray-700 mb-1">Default Calendar View</label>
                        <select id="default_calendar_view" name="default_calendar_view"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="day" {% if preferences.default_calendar_view == 'day' %}selected{% endif %}>Day View</option>
                            <option value="week" {% if preferences.default_calendar_view == 'week' %}selected{% endif %}>Week View</option>
                            <option value="month" {% if preferences.default_calendar_view == 'month' %}selected{% endif %}>Month View</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="week_start_day" class="block text-sm font-medium text-gray-700 mb-1">Week Starts On</label>
                        <select id="week_start_day" name="week_start_day"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="0" {% if preferences.week_start_day == 0 %}selected{% endif %}>Sunday</option>
                            <option value="1" {% if preferences.week_start_day == 1 %}selected{% endif %}>Monday</option>
                        </select>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="show_weekends" name="show_weekends" 
                               {% if preferences.show_weekends %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="show_weekends" class="ml-2 block text-sm text-gray-900">
                            Show Weekends
                            <span class="text-gray-500 text-xs block">Display weekends in calendar views</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="auto_refresh_calendar" name="auto_refresh_calendar" 
                               {% if preferences.auto_refresh_calendar %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="auto_refresh_calendar" class="ml-2 block text-sm text-gray-900">
                            Auto-Refresh Calendar
                            <span class="text-gray-500 text-xs block">Automatically refresh calendar data</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Dashboard Preferences</h3>
                <p class="text-sm text-gray-500 mt-1">Customize your dashboard layout and widgets</p>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="show_quick_stats" name="show_quick_stats" 
                               {% if preferences.show_quick_stats %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="show_quick_stats" class="ml-2 block text-sm text-gray-900">
                            Show Quick Stats
                            <span class="text-gray-500 text-xs block">Display statistics cards on dashboard</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="show_recent_activity" name="show_recent_activity" 
                               {% if preferences.show_recent_activity %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="show_recent_activity" class="ml-2 block text-sm text-gray-900">
                            Show Recent Activity
                            <span class="text-gray-500 text-xs block">Display recent activity feed</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="show_upcoming_mentions" name="show_upcoming_mentions" 
                               {% if preferences.show_upcoming_mentions %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="show_upcoming_mentions" class="ml-2 block text-sm text-gray-900">
                            Show Upcoming Mentions
                            <span class="text-gray-500 text-xs block">Display upcoming mentions widget</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="show_pending_approvals" name="show_pending_approvals" 
                               {% if preferences.show_pending_approvals %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="show_pending_approvals" class="ml-2 block text-sm text-gray-900">
                            Show Pending Approvals
                            <span class="text-gray-500 text-xs block">Display pending approvals widget</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-3 pt-6">
            <a href="{% url 'settings:settings_dashboard' %}" 
               class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                Save Preferences
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testNotifications() {
    // Test browser notification
    if (window.notificationSystem) {
        window.notificationSystem.show('This is a test notification! 🔔', 'info', 3000);
    }

    // Test email notification (if enabled)
    const emailEnabled = document.getElementById('email_notifications').checked;
    if (emailEnabled) {
        fetch('/api/test-notification/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: 'test_email',
                message: 'This is a test email notification from RadioMention!'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (window.notificationSystem) {
                    window.notificationSystem.show('Test email sent successfully! Check your inbox.', 'success');
                }
            } else {
                if (window.notificationSystem) {
                    window.notificationSystem.show('Failed to send test email: ' + (data.error || 'Unknown error'), 'error');
                }
            }
        })
        .catch(error => {
            console.error('Error sending test email:', error);
            if (window.notificationSystem) {
                window.notificationSystem.show('Error sending test email', 'error');
            }
        });
    }

    // Request browser notification permission if not granted
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                new Notification('RadioMention Test', {
                    body: 'Browser notifications are now enabled!',
                    icon: '/static/images/logo.png'
                });
            }
        });
    } else if ('Notification' in window && Notification.permission === 'granted') {
        new Notification('RadioMention Test', {
            body: 'Browser notifications are working correctly!',
            icon: '/static/images/logo.png'
        });
    }
}

// Auto-save preferences on change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, select');

    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Show saving indicator
            if (window.notificationSystem) {
                window.notificationSystem.show('Saving preferences...', 'info', 1000);
            }

            // Auto-save after a short delay
            setTimeout(() => {
                const formData = new FormData(form);
                fetch(form.action || window.location.pathname, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    }
                })
                .then(response => {
                    if (response.ok) {
                        if (window.notificationSystem) {
                            window.notificationSystem.show('Preferences saved!', 'success', 2000);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error saving preferences:', error);
                });
            }, 500);
        });
    });
});
</script>
{% endblock %}
