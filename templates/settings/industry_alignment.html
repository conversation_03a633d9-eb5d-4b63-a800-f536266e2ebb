{% extends 'base.html' %}
{% load static %}

{% block title %}Live Show Alignment - Settings - {{ block.super }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{% url 'settings:settings_dashboard' %}" class="hover:text-blue-600">Settings</a>
            <i class="fas fa-chevron-right text-xs"></i>
            <span>Live Show Alignment</span>
        </div>
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Live Show Alignment</h1>
                <p class="text-gray-600 mt-1">Configure how mentions are displayed in the live show interface</p>
            </div>
            <a href="{% url 'settings:settings_dashboard' %}"
               class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Settings
            </a>
        </div>
    </div>

    <div class="max-w-5xl">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <!-- Header Section -->
            <div class="px-6 py-5 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-align-center text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h2 class="text-xl font-semibold text-gray-900">Mention Alignment Configuration</h2>
                        <p class="text-sm text-gray-600 mt-1">Choose how mentions appear in your live show interface for optimal presenter experience</p>
                    </div>
                </div>
            </div>

            <!-- Form Content -->
            <form method="post" class="p-6 space-y-8">
                {% csrf_token %}

                <!-- Alignment Mode Selection -->
                <div class="space-y-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-cog text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Alignment Mode</h3>
                            <p class="text-sm text-gray-600">Choose how mentions should be positioned in the live show interface</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Alternating Mode -->
                        <label class="relative cursor-pointer">
                            <input type="radio" name="mention_alignment_mode" value="alternating"
                                   {% if org_settings.mention_alignment_mode == 'alternating' %}checked{% endif %}
                                   class="sr-only peer">
                            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-exchange-alt text-white"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900">Alternating</div>
                                        <div class="text-sm text-gray-500">Right, left, right pattern</div>
                                    </div>
                                </div>
                                <div class="mt-3 text-xs text-gray-600">
                                    First mention right, second left, third right, etc.
                                </div>
                            </div>
                        </label>

                        <!-- Industry Based Mode -->
                        <label class="relative cursor-pointer">
                            <input type="radio" name="mention_alignment_mode" value="industry_based"
                                   {% if org_settings.mention_alignment_mode == 'industry_based' %}checked{% endif %}
                                   class="sr-only peer">
                            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-industry text-white"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900">Industry Based</div>
                                        <div class="text-sm text-gray-500">Custom per industry</div>
                                    </div>
                                </div>
                                <div class="mt-3 text-xs text-gray-600">
                                    Align based on client industry settings below
                                </div>
                            </div>
                        </label>

                        <!-- All Left Mode -->
                        <label class="relative cursor-pointer">
                            <input type="radio" name="mention_alignment_mode" value="all_left"
                                   {% if org_settings.mention_alignment_mode == 'all_left' %}checked{% endif %}
                                   class="sr-only peer">
                            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-align-left text-white"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900">All Left</div>
                                        <div class="text-sm text-gray-500">Consistent left alignment</div>
                                    </div>
                                </div>
                                <div class="mt-3 text-xs text-gray-600">
                                    All mentions aligned to the left side
                                </div>
                            </div>
                        </label>

                        <!-- All Right Mode -->
                        <label class="relative cursor-pointer">
                            <input type="radio" name="mention_alignment_mode" value="all_right"
                                   {% if org_settings.mention_alignment_mode == 'all_right' %}checked{% endif %}
                                   class="sr-only peer">
                            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-r from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-align-right text-white"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900">All Right</div>
                                        <div class="text-sm text-gray-500">Consistent right alignment</div>
                                    </div>
                                </div>
                                <div class="mt-3 text-xs text-gray-600">
                                    All mentions aligned to the right side
                                </div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Industry-Specific Settings -->
                <div id="industry-settings" class="space-y-6" style="display: {% if org_settings.mention_alignment_mode == 'industry_based' %}block{% else %}none{% endif %};">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-industry text-green-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Industry Alignment Settings</h3>
                            <p class="text-sm text-gray-600">Configure alignment for each industry type</p>
                        </div>
                    </div>

                    <div class="bg-gray-50 rounded-lg p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {% for field in form %}
                                {% if field.name|slice:":9" == "industry_" %}
                                <div class="bg-white rounded-lg p-4 border border-gray-200 hover:border-gray-300 transition-colors">
                                    <div class="flex items-center justify-between">
                                        <label class="text-sm font-medium text-gray-700 flex items-center">
                                            <i class="fas fa-tag text-gray-400 mr-2"></i>
                                            {{ field.label }}
                                        </label>
                                        <div class="ml-3">
                                            {{ field }}
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Preview Section -->
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-eye text-blue-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Live Preview</h3>
                            <p class="text-sm text-gray-600">See how mentions will appear in your live show interface</p>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-4 border border-gray-200">
                        <div class="space-y-3" id="alignment-preview">
                            <!-- Preview will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>
                        Changes will apply to new live show sessions
                    </div>
                    <div class="flex space-x-3">
                        <a href="{% url 'settings:settings_dashboard' %}"
                           class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                            Cancel
                        </a>
                        <button type="submit"
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                            <i class="fas fa-save mr-2"></i>
                            Save Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const alignmentModeInputs = document.querySelectorAll('input[name="mention_alignment_mode"]');
    const industrySettings = document.getElementById('industry-settings');
    const preview = document.getElementById('alignment-preview');
    
    // Show/hide industry settings based on mode
    alignmentModeInputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.value === 'industry_based') {
                industrySettings.style.display = 'block';
            } else {
                industrySettings.style.display = 'none';
            }
            updatePreview();
        });
    });
    
    // Update preview when industry settings change
    const industryInputs = document.querySelectorAll('select[name^="industry_"]');
    industryInputs.forEach(input => {
        input.addEventListener('change', updatePreview);
    });
    
    function updatePreview() {
        const mode = document.querySelector('input[name="mention_alignment_mode"]:checked').value;
        let previewHTML = '';

        if (mode === 'alternating') {
            previewHTML = `
                <div class="flex justify-end mb-3">
                    <div class="bg-gradient-to-r from-purple-100 to-purple-200 border-r-4 border-purple-500 p-3 rounded-l-lg max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">📡</span>
                            <div>
                                <div class="font-medium text-gray-900">TechCorp Solutions</div>
                                <div class="text-sm text-gray-600">Telecommunications - Right Aligned</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-start mb-3">
                    <div class="bg-gradient-to-r from-blue-100 to-blue-200 border-l-4 border-blue-500 p-3 rounded-r-lg max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">🏛️</span>
                            <div>
                                <div class="font-medium text-gray-900">City Council</div>
                                <div class="text-sm text-gray-600">Government - Left Aligned</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end">
                    <div class="bg-gradient-to-r from-purple-100 to-purple-200 border-r-4 border-purple-500 p-3 rounded-l-lg max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">🛍️</span>
                            <div>
                                <div class="font-medium text-gray-900">Metro Mall</div>
                                <div class="text-sm text-gray-600">Retail - Right Aligned</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else if (mode === 'all_left') {
            previewHTML = `
                <div class="flex justify-start mb-3">
                    <div class="bg-gradient-to-r from-blue-100 to-blue-200 border-l-4 border-blue-500 p-3 rounded-r-lg max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">📡</span>
                            <div>
                                <div class="font-medium text-gray-900">TechCorp Solutions</div>
                                <div class="text-sm text-gray-600">Telecommunications - Left Aligned</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-start mb-3">
                    <div class="bg-gradient-to-r from-blue-100 to-blue-200 border-l-4 border-blue-500 p-3 rounded-r-lg max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">🏛️</span>
                            <div>
                                <div class="font-medium text-gray-900">City Council</div>
                                <div class="text-sm text-gray-600">Government - Left Aligned</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-start">
                    <div class="bg-gradient-to-r from-blue-100 to-blue-200 border-l-4 border-blue-500 p-3 rounded-r-lg max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">🛍️</span>
                            <div>
                                <div class="font-medium text-gray-900">Metro Mall</div>
                                <div class="text-sm text-gray-600">Retail - Left Aligned</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else if (mode === 'all_right') {
            previewHTML = `
                <div class="flex justify-end mb-3">
                    <div class="bg-gradient-to-r from-purple-100 to-purple-200 border-r-4 border-purple-500 p-3 rounded-l-lg max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">📡</span>
                            <div>
                                <div class="font-medium text-gray-900">TechCorp Solutions</div>
                                <div class="text-sm text-gray-600">Telecommunications - Right Aligned</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end mb-3">
                    <div class="bg-gradient-to-r from-purple-100 to-purple-200 border-r-4 border-purple-500 p-3 rounded-l-lg max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">🏛️</span>
                            <div>
                                <div class="font-medium text-gray-900">City Council</div>
                                <div class="text-sm text-gray-600">Government - Right Aligned</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end">
                    <div class="bg-gradient-to-r from-purple-100 to-purple-200 border-r-4 border-purple-500 p-3 rounded-l-lg max-w-sm shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-lg">🛍️</span>
                            <div>
                                <div class="font-medium text-gray-900">Metro Mall</div>
                                <div class="text-sm text-gray-600">Retail - Right Aligned</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else if (mode === 'industry_based') {
            // Show based on industry settings
            previewHTML = `
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-industry text-gray-400 text-xl"></i>
                    </div>
                    <div class="text-sm text-gray-600 font-medium">Industry-Based Preview</div>
                    <div class="text-xs text-gray-500 mt-1">Configure industry settings above to see preview</div>
                </div>
            `;
        }

        preview.innerHTML = previewHTML;
    }
    
    // Initial preview
    updatePreview();
});
</script>
{% endblock %}
