{% extends 'base.html' %}
{% load static %}

{% block title %}System Settings{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="{% url 'settings:settings_dashboard' %}" class="text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fa-solid fa-arrow-left"></i>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">System Settings</h1>
                    <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <i class="fa-solid fa-shield-halved mr-1"></i>
                        Superuser Only
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- System Settings Form -->
    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Database & Performance Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Database & Performance</h3>
                <p class="text-sm text-gray-500 mt-1">Configure system performance and database settings</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="max_concurrent_users" class="block text-sm font-medium text-gray-700 mb-1">Max Concurrent Users</label>
                        <input type="number" id="max_concurrent_users" name="max_concurrent_users" 
                               value="{{ settings.max_concurrent_users }}" min="1" max="1000"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Maximum number of concurrent active users</p>
                    </div>
                    
                    <div>
                        <label for="session_timeout_minutes" class="block text-sm font-medium text-gray-700 mb-1">Session Timeout (minutes)</label>
                        <input type="number" id="session_timeout_minutes" name="session_timeout_minutes" 
                               value="{{ settings.session_timeout_minutes }}" min="5" max="1440"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">User session timeout in minutes</p>
                    </div>
                    
                    <div>
                        <label for="cache_timeout_seconds" class="block text-sm font-medium text-gray-700 mb-1">Cache Timeout (seconds)</label>
                        <input type="number" id="cache_timeout_seconds" name="cache_timeout_seconds" 
                               value="{{ settings.cache_timeout_seconds }}" min="60" max="86400"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Default cache timeout for system data</p>
                    </div>
                    
                    <div>
                        <label for="max_file_upload_size_mb" class="block text-sm font-medium text-gray-700 mb-1">Max File Upload Size (MB)</label>
                        <input type="number" id="max_file_upload_size_mb" name="max_file_upload_size_mb" 
                               value="{{ settings.max_file_upload_size_mb }}" min="1" max="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Maximum file upload size in megabytes</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Security Settings</h3>
                <p class="text-sm text-gray-500 mt-1">Configure system security and authentication</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="enforce_https" name="enforce_https" 
                               {% if settings.enforce_https %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enforce_https" class="ml-2 block text-sm text-gray-900">
                            Enforce HTTPS
                            <span class="text-gray-500 text-xs block">Redirect all HTTP traffic to HTTPS</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="enable_two_factor" name="enable_two_factor" 
                               {% if settings.enable_two_factor %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enable_two_factor" class="ml-2 block text-sm text-gray-900">
                            Enable Two-Factor Authentication
                            <span class="text-gray-500 text-xs block">Require 2FA for all users</span>
                        </label>
                    </div>
                    
                    <div>
                        <label for="password_min_length" class="block text-sm font-medium text-gray-700 mb-1">Minimum Password Length</label>
                        <input type="number" id="password_min_length" name="password_min_length" 
                               value="{{ settings.password_min_length }}" min="6" max="50"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Minimum required password length</p>
                    </div>
                    
                    <div>
                        <label for="max_login_attempts" class="block text-sm font-medium text-gray-700 mb-1">Max Login Attempts</label>
                        <input type="number" id="max_login_attempts" name="max_login_attempts" 
                               value="{{ settings.max_login_attempts }}" min="3" max="10"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Maximum failed login attempts before lockout</p>
                    </div>
                    
                    <div>
                        <label for="lockout_duration_minutes" class="block text-sm font-medium text-gray-700 mb-1">Lockout Duration (minutes)</label>
                        <input type="number" id="lockout_duration_minutes" name="lockout_duration_minutes" 
                               value="{{ settings.lockout_duration_minutes }}" min="5" max="1440"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Account lockout duration after failed attempts</p>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="require_password_complexity" name="require_password_complexity" 
                               {% if settings.require_password_complexity %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="require_password_complexity" class="ml-2 block text-sm text-gray-900">
                            Require Password Complexity
                            <span class="text-gray-500 text-xs block">Require uppercase, lowercase, numbers, and symbols</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logging & Monitoring Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Logging & Monitoring</h3>
                <p class="text-sm text-gray-500 mt-1">Configure system logging and monitoring</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="log_level" class="block text-sm font-medium text-gray-700 mb-1">Log Level</label>
                        <select id="log_level" name="log_level"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <option value="DEBUG" {% if settings.log_level == 'DEBUG' %}selected{% endif %}>DEBUG</option>
                            <option value="INFO" {% if settings.log_level == 'INFO' %}selected{% endif %}>INFO</option>
                            <option value="WARNING" {% if settings.log_level == 'WARNING' %}selected{% endif %}>WARNING</option>
                            <option value="ERROR" {% if settings.log_level == 'ERROR' %}selected{% endif %}>ERROR</option>
                            <option value="CRITICAL" {% if settings.log_level == 'CRITICAL' %}selected{% endif %}>CRITICAL</option>
                        </select>
                        <p class="text-xs text-gray-500 mt-1">Minimum log level to record</p>
                    </div>
                    
                    <div>
                        <label for="log_retention_days" class="block text-sm font-medium text-gray-700 mb-1">Log Retention (days)</label>
                        <input type="number" id="log_retention_days" name="log_retention_days" 
                               value="{{ settings.log_retention_days }}" min="7" max="365"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Days to retain log files</p>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="enable_audit_logging" name="enable_audit_logging" 
                               {% if settings.enable_audit_logging %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enable_audit_logging" class="ml-2 block text-sm text-gray-900">
                            Enable Audit Logging
                            <span class="text-gray-500 text-xs block">Log all user actions for audit trail</span>
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="enable_performance_monitoring" name="enable_performance_monitoring" 
                               {% if settings.enable_performance_monitoring %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enable_performance_monitoring" class="ml-2 block text-sm text-gray-900">
                            Enable Performance Monitoring
                            <span class="text-gray-500 text-xs block">Monitor system performance metrics</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email & Communication Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Email & Communication</h3>
                <p class="text-sm text-gray-500 mt-1">Configure system email and communication settings</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="smtp_host" class="block text-sm font-medium text-gray-700 mb-1">SMTP Host</label>
                        <input type="text" id="smtp_host" name="smtp_host" 
                               value="{{ settings.smtp_host }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">SMTP server hostname</p>
                    </div>
                    
                    <div>
                        <label for="smtp_port" class="block text-sm font-medium text-gray-700 mb-1">SMTP Port</label>
                        <input type="number" id="smtp_port" name="smtp_port" 
                               value="{{ settings.smtp_port }}" min="1" max="65535"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">SMTP server port</p>
                    </div>
                    
                    <div>
                        <label for="smtp_username" class="block text-sm font-medium text-gray-700 mb-1">SMTP Username</label>
                        <input type="text" id="smtp_username" name="smtp_username" 
                               value="{{ settings.smtp_username }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">SMTP authentication username</p>
                    </div>
                    
                    <div>
                        <label for="from_email" class="block text-sm font-medium text-gray-700 mb-1">From Email Address</label>
                        <input type="email" id="from_email" name="from_email" 
                               value="{{ settings.from_email }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Default sender email address</p>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="smtp_use_tls" name="smtp_use_tls" 
                               {% if settings.smtp_use_tls %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="smtp_use_tls" class="ml-2 block text-sm text-gray-900">
                            Use TLS
                            <span class="text-gray-500 text-xs block">Enable TLS encryption for SMTP</span>
                        </label>
                    </div>
                    
                    <div>
                        <label for="email_rate_limit_per_hour" class="block text-sm font-medium text-gray-700 mb-1">Email Rate Limit (per hour)</label>
                        <input type="number" id="email_rate_limit_per_hour" name="email_rate_limit_per_hour" 
                               value="{{ settings.email_rate_limit_per_hour }}" min="1" max="1000"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Maximum emails to send per hour</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup & Maintenance Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Backup & Maintenance</h3>
                <p class="text-sm text-gray-500 mt-1">Configure automated backup and maintenance</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex items-center">
                        <input type="checkbox" id="enable_auto_backup" name="enable_auto_backup" 
                               {% if settings.enable_auto_backup %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enable_auto_backup" class="ml-2 block text-sm text-gray-900">
                            Enable Auto Backup
                            <span class="text-gray-500 text-xs block">Automatically backup database</span>
                        </label>
                    </div>
                    
                    <div>
                        <label for="backup_frequency_hours" class="block text-sm font-medium text-gray-700 mb-1">Backup Frequency (hours)</label>
                        <input type="number" id="backup_frequency_hours" name="backup_frequency_hours" 
                               value="{{ settings.backup_frequency_hours }}" min="1" max="168"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Hours between automatic backups</p>
                    </div>
                    
                    <div>
                        <label for="backup_retention_days" class="block text-sm font-medium text-gray-700 mb-1">Backup Retention (days)</label>
                        <input type="number" id="backup_retention_days" name="backup_retention_days" 
                               value="{{ settings.backup_retention_days }}" min="7" max="365"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <p class="text-xs text-gray-500 mt-1">Days to retain backup files</p>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="enable_maintenance_mode" name="enable_maintenance_mode" 
                               {% if settings.enable_maintenance_mode %}checked{% endif %}
                               class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                        <label for="enable_maintenance_mode" class="ml-2 block text-sm text-gray-900">
                            Maintenance Mode
                            <span class="text-gray-500 text-xs block">Enable maintenance mode (blocks user access)</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-3 pt-6">
            <a href="{% url 'settings:settings_dashboard' %}" 
               class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                <i class="fa-solid fa-shield-halved mr-2"></i>
                Save System Settings
            </button>
        </div>
    </form>
</div>
{% endblock %}
