{% extends 'base.html' %}
{% load static %}

{% block title %}News Reader Settings{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{% url 'settings:settings_dashboard' %}" class="hover:text-primary-600">Settings</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <span class="text-gray-900">News Reader Settings</span>
        </nav>
        <h1 class="text-2xl font-bold text-gray-900">News Reader Settings</h1>
        <p class="text-gray-600">Configure article management and reading preferences for {{ organization.name }}.</p>
    </div>

    <form method="post" class="space-y-8">
        {% csrf_token %}

        <!-- Article Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i class="fa-solid fa-newspaper mr-2 text-blue-600"></i>
                Article Settings
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="default_article_length_minutes" class="block text-sm font-medium text-gray-700 mb-1">
                        Default Article Length (minutes)
                    </label>
                    <input type="number" name="default_article_length_minutes" id="default_article_length_minutes" 
                           value="{{ org_settings.default_article_length_minutes }}" min="1" max="30"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <p class="mt-1 text-sm text-gray-500">Default target length for new articles</p>
                </div>
                
                <div>
                    <label for="max_articles_per_session" class="block text-sm font-medium text-gray-700 mb-1">
                        Max Articles per Session
                    </label>
                    <input type="number" name="max_articles_per_session" id="max_articles_per_session" 
                           value="{{ org_settings.max_articles_per_session }}" min="1" max="50"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <p class="mt-1 text-sm text-gray-500">Maximum articles in a reading session</p>
                </div>
                
                <div>
                    <label for="auto_save_interval" class="block text-sm font-medium text-gray-700 mb-1">
                        Auto-save Interval (seconds)
                    </label>
                    <input type="number" name="auto_save_interval" id="auto_save_interval" 
                           value="{{ org_settings.auto_save_interval }}" min="10" max="300"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <p class="mt-1 text-sm text-gray-500">How often to auto-save article changes</p>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="require_article_approval" id="require_article_approval" 
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                           {% if org_settings.require_article_approval %}checked{% endif %}>
                    <label for="require_article_approval" class="ml-2 text-sm text-gray-700">
                        Require approval before publishing articles
                    </label>
                </div>
            </div>
        </div>

        <!-- Reading Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i class="fa-solid fa-microphone mr-2 text-red-600"></i>
                Reading Settings
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="default_reading_speed" class="block text-sm font-medium text-gray-700 mb-1">
                        Default Reading Speed
                    </label>
                    <div class="flex items-center space-x-3">
                        <span class="text-sm text-gray-600">Slow</span>
                        <input type="range" name="default_reading_speed" id="default_reading_speed" 
                               value="{{ org_settings.default_reading_speed }}" min="1" max="5" 
                               class="flex-1">
                        <span class="text-sm text-gray-600">Fast</span>
                    </div>
                    <div class="text-center mt-1">
                        <span class="text-sm text-gray-600" id="speed-value">{{ org_settings.default_reading_speed }}</span>
                    </div>
                </div>
                
                <div>
                    <label for="teleprompter_font_size" class="block text-sm font-medium text-gray-700 mb-1">
                        Teleprompter Font Size (px)
                    </label>
                    <input type="number" name="teleprompter_font_size" id="teleprompter_font_size" 
                           value="{{ org_settings.teleprompter_font_size }}" min="16" max="72"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <p class="mt-1 text-sm text-gray-500">Default font size for teleprompter mode</p>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="enable_teleprompter_mode" id="enable_teleprompter_mode" 
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                           {% if org_settings.enable_teleprompter_mode %}checked{% endif %}>
                    <label for="enable_teleprompter_mode" class="ml-2 text-sm text-gray-700">
                        Enable teleprompter mode
                    </label>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="enable_live_reading_mode" id="enable_live_reading_mode" 
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                           {% if org_settings.enable_live_reading_mode %}checked{% endif %}>
                    <label for="enable_live_reading_mode" class="ml-2 text-sm text-gray-700">
                        Enable live reading interface
                    </label>
                </div>
            </div>
        </div>

        <!-- Assignment Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i class="fa-solid fa-user-plus mr-2 text-green-600"></i>
                Assignment Settings
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="assignment_reminder_hours" class="block text-sm font-medium text-gray-700 mb-1">
                        Reminder Hours Before Due
                    </label>
                    <input type="number" name="assignment_reminder_hours" id="assignment_reminder_hours" 
                           value="{{ org_settings.assignment_reminder_hours }}" min="1" max="48"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <p class="mt-1 text-sm text-gray-500">Hours before due date to send reminders</p>
                </div>
                
                <div>
                    <label for="live_reading_timeout_minutes" class="block text-sm font-medium text-gray-700 mb-1">
                        Live Reading Timeout (minutes)
                    </label>
                    <input type="number" name="live_reading_timeout_minutes" id="live_reading_timeout_minutes" 
                           value="{{ org_settings.live_reading_timeout_minutes }}" min="5" max="120"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <p class="mt-1 text-sm text-gray-500">Session timeout for live reading</p>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="auto_assign_breaking_news" id="auto_assign_breaking_news" 
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                           {% if org_settings.auto_assign_breaking_news %}checked{% endif %}>
                    <label for="auto_assign_breaking_news" class="ml-2 text-sm text-gray-700">
                        Auto-assign breaking news to available readers
                    </label>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="enable_reading_analytics" id="enable_reading_analytics" 
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                           {% if org_settings.enable_reading_analytics %}checked{% endif %}>
                    <label for="enable_reading_analytics" class="ml-2 text-sm text-gray-700">
                        Enable reading performance analytics
                    </label>
                </div>
            </div>
        </div>

        <!-- User Preferences -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <i class="fa-solid fa-user-cog mr-2 text-purple-600"></i>
                Personal Preferences
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="default_article_view" class="block text-sm font-medium text-gray-700 mb-1">
                        Default Article View
                    </label>
                    <select name="default_article_view" id="default_article_view" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="list" {% if user_preferences.default_article_view == 'list' %}selected{% endif %}>List View</option>
                        <option value="grid" {% if user_preferences.default_article_view == 'grid' %}selected{% endif %}>Grid View</option>
                        <option value="compact" {% if user_preferences.default_article_view == 'compact' %}selected{% endif %}>Compact View</option>
                    </select>
                </div>
                
                <div>
                    <label for="articles_per_page" class="block text-sm font-medium text-gray-700 mb-1">
                        Articles per Page
                    </label>
                    <select name="articles_per_page" id="articles_per_page" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <option value="10" {% if user_preferences.articles_per_page == 10 %}selected{% endif %}>10</option>
                        <option value="20" {% if user_preferences.articles_per_page == 20 %}selected{% endif %}>20</option>
                        <option value="50" {% if user_preferences.articles_per_page == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if user_preferences.articles_per_page == 100 %}selected{% endif %}>100</option>
                    </select>
                </div>
                
                <div>
                    <label for="preferred_reading_speed" class="block text-sm font-medium text-gray-700 mb-1">
                        Preferred Reading Speed
                    </label>
                    <div class="flex items-center space-x-3">
                        <span class="text-sm text-gray-600">Slow</span>
                        <input type="range" name="preferred_reading_speed" id="preferred_reading_speed" 
                               value="{{ user_preferences.preferred_reading_speed }}" min="1" max="5" 
                               class="flex-1">
                        <span class="text-sm text-gray-600">Fast</span>
                    </div>
                    <div class="text-center mt-1">
                        <span class="text-sm text-gray-600" id="user-speed-value">{{ user_preferences.preferred_reading_speed }}</span>
                    </div>
                </div>
                
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" name="show_pronunciation_guide" id="show_pronunciation_guide" 
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                               {% if user_preferences.show_pronunciation_guide %}checked{% endif %}>
                        <label for="show_pronunciation_guide" class="ml-2 text-sm text-gray-700">
                            Show pronunciation guide in articles
                        </label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" name="enable_reading_notifications" id="enable_reading_notifications" 
                               class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                               {% if user_preferences.enable_reading_notifications %}checked{% endif %}>
                        <label for="enable_reading_notifications" class="ml-2 text-sm text-gray-700">
                            Enable reading assignment notifications
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between">
            <a href="{% url 'settings:settings_dashboard' %}" 
               class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-2 rounded-lg text-sm font-medium">
                Cancel
            </a>
            <button type="submit" 
                    class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg text-sm font-medium">
                <i class="fa-solid fa-save mr-2"></i>
                Save Settings
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update reading speed display
    const speedSlider = document.getElementById('default_reading_speed');
    const speedValue = document.getElementById('speed-value');
    
    speedSlider.addEventListener('input', function() {
        speedValue.textContent = this.value;
    });
    
    // Update user reading speed display
    const userSpeedSlider = document.getElementById('preferred_reading_speed');
    const userSpeedValue = document.getElementById('user-speed-value');
    
    userSpeedSlider.addEventListener('input', function() {
        userSpeedValue.textContent = this.value;
    });
});
</script>
{% endblock %}
