{% extends 'base.html' %}
{% load static %}
{% load time_filters %}

{% block title %}
  Settings Dashboard
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Settings Dashboard</h1>
            <p class="text-gray-600 mt-1">Manage your organization and personal preferences</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Organization Settings -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-building text-blue-600 text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Organization Settings</h3>
              <p class="text-sm text-gray-500">Configure organization-wide preferences</p>
            </div>
          </div>
          <div class="mt-4">
            <a href="{% url 'settings:organization_settings' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
              <i class="fa-solid fa-cog mr-2"></i>
              Manage Settings
            </a>
          </div>
        </div>
      </div>

      <!-- User Preferences -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-user-cog text-green-600 text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">User Preferences</h3>
              <p class="text-sm text-gray-500">Customize your personal experience</p>
            </div>
          </div>
          <div class="mt-4">
            <a href="{% url 'settings:user_preferences' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
              <i class="fa-solid fa-sliders-h mr-2"></i>
              Edit Preferences
            </a>
          </div>
        </div>
      </div>

      <!-- Activity Logs -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-history text-purple-600 text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Activity Logs</h3>
              <p class="text-sm text-gray-500">View system activity and audit trails</p>
            </div>
          </div>
          <div class="mt-4">
            <a href="{% url 'activity_logs:activity_logs' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
              <i class="fa-solid fa-list mr-2"></i>
              View Logs
            </a>
          </div>
        </div>
      </div>

      <!-- Conflict Detection -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Conflict Detection</h3>
              <p class="text-sm text-gray-500">Monitor and resolve scheduling conflicts</p>
            </div>
          </div>
          <div class="mt-4">
            <a href="{% url 'activity_logs:conflict_logs' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
              <i class="fa-solid fa-search mr-2"></i>
              View Conflicts
            </a>
          </div>
        </div>
      </div>

      <!-- Recurring Mentions -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-repeat text-yellow-600 text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Recurring Mentions</h3>
              <p class="text-sm text-gray-500">Manage automated mention scheduling</p>
            </div>
          </div>
          <div class="mt-4">
            <a href="{% url 'mentions:recurring_mentions' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700">
              <i class="fa-solid fa-calendar-alt mr-2"></i>
              Manage Recurring
            </a>
          </div>
        </div>
      </div>

      <!-- Industry Alignment Settings -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-industry text-indigo-600 text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Live Show Alignment</h3>
              <p class="text-sm text-gray-500">Configure mention alignment in live shows</p>
            </div>
          </div>
          <div class="mt-4">
            <a href="{% url 'settings:industry_alignment' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
              <i class="fa-solid fa-list mr-2"></i>
              Configure Alignment
            </a>
          </div>
        </div>
      </div>

      <!-- Industry Management -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-industry text-indigo-600 text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Industry Management</h3>
              <p class="text-sm text-gray-500">Manage client industry categories</p>
            </div>
          </div>
          <div class="mt-4">
            <a href="{% url 'core:industry_list' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
              <i class="fa-solid fa-list mr-2"></i>
              Manage Industries
            </a>
          </div>
        </div>
      </div>

      <!-- Weather API Settings -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-cloud-sun text-orange-600 text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Weather API</h3>
              <p class="text-sm text-gray-500">Configure OpenWeatherMap integration</p>
            </div>
          </div>
          <div class="mt-4 flex items-center justify-between">
            <div class="flex items-center">
              {% if api_settings.has_weather_api %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <i class="fa-solid fa-check-circle mr-1"></i>
                  Configured
                </span>
                {% if api_settings.weather_location %}
                  <span class="ml-2 text-sm text-gray-600">{{ api_settings.weather_location }}</span>
                {% endif %}
              {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                  Not Configured
                </span>
              {% endif %}
            </div>
            <a href="{% url 'settings:api_settings' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700">
              <i class="fa-solid fa-cog mr-2"></i>
              Configure API
            </a>
          </div>
        </div>
      </div>

      <!-- News Reader Settings -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div class="p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-microphone text-red-600 text-xl"></i>
              </div>
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">News Reader Settings</h3>
              <p class="text-sm text-gray-500">Configure article and reading preferences</p>
            </div>
          </div>
          <div class="mt-4">
            <a href="{% url 'settings:news_reader_settings' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
              <i class="fa-solid fa-newspaper mr-2"></i>
              Configure News Reading
            </a>
          </div>
        </div>
      </div>

      <!-- System Settings (Admin Only) -->
      {% if user.is_superuser %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
          <div class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  <i class="fa-solid fa-server text-gray-600 text-xl"></i>
                </div>
              </div>
              <div class="ml-4">
                <h3 class="text-lg font-medium text-gray-900">System Settings</h3>
                <p class="text-sm text-gray-500">Configure system-wide parameters</p>
              </div>
            </div>
            <div class="mt-4">
              <a href="{% url 'settings:system_settings' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700">
                <i class="fa-solid fa-tools mr-2"></i>
                System Config
              </a>
            </div>
          </div>
        </div>
      {% endif %}
    </div>

    <!-- Quick Stats -->
    <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Quick Overview</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">
              {% if org_settings.enable_conflict_detection %}
                <i class="fa-solid fa-check-circle"></i>
              {% else %}
                <i class="fa-solid fa-times-circle text-red-600"></i>
              {% endif %}
            </div>
            <div class="text-sm text-gray-600 mt-1">Conflict Detection</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">
              {% if org_settings.enable_email_notifications %}
                <i class="fa-solid fa-check-circle"></i>
              {% else %}
                <i class="fa-solid fa-times-circle text-red-600"></i>
              {% endif %}
            </div>
            <div class="text-sm text-gray-600 mt-1">Email Notifications</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ org_settings.max_mentions_per_hour }}</div>
            <div class="text-sm text-gray-600 mt-1">Max Mentions/Hour</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-yellow-600">{{ user_preferences.get_default_dashboard_view_display }}</div>
            <div class="text-sm text-gray-600 mt-1">Default View</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Time Format Demo -->
    <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Your Time Preferences</h3>
        <p class="text-sm text-gray-500 mt-1">Current time and date formatting based on your preferences</p>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-lg font-semibold text-blue-900">
              {% current_time_formatted %}
            </div>
            <div class="text-sm text-blue-600 mt-1">
              Current Time{% user_time_format as time_fmt %}({% if time_fmt == '12' %}
                12-hour
              {% else %}
                24-hour
              {% endif %}format)
            </div>
          </div>
          <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-lg font-semibold text-green-900">
              {% current_date_formatted %}
            </div>
            <div class="text-sm text-green-600 mt-1">
              Today's Date{% user_date_format as date_fmt %}({{ date_fmt }} format)
            </div>
          </div>
          <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-lg font-semibold text-purple-900">
              {% user_timezone %}
            </div>
            <div class="text-sm text-purple-600 mt-1">Your Timezone</div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
