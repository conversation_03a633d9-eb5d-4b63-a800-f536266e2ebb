{% extends 'base.html' %}
{% load static %}

{% block title %}API Settings{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center">
        <a href="{% url 'settings:settings_dashboard' %}" class="text-gray-500 hover:text-gray-700 mr-4">
          <i class="fa-solid fa-arrow-left"></i>
        </a>
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            <i class="fa-solid fa-plug text-blue-600"></i>
          </div>
          <div>
            <h1 class="text-xl font-semibold text-gray-900">API Settings</h1>
            <p class="text-sm text-gray-500">Configure external API integrations</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- API Settings Form -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <form method="post" id="apiSettingsForm">
      {% csrf_token %}
      
      <!-- OpenWeatherMap Section -->
      <div id="openweather-section" class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
            <i class="fa-solid fa-cloud-sun text-orange-600"></i>
          </div>
          <div>
            <h3 class="text-lg font-medium text-gray-900">OpenWeatherMap Integration</h3>
            <p class="text-sm text-gray-500">Display live weather data on your dashboard</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Enable Weather -->
          <div class="md:col-span-2">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div class="flex items-center">
                {{ form.openweather_enabled }}
                <label for="{{ form.openweather_enabled.id_for_label }}" class="ml-2 text-sm font-medium text-gray-900">
                  <i class="fa-solid fa-toggle-on text-blue-600 mr-1"></i>
                  Enable weather display
                </label>
                {% if form.openweather_enabled.value %}
                  <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    <i class="fa-solid fa-check mr-1"></i>
                    Enabled
                  </span>
                {% else %}
                  <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                    <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                    Disabled
                  </span>
                {% endif %}
              </div>
              <div class="mt-2">
                {% if form.openweather_enabled.help_text %}
                  <p class="text-xs text-blue-700">{{ form.openweather_enabled.help_text|safe }}</p>
                {% endif %}
                <p class="text-xs text-blue-600 font-medium">
                  <i class="fa-solid fa-info-circle mr-1"></i>
                  You must check this box to activate weather integration after entering your API key and location below.
                </p>
              </div>
              {% if form.openweather_enabled.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.openweather_enabled.errors.0 }}</p>
              {% endif %}
            </div>
          </div>

          <!-- API Key -->
          <div>
            <label for="{{ form.openweather_api_key.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
              API Key <span class="text-red-500">*</span>
            </label>
            {{ form.openweather_api_key }}
            {% if form.openweather_api_key.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.openweather_api_key.errors.0 }}</p>
            {% endif %}
            {% if form.openweather_api_key.help_text %}
              <p class="mt-1 text-xs text-gray-500">{{ form.openweather_api_key.help_text|safe }}</p>
            {% endif %}
          </div>

          <!-- Weather Location -->
          <div>
            <label for="{{ form.weather_location.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
              Location <span class="text-red-500">*</span>
            </label>
            {{ form.weather_location }}
            {% if form.weather_location.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.weather_location.errors.0 }}</p>
            {% endif %}
            {% if form.weather_location.help_text %}
              <p class="mt-1 text-xs text-gray-500">{{ form.weather_location.help_text|safe }}</p>
            {% endif %}
          </div>

          <!-- Weather Units -->
          <div>
            <label for="{{ form.weather_units.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
              Temperature Units
            </label>
            {{ form.weather_units }}
            {% if form.weather_units.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.weather_units.errors.0 }}</p>
            {% endif %}
          </div>

          <!-- Test API Button -->
          <div class="flex items-end">
            <button type="button" id="testWeatherBtn" class="px-4 py-2 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
              <i class="fa-solid fa-flask mr-2"></i>
              Test API
            </button>
          </div>
        </div>

        <!-- Test Results -->
        <div id="testResults" class="mt-4 hidden">
          <div id="testSuccess" class="hidden bg-green-50 border border-green-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fa-solid fa-check-circle text-green-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">API Test Successful!</h3>
                <div class="mt-2 text-sm text-green-700">
                  <div id="weatherPreview"></div>
                </div>
              </div>
            </div>
          </div>
          
          <div id="testError" class="hidden bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fa-solid fa-exclamation-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">API Test Failed</h3>
                <div class="mt-2 text-sm text-red-700">
                  <p id="errorMessage"></p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- API Instructions -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fa-solid fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">How to get your API key:</h3>
              <div class="mt-2 text-sm text-blue-700">
                <ol class="list-decimal list-inside space-y-1">
                  <li>Visit <a href="https://openweathermap.org/api" target="_blank" class="underline hover:text-blue-900">OpenWeatherMap.org</a></li>
                  <li>Sign up for a free account</li>
                  <li>Go to your API keys section</li>
                  <li>Copy your API key and paste it above</li>
                  <li><strong>Don't forget to check "Enable weather display" checkbox</strong></li>
                  <li>Note: It may take a few minutes for new API keys to become active</li>
                </ol>
              </div>
            </div>
          </div>
        </div>

        <!-- Debug Information (remove in production) -->
        {% if settings.DEBUG %}
        <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fa-solid fa-bug text-yellow-400"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">Debug Info:</h3>
              <div class="mt-2 text-xs text-yellow-700">
                <p><strong>Enabled:</strong> {{ settings.openweather_enabled }}</p>
                <p><strong>API Key:</strong> {% if settings.openweather_api_key %}Set ({{ settings.openweather_api_key|length }} chars){% else %}Not set{% endif %}</p>
                <p><strong>Location:</strong> {{ settings.weather_location|default:"Not set" }}</p>
                <p><strong>Has Weather API:</strong> {{ settings.has_weather_api }}</p>
              </div>
            </div>
          </div>
        </div>
        {% endif %}
      </div>

      <!-- Weather APIs Table Section -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            <i class="fa-solid fa-table text-blue-600"></i>
          </div>
          <div>
            <h3 class="text-lg font-medium text-gray-900">Weather APIs Overview</h3>
            <p class="text-sm text-gray-500">Current status of weather API integrations</p>
          </div>
        </div>

        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  API Provider
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Location
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Units
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Last Updated
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                      <div class="h-10 w-10 bg-orange-100 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-cloud-sun text-orange-600"></i>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">OpenWeatherMap</div>
                      <div class="text-sm text-gray-500">Weather data provider</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {% if settings.has_weather_api %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <i class="fa-solid fa-check-circle mr-1"></i>
                      Active
                    </span>
                  {% elif settings.openweather_api_key %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                      Incomplete
                    </span>
                  {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      <i class="fa-solid fa-times-circle mr-1"></i>
                      Not Configured
                    </span>
                  {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {% if settings.weather_location %}
                    {{ settings.weather_location }}
                  {% else %}
                    <span class="text-gray-400">Not set</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {% if settings.weather_units %}
                    <span class="capitalize">{{ settings.get_weather_units_display }}</span>
                  {% else %}
                    <span class="text-gray-400">Default</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {% if settings.updated_at %}
                    {{ settings.updated_at|date:"M d, Y H:i" }}
                  {% else %}
                    <span class="text-gray-400">Never</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  {% if settings.has_weather_api %}
                    <button type="button" id="testWeatherTableBtn" class="text-green-600 hover:text-green-900 mr-3">
                      <i class="fa-solid fa-flask mr-1"></i>
                      Test
                    </button>
                  {% endif %}
                  <a href="#openweather-section" class="text-blue-600 hover:text-blue-900">
                    <i class="fa-solid fa-edit mr-1"></i>
                    Configure
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Table Actions -->
        <div class="mt-4 flex items-center justify-between">
          <div class="text-sm text-gray-500">
            <i class="fa-solid fa-info-circle mr-1"></i>
            Configure API settings above to activate weather data integration
          </div>
          {% if settings.has_weather_api %}
            <div class="text-sm text-green-600">
              <i class="fa-solid fa-check-circle mr-1"></i>
              Weather API is ready to use
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Future Integrations Section -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
            <i class="fa-solid fa-plus text-gray-400"></i>
          </div>
          <div>
            <h3 class="text-lg font-medium text-gray-900">Future Integrations</h3>
            <p class="text-sm text-gray-500">More API integrations coming soon</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <i class="fa-brands fa-spotify text-green-600"></i>
            </div>
            <h4 class="text-sm font-medium text-gray-900">Spotify</h4>
            <p class="text-xs text-gray-500 mt-1">Music integration</p>
            <span class="inline-block mt-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Coming Soon</span>
          </div>

          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <i class="fa-brands fa-twitter text-blue-600"></i>
            </div>
            <h4 class="text-sm font-medium text-gray-900">Twitter</h4>
            <p class="text-xs text-gray-500 mt-1">Social media feeds</p>
            <span class="inline-block mt-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Coming Soon</span>
          </div>

          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <i class="fa-solid fa-rss text-red-600"></i>
            </div>
            <h4 class="text-sm font-medium text-gray-900">RSS Feeds</h4>
            <p class="text-xs text-gray-500 mt-1">News integration</p>
            <span class="inline-block mt-2 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Coming Soon</span>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="px-6 py-4">
        <div class="flex items-center justify-end space-x-3">
          <a href="{% url 'settings:settings_dashboard' %}" 
             class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            Cancel
          </a>
          <button type="submit" 
                  class="px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <i class="fa-solid fa-save mr-2"></i>
            Save Settings
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Function to select a suggested location
function selectLocation(location) {
  document.getElementById('{{ form.weather_location.id_for_label }}').value = location;
}

// Auto-suggest enabling weather when user starts configuring
document.addEventListener('DOMContentLoaded', function() {
  const enableCheckbox = document.getElementById('{{ form.openweather_enabled.id_for_label }}');
  const apiKeyField = document.getElementById('{{ form.openweather_api_key.id_for_label }}');
  const locationField = document.getElementById('{{ form.weather_location.id_for_label }}');

  function suggestEnable() {
    if (!enableCheckbox.checked && (apiKeyField.value.trim() || locationField.value.trim())) {
      // Show a subtle suggestion to enable weather
      const suggestion = document.createElement('div');
      suggestion.className = 'mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700';
      suggestion.innerHTML = '<i class="fa-solid fa-lightbulb mr-1"></i>Don\'t forget to check "Enable weather display" above to activate this integration!';

      // Remove any existing suggestions
      const existingSuggestion = document.querySelector('.weather-enable-suggestion');
      if (existingSuggestion) {
        existingSuggestion.remove();
      }

      suggestion.classList.add('weather-enable-suggestion');
      apiKeyField.parentElement.appendChild(suggestion);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (suggestion.parentElement) {
          suggestion.remove();
        }
      }, 5000);
    }
  }

  if (apiKeyField && locationField) {
    apiKeyField.addEventListener('input', suggestEnable);
    locationField.addEventListener('input', suggestEnable);
  }
});

document.getElementById('testWeatherBtn').addEventListener('click', function() {
  const apiKey = document.getElementById('{{ form.openweather_api_key.id_for_label }}').value;
  const location = document.getElementById('{{ form.weather_location.id_for_label }}').value;
  
  if (!apiKey || !location) {
    alert('Please enter both API key and location before testing.');
    return;
  }
  
  const btn = this;
  const originalText = btn.innerHTML;
  btn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Testing...';
  btn.disabled = true;
  
  // Hide previous results
  document.getElementById('testResults').classList.add('hidden');
  document.getElementById('testSuccess').classList.add('hidden');
  document.getElementById('testError').classList.add('hidden');
  
  fetch('{% url "settings:test_weather_api" %}', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
    },
    body: `api_key=${encodeURIComponent(apiKey)}&location=${encodeURIComponent(location)}`
  })
  .then(response => response.json())
  .then(data => {
    document.getElementById('testResults').classList.remove('hidden');
    
    if (data.success) {
      document.getElementById('testSuccess').classList.remove('hidden');
      document.getElementById('weatherPreview').innerHTML = `
        <div class="flex items-center">
          <img src="https://openweathermap.org/img/w/${data.weather.icon}.png" alt="Weather" class="w-8 h-8 mr-2">
          <div>
            <strong>${data.weather.location}, ${data.weather.country}</strong><br>
            ${data.weather.temperature}°F - ${data.weather.description}
          </div>
        </div>
      `;
    } else {
      document.getElementById('testError').classList.remove('hidden');
      document.getElementById('errorMessage').textContent = data.error;
    }
  })
  .catch(error => {
    document.getElementById('testResults').classList.remove('hidden');
    document.getElementById('testError').classList.remove('hidden');
    document.getElementById('errorMessage').textContent = 'Network error: ' + error.message;
  })
  .finally(() => {
    btn.innerHTML = originalText;
    btn.disabled = false;
  });
});

// Test button functionality for the table
document.addEventListener('DOMContentLoaded', function() {
  const testTableBtn = document.getElementById('testWeatherTableBtn');
  if (testTableBtn) {
    testTableBtn.addEventListener('click', function() {
      const apiKey = document.getElementById('{{ form.openweather_api_key.id_for_label }}').value;
      const location = document.getElementById('{{ form.weather_location.id_for_label }}').value;

      if (!apiKey || !location) {
        alert('Please configure API key and location first by scrolling up to the OpenWeatherMap section.');
        // Scroll to the OpenWeatherMap section
        document.querySelector('#openweather-section, .px-6.py-4.border-b.border-gray-200').scrollIntoView({
          behavior: 'smooth'
        });
        return;
      }

      const btn = this;
      const originalText = btn.innerHTML;
      btn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-1"></i>Testing...';
      btn.disabled = true;

      fetch('{% url "settings:test_weather_api" %}', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `api_key=${encodeURIComponent(apiKey)}&location=${encodeURIComponent(location)}`
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success message with weather data
          const message = `✅ API Test Successful!\n\nLocation: ${data.weather.location}, ${data.weather.country}\nTemperature: ${data.weather.temperature}°F\nCondition: ${data.weather.description}`;
          alert(message);
        } else {
          alert('❌ API Test Failed: ' + data.error);
        }
      })
      .catch(error => {
        alert('❌ Network Error: ' + error.message);
      })
      .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
      });
    });
  }
});
</script>
{% endblock %}
