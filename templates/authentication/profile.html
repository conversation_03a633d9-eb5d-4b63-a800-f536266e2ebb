{% extends 'base.html' %}
{% load static %}

{% block title %}User Profile{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h1 class="text-xl font-semibold text-gray-900">User Profile</h1>
            <p class="text-sm text-gray-600 mt-1">Manage your account settings and preferences</p>
        </div>
        
        <!-- Profile Info -->
        <div class="p-6">
            <div class="flex items-start space-x-6">
                <div class="flex-shrink-0">
                    <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-user text-gray-400 text-3xl"></i>
                    </div>
                </div>
                <div class="flex-1">
                    <h2 class="text-2xl font-bold text-gray-900">{{ user.get_full_name|default:user.username }}</h2>
                    <p class="text-lg text-gray-600 mt-1">{{ user.email }}</p>
                    <div class="flex items-center mt-3 space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if user.is_superuser %}bg-red-100 text-red-800{% elif user.is_staff %}bg-blue-100 text-blue-800{% else %}bg-green-100 text-green-800{% endif %}">
                            {% if user.is_superuser %}
                                Administrator
                            {% elif user.is_staff %}
                                Staff Member
                            {% else %}
                                User
                            {% endif %}
                        </span>
                        <span class="text-sm text-gray-500">
                            <i class="fa-solid fa-calendar mr-1"></i>
                            Joined {{ user.date_joined|date:"M d, Y" }}
                        </span>
                    </div>
                </div>
                <div class="text-right">
                    <button onclick="editProfile()" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-edit mr-1"></i>
                        Edit Profile
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Account Information</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                    <p class="text-gray-900">{{ user.username }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <p class="text-gray-900">{{ user.email }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                    <p class="text-gray-900">{{ user.first_name|default:"Not set" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                    <p class="text-gray-900">{{ user.last_name|default:"Not set" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Last Login</label>
                    <p class="text-gray-900">{{ user.last_login|date:"M d, Y g:i A"|default:"Never" }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Account Status</label>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if user.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {% if user.is_active %}Active{% else %}Inactive{% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Summary -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Activity Summary</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fa-solid fa-bullhorn text-blue-600"></i>
                    </div>
                    <p class="text-2xl font-bold text-gray-900">{{ user_stats.mentions_created|default:0 }}</p>
                    <p class="text-sm text-gray-500">Mentions Created</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fa-solid fa-check-circle text-green-600"></i>
                    </div>
                    <p class="text-2xl font-bold text-gray-900">{{ user_stats.mentions_approved|default:0 }}</p>
                    <p class="text-sm text-gray-500">Mentions Approved</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fa-solid fa-building text-purple-600"></i>
                    </div>
                    <p class="text-2xl font-bold text-gray-900">{{ user_stats.clients_managed|default:0 }}</p>
                    <p class="text-sm text-gray-500">Clients Managed</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fa-solid fa-calendar text-orange-600"></i>
                    </div>
                    <p class="text-2xl font-bold text-gray-900">{{ user_stats.days_active|default:0 }}</p>
                    <p class="text-sm text-gray-500">Days Active</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Settings -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Security Settings</h3>
        </div>
        <div class="p-6 space-y-4">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Password</h4>
                    <p class="text-sm text-gray-500">Last changed {{ user.password_changed|default:"Never" }}</p>
                </div>
                <button onclick="changePassword()" class="bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-200">
                    Change Password
                </button>
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
                    <p class="text-sm text-gray-500">Add an extra layer of security to your account</p>
                </div>
                <button onclick="setup2FA()" class="bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-200">
                    Setup 2FA
                </button>
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Login Sessions</h4>
                    <p class="text-sm text-gray-500">Manage your active login sessions</p>
                </div>
                <button onclick="manageSessions()" class="bg-gray-100 text-gray-700 px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-200">
                    Manage Sessions
                </button>
            </div>
        </div>
    </div>

    <!-- Preferences -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Preferences</h3>
        </div>
        <div class="p-6 space-y-4">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Email Notifications</h4>
                    <p class="text-sm text-gray-500">Receive email notifications for important updates</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Desktop Notifications</h4>
                    <p class="text-sm text-gray-500">Show browser notifications for new mentions</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-sm font-medium text-gray-900">Dark Mode</h4>
                    <p class="text-sm text-gray-500">Use dark theme for the interface</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                {% for activity in recent_activities|default:""|slice:":5" %}
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-bullhorn text-blue-600 text-xs"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">{{ activity.description }}</p>
                        <p class="text-xs text-gray-500">{{ activity.timestamp }}</p>
                    </div>
                </div>
                {% empty %}
                <!-- Sample data -->
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-check text-green-600 text-xs"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">Approved mention "Product Launch"</p>
                        <p class="text-xs text-gray-500">2 hours ago</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-plus text-blue-600 text-xs"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">Created new client "Tech Corp"</p>
                        <p class="text-xs text-gray-500">1 day ago</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-edit text-purple-600 text-xs"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">Updated profile information</p>
                        <p class="text-xs text-gray-500">3 days ago</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editProfile() {
    alert('Profile editing functionality would be implemented here');
}

function changePassword() {
    alert('Password change functionality would be implemented here');
}

function setup2FA() {
    alert('Two-factor authentication setup would be implemented here');
}

function manageSessions() {
    alert('Session management functionality would be implemented here');
}
</script>
{% endblock %}
