{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<style>
  /* Custom toggle switch styles */
  .toggle-switch {
    background-color: #e5e7eb;
    border: 2px solid transparent;
  }

  .permission-toggle:checked + .toggle-switch {
    background-color: #3b82f6;
  }

  .permission-toggle:checked + .toggle-switch .toggle-dot {
    transform: translateX(20px);
  }

  .permission-toggle:disabled + .toggle-switch {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .toggle-dot {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* Smooth transitions for status updates */
  .permission-status {
    transition: all 0.3s ease;
  }

  /* Loading state */
  .permission-toggle:disabled + .toggle-switch .toggle-dot {
    background-color: #f3f4f6;
  }
</style>
{% endblock %}

{% block title %}Manage User Permissions - {{ user.get_full_name|default:user.username }}{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">User Permissions</h1>
          <p class="mt-2 text-gray-600">
            Manage permissions for {{ user.get_full_name|default:user.username }} in {{ current_organization.name }}
          </p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'authentication:user_detail' user.pk %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 flex items-center">
            <i class="fa-solid fa-arrow-left mr-2"></i>
            Back to User
          </a>
          <a href="{% url 'authentication:user_edit' user.pk %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 flex items-center">
            <i class="fa-solid fa-edit mr-2"></i>
            Edit User
          </a>
        </div>
      </div>
    </div>

    <!-- User Info Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="p-6">
        <div class="flex items-center">
          <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4">
            <i class="fa-solid fa-user text-gray-400 text-2xl"></i>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900">{{ user.get_full_name|default:user.username }}</h3>
            <p class="text-gray-500">{{ user.email }}</p>
            <div class="flex items-center mt-2">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                {% if user_membership.role == 'owner' %}bg-purple-100 text-purple-800
                {% elif user_membership.role == 'admin' %}bg-red-100 text-red-800
                {% elif user_membership.role == 'manager' %}bg-blue-100 text-blue-800
                {% elif user_membership.role == 'editor' %}bg-yellow-100 text-yellow-800
                {% elif user_membership.role == 'presenter' %}bg-orange-100 text-orange-800
                {% else %}bg-green-100 text-green-800{% endif %}">
                {{ user_membership.get_role_display }}
              </span>
              <span class="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                {% if user.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                {% if user.is_active %}Active{% else %}Inactive{% endif %}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Role Management -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Role Management</h3>
            
            <form method="post" class="space-y-4">
              {% csrf_token %}
              
              <div>
                <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Organization Role</label>
                <select id="role" name="role" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                  {% for role_code, role_name in available_roles %}
                    <option value="{{ role_code }}" {% if role_code == user_membership.role %}selected{% endif %}>
                      {{ role_name }}
                    </option>
                  {% endfor %}
                </select>
                <p class="mt-1 text-xs text-gray-500">Changing the role will automatically update permissions</p>
              </div>

              <button type="submit" class="w-full px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                Update Role
              </button>
            </form>

            <!-- Role Description -->
            <div class="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 class="text-sm font-medium text-gray-900 mb-2">Current Role: {{ user_membership.get_role_display }}</h4>
              <div id="role-description" class="text-sm text-gray-600">
                <!-- Role descriptions will be populated by JavaScript -->
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
          <div class="p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="space-y-3">
              <button class="w-full px-4 py-2 text-left border border-gray-300 rounded-md hover:bg-gray-50 flex items-center justify-between">
                <span class="flex items-center">
                  <i class="fa-solid fa-copy mr-2 text-gray-400"></i>
                  Copy Permissions From...
                </span>
                <i class="fa-solid fa-chevron-right text-gray-400"></i>
              </button>
              <button class="w-full px-4 py-2 text-left border border-gray-300 rounded-md hover:bg-gray-50 flex items-center justify-between">
                <span class="flex items-center">
                  <i class="fa-solid fa-download mr-2 text-gray-400"></i>
                  Export Permissions
                </span>
                <i class="fa-solid fa-chevron-right text-gray-400"></i>
              </button>
              <button class="w-full px-4 py-2 text-left border border-gray-300 rounded-md hover:bg-gray-50 flex items-center justify-between">
                <span class="flex items-center">
                  <i class="fa-solid fa-history mr-2 text-gray-400"></i>
                  Permission History
                </span>
                <i class="fa-solid fa-chevron-right text-gray-400"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Permissions Grid -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-lg font-medium text-gray-900">Detailed Permissions</h3>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">{{ current_permissions|length }} permissions granted</span>
                <button class="px-3 py-1 text-xs border border-gray-300 rounded-md hover:bg-gray-50">
                  Expand All
                </button>
              </div>
            </div>

            <!-- Permission Categories -->
            <div class="space-y-6">
              {% for category, permissions in permission_categories.items %}
                <div class="border border-gray-200 rounded-lg">
                  <div class="p-4 bg-gray-50 border-b border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900 flex items-center justify-between">
                      <span class="flex items-center">
                        <i class="fa-solid fa-folder mr-2 text-gray-400"></i>
                        {{ category }}
                      </span>
                      <span class="text-xs text-gray-500">
                        {% with granted=0 total=permissions|length %}
                          {% for perm_key, perm_desc in permissions.items %}
                            {% if perm_key in current_permissions %}
                              {% with granted=granted|add:1 %}{% endwith %}
                            {% endif %}
                          {% endfor %}
                          {{ granted }}/{{ total }} granted
                        {% endwith %}
                      </span>
                    </h4>
                  </div>
                  <div class="p-4">
                    <div class="grid grid-cols-1 gap-3">
                      {% for perm_key, perm_desc in permissions.items %}
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                          <div class="flex items-center">
                            <div class="w-4 h-4 mr-3">
                              {% if perm_key in current_permissions %}
                                <i class="fa-solid fa-check-circle text-green-500"></i>
                              {% else %}
                                <i class="fa-solid fa-times-circle text-red-500"></i>
                              {% endif %}
                            </div>
                            <div>
                              <div class="text-sm font-medium text-gray-900">{{ perm_desc }}</div>
                              <div class="text-xs text-gray-500 flex items-center space-x-2">
                                <span>{{ perm_key }}</span>
                                {% if perm_key in permission_overrides %}
                                  <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                    Override
                                  </span>
                                {% endif %}
                                {% if perm_key in role_based_permissions %}
                                  <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                    Role
                                  </span>
                                {% endif %}
                              </div>
                            </div>
                          </div>
                          <div class="flex items-center space-x-3">
                            <!-- Permission Toggle Switch -->
                            <label class="relative inline-flex items-center cursor-pointer">
                              <input
                                type="checkbox"
                                class="permission-toggle"
                                data-permission="{{ perm_key }}"
                                data-user-id="{{ user.id }}"
                                {% if perm_key in current_permissions %}checked{% endif %}
                                style="display: none;"
                              >
                              <div class="toggle-switch w-11 h-6 bg-gray-200 rounded-full relative transition-colors duration-300 ease-in-out cursor-pointer">
                                <div class="toggle-dot absolute top-0.5 left-0.5 bg-white w-5 h-5 rounded-full transition-transform duration-300 ease-in-out"></div>
                              </div>
                            </label>
                            <!-- Status Badge -->
                            <div class="permission-status">
                              {% if perm_key in current_permissions %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Granted
                                </span>
                              {% else %}
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                  Denied
                                </span>
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Role descriptions
    const roleDescriptions = {
      'owner': 'Full control over the organization including billing, settings, and all user management.',
      'admin': 'Administrative access to manage users, settings, and all content within the organization.',
      'manager': 'Can approve mentions, manage schedules, and oversee content operations.',
      'editor': 'Can create and edit mentions, manage content, but cannot approve or manage users.',
      'presenter': 'Can mark mentions during shows, manage own shows, and access presenter dashboard.',
      'news_reader': 'Can access news reader dashboard, manage reading notes, and use live reading tools.',
      'viewer': 'Read-only access to view content and reports.'
    };

    // Update role description when role changes
    document.getElementById('role').addEventListener('change', function() {
      const selectedRole = this.value;
      const description = roleDescriptions[selectedRole] || 'No description available.';
      document.getElementById('role-description').textContent = description;
    });

    // Set initial description
    const currentRole = document.getElementById('role').value;
    document.getElementById('role-description').textContent = roleDescriptions[currentRole] || 'No description available.';

    // Permission toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
      const toggles = document.querySelectorAll('.permission-toggle');

      toggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
          const permission = this.dataset.permission;
          const userId = this.dataset.userId;
          const enabled = this.checked;

          // Show loading state
          const statusElement = this.closest('.flex').querySelector('.permission-status');
          const originalStatus = statusElement.innerHTML;
          statusElement.innerHTML = '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Updating...</span>';

          // Disable toggle during request
          this.disabled = true;

          // Make AJAX request
          fetch(`/users/${userId}/toggle-permission/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
              permission: permission,
              enabled: enabled
            })
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              // Update status badge
              if (data.enabled) {
                statusElement.innerHTML = '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Granted</span>';
              } else {
                statusElement.innerHTML = '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Denied</span>';
              }

              // Update icon
              const iconElement = this.closest('.flex').querySelector('.w-4.h-4 i');
              if (data.enabled) {
                iconElement.className = 'fa-solid fa-check-circle text-green-500';
              } else {
                iconElement.className = 'fa-solid fa-times-circle text-red-500';
              }

              // Show success message
              showNotification('Permission updated successfully', 'success');

              // Update override badge if needed
              const overrideBadge = this.closest('.flex').querySelector('.text-xs .bg-blue-100');
              if (data.override_exists && !overrideBadge) {
                // Add override badge
                const badgeContainer = this.closest('.flex').querySelector('.text-xs');
                const newBadge = document.createElement('span');
                newBadge.className = 'inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
                newBadge.textContent = 'Override';
                badgeContainer.appendChild(newBadge);
              } else if (!data.override_exists && overrideBadge) {
                // Remove override badge
                overrideBadge.remove();
              }

            } else {
              // Revert toggle state
              this.checked = !enabled;
              statusElement.innerHTML = originalStatus;
              showNotification(data.error || 'Failed to update permission', 'error');
            }
          })
          .catch(error => {
            console.error('Error:', error);
            // Revert toggle state
            this.checked = !enabled;
            statusElement.innerHTML = originalStatus;
            showNotification('Network error occurred', 'error');
          })
          .finally(() => {
            // Re-enable toggle
            this.disabled = false;
          });
        });
      });
    });

    // Notification function
    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
      notification.textContent = message;
      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Expand/Collapse functionality
    const expandButton = document.querySelector('button');
    if (expandButton && expandButton.textContent.includes('Expand All')) {
      expandButton.addEventListener('click', function() {
        // Toggle expand/collapse logic here
      });
    }
  </script>
{% endblock %}
