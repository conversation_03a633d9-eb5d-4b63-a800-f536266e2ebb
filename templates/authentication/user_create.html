{% extends 'base.html' %}
{% load static %}

{% block title %}
  Create New User
{% endblock %}

{% block content %}
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <a href="{% url 'authentication:user_manager' %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
            <div>
              <h1 class="text-xl font-semibold text-gray-900">Create New User</h1>
              <p class="text-sm text-gray-500 mt-1">
                Add a new user to{% if current_organization %}
                  <span class="font-medium text-primary-600">{{ current_organization.name }}</span>
                {% else %}
                  the system
                {% endif %}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <form method="post" class="space-y-6">
      {% csrf_token %}

      <!-- Basic Information -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
          <p class="text-sm text-gray-500 mt-1">Enter the user's basic details</p>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Username *</label>
              <input type="text" id="username" name="username" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" />
              <p class="text-xs text-gray-500 mt-1">Unique username for login</p>
            </div>
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
              <input type="email" id="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" />
              <p class="text-xs text-gray-500 mt-1">User's email address</p>
            </div>
            <div>
              <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
              <input type="text" id="first_name" name="first_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" />
            </div>
            <div>
              <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
              <input type="text" id="last_name" name="last_name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" />
            </div>
          </div>
        </div>
      </div>

      <!-- Security -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Security</h3>
          <p class="text-sm text-gray-500 mt-1">Set password and permissions</p>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password *</label>
              <div class="relative">
                <input type="password" id="password" name="password" required class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" />
                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePasswordVisibility()"><i class="fa-solid fa-eye text-gray-400 hover:text-gray-600" id="password-toggle"></i></button>
              </div>
              <div class="mt-2">
                <div class="text-xs text-gray-500 mb-1">Password requirements:</div>
                <div class="grid grid-cols-2 gap-2 text-xs">
                  <div id="length-check" class="flex items-center text-gray-400">
                    <i class="fa-solid fa-circle text-xs mr-1"></i>
                    <span>At least 8 characters</span>
                  </div>
                  <div id="uppercase-check" class="flex items-center text-gray-400">
                    <i class="fa-solid fa-circle text-xs mr-1"></i>
                    <span>One uppercase letter</span>
                  </div>
                  <div id="lowercase-check" class="flex items-center text-gray-400">
                    <i class="fa-solid fa-circle text-xs mr-1"></i>
                    <span>One lowercase letter</span>
                  </div>
                  <div id="number-check" class="flex items-center text-gray-400">
                    <i class="fa-solid fa-circle text-xs mr-1"></i>
                    <span>One number</span>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Organization Role *</label>
              <select id="role" name="role" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                <option value="viewer">Viewer</option>
                <option value="editor">Editor</option>
                <option value="presenter">Presenter</option>
                <option value="news_reader">News Reader</option>
                <option value="manager">Manager</option>
                <option value="admin">Admin</option>
                <option value="owner">Owner</option>
              </select>
              <p class="text-xs text-gray-500 mt-1">User's role within the organization</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Organization Assignment -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Organization Assignment</h3>
          <p class="text-sm text-gray-500 mt-1">User will be added to the current organization</p>
        </div>
        <div class="p-6">
          {% if current_organization %}
            <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div class="flex items-center">
                <i class="fa-solid fa-building text-blue-600 mr-3"></i>
                <div>
                  <h4 class="text-sm font-medium text-blue-900">{{ current_organization.name }}</h4>
                  <p class="text-sm text-blue-800">
                    {% if current_organization.description %}
                      {{ current_organization.description }}
                    {% else %}
                      This user will be added to {{ current_organization.name }}
                    {% endif %}
                  </p>
                  <p class="text-xs text-blue-700 mt-1">Organization Type: {{ current_organization.get_organization_type_display }}</p>
                </div>
              </div>
            </div>
          {% else %}
            <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div class="flex items-center">
                <i class="fa-solid fa-exclamation-triangle text-red-600 mr-2"></i>
                <div>
                  <h4 class="text-sm font-medium text-red-900">No Organization Context</h4>
                  <p class="text-sm text-red-800">Please select an organization first before creating users.</p>
                  <a href="{% url 'organizations:list' %}" class="text-red-900 underline hover:text-red-700">Select Organization</a>
                </div>
              </div>
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Professional Information -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Professional Information</h3>
          <p class="text-sm text-gray-500 mt-1">Optional professional details</p>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="job_title" class="block text-sm font-medium text-gray-700 mb-1">Job Title</label>
              <input type="text" id="job_title" name="job_title" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" />
            </div>
            <div>
              <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department</label>
              <input type="text" id="department" name="department" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" />
            </div>
          </div>
        </div>
      </div>

      <!-- Role Permissions Preview -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Role Permissions</h3>
          <p class="text-sm text-gray-500 mt-1">Preview of permissions for selected role</p>
        </div>
        <div class="p-6">
          <div id="role-permissions">
            <!-- User permissions -->
            <div id="user-permissions" class="role-permissions">
              <h4 class="text-sm font-medium text-gray-900 mb-3">User Permissions</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View own mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Create mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Edit own profile</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-times text-red-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage other users</span>
                </div>
              </div>
            </div>

            <!-- Staff permissions -->
            <div id="staff-permissions" class="role-permissions hidden">
              <h4 class="text-sm font-medium text-gray-900 mb-3">Staff Permissions</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">All user permissions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Approve mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View reports</span>
                </div>
              </div>
            </div>

            <!-- Presenter permissions -->
            <div id="presenter-permissions" class="role-permissions hidden">
              <h4 class="text-sm font-medium text-gray-900 mb-3">Presenter Permissions</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Mark mentions as read</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Access presenter dashboard</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View show schedule</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage own shows</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-times text-red-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Approve mentions</span>
                </div>
              </div>
            </div>

            <!-- News Reader permissions -->
            <div id="news_reader-permissions" class="role-permissions hidden">
              <h4 class="text-sm font-medium text-gray-900 mb-3">News Reader Permissions</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View news reader dashboard</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage reading notes</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Update reading status</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View assigned mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Access live reading tools</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View schedule</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-times text-red-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage users</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-times text-red-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Approve mentions</span>
                </div>
              </div>
            </div>

            <!-- Admin permissions -->
            <div id="admin-permissions" class="role-permissions hidden">
              <h4 class="text-sm font-medium text-gray-900 mb-3">Administrator Permissions</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">All staff permissions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage users</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">System settings</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Full system access</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-end space-x-3 pt-6">
        <a href="{% url 'authentication:user_manager' %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">Cancel</a>
        <button type="submit" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">Create User</button>
      </div>
    </form>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
// Current organization data for JavaScript
const currentOrganization = {% if current_organization %}{
    name: "{{ current_organization.name|escapejs }}",
    description: "{{ current_organization.description|escapejs }}",
    type: "{{ current_organization.organization_type|escapejs }}"
}{% else %}null{% endif %};

document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const permissionDivs = document.querySelectorAll('.role-permissions');

    // Hide all permission divs
    permissionDivs.forEach(div => div.classList.add('hidden'));

    // Show the selected role's permissions
    if (role) {
        const permissionDiv = document.getElementById(role + '-permissions');
        if (permissionDiv) {
            permissionDiv.classList.remove('hidden');
        }
    }
});

// Password visibility toggle
function togglePasswordVisibility() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('password-toggle');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Password strength validation
function validatePassword(password) {
    const checks = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /[0-9]/.test(password)
    };

    // Update visual indicators
    updatePasswordCheck('length-check', checks.length);
    updatePasswordCheck('uppercase-check', checks.uppercase);
    updatePasswordCheck('lowercase-check', checks.lowercase);
    updatePasswordCheck('number-check', checks.number);

    return Object.values(checks).every(check => check);
}

function updatePasswordCheck(elementId, isValid) {
    const element = document.getElementById(elementId);
    const icon = element.querySelector('i');
    const text = element.querySelector('span');

    if (isValid) {
        element.classList.remove('text-gray-400');
        element.classList.add('text-green-600');
        icon.classList.remove('fa-circle');
        icon.classList.add('fa-check-circle');
    } else {
        element.classList.remove('text-green-600');
        element.classList.add('text-gray-400');
        icon.classList.remove('fa-check-circle');
        icon.classList.add('fa-circle');
    }
}

// Real-time password validation
document.getElementById('password').addEventListener('input', function() {
    validatePassword(this.value);
});

// Enhanced form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;

    if (!validatePassword(password)) {
        e.preventDefault();
        alert('Password must meet all requirements.');
        return;
    }

    if (!username.trim()) {
        e.preventDefault();
        alert('Username is required.');
        return;
    }

    if (!email.trim()) {
        e.preventDefault();
        alert('Email is required.');
        return;
    }
});
</script>
{% endblock %}
