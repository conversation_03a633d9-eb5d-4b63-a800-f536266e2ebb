{% extends 'base.html' %}
{% load static %}

{% block title %}Edit {{ user.get_full_name|default:user.username }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="{% url 'authentication:user_detail' user.pk %}" class="text-gray-500 hover:text-gray-700 mr-4">
                        <i class="fa-solid fa-arrow-left"></i>
                    </a>
                    <h1 class="text-xl font-semibold text-gray-900">Edit User</h1>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">User Information</h3>
        </div>
        <div class="p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                        <input type="text" id="username" name="username" value="{{ user.username }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" id="email" name="email" value="{{ user.email }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" required>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                        <input type="text" id="first_name" name="first_name" value="{{ user.first_name }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                        <input type="text" id="last_name" name="last_name" value="{{ user.last_name }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>

                <!-- Permissions -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Permissions & Status</h4>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="is_active" name="is_active" {% if user.is_active %}checked{% endif %}
                                   class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Active
                                <span class="text-gray-500 text-xs block">User can log in and access the system</span>
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="is_staff" name="is_staff" {% if user.is_staff %}checked{% endif %}
                                   class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                            <label for="is_staff" class="ml-2 block text-sm text-gray-900">
                                Staff Status
                                <span class="text-gray-500 text-xs block">User can access the admin interface</span>
                            </label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" id="is_superuser" name="is_superuser" {% if user.is_superuser %}checked{% endif %}
                                   class="h-4 w-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500">
                            <label for="is_superuser" class="ml-2 block text-sm text-gray-900">
                                Superuser Status
                                <span class="text-gray-500 text-xs block">User has all permissions without explicitly assigning them</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Password Change -->
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-md font-medium text-gray-900 mb-4">Change Password</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="new_password1" class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                            <input type="password" id="new_password1" name="new_password1" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <p class="text-xs text-gray-500 mt-1">Leave blank to keep current password</p>
                        </div>
                        <div>
                            <label for="new_password2" class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
                            <input type="password" id="new_password2" name="new_password2" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{% url 'authentication:user_detail' user.pk %}" 
                       class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Account Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Account Actions</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Reset Password</h4>
                        <p class="text-sm text-gray-500">Send a password reset email to the user</p>
                    </div>
                    <button onclick="resetPassword({{ user.pk }})" 
                            class="px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                        Send Reset Email
                    </button>
                </div>
                
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Deactivate Account</h4>
                        <p class="text-sm text-gray-500">Temporarily disable user access</p>
                    </div>
                    <button onclick="toggleUserStatus({{ user.pk }})" 
                            class="px-3 py-1.5 {% if user.is_active %}bg-yellow-600 hover:bg-yellow-700{% else %}bg-green-600 hover:bg-green-700{% endif %} text-white text-sm font-medium rounded-md">
                        {% if user.is_active %}Deactivate{% else %}Activate{% endif %}
                    </button>
                </div>
                
                <div class="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                    <div>
                        <h4 class="text-sm font-medium text-red-900">Delete Account</h4>
                        <p class="text-sm text-red-600">Permanently delete this user account</p>
                    </div>
                    <button onclick="deleteUser({{ user.pk }})" 
                            class="px-3 py-1.5 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700">
                        Delete User
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modals -->
<div id="confirmModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                <i class="fa-solid fa-exclamation-triangle text-yellow-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-2" id="modalTitle">Confirm Action</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="modalMessage">Are you sure you want to perform this action?</p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmAction" class="px-4 py-2 bg-primary-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-primary-700">
                    Confirm
                </button>
                <button onclick="closeModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function resetPassword(userId) {
    showModal('Reset Password', 'Send a password reset email to this user?', function() {
        // In a real implementation, this would make an AJAX call
        alert('Password reset email sent!');
        closeModal();
    });
}

function toggleUserStatus(userId) {
    const isActive = {{ user.is_active|yesno:"true,false" }};
    const action = isActive ? 'deactivate' : 'activate';
    showModal(
        `${action.charAt(0).toUpperCase() + action.slice(1)} User`, 
        `Are you sure you want to ${action} this user?`, 
        function() {
            // In a real implementation, this would make an AJAX call
            alert(`User ${action}d successfully!`);
            location.reload();
        }
    );
}

function deleteUser(userId) {
    showModal('Delete User', 'Are you sure you want to permanently delete this user? This action cannot be undone.', function() {
        // In a real implementation, this would make an AJAX call
        alert('User deleted successfully!');
        window.location.href = '/auth/users/';
    });
}

function showModal(title, message, confirmCallback) {
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalMessage').textContent = message;
    document.getElementById('confirmAction').onclick = confirmCallback;
    document.getElementById('confirmModal').classList.remove('hidden');
}

function closeModal() {
    document.getElementById('confirmModal').classList.add('hidden');
}
</script>
{% endblock %}
