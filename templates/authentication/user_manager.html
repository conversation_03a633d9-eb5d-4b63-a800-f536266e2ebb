{% extends 'base.html' %}
{% load static %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
  <!-- Header -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-xl font-semibold text-gray-900">User Management</h1>
          <p class="text-gray-600 mt-1">
            Manage user accounts, roles, and permissions
            {% if current_organization %} for
            <span class="font-medium text-primary-600"
              >{{ current_organization.name }}</span
            >
            {% endif %}
          </p>
        </div>
        <div class="flex space-x-3">
          <a
            href="{% url 'authentication:user_invite' %}"
            class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 flex items-center"
          >
            <i class="fa-solid fa-user-plus mr-2"></i>
            Invite User
          </a>
          <a
            href="{% url 'authentication:bulk_permissions' %}"
            class="px-4 py-2 border border-blue-300 text-blue-700 font-medium rounded-md hover:bg-blue-50 flex items-center"
          >
            <i class="fa-solid fa-users-cog mr-2"></i>
            Bulk Permissions
          </a>
          <a
            href="{% url 'authentication:user_create' %}"
            class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center"
          >
            <i class="fa-solid fa-plus mr-2"></i>
            Create User
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- User Statistics -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Total Users</p>
          <p class="text-2xl font-bold text-gray-800">
            {{ total_users|default:24 }}
          </p>
        </div>
        <div
          class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center"
        >
          <i class="fa-solid fa-users text-blue-600"></i>
        </div>
      </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Active Users</p>
          <p class="text-2xl font-bold text-gray-800">
            {{ active_users|default:18 }}
          </p>
        </div>
        <div
          class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center"
        >
          <i class="fa-solid fa-user-check text-green-600"></i>
        </div>
      </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Administrators</p>
          <p class="text-2xl font-bold text-gray-800">
            {{ admin_users|default:3 }}
          </p>
        </div>
        <div
          class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center"
        >
          <i class="fa-solid fa-user-shield text-purple-600"></i>
        </div>
      </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Pending Invites</p>
          <p class="text-2xl font-bold text-gray-800">
            {{ pending_invites|default:5 }}
          </p>
        </div>
        <div
          class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center"
        >
          <i class="fa-solid fa-envelope text-orange-600"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters and Search -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
    <div class="p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="relative">
            <input
              type="text"
              placeholder="Search users..."
              class="pl-9 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
            />
            <i
              class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            ></i>
          </div>
          <select
            class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">All Roles</option>
            <option value="owner">Owner</option>
            <option value="admin">Admin</option>
            <option value="manager">Manager</option>
            <option value="editor">Editor</option>
            <option value="presenter">Presenter</option>
            <option value="viewer">Viewer</option>
          </select>
          <select
            class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="pending">Pending</option>
          </select>
        </div>
        <div class="flex items-center space-x-2">
          <button class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800">
            <i class="fa-solid fa-download mr-1"></i>
            Export
          </button>
          <button class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800">
            <i class="fa-solid fa-filter mr-1"></i>
            More Filters
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Users Table -->
  <div
    class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
  >
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              <input
                type="checkbox"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              User
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Role
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Last Login
            </th>
            <th
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Created
            </th>
            <th
              class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          {% for user in users %}
          <tr class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <input
                type="checkbox"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div
                  class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3"
                >
                  <i class="fa-solid fa-user text-gray-400"></i>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    {{ user.get_full_name|default:user.username }}
                  </div>
                  <div class="text-sm text-gray-500">{{ user.email }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                {% if user.organization_membership %}
                  {% if user.organization_membership.role == 'owner' %}bg-purple-100 text-purple-800
                  {% elif user.organization_membership.role == 'admin' %}bg-red-100 text-red-800
                  {% elif user.organization_membership.role == 'manager' %}bg-blue-100 text-blue-800
                  {% elif user.organization_membership.role == 'editor' %}bg-yellow-100 text-yellow-800
                  {% elif user.organization_membership.role == 'presenter' %}bg-orange-100 text-orange-800
                  {% elif user.organization_membership.role == 'news_reader' %}bg-indigo-100 text-indigo-800
                  {% else %}bg-green-100 text-green-800{% endif %}
                {% else %}bg-gray-100 text-gray-800{% endif %}"
              >
                {% if user.organization_membership %}
                  {{ user.organization_membership.get_role_display|default:"Unknown Role" }}
                {% else %}
                  No Role
                {% endif %}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if user.is_active %} bg-green-100 text-green-800 {% else %} bg-red-100 text-red-800 {% endif %}"
              >
                {% if user.is_active %} Active {% else %} Inactive {% endif %}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {% if user.last_login %} {{ user.last_login|date:'M d, Y' }} {% else %} Never {% endif %}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ user.date_joined|date:'M d, Y' }}
            </td>
            <td
              class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
            >
              <div class="flex items-center justify-end space-x-2">
                <a
                  href="{% url 'authentication:user_detail' user.pk %}"
                  class="text-primary-600 hover:text-primary-900"
                  title="View User"
                  ><i class="fa-solid fa-eye"></i
                ></a>
                <a
                  href="{% url 'authentication:user_edit' user.pk %}"
                  class="text-gray-600 hover:text-gray-900"
                  title="Edit User"
                  ><i class="fa-solid fa-edit"></i
                ></a>
                <a
                  href="{% url 'authentication:user_permissions' user.pk %}"
                  class="text-blue-600 hover:text-blue-900"
                  title="Manage Permissions"
                  ><i class="fa-solid fa-key"></i
                ></a>
                <button
                  onclick="deleteUser({{ user.pk }})"
                  class="text-red-600 hover:text-red-900"
                  title="Delete User"
                >
                  <i class="fa-solid fa-trash"></i>
                </button>
              </div>
            </td>
          </tr>
          {% empty %}
          <!-- Empty state for organization with no users -->
          <tr>
            <td colspan="7" class="px-6 py-12 text-center">
              <div class="flex flex-col items-center">
                <div
                  class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4"
                >
                  <i class="fa-solid fa-users text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                  No Users in {{
                  current_organization.name|default:'Organization' }}
                </h3>
                <p class="text-gray-500 mb-6 max-w-md">
                  Get started by creating your first user or inviting team
                  members to join your organization.
                </p>
                <div class="flex space-x-3">
                  <a
                    href="{% url 'authentication:user_create' %}"
                    class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center"
                  >
                    <i class="fa-solid fa-plus mr-2"></i>
                    Create User
                  </a>
                  <a
                    href="{% url 'authentication:user_invite' %}"
                    class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 flex items-center"
                  >
                    <i class="fa-solid fa-user-plus mr-2"></i>
                    Invite User
                  </a>
                </div>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>

  <!-- Pagination -->
  <div
    class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6"
  >
    <div class="flex-1 flex justify-between sm:hidden">
      <a
        href="#"
        class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >Previous</a
      >
      <a
        href="#"
        class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >Next</a
      >
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-gray-700">
          Showing <span class="font-medium">1</span> to
          <span class="font-medium">10</span> of
          <span class="font-medium">24</span> results
        </p>
      </div>
      <div>
        <nav
          class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
          aria-label="Pagination"
        >
          <a
            href="#"
            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            ><i class="fa-solid fa-chevron-left"></i
          ></a>
          <a
            href="#"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >1</a
          >
          <a
            href="#"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >2</a
          >
          <a
            href="#"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >3</a
          >
          <a
            href="#"
            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
            ><i class="fa-solid fa-chevron-right"></i
          ></a>
        </nav>
      </div>
    </div>
  </div>
</div>

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
  function deleteUser(userId) {
    // Get user info from the table row for better confirmation
    const userRow = document
      .querySelector(`button[onclick="deleteUser(${userId})"]`)
      .closest("tr");
    const userName = userRow.querySelector(
      "td:nth-child(2) .text-sm.font-medium"
    ).textContent;
    const userRole = userRow.querySelector("td:nth-child(3) span").textContent;

    const confirmMessage = `Are you sure you want to delete user "${userName}" (${userRole})?\n\nThis action cannot be undone and will:\n• Remove the user from the organization\n• Delete their account if they're not in other organizations\n• Remove all their data and permissions`;

    if (confirm(confirmMessage)) {
      // Show loading state
      const deleteButton = document.querySelector(
        `button[onclick="deleteUser(${userId})"]`
      );
      const originalContent = deleteButton.innerHTML;
      deleteButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i>';
      deleteButton.disabled = true;

      fetch(`/users/${userId}/delete/`, {
        method: "POST",
        headers: {
          "X-CSRFToken": document.querySelector("[name=csrfmiddlewaretoken]")
            .value,
          "Content-Type": "application/json",
        },
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            // Show success message
            showNotification(data.message, "success");

            // Remove the row with animation
            userRow.style.transition = "opacity 0.3s ease";
            userRow.style.opacity = "0";
            setTimeout(() => {
              userRow.remove();
              updateUserCount();
            }, 300);
          } else {
            // Show error message
            showNotification("Error: " + data.error, "error");

            // Restore button state
            deleteButton.innerHTML = originalContent;
            deleteButton.disabled = false;
          }
        })
        .catch((error) => {
          showNotification("Error deleting user: " + error, "error");

          // Restore button state
          deleteButton.innerHTML = originalContent;
          deleteButton.disabled = false;
        });
    }
  }

  function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement("div");
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
      type === "success" ? "bg-green-500 text-white" : "bg-red-500 text-white"
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
          <i class="fa-solid ${
            type === "success" ? "fa-check-circle" : "fa-exclamation-circle"
          } mr-2"></i>
          <span>${message}</span>
        </div>
      `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
      notification.style.transition = "opacity 0.3s ease";
      notification.style.opacity = "0";
      setTimeout(() => notification.remove(), 300);
    }, 5000);
  }

  function updateUserCount() {
    // Update the user count in the statistics
    const userRows = document.querySelectorAll("tbody tr:not(.hidden)");
    const totalUsers = userRows.length;

    // Update any user count displays if they exist
    const countElements = document.querySelectorAll("[data-user-count]");
    countElements.forEach((el) => {
      el.textContent = totalUsers;
    });
  }

  // Search functionality
  document
    .querySelector('input[placeholder="Search users..."]')
    .addEventListener("input", function (e) {
      const searchTerm = e.target.value.toLowerCase();
      const rows = document.querySelectorAll("tbody tr");

      rows.forEach((row) => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
          row.style.display = "";
        } else {
          row.style.display = "none";
        }
      });
    });

  // Role filter functionality
  document.querySelector("select").addEventListener("change", function (e) {
    const selectedRole = e.target.value.toLowerCase();
    const rows = document.querySelectorAll("tbody tr");

    rows.forEach((row) => {
      if (selectedRole === "") {
        row.style.display = "";
      } else {
        const roleText = row
          .querySelector("td:nth-child(3)")
          .textContent.toLowerCase();
        if (roleText.includes(selectedRole)) {
          row.style.display = "";
        } else {
          row.style.display = "none";
        }
      }
    });
  });
</script>
{% endblock %}
