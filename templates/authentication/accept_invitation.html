{% extends 'base.html' %}
{% load static %}

{% block title %}Accept Invitation{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
            <i class="fa-solid fa-user-plus text-primary-600 text-4xl mb-4"></i>
            <h2 class="text-3xl font-bold text-gray-900">Accept Invitation</h2>
            <p class="mt-2 text-sm text-gray-600">
                You've been invited to join <strong>{{ invitation.organization.name }}</strong>
            </p>
        </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <!-- Invitation Details -->
            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-start">
                    <i class="fa-solid fa-info-circle text-blue-600 mt-0.5 mr-3"></i>
                    <div>
                        <h3 class="text-sm font-medium text-blue-900">Invitation Details</h3>
                        <div class="mt-2 text-sm text-blue-800">
                            <p><strong>Organization:</strong> {{ invitation.organization.name }}</p>
                            <p><strong>Role:</strong> {{ invitation.get_role_display }}</p>
                            <p><strong>Invited by:</strong> {{ invitation.invited_by.get_full_name|default:invitation.invited_by.username }}</p>
                            {% if invitation.message %}
                                <p class="mt-2"><strong>Message:</strong></p>
                                <p class="italic">"{{ invitation.message }}"</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">
                        Username *
                    </label>
                    <div class="mt-1">
                        <input id="username" name="username" type="text" required
                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="Choose a username">
                    </div>
                    <p class="mt-1 text-xs text-gray-500">This will be your login username</p>
                </div>

                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700">
                        First Name
                    </label>
                    <div class="mt-1">
                        <input id="first_name" name="first_name" type="text"
                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="Your first name">
                    </div>
                </div>

                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700">
                        Last Name
                    </label>
                    <div class="mt-1">
                        <input id="last_name" name="last_name" type="text"
                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="Your last name">
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">
                        Password *
                    </label>
                    <div class="mt-1">
                        <input id="password" name="password" type="password" required
                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="Create a secure password">
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Minimum 8 characters</p>
                </div>

                <div>
                    <label for="password_confirm" class="block text-sm font-medium text-gray-700">
                        Confirm Password *
                    </label>
                    <div class="mt-1">
                        <input id="password_confirm" name="password_confirm" type="password" required
                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="Confirm your password">
                    </div>
                </div>

                <!-- Email Display -->
                <div>
                    <label class="block text-sm font-medium text-gray-700">
                        Email Address
                    </label>
                    <div class="mt-1">
                        <input type="email" value="{{ invitation.email }}" disabled
                               class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500 sm:text-sm">
                    </div>
                    <p class="mt-1 text-xs text-gray-500">This email will be associated with your account</p>
                </div>

                <!-- Role Permissions -->
                <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Your Role: {{ invitation.get_role_display }}</h4>
                    <div class="space-y-2">
                        {% if invitation.role == 'user' %}
                            <div class="flex items-center text-xs">
                                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                                <span class="text-gray-700">Create and manage your own mentions</span>
                            </div>
                            <div class="flex items-center text-xs">
                                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                                <span class="text-gray-700">View calendar and schedules</span>
                            </div>
                            <div class="flex items-center text-xs">
                                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                                <span class="text-gray-700">Edit your profile and preferences</span>
                            </div>
                        {% elif invitation.role == 'staff' %}
                            <div class="flex items-center text-xs">
                                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                                <span class="text-gray-700">All user permissions</span>
                            </div>
                            <div class="flex items-center text-xs">
                                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                                <span class="text-gray-700">Manage mentions and approvals</span>
                            </div>
                            <div class="flex items-center text-xs">
                                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                                <span class="text-gray-700">View reports and analytics</span>
                            </div>
                        {% elif invitation.role == 'admin' %}
                            <div class="flex items-center text-xs">
                                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                                <span class="text-gray-700">Full system access</span>
                            </div>
                            <div class="flex items-center text-xs">
                                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                                <span class="text-gray-700">Manage users and permissions</span>
                            </div>
                            <div class="flex items-center text-xs">
                                <i class="fa-solid fa-check text-green-500 mr-2"></i>
                                <span class="text-gray-700">System configuration and settings</span>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <button type="submit"
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fa-solid fa-user-check text-primary-500 group-hover:text-primary-400"></i>
                        </span>
                        Accept Invitation & Create Account
                    </button>
                </div>
            </form>

            <!-- Terms and Privacy -->
            <div class="mt-6">
                <div class="text-center">
                    <p class="text-xs text-gray-500">
                        By creating an account, you agree to our
                        <a href="#" class="text-primary-600 hover:text-primary-500">Terms of Service</a>
                        and
                        <a href="#" class="text-primary-600 hover:text-primary-500">Privacy Policy</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Already have an account -->
    <div class="mt-6 text-center">
        <p class="text-sm text-gray-600">
            Already have an account?
            <a href="{% url 'authentication:login' %}" class="font-medium text-primary-600 hover:text-primary-500">
                Sign in here
            </a>
        </p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    // You could add a visual password strength indicator here
});

function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const passwordConfirm = document.getElementById('password_confirm').value;
    
    if (!username) {
        e.preventDefault();
        alert('Username is required.');
        return;
    }
    
    if (password.length < 8) {
        e.preventDefault();
        alert('Password must be at least 8 characters long.');
        return;
    }
    
    if (password !== passwordConfirm) {
        e.preventDefault();
        alert('Passwords do not match.');
        return;
    }
});

// Real-time password confirmation
document.getElementById('password_confirm').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const passwordConfirm = this.value;
    
    if (passwordConfirm && password !== passwordConfirm) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
