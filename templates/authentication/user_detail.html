{% extends 'base.html' %}
{% load static %}

{% block title %}
  {{ user.get_full_name|default:user.username }} - User Details
{% endblock %}

{% block content %}
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <a href="{% url 'authentication:user_manager' %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
            <h1 class="text-xl font-semibold text-gray-900">User Details</h1>
          </div>
          <div class="flex space-x-2">
            <a href="{% url 'authentication:user_edit' user.pk %}" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
              <i class="fa-solid fa-edit mr-1"></i>
              Edit
            </a>
            <a href="{% url 'authentication:user_permissions' user.pk %}" class="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700">
              <i class="fa-solid fa-key mr-1"></i>
              Permissions
            </a>
            <button onclick="deleteUser({{ user.pk }})" class="bg-red-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-red-700">
              <i class="fa-solid fa-trash mr-1"></i>
              Delete
            </button>
          </div>
        </div>
      </div>

      <!-- User Info -->
      <div class="p-6">
        <div class="flex items-start space-x-6">
          <div class="flex-shrink-0">
            <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
              <i class="fa-solid fa-user text-gray-400 text-2xl"></i>
            </div>
          </div>
          <div class="flex-1">
            <h2 class="text-2xl font-bold text-gray-900">{{ user.get_full_name|default:user.username }}</h2>
            <p class="text-lg text-gray-600 mt-1">@{{ user.username }}</p>
            <div class="flex items-center mt-2">
              {% if user.is_active %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <i class="fa-solid fa-circle text-green-400 mr-1 text-xs"></i>
                  Active
                </span>
              {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <i class="fa-solid fa-circle text-red-400 mr-1 text-xs"></i>
                  Inactive
                </span>
              {% endif %}

              {% if user.is_superuser %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 ml-2">
                  <i class="fa-solid fa-crown mr-1"></i>
                  Super Admin
                </span>
              {% elif user.is_staff %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                  <i class="fa-solid fa-user-tie mr-1"></i>
                  Staff
                </span>
              {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ml-2">
                  <i class="fa-solid fa-user mr-1"></i>
                  User
                </span>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Contact Information</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          {% if user.email %}
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <a href="mailto:{{ user.email }}" class="text-primary-600 hover:text-primary-700">{{ user.email }}</a>
            </div>
          {% endif %}
          {% if user.first_name %}
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
              <p class="text-gray-900">{{ user.first_name }}</p>
            </div>
          {% endif %}
          {% if user.last_name %}
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
              <p class="text-gray-900">{{ user.last_name }}</p>
            </div>
          {% endif %}
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
            <p class="text-gray-900">{{ user.username }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Account Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Account Information</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Date Joined</label>
            <p class="text-gray-900">{{ user.date_joined|date:'F j, Y' }}</p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Last Login</label>
            <p class="text-gray-900">
              {% if user.last_login %}
                {{ user.last_login|date:'F j, Y g:i A' }}
              {% else %}
                Never
              {% endif %}
            </p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Account Status</label>
            <p class="text-gray-900">
              {% if user.is_active %}
                <span class="text-green-600">Active</span>
              {% else %}
                <span class="text-red-600">Inactive</span>
              {% endif %}
            </p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Email Verified</label>
            <p class="text-gray-900">
              {% if user.email %}
                <span class="text-green-600">Yes</span>
              {% else %}
                <span class="text-red-600">No</span>
              {% endif %}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Permissions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Permissions & Roles</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Superuser Status</label>
            <p class="text-gray-900">
              {% if user.is_superuser %}
                <span class="text-green-600"><i class="fa-solid fa-check mr-1"></i>Yes</span>
              {% else %}
                <span class="text-red-600"><i class="fa-solid fa-times mr-1"></i>No</span>
              {% endif %}
            </p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Staff Status</label>
            <p class="text-gray-900">
              {% if user.is_staff %}
                <span class="text-green-600"><i class="fa-solid fa-check mr-1"></i>Yes</span>
              {% else %}
                <span class="text-red-600"><i class="fa-solid fa-times mr-1"></i>No</span>
              {% endif %}
            </p>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Active Status</label>
            <p class="text-gray-900">
              {% if user.is_active %}
                <span class="text-green-600"><i class="fa-solid fa-check mr-1"></i>Yes</span>
              {% else %}
                <span class="text-red-600"><i class="fa-solid fa-times mr-1"></i>No</span>
              {% endif %}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Activity Summary -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Activity Summary</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">0</div>
            <div class="text-sm text-gray-600">Mentions Created</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">0</div>
            <div class="text-sm text-gray-600">Shows Managed</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">0</div>
            <div class="text-sm text-gray-600">Reports Generated</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900">{{ user.date_joined|timesince }}</div>
            <div class="text-sm text-gray-600">Member Since</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <i class="fa-solid fa-exclamation-triangle text-red-600"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mt-2">Delete User</h3>
        <div class="mt-2 px-7 py-3">
          <p class="text-sm text-gray-500">Are you sure you want to delete this user? This action cannot be undone and will remove all associated data.</p>
        </div>
        <div class="items-center px-4 py-3">
          <button id="confirmDelete" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-700">Delete</button>
          <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400">Cancel</button>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    function deleteUser(userId) {
      document.getElementById('deleteModal').classList.remove('hidden')
      document.getElementById('confirmDelete').onclick = function () {
        // In a real implementation, this would make an AJAX call to delete the user
        alert('User deletion functionality would be implemented here')
        closeDeleteModal()
      }
    }
    
    function closeDeleteModal() {
      document.getElementById('deleteModal').classList.add('hidden')
    }
  </script>
{% endblock %}
