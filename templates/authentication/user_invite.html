{% extends 'base.html' %}
{% load static %}

{% block title %}
  Invite User
{% endblock %}

{% block content %}
  <div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <a href="{% url 'authentication:user_manager' %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
            <div>
              <h1 class="text-xl font-semibold text-gray-900">Invite User</h1>
              <p class="text-sm text-gray-500 mt-1">
                Send an invitation to join{% if current_organization %}
                  <span class="font-medium text-primary-600">{{ current_organization.name }}</span>
                {% else %}
                  the organization
                {% endif %}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <form method="post" class="space-y-6">
      {% csrf_token %}

      <!-- Invitation Details -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Invitation Details</h3>
          <p class="text-sm text-gray-500 mt-1">Enter the details for the user invitation</p>
        </div>
        <div class="p-6">
          <div class="space-y-6">
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
              <input type="email" id="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" placeholder="<EMAIL>" />
              <p class="text-xs text-gray-500 mt-1">The email address where the invitation will be sent</p>
            </div>

            <div>
              <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Organization Role *</label>
              <select id="role" name="role" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                <option value="">Select a role</option>
                <option value="viewer">Viewer</option>
                <option value="editor">Editor</option>
                <option value="presenter">Presenter</option>
                <option value="news_reader">News Reader</option>
                <option value="manager">Manager</option>
                <option value="admin">Admin</option>
                <option value="owner">Owner</option>
              </select>
              <p class="text-xs text-gray-500 mt-1">The role and permissions within the organization</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Organization</label>
              {% if current_organization %}
                <div class="p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <div class="flex items-center">
                    <i class="fa-solid fa-building text-blue-600 mr-2"></i>
                    <div>
                      <p class="text-sm font-medium text-blue-900">{{ current_organization.name }}</p>
                      <p class="text-xs text-blue-700">User will be invited to this organization</p>
                    </div>
                  </div>
                </div>
              {% else %}
                <div class="p-3 bg-red-50 border border-red-200 rounded-md">
                  <div class="flex items-center">
                    <i class="fa-solid fa-exclamation-triangle text-red-600 mr-2"></i>
                    <div>
                      <p class="text-sm font-medium text-red-900">No Organization Context</p>
                      <p class="text-xs text-red-700">Please select an organization first</p>
                    </div>
                  </div>
                </div>
              {% endif %}
            </div>

            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 mb-1">Personal Message (Optional)</label>
              <textarea id="message" name="message" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" placeholder="Welcome to our team! We're excited to have you join us..."></textarea>
              <p class="text-xs text-gray-500 mt-1">A personal message to include in the invitation email</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Role Permissions Preview -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Role Permissions</h3>
          <p class="text-sm text-gray-500 mt-1">Preview of permissions for selected role</p>
        </div>
        <div class="p-6">
          <div id="role-permissions">
            <!-- Default message -->
            <div id="no-role-selected" class="text-center py-8">
              <i class="fa-solid fa-user-shield text-gray-400 text-3xl mb-3"></i>
              <p class="text-gray-500">Select a role to see permissions</p>
            </div>

            <!-- User permissions -->
            <div id="user-permissions" class="role-permissions hidden">
              <h4 class="text-sm font-medium text-gray-900 mb-3">User Permissions</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View own mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Create mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Edit own profile</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View calendar</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-times text-red-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage other users</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-times text-red-500 mr-2"></i>
                  <span class="text-sm text-gray-700">System settings</span>
                </div>
              </div>
            </div>

            <!-- News Reader permissions -->
            <div id="news_reader-permissions" class="role-permissions hidden">
              <h4 class="text-sm font-medium text-gray-900 mb-3">News Reader Permissions</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View news reader dashboard</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage reading notes</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Update reading status</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View assigned mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Access live reading tools</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View schedule</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-times text-red-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage users</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-times text-red-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Approve mentions</span>
                </div>
              </div>
            </div>

            <!-- Staff permissions -->
            <div id="staff-permissions" class="role-permissions hidden">
              <h4 class="text-sm font-medium text-gray-900 mb-3">Staff Permissions</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">All user permissions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Approve mentions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">View reports</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage shows</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-times text-red-500 mr-2"></i>
                  <span class="text-sm text-gray-700">System administration</span>
                </div>
              </div>
            </div>

            <!-- Admin permissions -->
            <div id="admin-permissions" class="role-permissions hidden">
              <h4 class="text-sm font-medium text-gray-900 mb-3">Administrator Permissions</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">All staff permissions</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Manage users</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">System settings</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Organization management</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">Full system access</span>
                </div>
                <div class="flex items-center">
                  <i class="fa-solid fa-check text-green-500 mr-2"></i>
                  <span class="text-sm text-gray-700">User invitations</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Invitation Preview -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Email Preview</h3>
          <p class="text-sm text-gray-500 mt-1">Preview of the invitation email</p>
        </div>
        <div class="p-6">
          <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div class="text-sm">
              <p class="font-medium text-gray-900 mb-2">
                Subject: Invitation to join <span id="org-display">
                  {% if current_organization %}
                    {{ current_organization.name }}
                  {% else %}
                    Organization
                  {% endif %}
                </span>
              </p>
              <div class="text-gray-700 space-y-2">
                <p>Hello,</p>
                <p>
                  You have been invited to join <strong>
                    <span id="org-display-2">
                      {% if current_organization %}
                        {{ current_organization.name }}
                      {% else %}
                        Organization
                      {% endif %}
                    </span>
                  </strong> as a <span id="role-display">Viewer</span>.
                </p>
                <div id="message-preview" class="hidden">
                  <p>
                    <em id="message-content"></em>
                  </p>
                </div>
                <p>Click the link below to accept the invitation and create your account:</p>
                <p class="text-primary-600">[Invitation Link]</p>
                <p>This invitation expires in 7 days.</p>
                <p>
                  Best regards,<br />{{ request.user.get_full_name|default:request.user.username }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex items-center justify-end space-x-3 pt-6">
        <a href="{% url 'authentication:user_manager' %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">Cancel</a>
        <button type="submit" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
          <i class="fa-solid fa-paper-plane mr-2"></i>
          Send Invitation
        </button>
      </div>
    </form>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    document.getElementById('role').addEventListener('change', function () {
      const role = this.value
      const permissionDivs = document.querySelectorAll('.role-permissions')
      const noRoleDiv = document.getElementById('no-role-selected')
      const roleDisplay = document.getElementById('role-display')
    
      // Hide all permission divs
      permissionDivs.forEach((div) => div.classList.add('hidden'))
    
      if (role) {
        // Hide no role message
        noRoleDiv.classList.add('hidden')
    
        // Show the selected role's permissions (if it exists)
        const rolePermissionDiv = document.getElementById(role + '-permissions')
        if (rolePermissionDiv) {
          rolePermissionDiv.classList.remove('hidden')
        }
    
        // Update role display in email preview
        roleDisplay.textContent = role.charAt(0).toUpperCase() + role.slice(1)
      } else {
        // Show no role message
        noRoleDiv.classList.remove('hidden')
        roleDisplay.textContent = 'Viewer'
      }
    })
    
    document.getElementById('message').addEventListener('input', function () {
      const message = this.value.trim()
      const messagePreview = document.getElementById('message-preview')
      const messageContent = document.getElementById('message-content')
    
      if (message) {
        messageContent.textContent = message
        messagePreview.classList.remove('hidden')
      } else {
        messagePreview.classList.add('hidden')
      }
    })
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function (e) {
      const email = document.getElementById('email').value.trim()
      const role = document.getElementById('role').value
    
      if (!email) {
        e.preventDefault()
        alert('Email address is required.')
        return
      }
    
      if (!role) {
        e.preventDefault()
        alert('Please select a role for the user.')
        return
      }
    
      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        e.preventDefault()
        alert('Please enter a valid email address.')
        return
      }
    })
  </script>
{% endblock %}
