{% extends 'base.html' %}
{% load static %}

{% block title %}Bulk Permissions Management{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Bulk Permissions Management</h1>
          <p class="mt-2 text-gray-600">
            Manage permissions for multiple users in {{ current_organization.name }}
          </p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'authentication:user_manager' %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 flex items-center">
            <i class="fa-solid fa-arrow-left mr-2"></i>
            Back to Users
          </a>
        </div>
      </div>
    </div>

    <!-- Bulk Actions Form -->
    <form method="post" id="bulk-form">
      {% csrf_token %}
      
      <!-- Action Selection -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Bulk Actions</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Change Role -->
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center mb-3">
                <input type="radio" id="change_role" name="action" value="change_role" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300">
                <label for="change_role" class="ml-2 text-sm font-medium text-gray-900">Change Role</label>
              </div>
              <select name="new_role" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500" disabled>
                <option value="">Select Role</option>
                {% for role_code, role_name in available_roles %}
                  <option value="{{ role_code }}">{{ role_name }}</option>
                {% endfor %}
              </select>
            </div>

            <!-- Activate Users -->
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center mb-3">
                <input type="radio" id="activate_users" name="action" value="activate_users" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300">
                <label for="activate_users" class="ml-2 text-sm font-medium text-gray-900">Activate Users</label>
              </div>
              <p class="text-xs text-gray-500">Enable selected users to access the system</p>
            </div>

            <!-- Deactivate Users -->
            <div class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center mb-3">
                <input type="radio" id="deactivate_users" name="action" value="deactivate_users" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300">
                <label for="deactivate_users" class="ml-2 text-sm font-medium text-gray-900">Deactivate Users</label>
              </div>
              <p class="text-xs text-gray-500">Disable selected users from accessing the system</p>
            </div>
          </div>

          <div class="mt-6 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <button type="button" id="select-all" class="text-sm text-primary-600 hover:text-primary-700">Select All</button>
              <button type="button" id="select-none" class="text-sm text-primary-600 hover:text-primary-700">Select None</button>
              <span id="selected-count" class="text-sm text-gray-500">0 users selected</span>
            </div>
            <button type="submit" id="apply-action" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Apply Action
            </button>
          </div>
        </div>
      </div>

      <!-- Users Table -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input type="checkbox" id="select-all-checkbox" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" />
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Role</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permissions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for item in users_with_memberships %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <input type="checkbox" name="selected_users" value="{{ item.user.pk }}" class="user-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" />
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                        <i class="fa-solid fa-user text-gray-400"></i>
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ item.user.get_full_name|default:item.user.username }}</div>
                        <div class="text-sm text-gray-500">{{ item.user.email }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                      {% if item.membership.role == 'owner' %}bg-purple-100 text-purple-800
                      {% elif item.membership.role == 'admin' %}bg-red-100 text-red-800
                      {% elif item.membership.role == 'manager' %}bg-blue-100 text-blue-800
                      {% elif item.membership.role == 'editor' %}bg-yellow-100 text-yellow-800
                      {% elif item.membership.role == 'presenter' %}bg-orange-100 text-orange-800
                      {% else %}bg-green-100 text-green-800{% endif %}">
                      {{ item.membership.get_role_display }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                      {% if item.user.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                      {% if item.user.is_active %}Active{% else %}Inactive{% endif %}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {% if item.user.last_login %}
                      {{ item.user.last_login|date:'M d, Y' }}
                    {% else %}
                      Never
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <a href="{% url 'authentication:user_permissions' item.user.pk %}" class="text-primary-600 hover:text-primary-900">
                      <i class="fa-solid fa-key mr-1"></i>
                      Manage
                    </a>
                  </td>
                </tr>
              {% empty %}
                <tr>
                  <td colspan="6" class="px-6 py-12 text-center">
                    <div class="flex flex-col items-center">
                      <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fa-solid fa-users text-gray-400 text-2xl"></i>
                      </div>
                      <h3 class="text-lg font-medium text-gray-900 mb-2">No Users Found</h3>
                      <p class="text-gray-500">There are no users in this organization.</p>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </form>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Handle action selection
    document.querySelectorAll('input[name="action"]').forEach(radio => {
      radio.addEventListener('change', function() {
        // Enable/disable role select based on action
        const roleSelect = document.querySelector('select[name="new_role"]');
        if (this.value === 'change_role') {
          roleSelect.disabled = false;
          roleSelect.required = true;
        } else {
          roleSelect.disabled = true;
          roleSelect.required = false;
        }
        
        updateApplyButton();
      });
    });

    // Handle user selection
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const selectedCountSpan = document.getElementById('selected-count');
    const applyButton = document.getElementById('apply-action');

    function updateSelectedCount() {
      const selectedCount = document.querySelectorAll('.user-checkbox:checked').length;
      selectedCountSpan.textContent = `${selectedCount} users selected`;
      
      // Update select all checkbox state
      if (selectedCount === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
      } else if (selectedCount === userCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
      } else {
        selectAllCheckbox.indeterminate = true;
      }
      
      updateApplyButton();
    }

    function updateApplyButton() {
      const selectedCount = document.querySelectorAll('.user-checkbox:checked').length;
      const actionSelected = document.querySelector('input[name="action"]:checked');
      const roleSelected = document.querySelector('select[name="new_role"]').value;
      
      let canApply = selectedCount > 0 && actionSelected;
      
      if (actionSelected && actionSelected.value === 'change_role') {
        canApply = canApply && roleSelected;
      }
      
      applyButton.disabled = !canApply;
    }

    // User checkbox event listeners
    userCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', updateSelectedCount);
    });

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
      userCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
      });
      updateSelectedCount();
    });

    // Quick select buttons
    document.getElementById('select-all').addEventListener('click', function() {
      userCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
      });
      updateSelectedCount();
    });

    document.getElementById('select-none').addEventListener('click', function() {
      userCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
      });
      updateSelectedCount();
    });

    // Role select change
    document.querySelector('select[name="new_role"]').addEventListener('change', updateApplyButton);

    // Form submission confirmation
    document.getElementById('bulk-form').addEventListener('submit', function(e) {
      const selectedCount = document.querySelectorAll('.user-checkbox:checked').length;
      const actionSelected = document.querySelector('input[name="action"]:checked');
      
      if (!actionSelected || selectedCount === 0) {
        e.preventDefault();
        alert('Please select an action and at least one user.');
        return;
      }
      
      const actionText = actionSelected.nextElementSibling.textContent;
      const confirmMessage = `Are you sure you want to ${actionText.toLowerCase()} for ${selectedCount} selected users?`;
      
      if (!confirm(confirmMessage)) {
        e.preventDefault();
      }
    });

    // Initialize
    updateSelectedCount();
  </script>
{% endblock %}
