{% extends 'base.html' %}
{% load static %}

{% block title %}
  {% if object %}
    Edit Show
  {% else %}
    Create New Show
  {% endif %}
{% endblock %}

{% block content %}
  <div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center">
          <a href="{% url 'shows:show_list' %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
          <h1 class="text-xl font-semibold text-gray-900">
            {% if object %}
              Edit Show
            {% else %}
              Create New Show
            {% endif %}
          </h1>
        </div>
      </div>

      <!-- Form -->
      <div class="p-6">
        <form method="post" class="space-y-6">
          {% csrf_token %}

          <!-- Display form errors -->
          {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
              {% for error in form.non_field_errors %}
                <p class="text-sm">{{ error }}</p>
              {% endfor %}
            </div>
          {% endif %}

          <!-- Basic Information -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>

            <div class="space-y-4">
              <div>
                <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Show Name <span class="text-red-500">*</span></label>
                {{ form.name }}
                {% if form.name.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                {{ form.description }}
                {% if form.description.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                {% endif %}
              </div>

              <!-- Branch Selection -->
              {% if form.branch.field.queryset %}
                <div>
                  <label for="{{ form.branch.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Branch</label>
                  {{ form.branch }}
                  {% if form.branch.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.branch.errors.0 }}</p>
                  {% endif %}
                  <p class="mt-1 text-sm text-gray-500">{{ form.branch.help_text }}</p>
                </div>
              {% endif %}
            </div>
          </div>

          <!-- Schedule -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label for="{{ form.start_time.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Start Time <span class="text-red-500">*</span></label>
                {{ form.start_time }}
                {% if form.start_time.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.start_time.errors.0 }}</p>
                {% endif %}
              </div>

              <div>
                <label for="{{ form.end_time.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">End Time <span class="text-red-500">*</span></label>
                {{ form.end_time }}
                {% if form.end_time.errors %}
                  <p class="mt-1 text-sm text-red-600">{{ form.end_time.errors.0 }}</p>
                {% endif %}
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-3">Days of Week <span class="text-red-500">*</span></label>
              <div class="grid grid-cols-7 gap-2">
                {% for choice in form.days_of_week %}
                  <div class="text-center">
                    {{ choice.tag }}
                    <label for="{{ choice.id_for_label }}" class="day-checkbox block w-full py-2 px-1 text-sm font-medium rounded-md cursor-pointer transition-colors">{{ choice.choice_label|slice:':3' }}</label>
                    <p class="text-xs text-gray-500 mt-1">{{ choice.choice_label }}</p>
                  </div>
                {% endfor %}
              </div>
              {% if form.days_of_week.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.days_of_week.errors.0 }}</p>
              {% endif %}
              <p class="mt-2 text-sm text-gray-500">{{ form.days_of_week.help_text }}</p>
            </div>

            <div>
              <label for="{{ form.time_interval_seconds.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                Time Interval for Mentions
                <span class="ml-1 text-xs text-gray-500">(affects scheduling precision)</span>
              </label>
              {{ form.time_interval_seconds }}
              {% if form.time_interval_seconds.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.time_interval_seconds.errors.0 }}</p>
              {% endif %}
              <p class="mt-2 text-sm text-gray-500">{{ form.time_interval_seconds.help_text }}</p>
            </div>
          </div>

          <!-- Presenters -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Presenters</h3>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Assign Presenters</label>
              <div class="space-y-2 max-h-48 overflow-y-auto border border-gray-300 rounded-md p-3">
                {% for choice in form.presenters %}
                  <div class="flex items-center">
                    {{ choice.tag }}
                    <label for="{{ choice.id_for_label }}" class="ml-3 flex items-center cursor-pointer">
                      <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-2">
                        <i class="fa-solid fa-user text-gray-400 text-xs"></i>
                      </div>
                      <div>
                        <p class="text-sm font-medium text-gray-900">{{ choice.choice_label }}</p>
                      </div>
                    </label>
                  </div>
                {% empty %}
                  <p class="text-sm text-gray-500">
                    No presenters available. <a href="{% url 'core:presenter_list' %}" class="text-primary-600 hover:text-primary-700">View presenters</a> or assign users the "presenter" role.
                  </p>
                {% endfor %}
              </div>
              {% if form.presenters.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.presenters.errors.0 }}</p>
              {% endif %}
              <p class="mt-1 text-sm text-gray-500">{{ form.presenters.help_text }}</p>
            </div>
          </div>

          <!-- Status -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Status</h3>
            <div class="flex items-center">
              {{ form.is_active }}
              <label for="{{ form.is_active.id_for_label }}" class="ml-2 text-sm text-gray-700">Active show</label>
            </div>
            {% if form.is_active.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.is_active.errors.0 }}</p>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">Inactive shows won't appear in scheduling options</p>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <a href="{% url 'shows:show_list' %}" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</a>
            <button type="submit" class="bg-primary-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-primary-700">
              {% if object %}
                Update Show
              {% else %}
                Create Show
              {% endif %}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_css %}
  <style>
    /* Day checkbox styling */
    .day-checkbox {
      @apply bg-gray-100 text-gray-700 border border-gray-300;
    }
    
    input[type='checkbox']:checked + .day-checkbox {
      @apply bg-primary-100 text-primary-800 border-primary-300;
    }
    
    .day-checkbox:hover {
      @apply bg-gray-200;
    }
    
    input[type='checkbox']:checked + .day-checkbox:hover {
      @apply bg-primary-200;
    }
  </style>
{% endblock %}

{% block extra_js %}
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Calculate and display duration
      const startTimeInput = document.getElementById('id_start_time')
      const endTimeInput = document.getElementById('id_end_time')
    
      function calculateDuration() {
        if (startTimeInput.value && endTimeInput.value) {
          const start = new Date('2000-01-01 ' + startTimeInput.value)
          const end = new Date('2000-01-01 ' + endTimeInput.value)
    
          let duration = (end - start) / (1000 * 60) // Duration in minutes
    
          if (duration < 0) {
            duration += 24 * 60 // Handle overnight shows
          }
    
          const hours = Math.floor(duration / 60)
          const minutes = duration % 60
    
          let durationText = ''
          if (hours > 0) {
            durationText += hours + 'h '
          }
          if (minutes > 0) {
            durationText += minutes + 'm'
          }
    
          // Display duration (you could add a duration display element)
          console.log('Show duration:', durationText)
        }
      }
    
      startTimeInput.addEventListener('change', calculateDuration)
      endTimeInput.addEventListener('change', calculateDuration)
    
      // Initial calculation
      calculateDuration()
    })
  </script>
{% endblock %}
