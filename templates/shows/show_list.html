{% extends 'base.html' %}

{% block title %}
  Show Management - RadioMention
{% endblock %}

{% block page_title %}
  Show Management
{% endblock %}

{% block header_actions %}
  <a href="{% url 'shows:show_create' %}" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
    <i class="fa-solid fa-plus mr-2"></i>
    Add Show
  </a>
{% endblock %}

{% block content %}
  <div class="flex items-center justify-between mb-6">
    <div>
      <h3 class="text-xl font-semibold text-gray-800">Radio Shows</h3>
      <p class="text-gray-600">Manage show schedules and presenter assignments</p>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
    <form method="get" class="flex flex-wrap items-center gap-4">
      <div class="flex-1 min-w-64">{{ filter_form.search }}</div>
      <div>{{ filter_form.status }}</div>
      {% if filter_form.branch.field.queryset %}
        <div>{{ filter_form.branch }}</div>
      {% endif %}
      {% if filter_form.presenter.field.queryset %}
        <div>{{ filter_form.presenter }}</div>
      {% endif %}
      <div class="flex space-x-2">
        <button type="submit" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700"><i class="fa-solid fa-search mr-2"></i>Filter</button>
        <a href="{% url 'shows:show_list' %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50"><i class="fa-solid fa-times mr-2"></i>Clear</a>
      </div>
    </form>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
    {% for show in shows %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1">
            <h4 class="text-lg font-semibold text-gray-900">{{ show.name }}</h4>
            <p class="text-sm text-gray-600 mt-1">{{ show.description|default:'No description' }}</p>
            {% if show.branch %}
              <p class="text-xs text-gray-500 mt-1">
                <i class="fa-solid fa-building mr-1"></i>{{ show.branch.name }}
              </p>
            {% endif %}
          </div>
          {% if show.is_active %}
            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>
          {% else %}
            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
          {% endif %}
        </div>

        <!-- Schedule Info -->
        <div class="bg-gray-50 rounded-lg p-4 mb-4">
          <div class="flex items-center mb-2">
            <i class="fa-solid fa-clock text-gray-400 mr-2"></i>
            <span class="text-sm font-medium text-gray-700">Schedule</span>
          </div>
          <div class="text-sm text-gray-600">
            <p>{{ show.start_time|time:'g:i A' }} - {{ show.end_time|time:'g:i A' }}</p>
            <p class="mt-1">{{ show.weekdays_display }}</p>
            <p class="text-xs text-gray-500 mt-1">Duration: {{ show.duration_minutes }} minutes</p>
          </div>
        </div>

        <!-- Presenters -->
        <div class="mb-4">
          <div class="flex items-center mb-2">
            <i class="fa-solid fa-microphone text-gray-400 mr-2"></i>
            <span class="text-sm font-medium text-gray-700">Presenters</span>
          </div>
          <div class="space-y-2">
            {% for show_presenter in show.active_presenters %}
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                    <i class="fa-solid fa-user text-xs text-gray-500"></i>
                  </div>
                  <span class="text-sm text-gray-900">{{ show_presenter.presenter.display_name }}</span>
                </div>
                <div class="flex items-center space-x-1">
                  {% if show_presenter.is_primary %}
                    <span class="px-2 py-1 text-xs font-medium rounded bg-primary-100 text-primary-800">Primary</span>
                  {% endif %}
                  <span class="text-xs text-gray-500">{{ show_presenter.role }}</span>
                </div>
              </div>
            {% empty %}
              <p class="text-sm text-gray-500">No presenters assigned</p>
            {% endfor %}
          </div>
        </div>

        <!-- Stats -->
        <div class="bg-gray-50 rounded-lg p-4 mb-4">
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
              <p class="text-lg font-semibold text-gray-900">{{ show.total_mentions }}</p>
              <p class="text-xs text-gray-500">Total Mentions</p>
            </div>
            <div class="text-center">
              <p class="text-lg font-semibold text-gray-900">{{ show.active_presenters.count }}</p>
              <p class="text-xs text-gray-500">Presenters</p>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex space-x-2">
          <a href="{% url 'shows:show_detail' show.pk %}" class="flex-1 px-3 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700 text-center">View Details</a>
          <a href="{% url 'shows:show_edit' show.pk %}" class="px-3 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50" title="Edit Show"><i class="fa-solid fa-edit"></i></a>
          <a href="{% url 'shows:show_analytics' show.pk %}" class="px-3 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50" title="Analytics"><i class="fa-solid fa-chart-line"></i></a>
          <button onclick="scheduleShow({{ show.pk }})" class="px-3 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50" title="Schedule"><i class="fa-solid fa-calendar"></i></button>
        </div>
      </div>
    {% empty %}
      <div class="col-span-3 text-center py-12">
        <i class="fa-solid fa-broadcast-tower text-6xl text-gray-300 mb-4"></i>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No shows found</h3>
        <p class="text-gray-500 mb-4">Get started by creating your first radio show.</p>
        <a href="{% url 'shows:show_create' %}" class="inline-flex items-center px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700">
          <i class="fa-solid fa-plus mr-2"></i>
          Create Show
        </a>
      </div>
    {% endfor %}
  </div>

  <!-- Quick Stats -->
  {% if shows %}
    <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4">Show Statistics</h3>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="text-center">
          <p class="text-2xl font-bold text-gray-900">{{ shows|length }}</p>
          <p class="text-sm text-gray-500">Total Shows</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-gray-900">
            {% with active_shows=shows|length %}
              {{ active_shows }}
            {% endwith %}
          </p>
          <p class="text-sm text-gray-500">Active Shows</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-gray-900">
            {% with total_hours=0 %}
              {% for show in shows %}
                {% with total_hours=total_hours|add:show.duration_minutes %}

                {% endwith %}
              {% endfor %}
              {{ total_hours|floatformat:0 }}
            {% endwith %}
          </p>
          <p class="text-sm text-gray-500">Total Minutes/Day</p>
        </div>
        <div class="text-center">
          <p class="text-2xl font-bold text-gray-900">
            {% with total_mentions=0 %}
              {% for show in shows %}
                {% with total_mentions=total_mentions|add:show.total_mentions %}

                {% endwith %}
              {% endfor %}
              {{ total_mentions }}
            {% endwith %}
          </p>
          <p class="text-sm text-gray-500">Total Mentions</p>
        </div>
      </div>
    </div>
  {% endif %}
{% endblock %}

{% block extra_js %}
  <script>
    function scheduleShow(showId) {
      // This would open a scheduling interface
      window.location.href = `/mentions/calendar/?show=${showId}`
    }
  </script>
{% endblock %}
