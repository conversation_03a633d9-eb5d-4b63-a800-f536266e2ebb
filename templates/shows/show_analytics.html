{% extends 'base.html' %}
{% load static %}

{% block title %}
  {{ show.name }} Analytics - RadioMention
{% endblock %}

{% block page_title %}
  {{ show.name }} Analytics
{% endblock %}

{% block header_actions %}
  <div class="flex space-x-3">
    <a href="{% url 'shows:show_detail' show.pk %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">
      <i class="fa-solid fa-arrow-left mr-2"></i>
      Back to Show
    </a>
    <a href="{% url 'shows:show_edit' show.pk %}" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700">
      <i class="fa-solid fa-edit mr-2"></i>
      Edit Show
    </a>
    <button onclick="exportAnalytics()" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">
      <i class="fa-solid fa-download mr-2"></i>
      Export
    </button>
  </div>
{% endblock %}

{% block content %}
  <div class="space-y-6">
    <!-- Overview Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-microphone text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Mentions</p>
            <p class="text-2xl font-semibold text-gray-900">{{ show.total_mentions }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-check-circle text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Completion Rate</p>
            <p class="text-2xl font-semibold text-gray-900">{{ completion_rate|floatformat:1 }}%</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-users text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Active Presenters</p>
            <p class="text-2xl font-semibold text-gray-900">{{ show.active_presenters.count }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-clock text-yellow-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Show Duration</p>
            <p class="text-2xl font-semibold text-gray-900">{{ show.duration_display }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Mentions Over Time -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Mentions Over Time</h3>
        <div class="h-64 flex items-center justify-center text-gray-500">
          <div class="text-center">
            <i class="fa-solid fa-chart-line text-4xl mb-2"></i>
            <p>Chart visualization would be implemented here</p>
            <p class="text-sm">Using Chart.js or similar library</p>
          </div>
        </div>
      </div>

      <!-- Presenter Performance -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Presenter Performance</h3>
        <div class="space-y-4">
          {% for presenter_stat in presenter_stats %}
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                  <i class="fa-solid fa-user text-gray-500 text-xs"></i>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ presenter_stat.presenter.display_name }}</p>
                  <p class="text-xs text-gray-500">{{ presenter_stat.role }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-semibold text-gray-900">{{ presenter_stat.mention_count }}</p>
                <p class="text-xs text-gray-500">mentions</p>
              </div>
            </div>
          {% empty %}
            <p class="text-gray-500 text-center py-4">No presenter data available</p>
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- Schedule Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Schedule Information</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <h4 class="text-sm font-medium text-gray-700 mb-2">Air Time</h4>
          <p class="text-lg text-gray-900">{{ show.start_time|time:'g:i A' }} - {{ show.end_time|time:'g:i A' }}</p>
        </div>
        <div>
          <h4 class="text-sm font-medium text-gray-700 mb-2">Days</h4>
          <p class="text-lg text-gray-900">{{ show.weekdays_display }}</p>
        </div>
        <div>
          <h4 class="text-sm font-medium text-gray-700 mb-2">Duration</h4>
          <p class="text-lg text-gray-900">{{ show.duration_display }}</p>
        </div>
      </div>

      {% if show.branch %}
        <div class="mt-4 pt-4 border-t border-gray-200">
          <h4 class="text-sm font-medium text-gray-700 mb-2">Branch</h4>
          <p class="text-lg text-gray-900">{{ show.branch.name }}</p>
        </div>
      {% endif %}
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
// Placeholder for chart initialization
// In a real implementation, you would use Chart.js or similar

// Example mentions over time data
const mentionsData = {
    labels: [{% for item in mentions_by_month %}'{{ item.month }}'{% if not forloop.last %},{% endif %}{% endfor %}],
    datasets: [{
        label: 'Mentions',
        data: [{% for item in mentions_by_month %}{{ item.count }}{% if not forloop.last %},{% endif %}{% endfor %}],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.1
    }]
};

// You would initialize charts here
console.log('Analytics data loaded:', {
    mentions: mentionsData,
    completionRate: {{ completion_rate|default:0 }},
    totalMentions: {{ show.total_mentions }}
});

function exportAnalytics() {
    // Export functionality
    const data = {
        show: '{{ show.name }}',
        totalMentions: {{ show.total_mentions }},
        completionRate: {{ completion_rate|default:0 }},
        duration: '{{ show.duration_display }}',
        schedule: '{{ show.weekdays_display }}',
        airTime: '{{ show.start_time|time:"g:i A" }} - {{ show.end_time|time:"g:i A" }}'
    };

    // Create and download CSV
    const csv = Object.entries(data).map(([key, value]) => `${key},${value}`).join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${data.show}_analytics.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
}
</script>
{% endblock %}
