{% extends 'base.html' %}
{% load static %}

{% block title %}
  Delete Show - {{ show.name }}
{% endblock %}

{% block content %}
  <div class="max-w-md mx-auto mt-8">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center">
          <a href="{% url 'shows:show_detail' show.pk %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
          <h1 class="text-xl font-semibold text-gray-900">Delete Show</h1>
        </div>
      </div>

      <div class="p-6">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
          </div>

          <h3 class="text-lg font-medium text-gray-900 mb-2">Are you sure you want to delete this show?</h3>

          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div class="text-left">
              <p class="font-medium text-gray-900">{{ show.name }}</p>
              {% if show.description %}
                <p class="text-sm text-gray-600 mt-1">{{ show.description|truncatewords:15 }}</p>
              {% endif %}
              <div class="mt-2 flex items-center space-x-4">
                <span class="text-sm text-gray-500">
                  <i class="fa-solid fa-clock mr-1"></i>
                  {{ show.start_time|time:'g:i A' }} - {{ show.end_time|time:'g:i A' }}
                </span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if show.is_active %}
                    



                    bg-green-100 text-green-800




                  {% else %}
                    



                    bg-red-100 text-red-800




                  {% endif %}">
                  {% if show.is_active %}
                    Active
                  {% else %}
                    Inactive
                  {% endif %}
                </span>
              </div>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Airs on:{% for day_num in show.days_of_week %}
                    {% if day_num == 0 %}
                      Monday
                    {% elif day_num == 1 %}
                      Tuesday
                    {% elif day_num == 2 %}
                      Wednesday
                    {% elif day_num == 3 %}
                      Thursday
                    {% elif day_num == 4 %}
                      Friday
                    {% elif day_num == 5 %}
                      Saturday
                    {% elif day_num == 6 %}
                      Sunday
                    {% endif %}
                    {% if not forloop.last %}, {% endif %}
                  {% empty %}
                    No days scheduled
                  {% endfor %}
                </p>
              </div>
            </div>
          </div>

          <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Warning</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>This action cannot be undone. Deleting this show will:</p>
                  <ul class="list-disc list-inside mt-2 space-y-1">
                    <li>Remove the show permanently</li>
                    <li>Cancel all scheduled mentions for this show</li>
                    <li>Remove presenter assignments</li>
                    <li>Delete all historical data</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Show scheduled mentions if any -->
          {% if show.mentionreading.exists %}
            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fa-solid fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Scheduled Mentions Found</h3>
                  <div class="mt-2 text-sm text-red-700">
                    <p>
                      This show has <strong>{{ show.mentionreading.count }}</strong> scheduled mention(s) that will be cancelled:
                    </p>
                    <ul class="list-disc list-inside mt-2 space-y-1 max-h-24 overflow-y-auto">
                      {% for reading in show.mentionreading.all|slice:':5' %}
                        <li>{{ reading.mention.title }} - {{ reading.scheduled_date|date:'M d' }}</li>
                      {% endfor %}
                      {% if show.mentionreading.count > 5 %}
                        <li class="font-medium">... and {{ show.mentionreading.count|add:'-5' }} more</li>
                      {% endif %}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          {% endif %}

          <!-- Show presenters if any -->
          {% if show.presenters.exists %}
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fa-solid fa-info-circle text-blue-400"></i>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-blue-800">Assigned Presenters</h3>
                  <div class="mt-2 text-sm text-blue-700">
                    <p>The following presenters are assigned to this show:</p>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                      {% for presenter in show.presenters.all %}
                        <li>
                          {{ presenter.get_full_name }}{% if presenter.stage_name %}
                            ({{ presenter.stage_name }})
                          {% endif %}
                        </li>
                      {% endfor %}
                    </ul>
                    <p class="mt-2 text-xs">Note: Presenters will not be deleted, only unassigned from this show.</p>
                  </div>
                </div>
              </div>
            </div>
          {% endif %}

          <form method="post" class="space-y-4">
            {% csrf_token %}
            <div class="flex justify-center space-x-3">
              <a href="{% url 'shows:show_detail' show.pk %}" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</a>
              <button type="submit" class="bg-red-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <i class="fa-solid fa-trash mr-2"></i>
                Delete Show
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
