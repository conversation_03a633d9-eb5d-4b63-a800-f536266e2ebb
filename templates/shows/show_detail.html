{% extends 'base.html' %}
{% load static %}

{% block title %}
  {{ show.name }} - Show Details
{% endblock %}

{% block content %}
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <a href="{% url 'shows:show_list' %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
            <h1 class="text-xl font-semibold text-gray-900">Show Details</h1>
          </div>
          <div class="flex space-x-2">
            <a href="{% url 'shows:show_edit' show.pk %}" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
              <i class="fa-solid fa-edit mr-1"></i>
              Edit
            </a>
            <button onclick="deleteShow({{ show.pk }})" class="bg-red-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-red-700">
              <i class="fa-solid fa-trash mr-1"></i>
              Delete
            </button>
          </div>
        </div>
      </div>

      <!-- Show Info -->
      <div class="p-6">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h2 class="text-2xl font-bold text-gray-900">{{ show.name }}</h2>
            {% if show.description %}
              <p class="text-lg text-gray-600 mt-2">{{ show.description }}</p>
            {% endif %}
            <div class="flex items-center mt-4 space-x-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if show.is_active %}
                  
                  
                  
                  
                  bg-green-100 text-green-800




                {% else %}
                  
                  
                  
                  
                  bg-red-100 text-red-800




                {% endif %}">
                {% if show.is_active %}
                  Active
                {% else %}
                  Inactive
                {% endif %}
              </span>
              <span class="text-sm text-gray-500">
                <i class="fa-solid fa-clock mr-1"></i>
                {{ show.start_time|time:'g:i A' }} - {{ show.end_time|time:'g:i A' }}
              </span>
            </div>
          </div>
          <div class="text-right">
            <p class="text-sm text-gray-500">Duration</p>
            <p class="text-lg font-medium text-gray-900">{{ show.duration_display }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Schedule -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Schedule</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-7 gap-2">
          {% for day_num, day_name in days_of_week %}
            <div class="text-center">
              <div class="w-12 h-12 mx-auto rounded-full flex items-center justify-center text-sm font-medium
                            {% if day_num in show.days_of_week %}
                  
                  
                  
                  
                  
                                bg-primary-100 text-primary-800 border-2 border-primary-300





                {% else %}
                  
                  
                  
                  
                  
                                bg-gray-100 text-gray-400 border-2 border-gray-200





                {% endif %}">{{ day_name|slice:':3' }}</div>
              <p class="text-xs text-gray-500 mt-1">{{ day_name }}</p>
            </div>
          {% endfor %}
        </div>
        <div class="mt-4 text-center">
          <p class="text-sm text-gray-600">
            Airs on:{% for day_num in show.days_of_week %}
              {% for day_number, day_name in days_of_week %}
                {% if day_number == day_num %}
                  {{ day_name }}{% if not forloop.last %}, {% endif %}
                {% endif %}
              {% endfor %}
            {% empty %}
              No days scheduled
            {% endfor %}
          </p>
        </div>
      </div>
    </div>

    <!-- Presenters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">Presenters</h3>
          <button onclick="addPresenter()" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
            <i class="fa-solid fa-plus mr-1"></i>
            Add Presenter
          </button>
        </div>
      </div>
      <div class="p-6">
        {% if show.active_presenters %}
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for show_presenter in show.active_presenters %}
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-center space-x-3">
                  {% if show_presenter.presenter.profile_picture %}
                    <img src="{{ show_presenter.presenter.profile_picture.url }}" alt="{{ show_presenter.presenter.get_full_name }}" class="w-12 h-12 rounded-full object-cover" />
                  {% else %}
                    <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      <i class="fa-solid fa-user text-gray-400"></i>
                    </div>
                  {% endif %}
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900">{{ show_presenter.presenter.get_full_name }}</h4>
                    <p class="text-sm text-gray-600">
                      {{ show_presenter.role }}{% if show_presenter.is_primary %} (Primary){% endif %}
                    </p>
                    {% if show_presenter.presenter.stage_name %}
                      <p class="text-sm text-gray-600">{{ show_presenter.presenter.stage_name }}</p>
                    {% endif %}
                    {% if show_presenter.presenter.email %}
                      <p class="text-sm text-gray-500">{{ show_presenter.presenter.email }}</p>
                    {% endif %}
                  </div>
                  <a href="{% url 'core:presenter_detail' show_presenter.presenter.pk %}" class="text-primary-600 hover:text-primary-700"><i class="fa-solid fa-external-link-alt"></i></a>
                </div>
              </div>
            {% endfor %}
          </div>
        {% else %}
          <p class="text-gray-500">No presenters assigned to this show.</p>
        {% endif %}
      </div>
    </div>

    <!-- Recent Mentions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">Recent Mentions</h3>
          <a href="{% url 'mentions:calendar' %}?show={{ show.pk }}" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
            <i class="fa-solid fa-calendar-plus mr-1"></i>
            Schedule Mention
          </a>
        </div>
      </div>
      <div class="p-6">
        {% if recent_mentions %}
          <div class="space-y-3">
            {% for reading in recent_mentions %}
              <div class="border border-gray-200 rounded-lg p-3">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="font-medium text-gray-900">{{ reading.mention.title }}</h4>
                    <p class="text-sm text-gray-600">{{ reading.mention.client.name }}</p>
                    <p class="text-sm text-gray-500">{{ reading.scheduled_date|date:'M d, Y' }} at {{ reading.scheduled_time|time:'g:i A' }}</p>
                  </div>
                  <div class="text-right">
                    {% if reading.actual_read_time %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Completed</span>
                    {% else %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Scheduled</span>
                    {% endif %}
                  </div>
                </div>
              </div>
            {% endfor %}
          </div>
        {% else %}
          <p class="text-gray-500">No recent mentions for this show.</p>
        {% endif %}
      </div>
    </div>

    <!-- Statistics -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Statistics</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900">{{ total_mentions }}</p>
            <p class="text-sm text-gray-500">Total Mentions</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900">{{ completed_mentions }}</p>
            <p class="text-sm text-gray-500">Completed</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900">{{ scheduled_mentions }}</p>
            <p class="text-sm text-gray-500">Scheduled</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900">{{ avg_duration|floatformat:0 }}s</p>
            <p class="text-sm text-gray-500">Avg Duration</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div id="deleteModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3 text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <i class="fa-solid fa-exclamation-triangle text-red-600"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mt-4">Delete Show</h3>
        <div class="mt-2 px-7 py-3">
          <p class="text-sm text-gray-500">Are you sure you want to delete this show? This action cannot be undone.</p>
        </div>
        <div class="items-center px-4 py-3">
          <button id="confirmDelete" class="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-700">Delete</button>
          <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400">Cancel</button>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    function deleteShow(showId) {
      document.getElementById('deleteModal').classList.remove('hidden')
      document.getElementById('confirmDelete').onclick = function () {
        window.location.href = `/shows/${showId}/delete/`
      }
    }
    
    function closeDeleteModal() {
      document.getElementById('deleteModal').classList.add('hidden')
    }
    
    function addPresenter() {
      // This would open a modal to add presenters to the show
      alert('Add presenter functionality would be implemented here')
    }
  </script>
{% endblock %}
