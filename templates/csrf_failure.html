{% extends 'base.html' %}
{% load static %}

{% block title %}CSRF Verification Failed - RadioMention{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-red-100">
            <svg class="h-16 w-16 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
        </div>

        <!-- Error Content -->
        <div>
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Security Check Failed</h1>
            <h2 class="text-xl font-semibold text-gray-700 mb-4">CSRF Verification Failed</h2>
            <p class="text-gray-600 mb-8">
                Your request was blocked for security reasons. This usually happens when your session has expired or when there's a security token mismatch.
            </p>
        </div>

        <!-- Security Information -->
        <div class="bg-blue-50 rounded-lg p-4 mb-6">
            <h3 class="font-medium text-blue-800 mb-2">What is CSRF protection?</h3>
            <p class="text-sm text-blue-700">
                Cross-Site Request Forgery (CSRF) protection prevents malicious websites from performing actions on your behalf. This security measure ensures that requests come from legitimate sources.
            </p>
        </div>

        <!-- Common Causes -->
        <div class="bg-yellow-50 rounded-lg p-4 mb-6">
            <h3 class="font-medium text-yellow-800 mb-2">This usually happens when:</h3>
            <ul class="text-sm text-yellow-700 text-left space-y-1">
                <li>• Your session has expired (you've been inactive too long)</li>
                <li>• You opened the form in multiple browser tabs</li>
                <li>• Your browser cookies are disabled</li>
                <li>• You navigated back to a form after submitting it</li>
                <li>• There's a network connectivity issue</li>
            </ul>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
            <button onclick="location.reload()" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh and Try Again
            </button>
            
            <button onclick="history.back()" class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Go Back
            </button>

            <a href="{% url 'core:dashboard' %}" class="w-full flex justify-center py-3 px-4 border border-green-300 rounded-md shadow-sm text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                Go to Dashboard
            </a>
        </div>

        <!-- Troubleshooting Steps -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Troubleshooting Steps</h3>
            <div class="text-sm text-gray-600 text-left space-y-3">
                <div class="flex items-start">
                    <span class="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">1</span>
                    <div>
                        <strong>Refresh the page</strong> - This will get you a new security token
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">2</span>
                    <div>
                        <strong>Enable cookies</strong> - Make sure your browser accepts cookies from this site
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">3</span>
                    <div>
                        <strong>Close other tabs</strong> - If you have multiple tabs open with forms, close them
                    </div>
                </div>
                <div class="flex items-start">
                    <span class="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">4</span>
                    <div>
                        <strong>Clear browser cache</strong> - Clear your browser's cache and cookies for this site
                    </div>
                </div>
            </div>
        </div>

        <!-- Session Information -->
        {% if user.is_authenticated %}
        <div class="mt-6 p-4 bg-green-50 rounded-lg">
            <h4 class="font-medium text-green-800 mb-2">Session Status</h4>
            <div class="text-sm text-green-700">
                <p><strong>User:</strong> {{ user.username }}</p>
                <p><strong>Status:</strong> Logged in</p>
                <p><strong>Session:</strong> Active</p>
            </div>
        </div>
        {% else %}
        <div class="mt-6 p-4 bg-yellow-50 rounded-lg">
            <h4 class="font-medium text-yellow-800 mb-2">Session Status</h4>
            <p class="text-sm text-yellow-700">You are not currently logged in.</p>
            <a href="{% url 'account_login' %}" class="inline-flex items-center mt-2 text-sm font-medium text-yellow-800 hover:text-yellow-900">
                Login to continue
                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
        {% endif %}

        <!-- Technical Details -->
        <div class="mt-6 p-4 bg-gray-100 rounded-lg">
            <details class="text-left">
                <summary class="font-medium text-gray-900 cursor-pointer">Technical Details</summary>
                <div class="mt-2 text-xs text-gray-600 space-y-1">
                    <div><strong>Error:</strong> CSRF verification failed</div>
                    <div><strong>Time:</strong> <span id="error-time"></span></div>
                    <div><strong>Reason:</strong> {{ reason|default:"Security token mismatch" }}</div>
                    <div><strong>Request:</strong> {{ request.method }} {{ request.path }}</div>
                </div>
            </details>
        </div>
    </div>
</div>

<script>
// Set error time
document.getElementById('error-time').textContent = new Date().toLocaleString();

// Auto-refresh after 30 seconds if user doesn't interact
let autoRefreshTimer = setTimeout(function() {
    if (confirm('Would you like to refresh the page and try again?')) {
        location.reload();
    }
}, 30000);

// Cancel auto-refresh if user clicks anything
document.addEventListener('click', function() {
    clearTimeout(autoRefreshTimer);
});
</script>
{% endblock %}
