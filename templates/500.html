{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error - RadioMention</title>
    <link href="{% static 'css/tailwind.css' %}" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="{% static 'img/favicon.ico' %}">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8 text-center">
            <!-- Error Icon -->
            <div class="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-red-100">
                <svg class="h-16 w-16 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>

            <!-- Error Content -->
            <div>
                <h1 class="text-6xl font-bold text-gray-900 mb-4">500</h1>
                <h2 class="text-2xl font-semibold text-gray-700 mb-4">Internal Server Error</h2>
                <p class="text-gray-600 mb-8">
                    We're experiencing some technical difficulties. Our team has been notified and is working to resolve the issue.
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-4">
                <button onclick="location.reload()" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Try Again
                </button>
                
                <a href="/" class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Go to Homepage
                </a>
            </div>

            <!-- Status Information -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <p class="text-sm text-gray-500 mb-4">Error Information:</p>
                <div class="bg-gray-100 rounded-lg p-4 text-left">
                    <div class="text-xs text-gray-600 space-y-1">
                        <div><strong>Time:</strong> <span id="error-time"></span></div>
                        <div><strong>Error ID:</strong> <span id="error-id"></span></div>
                        <div><strong>Status:</strong> Server Error (500)</div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="mt-6 text-sm text-gray-500">
                <p>If this problem persists, please contact your system administrator.</p>
                <p class="mt-2">
                    <strong>Support:</strong> 
                    <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-500">
                        <EMAIL>
                    </a>
                </p>
            </div>

            <!-- Auto-refresh notice -->
            <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                <p class="text-sm text-blue-700">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    This page will automatically refresh in <span id="countdown">30</span> seconds.
                </p>
            </div>
        </div>
    </div>

    <script>
        // Set error time and ID
        document.getElementById('error-time').textContent = new Date().toLocaleString();
        document.getElementById('error-id').textContent = 'ERR-' + Math.random().toString(36).substr(2, 9).toUpperCase();

        // Countdown and auto-refresh
        let countdown = 30;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(function() {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                location.reload();
            }
        }, 1000);

        // Cancel auto-refresh if user interacts with the page
        document.addEventListener('click', function() {
            clearInterval(timer);
            document.querySelector('.bg-blue-50').style.display = 'none';
        });
    </script>
</body>
</html>
