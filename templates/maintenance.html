{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance - RadioMention</title>
    <link href="{% static 'css/tailwind.css' %}" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="{% static 'img/favicon.ico' %}">
    <meta http-equiv="refresh" content="300"> <!-- Auto-refresh every 5 minutes -->
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8 text-center">
            <!-- Maintenance Icon -->
            <div class="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-blue-100">
                <svg class="h-16 w-16 text-blue-600 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
            </div>

            <!-- Maintenance Content -->
            <div>
                <h1 class="text-4xl font-bold text-gray-900 mb-4">Under Maintenance</h1>
                <h2 class="text-xl font-semibold text-gray-700 mb-4">We'll be back soon!</h2>
                <p class="text-gray-600 mb-8">
                    RadioMention is currently undergoing scheduled maintenance to improve your experience. 
                    We apologize for any inconvenience and appreciate your patience.
                </p>
            </div>

            <!-- Status Information -->
            <div class="bg-blue-50 rounded-lg p-6 mb-6">
                <h3 class="font-medium text-blue-800 mb-3">Maintenance Details</h3>
                <div class="text-sm text-blue-700 space-y-2">
                    <div class="flex justify-between">
                        <span>Status:</span>
                        <span class="font-medium">In Progress</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Started:</span>
                        <span id="maintenance-start">--</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Expected Duration:</span>
                        <span>30-60 minutes</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Next Update:</span>
                        <span id="next-update">--</span>
                    </div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-2 mb-6">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-1000 ease-out" style="width: 0%" id="progress-bar"></div>
            </div>

            <!-- What's Being Updated -->
            <div class="bg-gray-100 rounded-lg p-4 mb-6">
                <h3 class="font-medium text-gray-900 mb-3">What we're working on:</h3>
                <ul class="text-sm text-gray-600 text-left space-y-1">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Database optimization
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Security updates
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-blue-500 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Performance improvements
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10"></circle>
                        </svg>
                        New features deployment
                    </li>
                </ul>
            </div>

            <!-- Contact Information -->
            <div class="text-sm text-gray-500">
                <p class="mb-2">For urgent matters, please contact:</p>
                <p>
                    <strong>Support:</strong> 
                    <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-500">
                        <EMAIL>
                    </a>
                </p>
                <p class="mt-2">
                    <strong>Status Updates:</strong> 
                    <a href="https://status.radiomention.com" class="text-blue-600 hover:text-blue-500">
                        status.radiomention.com
                    </a>
                </p>
            </div>

            <!-- Auto-refresh notice -->
            <div class="mt-6 p-3 bg-green-50 rounded-lg">
                <p class="text-sm text-green-700">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    This page will automatically refresh every 5 minutes to check if maintenance is complete.
                </p>
            </div>

            <!-- Manual Refresh Button -->
            <div class="mt-4">
                <button onclick="location.reload()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Check Again
                </button>
            </div>
        </div>
    </div>

    <script>
        // Set maintenance start time (you can customize this)
        const maintenanceStart = new Date();
        document.getElementById('maintenance-start').textContent = maintenanceStart.toLocaleTimeString();

        // Set next update time (5 minutes from now)
        const nextUpdate = new Date(Date.now() + 5 * 60 * 1000);
        document.getElementById('next-update').textContent = nextUpdate.toLocaleTimeString();

        // Animate progress bar
        let progress = 0;
        const progressBar = document.getElementById('progress-bar');
        
        function updateProgress() {
            progress += Math.random() * 5;
            if (progress > 85) progress = 85; // Don't go to 100% during maintenance
            progressBar.style.width = progress + '%';
        }

        // Update progress every 10 seconds
        setInterval(updateProgress, 10000);
        updateProgress(); // Initial update

        // Add some visual feedback
        document.addEventListener('DOMContentLoaded', function() {
            // Fade in the content
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease-in-out';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
