<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="{{ csrf_token }}" />
    <title>
      {% block title %}
        RadioMention - Radio Mentions Management System
      {% endblock %}
    </title>

    <!-- Load static files -->
    {% load static %}

    <!-- Offline Font Awesome -->
    <link rel="stylesheet" href="{% static 'css/fontawesome.min.css' %}" />
    <script>
      window.FontAwesomeConfig = { autoReplaceSvg: 'nest' }
    </script>
    <script src="{% static 'js/fontawesome.min.js' %}"></script>

    <!-- Offline Inter Font -->
    <link rel="stylesheet" href="{% static 'css/inter-font.css' %}" />

    <!-- Tailwind CSS - Local Build -->
    <link rel="stylesheet" href="{% static 'css/tailwind.css' %}?v=3" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/compact.css' %}" />
    <link rel="stylesheet" href="{% static 'css/print.css' %}" media="print" />

    <!-- IE11 Compatibility CSS -->
    <link rel="stylesheet" href="{% static 'css/ie11-compatibility.css' %}" />

    <style>
      ::-webkit-scrollbar {
        display: none;
      }
      
      body {
        font-family: 'Inter', sans-serif;
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      
      .highlighted-section {
        outline: 2px solid #3f20fb;
        background-color: rgba(63, 32, 251, 0.1);
      }
      
      .edit-button {
        position: absolute;
        z-index: 1000;
      }
      
      /* Notification styles */
      .notification-toast {
        animation: slideInRight 0.3s ease-out;
      }
      
      .notification-toast.removing {
        animation: slideOutRight 0.3s ease-in;
      }
      
      @keyframes slideInRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      
      @keyframes slideOutRight {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
      
      .notification-badge {
        animation: pulse 2s infinite;
      }
    </style>

    {% block extra_css %}

    {% endblock %}
  </head>
  <body class="bg-gray-50">
    <div class="flex h-screen">
      <!-- Sidebar -->
      {% include 'includes/sidebar_organized.html' %}

      <!-- Main content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Header -->
        {% include 'includes/header.html' %}

        <!-- Main content -->
        <main class="flex-1 overflow-y-auto p-4">
          <!-- System Messages -->
          {% if messages %}
            {% for message in messages %}
              <div class="mb-3 p-3 rounded-md text-sm flex items-center {% if message.tags == 'error' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-red-100 border border-red-400 text-red-700









                {% elif message.tags == 'warning' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-yellow-100 border border-yellow-400 text-yellow-700









                {% elif message.tags == 'success' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-green-100 border border-green-400 text-green-700









                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                  bg-blue-100 border border-blue-400 text-blue-700









                {% endif %}">
                <i class="fas {% if message.tags == 'error' %}
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    fa-exclamation-triangle









                  {% elif message.tags == 'warning' %}
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    fa-exclamation-circle









                  {% elif message.tags == 'success' %}
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    fa-check-circle









                  {% else %}
                    
                    
                    
                    
                    
                    
                    
                    
                    
                    fa-info-circle









                  {% endif %} mr-2">

                </i>
                {{ message }}
                <button onclick="this.parentElement.remove()" class="ml-auto text-gray-500 hover:text-gray-700"><i class="fas fa-times"></i></button>
              </div>
            {% endfor %}
          {% endif %}

          <!-- Notification Center -->
          <div id="notification-center" class="fixed top-4 right-4 z-50 max-w-sm space-y-2">
            <!-- Real-time notifications will be dynamically added here -->
          </div>

          {% block content %}

          {% endblock %}
        </main>
      </div>
    </div>

    <!-- Browser Compatibility Detection (load first) -->
    <script src="{% static 'js/browser-compatibility.js' %}"></script>

    <!-- IE11 Compatibility Polyfills -->
    <script src="{% static 'js/polyfills.js' %}"></script>

    <!-- Offline JavaScript Libraries -->
    <script src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/alpine.min.js' %}" defer></script>

    <!-- AJAX Manager - IE11 Compatible Version -->
    <script src="{% static 'js/ajax-manager-compatible.js' %}"></script>

    <!-- Custom Application JavaScript -->
    <script src="{% static 'js/app.js' %}"></script>

    <!-- Print Reports JavaScript -->
    <script src="{% static 'js/print-reports.js' %}"></script>

    <!-- Notification System JavaScript -->
    <script src="{% static 'js/notifications.js' %}?v=2"></script>

    <!-- Browser Compatibility Test Suite (Development Only) -->
    {% if debug %}
      <script src="{% static 'js/browser-test-suite.js' %}"></script>
    {% endif %}

    <!-- Notification System JavaScript -->
    <script>
      // Notification System - IE11 Compatible
      function NotificationSystem() {
        this.container = document.getElementById('notification-center')
        this.notifications = []
        this.maxNotifications = 5
      }
      
      NotificationSystem.prototype.show = function (message, type, duration) {
        type = type || 'info'
        duration = duration !== undefined ? duration : 5000
        var self = this
        var notification = this.createNotification(message, type)
        this.container.appendChild(notification)
        this.notifications.push(notification)
      
        // Auto-remove after duration
        if (duration > 0) {
          setTimeout(function () {
            self.remove(notification)
          }, duration)
        }
      
        // Limit number of notifications
        if (this.notifications.length > this.maxNotifications) {
          this.remove(this.notifications[0])
        }
      
        return notification
      }
      
      NotificationSystem.prototype.createNotification = function (message, type) {
        var notification = document.createElement('div')
        notification.className = 'notification-toast p-4 rounded-lg shadow-lg border-l-4 ' + this.getTypeClasses(type)
      
        notification.innerHTML = '<div class="flex items-start">' + '<div class="flex-shrink-0">' + '<i class="fas ' + this.getTypeIcon(type) + ' text-lg"></i>' + '</div>' + '<div class="ml-3 flex-1">' + '<p class="text-sm font-medium">' + message + '</p>' + '</div>' + '<div class="ml-4 flex-shrink-0">' + '<button onclick="window.notificationSystem.removeByElement(this)" class="text-gray-400 hover:text-gray-600">' + '<i class="fas fa-times"></i>' + '</button>' + '</div>' + '</div>'
      
        return notification
      }
      
      NotificationSystem.prototype.remove = function (notification) {
        var self = this
        if (!notification || !notification.parentNode) return
      
        notification.classList.add('removing')
        setTimeout(function () {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification)
          }
          var index = self.notifications.indexOf(notification)
          if (index > -1) {
            self.notifications.splice(index, 1)
          }
        }, 300)
      }
      
      NotificationSystem.prototype.removeByElement = function (buttonElement) {
        var notification = buttonElement.closest('.notification-toast')
        if (notification) {
          this.remove(notification)
        }
      }
      
      NotificationSystem.prototype.getTypeClasses = function (type) {
        var classes = {
          success: 'bg-green-50 border-green-400 text-green-800',
          error: 'bg-red-50 border-red-400 text-red-800',
          warning: 'bg-yellow-50 border-yellow-400 text-yellow-800',
          info: 'bg-blue-50 border-blue-400 text-blue-800'
        }
        return classes[type] || classes['info']
      }
      
      NotificationSystem.prototype.getTypeIcon = function (type) {
        var icons = {
          success: 'fa-check-circle',
          error: 'fa-exclamation-triangle',
          warning: 'fa-exclamation-circle',
          info: 'fa-info-circle'
        }
        return icons[type] || icons['info']
      }
      
      // Initialize notification system
      window.notificationSystem = new NotificationSystem()
      
      // Auto-dismiss Django messages after 5 seconds
      document.addEventListener('DOMContentLoaded', function () {
        // Only target actual Django messages, not calendar items or other UI components
        var messages = document.querySelectorAll('.alert, .flash-message, .django-message, [role="alert"]')
      
        // Also target messages in the main content area that have message-like structure
        var contentMessages = document.querySelectorAll('.main-content [class*="bg-red-100"][class*="border"], .main-content [class*="bg-yellow-100"][class*="border"], .main-content [class*="bg-green-100"][class*="border"], .main-content [class*="bg-blue-100"][class*="border"]')
      
        // Combine both sets but exclude calendar and UI components
        // Combine both sets but exclude calendar and UI components - IE11 compatible
        var allMessages = []
        for (var i = 0; i < messages.length; i++) {
          allMessages.push(messages[i])
        }
        for (var j = 0; j < contentMessages.length; j++) {
          allMessages.push(contentMessages[j])
        }
      
        // Filter out calendar and UI components
        var filteredMessages = []
        for (var k = 0; k < allMessages.length; k++) {
          var message = allMessages[k]
          if (!message.closest('.calendar-container') && !message.closest('.calendar-day') && !message.closest('.mention-item') && !message.closest('.recurring-mention') && !message.closest('.stats-card') && !message.classList.contains('persistent')) {
            filteredMessages.push(message)
          }
        }
      
        for (var m = 0; m < filteredMessages.length; m++) {
          ;(function (msg) {
            setTimeout(function () {
              if (msg.parentNode) {
                msg.style.opacity = '0'
                setTimeout(function () {
                  if (msg.parentNode) {
                    msg.parentNode.removeChild(msg)
                  }
                }, 300)
              }
            }, 5000)
          })(filteredMessages[m])
        }
      })
    </script>

    {% block extra_js %}

    {% endblock %}
  </body>
</html>
