{% extends 'base.html' %}
{% load static %}

{% block title %}
  Activity Dashboard
{% endblock %}

{% block extra_css %}
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Activity Dashboard</h1>
            <p class="text-gray-600 mt-1">System activity overview for the last 30 days</p>
          </div>
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500">{{ date_range.start|date:'M d' }} - {{ date_range.end|date:'M d, Y' }}</span>
            <button class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
              <i class="fa-solid fa-download mr-2"></i>
              Export Report
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Notice -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <i class="fa-solid fa-info-circle text-blue-400 text-lg"></i>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800">Performance Optimized</h3>
          <div class="mt-2 text-sm text-blue-700">
            <p>
              Auto-refresh is paused by default to improve performance. Click the <i class="fa-solid fa-play text-xs"></i> button in the activity cards to enable real-time updates.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- System Health Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-users text-blue-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-500">Active Users</p>
            <p class="text-2xl font-bold text-gray-800">{{ health_metrics.total_users }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-broadcast-tower text-green-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-500">Active Shows</p>
            <p class="text-2xl font-bold text-gray-800">{{ health_metrics.active_shows }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-clock text-yellow-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-500">Pending Mentions</p>
            <p class="text-2xl font-bold text-gray-800">{{ health_metrics.pending_mentions }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-500">Unresolved Conflicts</p>
            <p class="text-2xl font-bold text-gray-800">{{ health_metrics.unresolved_conflicts }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Top Users -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-800">Most Active Users</h3>
        </div>
        <div class="p-6">
          {% if activity_by_user %}
            <div class="space-y-4">
              {% for user_activity in activity_by_user %}
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <i class="fa-solid fa-user text-gray-500 text-sm"></i>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm font-medium text-gray-900">{{ user_activity.user__username|default:'System' }}</p>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-900">{{ user_activity.count }}</span>
                    <span class="text-xs text-gray-500 ml-1">activities</span>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <p class="text-gray-500 text-center py-4">No user activity data available</p>
          {% endif %}
        </div>
      </div>

      <!-- Recent Conflicts -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800">Recent Conflicts</h3>
            <a href="{% url 'activity_logs:conflict_logs' %}" class="text-sm text-primary-600 hover:text-primary-700">View all</a>
          </div>
        </div>
        <div class="p-6">
          {% if recent_conflicts %}
            <div class="space-y-4">
              {% for conflict in recent_conflicts %}
                <div class="border-l-4 border-red-400 pl-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <p class="text-sm font-medium text-gray-900">{{ conflict.get_conflict_type_display }}</p>
                      <p class="text-xs text-gray-600 mt-1">{{ conflict.description|truncatewords:10 }}</p>
                      <div class="flex items-center mt-2">
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-{{ conflict.severity }}-100 text-{{ conflict.severity }}-800">{{ conflict.get_severity_display }}</span>
                        <span class="ml-2 text-xs text-gray-500">{{ conflict.created_at|timesince }} ago</span>
                      </div>
                    </div>
                    <div class="ml-4">
                      {% if conflict.status == 'detected' %}
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Unresolved</span>
                      {% else %}
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">{{ conflict.get_status_display }}</span>
                      {% endif %}
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-4">
              <i class="fa-solid fa-check-circle text-green-400 text-2xl mb-2"></i>
              <p class="text-gray-500">No recent conflicts</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Dashboard is now fully optimized - no charts or auto-loading features
    console.log('Activity Dashboard loaded - Performance optimized')
  </script>
{% endblock %}
