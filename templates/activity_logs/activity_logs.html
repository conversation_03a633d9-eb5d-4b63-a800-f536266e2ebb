{% extends 'base.html' %}
{% load static %}

{% block title %}Activity Logs{% endblock %}

{% block extra_css %}
<style>
.activity-item {
    border-left: 3px solid #e5e7eb;
    padding-left: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 8px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #6b7280;
}

.activity-item.create {
    border-left-color: #10b981;
}

.activity-item.create::before {
    background: #10b981;
}

.activity-item.update {
    border-left-color: #f59e0b;
}

.activity-item.update::before {
    background: #f59e0b;
}

.activity-item.delete {
    border-left-color: #ef4444;
}

.activity-item.delete::before {
    background: #ef4444;
}

.activity-item.login {
    border-left-color: #3b82f6;
}

.activity-item.login::before {
    background: #3b82f6;
}

.activity-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    display: inline-block;
    margin-right: 0.5rem;
}

.badge-create {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-update {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-delete {
    background-color: #fee2e2;
    color: #991b1b;
}

.badge-login {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-logout {
    background-color: #f3f4f6;
    color: #374151;
}

.filter-tab {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.filter-tab.active {
    background: #3b82f6;
    color: white;
}

.filter-tab:not(.active) {
    background: #f3f4f6;
    color: #6b7280;
}

.filter-tab:not(.active):hover {
    background: #e5e7eb;
}
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">Activity Logs</h1>
                    <div class="ml-4 text-sm text-gray-500">
                        Track all system changes and user activities
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <form method="GET" class="flex items-center space-x-4">
                        <div class="relative">
                            <input type="text" name="search" value="{{ filters.search }}" placeholder="Search activities..." 
                                   class="pl-9 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <i class="fa-solid fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                        <select name="date" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500">
                            <option value="">All dates</option>
                            <option value="today" {% if filters.date == 'today' %}selected{% endif %}>Today</option>
                            <option value="yesterday" {% if filters.date == 'yesterday' %}selected{% endif %}>Yesterday</option>
                        </select>
                        <button type="submit" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
                            <i class="fa-solid fa-filter mr-2"></i>
                            Filter
                        </button>
                    </form>
                    <button class="px-4 py-2 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 flex items-center">
                        <i class="fa-solid fa-download mr-2"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Notice -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <i class="fa-solid fa-check-circle text-green-400 text-lg"></i>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-green-800">Performance Optimized</h3>
          <div class="mt-2 text-sm text-green-700">
            <p>This page has been optimized for better performance. Results are cached and pagination is limited to 25 items per page.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Activity Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-clock-rotate-left text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">Total Activities</p>
                    <p class="text-2xl font-bold text-gray-800">{{ stats.total_logs }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-calendar-day text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">Today</p>
                    <p class="text-2xl font-bold text-gray-800">{{ stats.today_logs }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">Errors</p>
                    <p class="text-2xl font-bold text-gray-800">{{ stats.error_logs }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-triangle-exclamation text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-gray-500">Warnings</p>
                    <p class="text-2xl font-bold text-gray-800">{{ stats.warning_logs }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Filter Activities</h3>
            <a href="{% url 'activity_logs:activity_logs' %}" class="text-sm text-primary-600 hover:text-primary-700">Clear all filters</a>
        </div>
        <div class="flex flex-wrap gap-3">
            <a href="?action=" class="filter-tab {% if not filters.action %}active{% endif %}">All Activities</a>
            {% for action in actions %}
                <a href="?action={{ action }}" class="filter-tab {% if filters.action == action %}active{% endif %}">{{ action|title }}</a>
            {% endfor %}
        </div>
    </div>

    <!-- Activity Timeline -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800">Recent Activities</h3>
        </div>
        <div class="p-6">
            {% if page_obj %}
                <div class="space-y-6">
                    {% for log in page_obj %}
                        <div class="activity-item {{ log.action|slice:':6' }}">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <span class="activity-badge badge-{{ log.action|slice:':6' }}">{{ log.get_action_display|upper }}</span>
                                        <span class="text-sm font-medium text-gray-900">{{ log.description }}</span>
                                    </div>
                                    <div class="flex items-center text-xs text-gray-500">
                                        {% if log.user %}
                                            <span>{{ log.user.get_full_name|default:log.user.username }}</span>
                                        {% else %}
                                            <span>System</span>
                                        {% endif %}
                                        <span class="mx-2">•</span>
                                        <span>{{ log.created_at|timesince }} ago</span>
                                        {% if log.ip_address %}
                                            <span class="mx-2">•</span>
                                            <span>IP: {{ log.ip_address }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="text-xs text-gray-400">
                                    {{ log.created_at|date:"M d, H:i" }}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <div class="mt-6 flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
                        </div>
                        <div class="flex space-x-2">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Previous</a>
                            {% endif %}
                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">Next</a>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            {% else %}
                <div class="text-center py-12">
                    <i class="fa-solid fa-clock-rotate-left text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No activity logs found</h3>
                    <p class="text-gray-500">No activities match your current filters.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function filterActivities(type) {
    // Remove active class from all tabs
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Add active class to clicked tab
    event.target.classList.add('active');

    // Filter activities (this would typically make an AJAX call)
    console.log('Filtering by:', type);
}
</script>
{% endblock %}
