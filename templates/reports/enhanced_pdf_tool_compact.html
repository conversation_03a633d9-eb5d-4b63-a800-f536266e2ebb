{% extends 'base.html' %}
{% load static %}

{% block title %}
  PDF Generator - AppRadio
{% endblock %}

{% block extra_css %}
  <style>
    .pdf-card {
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      transition: all 0.2s ease;
      background: white;
    }
    .pdf-card:hover {
      border-color: #3b82f6;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
    }
    .pdf-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      color: white;
      transition: all 0.2s ease;
      border-radius: 6px;
    }
    .pdf-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      color: white;
    }
    .compact-container {
      max-width: 800px;
      margin: 0 auto;
    }
    .quick-btn {
      padding: 12px 16px;
      border-radius: 6px;
      border: 1px solid #e5e7eb;
      background: white;
      transition: all 0.2s ease;
      text-decoration: none;
      color: #374151;
      display: block;
    }
    .quick-btn:hover {
      border-color: #3b82f6;
      background: #f8fafc;
      color: #1d4ed8;
      text-decoration: none;
    }
    .status-alert {
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 0.875rem;
      margin-top: 12px;
    }
    .form-compact {
      background: #f8fafc;
      padding: 16px;
      border-radius: 6px;
      border: 1px solid #e5e7eb;
    }
    .text-purple {
      color: #8b5cf6;
    }
    .quick-btn {
      min-height: 90px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .quick-btn .text-center {
      width: 100%;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="container-fluid" style="max-width: 1000px;">
    <!-- Header -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center py-3 border-bottom">
          <div>
            <h4 class="mb-1"><i class="fas fa-file-pdf text-primary me-2"></i>PDF Generator</h4>
            <small class="text-muted">Generate professional reports instantly</small>
          </div>
          <button onclick="generateQuickPDF('mentions-summary')" class="btn pdf-btn btn-sm"><i class="fas fa-download me-1"></i>Sample PDF</button>
        </div>
      </div>
    </div>

    <!-- Main Grid Layout -->
    <div class="row g-3">
      <!-- Quick Actions Grid -->
      <div class="col-lg-8">
        <h6 class="mb-3"><i class="fas fa-bolt text-primary me-1"></i>Quick Actions</h6>
        <div class="row g-2 mb-3">
          <div class="col-md-6 col-lg-4">
            <a href="#" onclick="generatePDF('mentions-summary'); return false;" class="quick-btn">
              <div class="text-center">
                <i class="fas fa-bullhorn text-primary mb-2" style="font-size: 1.5rem;"></i>
                <div class="fw-semibold">Mentions Summary</div>
                <small class="text-muted">Key metrics & mentions</small>
              </div>
            </a>
          </div>
          <div class="col-md-6 col-lg-4">
            <a href="#" onclick="generatePDF('analytics'); return false;" class="quick-btn">
              <div class="text-center">
                <i class="fas fa-chart-line text-success mb-2" style="font-size: 1.5rem;"></i>
                <div class="fw-semibold">Analytics Report</div>
                <small class="text-muted">Performance & trends</small>
              </div>
            </a>
          </div>
          <div class="col-md-6 col-lg-4">
            <a href="#" onclick="generatePDF('client-activity'); return false;" class="quick-btn">
              <div class="text-center">
                <i class="fas fa-users text-info mb-2" style="font-size: 1.5rem;"></i>
                <div class="fw-semibold">Client Activity</div>
                <small class="text-muted">Client performance</small>
              </div>
            </a>
          </div>
          <div class="col-md-6 col-lg-4">
            <a href="#" onclick="generatePDF('daily-summary'); return false;" class="quick-btn">
              <div class="text-center">
                <i class="fas fa-calendar-day text-warning mb-2" style="font-size: 1.5rem;"></i>
                <div class="fw-semibold">Daily Summary</div>
                <small class="text-muted">Today's performance</small>
              </div>
            </a>
          </div>
          <div class="col-md-6 col-lg-4">
            <a href="#" onclick="generatePDF('weekly-trends'); return false;" class="quick-btn">
              <div class="text-center">
                <i class="fas fa-chart-area text-purple mb-2" style="font-size: 1.5rem;"></i>
                <div class="fw-semibold">Weekly Trends</div>
                <small class="text-muted">Week analysis</small>
              </div>
            </a>
          </div>
          <div class="col-md-6 col-lg-4">
            <a href="#" onclick="generatePDF('client-revenue'); return false;" class="quick-btn">
              <div class="text-center">
                <i class="fas fa-dollar-sign text-success mb-2" style="font-size: 1.5rem;"></i>
                <div class="fw-semibold">Revenue Report</div>
                <small class="text-muted">Financial overview</small>
              </div>
            </a>
          </div>
        </div>
      </div>

      <!-- Stats & Controls Sidebar -->
      <div class="col-lg-4">
        <!-- Quick Stats -->
        <div class="pdf-card p-3 mb-3">
          <h6 class="mb-2"><i class="fas fa-chart-pie text-info me-1"></i>Quick Stats</h6>
          <div class="row text-center">
            <div class="col-6 mb-2">
              <div class="small text-muted">Report Types</div>
              <div class="fw-bold text-primary">15+</div>
            </div>
            <div class="col-6 mb-2">
              <div class="small text-muted">Success Rate</div>
              <div class="fw-bold text-success">100%</div>
            </div>
            <div class="col-6">
              <div class="small text-muted">Avg Time</div>
              <div class="fw-bold text-info">&lt;2s</div>
            </div>
            <div class="col-6">
              <div class="small text-muted">File Size</div>
              <div class="fw-bold text-warning">~4KB</div>
            </div>
          </div>
        </div>

        <!-- Advanced Options -->
        <div class="pdf-card p-3">
          <h6 class="mb-2"><i class="fas fa-cogs text-warning me-1"></i>Advanced Options</h6>
          <div class="mb-2">
            <label class="form-label small fw-semibold">Date Range</label>
            <div class="row">
              <div class="col-6">
                <input type="date" id="startDate" class="form-control form-control-sm" value="{{ default_start_date }}" />
              </div>
              <div class="col-6">
                <input type="date" id="endDate" class="form-control form-control-sm" value="{{ default_end_date }}" />
              </div>
            </div>
          </div>
          <div class="mb-2">
            <label class="form-label small fw-semibold">Report Type</label>
            <select id="advancedReportType" class="form-select form-select-sm">
              {% for report_type, report_info in report_types.items %}
                <option value="{{ report_type }}">{{ report_info.name }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="mb-2">
            <label class="form-label small fw-semibold">Custom Title</label>
            <input type="text" id="customTitle" class="form-control form-control-sm" placeholder="Optional" />
          </div>
          <div class="mb-3">
            <label class="form-label small fw-semibold">Client Filter</label>
            <select id="clientFilter" class="form-select form-select-sm">
              <option value="">All Clients</option>
              {% for client in clients %}
                <option value="{{ client.id }}">{{ client.name }}</option>
              {% endfor %}
            </select>
          </div>
          <button onclick="generateAdvancedPDF()" class="btn pdf-btn btn-sm w-100"><i class="fas fa-magic me-1"></i>Generate Custom PDF</button>
        </div>
      </div>
    </div>

    <!-- All Report Types (Collapsible) -->
    <div class="row mt-4">
      <div class="col-12">
        <div class="pdf-card p-3">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0"><i class="fas fa-list text-secondary me-1"></i>All Report Types</h6>
            <button onclick="toggleAllReports()" class="btn btn-outline-secondary btn-sm"><i class="fas fa-chevron-down" id="toggleIcon"></i></button>
          </div>
          <div id="allReportsSection" style="display: none;" class="mt-3">
            <div class="row g-2">
              {% for report_type, report_info in report_types.items %}
                <div class="col-md-4 col-lg-3 mb-2">
                  <button onclick="generatePDF('{{ report_type }}')" class="btn btn-outline-primary btn-sm w-100 text-start"><i class="{{ report_info.icon }} me-1"></i>{{ report_info.name }}</button>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Status Display -->
    <div class="row mt-3">
      <div class="col-12">
        <div id="statusDisplay" class="status-alert alert-info d-none">
          <div class="d-flex align-items-center">
            <div class="spinner-border spinner-border-sm me-2"></div>
            <span id="statusText">Generating PDF...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // PDF Generation Functions
    
    function generatePDF(reportType, customParams = {}) {
      showStatus('Generating PDF...', 'processing')
    
      const params = new URLSearchParams({
        type: reportType,
        format: 'pdf',
        ...customParams
      })
    
      const startDate = document.getElementById('startDate')?.value
      const endDate = document.getElementById('endDate')?.value
      if (startDate) params.append('start_date', startDate)
      if (endDate) params.append('end_date', endDate)
    
      const url = `{% url 'reports:export_report' %}?${params.toString()}`
    
      // Open in new tab instead of forcing download
      window.open(url, '_blank')
    
      setTimeout(() => {
        showStatus('PDF generated successfully!', 'success')
        setTimeout(hideStatus, 3000)
      }, 1000)
    }
    
    function generateQuickPDF(reportType) {
      generatePDF(reportType)
    }
    
    function generateAdvancedPDF() {
      const reportType = document.getElementById('advancedReportType').value
      const customTitle = document.getElementById('customTitle').value
      const clientId = document.getElementById('clientFilter').value
    
      const customParams = {}
      if (customTitle) customParams.title = customTitle
      if (clientId) customParams.client_id = clientId
    
      generatePDF(reportType, customParams)
    }
    
    function toggleAllReports() {
      const section = document.getElementById('allReportsSection')
      const icon = document.getElementById('toggleIcon')
    
      if (section.style.display === 'none') {
        section.style.display = 'block'
        icon.className = 'fas fa-chevron-up'
      } else {
        section.style.display = 'none'
        icon.className = 'fas fa-chevron-down'
      }
    }
    
    function showStatus(message, type) {
      const statusDisplay = document.getElementById('statusDisplay')
      const statusText = document.getElementById('statusText')
    
      statusText.textContent = message
      statusDisplay.className = `status-alert alert-${type === 'success' ? 'success' : 'info'}`
      statusDisplay.classList.remove('d-none')
    }
    
    function hideStatus() {
      document.getElementById('statusDisplay').classList.add('d-none')
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function () {
      const today = new Date()
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
    
      if (!document.getElementById('startDate').value) {
        document.getElementById('startDate').value = thirtyDaysAgo.toISOString().split('T')[0]
      }
      if (!document.getElementById('endDate').value) {
        document.getElementById('endDate').value = today.toISOString().split('T')[0]
      }
    })
  </script>
{% endblock %}
