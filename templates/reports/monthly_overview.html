{% extends 'base.html' %}
{% load static %}

{% block title %}
  Monthly Overview Report - RadioMention
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Monthly Overview Report</h1>
            <p class="text-sm text-gray-600 mt-1">{{ month_name }} {{ report_year }}</p>
          </div>
          <div class="flex space-x-2">
            <button onclick="window.location.href='{% url 'reports:report_list' %}'" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
              <i class="fa-solid fa-paint-brush mr-1"></i>
              Back to Reports
            </button>
            <!-- PDF Export Button -->
            {% include 'reports/partials/pdf_export_button.html' with report_type='monthly-overview' %}
          </div>
        </div>
      </div>
    </div>

    <!-- Monthly Summary -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-bullhorn text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Monthly Mentions</p>
            <p class="text-2xl font-semibold text-gray-900">{{ monthly_mentions|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-microphone text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Monthly Readings</p>
            <p class="text-2xl font-semibold text-gray-900">{{ monthly_readings|default:0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Weekly Breakdown -->
    {% if weekly_data %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Weekly Breakdown</h3>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Week</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Range</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mentions</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Readings</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for week in weekly_data %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">Week {{ week.week_number }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ week.start_date }} - {{ week.end_date }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ week.mentions|default:0 }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ week.readings|default:0 }}</div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    {% endif %}

    <!-- Top Clients -->
    {% if top_clients %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Top Clients This Month</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            {% for client in top_clients %}
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                      <span class="text-sm font-medium text-blue-600">{{ client.name|first|upper }}</span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ client.name }}</div>
                    <div class="text-sm text-gray-500">{{ client.month_mentions }} mentions this month</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">{{ client.month_mentions }}</div>
                  <div class="text-sm text-gray-500">mentions</div>
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    {% endif %}
  </div>
{% endblock %}
