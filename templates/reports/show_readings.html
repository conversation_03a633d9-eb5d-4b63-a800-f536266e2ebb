{% extends 'base.html' %}
{% load static %}

{% block title %}
  Show Readings Report - RadioMention
{% endblock %}

{% block content %}
  <div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Show Readings Report</h1>
            <p class="text-sm text-gray-600 mt-1">{{ start_date }} to {{ end_date }}</p>
          </div>
          <div class="flex space-x-2 no-print">
            <button onclick="window.location.href='{% url 'reports:report_list' %}'" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
              <i class="fa-solid fa-arrow-left mr-1"></i>
              Back to Reports
            </button>
            <!-- PDF Export Button -->
            {% include 'reports/partials/pdf_export_button.html' with report_type='show-readings' %}

            <button onclick="showFilters()" class="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700">
              <i class="fa-solid fa-filter mr-1"></i>
              Filters
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-broadcast-tower text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Active Shows</p>
            <p class="text-2xl font-semibold text-gray-900">{{ summary_stats.active_shows|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-microphone text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Readings</p>
            <p class="text-2xl font-semibold text-gray-900">{{ summary_stats.total_readings|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-check-circle text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Completed</p>
            <p class="text-2xl font-semibold text-gray-900">{{ summary_stats.total_completed|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-clock text-yellow-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Pending</p>
            <p class="text-2xl font-semibold text-gray-900">{{ summary_stats.total_pending|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-percentage text-indigo-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Completion Rate</p>
            <p class="text-2xl font-semibold text-gray-900">{{ summary_stats.overall_completion_rate|default:0 }}%</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Show Readings Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Show Readings Summary</h3>
      </div>
      <div class="overflow-x-auto">
        {% if show_stats %}
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Readings</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pending</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Rate</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Duration</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for show in show_stats %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                          <i class="fa-solid fa-broadcast-tower text-blue-600 text-sm"></i>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ show.name }}</div>
                        <div class="text-sm text-gray-500">{{ show.description|default:'No description'|truncatechars:40 }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ show.total_readings|default:0 }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ show.completed_readings|default:0 }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ show.pending_readings|default:0 }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: {{ show.completion_rate|default:0 }}%"></div>
                      </div>
                      <span class="text-sm text-gray-600">{{ show.completion_rate|default:0 }}%</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ show.avg_duration|default:0 }}s</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {% if show.start_time and show.end_time %}
                        {{ show.start_time|time:'H:i' }} - {{ show.end_time|time:'H:i' }}
                      {% else %}
                        Not scheduled
                      {% endif %}
                    </div>
                    <div class="text-xs text-gray-500">
                      {{ show.show.weekdays_display|default:'No specific days' }}
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        {% else %}
          <div class="text-center py-12">
            <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
              <i class="fa-solid fa-microphone text-gray-400 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Reading Data Available</h3>
            <p class="text-gray-500">No show readings found for the selected date range.</p>
            <button onclick="showFilters()" class="mt-4 bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">Adjust Filters</button>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Recent Readings -->
    {% if recent_readings %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Recent Readings</h3>
          <p class="text-sm text-gray-600 mt-1">Latest 20 readings in the selected period</p>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mention</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Presenter</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actual Read Time</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for reading in recent_readings %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ reading.scheduled_date|date:'M d, Y' }}</div>
                    <div class="text-sm text-gray-500">{{ reading.scheduled_time|time:'g:i A' }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ reading.show.name }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ reading.mention.title|truncatechars:30 }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ reading.mention.client.name }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ reading.presenter.name|default:'Not assigned' }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if reading.actual_read_time %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Completed</span>
                    {% else %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Pending</span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {% if reading.actual_read_time %}
                        {{ reading.actual_read_time|date:'M d, Y g:i A' }}
                      {% else %}
                        -
                      {% endif %}
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    {% endif %}
  </div>

  <!-- Filters Modal -->
  <div id="filtersModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-5xl shadow-lg rounded-md bg-white max-h-[90vh] overflow-y-auto">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Options</h3>
        <form method="GET" action="{% url 'reports:show_readings' %}">
          <div class="grid grid-cols-2 gap-4">
            <!-- Date Range Section -->
            <div class="col-span-2">
              <h4 class="text-sm font-medium text-gray-900 mb-2">Date Range</h4>
              <div class="grid grid-cols-2 gap-3">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                  <input type="date" name="start_date" value="{{ start_date|date:'Y-m-d' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                  <input type="date" name="end_date" value="{{ end_date|date:'Y-m-d' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
                </div>
              </div>
              <!-- Quick Date Range Buttons -->
              <div class="mt-2 flex flex-wrap gap-2">
                <button type="button" onclick="setDateRange('today')" class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Today</button>
                <button type="button" onclick="setDateRange('yesterday')" class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Yesterday</button>
                <button type="button" onclick="setDateRange('last7days')" class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Last 7 Days</button>
                <button type="button" onclick="setDateRange('last30days')" class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Last 30 Days</button>
                <button type="button" onclick="setDateRange('thismonth')" class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">This Month</button>
                <button type="button" onclick="setDateRange('lastmonth')" class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">Last Month</button>
              </div>
            </div>

            <!-- Show Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Show</label>
              <select name="show_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Shows</option>
                {% for show in all_shows %}
                  <option value="{{ show.id }}"{% if show.id == show_id %} selected{% endif %}>{{ show.name }}</option>
                {% endfor %}
              </select>
            </div>

            <!-- Client Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Client</label>
              <select name="client_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Clients</option>
                {% for client in all_clients %}
                  <option value="{{ client.id }}"{% if client.id == client_id %} selected{% endif %}>{{ client.name }}</option>
                {% endfor %}
              </select>
            </div>

            <!-- Presenter Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Presenter</label>
              <select name="presenter_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Presenters</option>
                {% for presenter in all_presenters %}
                  <option value="{{ presenter.id }}"{% if presenter.id == presenter_id %} selected{% endif %}>
                    {{ presenter.first_name }} {{ presenter.last_name }}{% if presenter.stage_name %} ({{ presenter.stage_name }}){% endif %}
                  </option>
                {% endfor %}
              </select>
            </div>

            <!-- Status Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Statuses</option>
                <option value="completed"{% if status == 'completed' %} selected{% endif %}>Completed</option>
                <option value="pending"{% if status == 'pending' %} selected{% endif %}>Pending</option>
              </select>
            </div>

            <!-- Priority Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
              <select name="priority" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Priorities</option>
                <option value="1"{% if priority == '1' %} selected{% endif %}>Low</option>
                <option value="2"{% if priority == '2' %} selected{% endif %}>Normal</option>
                <option value="3"{% if priority == '3' %} selected{% endif %}>High</option>
                <option value="4"{% if priority == '4' %} selected{% endif %}>Urgent</option>
              </select>
            </div>
          </div>

          <div class="flex justify-between items-center mt-6">
            <button type="button" onclick="clearFilters()" class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200">Clear All</button>
            <div class="flex space-x-3">
              <button type="button" onclick="closeFilters()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">Cancel</button>
              <button type="submit" class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">Apply Filters</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/simplified-template-selector.js' %}"></script>
  <script src="{% static 'js/print-reports.js' %}"></script>
  <script>
    // Override default date functions for this report type
    function getDefaultStartDate() {
      return '{{ start_date|date:"Y-m-d" }}'
    }
    
    function getDefaultEndDate() {
      return '{{ end_date|date:"Y-m-d" }}'
    }
    
    // Initialize print manager
    const printManager = new PrintManager()
    
    function showFilters() {
      document.getElementById('filtersModal').classList.remove('hidden')
    }

    function closeFilters() {
      document.getElementById('filtersModal').classList.add('hidden')
    }

    function clearFilters() {
      // Reset all form fields to default values
      document.querySelector('input[name="start_date"]').value = getDefaultStartDate()
      document.querySelector('input[name="end_date"]').value = getDefaultEndDate()
      document.querySelector('select[name="show_id"]').value = ''
      document.querySelector('select[name="client_id"]').value = ''
      document.querySelector('select[name="presenter_id"]').value = ''
      document.querySelector('select[name="status"]').value = ''
      document.querySelector('select[name="priority"]').value = ''
    }

    function setDateRange(range) {
      const today = new Date()
      const startDateInput = document.querySelector('input[name="start_date"]')
      const endDateInput = document.querySelector('input[name="end_date"]')

      let startDate, endDate

      switch(range) {
        case 'today':
          startDate = endDate = today
          break
        case 'yesterday':
          startDate = endDate = new Date(today.getTime() - 24 * 60 * 60 * 1000)
          break
        case 'last7days':
          startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          endDate = today
          break
        case 'last30days':
          startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
          endDate = today
          break
        case 'thismonth':
          startDate = new Date(today.getFullYear(), today.getMonth(), 1)
          endDate = today
          break
        case 'lastmonth':
          const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
          startDate = lastMonth
          endDate = new Date(today.getFullYear(), today.getMonth(), 0) // Last day of previous month
          break
      }

      startDateInput.value = startDate.toISOString().split('T')[0]
      endDateInput.value = endDate.toISOString().split('T')[0]
    }
  </script>
{% endblock %}
