{% extends 'base.html' %}
{% load static %}

{% block title %}Saved Reports - RadioMention{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">Saved Reports</h1>
                    <p class="text-sm text-gray-600 mt-1">Manage your saved report templates and configurations</p>
                </div>
                <div class="flex space-x-2">
                    <a href="{% url 'reports:report_list' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
                        <i class="fa-solid fa-arrow-left mr-1"></i>
                        Back to Reports
                    </a>
                    <a href="{% url 'reports:custom_report_builder' %}" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-plus mr-1"></i>
                        Create New Report
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Saved Reports List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Your Saved Reports</h3>
        </div>
        <div class="p-6">
            {% if saved_reports %}
                <div class="space-y-4">
                    {% for report in saved_reports %}
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fa-solid fa-bookmark text-blue-600"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ report.name }}</div>
                                    <div class="text-sm text-gray-500">{{ report.description|default:"No description" }}</div>
                                    <div class="text-xs text-gray-400 mt-1">
                                        Created: {{ report.created_at|date:"M d, Y" }} | 
                                        Type: {{ report.report_type|title }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
                                    <i class="fa-solid fa-play mr-1"></i>
                                    Run Report
                                </button>
                                <button class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
                                    <i class="fa-solid fa-edit mr-1"></i>
                                    Edit
                                </button>
                                <button class="bg-red-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-red-700">
                                    <i class="fa-solid fa-trash mr-1"></i>
                                    Delete
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fa-solid fa-bookmark text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Saved Reports</h3>
                    <p class="text-gray-500 mb-4">You haven't saved any report templates yet.</p>
                    <a href="{% url 'reports:custom_report_builder' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-plus mr-2"></i>
                        Create Your First Report
                    </a>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-chart-bar text-blue-600"></i>
                </div>
                <h3 class="ml-3 text-lg font-medium text-gray-900">Quick Reports</h3>
            </div>
            <p class="text-gray-600 mb-4">Generate common reports instantly</p>
            <div class="space-y-2">
                <a href="{% url 'reports:mentions_summary' %}" class="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">
                    <i class="fa-solid fa-bullhorn mr-2"></i>
                    Mentions Summary
                </a>
                <a href="{% url 'reports:show_performance' %}" class="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">
                    <i class="fa-solid fa-broadcast-tower mr-2"></i>
                    Show Performance
                </a>
                <a href="{% url 'reports:client_activity' %}" class="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md">
                    <i class="fa-solid fa-building mr-2"></i>
                    Client Activity
                </a>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-calendar-plus text-green-600"></i>
                </div>
                <h3 class="ml-3 text-lg font-medium text-gray-900">Scheduled Reports</h3>
            </div>
            <p class="text-gray-600 mb-4">Automate your reporting workflow</p>
            <a href="{% url 'reports:schedule_report' %}" class="block w-full bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 text-center">
                <i class="fa-solid fa-calendar-plus mr-2"></i>
                Schedule Reports
            </a>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-cog text-purple-600"></i>
                </div>
                <h3 class="ml-3 text-lg font-medium text-gray-900">Custom Builder</h3>
            </div>
            <p class="text-gray-600 mb-4">Build reports with specific parameters</p>
            <a href="{% url 'reports:custom_report_builder' %}" class="block w-full bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700 text-center">
                <i class="fa-solid fa-plus mr-2"></i>
                Build Custom Report
            </a>
        </div>
    </div>
</div>
{% endblock %}
