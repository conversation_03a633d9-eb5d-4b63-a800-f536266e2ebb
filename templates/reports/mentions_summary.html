{% extends 'base.html' %}
{% load static %}

{% block title %}
  Mentions Summary - RadioMention
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 no-print">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Mentions Summary Report</h1>
            <p class="text-sm text-gray-600 mt-1">{{ start_date }} to {{ end_date }}</p>
          </div>
          <div class="flex space-x-2 no-print">
            <a href="{% url 'reports:report_list' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
              <i class="fa-solid fa-arrow-left mr-1"></i>
              Back to Reports
            </a>
            <!-- PDF Export Button -->
            <button onclick="exportToPDF('mentions-summary')" class="bg-red-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-red-700">
              <i class="fa-solid fa-file-pdf mr-1"></i>
              Export PDF
            </button>
            {% comment %} <button onclick="showTemplateSelector('mentions-summary')" class="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700">
              <i class="fa-solid fa-paint-brush mr-1"></i>
              Generate with Template
            </button> {% endcomment %}
            {% comment %} <button onclick="printManager.showPrintModal()" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
              <i class="fa-solid fa-print mr-1"></i>
              Print Report
            </button> {% endcomment %}
            <div class="relative">
              {% comment %} <button onclick="toggleQuickPrint()" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
                <i class="fa-solid fa-download mr-1"></i>
                Quick Print
                <i class="fa-solid fa-chevron-down ml-1"></i>
              </button> {% endcomment %}
              <div id="quickPrintMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                <div class="py-1">
                  <button onclick="printManager.quickPrintA4()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"><i class="fa-solid fa-file-pdf mr-2"></i>A4 Portrait</button>
                  <button onclick="printManager.quickPrintA5()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"><i class="fa-solid fa-file-pdf mr-2"></i>A5 Portrait</button>
                  <button onclick="printManager.quickPrintLetter()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"><i class="fa-solid fa-file-pdf mr-2"></i>Letter Portrait</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6 page-break-inside-avoid">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-bullhorn text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Mentions</p>
            <p class="text-2xl font-semibold text-gray-900">{{ total_mentions|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-check text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Approved</p>
            <p class="text-2xl font-semibold text-gray-900">{{ approved_mentions|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-clock text-yellow-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Pending</p>
            <p class="text-2xl font-semibold text-gray-900">{{ pending_mentions|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-percentage text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Completion Rate</p>
            <p class="text-2xl font-semibold text-gray-900">{{ completion_rate|default:0 }}%</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 page-break-inside-avoid">
      <!-- Priority Breakdown -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Priority Breakdown</h3>
        </div>
        <div class="p-6">
          {% if priority_breakdown %}
            <div class="space-y-4">
              {% for priority in priority_breakdown %}
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full mr-3  {% if priority.priority == 1 %}
                        
                        
                        
                        
                        
                        
                        
 bg-green-500







                      {% elif priority.priority == 2 %}
                        
                        
                        
                        
                        
                        
                        
 bg-blue-500







                      {% elif priority.priority == 3 %}
                        
                        
                        
                        
                        
                        
                        
 bg-yellow-500







                      {% elif priority.priority == 4 %}
                        
                        
                        
                        
                        
                        
                        
 bg-red-500






                      {% endif %}"></div>
                    <span class="text-sm font-medium text-gray-900">
                      {% if priority.priority == 1 %}
                        Low
                      {% elif priority.priority == 2 %}
                        Normal
                      {% elif priority.priority == 3 %}
                        High
                      {% elif priority.priority == 4 %}
                        Urgent
                      {% endif %}
                    </span>
                  </div>
                  <span class="text-sm text-gray-600">{{ priority.count }}</span>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <p class="text-gray-500 text-center py-8">No priority data available</p>
          {% endif %}
        </div>
      </div>

      <!-- Daily Breakdown -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Daily Activity</h3>
        </div>
        <div class="p-6">
          {% if daily_breakdown %}
            <div class="space-y-3" id="daily-activity-bars">
              {% for day in daily_breakdown %}
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600">{{ day.day }}</span>
                  <div class="flex items-center">
                    <div class="w-24 bg-gray-200 rounded-full h-2 mr-3">
                      <div class="bg-primary-600 h-2 rounded-full daily-bar" data-count="{{ day.count }}"></div>
                    </div>
                    <span class="text-sm font-medium text-gray-900">{{ day.count }}</span>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <p class="text-gray-500 text-center py-8">No daily data available</p>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Status Summary -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 page-break-inside-avoid">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Status Summary</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600">{{ approved_mentions|default:0 }}</div>
            <div class="text-sm text-gray-500">Approved Mentions</div>
            <div class="text-xs text-gray-400 mt-1">
              {% if total_mentions > 0 %}
                {{ approved_mentions|default:0|floatformat:0 }}/{{ total_mentions }} ({% widthratio approved_mentions total_mentions 100 %}%)
              {% else %}
                0%
              {% endif %}
            </div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-yellow-600">{{ pending_mentions|default:0 }}</div>
            <div class="text-sm text-gray-500">Pending Mentions</div>
            <div class="text-xs text-gray-400 mt-1">
              {% if total_mentions > 0 %}
                {{ pending_mentions|default:0|floatformat:0 }}/{{ total_mentions }} ({% widthratio pending_mentions total_mentions 100 %}%)
              {% else %}
                0%
              {% endif %}
            </div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-red-600">{{ rejected_mentions|default:0 }}</div>
            <div class="text-sm text-gray-500">Rejected Mentions</div>
            <div class="text-xs text-gray-400 mt-1">
              {% if total_mentions > 0 %}
                {{ rejected_mentions|default:0|floatformat:0 }}/{{ total_mentions }} ({% widthratio rejected_mentions total_mentions 100 %}%)
              {% else %}
                0%
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/simplified-template-selector.js' %}"></script>
  <script>
    function toggleQuickPrint() {
      const menu = document.getElementById('quickPrintMenu')
      menu.classList.toggle('hidden')
    }
    
    // Close dropdown when clicking outside
    document.addEventListener('click', function (event) {
      const menu = document.getElementById('quickPrintMenu')
      const button = event.target.closest('button[onclick*="toggleQuickPrint"]')
    
      if (!button && !menu.contains(event.target)) {
        menu.classList.add('hidden')
      }
    })
    
    // Fix daily activity bar widths
    function fixDailyActivityBars() {
      const bars = document.querySelectorAll('.daily-bar')
      if (bars.length === 0) return
    
      // Find the maximum count
      let maxCount = 0
      bars.forEach((bar) => {
        const count = parseInt(bar.getAttribute('data-count'))
        if (count > maxCount) maxCount = count
      })
    
      // Set widths based on percentage of maximum
      bars.forEach((bar) => {
        const count = parseInt(bar.getAttribute('data-count'))
        const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0
        bar.style.width = Math.max(percentage, 2) + '%' // Minimum 2% for visibility
      })
    }
    
    // Run on page load
    document.addEventListener('DOMContentLoaded', fixDailyActivityBars)
    
    // PDF Export Function
    function exportToPDF(reportType) {
      // Get current page parameters
      const urlParams = new URLSearchParams(window.location.search)
      urlParams.set('type', reportType)
      urlParams.set('format', 'pdf')
    
      // Create download link
      const exportUrl = `/reports/export/?${urlParams.toString()}`
    
      // Create a temporary link element
      const link = document.createElement('a')
      link.href = exportUrl
      link.download = `${reportType}_${new Date().toISOString().split('T')[0]}.pdf`
    
      // Append to body, click, and remove
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  </script>
{% endblock %}
