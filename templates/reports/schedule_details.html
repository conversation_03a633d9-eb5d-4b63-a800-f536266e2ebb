{% extends 'base.html' %}
{% load static %}

{% block title %}
  Schedule Details - RadioMention
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Schedule Details & Analysis</h1>
            <p class="text-sm text-gray-600 mt-1">Comprehensive overview of recurring patterns, splits, and schedule history</p>
          </div>
          <div class="flex space-x-2">
            <button onclick="window.location.href='{% url 'reports:report_list' %}'" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
              <i class="fa-solid fa-paint-brush mr-1"></i>
              Back to Reports
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-repeat text-blue-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Total Patterns</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.total_patterns }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-play text-green-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Active Patterns</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.active_patterns }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-pause text-red-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Inactive Patterns</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.inactive_patterns }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-bullhorn text-purple-600"></i>
          </div>
          <div class="ml-3">
            <p class="text-xs text-gray-500">Generated Mentions</p>
            <p class="text-xl font-bold text-gray-800">{{ stats.total_generated_mentions }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Frequency Distribution -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Pattern Frequency Distribution</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          {% for frequency, data in stats.frequency_stats.items %}
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ frequency }}</p>
                  <p class="text-xs text-gray-500">{{ data.active }} active of {{ data.count }} total</p>
                </div>
                <div class="text-right">
                  <p class="text-lg font-bold text-gray-800">{{ data.count }}</p>
                  {% if data.count > 0 %}
                    <div class="w-16 bg-gray-200 rounded-full h-2">
                      <div class="bg-blue-600 h-2 rounded-full" style="width: {% widthratio data.active data.count 100 %}%"></div>
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>
          {% empty %}
            <p class="text-gray-500 text-sm">No patterns found</p>
          {% endfor %}
        </div>
      </div>
    </div>

    <!-- Split Schedule History -->
    {% if split_schedules %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Recent Split Schedule Actions</h3>
          <p class="text-sm text-gray-600 mt-1">History of schedule splits and pattern divisions</p>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Original Pattern</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Split Method</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">New Pattern</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for split in split_schedules %}
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ split.log.created_at|date:'M d, Y H:i' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ split.original_title }}</div>
                    <div class="text-sm text-gray-500">ID: {{ split.original_id }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{{ split.split_method|title }}</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ split.log.recurring_mention.title }}</div>
                    <div class="text-sm text-gray-500">Split {{ split.split_label }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ split.log.changed_by.username|default:'System' }}</td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    {% endif %}

    <!-- Recurring Patterns Details -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Recurring Patterns</h3>
        <p class="text-sm text-gray-600 mt-1">Detailed view of all recurring mention patterns</p>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pattern</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Frequency</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {% for pattern in recurring_mentions %}
              <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ pattern.title }}</div>
                  <div class="text-sm text-gray-500">{{ pattern.content|truncatechars:50 }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pattern.client.name }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">{{ pattern.get_frequency_display }}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>
                    {{ pattern.start_date|date:'M d, Y' }} -{% if pattern.end_date %}
                      {{ pattern.end_date|date:'M d, Y' }}
                    {% else %}
                      Ongoing
                    {% endif %}
                  </div>
                  <div class="text-xs text-gray-500">{{ pattern.recurringmentionshow_set.count }} show assignment{{ pattern.recurringmentionshow_set.count|pluralize }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pattern.generated_mentions_count }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {% if pattern.is_active %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                  {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                  {% endif %}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{{ pattern.created_at|date:'M d, Y' }}</div>
                  <div class="text-xs text-gray-500">{{ pattern.created_by.username|default:'System' }}</div>
                </td>
              </tr>
            {% empty %}
              <tr>
                <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">No recurring patterns found</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Recent Audit Activity -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Recent Schedule Changes</h3>
        <p class="text-sm text-gray-600 mt-1">Audit trail of recent schedule modifications</p>
      </div>
      <div class="p-6">
        {% if recent_audit_logs %}
          <div class="space-y-4">
            {% for log in recent_audit_logs %}
              <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fa-solid fa-history text-blue-600 text-xs"></i>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="text-sm font-medium text-gray-900">{{ log.description }}</div>
                  <div class="text-sm text-gray-500">{{ log.recurring_mention.title }} - {{ log.created_at|date:'M d, Y H:i' }}</div>
                  <div class="text-xs text-gray-400">By {{ log.changed_by.username|default:'System' }}</div>
                </div>
                <div class="flex-shrink-0">
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{{ log.get_change_type_display }}</span>
                </div>
              </div>
            {% endfor %}
          </div>
        {% else %}
          <p class="text-gray-500 text-sm">No recent schedule changes found</p>
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_css %}
  <style>
    @media print {
      .no-print {
        display: none !important;
      }
    }
  </style>
{% endblock %}
