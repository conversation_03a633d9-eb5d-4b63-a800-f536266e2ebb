{% extends 'base.html' %}
{% load static %}

{% block title %}
  Custom Report Builder - RadioMention
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Custom Report Builder</h1>
            <p class="text-sm text-gray-600 mt-1">Create custom reports with specific parameters and filters</p>
          </div>
          <div class="flex space-x-2">
            <a href="{% url 'reports:report_list' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
              <i class="fa-solid fa-arrow-left mr-1"></i>
              Back to Reports
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Report Builder Form -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Configuration Panel -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Report Configuration</h3>
          </div>
          <div class="p-6 space-y-6">
            <!-- Saved Templates -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Load Saved Template</label>
              <select id="savedTemplate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" onchange="loadTemplate()">
                <option value="">Select a saved template...</option>
                {% for template in saved_templates %}
                  <option value="{{ template.id }}" data-type="{{ template.report_type }}" data-filters="{{ template.filters|safe }}" data-metrics="{{ template.metrics|safe }}">{{ template.name }} ({{ template.get_report_type_display }})</option>
                {% endfor %}
              </select>
            </div>

            <!-- Report Type -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Report Type</label>
              <select id="reportType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" onchange="updateFiltersAndMetrics()">
                <option value="">Select report type...</option>
                <option value="mentions">Mentions Report</option>
                <option value="clients">Client Report</option>
                <option value="shows">Show Report</option>
                <option value="presenters">Presenter Report</option>
                <option value="performance">Performance Report</option>
              </select>
            </div>

            <!-- Dynamic Filters Section -->
            <div id="filtersSection">
              <label class="block text-sm font-medium text-gray-700 mb-2">Filters</label>
              <div id="filterOptions" class="space-y-3">
                <!-- Filters will be populated dynamically based on report type -->
              </div>
            </div>

            <!-- Dynamic Metrics Section -->
            <div id="metricsSection">
              <label class="block text-sm font-medium text-gray-700 mb-2">Metrics to Include</label>
              <div id="metricOptions" class="space-y-2">
                <!-- Metrics will be populated dynamically based on report type -->
              </div>
            </div>

            <!-- Date Range -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
              <div class="space-y-2">
                <input type="date" id="startDate" placeholder="Start Date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
                <input type="date" id="endDate" placeholder="End Date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
              </div>
            </div>

            <!-- Filters -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Filters</label>

              <!-- Client Filter -->
              <div class="mb-3">
                <label class="block text-xs font-medium text-gray-600 mb-1">Clients</label>
                <select id="clientFilter" multiple class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                  {% for client in clients %}
                    <option value="{{ client.id }}">{{ client.name }}</option>
                  {% endfor %}
                </select>
              </div>

              <!-- Show Filter -->
              <div class="mb-3">
                <label class="block text-xs font-medium text-gray-600 mb-1">Shows</label>
                <select id="showFilter" multiple class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                  {% for show in shows %}
                    <option value="{{ show.id }}">{{ show.name }}</option>
                  {% endfor %}
                </select>
              </div>

              <!-- Status Filter -->
              <div class="mb-3">
                <label class="block text-xs font-medium text-gray-600 mb-1">Status</label>
                <select id="statusFilter" multiple class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="completed">Completed</option>
                </select>
              </div>

              <!-- Priority Filter -->
              <div class="mb-3">
                <label class="block text-xs font-medium text-gray-600 mb-1">Priority</label>
                <select id="priorityFilter" multiple class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                  <option value="1">Low</option>
                  <option value="2">Normal</option>
                  <option value="3">High</option>
                  <option value="4">Urgent</option>
                </select>
              </div>
            </div>

            <!-- Grouping -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Group By</label>
              <select id="groupBy" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">No grouping</option>
                <option value="client">Client</option>
                <option value="show">Show</option>
                <option value="priority">Priority</option>
                <option value="status">Status</option>
                <option value="date">Date</option>
                <option value="week">Week</option>
                <option value="month">Month</option>
              </select>
            </div>

            <!-- Metrics -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Metrics to Include</label>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input type="checkbox" id="metricCount" checked class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                  <span class="ml-2 text-sm text-gray-700">Count</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="metricDuration" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                  <span class="ml-2 text-sm text-gray-700">Duration</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="metricCompletion" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                  <span class="ml-2 text-sm text-gray-700">Completion Rate</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" id="metricApproval" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                  <span class="ml-2 text-sm text-gray-700">Approval Rate</span>
                </label>
              </div>
            </div>

            <!-- Actions -->
            <div class="space-y-3">
              <button onclick="generateCustomReport()" class="w-full bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                <i class="fa-solid fa-chart-bar mr-2"></i>
                Generate Report
              </button>
              <button onclick="saveReportTemplate()" class="w-full bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                <i class="fa-solid fa-save mr-2"></i>
                Save as Template
              </button>
              <button onclick="resetForm()" class="w-full bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                <i class="fa-solid fa-refresh mr-2"></i>
                Reset Form
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Preview Panel -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Report Preview</h3>
          </div>
          <div class="p-6">
            <div id="reportPreview" class="text-center py-12">
              <div class="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                <i class="fa-solid fa-chart-bar text-gray-400 text-2xl"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">No Report Generated</h3>
              <p class="text-gray-500 mb-4">Configure your report settings and click "Generate Report" to see a preview.</p>
              <div class="text-sm text-gray-400">
                <p>• Select a report type</p>
                <p>• Choose date range</p>
                <p>• Apply filters as needed</p>
                <p>• Select metrics to include</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Saved Templates -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Saved Report Templates</h3>
          </div>
          <div class="p-6">
            <div class="text-center py-8">
              <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                <i class="fa-solid fa-bookmark text-gray-400 text-xl"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">No Saved Templates</h3>
              <p class="text-gray-500">Create and save report templates for quick access to frequently used reports.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Save Template Modal -->
  <div id="saveTemplateModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Save Report Template</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
            <input type="text" id="templateName" placeholder="Enter template name..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
            <textarea id="templateDescription" rows="3" placeholder="Describe this report template..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
          </div>
        </div>
        <div class="flex justify-end space-x-3 mt-6">
          <button type="button" onclick="closeSaveTemplate()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">Cancel</button>
          <button type="button" onclick="confirmSaveTemplate()" class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">Save Template</button>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Report type configurations
    const reportConfigs = {
      mentions: {
        filters: [
          { id: 'client', label: 'Client', type: 'select', options: [] },
          { id: 'status', label: 'Status', type: 'select', options: ['pending', 'approved', 'scheduled', 'read', 'rejected'] },
          { id: 'priority', label: 'Priority', type: 'select', options: ['Low', 'Normal', 'High', 'Urgent'] }
        ],
        metrics: ['total_count', 'by_status', 'by_priority', 'completion_rate']
      },
      clients: {
        filters: [
          { id: 'industry', label: 'Industry', type: 'text' },
          { id: 'active_only', label: 'Active Only', type: 'checkbox' }
        ],
        metrics: ['mention_count', 'completion_rate', 'activity_level']
      },
      shows: {
        filters: [
          { id: 'show', label: 'Show', type: 'select', options: [] },
          { id: 'active_only', label: 'Active Only', type: 'checkbox' }
        ],
        metrics: ['mention_count', 'completion_rate', 'presenter_activity']
      },
      presenters: {
        filters: [{ id: 'active_only', label: 'Active Only', type: 'checkbox' }],
        metrics: ['mentions_read', 'completion_rate', 'activity_level']
      },
      analytics: {
        filters: [{ id: 'include_charts', label: 'Include Charts', type: 'checkbox' }],
        metrics: ['overview', 'trends', 'performance']
      }
    }
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function () {
      // Populate options dynamically from server data
      populateSelectOptions()
    })
    
    function populateSelectOptions() {
      // This would typically fetch data from an API endpoint
      // For now, we'll use static options
      reportConfigs.mentions.filters[0].options = ['Client A', 'Client B', 'Client C']
      reportConfigs.shows.filters[0].options = ['Morning Show', 'Evening Show', 'Night Show']
    }
    
    function updateFiltersAndMetrics() {
      const reportType = document.getElementById('reportType').value
      const filtersContainer = document.getElementById('filterOptions')
      const metricsContainer = document.getElementById('metricOptions')
    
      // Clear existing options
      filtersContainer.innerHTML = ''
      metricsContainer.innerHTML = ''
    
      if (!reportType || !reportConfigs[reportType]) return
    
      const config = reportConfigs[reportType]
    
      // Add filters
      config.filters.forEach((filter) => {
        const filterDiv = document.createElement('div')
        filterDiv.className = 'flex items-center space-x-2'
    
        if (filter.type === 'select') {
          let optionsHtml = '<option value="">All</option>'
          filter.options.forEach((opt) => {
            optionsHtml += '<option value="' + opt + '">' + opt + '</option>'
          })
    
          filterDiv.innerHTML = '<label class="text-sm text-gray-600 w-20">' + filter.label + ':</label>' + '<select id="filter_' + filter.id + '" class="flex-1 px-2 py-1 border border-gray-300 rounded text-sm">' + optionsHtml + '</select>'
        } else if (filter.type === 'checkbox') {
          filterDiv.innerHTML = '<input type="checkbox" id="filter_' + filter.id + '" class="rounded">' + '<label for="filter_' + filter.id + '" class="text-sm text-gray-600">' + filter.label + '</label>'
        } else {
          filterDiv.innerHTML = '<label class="text-sm text-gray-600 w-20">' + filter.label + ':</label>' + '<input type="text" id="filter_' + filter.id + '" class="flex-1 px-2 py-1 border border-gray-300 rounded text-sm">'
        }
    
        filtersContainer.appendChild(filterDiv)
      })
    
      // Add metrics
      config.metrics.forEach((metric) => {
        const metricDiv = document.createElement('div')
        metricDiv.className = 'flex items-center space-x-2'
    
        const metricLabel = metric.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())
        metricDiv.innerHTML = '<input type="checkbox" id="metric_' + metric + '" class="rounded" checked>' + '<label for="metric_' + metric + '" class="text-sm text-gray-600">' + metricLabel + '</label>'
    
        metricsContainer.appendChild(metricDiv)
      })
    }
    
    function loadTemplate() {
      const templateSelect = document.getElementById('savedTemplate')
      const selectedOption = templateSelect.options[templateSelect.selectedIndex]
    
      if (!selectedOption.value) return
    
      // Load template data
      const reportType = selectedOption.dataset.type
      const filters = JSON.parse(selectedOption.dataset.filters || '{}')
      const metrics = JSON.parse(selectedOption.dataset.metrics || '[]')
    
      // Set report type
      document.getElementById('reportType').value = reportType
      updateFiltersAndMetrics()
    
      // Wait for filters to be created, then populate them
      setTimeout(() => {
        // Populate filters
        Object.keys(filters).forEach((key) => {
          const filterElement = document.getElementById(`filter_${key}`)
          if (filterElement) {
            if (filterElement.type === 'checkbox') {
              filterElement.checked = filters[key]
            } else {
              filterElement.value = filters[key]
            }
          }
        })
    
        // Populate metrics
        const allMetrics = document.querySelectorAll('[id^="metric_"]')
        allMetrics.forEach((checkbox) => {
          const metricName = checkbox.id.replace('metric_', '')
          checkbox.checked = metrics.includes(metricName)
        })
      }, 100)
    }
    
    function generateCustomReport() {
      const reportType = document.getElementById('reportType').value
      const startDate = document.getElementById('startDate').value
      const endDate = document.getElementById('endDate').value
    
      if (!reportType) {
        alert('Please select a report type')
        return
      }
    
      if (!startDate || !endDate) {
        alert('Please select both start and end dates')
        return
      }
    
      // Show loading state
      const preview = document.getElementById('reportPreview')
      preview.innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fa-solid fa-spinner fa-spin text-primary-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Generating Report...</h3>
                <p class="text-gray-500">Please wait while we process your custom report.</p>
            </div>
        `
    
      // Generate report via API call
      fetch(`/reports/api/generate/${reportType}/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
          start_date: startDate,
          end_date: endDate,
          filters: getSelectedFilters(),
          metrics: getSelectedMetrics()
        })
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            let summaryHtml = ''
            if (data.summary) {
              summaryHtml = '<div class="bg-gray-50 rounded-lg p-4 mb-4"><h4 class="font-medium text-gray-900 mb-2">Summary Statistics</h4>'
              Object.keys(data.summary).forEach((key) => {
                if (typeof data.summary[key] === 'number') {
                  summaryHtml += '<div class="flex justify-between text-sm"><span class="text-gray-600">' + key.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase()) + ':</span><span class="font-medium">' + data.summary[key] + '</span></div>'
                }
              })
              summaryHtml += '</div>'
            }
    
            preview.innerHTML = '<div class="py-6">' + '<div class="flex items-center mb-4">' + '<div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">' + '<i class="fa-solid fa-check text-green-600 text-xl"></i>' + '</div>' + '<div>' + '<h3 class="text-lg font-medium text-gray-900">Report Generated Successfully</h3>' + '<p class="text-sm text-gray-500">' + data.record_count + ' records found for ' + startDate + ' to ' + endDate + '</p>' + '</div>' + '</div>' + summaryHtml + '<div class="flex space-x-2">' + '<button onclick="viewFullReport(\'' + reportType + '\')" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">' + 'View Full Report' + '</button>' + '<button onclick="exportReport(\'' + reportType + '\', \'pdf\')" class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">' + 'Export to PDF' + '</button>' + '<button onclick="showSaveTemplate()" class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">' + 'Save as Template' + '</button>' + '</div>' + '</div>'
          } else {
            preview.innerHTML = '<div class="text-center py-8">' + '<div class="w-16 h-16 mx-auto bg-red-100 rounded-lg flex items-center justify-center mb-4">' + '<i class="fa-solid fa-exclamation-triangle text-red-600 text-2xl"></i>' + '</div>' + '<h3 class="text-lg font-medium text-gray-900 mb-2">Report Generation Failed</h3>' + '<p class="text-gray-500">' + (data.error || 'An error occurred while generating the report.') + '</p>' + '</div>'
          }
        })
        .catch((error) => {
          preview.innerHTML = `
                <div class="text-center py-8">
                    <div class="w-16 h-16 mx-auto bg-red-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fa-solid fa-exclamation-triangle text-red-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Connection Error</h3>
                    <p class="text-gray-500">Unable to connect to the server. Please try again.</p>
                </div>
            `
        })
    }
    
    function saveReportTemplate() {
      const reportType = document.getElementById('reportType').value
      if (!reportType) {
        alert('Please configure a report before saving as template')
        return
      }
      document.getElementById('saveTemplateModal').classList.remove('hidden')
    }
    
    function closeSaveTemplate() {
      document.getElementById('saveTemplateModal').classList.add('hidden')
    }
    
    function confirmSaveTemplate() {
      const templateName = document.getElementById('templateName').value
      if (!templateName) {
        alert('Please enter a template name')
        return
      }
    
      // Save template to backend
      const templateData = {
        name: templateName,
        report_type: document.getElementById('reportType').value,
        start_date: document.getElementById('startDate').value,
        end_date: document.getElementById('endDate').value,
        filters: getSelectedFilters(),
        metrics: getSelectedMetrics()
      }
    
      // For now, just show success message
      alert(`Template "${templateName}" saved successfully!`)
      closeSaveTemplate()
    }
    
    function getSelectedFilters() {
      // Get selected filter values
      return {
        client: document.getElementById('clientFilter')?.value || '',
        show: document.getElementById('showFilter')?.value || '',
        priority: document.getElementById('priorityFilter')?.value || ''
      }
    }
    
    function getSelectedMetrics() {
      // Get selected metrics checkboxes
      const metrics = []
      document.querySelectorAll('input[name="metrics"]:checked').forEach((checkbox) => {
        metrics.push(checkbox.value)
      })
      return metrics
    }
    
    function viewFullReport(reportType) {
      // Navigate to the full report view
      let url = ''
      switch (reportType) {
        case 'mentions':
          url = '{% url "reports:mentions_summary" %}'
          break
        case 'clients':
          url = '{% url "reports:client_activity" %}'
          break
        case 'shows':
          url = '{% url "reports:show_performance" %}'
          break
        default:
          url = '{% url "reports:analytics" %}'
      }
      window.location.href = url
    }
    
    function exportReport(reportType, format) {
      // Trigger report export
      window.location.href = `{% url "reports:export_report" %}?type=${reportType}&format=${format}`
    }
    
    function resetForm() {
      document.getElementById('reportType').value = ''
      document.getElementById('startDate').value = ''
      document.getElementById('endDate').value = ''
      document.getElementById('clientFilter').selectedIndex = -1
      document.getElementById('showFilter').selectedIndex = -1
      document.getElementById('statusFilter').selectedIndex = -1
      document.getElementById('priorityFilter').selectedIndex = -1
      document.getElementById('groupBy').value = ''
    
      // Reset checkboxes
      document.getElementById('metricCount').checked = true
      document.getElementById('metricDuration').checked = false
      document.getElementById('metricCompletion').checked = false
      document.getElementById('metricApproval').checked = false
    
      // Reset preview
      document.getElementById('reportPreview').innerHTML = `
            <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fa-solid fa-chart-bar text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Report Generated</h3>
                <p class="text-gray-500 mb-4">Configure your report settings and click "Generate Report" to see a preview.</p>
                <div class="text-sm text-gray-400">
                    <p>• Select a report type</p>
                    <p>• Choose date range</p>
                    <p>• Apply filters as needed</p>
                    <p>• Select metrics to include</p>
                </div>
            </div>
        `
    }
  </script>
{% endblock %}
