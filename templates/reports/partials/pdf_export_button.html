<div class="pdf-export-section">
  <!-- Export Button -->
  <div class="flex items-center space-x-2">
    <button type="button" onclick="exportToPDF('{{ report_type|default:'custom' }}')" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
      <i class="fa-solid fa-file-pdf mr-2"></i>
      Export PDF
    </button>

    <!-- Export Options Dropdown -->
    <div class="relative" id="exportOptionsDropdown">
      <button type="button" onclick="toggleExportOptions()" class="inline-flex items-center px-3 py-2 border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
        <i class="fa-solid fa-cog mr-1"></i>
        Options
        <i class="fa-solid fa-chevron-down ml-1 text-xs"></i>
      </button>

      <!-- Dropdown Menu -->
      <div id="exportOptionsMenu" class="hidden absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg border border-gray-200 z-50">
        <div class="p-4">
          <h4 class="text-sm font-medium text-gray-900 mb-3">Export Options</h4>

          <!-- Include Logo -->
          <div class="mb-3">
            <label class="flex items-center">
              <input type="checkbox" id="includeLogo" checked class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
              <span class="ml-2 text-sm text-gray-700">Include company logo</span>
            </label>
          </div>

          <!-- Page Orientation -->
          <div class="mb-3">
            <label class="block text-sm font-medium text-gray-700 mb-1">Page Orientation</label>
            <select id="pageOrientation" class="w-full text-sm border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500">
              <option value="portrait">Portrait</option>
              <option value="landscape">Landscape</option>
            </select>
          </div>

          <!-- Font Size -->
          <div class="mb-3">
            <label class="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
            <select id="fontSize" class="w-full text-sm border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500">
              <option value="10">Small (10pt)</option>
              <option value="12" selected>Normal (12pt)</option>
              <option value="14">Large (14pt)</option>
            </select>
          </div>

          <!-- Include Summary -->
          <div class="mb-3">
            <label class="flex items-center">
              <input type="checkbox" id="includeSummary" checked class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
              <span class="ml-2 text-sm text-gray-700">Include summary statistics</span>
            </label>
          </div>

          <!-- Include Charts -->
          <div class="mb-4">
            <label class="flex items-center">
              <input type="checkbox" id="includeCharts" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
              <span class="ml-2 text-sm text-gray-700">Include charts (if available)</span>
            </label>
          </div>

          <!-- Action Buttons -->
          <div class="flex space-x-2">
            <button type="button" onclick="exportToPDFWithOptions('{{ report_type|default:'custom' }}')" class="flex-1 px-3 py-2 bg-primary-600 hover:bg-primary-700 text-white text-sm font-medium rounded-md transition-colors duration-200">Export</button>
            <button type="button" onclick="toggleExportOptions()" class="px-3 py-2 border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 text-sm font-medium rounded-md transition-colors duration-200">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div id="pdfExportLoading" class="hidden mt-2">
    <div class="flex items-center text-sm text-gray-600">
      <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2"></div>Generating PDF...
    </div>
  </div>

  <!-- Success Message -->
  <div id="pdfExportSuccess" class="hidden mt-2 p-2 bg-green-100 border border-green-400 text-green-700 rounded-md text-sm">
    <i class="fa-solid fa-check-circle mr-1"></i>
    PDF exported successfully!
  </div>

  <!-- Error Message -->
  <div id="pdfExportError" class="hidden mt-2 p-2 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
    <i class="fa-solid fa-exclamation-circle mr-1"></i>
    <span id="pdfExportErrorMessage">Failed to export PDF. Please try again.</span>
  </div>
</div>

<script>
  // PDF Export Functions
  function exportToPDF(reportType) {
    showPDFLoading()
  
    // Get current page parameters
    const urlParams = new URLSearchParams(window.location.search)
    urlParams.set('type', reportType)
    urlParams.set('format', 'pdf')
  
    // Create download link
    const exportUrl = `/reports/export/?${urlParams.toString()}`
  
    // Open the PDF in new tab
    openPDFInNewTab(exportUrl, reportType)
  }
  
  function exportToPDFWithOptions(reportType) {
    showPDFLoading()
    toggleExportOptions() // Close the options menu
  
    // Get export options
    const options = {
      includeLogo: document.getElementById('includeLogo').checked,
      pageOrientation: document.getElementById('pageOrientation').value,
      fontSize: document.getElementById('fontSize').value,
      includeSummary: document.getElementById('includeSummary').checked,
      includeCharts: document.getElementById('includeCharts').checked
    }
  
    // Get current page parameters
    const urlParams = new URLSearchParams(window.location.search)
    urlParams.set('type', reportType)
    urlParams.set('format', 'pdf')
  
    // Add export options to URL
    Object.keys(options).forEach((key) => {
      urlParams.set(key, options[key])
    })
  
    // Create export URL
    const exportUrl = `/reports/export/?${urlParams.toString()}`
  
    // Open the PDF in new tab
    openPDFInNewTab(exportUrl, reportType)
  }
  
  function openPDFInNewTab(url, reportType) {
    // Use fetch to check the response first
    fetch(url, {
      method: 'GET',
      credentials: 'same-origin' // Include cookies for authentication
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
  
        // Check if response is actually a PDF
        const contentType = response.headers.get('content-type')
        if (!contentType || !contentType.includes('application/pdf')) {
          throw new Error('Response is not a PDF document')
        }
  
        // If it's a valid PDF, open in new tab instead of downloading
        window.open(url, '_blank')
  
        // Show success message
        hidePDFLoading()
        showPDFSuccess()
        setTimeout(hidePDFMessages, 3000)
      })
      .catch((error) => {
        console.error('PDF export failed:', error)
        hidePDFLoading()
  
        // Show error message
        const errorDiv = document.createElement('div')
        errorDiv.className = 'fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50'
        errorDiv.innerHTML = `
              <strong>PDF Export Failed!</strong><br>
              ${error.message || 'Failed to generate PDF document.'}
            `
        document.body.appendChild(errorDiv)
  
        // Remove error message after 5 seconds
        setTimeout(() => {
          if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv)
          }
        }, 5000)
      })
  }
  
  function toggleExportOptions() {
    const menu = document.getElementById('exportOptionsMenu')
    menu.classList.toggle('hidden')
  }
  
  function showPDFLoading() {
    hidePDFMessages()
    document.getElementById('pdfExportLoading').classList.remove('hidden')
  }
  
  function hidePDFLoading() {
    document.getElementById('pdfExportLoading').classList.add('hidden')
  }
  
  function showPDFSuccess() {
    document.getElementById('pdfExportSuccess').classList.remove('hidden')
  }
  
  function showPDFError(message = null) {
    if (message) {
      document.getElementById('pdfExportErrorMessage').textContent = message
    }
    document.getElementById('pdfExportError').classList.remove('hidden')
  }
  
  function hidePDFMessages() {
    document.getElementById('pdfExportSuccess').classList.add('hidden')
    document.getElementById('pdfExportError').classList.add('hidden')
  }
  
  // Close dropdown when clicking outside
  document.addEventListener('click', function (event) {
    const dropdown = document.getElementById('exportOptionsDropdown')
    const menu = document.getElementById('exportOptionsMenu')
  
    if (dropdown && !dropdown.contains(event.target)) {
      menu.classList.add('hidden')
    }
  })
  
  // Handle PDF export errors
  window.addEventListener('error', function (event) {
    if (event.target && event.target.tagName === 'A' && event.target.href.includes('export')) {
      hidePDFLoading()
      showPDFError('Failed to download PDF. Please check your connection and try again.')
    }
  })
</script>

<style>
  .pdf-export-section {
    margin-bottom: 1rem;
  }
  
  @media (max-width: 640px) {
    .pdf-export-section .flex {
      flex-direction: column;
      space-x: 0;
      gap: 0.5rem;
    }
  
    #exportOptionsMenu {
      right: 0;
      left: 0;
      width: auto;
    }
  }
</style>
