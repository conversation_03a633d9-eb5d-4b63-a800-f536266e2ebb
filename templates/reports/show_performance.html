{% extends 'base.html' %}
{% load static %}

{% block title %}
  Show Performance Report - RadioMention
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Show Performance Report</h1>
            <p class="text-sm text-gray-600 mt-1">{{ start_date }} to {{ end_date }}</p>
          </div>
          <div class="flex space-x-2 no-print">
            <button onclick="window.location.href='{% url 'reports:report_list' %}'" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
              <i class="fa-solid fa-paint-brush mr-1"></i>
              Back to Reports
            </button>
            <!-- PDF Export Button -->
            {% include 'reports/partials/pdf_export_button.html' with report_type='show-performance' %}

            <button onclick="showDateFilter()" class="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700">
              <i class="fa-solid fa-filter mr-1"></i>
              Filter Dates
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Show Performance Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Show Performance Metrics</h3>
      </div>
      <div class="overflow-x-auto">
        {% if show_stats %}
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Mentions</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Rate</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Duration</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for show in show_stats %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <div class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                          <i class="fa-solid fa-broadcast-tower text-green-600 text-sm"></i>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ show.name }}</div>
                        <div class="text-sm text-gray-500">{{ show.description|default:'No description'|truncatechars:50 }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ show.total_mentions|default:0 }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ show.completed_mentions|default:0 }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: {{ show.completion_rate|default:0 }}%"></div>
                      </div>
                      <span class="text-sm text-gray-600">{{ show.completion_rate|default:0 }}%</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ show.avg_duration|default:0 }}s</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {% if show.start_time and show.end_time %}
                        {{ show.start_time|time:'H:i' }} - {{ show.end_time|time:'H:i' }}
                      {% else %}
                        Not scheduled
                      {% endif %}
                    </div>
                    <div class="text-xs text-gray-500">
                      {% if show.days_of_week %}
                        {{ show.days_of_week }}
                      {% else %}
                        No specific days
                      {% endif %}
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        {% else %}
          <div class="text-center py-12">
            <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
              <i class="fa-solid fa-broadcast-tower text-gray-400 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Show Data Available</h3>
            <p class="text-gray-500">No show activity found for the selected date range.</p>
            <button onclick="showDateFilter()" class="mt-4 bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">Adjust Date Range</button>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Performance Summary -->
    {% if show_stats %}
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-broadcast-tower text-blue-600"></i>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Active Shows</p>
              <p class="text-2xl font-semibold text-gray-900">{{ show_stats|length }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-bullhorn text-green-600"></i>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Mentions</p>
              <p class="text-2xl font-semibold text-gray-900">
                {% with total=0 %}
                  {% for show in show_stats %}
                    {% with total=total|add:show.total_mentions %}

                    {% endwith %}
                  {% endfor %}
                  {{ total }}
                {% endwith %}
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-check text-purple-600"></i>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Completed</p>
              <p class="text-2xl font-semibold text-gray-900">
                {% with completed=0 %}
                  {% for show in show_stats %}
                    {% with completed=completed|add:show.completed_mentions %}

                    {% endwith %}
                  {% endfor %}
                  {{ completed }}
                {% endwith %}
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <i class="fa-solid fa-percentage text-yellow-600"></i>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Avg Completion</p>
              <p class="text-2xl font-semibold text-gray-900">
                {% if show_stats %}
                  {% with total_mentions=0 total_completed=0 %}
                    {% for show in show_stats %}
                      {% with total_mentions=total_mentions|add:show.total_mentions total_completed=total_completed|add:show.completed_mentions %}

                      {% endwith %}
                    {% endfor %}
                    {% if total_mentions > 0 %}
                      {% widthratio total_completed total_mentions 100 %}%
                    {% else %}
                      0%
                    {% endif %}
                  {% endwith %}
                {% else %}
                  0%
                {% endif %}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Performing Shows -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Top Performing Shows</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            {% for show in show_stats|slice:':5' %}
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                      <i class="fa-solid fa-broadcast-tower text-green-600"></i>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ show.name }}</div>
                    <div class="text-sm text-gray-500">{{ show.completed_mentions|default:0 }} completed mentions</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">{{ show.completion_rate|default:0 }}%</div>
                  <div class="text-sm text-gray-500">completion rate</div>
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    {% endif %}
  </div>

  <!-- Date Range Filter Modal -->
  <div id="dateFilterModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Date Range</h3>
        <form method="GET" action="{% url 'reports:show_performance' %}">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
              <input type="date" name="start_date" value="{{ start_date|date:'Y-m-d' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
              <input type="date" name="end_date" value="{{ end_date|date:'Y-m-d' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" onclick="closeDateFilter()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">Cancel</button>
            <button type="submit" class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">Apply Filter</button>
          </div>
        </form>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/simplified-template-selector.js' %}"></script>
  <script src="{% static 'js/print-reports.js' %}"></script>
  <script>
    // Override default date functions for this report type
    function getDefaultStartDate() {
      return '{{ start_date|date:"Y-m-d" }}'
    }
    
    function getDefaultEndDate() {
      return '{{ end_date|date:"Y-m-d" }}'
    }
    
    // Initialize print manager
    const printManager = new PrintManager()
    
    function showDateFilter() {
      document.getElementById('dateFilterModal').classList.remove('hidden')
    }
    
    function closeDateFilter() {
      document.getElementById('dateFilterModal').classList.add('hidden')
    }
  </script>
{% endblock %}
