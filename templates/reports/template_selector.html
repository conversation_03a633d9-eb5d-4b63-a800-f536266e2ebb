<!-- Template Selector Modal -->
<div id="templateSelectorModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 no-print">
  <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Generate Report with Template</h3>
        <button onclick="closeTemplateSelector()" class="text-gray-400 hover:text-gray-600">
          <i class="fa-solid fa-times text-xl"></i>
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <!-- Email Templates -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-500 cursor-pointer template-option" 
             data-template-type="email" data-template-name="Professional Report Email">
          <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
              <i class="fa-solid fa-envelope text-blue-600"></i>
            </div>
            <h4 class="font-medium text-gray-900">Email Report</h4>
          </div>
          <p class="text-sm text-gray-600">Send report via email with professional formatting</p>
        </div>

        <!-- Document Templates -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-500 cursor-pointer template-option" 
             data-template-type="document" data-template-name="Formal Business Report">
          <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
              <i class="fa-solid fa-file-text text-green-600"></i>
            </div>
            <h4 class="font-medium text-gray-900">Document Report</h4>
          </div>
          <p class="text-sm text-gray-600">Generate formal document with letterhead</p>
        </div>

        <!-- PDF Templates -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-500 cursor-pointer template-option" 
             data-template-type="pdf" data-template-name="Executive Summary PDF">
          <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
              <i class="fa-solid fa-file-pdf text-red-600"></i>
            </div>
            <h4 class="font-medium text-gray-900">PDF Report</h4>
          </div>
          <p class="text-sm text-gray-600">Professional PDF with charts and branding</p>
        </div>

        <!-- Presentation Templates -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-500 cursor-pointer template-option" 
             data-template-type="presentation" data-template-name="Executive Presentation">
          <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
              <i class="fa-solid fa-presentation-screen text-purple-600"></i>
            </div>
            <h4 class="font-medium text-gray-900">Presentation</h4>
          </div>
          <p class="text-sm text-gray-600">Slide deck for stakeholder meetings</p>
        </div>

        <!-- Dashboard Templates -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-500 cursor-pointer template-option" 
             data-template-type="dashboard" data-template-name="Interactive Dashboard">
          <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
              <i class="fa-solid fa-chart-line text-orange-600"></i>
            </div>
            <h4 class="font-medium text-gray-900">Dashboard</h4>
          </div>
          <p class="text-sm text-gray-600">Interactive web dashboard with filters</p>
        </div>

        <!-- Custom Templates -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-primary-500 cursor-pointer template-option" 
             data-template-type="custom" data-template-name="Custom Template">
          <div class="flex items-center mb-2">
            <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
              <i class="fa-solid fa-paint-brush text-gray-600"></i>
            </div>
            <h4 class="font-medium text-gray-900">Custom Template</h4>
          </div>
          <p class="text-sm text-gray-600">Use your own custom template design</p>
        </div>
      </div>

      <!-- Template Preview -->
      <div id="templatePreview" class="hidden border border-gray-200 rounded-lg p-4 mb-6 bg-gray-50">
        <h4 class="font-medium text-gray-900 mb-2">Template Preview</h4>
        <div id="previewContent" class="text-sm text-gray-600">
          Select a template to see preview
        </div>
      </div>

      <!-- Template Options -->
      <div id="templateOptions" class="hidden border border-gray-200 rounded-lg p-4 mb-6">
        <h4 class="font-medium text-gray-900 mb-3">Template Options</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Include Logo</label>
            <select id="includeLogo" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="yes">Yes</option>
              <option value="no">No</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Color Scheme</label>
            <select id="colorScheme" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="default">Default</option>
              <option value="blue">Blue</option>
              <option value="green">Green</option>
              <option value="purple">Purple</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Page Layout</label>
            <select id="pageLayout" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="portrait">Portrait</option>
              <option value="landscape">Landscape</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
            <select id="fontSize" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="small">Small</option>
              <option value="medium" selected>Medium</option>
              <option value="large">Large</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-3">
        <button type="button" onclick="closeTemplateSelector()" 
                class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
          Cancel
        </button>
        <button type="button" onclick="generateWithTemplate()" 
                class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">
          Generate Report
        </button>
      </div>
    </div>
  </div>
</div>

<script>
let selectedTemplate = null;
let currentReportType = null;

function showTemplateSelector(reportType) {
  console.log('showTemplateSelector called with:', reportType);
  currentReportType = reportType;
  document.getElementById('templateSelectorModal').classList.remove('hidden');
}

function closeTemplateSelector() {
  document.getElementById('templateSelectorModal').classList.add('hidden');
  selectedTemplate = null;
  currentReportType = null;
  
  // Reset UI
  document.querySelectorAll('.template-option').forEach(option => {
    option.classList.remove('border-primary-500', 'bg-primary-50');
  });
  document.getElementById('templatePreview').classList.add('hidden');
  document.getElementById('templateOptions').classList.add('hidden');
}

// Template selection handling
document.querySelectorAll('.template-option').forEach(option => {
  option.addEventListener('click', function() {
    // Remove previous selection
    document.querySelectorAll('.template-option').forEach(opt => {
      opt.classList.remove('border-primary-500', 'bg-primary-50');
    });
    
    // Add selection to clicked option
    this.classList.add('border-primary-500', 'bg-primary-50');
    
    selectedTemplate = {
      type: this.dataset.templateType,
      name: this.dataset.templateName
    };
    
    // Show preview and options
    showTemplatePreview(selectedTemplate);
    document.getElementById('templateOptions').classList.remove('hidden');
  });
});

function showTemplatePreview(template) {
  const previewContent = document.getElementById('previewContent');
  const preview = document.getElementById('templatePreview');
  
  let previewText = '';
  switch(template.type) {
    case 'email':
      previewText = 'Professional email template with header, summary statistics, and detailed mention breakdown.';
      break;
    case 'document':
      previewText = 'Formal business document with letterhead, executive summary, and comprehensive data tables.';
      break;
    case 'pdf':
      previewText = 'Executive PDF report with charts, graphs, and professional formatting for stakeholder distribution.';
      break;
    case 'presentation':
      previewText = 'PowerPoint-style presentation with key metrics, trends, and actionable insights.';
      break;
    case 'dashboard':
      previewText = 'Interactive web dashboard with real-time filtering and drill-down capabilities.';
      break;
    case 'custom':
      previewText = 'Use your own custom template design with personalized branding and layout.';
      break;
  }
  
  previewContent.textContent = previewText;
  preview.classList.remove('hidden');
}

function generateWithTemplate() {
  if (!selectedTemplate) {
    alert('Please select a template first.');
    return;
  }
  
  // Get template options
  const options = {
    includeLogo: document.getElementById('includeLogo').value,
    colorScheme: document.getElementById('colorScheme').value,
    pageLayout: document.getElementById('pageLayout').value,
    fontSize: document.getElementById('fontSize').value
  };
  
  // Get current URL parameters for the report data
  const urlParams = new URLSearchParams(window.location.search);
  
  // Add template parameters
  urlParams.set('template_type', selectedTemplate.type);
  urlParams.set('template_name', selectedTemplate.name);
  urlParams.set('include_logo', options.includeLogo);
  urlParams.set('color_scheme', options.colorScheme);
  urlParams.set('page_layout', options.pageLayout);
  urlParams.set('font_size', options.fontSize);
  
  // Generate the templated report
  const templateUrl = `/template-designer/generate-report/${currentReportType}/?${urlParams.toString()}`;
  
  // Open in new window
  window.open(templateUrl, '_blank');
  
  // Close modal
  closeTemplateSelector();
}

// Close modal when clicking outside
document.getElementById('templateSelectorModal').addEventListener('click', function(e) {
  if (e.target === this) {
    closeTemplateSelector();
  }
});
</script>
