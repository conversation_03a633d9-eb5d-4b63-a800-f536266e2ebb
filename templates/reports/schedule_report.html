{% extends 'base.html' %}
{% load static %}

{% block title %}Schedule Reports - RadioMention{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">Schedule Reports</h1>
                    <p class="text-sm text-gray-600 mt-1">Automate report generation and delivery</p>
                </div>
                <div class="flex space-x-2">
                    <a href="{% url 'reports:report_list' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
                        <i class="fa-solid fa-arrow-left mr-1"></i>
                        Back to Reports
                    </a>
                    <button onclick="showScheduleModal()" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-plus mr-1"></i>
                        Schedule New Report
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scheduled Reports List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Scheduled Reports</h3>
        </div>
        <div class="p-6">
            {% if scheduled_reports %}
                <div class="space-y-4">
                    {% for report in scheduled_reports %}
                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                        <i class="fa-solid fa-calendar-plus text-green-600"></i>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ report.name }}</div>
                                    <div class="text-sm text-gray-500">{{ report.description|default:"No description" }}</div>
                                    <div class="text-xs text-gray-400 mt-1">
                                        Frequency: {{ report.frequency|title }} | 
                                        Next Run: {{ report.next_run|date:"M d, Y H:i" }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    {% if report.is_active %}bg-green-100 text-green-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {% if report.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                                <button class="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700">
                                    <i class="fa-solid fa-edit mr-1"></i>
                                    Edit
                                </button>
                                <button class="bg-red-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-red-700">
                                    <i class="fa-solid fa-trash mr-1"></i>
                                    Delete
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fa-solid fa-calendar-plus text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Scheduled Reports</h3>
                    <p class="text-gray-500 mb-4">Automate your reporting by scheduling regular report generation and delivery.</p>
                    <button onclick="showScheduleModal()" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-plus mr-2"></i>
                        Schedule Your First Report
                    </button>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Benefits Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-clock text-blue-600"></i>
                </div>
                <h3 class="ml-3 text-lg font-medium text-gray-900">Save Time</h3>
            </div>
            <p class="text-gray-600">Automatically generate reports on a schedule without manual intervention.</p>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-envelope text-green-600"></i>
                </div>
                <h3 class="ml-3 text-lg font-medium text-gray-900">Email Delivery</h3>
            </div>
            <p class="text-gray-600">Receive reports directly in your inbox or share with team members automatically.</p>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-chart-line text-purple-600"></i>
                </div>
                <h3 class="ml-3 text-lg font-medium text-gray-900">Consistent Insights</h3>
            </div>
            <p class="text-gray-600">Regular reporting helps track trends and performance over time.</p>
        </div>
    </div>
</div>

<!-- Schedule Report Modal -->
<div id="scheduleModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Schedule New Report</h3>
            <form>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Report Name</label>
                        <input type="text" id="reportName" placeholder="Enter report name..." 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Report Type</label>
                        <select id="reportType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select report type...</option>
                            <option value="mentions-summary">Mentions Summary</option>
                            <option value="show-performance">Show Performance</option>
                            <option value="client-activity">Client Activity</option>
                            <option value="daily-summary">Daily Summary</option>
                            <option value="weekly-trends">Weekly Trends</option>
                            <option value="monthly-overview">Monthly Overview</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Frequency</label>
                        <select id="frequency" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email Recipients</label>
                        <input type="email" id="recipients" placeholder="Enter email addresses (comma separated)..." 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="startDate" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeScheduleModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="button" onclick="confirmSchedule()" 
                            class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">
                        Schedule Report
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showScheduleModal() {
    document.getElementById('scheduleModal').classList.remove('hidden');
}

function closeScheduleModal() {
    document.getElementById('scheduleModal').classList.add('hidden');
}

function confirmSchedule() {
    const reportName = document.getElementById('reportName').value;
    const reportType = document.getElementById('reportType').value;
    const frequency = document.getElementById('frequency').value;
    
    if (!reportName || !reportType || !frequency) {
        alert('Please fill in all required fields');
        return;
    }
    
    // In a real app, this would save to the backend
    alert(`Report "${reportName}" scheduled successfully!`);
    closeScheduleModal();
}
</script>
{% endblock %}
