{% extends 'base.html' %}
{% load static %}

{% block title %}
  Detailed Client Mentions Report - RadioMention
{% endblock %}

{% block extra_css %}
<style>
@media print {
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
  .page-break-inside-avoid { page-break-inside: avoid; }

  table { font-size: 10px; }
  .mention-content { max-width: 200px; font-size: 9px; }
}
</style>
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">

    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 no-print">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Detailed Client Mentions Report</h1>
            <p class="text-sm text-gray-600 mt-1">{{ start_date }} to {{ end_date }}</p>
          </div>
          <div class="flex space-x-2 {% if hide_actions %}no-print{% endif %}">
            <button onclick="window.location.href='{% url 'reports:report_list' %}'" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
              <i class="fa-solid fa-paint-brush mr-1"></i>
              Back to Reports
            </button>
            <!-- PDF Export Button -->
            {% comment %} {% include 'reports/partials/pdf_export_button.html' with report_type='client-mentions-detailed' %} {% endcomment %}
            <button onclick="showFilters()" class="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700">
              <i class="fa-solid fa-filter mr-1"></i>
              Filters
            </button>
            <button onclick="exportToPDF()" class="bg-red-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-red-700">
              <i class="fa-solid fa-file-pdf mr-1"></i>
              Export PDF
            </button>


          </div>
        </div>
      </div>
    </div>

    <!-- Summary Cards -->
    {% if not show_summary or show_summary == '1' %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6 {% if compact_view %}print:grid-cols-2{% endif %}">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-bullhorn text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Mentions</p>
            <p class="text-2xl font-semibold text-gray-900">{{ total_mentions }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-check-circle text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Completed</p>
            <p class="text-2xl font-semibold text-gray-900">{{ completed_mentions }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-percentage text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Completion Rate</p>
            <p class="text-2xl font-semibold text-gray-900">{{ completion_rate }}%</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-clock text-orange-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Total Air Time</p>
            <p class="text-2xl font-semibold text-gray-900">{{ total_duration_minutes }}m</p>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Mentions Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 page-break-inside-avoid">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Mention Details</h3>
      </div>
      <div class="overflow-x-auto">
        {% if mentions %}
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date/Time</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title & Content</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Presenter</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for mention in mentions %}
                {% with reading=mention.mentionreading_set.first %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <div class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                          <span class="text-sm font-medium text-primary-600">{{ mention.client.name|first|upper }}</span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ mention.client.name }}</div>
                        <div class="text-sm text-gray-500">{{ mention.client.contact_person|default:'No contact' }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {% if reading and reading.actual_read_time %}
                        {{ reading.actual_read_time|date:'M d, Y' }}
                        <div class="text-xs text-gray-500">{{ reading.actual_read_time|time:'H:i' }}</div>
                      {% elif reading %}
                        {{ reading.scheduled_date|date:'M d, Y' }}
                        <div class="text-xs text-gray-500">{{ reading.scheduled_time|time:'H:i' }} (scheduled)</div>
                      {% else %}
                        {{ mention.created_at|date:'M d, Y' }}
                        <div class="text-xs text-gray-500">{{ mention.created_at|time:'H:i' }} (created)</div>
                      {% endif %}
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="mention-content">
                      <div class="text-sm font-medium text-gray-900 mb-1">{{ mention.title }}</div>
                      <div class="text-xs text-gray-600 line-clamp-3">{{ mention.content|truncatewords:20 }}</div>
                      {% if mention.priority > 2 %}
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 mt-1">
                          {% if mention.priority == 4 %}Urgent{% else %}High{% endif %}
                        </span>
                      {% endif %}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if reading and reading.show %}
                      <div class="text-sm text-gray-900">{{ reading.show.name }}</div>
                      <div class="text-xs text-gray-500">{{ reading.show.start_time|time:'H:i' }} - {{ reading.show.end_time|time:'H:i' }}</div>
                    {% else %}
                      <span class="text-sm text-gray-400">Not scheduled</span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if reading and reading.presenter %}
                      <div class="text-sm text-gray-900">{{ reading.presenter.first_name }} {{ reading.presenter.last_name }}</div>
                      {% if reading.presenter.stage_name %}
                        <div class="text-xs text-gray-500">({{ reading.presenter.stage_name }})</div>
                      {% endif %}
                    {% elif reading and reading.read_by %}
                      <div class="text-sm text-gray-900">{{ reading.read_by }}</div>
                    {% else %}
                      <span class="text-sm text-gray-400">Not assigned</span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if reading and reading.actual_read_time %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fa-solid fa-check mr-1"></i>Completed
                      </span>
                    {% elif mention.status == 'approved' %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <i class="fa-solid fa-calendar mr-1"></i>Scheduled
                      </span>
                    {% elif mention.status == 'pending' %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i class="fa-solid fa-clock mr-1"></i>Pending
                      </span>
                    {% else %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {{ mention.get_status_display }}
                      </span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {% if reading and reading.duration_seconds %}
                        {{ reading.duration_seconds }}s
                      {% else %}
                        {{ mention.duration_seconds }}s
                      {% endif %}
                    </div>
                    {% if reading and reading.duration_seconds and reading.duration_seconds != mention.duration_seconds %}
                      <div class="text-xs text-gray-500">(planned: {{ mention.duration_seconds }}s)</div>
                    {% endif %}
                  </td>
                </tr>
                {% endwith %}
              {% endfor %}
            </tbody>
          </table>
        {% else %}
          <div class="text-center py-12">
            <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
              <i class="fa-solid fa-bullhorn text-gray-400 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Mentions Found</h3>
            <p class="text-gray-500">No mentions found for the selected criteria.</p>
            <button onclick="showFilters()" class="mt-4 bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">Adjust Filters</button>
          </div>
        {% endif %}
      </div>
    </div>

  </div>

  <!-- Filters Modal -->
  <div id="filtersModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 no-print">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Report</h3>
        <form method="GET" action="{% url 'reports:client_mentions_detailed' %}">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
              <input type="date" name="start_date" value="{{ start_date|date:'Y-m-d' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
              <input type="date" name="end_date" value="{{ end_date|date:'Y-m-d' }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Client</label>
              <select name="client_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="">All Clients</option>
                {% for client in clients %}
                  <option value="{{ client.id }}"{% if client.id == selected_client_id %} selected{% endif %}>{{ client.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
              <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Statuses</option>
                <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                <option value="scheduled" {% if status_filter == 'scheduled' %}selected{% endif %}>Scheduled</option>
                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
              </select>
            </div>

            <!-- Display Options -->
            <div class="border-t pt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">Display Options</label>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input type="checkbox" name="show_summary" value="1" {% if show_summary %}checked{% endif %} class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                  <span class="ml-2 text-sm text-gray-700">Show Summary Statistics</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" name="compact_view" value="1" {% if compact_view %}checked{% endif %} class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                  <span class="ml-2 text-sm text-gray-700">Compact View (Print-friendly)</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" name="show_content" value="1" {% if show_content %}checked{% endif %} class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                  <span class="ml-2 text-sm text-gray-700">Show Full Content</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" name="hide_actions" value="1" {% if hide_actions %}checked{% endif %} class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                  <span class="ml-2 text-sm text-gray-700">Hide Action Buttons (Print Mode)</span>
                </label>
              </div>
            </div>
          </div>
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" onclick="closeFilters()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">Cancel</button>
            <button type="submit" class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">Apply Filters</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Include Template Selector -->
  {% include 'reports/template_selector.html' %}
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/simplified-template-selector.js' %}"></script>
  <script src="{% static 'js/print-reports.js' %}"></script>
  <script>
    // Override default date functions for this report type
    function getDefaultStartDate() {
      return '{{ start_date|date:"Y-m-d" }}'
    }

    function getDefaultEndDate() {
      return '{{ end_date|date:"Y-m-d" }}'
    }

    // Initialize print manager
    const printManager = new PrintManager()

    function showFilters() {
      document.getElementById('filtersModal').classList.remove('hidden')
    }

    function closeFilters() {
      document.getElementById('filtersModal').classList.add('hidden')
    }

    function exportToPDF() {
      // Get current filter parameters
      const urlParams = new URLSearchParams(window.location.search)
      const pdfUrl = '{% url "reports:generate_pdf_report" %}?' + urlParams.toString()

      // Open PDF in new window
      window.open(pdfUrl, '_blank')
    }



















    // Close modal when clicking outside
    document.getElementById('filtersModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeFilters()
      }
    })
  </script>
{% endblock %}
