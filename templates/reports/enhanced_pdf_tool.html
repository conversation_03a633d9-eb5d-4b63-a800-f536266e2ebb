{% extends 'base.html' %}
{% load static %}

{% block title %}
  Enhanced PDF Generator - AppRadio
{% endblock %}

{% block extra_css %}
  <style>
    .pdf-card {
      transition: all 0.3s ease;
    }
    .pdf-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
  </style>
{% endblock %}

{% block content %}
  <!-- PDF Generator Content -->
  <div class="max-w-7xl mx-auto">
    <!-- Header Section -->
    <div class="mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-semibold text-gray-800 flex items-center">
            <i class="fa-solid fa-file-pdf text-blue-600 mr-3"></i>
            Universal PDF Generator
          </h1>
          <p class="text-gray-600 mt-1">Generate professional reports for any data type - {{ report_types|length }} report types available</p>
        </div>
        <div class="flex items-center space-x-3">
          <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center">
            <i class="fa-solid fa-cog mr-2"></i>
            Settings
          </button>
          <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
            <i class="fa-solid fa-history mr-2"></i>
            History
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content Layout -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Left Column - Quick Actions -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-800 mb-2">Quick PDF Generation</h2>
            <p class="text-gray-600">Generate reports with one click</p>
          </div>

          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div class="pdf-card bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 cursor-pointer" onclick="generatePDF('mentions-summary')">
                <div class="flex items-center">
                  <div class="w-12 h-12 rounded-lg bg-blue-500 flex items-center justify-center mr-4">
                    <i class="fa-solid fa-comment-dots text-white text-lg"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Mentions Summary</h4>
                    <p class="text-sm text-gray-600">Key metrics & mentions</p>
                  </div>
                </div>
              </div>

              <div class="pdf-card bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4 cursor-pointer" onclick="generatePDF('analytics')">
                <div class="flex items-center">
                  <div class="w-12 h-12 rounded-lg bg-green-500 flex items-center justify-center mr-4">
                    <i class="fa-solid fa-chart-bar text-white text-lg"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Analytics Report</h4>
                    <p class="text-sm text-gray-600">Performance & trends</p>
                  </div>
                </div>
              </div>

              <div class="pdf-card bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 cursor-pointer" onclick="generatePDF('client-activity')">
                <div class="flex items-center">
                  <div class="w-12 h-12 rounded-lg bg-purple-500 flex items-center justify-center mr-4">
                    <i class="fa-solid fa-user-tie text-white text-lg"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Client Activity</h4>
                    <p class="text-sm text-gray-600">Client performance</p>
                  </div>
                </div>
              </div>

              <div class="pdf-card bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-4 cursor-pointer" onclick="generatePDF('daily-summary')">
                <div class="flex items-center">
                  <div class="w-12 h-12 rounded-lg bg-orange-500 flex items-center justify-center mr-4">
                    <i class="fa-solid fa-calendar-day text-white text-lg"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Daily Summary</h4>
                    <p class="text-sm text-gray-600">Today's performance</p>
                  </div>
                </div>
              </div>

              <div class="pdf-card bg-gradient-to-br from-indigo-50 to-indigo-100 border border-indigo-200 rounded-lg p-4 cursor-pointer" onclick="generatePDF('weekly-trends')">
                <div class="flex items-center">
                  <div class="w-12 h-12 rounded-lg bg-indigo-500 flex items-center justify-center mr-4">
                    <i class="fa-solid fa-chart-line text-white text-lg"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Weekly Trends</h4>
                    <p class="text-sm text-gray-600">Week analysis</p>
                  </div>
                </div>
              </div>

              <div class="pdf-card bg-gradient-to-br from-red-50 to-red-100 border border-red-200 rounded-lg p-4 cursor-pointer" onclick="generatePDF('revenue-report')">
                <div class="flex items-center">
                  <div class="w-12 h-12 rounded-lg bg-red-500 flex items-center justify-center mr-4">
                    <i class="fa-solid fa-dollar-sign text-white text-lg"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800">Revenue Report</h4>
                    <p class="text-sm text-gray-600">Financial overview</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6 border-b border-gray-200">
            <h3 class="font-medium text-gray-700">Quick Stats</h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">15+</div>
                <div class="text-sm text-gray-600">Report Types</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">100%</div>
                <div class="text-sm text-gray-600">Success Rate</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-orange-600">&lt;2s</div>
                <div class="text-sm text-gray-600">Avg Time</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-red-600">~4KB</div>
                <div class="text-sm text-gray-600">File Size</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column - Advanced Options -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6 border-b border-gray-200">
            <h3 class="font-medium text-gray-800 mb-2">Advanced Options</h3>
            <p class="text-sm text-gray-600">Customize your PDF report</p>
          </div>

          <div class="p-6">
            <form>
              <div class="mb-5">
                <label class="block text-gray-700 text-sm font-medium mb-2">Date Range</label>
                <div class="grid grid-cols-2 gap-3">
                  <input type="date" id="startDate" value="{{ default_start_date }}" class="w-full p-2.5 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm" />
                  <input type="date" id="endDate" value="{{ default_end_date }}" class="w-full p-2.5 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm" />
                </div>
              </div>

              <div class="mb-5">
                <label class="block text-gray-700 text-sm font-medium mb-2">Report Type</label>
                <select id="advancedReportType" class="w-full p-2.5 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm">
                  {% for report_type, report_info in report_types.items %}
                    <option value="{{ report_type }}">{{ report_info.name }}</option>
                  {% endfor %}
                </select>
                <p class="text-xs text-gray-500 mt-1">Select any available report type - the system will generate it automatically</p>
              </div>

              <div class="mb-5">
                <label class="block text-gray-700 text-sm font-medium mb-2">Custom Title</label>
                <input type="text" id="customTitle" placeholder="Optional custom title" class="w-full p-2.5 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm" />
              </div>

              <div class="mb-6">
                <label class="block text-gray-700 text-sm font-medium mb-2">Client Filter</label>
                <select id="clientFilter" class="w-full p-2.5 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm">
                  <option value="">All Clients</option>
                  {% for client in clients %}
                    <option value="{{ client.id }}">{{ client.name }}</option>
                  {% endfor %}
                </select>
              </div>

              <button type="button" onclick="generateAdvancedPDF()" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition duration-200">Generate Custom PDF</button>
            </form>
          </div>

          <div class="p-6 border-t border-gray-200">
            <button onclick="showAllReports()" class="w-full text-gray-700 bg-gray-100 hover:bg-gray-200 font-medium py-2.5 px-4 rounded-lg transition duration-200 flex items-center justify-center">
              <i class="fa-solid fa-list mr-2"></i>
              All Report Types
            </button>
          </div>
        </div>

        <!-- Generation Status -->
        <div id="generation-status" class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6 p-6" style="display: none;">
          <div class="flex items-center justify-between mb-4">
            <h3 class="font-medium text-gray-800">Generating PDF...</h3>
            <div class="text-gray-500 text-sm">
              <i class="fa-regular fa-clock mr-1"></i>
              Estimated: 2s
            </div>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
            <div class="bg-blue-600 h-2.5 rounded-full w-3/4 animate-pulse"></div>
          </div>
          <div class="flex justify-between text-sm text-gray-500">
            <span>Processing data</span>
            <span>75%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- All Report Types Section (Hidden by default) -->
    <div id="allReportsSection" class="mt-6" style="display: none;">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
          <h3 class="font-medium text-gray-800">All Available Report Types</h3>
          <p class="text-sm text-gray-600 mt-1">Dynamically discovered report types ({{ report_types|length }} total)</p>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {% for report_type, report_info in report_types.items %}
              <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition duration-200">
                <div class="flex items-center justify-between mb-2">
                  <h6 class="font-medium text-gray-800 text-sm">{{ report_info.name }}</h6>
                  <i class="{{ report_info.icon }} text-gray-400"></i>
                </div>
                <p class="text-gray-600 text-xs mb-3">{{ report_info.description }}</p>
                <button onclick="generatePDF('{{ report_type }}')" class="w-full bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-3 rounded transition duration-200"><i class="fa-solid fa-download mr-1"></i>Generate</button>
              </div>
            {% endfor %}
          </div>

          <!-- Add Custom Report Type Section -->
          <div class="mt-6 p-4 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <h4 class="font-medium text-gray-900 mb-2">Custom Report Type</h4>
            <p class="text-sm text-gray-600 mb-3">Enter any report type name - the system will attempt to generate it automatically</p>
            <div class="flex gap-2">
              <input type="text" id="customReportType" placeholder="e.g., custom-analytics, special-client-report" class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
              <button onclick="generateCustomReportType()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"><i class="fa-solid fa-magic mr-1"></i>Generate</button>
            </div>
            <p class="text-xs text-gray-500 mt-2">The system will intelligently collect relevant data and generate a PDF report</p>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // PDF Generation Functions
    function generatePDF(reportType, customParams = {}) {
      showStatus('Generating PDF...', 'processing')
    
      // Build URL with parameters
      const params = new URLSearchParams({
        type: reportType,
        format: 'pdf',
        ...customParams
      })
    
      // Add date range if specified
      const startDate = document.getElementById('startDate')?.value
      const endDate = document.getElementById('endDate')?.value
      if (startDate) params.append('start_date', startDate)
      if (endDate) params.append('end_date', endDate)
    
      const url = `{% url 'reports:export_report' %}?${params.toString()}`
    
      // Create download link
      const link = document.createElement('a')
      link.href = url
      link.download = `${reportType}_${new Date().toISOString().split('T')[0]}.pdf`
    
      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    
      // Show success status
      setTimeout(() => {
        showStatus('PDF generated successfully!', 'success')
        setTimeout(hideStatus, 3000)
      }, 1000)
    }
    
    function generateQuickPDF(reportType) {
      generatePDF(reportType)
    }
    
    function generateAdvancedPDF() {
      const reportType = document.getElementById('advancedReportType').value
      const customTitle = document.getElementById('customTitle').value
      const clientId = document.getElementById('clientFilter').value
    
      const customParams = {}
      if (customTitle) customParams.title = customTitle
      if (clientId) customParams.client_id = clientId
    
      generatePDF(reportType, customParams)
    }
    
    function generateCustomReportType() {
      const customReportType = document.getElementById('customReportType').value.trim()
    
      if (!customReportType) {
        alert('Please enter a report type name')
        return
      }
    
      // Validate report type format (lowercase with hyphens)
      const validFormat = /^[a-z0-9-]+$/.test(customReportType)
      if (!validFormat) {
        alert('Report type should contain only lowercase letters, numbers, and hyphens (e.g., "custom-analytics")')
        return
      }
    
      showStatus(`Generating custom report: ${customReportType}...`, 'processing')
    
      // Generate the custom report
      generatePDF(customReportType, {
        title: customReportType.replace('-', ' ').replace(/\b\w/g, (l) => l.toUpperCase()) + ' Report'
      })
    }
    
    function showAllReports() {
      const section = document.getElementById('allReportsSection')
      if (section.style.display === 'none') {
        section.style.display = 'block'
        section.scrollIntoView({ behavior: 'smooth' })
      } else {
        section.style.display = 'none'
      }
    }
    
    function showStatus(message, type) {
      const statusDisplay = document.getElementById('generation-status')
      if (statusDisplay) {
        statusDisplay.style.display = 'block'
        const statusText = statusDisplay.querySelector('h3')
        if (statusText) {
          statusText.textContent = message
        }
      }
    
      // Also use the notification system
      if (window.notificationSystem) {
        window.notificationSystem.show(message, type === 'processing' ? 'info' : type, type === 'processing' ? 0 : 3000)
      }
    }
    
    function hideStatus() {
      const statusDisplay = document.getElementById('generation-status')
      if (statusDisplay) {
        statusDisplay.style.display = 'none'
      }
    }
  </script>
{% endblock %}
