{% extends 'base.html' %}
{% load static %}

{% block title %}
  Daily Summary Report - RadioMention
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Daily Summary Report</h1>
            <p class="text-sm text-gray-600 mt-1">{{ report_date }}</p>
          </div>
          <div class="flex space-x-2">
            <a href="{% url 'reports:report_list' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
              <i class="fa-solid fa-arrow-left mr-1"></i>
              Back to Reports
            </a>
            <!-- PDF Export Button -->
            {% include 'reports/partials/pdf_export_button.html' with report_type='daily-summary' %}
            {% comment %} <button onclick="window.print()" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
              <i class="fa-solid fa-print mr-1"></i>
              Print Report
            </button> {% endcomment %}
          </div>
        </div>
      </div>
    </div>

    <!-- Daily Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-plus text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Mentions Created</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mentions_created|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-check text-green-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Mentions Approved</p>
            <p class="text-2xl font-semibold text-gray-900">{{ mentions_approved|default:0 }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-microphone text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">Readings Completed</p>
            <p class="text-2xl font-semibold text-gray-900">{{ readings_completed|default:0 }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Hourly Breakdown -->
    {% if hourly_breakdown %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Hourly Activity</h3>
        </div>
        <div class="p-6">
          <div class="space-y-3">
            {% for hour in hourly_breakdown %}
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">{{ hour.hour|floatformat:0 }}:00</span>
                <div class="flex items-center">
                  <div class="w-32 bg-gray-200 rounded-full h-2 mr-3">
                    <div class="bg-primary-600 h-2 rounded-full" style="width: {% widthratio hour.count 10 100 %}%"></div>
                  </div>
                  <span class="text-sm font-medium text-gray-900">{{ hour.count }}</span>
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Show Activity -->
    {% if show_activity %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Show Activity</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            {% for show in show_activity %}
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                      <i class="fa-solid fa-broadcast-tower text-green-600"></i>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ show.name }}</div>
                    <div class="text-sm text-gray-500">{{ show.readings_count }} readings completed</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">{{ show.readings_count }}</div>
                  <div class="text-sm text-gray-500">readings</div>
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    {% endif %}
  </div>
{% endblock %}
