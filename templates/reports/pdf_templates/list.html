{% extends 'base.html' %}
{% load static %}

{% block title %}PDF Templates - RadioMention{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">PDF Templates</h1>
                    <p class="text-sm text-gray-600 mt-1">Manage PDF report templates</p>
                </div>
                <div class="flex space-x-2">
                    <a href="{% url 'reports:report_list' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
                        <i class="fa-solid fa-arrow-left mr-1"></i>
                        Back to Reports
                    </a>
                    <a href="{% url 'reports:create_pdf_template' %}" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-plus mr-1"></i>
                        Create Template
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Templates List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Available Templates</h3>
        </div>
        
        {% if templates %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for template in templates %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                                            <i class="fa-solid fa-file-pdf text-primary-600 text-sm"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ template.name }}</div>
                                        <div class="text-sm text-gray-500">{{ template.description|truncatechars:50 }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ template.get_template_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ template.created_by.get_full_name|default:template.created_by.username }}</div>
                                <div class="text-sm text-gray-500">{{ template.created_by.email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ template.created_at|date:"M d, Y" }}</div>
                                <div class="text-sm text-gray-500">{{ template.created_at|time:"g:i A" }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if template.is_active %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Inactive
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{% url 'reports:template_preview' template.id %}" 
                                       class="text-primary-600 hover:text-primary-900" 
                                       title="Preview Template">
                                        <i class="fa-solid fa-eye"></i>
                                    </a>
                                    <a href="{% url 'reports:generate_pdf_from_template' template.id %}" 
                                       class="text-green-600 hover:text-green-900" 
                                       title="Generate PDF">
                                        <i class="fa-solid fa-file-pdf"></i>
                                    </a>
                                    <button onclick="editTemplate({{ template.id }})" 
                                            class="text-blue-600 hover:text-blue-900" 
                                            title="Edit Template">
                                        <i class="fa-solid fa-edit"></i>
                                    </button>
                                    <button onclick="deleteTemplate({{ template.id }})" 
                                            class="text-red-600 hover:text-red-900" 
                                            title="Delete Template">
                                        <i class="fa-solid fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fa-solid fa-file-pdf text-gray-400 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No PDF Templates</h3>
                <p class="text-gray-500 mb-4">Get started by creating your first PDF template.</p>
                <a href="{% url 'reports:create_pdf_template' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                    <i class="fa-solid fa-plus mr-1"></i>
                    Create Template
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Template</h3>
            <p class="text-sm text-gray-500 mb-4">Are you sure you want to delete this template? This action cannot be undone.</p>
            <div class="flex justify-center space-x-3">
                <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
                    Cancel
                </button>
                <button onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700">
                    Delete
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let templateToDelete = null;

function editTemplate(templateId) {
    // For now, redirect to create page with template ID
    // In a full implementation, this would open an edit form
    alert('Edit functionality would be implemented here. Template ID: ' + templateId);
}

function deleteTemplate(templateId) {
    templateToDelete = templateId;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    templateToDelete = null;
    document.getElementById('deleteModal').classList.add('hidden');
}

function confirmDelete() {
    if (templateToDelete) {
        // In a full implementation, this would make an AJAX call to delete the template
        alert('Delete functionality would be implemented here. Template ID: ' + templateToDelete);
        closeDeleteModal();
    }
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closeDeleteModal();
    }
});
</script>
{% endblock %}
