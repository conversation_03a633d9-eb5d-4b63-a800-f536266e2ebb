{% extends 'base.html' %}
{% load static %}

{% block title %}Create PDF Template - RadioMention{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">Create PDF Template</h1>
                    <p class="text-sm text-gray-600 mt-1">Design a custom PDF template for your reports</p>
                </div>
                <div class="flex space-x-2">
                    <a href="{% url 'reports:list_pdf_templates' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
                        <i class="fa-solid fa-arrow-left mr-1"></i>
                        Back to Templates
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Creation Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <form id="templateForm" method="post">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Template Name</label>
                        <input type="text" name="name" required 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                               placeholder="e.g., Monthly Client Report Template">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Template Type</label>
                        <select name="template_type" required 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">Select a type...</option>
                            <option value="mentions_summary">Mentions Summary</option>
                            <option value="client_report">Client Report</option>
                            <option value="analytics_report">Analytics Report</option>
                            <option value="daily_summary">Daily Summary</option>
                            <option value="weekly_trends">Weekly Trends</option>
                            <option value="monthly_overview">Monthly Overview</option>
                            <option value="custom">Custom Report</option>
                        </select>
                    </div>
                </div>
                
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <textarea name="description" rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                              placeholder="Describe what this template is used for..."></textarea>
                </div>
            </div>

            <!-- Header Configuration -->
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Header Configuration</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="headerEnabled" name="header_enabled" checked 
                               class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        <label for="headerEnabled" class="ml-2 text-sm text-gray-700">Enable header</label>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Header Title</label>
                            <input type="text" name="header_title" value="AppRadio Report"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Header Font Size</label>
                            <select name="header_font_size" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option value="12">12pt</option>
                                <option value="14">14pt</option>
                                <option value="16" selected>16pt</option>
                                <option value="18">18pt</option>
                                <option value="20">20pt</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Configuration -->
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Footer Configuration</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="footerEnabled" name="footer_enabled" checked 
                               class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        <label for="footerEnabled" class="ml-2 text-sm text-gray-700">Enable footer</label>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Footer Text</label>
                            <input type="text" name="footer_text" value="Generated on {{date}} - Page {{page}}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                                   placeholder="Use {{date}} and {{page}} for dynamic values">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Footer Font Size</label>
                            <select name="footer_font_size" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option value="6">6pt</option>
                                <option value="8" selected>8pt</option>
                                <option value="10">10pt</option>
                                <option value="12">12pt</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Style Configuration -->
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Style Configuration</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Font Family</label>
                        <select name="font_family" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="Arial" selected>Arial</option>
                            <option value="Times">Times</option>
                            <option value="Courier">Courier</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Default Font Size</label>
                        <select name="font_size" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="10">10pt</option>
                            <option value="12" selected>12pt</option>
                            <option value="14">14pt</option>
                            <option value="16">16pt</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Page Orientation</label>
                        <select name="page_orientation" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="portrait" selected>Portrait</option>
                            <option value="landscape">Landscape</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4">
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'reports:list_pdf_templates' %}" 
                       class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
                        Cancel
                    </a>
                    <button type="button" onclick="previewTemplate()" 
                            class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                        <i class="fa-solid fa-eye mr-1"></i>
                        Preview
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">
                        <i class="fa-solid fa-save mr-1"></i>
                        Create Template
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Creating Template</h3>
            <p class="text-sm text-gray-500">Please wait while we create your PDF template...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function previewTemplate() {
    alert('Preview functionality would show a sample PDF with the current settings.');
}

document.getElementById('templateForm').addEventListener('submit', function(e) {
    // Show loading modal
    document.getElementById('loadingModal').classList.remove('hidden');
    
    // Collect form data and create JSON configurations
    const formData = new FormData(this);
    
    // Create header config
    const headerConfig = {
        enabled: formData.get('header_enabled') === 'on',
        title: formData.get('header_title') || 'AppRadio Report',
        font_size: parseInt(formData.get('header_font_size')) || 16,
        text_color: {r: 0, g: 0, b: 0}
    };
    
    // Create footer config
    const footerConfig = {
        enabled: formData.get('footer_enabled') === 'on',
        text: formData.get('footer_text') || 'Generated on {{date}} - Page {{page}}',
        font_size: parseInt(formData.get('footer_font_size')) || 8
    };
    
    // Create style config
    const styleConfig = {
        font_family: formData.get('font_family') || 'Arial',
        font_size: parseInt(formData.get('font_size')) || 12,
        text_color: {r: 0, g: 0, b: 0},
        page_orientation: formData.get('page_orientation') || 'portrait'
    };
    
    // Create body config (empty for now)
    const bodyConfig = {};
    
    // Add JSON configs to form
    const headerInput = document.createElement('input');
    headerInput.type = 'hidden';
    headerInput.name = 'header_config';
    headerInput.value = JSON.stringify(headerConfig);
    this.appendChild(headerInput);
    
    const footerInput = document.createElement('input');
    footerInput.type = 'hidden';
    footerInput.name = 'footer_config';
    footerInput.value = JSON.stringify(footerConfig);
    this.appendChild(footerInput);
    
    const styleInput = document.createElement('input');
    styleInput.type = 'hidden';
    styleInput.name = 'style_config';
    styleInput.value = JSON.stringify(styleConfig);
    this.appendChild(styleInput);
    
    const bodyInput = document.createElement('input');
    bodyInput.type = 'hidden';
    bodyInput.name = 'body_config';
    bodyInput.value = JSON.stringify(bodyConfig);
    this.appendChild(bodyInput);
});
</script>
{% endblock %}
