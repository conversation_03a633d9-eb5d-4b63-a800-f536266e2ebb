{% extends 'base.html' %}
{% load static %}

{% block title %}Generate PDF - {{ template.name }} - RadioMention{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">Generate PDF Report</h1>
                    <p class="text-sm text-gray-600 mt-1">Using template: {{ template.name }}</p>
                </div>
                <div class="flex space-x-2">
                    <a href="{% url 'reports:list_pdf_templates' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
                        <i class="fa-solid fa-arrow-left mr-1"></i>
                        Back to Templates
                    </a>
                    <a href="{% url 'reports:template_preview' template.id %}" class="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700">
                        <i class="fa-solid fa-eye mr-1"></i>
                        Preview Template
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Information -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Template Information</h3>
        </div>
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <div class="flex items-center mb-2">
                        <div class="flex-shrink-0 h-8 w-8">
                            <div class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                                <i class="fa-solid fa-file-pdf text-primary-600 text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-900">{{ template.name }}</div>
                            <div class="text-sm text-gray-500">{{ template.get_template_type_display }}</div>
                        </div>
                    </div>
                    {% if template.description %}
                        <p class="text-sm text-gray-600">{{ template.description }}</p>
                    {% endif %}
                </div>
                <div>
                    <div class="text-sm text-gray-500">Created by</div>
                    <div class="text-sm font-medium text-gray-900">{{ template.created_by.get_full_name|default:template.created_by.username }}</div>
                    <div class="text-sm text-gray-500">{{ template.created_at|date:"M d, Y" }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Input Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Report Data</h3>
            <p class="text-sm text-gray-600 mt-1">Provide the data for your PDF report</p>
        </div>
        
        <form id="generateForm" method="post">
            {% csrf_token %}
            
            <!-- Basic Report Information -->
            <div class="px-6 py-4 border-b border-gray-200">
                <h4 class="text-md font-medium text-gray-900 mb-4">Basic Information</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Report Title</label>
                        <input type="text" name="title" required 
                               value="{{ template.name }} - {{ 'now'|date:'M d, Y' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Report Date</label>
                        <input type="date" name="report_date" 
                               value="{{ 'now'|date:'Y-m-d' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
            </div>

            <!-- Date Range (for time-based reports) -->
            {% if template.template_type != 'daily_summary' %}
            <div class="px-6 py-4 border-b border-gray-200">
                <h4 class="text-md font-medium text-gray-900 mb-4">Date Range</h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" name="start_date" 
                               value="{{ 'now'|date:'Y-m-d'|add:'-30 days' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" name="end_date" 
                               value="{{ 'now'|date:'Y-m-d' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Client Filter (for client reports) -->
            {% if 'client' in template.template_type %}
            <div class="px-6 py-4 border-b border-gray-200">
                <h4 class="text-md font-medium text-gray-900 mb-4">Client Filter</h4>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Select Client (optional)</label>
                    <select name="client_id" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Clients</option>
                        <!-- In a real implementation, this would be populated with actual clients -->
                        <option value="1">Sample Client 1</option>
                        <option value="2">Sample Client 2</option>
                    </select>
                </div>
            </div>
            {% endif %}

            <!-- Additional Options -->
            <div class="px-6 py-4 border-b border-gray-200">
                <h4 class="text-md font-medium text-gray-900 mb-4">Options</h4>
                
                <div class="space-y-3">
                    <div class="flex items-center">
                        <input type="checkbox" id="includeSummary" name="include_summary" checked 
                               class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        <label for="includeSummary" class="ml-2 text-sm text-gray-700">Include summary statistics</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="includeCharts" name="include_charts" 
                               class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        <label for="includeCharts" class="ml-2 text-sm text-gray-700">Include charts (if available)</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="includeLogo" name="include_logo" checked 
                               class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        <label for="includeLogo" class="ml-2 text-sm text-gray-700">Include company logo</label>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-6 py-4">
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'reports:list_pdf_templates' %}" 
                       class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
                        Cancel
                    </a>
                    <button type="button" onclick="generateWithSampleData()" 
                            class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                        <i class="fa-solid fa-flask mr-1"></i>
                        Generate with Sample Data
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">
                        <i class="fa-solid fa-file-pdf mr-1"></i>
                        Generate PDF
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Loading Modal -->
<div id="loadingModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-primary-100 mb-4">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Generating PDF</h3>
            <p class="text-sm text-gray-500">Please wait while we generate your PDF report...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function generateWithSampleData() {
    // Fill form with sample data and submit
    document.querySelector('input[name="title"]').value = '{{ template.name }} - Sample Report';
    
    // Show loading and submit
    document.getElementById('loadingModal').classList.remove('hidden');
    
    // Add sample data flag
    const sampleInput = document.createElement('input');
    sampleInput.type = 'hidden';
    sampleInput.name = 'use_sample_data';
    sampleInput.value = 'true';
    document.getElementById('generateForm').appendChild(sampleInput);
    
    document.getElementById('generateForm').submit();
}

document.getElementById('generateForm').addEventListener('submit', function(e) {
    // Show loading modal
    document.getElementById('loadingModal').classList.remove('hidden');
    
    // Collect all form data into a JSON object
    const formData = new FormData(this);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // Add the data as a hidden input
    const dataInput = document.createElement('input');
    dataInput.type = 'hidden';
    dataInput.name = 'report_data';
    dataInput.value = JSON.stringify(data);
    this.appendChild(dataInput);
});
</script>
{% endblock %}
