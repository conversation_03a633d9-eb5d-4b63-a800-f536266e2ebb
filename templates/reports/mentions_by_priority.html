{% extends 'base.html' %}
{% load static %}

{% block title %}
  Mentions by Priority Report - RadioMention
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Mentions by Priority Report</h1>
            <p class="text-sm text-gray-600 mt-1">{{ start_date }} to {{ end_date }}</p>
          </div>
          <div class="flex space-x-2">
            <button onclick="window.location.href='{% url 'reports:report_list' %}'" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
              <i class="fa-solid fa-paint-brush mr-1"></i>
              Back to Reports
            </button>
            <!-- PDF Export Button -->
            {% include 'reports/partials/pdf_export_button.html' with report_type='mentions-by-priority' %}
          </div>
        </div>
      </div>
    </div>

    <!-- Priority Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      {% for priority in priority_stats %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-8 h-8 rounded-lg flex items-center justify-center
                            {% if priority.priority == 1 %}
                  
                  
                  
                  bg-green-100




                {% elif priority.priority == 2 %}
                  
                  
                  
                  bg-blue-100




                {% elif priority.priority == 3 %}
                  
                  
                  
                  bg-yellow-100




                {% elif priority.priority == 4 %}
                  
                  
                  
                  bg-red-100




                {% endif %}">
                <i class="fa-solid fa-exclamation-triangle 
                                {% if priority.priority == 1 %}
                    
                    
                    
                    text-green-600




                  {% elif priority.priority == 2 %}
                    
                    
                    
                    text-blue-600




                  {% elif priority.priority == 3 %}
                    
                    
                    
                    text-yellow-600




                  {% elif priority.priority == 4 %}
                    
                    
                    
                    text-red-600




                  {% endif %}">

                </i>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">{{ priority.name }} Priority</p>
              <p class="text-2xl font-semibold text-gray-900">{{ priority.total|default:0 }}</p>
              <p class="text-xs text-gray-500">{{ priority.completion_rate|default:0 }}% completed</p>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>

    <!-- Priority Breakdown Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Priority Breakdown</h3>
      </div>
      <div class="overflow-x-auto">
        {% if priority_stats %}
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority Level</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Mentions</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completion Rate</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for priority in priority_stats %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <div class="h-8 w-8 rounded-full flex items-center justify-center
                                                {% if priority.priority == 1 %}
                            
                            
                            
                            bg-green-100




                          {% elif priority.priority == 2 %}
                            
                            
                            
                            bg-blue-100




                          {% elif priority.priority == 3 %}
                            
                            
                            
                            bg-yellow-100




                          {% elif priority.priority == 4 %}
                            
                            
                            
                            bg-red-100




                          {% endif %}">
                          <span class="text-sm font-medium
                                                    {% if priority.priority == 1 %}
                              
                              
                              
                              text-green-600




                            {% elif priority.priority == 2 %}
                              
                              
                              
                              text-blue-600




                            {% elif priority.priority == 3 %}
                              
                              
                              
                              text-yellow-600




                            {% elif priority.priority == 4 %}
                              
                              
                              
                              text-red-600




                            {% endif %}">
                            {{ priority.priority }}
                          </span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ priority.name }}</div>
                        <div class="text-sm text-gray-500">Priority Level {{ priority.priority }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ priority.total|default:0 }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ priority.approved|default:0 }}</div>
                    <div class="text-xs text-gray-500">
                      {% if priority.total > 0 %}
                        {% widthratio priority.approved priority.total 100 %}% approval rate
                      {% else %}
                        0% approval rate
                      {% endif %}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">{{ priority.completed|default:0 }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div class="h-2 rounded-full
                                                {% if priority.priority == 1 %}
                            
                            
                            
                            bg-green-600




                          {% elif priority.priority == 2 %}
                            
                            
                            
                            bg-blue-600




                          {% elif priority.priority == 3 %}
                            
                            
                            
                            bg-yellow-600




                          {% elif priority.priority == 4 %}
                            
                            
                            
                            bg-red-600




                          {% endif %}"
                          style="width: {{ priority.completion_rate|default:0 }}%"></div>
                      </div>
                      <span class="text-sm text-gray-600">{{ priority.completion_rate|default:0 }}%</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if priority.completion_rate >= 80 %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Excellent</span>
                    {% elif priority.completion_rate >= 60 %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Good</span>
                    {% elif priority.completion_rate >= 40 %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Fair</span>
                    {% else %}
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Poor</span>
                    {% endif %}
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        {% else %}
          <div class="text-center py-12">
            <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
              <i class="fa-solid fa-exclamation-triangle text-gray-400 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Priority Data Available</h3>
            <p class="text-gray-500">No mention priority data found for the selected date range.</p>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Priority Distribution Chart -->
    {% if priority_stats %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Priority Distribution</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Priority Chart -->
            <div>
              <canvas id="priorityChart" width="400" height="300"></canvas>
            </div>

            <!-- Priority Insights -->
            <div class="space-y-4">
              <h4 class="text-lg font-medium text-gray-900">Priority Insights</h4>

              {% for priority in priority_stats %}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-center">
                    <div class="w-3 h-3 rounded-full mr-3
                                        {% if priority.priority == 1 %}
                        
                        
                        
                        bg-green-500




                      {% elif priority.priority == 2 %}
                        
                        
                        
                        bg-blue-500




                      {% elif priority.priority == 3 %}
                        
                        
                        
                        bg-yellow-500




                      {% elif priority.priority == 4 %}
                        
                        
                        
                        bg-red-500




                      {% endif %}"></div>
                    <span class="text-sm font-medium text-gray-900">{{ priority.name }}</span>
                  </div>
                  <div class="text-right">
                    <div class="text-sm font-medium text-gray-900">{{ priority.total|default:0 }}</div>
                    <div class="text-xs text-gray-500">
                      {% if priority_stats %}
                        {% with total_all=0 %}
                          {% for p in priority_stats %}
                            {% with total_all=total_all|add:p.total %}

                            {% endwith %}
                          {% endfor %}
                          {% if total_all > 0 %}
                            {% widthratio priority.total total_all 100 %}%
                          {% else %}
                            0%
                          {% endif %}
                        {% endwith %}
                      {% else %}
                        0%
                      {% endif %}
                    </div>
                  </div>
                </div>
              {% endfor %}

              <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                <h5 class="text-sm font-medium text-blue-900 mb-2">Recommendations</h5>
                <ul class="text-sm text-blue-800 space-y-1">
                  {% if priority_stats.3.completion_rate < 90 %}
                    <li>• Focus on urgent priority mentions completion</li>
                  {% endif %}
                  {% if priority_stats.2.completion_rate < 80 %}
                    <li>• Improve high priority mention processing</li>
                  {% endif %}
                  <li>• Monitor priority distribution trends</li>
                  <li>• Consider priority-based scheduling</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/chart.min.js' %}"></script>
  <script>
{% if priority_stats %}
// Priority distribution chart
const priorityData = {
    labels: [
        {% for priority in priority_stats %}
            '{{ priority.name }}'{% if not forloop.last %},{% endif %}
        {% endfor %}
    ],
    datasets: [{
        data: [
            {% for priority in priority_stats %}
                {{ priority.total|default:0 }}{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        backgroundColor: [
            {% for priority in priority_stats %}
                {% if priority.priority == 1 %}'rgb(34, 197, 94)'
                {% elif priority.priority == 2 %}'rgb(59, 130, 246)'
                {% elif priority.priority == 3 %}'rgb(251, 191, 36)'
                {% elif priority.priority == 4 %}'rgb(239, 68, 68)'
                {% endif %}{% if not forloop.last %},{% endif %}
            {% endfor %}
        ]
    }]
};

const priorityChart = new Chart(document.getElementById('priorityChart'), {
    type: 'doughnut',
    data: priorityData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}
</script>
{% endblock %}
