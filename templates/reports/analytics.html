{% extends 'base.html' %}
{% load static %}

{% block title %}Analytics Dashboard{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Print-only header -->
    {% comment %} <div class="print-only print-header">
        <h1>Analytics Dashboard</h1>
        <div class="date-range">
            Analytics for {{ start_date|date:"M d, Y" }} - {{ end_date|date:"M d, Y" }}
            {% if time_range == '7' %}(Last 7 days)
            {% elif time_range == '30' %}(Last 30 days)
            {% elif time_range == '90' %}(Last 3 months)
            {% elif time_range == '365' %}(Last year)
            {% endif %}
        </div>
        <div class="generated-date">Generated on {% now "F d, Y" %}</div>
    </div> {% endcomment %}
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">Analytics Dashboard</h1>
                    <p class="text-sm text-gray-600 mt-1">
                        Analytics for {{ start_date|date:"M d, Y" }} - {{ end_date|date:"M d, Y" }}
                        {% if time_range == '7' %}(Last 7 days)
                        {% elif time_range == '30' %}(Last 30 days)
                        {% elif time_range == '90' %}(Last 3 months)
                        {% elif time_range == '365' %}(Last year)
                        {% endif %}
                    </p>
                </div>
                <div class="flex space-x-2 no-print">
                    <form method="GET" id="timeRangeForm" class="flex space-x-2">
                        <select name="time_range" id="timeRange" onchange="updateTimeRange()" class="px-3 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="7"{% if time_range == '7' %} selected{% endif %}>Last 7 days</option>
                            <option value="30"{% if time_range == '30' %} selected{% endif %}>Last 30 days</option>
                            <option value="90"{% if time_range == '90' %} selected{% endif %}>Last 3 months</option>
                            <option value="365"{% if time_range == '365' %} selected{% endif %}>Last year</option>
                        </select>
                        <!-- PDF Export Button -->
                        {% include 'reports/partials/pdf_export_button.html' with report_type='analytics' %}
                        {% comment %} <button onclick="showTemplateSelector('analytics-dashboard')" class="bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-blue-700">
                            <i class="fa-solid fa-paint-brush mr-1"></i>
                            Generate with Template
                        </button> {% endcomment %}
                        <button type="button" onclick="refreshData()" class="bg-primary-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-primary-700">
                            
                            <i class="fa-solid fa-refresh mr-1"></i>
                            Refresh
                        </button>
                        {% comment %} <button onclick="printManager.showPrintModal()" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
                            <i class="fa-solid fa-print mr-1"></i>
                            Print Dashboard
                        </button> {% endcomment %}
                        <a href="{% url 'reports:report_list' %}" class="bg-gray-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-gray-700">
                            <i class="fa-solid fa-chart-bar mr-1"></i>
                            All Reports
                        </a>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-bullhorn text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Mentions</p>
                    <p class="text-2xl font-semibold text-gray-900" id="totalMentions">{{ total_mentions|default:0 }}</p>
                    <p class="text-xs {% if mentions_change >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                        <i class="fa-solid fa-arrow-{% if mentions_change >= 0 %}up{% else %}down{% endif %}"></i>
                        {% if mentions_change >= 0 %}+{% endif %}{{ mentions_change|floatformat:1 }}% from last period
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-check-circle text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Completion Rate</p>
                    <p class="text-2xl font-semibold text-gray-900" id="completionRate">{{ completion_rate|default:0 }}%</p>
                    <p class="text-xs {% if completed_change >= 0 %}text-green-600{% else %}text-red-600{% endif %}">
                        <i class="fa-solid fa-arrow-{% if completed_change >= 0 %}up{% else %}down{% endif %}"></i>
                        {% if completed_change >= 0 %}+{% endif %}{{ completed_change|floatformat:1 }}% from last period
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-building text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Active Clients</p>
                    <p class="text-2xl font-semibold text-gray-900" id="activeClients">{{ active_clients|default:0 }}</p>
                    <p class="text-xs text-gray-500">
                        <i class="fa-solid fa-users"></i>
                        {{ clients_with_mentions|default:0 }} with mentions
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-clock text-orange-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Avg Duration</p>
                    <p class="text-2xl font-semibold text-gray-900" id="avgDuration">{{ avg_duration|default:0 }}s</p>
                    <p class="text-xs text-gray-500">
                        No change
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 page-break-inside-avoid analytics-chart">
        <!-- Mentions Over Time -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Mentions Over Time</h3>
            </div>
            <div class="p-6">
                <canvas id="mentionsChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Status Distribution -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Status Distribution</h3>
            </div>
            <div class="p-6">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6 page-break-inside-avoid">
        <!-- Top Clients -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Top Clients by Mentions</h3>
            </div>
            <div class="p-6">
                {% if top_clients %}
                    <div class="space-y-4">
                        {% for client in top_clients %}
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center text-primary-600 text-sm font-medium">
                                    {{ forloop.counter }}
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ client.name }}</p>
                                    <p class="text-xs text-gray-500">{{ client.industry|default:"No industry specified" }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900">{{ client.mention_count }}</p>
                                <p class="text-xs text-gray-500">mentions</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fa-solid fa-building text-gray-400 text-xl"></i>
                        </div>
                        <p class="text-gray-500">No client data available for this period</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Show Performance -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Top Shows by Mentions</h3>
            </div>
            <div class="p-6">
                {% if show_performance %}
                    <div class="space-y-4">
                        {% for show in show_performance %}
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 text-sm font-medium">
                                        {{ forloop.counter }}
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">{{ show.name }}</p>
                                        <p class="text-xs text-gray-500">{{ show.completed_count }} completed</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">{{ show.mention_count }}</p>
                                    <p class="text-xs text-gray-500">mentions</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fa-solid fa-broadcast-tower text-gray-400 text-xl"></i>
                        </div>
                        <p class="text-gray-500">No show data available for this period</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Priority Analysis -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Priority Analysis</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fa-solid fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ high_priority|default:0 }}</p>
                    <p class="text-sm text-gray-500">High/Urgent Priority</p>
                    <div class="mt-2 bg-gray-200 rounded-full h-2">
                        <div class="bg-red-500 h-2 rounded-full" style="width: {% if total_mentions > 0 %}{% widthratio high_priority total_mentions 100 %}{% else %}0{% endif %}%"></div>
                    </div>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fa-solid fa-circle text-blue-600 text-xl"></i>
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ medium_priority|default:0 }}</p>
                    <p class="text-sm text-gray-500">Normal Priority</p>
                    <div class="mt-2 bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: {% if total_mentions > 0 %}{% widthratio medium_priority total_mentions 100 %}{% else %}0{% endif %}%"></div>
                    </div>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fa-solid fa-check text-green-600 text-xl"></i>
                    </div>
                    <p class="text-2xl font-semibold text-gray-900">{{ low_priority|default:0 }}</p>
                    <p class="text-sm text-gray-500">Low Priority</p>
                    <div class="mt-2 bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: {% if total_mentions > 0 %}{% widthratio low_priority total_mentions 100 %}{% else %}0{% endif %}%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                {% for activity in recent_activities|slice:":8" %}
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 {% if activity.type == 'completed' %}bg-green-100{% else %}bg-blue-100{% endif %} rounded-full flex items-center justify-center">
                            {% if activity.type == 'completed' %}
                                <i class="fa-solid fa-check text-green-600 text-xs"></i>
                            {% else %}
                                <i class="fa-solid fa-plus text-blue-600 text-xs"></i>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">{{ activity.description }}</p>
                        <p class="text-xs text-gray-500">{{ activity.relative_time }}</p>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-8">
                    <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                        <i class="fa-solid fa-clock text-gray-400 text-xl"></i>
                    </div>
                    <p class="text-gray-500">No recent activity</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    {% comment %} <!-- Print-only footer -->
    <div class="print-only print-footer">
        <div>RadioMention - Analytics Dashboard</div>
        <div>Generated on {% now "F d, Y \a\t g:i A" %}</div>
    </div> {% endcomment %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/simplified-template-selector.js' %}"></script>
<script src="{% static 'js/print-reports.js' %}"></script>
<script src="{% static 'js/chart.min.js' %}"></script>
<script>
// Override default date functions for this report type
function getDefaultStartDate() {
  const date = new Date()
  date.setDate(date.getDate() - {{ time_range|default:30 }})
  return date.toISOString().split('T')[0]
}

function getDefaultEndDate() {
  return new Date().toISOString().split('T')[0]
}

// Initialize print manager
const printManager = new PrintManager()
</script>
<script>
// Real data from Django backend
const dailyData = {{ daily_data|safe }};

// Prepare mentions over time chart data
const mentionsData = {
    labels: dailyData.map(d => d.day_name),
    datasets: [{
        label: 'Mentions Created',
        data: dailyData.map(d => d.mentions),
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true
    }, {
        label: 'Mentions Completed',
        data: dailyData.map(d => d.completed),
        borderColor: 'rgb(34, 197, 94)',
        backgroundColor: 'rgba(34, 197, 94, 0.1)',
        tension: 0.4,
        fill: true
    }]
};

// Status distribution data
const statusData = {
    labels: ['Approved', 'Pending', 'Rejected'],
    datasets: [{
        data: [{{ approved_mentions|default:0 }}, {{ pending_mentions|default:0 }}, {{ rejected_mentions|default:0 }}],
        backgroundColor: [
            'rgb(34, 197, 94)',
            'rgb(251, 191, 36)',
            'rgb(239, 68, 68)'
        ]
    }]
};

// Initialize charts
const mentionsChart = new Chart(document.getElementById('mentionsChart'), {
    type: 'line',
    data: mentionsData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        }
    }
});

const statusChart = new Chart(document.getElementById('statusChart'), {
    type: 'doughnut',
    data: statusData,
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Time range update function
function updateTimeRange() {
    const form = document.getElementById('timeRangeForm');
    form.submit();
}

// Refresh data function
function refreshData() {
    // Show loading state
    const refreshBtn = event.target;
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-1"></i>Refreshing...';
    refreshBtn.disabled = true;

    // Reload the page to get fresh data
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// Initialize tooltips and other interactive elements
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to metric cards
    const metricCards = document.querySelectorAll('.bg-white.rounded-lg.shadow-sm');
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('shadow-md');
        });
        card.addEventListener('mouseleave', function() {
            this.classList.remove('shadow-md');
        });
    });
});
</script>
{% endblock %}
