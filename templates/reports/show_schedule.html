{% extends 'base.html' %}
{% load static %}

{% block title %}
  Show Schedule Report - RadioMention
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Show Schedule Report</h1>
            <p class="text-sm text-gray-600 mt-1">Current show schedules and upcoming mentions</p>
          </div>
          <div class="flex space-x-2">
            <button onclick="window.location.href='{% url 'reports:report_list' %}'" class="bg-green-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-green-700">
              <i class="fa-solid fa-paint-brush mr-1"></i>
              Back to Reports
            </button>
            <!-- PDF Export Button -->
            {% include 'reports/partials/pdf_export_button.html' with report_type='show-schedule' %}
          </div>
        </div>
      </div>
    </div>

    <!-- Show Schedule Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Show Schedules</h3>
      </div>
      <div class="overflow-x-auto">
        {% if shows %}
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Upcoming Mentions</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for show in shows %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8">
                        <div class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                          <i class="fa-solid fa-broadcast-tower text-green-600 text-sm"></i>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">{{ show.name }}</div>
                        <div class="text-sm text-gray-500">{{ show.description|default:'No description'|truncatechars:30 }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {% if show.start_time and show.end_time %}
                        {{ show.start_time|time:'H:i' }} - {{ show.end_time|time:'H:i' }}
                      {% else %}
                        Not scheduled
                      {% endif %}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {% if show.days_of_week %}
                        {{ show.days_of_week }}
                      {% else %}
                        No specific days
                      {% endif %}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">
                      {% if upcoming_mentions %}
                        {{ upcoming_mentions|length|default:0 }}
                      {% else %}
                        0
                      {% endif %}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        {% else %}
          <div class="text-center py-12">
            <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
              <i class="fa-solid fa-calendar text-gray-400 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Shows Scheduled</h3>
            <p class="text-gray-500">No show schedules found in the system.</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}
