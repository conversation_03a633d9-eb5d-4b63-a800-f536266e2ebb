{% extends 'base.html' %}
{% load static %}

{% block title %}{% if is_editing %}Edit Template{% else %}Create Template{% endif %} - Template Designer{% endblock %}

{% block extra_css %}
<style>
  .highlighted-section {
    outline: 2px solid #3F20FB;
    background-color: rgba(63, 32, 251, 0.1);
  }

  .edit-button {
    position: absolute;
    z-index: 1000;
  }

  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* Ensure proper sizing for the editor */
  #main-content {
    min-height: calc(100vh - 110px);
  }

  #element-library {
    min-width: 280px;
    max-width: 320px;
  }

  #properties-panel {
    min-width: 320px;
    max-width: 360px;
  }

  #canvas-area {
    flex: 1;
    min-width: 600px;
  }

  #templateCanvas {
    min-width: 595px;
    min-height: 842px;
    transform-origin: top center;
  }

  /* Better text sizing */
  .text-xs { font-size: 0.75rem; }
  .text-sm { font-size: 0.875rem; }
  .text-base { font-size: 1rem; }
  .text-lg { font-size: 1.125rem; }
  .text-xl { font-size: 1.25rem; }
  .text-2xl { font-size: 1.5rem; }
  .text-3xl { font-size: 1.875rem; }

  /* Better spacing */
  .p-2 { padding: 0.5rem; }
  .p-3 { padding: 0.75rem; }
  .p-4 { padding: 1rem; }
  .p-6 { padding: 1.5rem; }
  .p-8 { padding: 2rem; }

  /* Better button sizing */
  button, .btn {
    min-height: 36px;
    padding: 8px 16px;
    font-size: 14px;
  }

  /* Better input sizing */
  input, select, textarea {
    min-height: 36px;
    padding: 8px 12px;
    font-size: 14px;
  }

  /* Canvas and element styling */
  #templateCanvas {
    position: relative;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .resize-handle {
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  .resize-handle:hover {
    background: #2563EB !important;
    transform: scale(1.2);
  }

  /* Drag feedback */
  .dragging {
    opacity: 0.8;
    transform: rotate(2deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Selection feedback */
  .selected-element {
    box-shadow: 0 0 0 2px #3B82F6, 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Hover feedback for elements */
  .canvas-element:hover {
    box-shadow: 0 0 0 1px #93C5FD;
  }

  /* Toolbar improvements */
  #header {
    height: 64px;
    min-height: 64px;
  }

  #secondary-toolbar {
    height: 56px;
    min-height: 56px;
  }

  #footer {
    height: 48px;
    min-height: 48px;
  }

  /* Responsive improvements */
  @media (max-width: 1024px) {
    #element-library {
      width: 240px;
      min-width: 240px;
    }

    #properties-panel {
      width: 280px;
      min-width: 280px;
    }

    #canvas-area {
      min-width: 400px;
    }
  }

  @media (max-width: 768px) {
    #main-content {
      flex-direction: column;
      height: auto;
      min-height: calc(100vh - 160px);
    }

    #element-library,
    #properties-panel {
      width: 100%;
      max-height: 200px;
      border-right: none;
      border-left: none;
      border-bottom: 1px solid #e5e7eb;
    }

    #canvas-area {
      min-width: 100%;
      padding: 1rem;
    }

    #templateCanvas {
      width: 100%;
      max-width: 595px;
      margin: 0 auto;
    }
  }
</style>
{% endblock %}

{% block content %}
<!-- Top Navigation Bar -->
<header id="header" class="bg-white border-b border-gray-200 flex items-center justify-between px-6 py-3 h-16 z-50">
    <div class="flex items-center space-x-4">
        <div class="flex items-center">
            <a href="{% url 'template_designer:dashboard' %}" class="p-2 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100">
                <i class="fa-solid fa-arrow-left"></i>
            </a>
            <span class="font-semibold text-xl text-primary-600 ml-3">Template Designer</span>
        </div>
        <div class="flex items-center space-x-3">
            <button class="p-2 text-gray-600 hover:text-gray-800 text-lg">
                <i class="fa-solid fa-file"></i>
            </button>
            <span class="text-gray-700 font-medium text-lg">
                {% if template %}{{ template.name }}{% else %}New Template{% endif %}
            </span>
            <button class="p-2 text-gray-600 hover:text-gray-800 text-lg">
                <i class="fa-solid fa-pen"></i>
            </button>
        </div>
    </div>
    <div class="flex items-center space-x-4">
        <button class="p-3 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 text-lg" onclick="undoAction()">
            <i class="fa-solid fa-undo"></i>
        </button>
        <button class="p-3 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 text-lg" onclick="redoAction()">
            <i class="fa-solid fa-redo"></i>
        </button>
        <div class="h-6 w-px bg-gray-300 mx-2"></div>
        <button class="flex items-center px-4 py-2 text-gray-700 font-medium rounded-md hover:bg-gray-100" onclick="previewTemplate()">
            <i class="fa-solid fa-eye mr-2"></i>
            Preview
        </button>
        <button class="flex items-center px-4 py-2 text-gray-700 font-medium rounded-md hover:bg-gray-100" onclick="exportTemplate()">
            <i class="fa-solid fa-download mr-2"></i>
            Export
        </button>
        <button class="px-6 py-2 font-medium bg-primary-600 text-white rounded-md hover:bg-primary-700" onclick="saveTemplate()">
            Save
        </button>
        <div class="flex items-center ml-3">
            <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                <i class="fa-solid fa-user text-primary-600"></i>
            </div>
        </div>
    </div>
</header>

<!-- Secondary Toolbar -->
<div id="secondary-toolbar" class="bg-white border-b border-gray-200 px-6 py-3 flex justify-between items-center">
    <div class="flex items-center space-x-4">
        <button class="p-2 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 text-lg" onclick="addElement()">
            <i class="fa-solid fa-plus"></i>
        </button>
        <div class="flex items-center space-x-2">
            <button class="p-2 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 text-lg" onclick="formatText('bold')">
                <i class="fa-solid fa-bold"></i>
            </button>
            <button class="p-2 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 text-lg" onclick="formatText('italic')">
                <i class="fa-solid fa-italic"></i>
            </button>
            <button class="p-2 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 text-lg" onclick="formatText('underline')">
                <i class="fa-solid fa-underline"></i>
            </button>
        </div>
        <div class="flex items-center space-x-2">
            <button class="p-2 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 text-lg" onclick="alignText('left')">
                <i class="fa-solid fa-align-left"></i>
            </button>
            <button class="p-2 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 text-lg" onclick="alignText('center')">
                <i class="fa-solid fa-align-center"></i>
            </button>
            <button class="p-2 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 text-lg" onclick="alignText('right')">
                <i class="fa-solid fa-align-right"></i>
            </button>
        </div>
        <div class="h-5 w-px bg-gray-300 mx-1"></div>
        <div class="flex items-center space-x-2">
            <span class="text-gray-700 text-sm">Font:</span>
            <div class="relative">
                <select id="fontFamily" class="border border-gray-300 rounded px-3 py-1 text-sm">
                    <option>Inter</option>
                    <option>Arial</option>
                    <option>Helvetica</option>
                    <option>Times New Roman</option>
                </select>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <span class="text-gray-700 text-sm">Size:</span>
            <div class="relative">
                <select id="fontSize" class="border border-gray-300 rounded px-3 py-1 text-sm">
                    <option>8pt</option>
                    <option>10pt</option>
                    <option selected>12pt</option>
                    <option>14pt</option>
                    <option>16pt</option>
                    <option>18pt</option>
                    <option>24pt</option>
                </select>
            </div>
        </div>
        <div class="flex items-center space-x-2">
            <span class="text-gray-700 text-sm">Color:</span>
            <div class="flex items-center space-x-1">
                <button class="w-6 h-6 bg-black rounded-full ring-2 ring-offset-2 ring-primary-400" onclick="setTextColor('#000000')"></button>
                <button class="w-6 h-6 bg-red-500 rounded-full" onclick="setTextColor('#EF4444')"></button>
                <button class="w-6 h-6 bg-blue-500 rounded-full" onclick="setTextColor('#3B82F6')"></button>
                <button class="w-6 h-6 bg-green-500 rounded-full" onclick="setTextColor('#10B981')"></button>
                <button class="w-6 h-6 bg-white rounded-full border border-gray-300" onclick="setTextColor('#FFFFFF')"></button>
                <button class="w-6 h-6 flex items-center justify-center bg-gray-100 rounded-full border border-gray-300" onclick="showColorPicker()">
                    <i class="fa-solid fa-plus text-xs text-gray-600"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="flex items-center space-x-3">
        <div class="flex items-center space-x-1">
            <button class="p-1.5 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100" onclick="zoomOut()">
                <i class="fa-solid fa-magnifying-glass-minus"></i>
            </button>
            <span class="text-sm text-gray-700" id="zoomLevel">100%</span>
            <button class="p-1.5 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100" onclick="zoomIn()">
                <i class="fa-solid fa-magnifying-glass-plus"></i>
            </button>
        </div>
        <button class="p-1.5 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100" onclick="toggleGrid()">
            <i class="fa-solid fa-grid"></i>
        </button>
        <button class="p-1.5 text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100" onclick="toggleRuler()">
            <i class="fa-solid fa-ruler"></i>
        </button>
    </div>
</div>

<!-- Main Content Area -->
<div id="main-content" class="flex h-[calc(100vh-160px)]">
    <!-- Left Sidebar - Element Library -->
    <div id="element-library" class="w-80 bg-white border-r border-gray-200 flex flex-col overflow-y-auto">
        <div class="p-4 border-b border-gray-200">
            <div class="relative">
                <input type="text" placeholder="Search elements..." class="w-full px-4 py-3 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                <i class="fa-solid fa-search absolute left-3 top-3.5 text-gray-400"></i>
            </div>
        </div>

        <!-- Template Settings Section -->
        <div class="p-5 border-b border-gray-200">
            <h3 class="font-semibold text-gray-700 mb-4">Template Settings</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm text-gray-600 mb-2">Name</label>
                    <input type="text" id="templateName"
                           value="{% if template %}{{ template.name }}{% endif %}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md"
                           placeholder="Template name">
                </div>
                <div>
                    <label class="block text-sm text-gray-600 mb-2">Type</label>
                    <select id="templateType" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        {% for type_key, type_label in template_types %}
                        <option value="{{ type_key }}" {% if template and template.template_type == type_key %}selected{% endif %}>
                            {{ type_label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm text-gray-600 mb-2">Category</label>
                    <select id="templateCategory" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        <option value="">Select category</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if template and template.category_id == category.id %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="isPublic"
                           {% if template and template.is_public %}checked{% endif %}
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                    <label for="isPublic" class="ml-3 block text-sm text-gray-700">
                        Public template
                    </label>
                </div>
            </div>
        </div>


        <!-- Basic Elements Section -->
        <div class="p-5 border-b border-gray-200">
            <h3 class="font-semibold text-gray-700 mb-4">Basic Elements</h3>
            <div class="space-y-3">
                <div class="flex items-center p-3 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addTextElement()">
                    <i class="fa-solid fa-font text-gray-600 mr-4 text-lg"></i>
                    <span class="text-gray-700">Text</span>
                </div>
                <div class="flex items-center p-3 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addImageElement()">
                    <i class="fa-regular fa-image text-gray-600 mr-4 text-lg"></i>
                    <span class="text-gray-700">Image</span>
                </div>
                <div class="flex items-center p-3 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addLineElement()">
                    <i class="fa-solid fa-minus text-gray-600 mr-4 text-lg"></i>
                    <span class="text-gray-700">Line</span>
                </div>
                <div class="flex items-center p-3 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addRectangleElement()">
                    <i class="fa-regular fa-square text-gray-600 mr-4 text-lg"></i>
                    <span class="text-gray-700">Rectangle</span>
                </div>
                <div class="flex items-center p-3 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addCircleElement()">
                    <i class="fa-regular fa-circle text-gray-600 mr-4 text-lg"></i>
                    <span class="text-gray-700">Circle</span>
                </div>
            </div>
        </div>

        <!-- Business Elements Section -->
        <div class="p-4 border-b border-gray-200">
            <h3 class="font-semibold text-sm text-gray-700 mb-3">Business Elements</h3>
            <div class="space-y-2">
                <div class="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addTableElement()">
                    <i class="fa-solid fa-table text-gray-600 mr-3"></i>
                    <span class="text-sm text-gray-700">Table</span>
                </div>
                <div class="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addLogoElement()">
                    <i class="fa-solid fa-building text-gray-600 mr-3"></i>
                    <span class="text-sm text-gray-700">Logo</span>
                </div>
                <div class="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addSignatureElement()">
                    <i class="fa-solid fa-signature text-gray-600 mr-3"></i>
                    <span class="text-sm text-gray-700">Signature</span>
                </div>
                <div class="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addDateElement()">
                    <i class="fa-regular fa-calendar text-gray-600 mr-3"></i>
                    <span class="text-sm text-gray-700">Date</span>
                </div>
                <div class="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addNumberElement()">
                    <i class="fa-solid fa-hashtag text-gray-600 mr-3"></i>
                    <span class="text-sm text-gray-700">Number</span>
                </div>
            </div>
        </div>

        <!-- Data Elements Section -->
        <div class="p-4">
            <h3 class="font-semibold text-sm text-gray-700 mb-3">Data Elements</h3>
            <div class="space-y-2">
                <div class="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addVariableElement()">
                    <i class="fa-solid fa-code-bracket text-gray-600 mr-3"></i>
                    <span class="text-sm text-gray-700">Variable</span>
                </div>
                <div class="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addListElement()">
                    <i class="fa-solid fa-list text-gray-600 mr-3"></i>
                    <span class="text-sm text-gray-700">List</span>
                </div>
                <div class="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer" onclick="addConditionalElement()">
                    <i class="fa-solid fa-code text-gray-600 mr-3"></i>
                    <span class="text-sm text-gray-700">Conditional</span>
                </div>
            </div>

            <!-- Variables List -->
            <div class="mt-4">
                <h4 class="font-semibold text-xs text-gray-500 uppercase mb-2">Template Variables</h4>
                <div id="variablesList" class="space-y-2">
                    <!-- Variables will be added here dynamically -->
                </div>
                <button onclick="addVariable()" class="mt-2 w-full bg-gray-100 text-gray-700 px-2 py-1.5 rounded-md text-xs font-medium hover:bg-gray-200">
                    <i class="fa-solid fa-plus mr-1"></i>
                    Add Variable
                </button>
            </div>
        </div>
    </div>

    <!-- Center Canvas Area -->
    <div id="canvas-area" class="flex-1 bg-gray-100 overflow-auto flex justify-center p-8">
        <div class="bg-white shadow-md w-[595px] h-[842px] relative" id="templateCanvas">
            <!-- Template Content Area -->
            <div class="p-8" id="canvasContent">
                <!-- Dynamic content will be added here -->
                {% if template and template.content %}
                    <div class="template-content">{{ template.content|safe }}</div>
                {% else %}
                    <div class="text-center text-gray-400 mt-32">
                        <i class="fa-solid fa-file-lines text-4xl mb-4"></i>
                        <p class="text-lg">Start designing your template</p>
                        <p class="text-sm">Drag elements from the left panel to begin</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Right Sidebar - Properties Panel -->
    <div id="properties-panel" class="w-80 bg-white border-l border-gray-200 overflow-y-auto">
        <div class="p-5 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="font-semibold text-gray-700 text-lg">Properties</h3>
                    <p class="text-sm text-gray-500 mt-1" id="selectedElementType">No element selected</p>
                </div>
                <button id="deleteElementBtn" onclick="deleteSelectedElement()"
                        class="bg-red-600 text-white px-3 py-1.5 rounded-md text-sm font-medium hover:bg-red-700 hidden">
                    <i class="fa-solid fa-trash mr-1"></i>
                    Delete
                </button>
            </div>
        </div>

        <!-- Position & Size Section -->
        <div class="p-4 border-b border-gray-200" id="positionSizeSection" style="display: none;">
            <h4 class="text-xs font-semibold text-gray-500 uppercase mb-3">Position & Size</h4>
            <div class="grid grid-cols-2 gap-3">
                <div>
                    <label class="block text-xs text-gray-600 mb-1">X Position</label>
                    <input type="text" id="elementX" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Y Position</label>
                    <input type="text" id="elementY" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Width</label>
                    <input type="text" id="elementWidth" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Height</label>
                    <input type="text" id="elementHeight" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                </div>
            </div>
        </div>

        <!-- Text Properties Section -->
        <div class="p-4 border-b border-gray-200" id="textPropertiesSection" style="display: none;">
            <h4 class="text-xs font-semibold text-gray-500 uppercase mb-3">Text Properties</h4>
            <div class="space-y-3">
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Content</label>
                    <textarea id="textContent" rows="3" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md"></textarea>
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Font Family</label>
                    <select id="textFontFamily" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                        <option>Inter</option>
                        <option>Arial</option>
                        <option>Helvetica</option>
                        <option>Times New Roman</option>
                    </select>
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Font Size</label>
                    <input type="text" id="textFontSize" value="12pt" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Color</label>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-black rounded-full border border-gray-300" id="textColorPreview"></div>
                        <input type="text" id="textColor" value="#000000" class="flex-1 px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                    </div>
                </div>
            </div>
        </div>

        <!-- Styling Section -->
        <div class="p-4 border-b border-gray-200" id="stylingSection" style="display: none;">
            <h4 class="text-xs font-semibold text-gray-500 uppercase mb-3">Styling</h4>
            <div class="space-y-3">
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Background Color</label>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-white rounded-full border border-gray-300" id="bgColorPreview"></div>
                        <input type="text" id="backgroundColor" value="#FFFFFF" class="flex-1 px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                    </div>
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Border Color</label>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gray-200 rounded-full border border-gray-300" id="borderColorPreview"></div>
                        <input type="text" id="borderColor" value="#E5E7EB" class="flex-1 px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                    </div>
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Border Width</label>
                    <input type="text" id="borderWidth" value="1px" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Padding</label>
                    <div class="grid grid-cols-4 gap-1">
                        <input type="text" id="paddingTop" value="0px" placeholder="T" class="px-2 py-1.5 text-sm border border-gray-300 rounded-md text-center">
                        <input type="text" id="paddingRight" value="0px" placeholder="R" class="px-2 py-1.5 text-sm border border-gray-300 rounded-md text-center">
                        <input type="text" id="paddingBottom" value="0px" placeholder="B" class="px-2 py-1.5 text-sm border border-gray-300 rounded-md text-center">
                        <input type="text" id="paddingLeft" value="0px" placeholder="L" class="px-2 py-1.5 text-sm border border-gray-300 rounded-md text-center">
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Binding Section -->
        <div class="p-4" id="dataBindingSection" style="display: none;">
            <h4 class="text-xs font-semibold text-gray-500 uppercase mb-3">Data Binding</h4>
            <div class="space-y-3">
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Variable Name</label>
                    <input type="text" id="variableName" placeholder="e.g., company_name" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                </div>
                <div>
                    <label class="block text-xs text-gray-600 mb-1">Default Value</label>
                    <input type="text" id="defaultValue" placeholder="Default text" class="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md">
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="isRequired" class="mr-2">
                    <label for="isRequired" class="text-xs text-gray-600">Required field</label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bottom Status Bar -->
<div id="footer" class="bg-white border-t border-gray-200 px-6 py-3 flex justify-between items-center h-12">
    <div class="flex items-center space-x-6 text-sm text-gray-600">
        <span>Page: 1 of 1</span>
        <span>A4 (210 × 297 mm)</span>
    </div>
    <div class="flex items-center space-x-6">
        <div class="flex items-center space-x-2 text-sm text-gray-600">
            <button class="p-2 hover:text-gray-800" onclick="previousPage()">
                <i class="fa-solid fa-angle-left"></i>
            </button>
            <span id="currentPage">1</span>
            <button class="p-2 hover:text-gray-800" onclick="nextPage()">
                <i class="fa-solid fa-angle-right"></i>
            </button>
        </div>
        <div class="text-sm text-gray-600">
            <span id="saveStatus">Last saved: Never</span>
        </div>
    </div>
</div>

<script>
let templateData = {
    id: {% if template %}{{ template.id }}{% else %}null{% endif %},
    variables: {% if template %}{{ template.variables|safe }}{% else %}{}{% endif %},
    selectedElement: null,
    zoom: 100,
    gridVisible: false,
    rulerVisible: false
};

// Drag and drop functionality
let isDragging = false;
let dragOffset = { x: 0, y: 0 };
let currentDragElement = null;

// Make elements draggable
function makeDraggable(element) {
    element.addEventListener('mousedown', startDrag);
    element.addEventListener('dragstart', (e) => e.preventDefault());
}

function startDrag(e) {
    if (e.target.closest('.resize-handle')) return; // Don't drag when resizing

    currentDragElement = e.currentTarget;
    isDragging = true;

    const rect = currentDragElement.getBoundingClientRect();
    const canvasRect = document.getElementById('canvasContent').getBoundingClientRect();

    dragOffset.x = e.clientX - rect.left;
    dragOffset.y = e.clientY - rect.top;

    currentDragElement.style.zIndex = '1000';
    currentDragElement.classList.add('dragging');

    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);

    e.preventDefault();
}

function drag(e) {
    if (!isDragging || !currentDragElement) return;

    const canvasRect = document.getElementById('canvasContent').getBoundingClientRect();

    let newX = e.clientX - canvasRect.left - dragOffset.x;
    let newY = e.clientY - canvasRect.top - dragOffset.y;

    // Constrain to canvas bounds
    newX = Math.max(0, Math.min(newX, canvasRect.width - currentDragElement.offsetWidth));
    newY = Math.max(0, Math.min(newY, canvasRect.height - currentDragElement.offsetHeight));

    currentDragElement.style.left = newX + 'px';
    currentDragElement.style.top = newY + 'px';
}

function stopDrag() {
    if (currentDragElement) {
        currentDragElement.style.zIndex = '';
        currentDragElement.classList.remove('dragging');
    }

    isDragging = false;
    currentDragElement = null;

    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mouseup', stopDrag);
}

// Make elements resizable
function makeResizable(element) {
    const resizeHandles = ['nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'];

    resizeHandles.forEach(direction => {
        const handle = document.createElement('div');
        handle.className = `resize-handle resize-${direction}`;
        handle.style.cssText = `
            position: absolute;
            background: #3B82F6;
            border: 1px solid white;
            width: 8px;
            height: 8px;
            cursor: ${direction}-resize;
            display: none;
        `;

        // Position handles
        switch(direction) {
            case 'nw': handle.style.top = '-4px'; handle.style.left = '-4px'; break;
            case 'ne': handle.style.top = '-4px'; handle.style.right = '-4px'; break;
            case 'sw': handle.style.bottom = '-4px'; handle.style.left = '-4px'; break;
            case 'se': handle.style.bottom = '-4px'; handle.style.right = '-4px'; break;
            case 'n': handle.style.top = '-4px'; handle.style.left = '50%'; handle.style.transform = 'translateX(-50%)'; break;
            case 's': handle.style.bottom = '-4px'; handle.style.left = '50%'; handle.style.transform = 'translateX(-50%)'; break;
            case 'e': handle.style.right = '-4px'; handle.style.top = '50%'; handle.style.transform = 'translateY(-50%)'; break;
            case 'w': handle.style.left = '-4px'; handle.style.top = '50%'; handle.style.transform = 'translateY(-50%)'; break;
        }

        handle.addEventListener('mousedown', (e) => startResize(e, element, direction));
        element.appendChild(handle);
    });
}

function startResize(e, element, direction) {
    e.stopPropagation();

    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = parseInt(window.getComputedStyle(element).width);
    const startHeight = parseInt(window.getComputedStyle(element).height);
    const startLeft = parseInt(element.style.left);
    const startTop = parseInt(element.style.top);

    function resize(e) {
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;

        let newWidth = startWidth;
        let newHeight = startHeight;
        let newLeft = startLeft;
        let newTop = startTop;

        if (direction.includes('e')) newWidth = startWidth + deltaX;
        if (direction.includes('w')) { newWidth = startWidth - deltaX; newLeft = startLeft + deltaX; }
        if (direction.includes('s')) newHeight = startHeight + deltaY;
        if (direction.includes('n')) { newHeight = startHeight - deltaY; newTop = startTop + deltaY; }

        // Minimum size constraints
        newWidth = Math.max(20, newWidth);
        newHeight = Math.max(20, newHeight);

        element.style.width = newWidth + 'px';
        element.style.height = newHeight + 'px';
        element.style.left = newLeft + 'px';
        element.style.top = newTop + 'px';

        // Update properties panel if this element is selected
        if (templateData.selectedElement === element) {
            updatePropertiesPanel(element);
        }
    }

    function stopResize() {
        document.removeEventListener('mousemove', resize);
        document.removeEventListener('mouseup', stopResize);
    }

    document.addEventListener('mousemove', resize);
    document.addEventListener('mouseup', stopResize);
}

// Element creation functions
function addTextElement() {
    const canvas = document.getElementById('canvasContent');
    const textElement = document.createElement('div');
    textElement.className = 'canvas-element absolute border border-dashed border-gray-400 p-2 cursor-move bg-white';
    textElement.style.left = '50px';
    textElement.style.top = '50px';
    textElement.style.width = '200px';
    textElement.style.minHeight = '40px';
    textElement.innerHTML = 'Sample Text';
    textElement.contentEditable = true;
    textElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(textElement, 'text');
    };

    // Double-click to edit text inline
    textElement.addEventListener('dblclick', function(e) {
        e.stopPropagation();
        this.focus();
        // Select all text
        const range = document.createRange();
        range.selectNodeContents(this);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
    });

    // Update properties when text changes
    textElement.addEventListener('input', function() {
        if (templateData.selectedElement === this) {
            document.getElementById('textContent').value = this.textContent;
        }
    });

    makeDraggable(textElement);
    makeResizable(textElement);
    canvas.appendChild(textElement);
    selectElement(textElement, 'text');
}

function addImageElement() {
    const canvas = document.getElementById('canvasContent');
    const imageElement = document.createElement('div');
    imageElement.className = 'canvas-element absolute border border-dashed border-gray-400 p-4 cursor-move bg-gray-100 flex items-center justify-center';
    imageElement.style.left = '50px';
    imageElement.style.top = '100px';
    imageElement.style.width = '150px';
    imageElement.style.height = '100px';
    imageElement.innerHTML = '<i class="fa-regular fa-image text-gray-500 text-2xl"></i><span class="ml-2 text-gray-500">Image</span>';
    imageElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(imageElement, 'image');
    };

    makeDraggable(imageElement);
    makeResizable(imageElement);
    canvas.appendChild(imageElement);
    selectElement(imageElement, 'image');
}

function addTableElement() {
    const canvas = document.getElementById('canvasContent');
    const tableElement = document.createElement('div');
    tableElement.className = 'canvas-element absolute border border-dashed border-gray-400 cursor-move bg-white';
    tableElement.style.left = '50px';
    tableElement.style.top = '150px';
    tableElement.style.width = '300px';
    tableElement.innerHTML = `
        <table class="w-full text-sm border-collapse">
            <thead>
                <tr class="bg-gray-100">
                    <th class="border border-gray-300 p-2">Header 1</th>
                    <th class="border border-gray-300 p-2">Header 2</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="border border-gray-300 p-2">Data 1</td>
                    <td class="border border-gray-300 p-2">Data 2</td>
                </tr>
            </tbody>
        </table>
    `;
    tableElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(tableElement, 'table');
    };

    makeDraggable(tableElement);
    makeResizable(tableElement);
    canvas.appendChild(tableElement);
    selectElement(tableElement, 'table');
}

function addVariableElement() {
    const variableName = prompt('Enter variable name:');
    if (variableName) {
        const canvas = document.getElementById('canvasContent');
        const variableElement = document.createElement('div');
        variableElement.className = 'absolute border border-dashed border-primary-400 p-2 cursor-move bg-primary-50';
        variableElement.style.left = '50px';
        variableElement.style.top = '200px';
        variableElement.style.minWidth = '100px';
        variableElement.style.minHeight = '30px';
        variableElement.innerHTML = '{{' + variableName + '}}';
        variableElement.dataset.variableName = variableName;
        variableElement.onclick = (e) => {
            e.stopPropagation();
            selectElement(variableElement, 'variable');
        };

        // Double-click to edit variable name
        variableElement.addEventListener('dblclick', function(e) {
            e.stopPropagation();
            const newName = prompt('Edit variable name:', variableName);
            if (newName && newName !== variableName) {
                // Update variable in data
                delete templateData.variables[variableName];
                templateData.variables[newName] = { type: 'text', required: false };

                // Update element
                this.innerHTML = '{{' + newName + '}}';
                this.dataset.variableName = newName;

                updateVariablesList();
            }
        });

        makeDraggable(variableElement);
        makeResizable(variableElement);
        canvas.appendChild(variableElement);
        selectElement(variableElement, 'variable');

        // Add to variables list
        if (!templateData.variables[variableName]) {
            templateData.variables[variableName] = { type: 'text', required: false };
            updateVariablesList();
        }
    }
}

function addVariable() {
    const variablesList = document.getElementById('variablesList');
    const variableDiv = document.createElement('div');
    variableDiv.className = 'flex space-x-1 items-center';
    variableDiv.innerHTML = `
        <input type="text" placeholder="Variable name" class="flex-1 px-2 py-1 border border-gray-300 rounded text-xs">
        <select class="px-2 py-1 border border-gray-300 rounded text-xs">
            <option value="text">Text</option>
            <option value="number">Number</option>
            <option value="date">Date</option>
            <option value="email">Email</option>
        </select>
        <button onclick="this.parentElement.remove()" class="text-red-600 hover:text-red-800 text-xs">
            <i class="fa-solid fa-trash"></i>
        </button>
    `;
    variablesList.appendChild(variableDiv);
}

// Element selection and properties
function selectElement(element, type) {
    // Remove previous selection
    if (templateData.selectedElement) {
        templateData.selectedElement.classList.remove('ring-2', 'ring-primary-500', 'selected-element');
        // Hide resize handles
        const handles = templateData.selectedElement.querySelectorAll('.resize-handle');
        handles.forEach(handle => handle.style.display = 'none');
    }

    // Select new element
    templateData.selectedElement = element;
    element.classList.add('ring-2', 'ring-primary-500', 'selected-element');

    // Show resize handles
    const handles = element.querySelectorAll('.resize-handle');
    handles.forEach(handle => handle.style.display = 'block');

    // Show delete button
    document.getElementById('deleteElementBtn').classList.remove('hidden');

    // Update properties panel
    document.getElementById('selectedElementType').textContent = type.charAt(0).toUpperCase() + type.slice(1) + ' Element';

    // Show relevant property sections
    document.getElementById('positionSizeSection').style.display = 'block';
    document.getElementById('stylingSection').style.display = 'block';

    if (type === 'text' || type === 'variable') {
        document.getElementById('textPropertiesSection').style.display = 'block';
        document.getElementById('dataBindingSection').style.display = type === 'variable' ? 'block' : 'none';

        // Populate text content
        if (type === 'text') {
            document.getElementById('textContent').value = element.textContent || element.innerText;
        }
    } else {
        document.getElementById('textPropertiesSection').style.display = 'none';
        document.getElementById('dataBindingSection').style.display = 'none';
    }

    updatePropertiesPanel(element);
}

function updatePropertiesPanel(element) {
    // Update position and size
    document.getElementById('elementX').value = parseInt(element.style.left) || 0;
    document.getElementById('elementY').value = parseInt(element.style.top) || 0;
    document.getElementById('elementWidth').value = parseInt(element.style.width) || element.offsetWidth;
    document.getElementById('elementHeight').value = parseInt(element.style.height) || element.offsetHeight;

    // Update styling
    const computedStyle = window.getComputedStyle(element);
    document.getElementById('backgroundColor').value = rgbToHex(computedStyle.backgroundColor) || '#FFFFFF';
    document.getElementById('borderColor').value = rgbToHex(computedStyle.borderColor) || '#E5E7EB';
    document.getElementById('borderWidth').value = computedStyle.borderWidth || '1px';

    // Update color previews
    document.getElementById('bgColorPreview').style.backgroundColor = computedStyle.backgroundColor;
    document.getElementById('borderColorPreview').style.backgroundColor = computedStyle.borderColor;
}

// Helper function to convert RGB to Hex
function rgbToHex(rgb) {
    if (!rgb || rgb === 'rgba(0, 0, 0, 0)') return '#FFFFFF';

    const result = rgb.match(/\d+/g);
    if (!result) return '#FFFFFF';

    return '#' + result.slice(0, 3).map(x => {
        const hex = parseInt(x).toString(16);
        return hex.length === 1 ? '0' + hex : hex;
    }).join('');
}

// Toolbar functions
function formatText(format) {
    if (templateData.selectedElement) {
        const element = templateData.selectedElement;
        switch(format) {
            case 'bold':
                element.style.fontWeight = element.style.fontWeight === 'bold' ? 'normal' : 'bold';
                break;
            case 'italic':
                element.style.fontStyle = element.style.fontStyle === 'italic' ? 'normal' : 'italic';
                break;
            case 'underline':
                element.style.textDecoration = element.style.textDecoration === 'underline' ? 'none' : 'underline';
                break;
        }
    }
}

function alignText(alignment) {
    if (templateData.selectedElement) {
        templateData.selectedElement.style.textAlign = alignment;
    }
}

function setTextColor(color) {
    if (templateData.selectedElement) {
        templateData.selectedElement.style.color = color;
        document.getElementById('textColor').value = color;
        document.getElementById('textColorPreview').style.backgroundColor = color;
    }
}

function zoomIn() {
    templateData.zoom = Math.min(templateData.zoom + 10, 200);
    updateZoom();
}

function zoomOut() {
    templateData.zoom = Math.max(templateData.zoom - 10, 50);
    updateZoom();
}

function updateZoom() {
    document.getElementById('zoomLevel').textContent = templateData.zoom + '%';
    const canvas = document.getElementById('templateCanvas');
    canvas.style.transform = 'scale(' + (templateData.zoom / 100) + ')';
}

function toggleGrid() {
    templateData.gridVisible = !templateData.gridVisible;
    const canvas = document.getElementById('templateCanvas');
    if (templateData.gridVisible) {
        canvas.style.backgroundImage = 'linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px)';
        canvas.style.backgroundSize = '20px 20px';
    } else {
        canvas.style.backgroundImage = 'none';
    }
}

function toggleRuler() {
    templateData.rulerVisible = !templateData.rulerVisible;
    // Ruler implementation would go here
}

function updateVariablesList() {
    const variablesList = document.getElementById('variablesList');
    variablesList.innerHTML = '';

    Object.keys(templateData.variables).forEach(varName => {
        const varDiv = document.createElement('div');
        varDiv.className = 'flex items-center justify-between p-2 bg-gray-50 rounded text-xs';
        varDiv.innerHTML = `
            <span class="font-mono text-primary-600">${varName}</span>
            <button onclick="removeVariable('${varName}')" class="text-red-600 hover:text-red-800">
                <i class="fa-solid fa-trash"></i>
            </button>
        `;
        variablesList.appendChild(varDiv);
    });
}

function removeVariable(varName) {
    delete templateData.variables[varName];
    updateVariablesList();
}

// Action functions
function undoAction() {
    // Undo implementation
    console.log('Undo action');
}

function redoAction() {
    // Redo implementation
    console.log('Redo action');
}

function previewTemplate() {
    // Generate preview
    const canvas = document.getElementById('templateCanvas');
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <html>
            <head>
                <title>Template Preview</title>
                <style>
                    body { font-family: Inter, sans-serif; margin: 20px; }
                    .template-preview { max-width: 595px; margin: 0 auto; }
                </style>
            </head>
            <body>
                <div class="template-preview">
                    ${canvas.innerHTML}
                </div>
            </body>
        </html>
    `);
}

function exportTemplate() {
    // Export functionality
    const templateContent = generateTemplateHTML();
    const blob = new Blob([templateContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = (document.getElementById('templateName').value || 'template') + '.html';
    a.click();
    URL.revokeObjectURL(url);
}

function generateTemplateHTML() {
    const canvas = document.getElementById('canvasContent');
    return canvas.innerHTML;
}

function saveTemplate() {
    const data = {
        template_id: templateData.id,
        name: document.getElementById('templateName').value,
        description: document.getElementById('templateDescription').value || '',
        template_type: document.getElementById('templateType').value,
        category_id: document.getElementById('templateCategory').value || null,
        content: generateTemplateHTML(),
        is_public: document.getElementById('isPublic').checked,
        variables: templateData.variables
    };

    if (!data.name.trim()) {
        alert('Please enter a template name');
        return;
    }

    // Show saving status
    document.getElementById('saveStatus').textContent = 'Saving...';

    fetch('{% url "template_designer:save_template" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            templateData.id = data.template_id;
            document.getElementById('saveStatus').textContent = 'Last saved: Just now';
            // Show success message briefly
            const saveBtn = document.querySelector('button[onclick="saveTemplate()"]');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fa-solid fa-check mr-1"></i>Saved';
            saveBtn.classList.add('bg-green-600');
            saveBtn.classList.remove('bg-primary-600');

            setTimeout(() => {
                saveBtn.innerHTML = originalText;
                saveBtn.classList.remove('bg-green-600');
                saveBtn.classList.add('bg-primary-600');
            }, 2000);
        } else {
            alert('Error saving template: ' + data.error);
            document.getElementById('saveStatus').textContent = 'Save failed';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving template');
        document.getElementById('saveStatus').textContent = 'Save failed';
    });
}

// Auto-save functionality
let autoSaveTimer;
function scheduleAutoSave() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(() => {
        if (templateData.id && document.getElementById('templateName').value.trim()) {
            saveTemplate();
        }
    }, 30000); // Auto-save every 30 seconds
}

// Add event listeners for auto-save
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables list
    updateVariablesList();

    // Add auto-save listeners
    document.getElementById('templateName').addEventListener('input', scheduleAutoSave);
    document.getElementById('templateDescription').addEventListener('input', scheduleAutoSave);

    // Initialize canvas interactions
    const canvas = document.getElementById('canvasContent');
    canvas.addEventListener('click', function(e) {
        if (e.target === canvas || e.target === document.getElementById('templateCanvas')) {
            // Deselect all elements
            deselectAllElements();
        }
    });

    // Add deselect function
    function deselectAllElements() {
        if (templateData.selectedElement) {
            templateData.selectedElement.classList.remove('ring-2', 'ring-primary-500', 'selected-element');
            // Hide resize handles
            const handles = templateData.selectedElement.querySelectorAll('.resize-handle');
            handles.forEach(handle => handle.style.display = 'none');
            templateData.selectedElement = null;
            document.getElementById('selectedElementType').textContent = 'No element selected';
            document.getElementById('deleteElementBtn').classList.add('hidden');
            document.getElementById('positionSizeSection').style.display = 'none';
            document.getElementById('textPropertiesSection').style.display = 'none';
            document.getElementById('stylingSection').style.display = 'none';
            document.getElementById('dataBindingSection').style.display = 'none';
        }
    }

    // Delete selected element function
    function deleteSelectedElement() {
        if (templateData.selectedElement) {
            // Remove from variables list if it's a variable element
            if (templateData.selectedElement.dataset.variableName) {
                delete templateData.variables[templateData.selectedElement.dataset.variableName];
                updateVariablesList();
            }

            templateData.selectedElement.remove();
            deselectAllElements();
        }
    }

    // Add property change handlers
    setupPropertyHandlers();

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 's':
                    e.preventDefault();
                    saveTemplate();
                    break;
                case 'z':
                    e.preventDefault();
                    if (e.shiftKey) {
                        redoAction();
                    } else {
                        undoAction();
                    }
                    break;
            }
        }

        // Delete selected element
        if ((e.key === 'Delete' || e.key === 'Backspace') && templateData.selectedElement) {
            // Don't delete if user is editing text
            if (document.activeElement.contentEditable === 'true' ||
                document.activeElement.tagName === 'INPUT' ||
                document.activeElement.tagName === 'TEXTAREA') {
                return;
            }

            e.preventDefault();
            deleteSelectedElement();
        }

        // Arrow keys for fine positioning
        if (templateData.selectedElement && ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
            e.preventDefault();
            const element = templateData.selectedElement;
            const step = e.shiftKey ? 10 : 1; // Hold shift for larger steps

            let currentX = parseInt(element.style.left) || 0;
            let currentY = parseInt(element.style.top) || 0;

            switch(e.key) {
                case 'ArrowUp': currentY -= step; break;
                case 'ArrowDown': currentY += step; break;
                case 'ArrowLeft': currentX -= step; break;
                case 'ArrowRight': currentX += step; break;
            }

            // Constrain to canvas bounds
            const canvas = document.getElementById('canvasContent');
            currentX = Math.max(0, Math.min(currentX, canvas.offsetWidth - element.offsetWidth));
            currentY = Math.max(0, Math.min(currentY, canvas.offsetHeight - element.offsetHeight));

            element.style.left = currentX + 'px';
            element.style.top = currentY + 'px';

            // Update properties panel
            document.getElementById('elementX').value = currentX;
            document.getElementById('elementY').value = currentY;
        }
    });
});

// Property handlers
function setupPropertyHandlers() {
    // Position and size handlers
    document.getElementById('elementX').addEventListener('input', function() {
        if (templateData.selectedElement) {
            templateData.selectedElement.style.left = this.value + 'px';
        }
    });

    document.getElementById('elementY').addEventListener('input', function() {
        if (templateData.selectedElement) {
            templateData.selectedElement.style.top = this.value + 'px';
        }
    });

    document.getElementById('elementWidth').addEventListener('input', function() {
        if (templateData.selectedElement) {
            templateData.selectedElement.style.width = this.value + 'px';
        }
    });

    document.getElementById('elementHeight').addEventListener('input', function() {
        if (templateData.selectedElement) {
            templateData.selectedElement.style.height = this.value + 'px';
        }
    });

    // Text content handler
    document.getElementById('textContent').addEventListener('input', function() {
        if (templateData.selectedElement) {
            templateData.selectedElement.innerHTML = this.value;
        }
    });

    // Background color handler
    document.getElementById('backgroundColor').addEventListener('input', function() {
        if (templateData.selectedElement) {
            templateData.selectedElement.style.backgroundColor = this.value;
            document.getElementById('bgColorPreview').style.backgroundColor = this.value;
        }
    });

    // Border color handler
    document.getElementById('borderColor').addEventListener('input', function() {
        if (templateData.selectedElement) {
            templateData.selectedElement.style.borderColor = this.value;
            document.getElementById('borderColorPreview').style.backgroundColor = this.value;
        }
    });

    // Border width handler
    document.getElementById('borderWidth').addEventListener('input', function() {
        if (templateData.selectedElement) {
            templateData.selectedElement.style.borderWidth = this.value;
        }
    });
}

// Additional element creation functions
function addLineElement() {
    const canvas = document.getElementById('canvasContent');
    const lineElement = document.createElement('div');
    lineElement.className = 'absolute border-t-2 border-gray-400 cursor-move bg-white';
    lineElement.style.left = '50px';
    lineElement.style.top = '250px';
    lineElement.style.width = '200px';
    lineElement.style.height = '2px';
    lineElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(lineElement, 'line');
    };

    makeDraggable(lineElement);
    makeResizable(lineElement);
    canvas.appendChild(lineElement);
    selectElement(lineElement, 'line');
}

function addRectangleElement() {
    const canvas = document.getElementById('canvasContent');
    const rectElement = document.createElement('div');
    rectElement.className = 'absolute border-2 border-gray-400 cursor-move bg-white';
    rectElement.style.left = '50px';
    rectElement.style.top = '300px';
    rectElement.style.width = '150px';
    rectElement.style.height = '100px';
    rectElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(rectElement, 'rectangle');
    };

    makeDraggable(rectElement);
    makeResizable(rectElement);
    canvas.appendChild(rectElement);
    selectElement(rectElement, 'rectangle');
}

function addCircleElement() {
    const canvas = document.getElementById('canvasContent');
    const circleElement = document.createElement('div');
    circleElement.className = 'absolute border-2 border-gray-400 cursor-move rounded-full bg-white';
    circleElement.style.left = '50px';
    circleElement.style.top = '350px';
    circleElement.style.width = '100px';
    circleElement.style.height = '100px';
    circleElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(circleElement, 'circle');
    };

    makeDraggable(circleElement);
    makeResizable(circleElement);
    canvas.appendChild(circleElement);
    selectElement(circleElement, 'circle');
}

function addLogoElement() {
    const canvas = document.getElementById('canvasContent');
    const logoElement = document.createElement('div');
    logoElement.className = 'canvas-element absolute border border-dashed border-gray-400 p-4 cursor-move bg-gray-100 flex items-center justify-center';
    logoElement.style.left = '50px';
    logoElement.style.top = '400px';
    logoElement.style.width = '120px';
    logoElement.style.height = '60px';
    logoElement.innerHTML = '<i class="fa-solid fa-building text-gray-500"></i><span class="ml-2 text-gray-500 text-sm">Logo</span>';
    logoElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(logoElement, 'logo');
    };

    makeDraggable(logoElement);
    makeResizable(logoElement);
    canvas.appendChild(logoElement);
    selectElement(logoElement, 'logo');
}

function addSignatureElement() {
    const canvas = document.getElementById('canvasContent');
    const signatureElement = document.createElement('div');
    signatureElement.className = 'canvas-element absolute border border-dashed border-gray-400 p-2 cursor-move bg-gray-50';
    signatureElement.style.left = '250px';
    signatureElement.style.top = '400px';
    signatureElement.style.width = '200px';
    signatureElement.style.height = '60px';
    signatureElement.innerHTML = '<div class="text-center text-gray-500 text-sm">Signature Line</div>';
    signatureElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(signatureElement, 'signature');
    };

    makeDraggable(signatureElement);
    makeResizable(signatureElement);
    canvas.appendChild(signatureElement);
    selectElement(signatureElement, 'signature');
}

function addDateElement() {
    const canvas = document.getElementById('canvasContent');
    const dateElement = document.createElement('div');
    dateElement.className = 'canvas-element absolute border border-dashed border-primary-400 p-2 cursor-move bg-primary-50';
    dateElement.style.left = '250px';
    dateElement.style.top = '50px';
    dateElement.style.minWidth = '100px';
    dateElement.style.minHeight = '30px';
    dateElement.innerHTML = '{{current_date}}';
    dateElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(dateElement, 'date');
    };

    makeDraggable(dateElement);
    makeResizable(dateElement);
    canvas.appendChild(dateElement);
    selectElement(dateElement, 'date');

    // Add to variables
    if (!templateData.variables['current_date']) {
        templateData.variables['current_date'] = { type: 'date', required: true };
        updateVariablesList();
    }
}

function addNumberElement() {
    const canvas = document.getElementById('canvasContent');
    const numberElement = document.createElement('div');
    numberElement.className = 'canvas-element absolute border border-dashed border-primary-400 p-2 cursor-move bg-primary-50';
    numberElement.style.left = '250px';
    numberElement.style.top = '100px';
    numberElement.style.minWidth = '100px';
    numberElement.style.minHeight = '30px';
    numberElement.innerHTML = '{{number_value}}';
    numberElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(numberElement, 'number');
    };

    makeDraggable(numberElement);
    makeResizable(numberElement);
    canvas.appendChild(numberElement);
    selectElement(numberElement, 'number');

    // Add to variables
    if (!templateData.variables['number_value']) {
        templateData.variables['number_value'] = { type: 'number', required: true };
        updateVariablesList();
    }
}

function addListElement() {
    const canvas = document.getElementById('canvasContent');
    const listElement = document.createElement('div');
    listElement.className = 'canvas-element absolute border border-dashed border-gray-400 p-4 cursor-move bg-white';
    listElement.style.left = '250px';
    listElement.style.top = '150px';
    listElement.style.minWidth = '150px';
    listElement.innerHTML = `
        <ul class="list-disc list-inside">
            <li>{{list_item_1}}</li>
            <li>{{list_item_2}}</li>
            <li>{{list_item_3}}</li>
        </ul>
    `;
    listElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(listElement, 'list');
    };

    makeDraggable(listElement);
    makeResizable(listElement);
    canvas.appendChild(listElement);
    selectElement(listElement, 'list');
}

function addConditionalElement() {
    const canvas = document.getElementById('canvasContent');
    const conditionalElement = document.createElement('div');
    conditionalElement.className = 'canvas-element absolute border border-dashed border-yellow-400 p-2 cursor-move bg-yellow-50';
    conditionalElement.style.left = '250px';
    conditionalElement.style.top = '250px';
    conditionalElement.style.minWidth = '150px';
    conditionalElement.style.minHeight = '40px';
    conditionalElement.innerHTML = '<div class="text-yellow-700 text-sm">{% if condition %}Content{% endif %}</div>';
    conditionalElement.onclick = (e) => {
        e.stopPropagation();
        selectElement(conditionalElement, 'conditional');
    };

    makeDraggable(conditionalElement);
    makeResizable(conditionalElement);
    canvas.appendChild(conditionalElement);
    selectElement(conditionalElement, 'conditional');
}

function showColorPicker() {
    const color = prompt('Enter hex color (e.g., #FF0000):');
    if (color && /^#[0-9A-F]{6}$/i.test(color)) {
        setTextColor(color);
    }
}

function addElement() {
    // Show element selection menu
    alert('Select an element from the left panel to add it to your template');
}

// Page navigation functions
function previousPage() {
    // Previous page implementation
    console.log('Previous page');
}

function nextPage() {
    // Next page implementation
    console.log('Next page');
}
</script>

{% csrf_token %}
{% endblock %}
