{% extends 'base.html' %}
{% load static %}

{% block title %}Template Designer - RadioMention{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Template Designer</h1>
                    <p class="text-sm text-gray-600 mt-1">
                        Create and manage reusable templates for your content
                    </p>
                </div>
                <div class="flex space-x-2">
                    <a href="{% url 'template_designer:template_editor' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-plus mr-1"></i>
                        Create Template
                    </a>
                    <a href="{% url 'template_designer:template_list' %}" class="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                        <i class="fa-solid fa-list mr-1"></i>
                        All Templates
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-file-lines text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">My Templates</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ total_templates|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-globe text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Public Templates</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ total_public|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-layer-group text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Categories</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ categories.count|default:0 }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-chart-line text-orange-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Recent Usage</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ recent_usage.count|default:0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <a href="{% url 'template_designer:template_editor' %}" class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white hover:from-blue-600 hover:to-blue-700 transition-all">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4">
                    <i class="fa-solid fa-plus text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold">Create New Template</h3>
                    <p class="text-blue-100">Start building a new template</p>
                </div>
            </div>
        </a>
        
        <a href="{% url 'template_designer:template_list' %}?type=email" class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white hover:from-green-600 hover:to-green-700 transition-all">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4">
                    <i class="fa-solid fa-envelope text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold">Email Templates</h3>
                    <p class="text-green-100">Browse email templates</p>
                </div>
            </div>
        </a>
        
        <a href="{% url 'template_designer:template_list' %}?type=document" class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white hover:from-purple-600 hover:to-purple-700 transition-all">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4">
                    <i class="fa-solid fa-file-text text-2xl"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold">Document Templates</h3>
                    <p class="text-purple-100">Browse document templates</p>
                </div>
            </div>
        </a>
    </div>

    <!-- Recent Templates -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- My Recent Templates -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">My Recent Templates</h3>
                    <a href="{% url 'template_designer:template_list' %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if user_templates %}
                    <div class="space-y-4">
                        {% for template in user_templates %}
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fa-solid fa-file-lines text-blue-600 text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
                                    <p class="text-sm text-gray-600">{{ template.get_template_type_display }}</p>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <a href="{% url 'template_designer:template_detail' template.id %}" class="text-gray-400 hover:text-gray-600">
                                    <i class="fa-solid fa-eye"></i>
                                </a>
                                <a href="{% url 'template_designer:template_editor_edit' template.id %}" class="text-gray-400 hover:text-gray-600">
                                    <i class="fa-solid fa-edit"></i>
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fa-solid fa-file-lines text-gray-400 text-xl"></i>
                        </div>
                        <p class="text-gray-500">No templates yet</p>
                        <a href="{% url 'template_designer:template_editor' %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                            Create your first template
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Public Templates -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Popular Public Templates</h3>
                    <a href="{% url 'template_designer:template_list' %}?public=true" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                {% if public_templates %}
                    <div class="space-y-4">
                        {% for template in public_templates %}
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fa-solid fa-globe text-green-600 text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
                                    <p class="text-sm text-gray-600">by {{ template.created_by.username }}</p>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <a href="{% url 'template_designer:template_detail' template.id %}" class="text-gray-400 hover:text-gray-600">
                                    <i class="fa-solid fa-eye"></i>
                                </a>
                                <span class="text-xs text-gray-500">{{ template.usage_count }} uses</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <div class="w-12 h-12 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fa-solid fa-globe text-gray-400 text-xl"></i>
                        </div>
                        <p class="text-gray-500">No public templates available</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
