{% extends 'base.html' %}
{% load static %}

{% block title %}{{ template.name }} - Template Designer{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    {% if template.category %}
                    <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-4" style="background-color: {{ template.category.color }}20;">
                        <i class="{{ template.category.icon }}" style="color: {{ template.category.color }};"></i>
                    </div>
                    {% endif %}
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">{{ template.name }}</h1>
                        <div class="flex items-center space-x-4 mt-1">
                            <span class="text-sm text-gray-600">{{ template.get_template_type_display }}</span>
                            {% if template.category %}
                            <span class="text-sm text-gray-600">• {{ template.category.name }}</span>
                            {% endif %}
                            <span class="text-sm text-gray-600">• by {{ template.created_by.username }}</span>
                            {% if template.is_public %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fa-solid fa-globe mr-1"></i>
                                Public
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="flex space-x-2">
                    {% if can_edit %}
                    <a href="{% url 'template_designer:template_editor_edit' template.id %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-edit mr-1"></i>
                        Edit Template
                    </a>
                    {% endif %}
                    <button onclick="useTemplate()" class="bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                        <i class="fa-solid fa-copy mr-1"></i>
                        Use Template
                    </button>
                    <a href="{% url 'template_designer:template_list' %}" class="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                        <i class="fa-solid fa-arrow-left mr-1"></i>
                        Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Template Info -->
        <div class="lg:col-span-1 space-y-6">
            <!-- Description -->
            {% if template.description %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Description</h3>
                </div>
                <div class="p-6">
                    <p class="text-gray-700">{{ template.description }}</p>
                </div>
            </div>
            {% endif %}

            <!-- Template Variables -->
            {% if template.variables %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Template Variables</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        {% for var_name, var_info in template.variables.items %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <code class="text-sm font-mono text-primary-600">{{ var_name }}</code>
                                {% if var_info.required %}
                                <span class="ml-2 text-xs text-red-600">*required</span>
                                {% endif %}
                            </div>
                            <span class="text-xs text-gray-500 uppercase">{{ var_info.type|default:"text" }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Template Stats -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Statistics</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Usage Count</span>
                        <span class="font-medium">{{ template.usage_count }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Created</span>
                        <span class="font-medium">{{ template.created_at|date:"M d, Y" }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Last Updated</span>
                        <span class="font-medium">{{ template.updated_at|date:"M d, Y" }}</span>
                    </div>
                    {% if versions %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Versions</span>
                        <span class="font-medium">{{ versions.count }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Version History -->
            {% if versions %}
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Version History</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-3">
                        {% for version in versions %}
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <div class="font-medium text-sm">v{{ version.version_number }}</div>
                                <div class="text-xs text-gray-600">{{ version.created_at|date:"M d, Y g:i A" }}</div>
                            </div>
                            <div class="text-xs text-gray-500">{{ version.created_by.username }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Template Content -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Template Content</h3>
                        <div class="flex space-x-2">
                            <button onclick="copyContent()" class="bg-gray-100 text-gray-700 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-200">
                                <i class="fa-solid fa-copy mr-1"></i>
                                Copy
                            </button>
                            <button onclick="downloadTemplate()" class="bg-gray-100 text-gray-700 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-200">
                                <i class="fa-solid fa-download mr-1"></i>
                                Download
                            </button>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <pre id="templateContent" class="text-sm text-gray-800 whitespace-pre-wrap font-mono">{{ template.content }}</pre>
                    </div>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Template Preview</h3>
                        <button onclick="generatePreview()" class="bg-primary-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                            <i class="fa-solid fa-eye mr-1"></i>
                            Generate Preview
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div id="previewContent" class="bg-gray-50 rounded-lg p-4 min-h-32">
                        <p class="text-gray-500 text-center">Click "Generate Preview" to see how this template looks with sample data</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyContent() {
    const content = document.getElementById('templateContent').textContent;
    navigator.clipboard.writeText(content).then(() => {
        alert('Template content copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy: ', err);
        alert('Failed to copy content');
    });
}

function downloadTemplate() {
    const content = document.getElementById('templateContent').textContent;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = '{{ template.name|slugify }}.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function generatePreview() {
    const content = `{{ template.content|escapejs }}`;
    const variables = {{ template.variables|safe }};
    
    // Replace variables with sample data
    let previewContent = content;
    Object.keys(variables).forEach(varName => {
        const varType = variables[varName].type || 'text';
        let sampleValue = '';
        
        switch(varType) {
            case 'text':
                sampleValue = `[Sample ${varName}]`;
                break;
            case 'number':
                sampleValue = '123';
                break;
            case 'date':
                sampleValue = new Date().toLocaleDateString();
                break;
            case 'email':
                sampleValue = '<EMAIL>';
                break;
            default:
                sampleValue = `[${varName}]`;
        }
        
        const regex = new RegExp(`\\{\\{\\s*${varName}\\s*\\}\\}`, 'g');
        previewContent = previewContent.replace(regex, `<span class="bg-yellow-200 px-1 rounded">${sampleValue}</span>`);
    });
    
    document.getElementById('previewContent').innerHTML = previewContent.replace(/\n/g, '<br>');
}

function useTemplate() {
    // This would typically redirect to a form or editor where the user can fill in the template
    alert('Template usage functionality would be implemented here. This could redirect to a form where users can fill in the variables and generate the final content.');
}
</script>
{% endblock %}
