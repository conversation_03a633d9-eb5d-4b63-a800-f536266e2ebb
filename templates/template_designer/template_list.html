{% extends 'base.html' %}
{% load static %}

{% block title %}Template List - Template Designer{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Template Library</h1>
                    <p class="text-sm text-gray-600 mt-1">
                        Browse and manage all templates
                    </p>
                </div>
                <div class="flex space-x-2">
                    <a href="{% url 'template_designer:template_editor' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-plus mr-1"></i>
                        Create Template
                    </a>
                    <a href="{% url 'template_designer:dashboard' %}" class="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                        <i class="fa-solid fa-arrow-left mr-1"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="p-6">
            <form method="GET" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-64">
                    <input type="text" name="search" value="{{ search_query|default:'' }}" 
                           placeholder="Search templates..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div>
                    <select name="category" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if current_category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <select name="type" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">All Types</option>
                        {% for type_key, type_label in template_types %}
                        <option value="{{ type_key }}" {% if current_type == type_key %}selected{% endif %}>
                            {{ type_label }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <button type="submit" class="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700">
                    <i class="fa-solid fa-search mr-1"></i>
                    Filter
                </button>
                {% if search_query or current_category or current_type %}
                <a href="{% url 'template_designer:template_list' %}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                    <i class="fa-solid fa-times mr-1"></i>
                    Clear
                </a>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Templates Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for template in templates %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center">
                        {% if template.category %}
                        <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3" style="background-color: {{ template.category.color }}20;">
                            <i class="{{ template.category.icon }} text-sm" style="color: {{ template.category.color }};"></i>
                        </div>
                        {% else %}
                        <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fa-solid fa-file-lines text-gray-600 text-sm"></i>
                        </div>
                        {% endif %}
                        <div>
                            <h3 class="font-medium text-gray-900">{{ template.name }}</h3>
                            <p class="text-sm text-gray-600">{{ template.get_template_type_display }}</p>
                        </div>
                    </div>
                    {% if template.is_public %}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fa-solid fa-globe mr-1"></i>
                        Public
                    </span>
                    {% endif %}
                </div>
                
                {% if template.description %}
                <p class="text-sm text-gray-600 mb-4">{{ template.description|truncatechars:100 }}</p>
                {% endif %}
                
                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>by {{ template.created_by.username }}</span>
                    <span>{{ template.usage_count }} uses</span>
                </div>
                
                <div class="flex space-x-2">
                    <a href="{% url 'template_designer:template_detail' template.id %}" 
                       class="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-200 text-center">
                        <i class="fa-solid fa-eye mr-1"></i>
                        View
                    </a>
                    {% if template.created_by == user %}
                    <a href="{% url 'template_designer:template_editor_edit' template.id %}" 
                       class="flex-1 bg-primary-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-primary-700 text-center">
                        <i class="fa-solid fa-edit mr-1"></i>
                        Edit
                    </a>
                    {% else %}
                    <button onclick="useTemplate({{ template.id }})" 
                            class="flex-1 bg-primary-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-primary-700">
                        <i class="fa-solid fa-copy mr-1"></i>
                        Use
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full">
            <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                    <i class="fa-solid fa-file-lines text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
                <p class="text-gray-600 mb-4">
                    {% if search_query or current_category or current_type %}
                        Try adjusting your filters or search terms.
                    {% else %}
                        Get started by creating your first template.
                    {% endif %}
                </p>
                <a href="{% url 'template_designer:template_editor' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700">
                    <i class="fa-solid fa-plus mr-1"></i>
                    Create Template
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
function useTemplate(templateId) {
    // This would typically copy the template or redirect to editor with template data
    alert('Template usage functionality would be implemented here');
}
</script>
{% endblock %}
