{% extends 'base.html' %}
{% load time_filters %}
{% block title %}
  Dashboard - RadioMention
{% endblock %} {% block content %}
  <!-- Welcome Header -->
  <div class="mb-6">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Welcome back, {{ user.get_full_name|default:user.username }}!</h1>
        <p class="text-gray-600 mt-1">Here's what's happening at {{ current_organization.name }} today</p>
      </div>
      <div class="text-right">
        <div class="text-sm text-gray-500">
          {% current_date_formatted %}
        </div>
        <div class="text-lg font-semibold text-gray-900">
          {% current_time_formatted %}
        </div>
      </div>
    </div>
  </div>

  <!-- System Notifications -->
  <div id="dashboard-notifications" class="mb-6 space-y-3">
    <!-- Real-time notifications will be inserted here -->
  </div>

  <!-- Alerts Section -->
  {% if alerts %}
    <div class="mb-6 space-y-3">
      {% for alert in alerts %}
        <div class="rounded-md p-4 {% if alert.type == 'error' %}
            
            
            
            
            
            
            
            
            
             bg-red-50 border border-red-200









          {% elif alert.type == 'warning' %}
            
            
            
            
            
            
            
            
            
             bg-yellow-50 border border-yellow-200









          {% else %}
            
            
            
            
            
            
            
            
            
             bg-blue-50 border border-blue-200









          {% endif %}">
          <div class="flex">
            <div class="flex-shrink-0">
              {% if alert.type == 'error' %}
                <i class="fa-solid fa-exclamation-circle text-red-400"></i>
              {% elif alert.type == 'warning' %}
                <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
              {% else %}
                <i class="fa-solid fa-info-circle text-blue-400"></i>
              {% endif %}
            </div>
            <div class="ml-3 flex-1">
              <h3 class="text-sm font-medium {% if alert.type == 'error' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                   text-red-800









                {% elif alert.type == 'warning' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                   text-yellow-800









                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                   text-blue-800









                {% endif %}">
                {{ alert.title }}
              </h3>
              <div class="mt-1 text-sm {% if alert.type == 'error' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                   text-red-700









                {% elif alert.type == 'warning' %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                   text-yellow-700









                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  
                  
                   text-blue-700









                {% endif %}">{{ alert.message }}</div>
              {% if alert.action_url %}
                <div class="mt-3">
                  <a href="{{ alert.action_url }}"
                    class="text-sm font-medium {% if alert.type == 'error' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                       text-red-800 hover:text-red-900









                    {% elif alert.type == 'warning' %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                       text-yellow-800 hover:text-yellow-900









                    {% else %}
                      
                      
                      
                      
                      
                      
                      
                      
                      
                       text-blue-800 hover:text-blue-900









                    {% endif %}">
                    {{ alert.action_text }} →
                  </a>
                </div>
              {% endif %}
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
  {% endif %}

  <!-- Key Metrics Overview -->
  <section id="overview-section" class="mb-8">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Key Metrics</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- Total Mentions -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-bullhorn text-blue-600"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-medium text-gray-500">Total Mentions</h3>
              <span class="text-green-500 text-xs font-medium flex items-center">
                <i class="fa-solid fa-arrow-up mr-1"></i>
                {{ stats.mentions_growth }}%
              </span>
            </div>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.total_mentions }}</p>
            <div class="mt-1 text-xs text-gray-500">
              <span>vs. {{ stats.last_month_mentions }} last month</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Pending Approvals -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-clock text-orange-600"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-medium text-gray-500">Pending Approvals</h3>
              {% if stats.pending_mentions > 0 %}
                <span class="text-red-500 text-xs font-medium flex items-center">
                  <i class="fa-solid fa-exclamation-circle mr-1"></i>
                  Action needed
                </span>
              {% endif %}
            </div>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.pending_mentions }}</p>
            <div class="mt-1 text-xs text-gray-500">
              <span>vs. {{ stats.last_week_pending }} last week</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Active Clients -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-building text-green-600"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-medium text-gray-500">Active Clients</h3>
              <span class="text-green-500 text-xs font-medium flex items-center">
                <i class="fa-solid fa-arrow-up mr-1"></i>
                {{ stats.clients_growth }}%
              </span>
            </div>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.active_clients }}</p>
            <div class="mt-1 text-xs text-gray-500">
              <span>vs. {{ stats.last_month_clients }} last month</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Today's Schedule -->
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <i class="fa-solid fa-calendar-day text-purple-600"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-medium text-gray-500">Scheduled Today</h3>
              <div class="text-xs text-gray-500">{{ stats.completion_rate }}% complete</div>
            </div>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.scheduled_today }}</p>
            <div class="mt-1 text-xs text-gray-500">
              <span>across {{ stats.shows_today }} shows</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Quick Actions & Live Status -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Live Status -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Live Status</h3>
        <div class="flex items-center">
          <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
          <span class="text-sm text-gray-600">{% current_time_formatted %}</span>
        </div>
      </div>
      <div class="space-y-3">
        {% if todays_schedule %}
          {% for reading in todays_schedule|slice:':3' %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <div class="font-medium text-sm text-gray-900">{{ reading.show.name }}</div>
                <div class="text-xs text-gray-500">{{ reading.scheduled_time|format_time_user:user }}</div>
              </div>
              <div>
                {% if reading.actual_read_time %}
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <i class="fa-solid fa-check mr-1"></i>
                    Complete
                  </span>
                {% else %}
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <i class="fa-solid fa-clock mr-1"></i>
                    Pending
                  </span>
                {% endif %}
              </div>
            </div>
          {% endfor %}
        {% else %}
          <div class="text-center py-4 text-gray-500">
            <i class="fa-solid fa-calendar-xmark text-2xl mb-2"></i>
            <p>No mentions scheduled for today</p>
          </div>
        {% endif %}
      </div>
      <div class="mt-4">
        <a href="{% url 'mentions:calendar_interface' %}" class="text-sm font-medium text-primary-600 hover:text-primary-700">View full schedule →</a>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
      <div class="space-y-3">
        {% if recent_activity %}
          {% for reading in recent_activity|slice:':4' %}
            <div class="flex items-start space-x-3">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <i class="fa-solid fa-check text-green-600 text-sm"></i>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900">{{ reading.mention.client.name }}</p>
                <p class="text-xs text-gray-500">{{ reading.show.name }} • {{ reading.actual_read_time|timesince }}
                  ago</p>
              </div>
            </div>
          {% endfor %}
        {% else %}
          <div class="text-center py-4 text-gray-500">
            <i class="fa-solid fa-history text-2xl mb-2"></i>
            <p>No recent activity</p>
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Upcoming Schedule -->
    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Coming Up</h3>
      <div class="space-y-3">
        {% if upcoming_schedule %}
          {% for reading in upcoming_schedule|slice:':4' %}
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-sm text-gray-900">{{ reading.mention.client.name }}</div>
                <div class="text-xs text-gray-500">{{ reading.scheduled_date|format_date_user:user }} at {{ reading.scheduled_time|format_time_user:user }}</div>
              </div>
              <div class="text-xs text-gray-400">{{ reading.show.name }}</div>
            </div>
          {% endfor %}
        {% else %}
          <div class="text-center py-4 text-gray-500">
            <i class="fa-solid fa-calendar-plus text-2xl mb-2"></i>
            <p>No upcoming mentions</p>
          </div>
        {% endif %}
      </div>
      <div class="mt-4">
        <a href="{% url 'mentions:mention_list' %}" class="text-sm font-medium text-primary-600 hover:text-primary-700">View all mentions →</a>
      </div>
    </div>
  </div>

  <!-- Charts section -->
  <section id="charts-section" class="mb-8">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Analytics</h2>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Mentions by Show</h3>
          <div>
            <select class="text-sm border-gray-300 rounded-md text-gray-500" id="mentionsChartPeriod">
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
            </select>
          </div>
        </div>
        <div class="h-[250px]">
          <canvas id="mentionsChart"></canvas>
        </div>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium text-gray-800">Mentions Status</h3>
          <div>
            <select class="text-sm border-gray-300 rounded-md text-gray-500" id="statusChartPeriod">
              <option value="7">Last 7 days</option>
              <option value="30">Last 30 days</option>
              <option value="90">Last 90 days</option>
            </select>
          </div>
        </div>
        <div class="h-[250px]">
          <canvas id="statusChart"></canvas>
        </div>
      </div>
    </div>
  </section>

  <!-- Today's Detailed Schedule -->
  <section id="schedule-section" class="mb-8">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-lg font-semibold text-gray-900">Today's Detailed Schedule</h2>
      <a href="{% url 'mentions:calendar_interface' %}" class="text-primary-600 text-sm font-medium hover:text-primary-700">View Calendar →</a>
    </div>
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {% if todays_schedule %}
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Presenter</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for reading in todays_schedule %}
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ reading.scheduled_time|format_time_user:user }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reading.show.name }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-user text-gray-500 text-sm"></i>
                      </div>
                      <span class="ml-3 text-sm text-gray-900">{{ reading.presenter.display_name|default:'TBD' }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reading.mention.client.name }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if reading.actual_read_time %}
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <i class="fa-solid fa-check mr-1"></i>
                        Completed
                      </span>
                    {% else %}
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <i class="fa-solid fa-clock mr-1"></i>
                        Pending
                      </span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <a href="{% url 'mentions:mention_detail' reading.mention.pk %}" class="text-primary-600 hover:text-primary-900">
                      {% if reading.actual_read_time %}
                        View Details
                      {% else %}
                        Manage
                      {% endif %}
                    </a>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      {% else %}
        <div class="text-center py-12">
          <i class="fa-solid fa-calendar-xmark text-4xl text-gray-300 mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No mentions scheduled for today</h3>
          <p class="text-gray-500 mb-4">Looks like it's a quiet day on the airwaves!</p>
          <a href="{% url 'mentions:mention_create' %}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
            <i class="fa-solid fa-plus mr-2"></i>
            Schedule a Mention
          </a>
        </div>
      {% endif %}
    </div>
  </section>

  <!-- Pending Approvals -->
  {% if pending_mentions %}
    <section id="approvals-section" class="mb-8">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-900">Pending Approvals</h2>
        <a href="{% url 'mentions:approval_workflow' %}" class="text-primary-600 text-sm font-medium hover:text-primary-700">View All →</a>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for mention in pending_mentions %}
          <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200" data-mention-id="{{ mention.pk }}">
            <div class="flex justify-between items-start mb-4">
              <div class="flex-1">
                <h4 class="font-medium text-gray-900 mb-1">{{ mention.title|default:'Untitled Mention' }}</h4>
                <p class="text-sm text-gray-500">{{ mention.client.name }} • {{ mention.created_at|format_date_user:user }}</p>
              </div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ml-2">
                <i class="fa-solid fa-clock mr-1"></i>
                Pending
              </span>
            </div>
            <p class="text-sm text-gray-600 mb-4 line-clamp-3">{{ mention.content|truncatewords:15 }}</p>
            <div class="flex items-center text-xs text-gray-500 mb-4">
              <i class="fa-regular fa-clock mr-1"></i>
              <span>Submitted {{ mention.created_at|timesince }} ago</span>
            </div>
            <div class="flex space-x-2">
              <button onclick="approveMention({{ mention.pk }})" class="flex-1 px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors duration-200">
                <i class="fa-solid fa-check mr-1"></i>
                Approve
              </button>
              <a href="{% url 'mentions:mention_detail' mention.pk %}" class="flex-1 px-3 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 transition-colors duration-200 text-center">
                <i class="fa-solid fa-eye mr-1"></i>
                Review
              </a>
            </div>
          </div>
        {% endfor %}
      </div>
    </section>
  {% endif %}

  <!-- Notification Widget -->
  <div class="mb-8">
    {% include 'includes/notification_widget.html' %}
  </div>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/chart.min.js' %}"></script>
  <script>
  // Mentions by Show Chart
  const mentionsCtx = document.getElementById('mentionsChart').getContext('2d');
  const mentionsChart = new Chart(mentionsCtx, {
      type: 'bar',
      data: {
          labels: {{ chart_data.show_labels|safe }},
          datasets: [{
              label: 'Number of Mentions',
              data: {{ chart_data.show_data|safe }},
              backgroundColor: 'rgba(14, 165, 233, 0.7)',
              borderColor: 'rgba(14, 165, 233, 1)',
              borderWidth: 1
          }]
      },
      options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
              y: {
                  beginAtZero: true
              }
          }
      }
  });

  // Mentions Status Chart
  const statusCtx = document.getElementById('statusChart').getContext('2d');
  const statusChart = new Chart(statusCtx, {
      type: 'doughnut',
      data: {
          labels: {{ chart_data.status_labels|safe }},
          datasets: [{
              data: {{ chart_data.status_data|safe }},
              backgroundColor: [
                  'rgba(34, 197, 94, 0.7)',
                  'rgba(234, 179, 8, 0.7)',
                  'rgba(59, 130, 246, 0.7)',
                  'rgba(239, 68, 68, 0.7)'
              ],
              borderColor: [
                  'rgba(34, 197, 94, 1)',
                  'rgba(234, 179, 8, 1)',
                  'rgba(59, 130, 246, 1)',
                  'rgba(239, 68, 68, 1)'
              ],
              borderWidth: 1
          }]
      },
      options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
              legend: {
                  position: 'right',
              }
          }
      }
  });

  // Approval functions
  function approveMention(mentionId) {
      if (confirm('Are you sure you want to approve this mention?')) {
          fetch(`/mentions/${mentionId}/approve/`, {
              method: 'POST',
              headers: {
                  'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                  'Content-Type': 'application/json',
              },
          })
          .then(response => response.json())
          .then(data => {
              if (data.success) {
                  // Show success notification
                  if (window.notificationSystem) {
                      window.notificationSystem.show('Mention approved successfully!', 'success');
                  }
                  // Remove the mention card with animation
                  const mentionCard = document.querySelector(`[data-mention-id="${mentionId}"]`);
                  if (mentionCard) {
                      mentionCard.style.transition = 'all 0.3s ease';
                      mentionCard.style.opacity = '0';
                      mentionCard.style.transform = 'scale(0.95)';
                      setTimeout(() => {
                          mentionCard.remove();
                      }, 300);
                  } else {
                      location.reload();
                  }
              } else {
                  if (window.notificationSystem) {
                      window.notificationSystem.show('Error approving mention: ' + data.error, 'error');
                  } else {
                      alert('Error approving mention: ' + data.error);
                  }
              }
          })
          .catch(error => {
              console.error('Error:', error);
              alert('Error approving mention');
          });
      }
  }

  function rejectMention(mentionId) {
      const reason = prompt('Please provide a reason for rejection:');
      if (reason) {
          fetch(`/mentions/${mentionId}/reject/`, {
              method: 'POST',
              headers: {
                  'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                  'Content-Type': 'application/json',
              },
              body: JSON.stringify({reason: reason})
          })
          .then(response => response.json())
          .then(data => {
              if (data.success) {
                  location.reload();
              } else {
                  alert('Error rejecting mention: ' + data.error);
              }
          })
          .catch(error => {
              console.error('Error:', error);
              alert('Error rejecting mention');
          });
      }
  }
</script>
{% endblock %}
