{% extends 'base.html' %}
{% load static %}

{% block title %}Access Forbidden - RadioMention{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
        <!-- Error Icon -->
        <div class="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-yellow-100">
            <svg class="h-16 w-16 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
        </div>

        <!-- Error Content -->
        <div>
            <h1 class="text-6xl font-bold text-gray-900 mb-4">403</h1>
            <h2 class="text-2xl font-semibold text-gray-700 mb-4">Access Forbidden</h2>
            <p class="text-gray-600 mb-8">
                You don't have permission to access this resource. This could be due to insufficient privileges or organization restrictions.
            </p>
        </div>

        <!-- User Info -->
        {% if user.is_authenticated %}
        <div class="bg-blue-50 rounded-lg p-4 mb-6">
            <div class="text-sm text-blue-700">
                <p><strong>Current User:</strong> {{ user.username }}</p>
                {% if user.userprofile.organization %}
                <p><strong>Organization:</strong> {{ user.userprofile.organization.name }}</p>
                {% endif %}
                <p><strong>Role:</strong> {{ user.userprofile.get_role_display|default:"Not assigned" }}</p>
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="space-y-4">
            <button onclick="history.back()" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Go Back
            </button>
            
            <a href="{% url 'core:dashboard' %}" class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition duration-150 ease-in-out">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                Go to Dashboard
            </a>

            {% if not user.is_authenticated %}
            <a href="{% url 'account_login' %}" class="w-full flex justify-center py-3 px-4 border border-green-300 rounded-md shadow-sm text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                </svg>
                Login to Access
            </a>
            {% endif %}
        </div>

        <!-- Permission Information -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Need Access?</h3>
            <div class="text-sm text-gray-600 space-y-2">
                <p>If you believe you should have access to this resource:</p>
                <ul class="list-disc list-inside space-y-1 text-left">
                    <li>Contact your organization administrator</li>
                    <li>Verify you're logged into the correct account</li>
                    <li>Check if your role has the required permissions</li>
                    <li>Ensure you're in the correct organization</li>
                </ul>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="mt-6 p-4 bg-gray-100 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">Contact Support</h4>
            <div class="text-sm text-gray-600">
                <p>For permission requests or access issues:</p>
                <p class="mt-1">
                    <strong>Email:</strong> 
                    <a href="mailto:<EMAIL>" class="text-primary-600 hover:text-primary-500">
                        <EMAIL>
                    </a>
                </p>
                {% if user.userprofile.organization %}
                <p class="mt-1">
                    <strong>Organization:</strong> {{ user.userprofile.organization.name }}
                </p>
                {% endif %}
            </div>
        </div>

        <!-- Available Actions -->
        {% if user.is_authenticated %}
        <div class="mt-6">
            <h4 class="font-medium text-gray-900 mb-3">What you can do:</h4>
            <div class="grid grid-cols-2 gap-3 text-sm">
                <a href="{% url 'core:dashboard' %}" class="p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150">
                    <div class="font-medium text-gray-900">Dashboard</div>
                    <div class="text-gray-500">View your dashboard</div>
                </a>
                <a href="{% url 'authentication:profile' %}" class="p-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150">
                    <div class="font-medium text-gray-900">Profile</div>
                    <div class="text-gray-500">Manage your profile</div>
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
