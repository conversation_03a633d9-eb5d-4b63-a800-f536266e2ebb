{% extends 'base.html' %}
{% load static %}

{% block title %}Organizations{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">Organizations</h1>
                    <p class="text-gray-600 mt-1">Manage your radio station organizations</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{% url 'organizations:create' %}" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
                        <i class="fa-solid fa-plus mr-2"></i>
                        Create Organization
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Organizations Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for membership in memberships %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        {% if membership.organization.logo %}
                        <img src="{{ membership.organization.logo.url }}" alt="{{ membership.organization.name }}" class="w-12 h-12 rounded-lg object-cover mb-4">
                        {% else %}
                        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                            <i class="fa-solid fa-radio text-primary-600 text-xl"></i>
                        </div>
                        {% endif %}
                        
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <a href="{% url 'organizations:detail' slug=membership.organization.slug %}" class="hover:text-primary-600">
                                {{ membership.organization.name }}
                            </a>
                        </h3>
                        
                        {% if membership.organization.description %}
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ membership.organization.description }}</p>
                        {% endif %}
                        
                        <div class="flex items-center justify-between">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if membership.role == 'owner' %}bg-purple-100 text-purple-800
                                {% elif membership.role == 'admin' %}bg-blue-100 text-blue-800
                                {% elif membership.role == 'manager' %}bg-green-100 text-green-800
                                {% elif membership.role == 'editor' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ membership.get_role_display }}
                            </span>
                            
                            {% if membership.is_default %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                <i class="fa-solid fa-star mr-1"></i>
                                Default
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="ml-4">
                        <div class="relative">
                            <button class="text-gray-400 hover:text-gray-600" onclick="toggleOrgMenu({{ membership.organization.id }})">
                                <i class="fa-solid fa-ellipsis-vertical"></i>
                            </button>
                            <div id="orgMenu{{ membership.organization.id }}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                                <a href="{% url 'organizations:detail' slug=membership.organization.slug %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fa-solid fa-eye mr-2"></i>View Details
                                </a>
                                <a href="{% url 'organizations:switch' slug=membership.organization.slug %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fa-solid fa-exchange-alt mr-2"></i>Switch To
                                </a>
                                {% if membership.has_permission.manage_settings %}
                                <a href="{% url 'organizations:edit' slug=membership.organization.slug %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fa-solid fa-edit mr-2"></i>Edit
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>{{ membership.organization.total_users }} member{{ membership.organization.total_users|pluralize }}</span>
                        <span>{{ membership.organization.plan_type|title }} Plan</span>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full">
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fa-solid fa-building text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Organizations</h3>
                <p class="text-gray-600 mb-6">You're not a member of any organizations yet.</p>
                <a href="{% url 'organizations:create' %}" class="inline-flex items-center px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700">
                    <i class="fa-solid fa-plus mr-2"></i>
                    Create Your First Organization
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
function toggleOrgMenu(orgId) {
    const menu = document.getElementById(`orgMenu${orgId}`);
    // Close all other menus
    document.querySelectorAll('[id^="orgMenu"]').forEach(m => {
        if (m.id !== `orgMenu${orgId}`) {
            m.classList.add('hidden');
        }
    });
    menu.classList.toggle('hidden');
}

// Close menus when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[onclick^="toggleOrgMenu"]')) {
        document.querySelectorAll('[id^="orgMenu"]').forEach(m => {
            m.classList.add('hidden');
        });
    }
});
</script>
{% endblock %}
