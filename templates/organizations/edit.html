{% extends 'base.html' %}
{% load static %}

{% block title %}
  Edit {{ organization.name }}
{% endblock %}

{% block content %}
  <div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-xl font-semibold text-gray-900">Edit Organization</h1>
            <p class="text-gray-600 mt-1">Update {{ organization.name }} information</p>
          </div>
          <a href="{% url 'organizations:detail' slug=organization.slug %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">
            <i class="fa-solid fa-arrow-left mr-2"></i>
            Back to Organization
          </a>
        </div>
      </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <form method="post" enctype="multipart/form-data" class="p-6 space-y-6">
        {% csrf_token %}

        <!-- Display form errors -->
        {% if form.non_field_errors %}
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fa-solid fa-exclamation-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                <div class="mt-2 text-sm text-red-700">{{ form.non_field_errors }}</div>
              </div>
            </div>
          </div>
        {% endif %}

        <!-- Basic Information -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Organization Name <span class="text-red-500">*</span></label>
              {{ form.name }}
              {% if form.name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
              {% endif %}
            </div>

            <div>
              <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
              {{ form.email }}
              {% if form.email.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
              {% endif %}
            </div>
          </div>

          <div class="mt-4">
            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
            {{ form.description }}
            {% if form.description.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
            {% endif %}
          </div>
        </div>

        <!-- Contact Information -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="{{ form.website.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Website</label>
              {{ form.website }}
              {% if form.website.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.website.errors.0 }}</p>
              {% endif %}
            </div>

            <div>
              <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
              {{ form.phone }}
              {% if form.phone.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.phone.errors.0 }}</p>
              {% endif %}
            </div>
          </div>

          <div class="mt-4">
            <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
            {{ form.address }}
            {% if form.address.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.address.errors.0 }}</p>
            {% endif %}
          </div>
        </div>

        <!-- Logo Upload -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Logo</h3>
          <div>
            <label for="{{ form.logo.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Organization Logo</label>
            {% if organization.logo %}
              <div class="mb-3">
                <img src="{{ organization.logo.url }}" alt="Current logo" class="h-16 w-16 object-cover rounded-lg border border-gray-200" />
                <p class="text-sm text-gray-500 mt-1">Current logo</p>
              </div>
            {% endif %}
            {{ form.logo }}
            {% if form.logo.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.logo.errors.0 }}</p>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">Upload a new logo to replace the current one.</p>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="border-t border-gray-200 pt-6">
          <div class="flex items-center justify-end space-x-3">
            <a href="{% url 'organizations:detail' slug=organization.slug %}" class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50">Cancel</a>
            <button type="submit" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500">
              <i class="fa-solid fa-save mr-2"></i>
              Update Organization
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
{% endblock %}
