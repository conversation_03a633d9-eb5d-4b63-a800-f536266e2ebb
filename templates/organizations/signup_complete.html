{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Welcome to RadioMention - Setup Complete</title>

    <!-- Tailwind CSS -->
    <!-- Offline Font Awesome -->
    <link rel="stylesheet" href="{% static 'css/fontawesome.min.css' %}" />
    <script>
      window.FontAwesomeConfig = { autoReplaceSvg: 'nest' }
    </script>
    <script src="{% static 'js/fontawesome.min.js' %}"></script>

    <!-- Offline Inter Font -->
    <link rel="stylesheet" href="{% static 'css/inter-font.css' %}" />

    <!-- Tailwind CSS - Local Build -->
    <link rel="stylesheet" href="{% static 'css/tailwind.css' %}?v=3" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/compact.css' %}" />
    <link rel="stylesheet" href="{% static 'css/print.css' %}" media="print" />

    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <style>
      body {
        font-family: 'Inter', sans-serif;
      }
      .celebration-animation {
        animation: bounce 2s infinite;
      }
      @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-10px);
        }
        60% {
          transform: translateY(-5px);
        }
      }
    </style>

    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                50: '#f0f9ff',
                100: '#e0f2fe',
                200: '#bae6fd',
                300: '#7dd3fc',
                400: '#38bdf8',
                500: '#0ea5e9',
                600: '#0284c7',
                700: '#0369a1',
                800: '#075985',
                900: '#0c4a6e'
              }
            }
          }
        }
      }
    </script>
  </head>
  <body class="bg-gradient-to-br from-primary-50 to-blue-100 min-h-screen">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-4xl mx-auto px-4 py-4">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white shadow-sm">
            <i class="fa-solid fa-radio text-lg"></i>
          </div>
          <h1 class="ml-3 text-xl font-bold text-gray-900">RadioMention</h1>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-4 py-12">
      <div class="text-center">
        <!-- Success Icon -->
        <div class="mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-8 celebration-animation">
          <i class="fa-solid fa-check text-4xl text-green-600"></i>
        </div>

        <!-- Welcome Message -->
        <h1 class="text-4xl font-bold text-gray-900 mb-4">Welcome to RadioMention!</h1>
        <p class="text-xl text-gray-600 mb-8">
          Your radio station <strong>{{ organization.name }}</strong> has been successfully set up.
        </p>

        <!-- Organization Info Card -->
        <div class="bg-white rounded-lg shadow-lg border max-w-2xl mx-auto mb-8">
          <div class="px-6 py-6">
            <div class="flex items-center justify-center mb-4">
              {% if organization.logo %}
                <img src="{{ organization.logo.url }}" alt="{{ organization.name }}" class="w-16 h-16 rounded-lg object-cover shadow-sm" />
              {% else %}
                <div class="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center">
                  <i class="fa-solid fa-radio text-primary-600 text-2xl"></i>
                </div>
              {% endif %}
            </div>

            <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ organization.name }}</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 mb-6">
              {% if organization.call_sign %}
                <div class="flex items-center justify-center">
                  <i class="fa-solid fa-broadcast-tower mr-2 text-gray-400"></i>
                  <span>{{ organization.call_sign }}</span>
                </div>
              {% endif %}
              {% if organization.frequency %}
                <div class="flex items-center justify-center">
                  <i class="fa-solid fa-signal mr-2 text-gray-400"></i>
                  <span>{{ organization.frequency }}</span>
                </div>
              {% endif %}
              <div class="flex items-center justify-center">
                <i class="fa-solid fa-user-crown mr-2 text-yellow-500"></i>
                <span>{{ membership.get_role_display }} (Organization Superuser)</span>
              </div>
              <div class="flex items-center justify-center">
                <i class="fa-solid fa-crown mr-2 text-yellow-500"></i>
                <span>{{ organization.get_plan_type_display }} Plan</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-white rounded-lg shadow-lg border max-w-3xl mx-auto mb-8">
          <div class="px-6 py-6">
            <h3 class="text-xl font-bold text-gray-900 mb-6">What's Next?</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- Step 1 -->
              <div class="text-center">
                <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <i class="fa-solid fa-chart-line text-primary-600 text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-900 mb-2">Explore Dashboard</h4>
                <p class="text-sm text-gray-600 mb-3">Get familiar with your new dashboard and see how everything works.</p>
                <a href="{% url 'core:dashboard' %}" class="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-700">
                  Go to Dashboard
                  <i class="fa-solid fa-arrow-right ml-1"></i>
                </a>
              </div>

              <!-- Step 2 -->
              <div class="text-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <i class="fa-solid fa-users text-green-600 text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-900 mb-2">Invite Team Members</h4>
                <p class="text-sm text-gray-600 mb-3">Add your presenters, producers, and other team members.</p>
                <a href="{% url 'organizations:invite_member' slug=organization.slug %}" class="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-700">
                  Invite Members
                  <i class="fa-solid fa-arrow-right ml-1"></i>
                </a>
              </div>

              <!-- Step 3 -->
              <div class="text-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <i class="fa-solid fa-bullhorn text-purple-600 text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-900 mb-2">Create First Mention</h4>
                <p class="text-sm text-gray-600 mb-3">Start by creating your first radio mention to see the system in action.</p>
                <a href="{% url 'mentions:mention_create' %}" class="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-700">
                  Create Mention
                  <i class="fa-solid fa-arrow-right ml-1"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Links -->
        <div class="bg-white rounded-lg shadow-lg border max-w-2xl mx-auto">
          <div class="px-6 py-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">Quick Links</h3>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              <a href="{% url 'core:client_create' %}" class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fa-solid fa-building text-gray-400 text-lg mb-2"></i>
                <span class="text-sm font-medium text-gray-700">Add Clients</span>
              </a>

              <a href="{% url 'shows:show_create' %}" class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fa-solid fa-broadcast-tower text-gray-400 text-lg mb-2"></i>
                <span class="text-sm font-medium text-gray-700">Create Shows</span>
              </a>

              <a href="{% url 'core:presenter_list' %}" class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fa-solid fa-microphone text-gray-400 text-lg mb-2"></i>
                <span class="text-sm font-medium text-gray-700">View Presenters</span>
              </a>

              <a href="{% url 'settings:settings_dashboard' %}" class="flex flex-col items-center p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <i class="fa-solid fa-cog text-gray-400 text-lg mb-2"></i>
                <span class="text-sm font-medium text-gray-700">Settings</span>
              </a>
            </div>
          </div>
        </div>

        <!-- Get Started Button -->
        <div class="mt-8">
          <a href="{% url 'core:dashboard' %}" class="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors shadow-lg">
            <i class="fa-solid fa-rocket mr-2"></i>
            Get Started
          </a>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="bg-white border-t mt-12">
      <div class="max-w-4xl mx-auto px-4 py-6">
        <div class="text-center text-sm text-gray-500">
          <p>
            Need help getting started? <a href="#" class="text-primary-600 hover:text-primary-700">Check out our documentation</a> or <a href="#" class="text-primary-600 hover:text-primary-700">contact support</a>
          </p>
        </div>
      </div>
    </div>

    <script>
      // Add some celebration effects
      document.addEventListener('DOMContentLoaded', function () {
        // You could add confetti or other celebration effects here
        console.log('Welcome to RadioMention! 🎉')
      })
    </script>
  </body>
</html>
