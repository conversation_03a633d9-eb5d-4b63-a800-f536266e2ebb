<!-- Step 2: Organization Basic Information -->
<div class="space-y-6">
    <!-- Station Identity -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="md:col-span-2">
            <label for="{{ form.organization_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Radio Station Name <span class="text-red-500">*</span>
            </label>
            {{ form.organization_name }}
            {% if form.organization_name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.organization_name.errors.0 }}</p>
            {% endif %}
            <p class="mt-1 text-xs text-gray-500">The official name of your radio station</p>
        </div>

        <div>
            <label for="{{ form.call_sign.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Call Sign
            </label>
            {{ form.call_sign }}
            {% if form.call_sign.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.call_sign.errors.0 }}</p>
            {% endif %}
            {% if form.call_sign.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ form.call_sign.help_text }}</p>
            {% endif %}
        </div>

        <div>
            <label for="{{ form.frequency.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Frequency
            </label>
            {{ form.frequency }}
            {% if form.frequency.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.frequency.errors.0 }}</p>
            {% endif %}
            {% if form.frequency.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ form.frequency.help_text }}</p>
            {% endif %}
        </div>
    </div>

    <!-- Station Type -->
    <div>
        <label for="{{ form.station_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Station Type
        </label>
        {{ form.station_type }}
        {% if form.station_type.errors %}
            <p class="mt-1 text-sm text-red-600">{{ form.station_type.errors.0 }}</p>
        {% endif %}
        <p class="mt-1 text-xs text-gray-500">Select the type that best describes your radio station</p>
    </div>

    <!-- Description -->
    <div>
        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
            Station Description
        </label>
        {{ form.description }}
        {% if form.description.errors %}
            <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
        {% endif %}
        <p class="mt-1 text-xs text-gray-500">Tell us about your station's mission, target audience, and programming style</p>
    </div>
</div>

<!-- Information Box -->
<div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fa-solid fa-lightbulb text-green-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">Station Identity</h3>
            <div class="mt-2 text-sm text-green-700">
                <p>This information helps us customize the system for your specific type of radio station. You can always update these details later.</p>
            </div>
        </div>
    </div>
</div>

<!-- Examples Box -->
<div class="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
    <h4 class="text-sm font-medium text-gray-900 mb-2">Examples:</h4>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
        <div>
            <strong>Commercial Station:</strong><br>
            Name: "WXYZ 101.5 FM"<br>
            Call Sign: "WXYZ"<br>
            Frequency: "101.5 FM"
        </div>
        <div>
            <strong>Community Station:</strong><br>
            Name: "Community Voice Radio"<br>
            Call Sign: "KCVR"<br>
            Frequency: "89.3 FM"
        </div>
    </div>
</div>
