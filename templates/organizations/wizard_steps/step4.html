<!-- Step 4: Station Details & Configuration -->
<div class="space-y-8">
  <!-- Station Logo -->
  <div>
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Station Branding</h3>
    <div>
      <label for="{{ form.logo.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Station Logo</label>

      <!-- Logo Upload Area -->
      <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-primary-400 transition-colors">
        <div class="space-y-1 text-center">
          <div id="logo-preview" class="hidden mb-4">
            <img id="logo-preview-img" src="" alt="Logo preview" class="mx-auto h-32 w-auto rounded-lg shadow-sm" />
          </div>
          <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <div class="flex text-sm text-gray-600">
            <label for="{{ form.logo.id_for_label }}" class="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
              <span>Upload a logo</span>
              {{ form.logo }}
            </label>
            <p class="pl-1">or drag and drop</p>
          </div>
          <p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
        </div>
      </div>

      {% if form.logo.errors %}
        <p class="mt-1 text-sm text-red-600">{{ form.logo.errors.0 }}</p>
      {% endif %}
      {% if form.logo.help_text %}
        <p class="mt-1 text-xs text-gray-500">{{ form.logo.help_text }}</p>
      {% endif %}
    </div>
  </div>

  <!-- Plan Selection -->
  <div>
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Choose Your Plan</h3>
    <div class="space-y-3 plan-radio">
      {% for choice in form.plan_type %}
        <div>
          {{ choice.tag }}
          <label for="{{ choice.id_for_label }}" class="cursor-pointer">
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-1">
                <div class="w-4 h-4 border-2 border-gray-300 rounded-full flex items-center justify-center">
                  <div class="w-2 h-2 bg-primary-600 rounded-full hidden"></div>
                </div>
              </div>
              <div class="ml-3">
                <div class="font-medium text-gray-900">{{ choice.choice_label }}</div>
              </div>
            </div>
          </label>
        </div>
      {% endfor %}
    </div>
    {% if form.plan_type.errors %}
      <p class="mt-1 text-sm text-red-600">{{ form.plan_type.errors.0 }}</p>
    {% endif %}
  </div>

  <!-- Usage Estimates -->
  <div>
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Usage Estimates</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label for="{{ form.estimated_users.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Estimated Users</label>
        {{ form.estimated_users }}
        {% if form.estimated_users.errors %}
          <p class="mt-1 text-sm text-red-600">{{ form.estimated_users.errors.0 }}</p>
        {% endif %}
        {% if form.estimated_users.help_text %}
          <p class="mt-1 text-xs text-gray-500">{{ form.estimated_users.help_text }}</p>
        {% endif %}
      </div>

      <div>
        <label for="{{ form.estimated_mentions_per_month.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Mentions Per Month</label>
        {{ form.estimated_mentions_per_month }}
        {% if form.estimated_mentions_per_month.errors %}
          <p class="mt-1 text-sm text-red-600">{{ form.estimated_mentions_per_month.errors.0 }}</p>
        {% endif %}
        {% if form.estimated_mentions_per_month.help_text %}
          <p class="mt-1 text-xs text-gray-500">{{ form.estimated_mentions_per_month.help_text }}</p>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Station Features -->
  <div>
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Station Features</h3>
    <div class="space-y-4">
      <div class="flex items-start">
        {{ form.has_multiple_shows }}
        <div class="ml-3">
          <label for="{{ form.has_multiple_shows.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.has_multiple_shows.label }}</label>
          {% if form.has_multiple_shows.help_text %}
            <p class="text-xs text-gray-500">{{ form.has_multiple_shows.help_text }}</p>
          {% endif %}
        </div>
      </div>

      <div class="flex items-start">
        {{ form.has_live_programming }}
        <div class="ml-3">
          <label for="{{ form.has_live_programming.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.has_live_programming.label }}</label>
          {% if form.has_live_programming.help_text %}
            <p class="text-xs text-gray-500">{{ form.has_live_programming.help_text }}</p>
          {% endif %}
        </div>
      </div>

      <div class="flex items-start">
        {{ form.has_automated_programming }}
        <div class="ml-3">
          <label for="{{ form.has_automated_programming.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.has_automated_programming.label }}</label>
          {% if form.has_automated_programming.help_text %}
            <p class="text-xs text-gray-500">{{ form.has_automated_programming.help_text }}</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Notification Preferences -->
  <div>
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h3>
    <div class="space-y-4">
      <div class="flex items-start">
        {{ form.enable_email_notifications }}
        <div class="ml-3">
          <label for="{{ form.enable_email_notifications.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.enable_email_notifications.label }}</label>
          {% if form.enable_email_notifications.help_text %}
            <p class="text-xs text-gray-500">{{ form.enable_email_notifications.help_text }}</p>
          {% endif %}
        </div>
      </div>

      <div class="flex items-start">
        {{ form.enable_conflict_alerts }}
        <div class="ml-3">
          <label for="{{ form.enable_conflict_alerts.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.enable_conflict_alerts.label }}</label>
          {% if form.enable_conflict_alerts.help_text %}
            <p class="text-xs text-gray-500">{{ form.enable_conflict_alerts.help_text }}</p>
          {% endif %}
        </div>
      </div>

      <div class="flex items-start">
        {{ form.enable_deadline_reminders }}
        <div class="ml-3">
          <label for="{{ form.enable_deadline_reminders.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.enable_deadline_reminders.label }}</label>
          {% if form.enable_deadline_reminders.help_text %}
            <p class="text-xs text-gray-500">{{ form.enable_deadline_reminders.help_text }}</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Information Box -->
<div class="mt-6 bg-purple-50 border border-purple-200 rounded-lg p-4">
  <div class="flex">
    <div class="flex-shrink-0">
      <i class="fa-solid fa-cog text-purple-400"></i>
    </div>
    <div class="ml-3">
      <h3 class="text-sm font-medium text-purple-800">Customization</h3>
      <div class="mt-2 text-sm text-purple-700">
        <p>These settings help us configure the system to match your station's workflow. You can modify all of these settings later from your dashboard.</p>
      </div>
    </div>
  </div>
</div>
