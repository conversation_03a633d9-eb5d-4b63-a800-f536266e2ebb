<!-- Step 3: Contact Information & Location -->
<div class="space-y-6">
    <!-- Contact Information -->
    <div>
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="{{ form.organization_email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Station Email <span class="text-red-500">*</span>
                </label>
                {{ form.organization_email }}
                {% if form.organization_email.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.organization_email.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-xs text-gray-500">Main contact email for your station</p>
            </div>

            <div>
                <label for="{{ form.organization_phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Station Phone <span class="text-red-500">*</span>
                </label>
                {{ form.organization_phone }}
                {% if form.organization_phone.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.organization_phone.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-xs text-gray-500">Main phone number for your station</p>
            </div>

            <div class="md:col-span-2">
                <label for="{{ form.website.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Website
                </label>
                {{ form.website }}
                {% if form.website.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.website.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-xs text-gray-500">Your station's website URL (optional)</p>
            </div>
        </div>
    </div>

    <!-- Location Information -->
    <div>
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Station Location</h3>
        <div class="space-y-4">
            <div>
                <label for="{{ form.address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Street Address <span class="text-red-500">*</span>
                </label>
                {{ form.address }}
                {% if form.address.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.address.errors.0 }}</p>
                {% endif %}
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="{{ form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        City <span class="text-red-500">*</span>
                    </label>
                    {{ form.city }}
                    {% if form.city.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.city.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.state.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        State/Province <span class="text-red-500">*</span>
                    </label>
                    {{ form.state }}
                    {% if form.state.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.state.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.zip_code.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        ZIP/Postal Code <span class="text-red-500">*</span>
                    </label>
                    {{ form.zip_code }}
                    {% if form.zip_code.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.zip_code.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Country <span class="text-red-500">*</span>
                    </label>
                    {{ form.country }}
                    {% if form.country.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.country.errors.0 }}</p>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.timezone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        Time Zone <span class="text-red-500">*</span>
                    </label>
                    {{ form.timezone }}
                    {% if form.timezone.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.timezone.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-xs text-gray-500">This affects scheduling and timestamps</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Information Box -->
<div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fa-solid fa-map-marker-alt text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Location & Time Zone</h3>
            <div class="mt-2 text-sm text-blue-700">
                <p>Your location and time zone information helps us provide accurate scheduling and ensures mentions are aired at the correct times.</p>
            </div>
        </div>
    </div>
</div>
