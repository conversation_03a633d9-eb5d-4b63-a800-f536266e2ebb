<!-- Step 1: User Account Information -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Personal Information -->
    <div class="space-y-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
        
        <div>
            <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                First Name <span class="text-red-500">*</span>
            </label>
            {{ form.first_name }}
            {% if form.first_name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.first_name.errors.0 }}</p>
            {% endif %}
        </div>

        <div>
            <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Last Name <span class="text-red-500">*</span>
            </label>
            {{ form.last_name }}
            {% if form.last_name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.last_name.errors.0 }}</p>
            {% endif %}
        </div>

        <div>
            <label for="{{ form.job_title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Job Title
            </label>
            {{ form.job_title }}
            {% if form.job_title.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.job_title.errors.0 }}</p>
            {% endif %}
            <p class="mt-1 text-xs text-gray-500">e.g., Station Manager, Program Director, DJ</p>
        </div>

        <div>
            <label for="{{ form.phone.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
            </label>
            {{ form.phone }}
            {% if form.phone.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.phone.errors.0 }}</p>
            {% endif %}
        </div>
    </div>

    <!-- Account Information -->
    <div class="space-y-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
        
        <div>
            <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Username <span class="text-red-500">*</span>
            </label>
            {{ form.username }}
            {% if form.username.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
            {% endif %}
            {% if form.username.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ form.username.help_text }}</p>
            {% endif %}
        </div>

        <div>
            <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Email Address <span class="text-red-500">*</span>
            </label>
            {{ form.email }}
            {% if form.email.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
            {% endif %}
            <p class="mt-1 text-xs text-gray-500">This will be your login email and primary contact</p>
        </div>

        <div>
            <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Password <span class="text-red-500">*</span>
            </label>
            {{ form.password1 }}
            {% if form.password1.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.password1.errors.0 }}</p>
            {% endif %}
            {% if form.password1.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ form.password1.help_text }}</p>
            {% endif %}
        </div>

        <div>
            <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                Confirm Password <span class="text-red-500">*</span>
            </label>
            {{ form.password2 }}
            {% if form.password2.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.password2.errors.0 }}</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- Non-field errors -->
{% if form.non_field_errors %}
    <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
        {% for error in form.non_field_errors %}
            <p class="text-sm text-red-600">{{ error }}</p>
        {% endfor %}
    </div>
{% endif %}

<!-- Information Box -->
<div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fa-solid fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Account Security</h3>
            <div class="mt-2 text-sm text-blue-700">
                <p>Your account will be the owner of your radio station organization. You'll be able to invite team members and manage all settings.</p>
            </div>
        </div>
    </div>
</div>
