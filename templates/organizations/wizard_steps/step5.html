<!-- Step 5: Review & Complete -->
<div class="space-y-8">
  <!-- Summary Review -->
  {% if summary_data %}
    <div>
      <h3 class="text-lg font-semibold text-gray-900 mb-6">Review Your Information</h3>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- User Information -->
        {% if summary_data.user %}
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 mb-3 flex items-center">
              <i class="fa-solid fa-user text-gray-400 mr-2"></i>
              Account Owner
            </h4>
            <dl class="space-y-2 text-sm">
              <div class="flex justify-between">
                <dt class="text-gray-600">Name:</dt>
                <dd class="text-gray-900 font-medium">{{ summary_data.user.name }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Username:</dt>
                <dd class="text-gray-900">{{ summary_data.user.username }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Email:</dt>
                <dd class="text-gray-900">{{ summary_data.user.email }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Job Title:</dt>
                <dd class="text-gray-900">{{ summary_data.user.job_title }}</dd>
              </div>
            </dl>
          </div>
        {% endif %}

        <!-- Organization Information -->
        {% if summary_data.organization %}
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 mb-3 flex items-center">
              <i class="fa-solid fa-radio text-gray-400 mr-2"></i>
              Radio Station
            </h4>
            <dl class="space-y-2 text-sm">
              <div class="flex justify-between">
                <dt class="text-gray-600">Name:</dt>
                <dd class="text-gray-900 font-medium">{{ summary_data.organization.name }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Call Sign:</dt>
                <dd class="text-gray-900">{{ summary_data.organization.call_sign }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Frequency:</dt>
                <dd class="text-gray-900">{{ summary_data.organization.frequency }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Type:</dt>
                <dd class="text-gray-900">{{ summary_data.organization.station_type|title }}</dd>
              </div>
            </dl>
          </div>
        {% endif %}

        <!-- Contact Information -->
        {% if summary_data.contact %}
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 mb-3 flex items-center">
              <i class="fa-solid fa-map-marker-alt text-gray-400 mr-2"></i>
              Contact & Location
            </h4>
            <dl class="space-y-2 text-sm">
              <div class="flex justify-between">
                <dt class="text-gray-600">Email:</dt>
                <dd class="text-gray-900">{{ summary_data.contact.email }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Phone:</dt>
                <dd class="text-gray-900">{{ summary_data.contact.phone }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">City:</dt>
                <dd class="text-gray-900">{{ summary_data.contact.city }}, {{ summary_data.contact.state }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Time Zone:</dt>
                <dd class="text-gray-900">{{ summary_data.contact.timezone }}</dd>
              </div>
            </dl>
          </div>
        {% endif %}

        <!-- Configuration -->
        {% if summary_data.configuration %}
          <div class="bg-gray-50 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 mb-3 flex items-center">
              <i class="fa-solid fa-cog text-gray-400 mr-2"></i>
              Configuration
            </h4>
            <dl class="space-y-2 text-sm">
              <div class="flex justify-between">
                <dt class="text-gray-600">Plan:</dt>
                <dd class="text-gray-900 font-medium">{{ summary_data.configuration.plan_type }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Users:</dt>
                <dd class="text-gray-900">{{ summary_data.configuration.estimated_users }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-gray-600">Mentions/Month:</dt>
                <dd class="text-gray-900">{{ summary_data.configuration.estimated_mentions }}</dd>
              </div>
            </dl>
          </div>
        {% endif %}
      </div>
    </div>
  {% endif %}

  <!-- Initial Setup Options -->
  <div>
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Initial Setup</h3>
    <div class="space-y-4">
      <div class="flex items-start">
        {{ form.create_sample_data }}
        <div class="ml-3">
          <label for="{{ form.create_sample_data.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.create_sample_data.label }}</label>
          {% if form.create_sample_data.help_text %}
            <p class="text-xs text-gray-500">{{ form.create_sample_data.help_text }}</p>
          {% endif %}
          <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fa-solid fa-exclamation-triangle text-yellow-400 text-xs"></i>
              </div>
              <div class="ml-2">
                <p class="text-xs text-yellow-800">
                  <strong>Note:</strong> Sample data is only recommended for testing and demo purposes. For production use, start with a clean organization and add your own data.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-start">
        {{ form.create_main_branch }}
        <div class="ml-3">
          <label for="{{ form.create_main_branch.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.create_main_branch.label }}</label>
          {% if form.create_main_branch.help_text %}
            <p class="text-xs text-gray-500">{{ form.create_main_branch.help_text }}</p>
          {% endif %}
        </div>
      </div>

      <div class="ml-6">
        <label for="{{ form.main_branch_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Branch Name</label>
        {{ form.main_branch_name }}
        {% if form.main_branch_name.errors %}
          <p class="mt-1 text-sm text-red-600">{{ form.main_branch_name.errors.0 }}</p>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Terms and Conditions -->
  <div>
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Terms & Conditions</h3>
    <div class="space-y-4">
      <div class="flex items-start">
        {{ form.agree_to_terms }}
        <div class="ml-3">
          <label for="{{ form.agree_to_terms.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.agree_to_terms.label }}</label>
        </div>
      </div>
      {% if form.agree_to_terms.errors %}
        <p class="ml-7 text-sm text-red-600">{{ form.agree_to_terms.errors.0 }}</p>
      {% endif %}

      <div class="flex items-start">
        {{ form.subscribe_to_updates }}
        <div class="ml-3">
          <label for="{{ form.subscribe_to_updates.id_for_label }}" class="text-sm font-medium text-gray-700">{{ form.subscribe_to_updates.label }}</label>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Non-field errors -->
{% if form.non_field_errors %}
  <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
    {% for error in form.non_field_errors %}
      <p class="text-sm text-red-600">{{ error }}</p>
    {% endfor %}
  </div>
{% endif %}

<!-- Success Box -->
<div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
  <div class="flex">
    <div class="flex-shrink-0">
      <i class="fa-solid fa-check-circle text-green-400"></i>
    </div>
    <div class="ml-3">
      <h3 class="text-sm font-medium text-green-800">Ready to Launch!</h3>
      <div class="mt-2 text-sm text-green-700">
        <p>You're all set! Click "Complete Setup" to create your radio station organization and start managing your mentions.</p>
      </div>
    </div>
  </div>
</div>
