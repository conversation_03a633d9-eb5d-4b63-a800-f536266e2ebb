{% extends 'base.html' %}
{% load static %}
{% load permission_tags %}

{% block title %}
  {{ organization.name }} - Organization Details
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <a href="{% url 'organizations:list' %}" class="text-gray-500 hover:text-gray-700 mr-4"><i class="fa-solid fa-arrow-left"></i></a>
            <div>
              <h1 class="text-xl font-semibold text-gray-900">{{ organization.name }}</h1>
              <p class="text-sm text-gray-500 mt-1">Organization Details</p>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            {% has_permission 'manage_settings' as can_manage_settings %}
            {% if can_manage_settings %}
              <a href="{% url 'organizations:edit' organization.slug %}" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 flex items-center">
                <i class="fa-solid fa-edit mr-2"></i>
                Edit Organization
              </a>
            {% endif %}
            <a href="{% url 'settings:organization_settings' %}" class="px-4 py-2 bg-primary-600 text-white font-medium rounded-md hover:bg-primary-700 flex items-center">
              <i class="fa-solid fa-cog mr-2"></i>
              Settings
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Organization Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Organization Information</h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-500">Organization Name</label>
                <p class="mt-1 text-sm text-gray-900">{{ organization.name }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Slug</label>
                <p class="mt-1 text-sm text-gray-900">{{ organization.slug }}</p>
              </div>
              {% if organization.description %}
                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-500">Description</label>
                  <p class="mt-1 text-sm text-gray-900">{{ organization.description }}</p>
                </div>
              {% endif %}
              <div>
                <label class="block text-sm font-medium text-gray-500">Created</label>
                <p class="mt-1 text-sm text-gray-900">{{ organization.created_at|date:'M d, Y' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Status</label>
                <p class="mt-1">
                  {% if organization.is_active %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <i class="fa-solid fa-circle text-green-400 mr-1 text-xs"></i>
                      Active
                    </span>
                  {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      <i class="fa-solid fa-circle text-gray-400 mr-1 text-xs"></i>
                      Inactive
                    </span>
                  {% endif %}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Contact Information</h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              {% if organization.email %}
                <div>
                  <label class="block text-sm font-medium text-gray-500">Email</label>
                  <p class="mt-1 text-sm text-gray-900">
                    <a href="mailto:{{ organization.email }}" class="text-primary-600 hover:text-primary-700">{{ organization.email }}</a>
                  </p>
                </div>
              {% endif %}
              {% if organization.phone %}
                <div>
                  <label class="block text-sm font-medium text-gray-500">Phone</label>
                  <p class="mt-1 text-sm text-gray-900">
                    <a href="tel:{{ organization.phone }}" class="text-primary-600 hover:text-primary-700">{{ organization.phone }}</a>
                  </p>
                </div>
              {% endif %}
              {% if organization.website %}
                <div>
                  <label class="block text-sm font-medium text-gray-500">Website</label>
                  <p class="mt-1 text-sm text-gray-900">
                    <a href="{{ organization.website }}" target="_blank" class="text-primary-600 hover:text-primary-700">
                      {{ organization.website }}
                      <i class="fa-solid fa-external-link text-xs ml-1"></i>
                    </a>
                  </p>
                </div>
              {% endif %}
              {% if organization.address %}
                <div class="{% if not organization.email and not organization.phone and not organization.website %}md:col-span-2{% endif %}">
                  <label class="block text-sm font-medium text-gray-500">Address</label>
                  <p class="mt-1 text-sm text-gray-900 whitespace-pre-line">{{ organization.address }}</p>
                </div>
              {% endif %}
            </div>
          </div>
        </div>

        <!-- Statistics -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Statistics</h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">{{ stats.total_members }}</div>
                <div class="text-sm text-gray-500">Members</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">{{ stats.total_shows }}</div>
                <div class="text-sm text-gray-500">Shows</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">{{ stats.total_clients }}</div>
                <div class="text-sm text-gray-500">Clients</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-gray-900">{{ stats.total_mentions }}</div>
                <div class="text-sm text-gray-500">Mentions</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
              <a href="{% url 'activity_logs:activity_logs' %}" class="text-sm text-primary-600 hover:text-primary-700">View all</a>
            </div>
          </div>
          <div class="p-6">
            {% if recent_activities %}
              <div class="space-y-4">
                {% for activity in recent_activities %}
                  <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                      <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        {% if activity.action == 'create' %}
                          <i class="fa-solid fa-plus text-green-600 text-xs"></i>
                        {% elif activity.action == 'update' %}
                          <i class="fa-solid fa-edit text-blue-600 text-xs"></i>
                        {% elif activity.action == 'delete' %}
                          <i class="fa-solid fa-trash text-red-600 text-xs"></i>
                        {% else %}
                          <i class="fa-solid fa-circle text-gray-600 text-xs"></i>
                        {% endif %}
                      </div>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm text-gray-900">{{ activity.description }}</p>
                      <p class="text-xs text-gray-500">
                        {% if activity.user %}
                          {{ activity.user.get_full_name|default:activity.user.username }}
                        {% else %}
                          System
                        {% endif %}• {{ activity.created_at|timesince }} ago
                      </p>
                    </div>
                  </div>
                {% endfor %}
              </div>
            {% else %}
              <p class="text-gray-500 text-center py-4">No recent activity</p>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
          </div>
          <div class="p-6">
            <div class="space-y-3">
              <a href="{% url 'mentions:mention_create' %}" class="w-full flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                <i class="fa-solid fa-plus mr-2 text-gray-500"></i>
                New Mention
              </a>
              <a href="{% url 'core:client_create' %}" class="w-full flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                <i class="fa-solid fa-building mr-2 text-gray-500"></i>
                New Client
              </a>
              <a href="{% url 'shows:show_create' %}" class="w-full flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                <i class="fa-solid fa-broadcast-tower mr-2 text-gray-500"></i>
                New Show
              </a>
              <a href="{% url 'mentions:calendar' %}" class="w-full flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                <i class="fa-solid fa-calendar mr-2 text-gray-500"></i>
                View Calendar
              </a>
            </div>
          </div>
        </div>

        <!-- Organization Members -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">Members</h3>
              {% if user.is_staff %}
                <a href="{% url 'authentication:user_manager' %}" class="text-sm text-primary-600 hover:text-primary-700">Manage</a>
              {% endif %}
            </div>
          </div>
          <div class="p-6">
            {% if members %}
              <div class="space-y-3">
                {% for member in members %}
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <i class="fa-solid fa-user text-gray-500 text-sm"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900">{{ member.user.get_full_name|default:member.user.username }}</p>
                      <p class="text-xs text-gray-500">{{ member.get_role_display }}</p>
                    </div>
                    {% if member.is_active %}
                      <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                    {% else %}
                      <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>
                    {% endif %}
                  </div>
                {% endfor %}
              </div>
              {% if members|length > 5 %}
                <div class="mt-4 text-center">
                  <a href="{% url 'authentication:user_manager' %}" class="text-sm text-primary-600 hover:text-primary-700">View all {{ stats.total_members }} members</a>
                </div>
              {% endif %}
            {% else %}
              <p class="text-gray-500 text-center py-4">No members found</p>
            {% endif %}
          </div>
        </div>

        <!-- Recent Shows -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">Shows</h3>
              <a href="{% url 'shows:show_list' %}" class="text-sm text-primary-600 hover:text-primary-700">View all</a>
            </div>
          </div>
          <div class="p-6">
            {% if shows %}
              <div class="space-y-3">
                {% for show in shows %}
                  <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900">{{ show.name }}</p>
                      <p class="text-xs text-gray-500">{{ show.start_time|time:'H:i' }} - {{ show.end_time|time:'H:i' }}</p>
                    </div>
                    {% if show.is_active %}
                      <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                    {% else %}
                      <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>
                    {% endif %}
                  </div>
                {% endfor %}
              </div>
            {% else %}
              <p class="text-gray-500 text-center py-4">No shows found</p>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
