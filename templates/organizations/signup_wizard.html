{% load static %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      {% block title %}
        Setup Your Radio Station - RadioMention
      {% endblock %}
    </title>

    <!-- Tailwind CSS -->
    <!-- Offline Font Awesome -->
    <link rel="stylesheet" href="{% static 'css/fontawesome.min.css' %}" />
    <script>
      window.FontAwesomeConfig = { autoReplaceSvg: 'nest' }
    </script>
    <script src="{% static 'js/fontawesome.min.js' %}"></script>

    <!-- Offline Inter Font -->
    <link rel="stylesheet" href="{% static 'css/inter-font.css' %}" />

    <!-- Tailwind CSS - Local Build -->
    <link rel="stylesheet" href="{% static 'css/tailwind.css' %}?v=3" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/compact.css' %}" />
    <link rel="stylesheet" href="{% static 'css/print.css' %}" media="print" />
    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

    <style>
      body {
        font-family: 'Inter', sans-serif;
      }
      .plan-radio input[type='radio'] {
        display: none;
      }
      .plan-radio label {
        display: block;
        padding: 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.2s;
      }
      .plan-radio input[type='radio']:checked + label {
        border-color: #3b82f6;
        background-color: #eff6ff;
      }
    </style>

    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: {
                50: '#f0f9ff',
                100: '#e0f2fe',
                200: '#bae6fd',
                300: '#7dd3fc',
                400: '#38bdf8',
                500: '#0ea5e9',
                600: '#0284c7',
                700: '#0369a1',
                800: '#075985',
                900: '#0c4a6e'
              }
            }
          }
        }
      }
    </script>
  </head>
  <body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
      <div class="max-w-4xl mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white shadow-sm">
              <i class="fa-solid fa-radio text-lg"></i>
            </div>
            <h1 class="ml-3 text-xl font-bold text-gray-900">RadioMention</h1>
          </div>
          <div class="text-sm text-gray-500">Step {{ step }} of 5</div>
        </div>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="bg-white border-b">
      <div class="max-w-4xl mx-auto px-4 py-3">
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: {{ progress_percentage }}%"></div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <div class="bg-white rounded-lg shadow-sm border">
        <!-- Step Header -->
        <div class="px-6 py-6 border-b border-gray-200">
          <h2 class="text-2xl font-bold text-gray-900">{{ step_title }}</h2>
          <p class="text-gray-600 mt-1">{{ step_description }}</p>
        </div>

        <!-- Form Content -->
        <div class="px-6 py-6">
          {% if messages %}
            {% for message in messages %}
              <div class="mb-4 p-4 rounded-md {% if message.tags == 'error' %}
                  
                  
                  
                  bg-red-50 border border-red-200 text-red-700



                {% elif message.tags == 'success' %}
                  
                  
                  
                  bg-green-50 border border-green-200 text-green-700



                {% else %}
                  
                  
                  
                  bg-blue-50 border border-blue-200 text-blue-700



                {% endif %}">{{ message }}</div>
            {% endfor %}
          {% endif %}

          <form method="post" enctype="multipart/form-data" class="space-y-6">
            {% csrf_token %}

            {% if step == 1 %}
              {% include 'organizations/wizard_steps/step1.html' %}
            {% elif step == 2 %}
              {% include 'organizations/wizard_steps/step2.html' %}
            {% elif step == 3 %}
              {% include 'organizations/wizard_steps/step3.html' %}
            {% elif step == 4 %}
              {% include 'organizations/wizard_steps/step4.html' %}
            {% elif step == 5 %}
              {% include 'organizations/wizard_steps/step5.html' %}
            {% endif %}

            <!-- Navigation Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
              <div>
                {% if step > 1 %}
                  {% with prev_step=step|add:'-1' %}
                    <a href="{% url 'organizations:signup_wizard' %}?step={{ prev_step }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                      <i class="fa-solid fa-arrow-left mr-2"></i>
                      Previous
                    </a>
                  {% endwith %}
                {% endif %}
              </div>

              <div class="flex items-center space-x-3">
                {% if step < 5 %}
                  <button type="submit" class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors">
                    Continue
                    <i class="fa-solid fa-arrow-right ml-2"></i>
                  </button>
                {% else %}
                  <button type="submit" class="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors">
                    <i class="fa-solid fa-check mr-2"></i>
                    Complete Setup
                  </button>
                {% endif %}
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="bg-white border-t mt-12">
      <div class="max-w-4xl mx-auto px-4 py-6">
        <div class="text-center text-sm text-gray-500">
          <p>
            Need help? <a href="#" class="text-primary-600 hover:text-primary-700">Contact Support</a>
          </p>
          <p class="mt-1">
            By continuing, you agree to our <a href="#" class="text-primary-600 hover:text-primary-700">Terms of Service</a> and <a href="#" class="text-primary-600 hover:text-primary-700">Privacy Policy</a>
          </p>
        </div>
      </div>
    </div>

    <script>
      // Form validation and enhancement
      document.addEventListener('DOMContentLoaded', function () {
        // Password strength indicator
        const password1 = document.getElementById('id_password1')
        if (password1) {
          password1.addEventListener('input', function () {
            // Add password strength indicator logic here
          })
        }
      
        // Plan selection enhancement
        const planRadios = document.querySelectorAll('input[name="plan_type"]')
        planRadios.forEach((radio) => {
          radio.addEventListener('change', function () {
            // Update estimated costs or features based on plan
          })
        })
      
        // Auto-format phone numbers
        const phoneInputs = document.querySelectorAll('input[type="tel"], input[name*="phone"]')
        phoneInputs.forEach((input) => {
          input.addEventListener('input', function () {
            // Add phone number formatting logic here
          })
        })
      
        // Logo upload preview
        const logoInput = document.getElementById('id_logo')
        if (logoInput) {
          logoInput.addEventListener('change', function (e) {
            const file = e.target.files[0]
            const preview = document.getElementById('logo-preview')
            const previewImg = document.getElementById('logo-preview-img')
      
            if (file) {
              // Check file size (5MB)
              if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB')
                this.value = ''
                return
              }
      
              // Check file type
              if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file')
                this.value = ''
                return
              }
      
              // Show preview
              const reader = new FileReader()
              reader.onload = function (e) {
                previewImg.src = e.target.result
                preview.classList.remove('hidden')
              }
              reader.readAsDataURL(file)
            } else {
              preview.classList.add('hidden')
            }
          })
        }
      })
    </script>
  </body>
</html>
