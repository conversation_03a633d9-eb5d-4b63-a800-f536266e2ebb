#!/bin/bash

# Simple Low-CPU Docker Build Script
# This script builds your Docker image with minimal CPU usage

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

# Load configuration if exists
if [[ -f "docker-build.config" ]]; then
    source docker-build.config
    log "Loaded configuration from docker-build.config"
else
    # Default low-CPU settings
    CPU_LIMIT=1
    MEMORY_LIMIT=1g
    PARALLEL_JOBS=1
    BUILD_TARGET=production
    warning "Using default low-CPU settings"
fi

log "Starting low-CPU Docker build..."
log "Settings: CPU=$CPU_LIMIT, Memory=$MEMORY_LIMIT, Jobs=$PARALLEL_JOBS"

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    error "Docker is not running. Please start Docker Desktop."
fi

# Set environment variables for build optimization
export DOCKER_BUILDKIT=1
export BUILDKIT_PROGRESS=plain
export MAKEFLAGS="-j$PARALLEL_JOBS"
export PIP_COMPILE_JOBS="$PARALLEL_JOBS"
export NODE_OPTIONS="--max-old-space-size=512"

# Clean up before build to free resources
log "Cleaning up Docker resources..."
docker system prune -f >/dev/null 2>&1 || true

# Build with resource limits
log "Building Docker image with CPU optimization..."

# Use nice and ionice if available to lower priority
if command -v nice >/dev/null 2>&1 && command -v ionice >/dev/null 2>&1; then
    NICE_CMD="nice -n 10 ionice -c 3"
    log "Using nice/ionice to lower build priority"
else
    NICE_CMD=""
fi

$NICE_CMD docker build \
    --memory="$MEMORY_LIMIT" \
    --cpus="$CPU_LIMIT" \
    --target "$BUILD_TARGET" \
    --build-arg MAKEFLAGS="-j$PARALLEL_JOBS" \
    --build-arg PIP_COMPILE_JOBS="$PARALLEL_JOBS" \
    --tag radiomention:latest \
    --progress=plain \
    . || error "Docker build failed"

log "Docker build completed successfully!"

# Show image size
docker images radiomention:latest

log "Build complete! You can now run: docker-compose up -d"

# Optional: Clean up build cache to save space
read -p "Clean up build cache to save disk space? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log "Cleaning up build cache..."
    docker builder prune -f
    log "Build cache cleaned"
fi
