# RadioMention - Offline Static Assets

This directory contains all the offline static assets for the RadioMention application, ensuring the application works without internet connectivity.

## 📁 Directory Structure

```
static/
├── css/
│   ├── compact.css          # Custom compact styling
│   ├── fallback.css         # Fallback CSS when Tai<PERSON><PERSON> fails
│   ├── fontawesome.min.css  # Font Awesome icons (offline)
│   ├── inter-font.css       # Inter font definitions
│   ├── tailwind.min.css     # Tailwind CSS framework (offline)
│   └── wizard.css           # Wizard form styling
├── fonts/
│   ├── Inter-*.woff2        # Inter font files (WOFF2 format)
│   ├── Inter-*.woff         # Inter font files (WOFF format)
│   └── ...                  # All Inter font weights and styles
├── img/
│   └── (application images)
└── js/
    ├── alpine.min.js        # Alpine.js for reactivity
    ├── app.js               # Custom application JavaScript
    ├── fontawesome.min.js   # Font Awesome JavaScript
    └── jquery.min.js        # jQuery library
```

## 🔧 Assets Included

### CSS Frameworks & Libraries
- **Tailwind CSS v3.3.0** - Complete utility-first CSS framework
- **Font Awesome v6.4.0** - Icon library (CSS + JS)
- **Inter Font** - Google Fonts Inter family (all weights)
- **Fallback CSS** - Basic styling when <PERSON><PERSON><PERSON> fails to load

### JavaScript Libraries
- **jQuery v3.7.1** - DOM manipulation and AJAX
- **Alpine.js v3.13.3** - Lightweight reactive framework
- **Custom App.js** - Application-specific functionality

### Fonts
- **Inter Font Family** - Complete set including:
  - Thin (100)
  - Extra Light (200)
  - Light (300)
  - Regular (400)
  - Medium (500)
  - Semi Bold (600)
  - Bold (700)
  - Extra Bold (800)
  - Black (900)
  - All italic variants
  - Variable font versions

## 🚀 Benefits of Offline Assets

### 1. **No Internet Dependency**
- Application works completely offline
- No external CDN dependencies
- Faster loading times (no external requests)

### 2. **Improved Performance**
- Reduced latency (local files)
- Better caching control
- No external DNS lookups

### 3. **Enhanced Security**
- No external script loading
- Reduced attack surface
- Complete control over asset integrity

### 4. **Reliability**
- No CDN downtime issues
- Consistent availability
- Predictable performance

## 📋 Implementation Details

### Base Template Integration
The `templates/base.html` has been updated to use offline assets:

```html
<!-- Offline Tailwind CSS -->
<link rel="stylesheet" href="{% static 'css/tailwind.min.css' %}" />

<!-- Offline Font Awesome -->
<link rel="stylesheet" href="{% static 'css/fontawesome.min.css' %}" />
<script src="{% static 'js/fontawesome.min.js' %}"></script>

<!-- Offline Inter Font -->
<link rel="stylesheet" href="{% static 'css/inter-font.css' %}" />

<!-- Fallback CSS -->
<link rel="stylesheet" href="{% static 'css/fallback.css' %}" />

<!-- JavaScript Libraries -->
<script src="{% static 'js/jquery.min.js' %}"></script>
<script src="{% static 'js/alpine.min.js' %}" defer></script>
<script src="{% static 'js/app.js' %}"></script>
```

### Fallback Strategy
1. **Fallback CSS** loads first to provide basic styling
2. **Tailwind CSS** enhances with utility classes
3. **Custom CSS** adds application-specific styles

## 🔄 Updating Assets

### Tailwind CSS
```bash
curl -o static/css/tailwind.min.css https://cdn.tailwindcss.com/3.3.0
```

### Font Awesome
```bash
# CSS
curl -o static/css/fontawesome.min.css https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css

# JavaScript
curl -o static/js/fontawesome.min.js https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js
```

### jQuery
```bash
curl -o static/js/jquery.min.js https://code.jquery.com/jquery-3.7.1.min.js
```

### Alpine.js
```bash
curl -o static/js/alpine.min.js https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js
```

### Inter Font
```bash
# Download and extract Inter font package
curl -L -o static/fonts/inter-fonts.zip https://github.com/rsms/inter/releases/download/v3.19/Inter-3.19.zip
cd static/fonts && unzip inter-fonts.zip
cp "Inter Web"/*.woff2 . && cp "Inter Web"/*.woff .
```

## 🛠️ Custom JavaScript Features

The `app.js` file provides:

### Core Functionality
- **Tooltips** - `data-tooltip` attribute support
- **Modals** - Open/close modal functionality
- **Form Validation** - Client-side validation
- **Notifications** - Toast-style notifications

### Utility Functions
- **AJAX Requests** - `makeRequest()` with CSRF support
- **Date Formatting** - `formatDate()` with multiple formats
- **Debouncing** - `debounce()` for performance
- **CSRF Token** - Automatic CSRF token handling

### Usage Examples
```javascript
// Show notification
RadioMention.showNotification('Success!', 'success');

// Open modal
RadioMention.openModal('myModal');

// Make AJAX request
RadioMention.makeRequest('/api/mentions/')
    .then(data => console.log(data));

// Format date
const formatted = RadioMention.formatDate(new Date(), 'long');
```

## 📊 File Sizes

| Asset | Size | Description |
|-------|------|-------------|
| tailwind.min.css | ~391KB | Complete Tailwind framework |
| fontawesome.min.css | ~99KB | Font Awesome CSS |
| fontawesome.min.js | ~1.4MB | Font Awesome JavaScript |
| jquery.min.js | ~87KB | jQuery library |
| alpine.min.js | ~43KB | Alpine.js framework |
| Inter fonts | ~3.5MB | Complete Inter font family |
| app.js | ~12KB | Custom application code |
| fallback.css | ~8KB | Fallback styling |

**Total Size:** ~5.5MB (one-time download, cached locally)

## 🔍 Browser Compatibility

All assets are compatible with:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📝 Notes

1. **Font Loading**: Inter fonts use `font-display: swap` for better performance
2. **Tailwind Config**: Custom configuration preserved for primary colors
3. **Icon Loading**: Font Awesome uses SVG replacement for better performance
4. **Caching**: All assets include proper cache headers for production
5. **Compression**: Consider enabling gzip compression for static files

## 🚨 Important

- Keep assets updated for security patches
- Test offline functionality regularly
- Monitor asset sizes for performance
- Backup original CDN links in comments for reference
