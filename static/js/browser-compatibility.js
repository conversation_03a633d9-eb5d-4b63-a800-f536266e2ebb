/**
 * Browser Compatibility Detection and Warnings
 * Detects IE11 and other older browsers and shows appropriate warnings
 */

(function() {
    'use strict';
    
    // Browser detection
    function detectBrowser() {
        var ua = navigator.userAgent;
        var browser = {
            name: 'unknown',
            version: 0,
            isIE: false,
            isEdge: false,
            isChrome: false,
            isFirefox: false,
            isSafari: false,
            isSupported: true
        };
        
        // Internet Explorer detection
        if (ua.indexOf('MSIE') !== -1 || ua.indexOf('Trident/') !== -1) {
            browser.isIE = true;
            browser.name = 'Internet Explorer';
            
            // Extract version
            var version = ua.match(/(?:MSIE |rv:)(\d+(\.\d+)?)/);
            if (version) {
                browser.version = parseFloat(version[1]);
            }
            
            // IE11 and below are not fully supported
            browser.isSupported = browser.version >= 11;
        }
        // Microsoft Edge (Legacy)
        else if (ua.indexOf('Edge/') !== -1) {
            browser.isEdge = true;
            browser.name = 'Microsoft Edge (Legacy)';
            var edgeVersion = ua.match(/Edge\/(\d+(\.\d+)?)/);
            if (edgeVersion) {
                browser.version = parseFloat(edgeVersion[1]);
            }
            browser.isSupported = browser.version >= 79; // Chromium-based Edge
        }
        // Chrome
        else if (ua.indexOf('Chrome') !== -1 && ua.indexOf('Edg') === -1) {
            browser.isChrome = true;
            browser.name = 'Google Chrome';
            var chromeVersion = ua.match(/Chrome\/(\d+(\.\d+)?)/);
            if (chromeVersion) {
                browser.version = parseFloat(chromeVersion[1]);
            }
            browser.isSupported = browser.version >= 60;
        }
        // Firefox
        else if (ua.indexOf('Firefox') !== -1) {
            browser.isFirefox = true;
            browser.name = 'Mozilla Firefox';
            var firefoxVersion = ua.match(/Firefox\/(\d+(\.\d+)?)/);
            if (firefoxVersion) {
                browser.version = parseFloat(firefoxVersion[1]);
            }
            browser.isSupported = browser.version >= 55;
        }
        // Safari
        else if (ua.indexOf('Safari') !== -1 && ua.indexOf('Chrome') === -1) {
            browser.isSafari = true;
            browser.name = 'Safari';
            var safariVersion = ua.match(/Version\/(\d+(\.\d+)?)/);
            if (safariVersion) {
                browser.version = parseFloat(safariVersion[1]);
            }
            browser.isSupported = browser.version >= 12;
        }
        
        return browser;
    }
    
    // Feature detection
    function detectFeatures() {
        var features = {
            fetch: typeof fetch !== 'undefined',
            promise: typeof Promise !== 'undefined',
            map: typeof Map !== 'undefined',
            arrow: false,
            const: false,
            let: false,
            classes: false,
            asyncAwait: false,
            templateLiterals: false
        };
        
        // Test arrow functions
        try {
            eval('(() => {})');
            features.arrow = true;
        } catch (e) {
            features.arrow = false;
        }
        
        // Test const/let
        try {
            eval('const test = 1;');
            features.const = true;
        } catch (e) {
            features.const = false;
        }
        
        try {
            eval('let test = 1;');
            features.let = true;
        } catch (e) {
            features.let = false;
        }
        
        // Test classes
        try {
            eval('class Test {}');
            features.classes = true;
        } catch (e) {
            features.classes = false;
        }
        
        // Test async/await
        try {
            eval('async function test() { await Promise.resolve(); }');
            features.asyncAwait = true;
        } catch (e) {
            features.asyncAwait = false;
        }
        
        // Test template literals
        try {
            eval('`template ${1} literal`');
            features.templateLiterals = true;
        } catch (e) {
            features.templateLiterals = false;
        }
        
        return features;
    }
    
    // Show compatibility warning
    function showCompatibilityWarning(browser, features) {
        var warningDiv = document.createElement('div');
        warningDiv.id = 'browser-compatibility-warning';
        warningDiv.style.cssText = [
            'position: fixed',
            'top: 0',
            'left: 0',
            'right: 0',
            'background: #f59e0b',
            'color: white',
            'padding: 12px',
            'text-align: center',
            'z-index: 9999',
            'font-family: Arial, sans-serif',
            'font-size: 14px',
            'box-shadow: 0 2px 4px rgba(0,0,0,0.1)'
        ].join(';');
        
        var message = '';
        if (browser.isIE) {
            message = '⚠️ You are using Internet Explorer. Some features may not work properly. Please consider upgrading to a modern browser like Chrome, Firefox, or Edge.';
        } else if (!browser.isSupported) {
            message = '⚠️ Your browser (' + browser.name + ' ' + browser.version + ') may not support all features. Please update to the latest version.';
        } else if (!features.fetch || !features.promise) {
            message = '⚠️ Your browser is missing some modern features. Some functionality may be limited.';
        }
        
        if (message) {
            warningDiv.innerHTML = message + ' <button onclick="this.parentElement.style.display=\'none\'" style="margin-left: 10px; background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 4px 8px; cursor: pointer;">Dismiss</button>';
            document.body.insertBefore(warningDiv, document.body.firstChild);
            
            // Auto-hide after 10 seconds
            setTimeout(function() {
                if (warningDiv.parentElement) {
                    warningDiv.style.display = 'none';
                }
            }, 10000);
        }
    }
    
    // Log compatibility information
    function logCompatibilityInfo(browser, features) {
        console.group('Browser Compatibility Check');
        console.log('Browser:', browser.name, browser.version);
        console.log('Supported:', browser.isSupported);
        console.log('Features:', features);
        
        var missingFeatures = [];
        for (var feature in features) {
            if (!features[feature]) {
                missingFeatures.push(feature);
            }
        }
        
        if (missingFeatures.length > 0) {
            console.warn('Missing features:', missingFeatures.join(', '));
        } else {
            console.log('✅ All required features are supported');
        }
        console.groupEnd();
    }
    
    // Initialize compatibility check
    function initCompatibilityCheck() {
        var browser = detectBrowser();
        var features = detectFeatures();
        
        // Store in global object for debugging
        window.browserCompatibility = {
            browser: browser,
            features: features,
            isFullySupported: browser.isSupported && features.fetch && features.promise
        };
        
        logCompatibilityInfo(browser, features);
        
        // Show warning if needed
        if (!browser.isSupported || !features.fetch || !features.promise) {
            showCompatibilityWarning(browser, features);
        }
        
        // Add CSS class to body for conditional styling
        if (browser.isIE) {
            document.body.className += ' browser-ie';
        }
        if (!browser.isSupported) {
            document.body.className += ' browser-unsupported';
        }
    }
    
    // Run compatibility check when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCompatibilityCheck);
    } else {
        initCompatibilityCheck();
    }
    
})();
