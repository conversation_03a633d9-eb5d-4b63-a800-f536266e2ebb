/**
 * Polyfills for Internet Explorer 11 and older browsers
 * Add this script before your main application scripts
 */

// Polyfill for fetch API
if (!window.fetch) {
    window.fetch = function(url, options) {
        return new Promise(function(resolve, reject) {
            var xhr = new XMLHttpRequest();
            var method = (options && options.method) || 'GET';
            var headers = (options && options.headers) || {};
            var body = (options && options.body) || null;
            
            xhr.open(method, url, true);
            
            // Set headers
            for (var key in headers) {
                if (headers.hasOwnProperty(key)) {
                    xhr.setRequestHeader(key, headers[key]);
                }
            }
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    var response = {
                        ok: xhr.status >= 200 && xhr.status < 300,
                        status: xhr.status,
                        statusText: xhr.statusText,
                        headers: {
                            get: function(name) {
                                return xhr.getResponseHeader(name);
                            }
                        },
                        json: function() {
                            return Promise.resolve(JSON.parse(xhr.responseText));
                        },
                        text: function() {
                            return Promise.resolve(xhr.responseText);
                        },
                        blob: function() {
                            return Promise.resolve(new Blob([xhr.response]));
                        }
                    };
                    
                    if (response.ok) {
                        resolve(response);
                    } else {
                        reject(new Error('HTTP ' + xhr.status + ': ' + xhr.statusText));
                    }
                }
            };
            
            xhr.onerror = function() {
                reject(new Error('Network error'));
            };
            
            xhr.send(body);
        });
    };
}

// Polyfill for Promise (if not available)
if (!window.Promise) {
    window.Promise = function(executor) {
        var self = this;
        self.state = 'pending';
        self.value = undefined;
        self.handlers = [];
        
        function resolve(result) {
            if (self.state === 'pending') {
                self.state = 'fulfilled';
                self.value = result;
                self.handlers.forEach(handle);
                self.handlers = null;
            }
        }
        
        function reject(error) {
            if (self.state === 'pending') {
                self.state = 'rejected';
                self.value = error;
                self.handlers.forEach(handle);
                self.handlers = null;
            }
        }
        
        function handle(handler) {
            if (self.state === 'pending') {
                self.handlers.push(handler);
            } else {
                if (self.state === 'fulfilled' && typeof handler.onFulfilled === 'function') {
                    handler.onFulfilled(self.value);
                }
                if (self.state === 'rejected' && typeof handler.onRejected === 'function') {
                    handler.onRejected(self.value);
                }
            }
        }
        
        this.then = function(onFulfilled, onRejected) {
            return new Promise(function(resolve, reject) {
                handle({
                    onFulfilled: function(result) {
                        try {
                            resolve(onFulfilled ? onFulfilled(result) : result);
                        } catch (ex) {
                            reject(ex);
                        }
                    },
                    onRejected: function(error) {
                        try {
                            resolve(onRejected ? onRejected(error) : error);
                        } catch (ex) {
                            reject(ex);
                        }
                    }
                });
            });
        };
        
        this.catch = function(onRejected) {
            return this.then(null, onRejected);
        };
        
        executor(resolve, reject);
    };
    
    Promise.resolve = function(value) {
        return new Promise(function(resolve) {
            resolve(value);
        });
    };
    
    Promise.reject = function(error) {
        return new Promise(function(resolve, reject) {
            reject(error);
        });
    };
    
    Promise.all = function(promises) {
        return new Promise(function(resolve, reject) {
            var results = [];
            var remaining = promises.length;
            
            if (remaining === 0) {
                resolve(results);
                return;
            }
            
            promises.forEach(function(promise, index) {
                Promise.resolve(promise).then(function(value) {
                    results[index] = value;
                    remaining--;
                    if (remaining === 0) {
                        resolve(results);
                    }
                }).catch(reject);
            });
        });
    };
}

// Polyfill for Map (basic implementation)
if (!window.Map) {
    window.Map = function() {
        this._keys = [];
        this._values = [];
        this.size = 0;
    };
    
    Map.prototype.set = function(key, value) {
        var index = this._keys.indexOf(key);
        if (index === -1) {
            this._keys.push(key);
            this._values.push(value);
            this.size++;
        } else {
            this._values[index] = value;
        }
        return this;
    };
    
    Map.prototype.get = function(key) {
        var index = this._keys.indexOf(key);
        return index === -1 ? undefined : this._values[index];
    };
    
    Map.prototype.has = function(key) {
        return this._keys.indexOf(key) !== -1;
    };
    
    Map.prototype.delete = function(key) {
        var index = this._keys.indexOf(key);
        if (index !== -1) {
            this._keys.splice(index, 1);
            this._values.splice(index, 1);
            this.size--;
            return true;
        }
        return false;
    };
    
    Map.prototype.clear = function() {
        this._keys = [];
        this._values = [];
        this.size = 0;
    };
    
    Map.prototype.keys = function() {
        var index = 0;
        var keys = this._keys;
        return {
            next: function() {
                if (index < keys.length) {
                    return { value: keys[index++], done: false };
                }
                return { done: true };
            }
        };
    };
}

// Polyfill for Object.assign
if (!Object.assign) {
    Object.assign = function(target) {
        if (target == null) {
            throw new TypeError('Cannot convert undefined or null to object');
        }
        
        var to = Object(target);
        
        for (var index = 1; index < arguments.length; index++) {
            var nextSource = arguments[index];
            
            if (nextSource != null) {
                for (var nextKey in nextSource) {
                    if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                        to[nextKey] = nextSource[nextKey];
                    }
                }
            }
        }
        return to;
    };
}

// Polyfill for Array.from
if (!Array.from) {
    Array.from = function(arrayLike) {
        var result = [];
        for (var i = 0; i < arrayLike.length; i++) {
            result.push(arrayLike[i]);
        }
        return result;
    };
}

// Polyfill for Array.prototype.forEach (IE8 and below)
if (!Array.prototype.forEach) {
    Array.prototype.forEach = function(callback, thisArg) {
        for (var i = 0; i < this.length; i++) {
            callback.call(thisArg, this[i], i, this);
        }
    };
}

// Polyfill for performance.now
if (!window.performance || !window.performance.now) {
    window.performance = window.performance || {};
    window.performance.now = function() {
        return Date.now();
    };
}

console.log('IE11 Polyfills loaded successfully');
