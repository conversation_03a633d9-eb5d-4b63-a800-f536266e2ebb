/**
 * Recurring Mentions JavaScript
 * Handles form validation, time slot management, and show scheduling
 */

// Global variables
let showsData = {};
let csrfToken = '';

/**
 * Initialize the recurring mentions form
 */
function initializeRecurringMentions(shows, csrf) {
    showsData = shows;
    csrfToken = csrf;
    
    // Initialize form elements
    initializeFormElements();
    
    // Initialize existing time slots for edit forms
    initializeExistingTimeSlots();
    
    // Add event listeners
    addEventListeners();
}

/**
 * Initialize form elements and their states
 */
function initializeFormElements() {
    // Show/hide weekdays section based on frequency
    const frequencySelect = document.getElementById('frequency');
    if (frequencySelect) {
        const weekdaysSection = document.getElementById('weekdaysSection');
        if (frequencySelect.value === 'weekly') {
            weekdaysSection.style.display = 'block';
        }
    }
}

/**
 * Add event listeners to form elements
 */
function addEventListeners() {
    // Frequency change handler
    const frequencySelect = document.getElementById('frequency');
    if (frequencySelect) {
        frequencySelect.addEventListener('change', handleFrequencyChange);
    }
    
    // Show select handlers
    document.querySelectorAll('.show-select').forEach(select => {
        select.addEventListener('change', function() { 
            validateShowTimeEdit(this); 
        });
    });
    
    // Time select handlers
    document.querySelectorAll('.time-select').forEach(select => {
        select.addEventListener('change', function() { 
            validateTimeSelectionEdit(this); 
        });
    });
    
    // Time input handlers (backward compatibility)
    document.querySelectorAll('.time-input').forEach(input => {
        input.addEventListener('change', function() { 
            validateTimeRange(this); 
        });
    });
}

/**
 * Handle frequency selection change
 */
function handleFrequencyChange() {
    const weekdaysSection = document.getElementById('weekdaysSection');
    if (this.value === 'weekly') {
        weekdaysSection.style.display = 'block';
    } else {
        weekdaysSection.style.display = 'none';
    }
}

/**
 * Generate time slots for a given time range
 */
function generateTimeSlots(startTime, endTime) {
    const slots = [];
    const start = startTime.split(':').map(Number);
    const end = endTime.split(':').map(Number);

    let startMinutes = start[0] * 60 + start[1];
    let endMinutes = end[0] * 60 + end[1];

    // Handle shows that cross midnight
    if (endMinutes < startMinutes) {
        endMinutes += 24 * 60; // Add 24 hours
    }

    // Generate slots every 30 seconds (0.5 minutes)
    for (let minutes = startMinutes; minutes <= endMinutes; minutes += 0.5) {
        const actualMinutes = minutes % (24 * 60); // Handle overflow past midnight
        const hours = Math.floor(actualMinutes / 60);
        const mins = Math.floor(actualMinutes % 60);
        const secs = (actualMinutes % 1) * 60;

        const timeStr = `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        const displayStr = secs === 0 ? `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}` : timeStr;

        slots.push({
            value: timeStr,
            display: displayStr,
            minutes: actualMinutes
        });
    }

    return slots;
}

/**
 * Initialize time slots for existing assignments
 */
function initializeExistingTimeSlots() {
    document.querySelectorAll('.show-select').forEach(showSelect => {
        if (showSelect.value) {
            const selectedOption = showSelect.options[showSelect.selectedIndex];
            if (selectedOption.dataset.startTime && selectedOption.dataset.endTime) {
                loadTimeSlotsEdit(showSelect.value, showSelect);
            }
        }
    });
}

/**
 * Validate show and time selection
 */
function validateShowTimeEdit(selectElement) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const timeSelect = selectElement.closest('.schedule-row').querySelector('.time-select');
    const timeHint = selectElement.closest('.schedule-row').querySelector('.time-hint');

    // Clear previous validation styling
    selectElement.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300');

    if (selectedOption.value && selectedOption.dataset.startTime && selectedOption.dataset.endTime) {
        // Show time range hint
        timeHint.textContent = `Show airs: ${selectedOption.dataset.startTime} - ${selectedOption.dataset.endTime}`;
        timeHint.style.display = 'inline';

        // Load available time slots for this show
        loadTimeSlotsEdit(selectedOption.value, selectElement);
    } else {
        timeHint.style.display = 'none';
        // Clear and disable time select
        timeSelect.innerHTML = '<option value="">Select a show first</option>';
        timeSelect.disabled = true;
        timeSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300');
    }
}

/**
 * Load time slots for a specific show
 */
function loadTimeSlotsEdit(showId, showSelectElement) {
    const show = showsData[showId];
    if (!show || !show.startTime || !show.endTime) return;

    const scheduleRow = showSelectElement.closest('.schedule-row');
    const timeSelect = scheduleRow.querySelector('.time-select');

    // Get the current time from data attribute (for existing assignments) or current value
    const currentValue = timeSelect.dataset.currentTime || timeSelect.value;

    // Generate time slots
    const timeSlots = generateTimeSlots(show.startTime, show.endTime);

    // Clear existing options
    timeSelect.innerHTML = '';

    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Select mention time';
    timeSelect.appendChild(defaultOption);

    // Add time slot options
    timeSlots.forEach(slot => {
        const option = document.createElement('option');
        option.value = slot.value;
        option.textContent = slot.display;
        option.className = 'available-slot';

        // Restore previous selection if it matches
        // Handle different time formats: HH:MM:SS, HH:MM, etc.
        let isMatch = false;

        if (currentValue) {
            // Convert both to comparable formats
            const currentTimeNormalized = currentValue.substring(0, 5); // Get HH:MM part
            const slotTimeNormalized = slot.display; // Already in HH:MM format

            // Check various matching scenarios
            if (currentValue === slot.value ||
                currentValue === slot.display ||
                currentTimeNormalized === slotTimeNormalized) {
                isMatch = true;
            }
        }

        if (isMatch) {
            option.selected = true;
        }

        timeSelect.appendChild(option);
    });

    // Enable the time select
    timeSelect.disabled = false;

    // Validate the current selection
    if (timeSelect.value) {
        validateTimeSelectionEdit(timeSelect);
    }
}

/**
 * Validate time selection and check for conflicts
 */
function validateTimeSelectionEdit(timeSelect) {
    const scheduleRow = timeSelect.closest('.schedule-row');
    const showSelect = scheduleRow.querySelector('.show-select');
    const validationMessage = scheduleRow.querySelector('.time-validation-message');
    const conflictIndicator = scheduleRow.querySelector('.conflict-indicator');

    // Clear previous styling
    timeSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    showSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    scheduleRow.classList.remove('duplicate-highlight', 'conflict-highlight');

    // Hide validation messages
    validationMessage.style.display = 'none';
    conflictIndicator.style.display = 'none';

    if (!timeSelect.value || !showSelect.value) {
        return;
    }

    // Validate time is within show range
    const isValidTime = validateTimeWithinShowRange(timeSelect, showSelect);
    
    if (!isValidTime) {
        return; // Stop validation if time is outside show range
    }

    // Basic validation passed
    timeSelect.classList.add('border-green-300');
    showSelect.classList.add('border-green-300');

    // Check for duplicates within this form
    checkForDuplicatesInForm(scheduleRow);
}

/**
 * Validate that selected time is within show's time range
 */
function validateTimeWithinShowRange(timeSelect, showSelect) {
    const selectedTime = timeSelect.value;
    const showId = showSelect.value;
    const show = showsData[showId];

    if (!show || !selectedTime) return false;

    const scheduleRow = timeSelect.closest('.schedule-row');
    const conflictIndicator = scheduleRow.querySelector('.conflict-indicator');

    // Convert times to minutes for comparison
    const selectedMinutes = timeToMinutes(selectedTime);
    let startMinutes = timeToMinutes(show.startTime);
    let endMinutes = timeToMinutes(show.endTime);

    // Special handling for shows ending at midnight (00:00)
    // Convert 00:00 end time to 24:00 (1440 minutes) for proper comparison
    if (show.endTime === '00:00' || endMinutes === 0) {
        endMinutes = 1440; // 24:00 in minutes
    }

    let isValid = false;

    // Handle shows that cross midnight (e.g., 22:00 - 02:00)
    if (endMinutes < startMinutes) {
        // Show crosses midnight - time is valid if it's after start OR before end
        isValid = selectedMinutes >= startMinutes || selectedMinutes <= (endMinutes % 1440);
    } else {
        // Normal show or show ending at midnight - time must be between start and end
        isValid = selectedMinutes >= startMinutes && selectedMinutes <= endMinutes;
    }

    if (!isValid) {
        // Time is outside show range
        timeSelect.classList.add('border-red-300');
        showSelect.classList.add('border-red-300');
        scheduleRow.classList.add('conflict-highlight');

        const displayEndTime = show.endTime === '00:00' ? '24:00' : show.endTime;
        conflictIndicator.innerHTML = `❌ Time ${selectedTime} is outside show "${show.name}" time frame (${show.startTime} - ${displayEndTime})`;
        conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-red-600';
        conflictIndicator.style.display = 'block';

        return false;
    }

    return true;
}

/**
 * Convert time string to minutes
 */
function timeToMinutes(timeStr) {
    const parts = timeStr.split(':');
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    const seconds = parts[2] ? parseInt(parts[2], 10) : 0;

    return hours * 60 + minutes + (seconds / 60);
}

/**
 * Check for duplicate show/time combinations within the form
 */
function checkForDuplicatesInForm(currentRow) {
    const currentShowSelect = currentRow.querySelector('.show-select');
    const currentTimeSelect = currentRow.querySelector('.time-select');

    if (!currentShowSelect.value || !currentTimeSelect.value) return false;

    const allRows = document.querySelectorAll('.schedule-row');
    let hasDuplicate = false;

    allRows.forEach(row => {
        if (row === currentRow) return;

        const showSelect = row.querySelector('.show-select');
        const timeSelect = row.querySelector('.time-select');

        if (showSelect.value === currentShowSelect.value && timeSelect.value === currentTimeSelect.value) {
            hasDuplicate = true;

            // Highlight both rows
            row.classList.add('duplicate-highlight');
            currentRow.classList.add('duplicate-highlight');

            // Show warning
            const conflictIndicator = currentRow.querySelector('.conflict-indicator');
            conflictIndicator.innerHTML = '⚠️ Duplicate: Same show and time already selected';
            conflictIndicator.style.display = 'block';
            conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-yellow-600';
        }
    });

    return hasDuplicate;
}

/**
 * Add a new show assignment row
 */
function addShowAssignmentEdit() {
    const container = document.getElementById('showAssignments');
    const firstAssignment = container.querySelector('.show-assignment');
    const newAssignment = firstAssignment.cloneNode(true);

    // Generate a unique ID for the new assignment (for scheduled_days)
    const newAssignmentId = 'new_' + Date.now();

    // Clear values and reset state
    newAssignment.querySelectorAll('select, input').forEach(field => {
        if (field.type === 'hidden' && field.name === 'assignment_ids') {
            field.value = ''; // Clear assignment ID for new assignments
        } else if (field.type === 'checkbox' && field.name.startsWith('scheduled_days_')) {
            // Update checkbox names for new assignment
            field.name = `scheduled_days_${newAssignmentId}`;
            field.checked = false;
        } else {
            field.value = '';
        }
        field.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    });

    // Reset time select to default state
    const timeSelect = newAssignment.querySelector('.time-select');
    if (timeSelect) {
        timeSelect.innerHTML = '<option value="">Select a show first</option>';
        timeSelect.disabled = true;
        timeSelect.removeAttribute('data-assignment-id');
    }

    // Reset show select
    const showSelect = newAssignment.querySelector('.show-select');
    if (showSelect) {
        showSelect.removeAttribute('data-assignment-id');
    }

    // Clear validation messages
    newAssignment.querySelectorAll('.time-validation-message, .conflict-indicator').forEach(msg => {
        msg.style.display = 'none';
        msg.textContent = '';
    });

    // Clear highlighting
    newAssignment.classList.remove('duplicate-highlight', 'conflict-highlight');

    // Add remove button if it doesn't exist
    if (!newAssignment.querySelector('button[onclick*="removeShowAssignment"]')) {
        const removeDiv = document.createElement('div');
        removeDiv.className = 'mt-3';
        removeDiv.innerHTML = `
            <button type="button" onclick="removeShowAssignment(this)"
                    class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700">
                <i class="fa-solid fa-trash mr-1"></i>
                Remove Assignment
            </button>
        `;
        newAssignment.appendChild(removeDiv);
    }

    // Update event handlers
    const newShowSelect = newAssignment.querySelector('.show-select');
    const newTimeSelect = newAssignment.querySelector('.time-select');

    if (newShowSelect) {
        newShowSelect.addEventListener('change', function() { validateShowTimeEdit(this); });
    }
    if (newTimeSelect) {
        newTimeSelect.addEventListener('change', function() { validateTimeSelectionEdit(this); });
    }

    // Append to container
    container.appendChild(newAssignment);
}

/**
 * Remove a show assignment row
 */
function removeShowAssignment(button) {
    const assignment = button.closest('.show-assignment');
    if (assignment) {
        assignment.remove();
    }
}

/**
 * Set duration using quick buttons
 */
function setDuration(days) {
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');

    if (startDate.value) {
        const start = new Date(startDate.value);
        const end = new Date(start);
        end.setDate(start.getDate() + days);

        endDate.value = end.toISOString().split('T')[0];
    } else {
        alert('Please select a start date first');
    }
}

/**
 * Validate form and show preview
 */
function validateFormAndPreview() {
    // Basic form validation
    const form = document.getElementById('edit-form');
    if (!form.checkValidity()) {
        form.reportValidity();
        return false;
    }

    // Check for duplicates
    const rows = document.querySelectorAll('.schedule-row');
    let hasDuplicates = false;
    let hasInvalidTimes = false;

    rows.forEach(row => {
        const showSelect = row.querySelector('.show-select');
        const timeSelect = row.querySelector('.time-select');

        if (showSelect.value && timeSelect.value) {
            // Check for duplicates
            if (checkForDuplicatesInForm(row)) {
                hasDuplicates = true;
            }

            // Check for invalid times
            if (!validateTimeWithinShowRange(timeSelect, showSelect)) {
                hasInvalidTimes = true;
            }
        }
    });

    if (hasDuplicates) {
        alert('Please resolve duplicate show/time assignments before proceeding.');
        return false;
    }

    if (hasInvalidTimes) {
        alert('Please ensure all selected times are within their show time frames.');
        return false;
    }

    // Show preview (you can implement a modal or redirect to preview page)
    alert('Form validation passed! Preview functionality can be implemented here.');
    return true;
}

/**
 * Confirm update action
 */
function confirmUpdate() {
    const title = document.getElementById('title').value;
    const clientSelect = document.getElementById('client');
    const client = clientSelect.selectedOptions[0]?.text || 'Unknown';

    return confirm(`Are you sure you want to update the recurring mention "${title}" for ${client}?\n\nThis will affect future mentions and may regenerate the schedule.`);
}

/**
 * Backward compatibility function for time range validation
 */
function validateTimeRange(input) {
    // This function can be implemented for backward compatibility
    // with any remaining time input fields
    console.log('validateTimeRange called for:', input);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a recurring mentions page
    if (document.getElementById('edit-form') || document.querySelector('.show-assignment')) {
        // The template will call initializeRecurringMentions with the data
    }
});
