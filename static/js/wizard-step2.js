/**
 * Recurring Mentions Wizard - Step 2: Schedule Configuration
 * Handles show selection, time validation, and scheduling logic
 */

// Global variables for this step
let showsData = {};
let csrfToken = '';

document.addEventListener('DOMContentLoaded', function() {
    initializeStep2();
});

/**
 * Initialize Step 2 functionality
 */
function initializeStep2() {
    // Get CSRF token
    csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    // Initialize show data from template (will be populated by Django)
    initializeShowData();
    
    // Add event listeners
    addStep2EventListeners();
    
    // Initialize validation
    initializeValidation();
    
    // Set up API URLs
    setupApiUrls();
}

/**
 * Initialize show data (populated by Django template)
 */
function initializeShowData() {
    // Get show data from global scope (set by Django template)
    if (window.showsData) {
        showsData = window.showsData;
    }
}

/**
 * Add event listeners for Step 2
 */
function addStep2EventListeners() {
    // Show select handlers
    document.querySelectorAll('.show-select').forEach(select => {
        select.addEventListener('change', function() {
            validateShowTime(this);
            updateScheduleSummary();
        });
    });

    // Time select handlers
    document.querySelectorAll('.time-select').forEach(select => {
        select.addEventListener('change', function() {
            validateTimeSelection(this);
            updateScheduleSummary();
        });
    });
}

/**
 * Initialize validation state
 */
function initializeValidation() {
    // Clear any previous validation state
    clearAllValidationState();
    
    // Update initial summary
    updateScheduleSummary();
}

/**
 * Setup API URLs for AJAX calls
 */
function setupApiUrls() {
    // These will be set by the Django template
    window.wizardApiUrls = window.wizardApiUrls || {
        getAvailableTimeSlots: '/mentions/api/time-slots/',
        checkTimeSlotConflicts: '/mentions/api/check-conflicts/'
    };
}

/**
 * Clear all validation state from the form
 */
function clearAllValidationState() {
    // Clear all border colors
    document.querySelectorAll('.show-select, .time-select').forEach(element => {
        element.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    });
    
    // Clear all validation messages
    document.querySelectorAll('.time-validation-message, .conflict-indicator').forEach(element => {
        element.style.display = 'none';
    });
    
    // Clear all highlight classes
    document.querySelectorAll('.schedule-row').forEach(row => {
        row.classList.remove('duplicate-highlight', 'conflict-highlight');
    });
    
    // Clear time hints
    document.querySelectorAll('.time-hint').forEach(hint => {
        hint.style.display = 'none';
    });
}

/**
 * Validate show and time selection
 */
function validateShowTime(selectElement) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const timeSelect = selectElement.closest('.schedule-row').querySelector('.time-select');
    const timeHint = selectElement.closest('.schedule-row').querySelector('.time-hint');
    const dayNumber = selectElement.dataset.day;

    // Clear previous validation styling for this row
    const scheduleRow = selectElement.closest('.schedule-row');
    selectElement.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    timeSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    scheduleRow.classList.remove('duplicate-highlight', 'conflict-highlight');
    
    // Clear validation messages for this row
    const validationMessage = scheduleRow.querySelector('.time-validation-message');
    const conflictIndicator = scheduleRow.querySelector('.conflict-indicator');
    if (validationMessage) validationMessage.style.display = 'none';
    if (conflictIndicator) conflictIndicator.style.display = 'none';

    if (selectedOption.value && selectedOption.dataset.startTime && selectedOption.dataset.endTime) {
        // Show time range hint
        if (timeHint) {
            timeHint.textContent = `Show airs: ${selectedOption.dataset.startTime} - ${selectedOption.dataset.endTime}`;
            timeHint.style.display = 'inline';
        }

        // Load available time slots for this show
        loadTimeSlots(selectedOption.value, dayNumber, timeSelect);
    } else {
        if (timeHint) {
            timeHint.style.display = 'none';
        }
        // Clear and disable time select
        timeSelect.innerHTML = '<option value="">Select a show first</option>';
        timeSelect.disabled = true;
    }
    
    // Update schedule summary after any show selection change
    updateScheduleSummary();
}

/**
 * Load available time slots for a show
 */
// function loadTimeSlots(showId, dayNumber, timeSelect) {
//     console.log('Loading time slots for show:', showId, 'day:', dayNumber); // Debug log

//     // Show loading state
//     timeSelect.innerHTML = '<option value="">Loading time slots...</option>';
//     timeSelect.disabled = true;

//     // Check if API URL is available
//     if (!window.wizardApiUrls || !window.wizardApiUrls.getAvailableTimeSlots) {
//         console.error('API URL not available:', window.wizardApiUrls);
//         timeSelect.innerHTML = '<option value="">API URL not configured</option>';
//         return;
//     }

//     // Make API call to get available time slots
//     fetch(window.wizardApiUrls.getAvailableTimeSlots, {
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/json',
//             'X-CSRFToken': csrfToken
//         },
//         body: JSON.stringify({
//             show_id: showId,
//             weekday: parseInt(dayNumber)
//         })
//     })
//     .then(response => {
//         console.log('API response status:', response.status); // Debug log
//         return response.json();
//     })
//     .then(data => {
//         console.log('API response data:', data); // Debug log
//         if (data.success) {
//             populateTimeSlots(timeSelect, data.time_slots);
//         } else {
//             timeSelect.innerHTML = '<option value="">Error loading time slots</option>';
//             console.error('Error loading time slots:', data.error);
//         }
//     })
//     .catch(error => {
//         console.error('Error loading time slots:', error);

//         // Try to generate time slots locally as fallback
//         const showSelect = timeSelect.closest('.schedule-row').querySelector('.show-select');
//         const selectedOption = showSelect.options[showSelect.selectedIndex];

//         if (selectedOption && selectedOption.dataset.startTime && selectedOption.dataset.endTime) {
//             console.log('Attempting local time slot generation as fallback');
//             generateLocalTimeSlots(timeSelect, selectedOption.dataset.startTime, selectedOption.dataset.endTime);
//         } else {
//             timeSelect.innerHTML = '<option value="">Network error loading time slots</option>';
//         }
//     });
// }
function loadTimeSlots(showId, dayNumber, timeSelect) {
    console.log('📡 Loading time slots for show:', showId, '| Day:', dayNumber);

    if (!showId || !timeSelect) {
        console.error('❌ Invalid showId or timeSelect input.');
        return;
    }

    // Get show information for immediate local generation
    const showSelect = timeSelect.closest('.schedule-row')?.querySelector('.show-select');
    const selectedOption = showSelect?.options?.[showSelect.selectedIndex];
    const startTime = selectedOption?.dataset?.startTime;
    const endTime = selectedOption?.dataset?.endTime;

    // Immediately provide local time slots for better UX
    if (startTime && endTime) {
        console.log('🚀 Generating immediate local time slots for better UX');
        generateLocalTimeSlots(timeSelect, startTime, endTime);

        // Add manual input option
        addManualTimeInputOption(timeSelect);
    } else {
        // Show loading indicator if we can't generate local slots
        timeSelect.innerHTML = '<option value="">Loading time slots...</option>';
        timeSelect.disabled = true;
    }

    // Then load detailed slots with conflict checking in background
    loadDetailedTimeSlotsInBackground(showId, dayNumber, timeSelect);
}

function addManualTimeInputOption(timeSelect) {
    // Add a manual input option at the top
    const manualOption = document.createElement('option');
    manualOption.value = 'MANUAL_INPUT';
    manualOption.textContent = '✏️ Enter custom time...';
    manualOption.style.color = '#2563EB';
    manualOption.style.backgroundColor = '#EFF6FF';
    manualOption.style.fontWeight = 'bold';

    // Insert after the first option (Select time slot)
    if (timeSelect.options.length > 1) {
        timeSelect.insertBefore(manualOption, timeSelect.options[1]);
    } else {
        timeSelect.appendChild(manualOption);
    }
}

function loadDetailedTimeSlotsInBackground(showId, dayNumber, timeSelect) {
    // Ensure API URL is configured
    const apiUrl = window.wizardApiUrls?.getAvailableTimeSlots;
    if (!apiUrl) {
        console.error('⚠️ API URL for getAvailableTimeSlots is missing.');
        return;
    }

    // Prepare request payload
    const payload = {
        show_id: showId,
        weekday: parseInt(dayNumber, 10)
    };

    fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(payload)
    })
    .then(response => {
        console.log('✅ Background API status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('📊 Background API response:', data);
        if (data?.success && Array.isArray(data.time_slots)) {
            // Only update if user hasn't selected a manual input or specific time
            if (timeSelect.value === '' || timeSelect.value === 'MANUAL_INPUT') {
                const currentValue = timeSelect.value;
                populateDetailedTimeSlots(timeSelect, data.time_slots);

                // Restore manual input option and selection
                addManualTimeInputOption(timeSelect);
                if (currentValue === 'MANUAL_INPUT') {
                    timeSelect.value = 'MANUAL_INPUT';
                }
            }
        } else {
            console.error('❌ Background API returned error or no time slots:', data);
        }
    })
    .catch(error => {
        console.error('❌ Failed to load detailed time slots in background:', error);
        // Don't replace existing options on error since we already have local fallback
    });
}

function populateDetailedTimeSlots(timeSelect, timeSlots) {
    // Clear existing options but preserve the first "Select time slot" option
    timeSelect.innerHTML = '<option value="">Select time slot</option>';
    console.log('🕐 Populating detailed time slots:', timeSlots);

    timeSlots.forEach((slot, index) => {
        const option = document.createElement('option');
        const label = slot.time_display || slot.display_time || slot.time;
        option.value = slot.time;

        // Determine styling and message
        if (slot.conflicts?.length) {
            option.dataset.conflicts = JSON.stringify(slot.conflicts);
            option.classList.add('unavailable-slot');
            option.textContent = `🚫 ${label} (Conflict)`;
            option.style.color = '#9CA3AF';
            option.style.backgroundColor = '#F3F4F6';
        } else if (slot.warnings?.length) {
            option.dataset.warnings = JSON.stringify(slot.warnings);
            option.classList.add('warning-slot');
            option.textContent = `⚠️ ${label} (Warning)`;
            option.style.color = '#D97706';
            option.style.backgroundColor = '#FEF3C7';
        } else if (slot.is_available) {
            option.classList.add('available-slot');
            option.textContent = `✅ ${label}`;
            option.style.color = '#059669';
            option.style.backgroundColor = '#ECFDF5';
        } else {
            option.textContent = label;
            option.style.color = '#374151';
            option.style.backgroundColor = '#FFFFFF';
        }

        option.setAttribute('aria-label', label);
        timeSelect.appendChild(option);
        console.log(`➕ Added detailed [${index + 1}]: ${option.textContent} (${option.value})`);
    });

    timeSelect.disabled = false;
    timeSelect.style.display = 'block';
    timeSelect.style.visibility = 'visible';

    console.log('✅ Detailed time slots ready:', timeSelect.options.length, 'options loaded');
}


/**
 * Populate time slots dropdown
 */
// function populateTimeSlots(timeSelect, timeSlots) {
//     // Clear existing options
//     timeSelect.innerHTML = '<option value="">Select time slot</option>';

//     console.log('Populating time slots:', timeSlots); // Debug log

//     // Add time slot options
//     timeSlots.forEach((slot, index) => {
//         const option = document.createElement('option');
//         option.value = slot.time;

//         // Use time_display if available, otherwise format the time
//         option.textContent = slot.time_display || slot.display_time || slot.time;

//         // Ensure option is visible
//         option.style.display = 'block';
//         option.style.visibility = 'visible';

//         // Add data attributes for validation
//         if (slot.conflicts && slot.conflicts.length > 0) {
//             option.dataset.conflicts = JSON.stringify(slot.conflicts);
//             option.classList.add('unavailable-slot');
//             option.style.color = '#9CA3AF !important';
//             option.style.backgroundColor = '#F3F4F6 !important';
//             option.textContent = '🚫 ' + option.textContent + ' (Conflict)';
//         } else if (slot.warnings && slot.warnings.length > 0) {
//             option.dataset.warnings = JSON.stringify(slot.warnings);
//             option.classList.add('warning-slot');
//             option.style.color = '#D97706 !important';
//             option.style.backgroundColor = '#FEF3C7 !important';
//             option.textContent = '⚠️ ' + option.textContent + ' (Warning)';
//         } else if (slot.is_available) {
//             option.classList.add('available-slot');
//             option.style.color = '#059669 !important';
//             option.style.backgroundColor = '#ECFDF5 !important';
//             option.textContent = '✅ ' + option.textContent;
//         } else {
//             // Default styling for neutral slots
//             option.style.color = '#374151 !important';
//             option.style.backgroundColor = '#FFFFFF !important';
//         }

//         timeSelect.appendChild(option);
//         console.log(`Added option ${index + 1}: ${option.textContent} (value: ${option.value})`); // Debug log
//     });

//     // Enable the select
//     timeSelect.disabled = false;

//     // Force refresh the select element
//     timeSelect.style.display = 'block';
//     timeSelect.style.visibility = 'visible';

//     console.log('Time slots populated, dropdown enabled:', !timeSelect.disabled); // Debug log
//     console.log('Total options in select:', timeSelect.options.length); // Debug log
//     console.log('Select element HTML:', timeSelect.outerHTML.substring(0, 200) + '...'); // Debug log
// }
function populateTimeSlots(timeSelect, timeSlots) {
    // Clear existing options
    timeSelect.innerHTML = '<option value="">Select time slot</option>';
    console.log('🕐 Populating time slots:', timeSlots);

    timeSlots.forEach((slot, index) => {
        const option = document.createElement('option');
        const label = slot.time_display || slot.display_time || slot.time;
        option.value = slot.time;

        // Determine styling and message
        if (slot.conflicts?.length) {
            option.dataset.conflicts = JSON.stringify(slot.conflicts);
            option.classList.add('unavailable-slot');
            option.textContent = `🚫 ${label} (Conflict)`;
            option.style.color = '#9CA3AF';
            option.style.backgroundColor = '#F3F4F6';
        } else if (slot.warnings?.length) {
            option.dataset.warnings = JSON.stringify(slot.warnings);
            option.classList.add('warning-slot');
            option.textContent = `⚠️ ${label} (Warning)`;
            option.style.color = '#D97706';
            option.style.backgroundColor = '#FEF3C7';
        } else if (slot.is_available) {
            option.classList.add('available-slot');
            option.textContent = `✅ ${label}`;
            option.style.color = '#059669';
            option.style.backgroundColor = '#ECFDF5';
        } else {
            option.textContent = label;
            option.style.color = '#374151';
            option.style.backgroundColor = '#FFFFFF';
        }

        option.setAttribute('aria-label', label);
        timeSelect.appendChild(option);
        console.log(`➕ Added [${index + 1}]: ${option.textContent} (${option.value})`);
    });

    timeSelect.disabled = false;
    timeSelect.style.display = 'block';
    timeSelect.style.visibility = 'visible';

    console.log('✅ Time slots ready:', timeSelect.options.length, 'options loaded');
}


/**
 * Generate time slots locally as fallback
 */
function generateLocalTimeSlots(timeSelect, startTime, endTime) {
    console.log('Generating local time slots from', startTime, 'to', endTime);

    // Clear existing options
    timeSelect.innerHTML = '<option value="">Select time slot</option>';

    // Parse start and end times
    const start = startTime.split(':').map(Number);
    const end = endTime.split(':').map(Number);

    let startMinutes = start[0] * 60 + start[1];
    let endMinutes = end[0] * 60 + end[1];

    // Handle shows that cross midnight
    if (endMinutes < startMinutes) {
        endMinutes += 24 * 60; // Add 24 hours
    }

    // Generate 30-second intervals
    const intervalMinutes = 0.5; // 30 seconds
    let currentMinutes = startMinutes;
    let slotCount = 0;
    const maxSlots = 240; // Limit to prevent overwhelming dropdown

    while (currentMinutes < endMinutes && slotCount < maxSlots) {
        const hours = Math.floor(currentMinutes / 60) % 24;
        const minutes = Math.floor(currentMinutes % 60);
        const seconds = Math.floor((currentMinutes % 1) * 60);

        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        const displayTime = new Date(2000, 0, 1, hours, minutes, seconds).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        });

        const option = document.createElement('option');
        option.value = timeStr;
        option.textContent = `⏰ ${displayTime}`;
        option.style.color = '#374151';
        option.style.backgroundColor = '#FFFFFF';

        timeSelect.appendChild(option);

        currentMinutes += intervalMinutes;
        slotCount++;
    }

    // Enable the select
    timeSelect.disabled = false;

    console.log(`Generated ${slotCount} local time slots`);
}

/**
 * Test function for debugging time slot loading
 */
function testTimeSlotLoading(dayNumber) {
    console.log('Testing time slot loading for day:', dayNumber);

    const dayContainer = document.getElementById(`day-${dayNumber}-schedules`);
    if (!dayContainer) {
        console.error('Day container not found');
        return;
    }

    const showSelect = dayContainer.querySelector('.show-select');
    const timeSelect = dayContainer.querySelector('.time-select');

    if (!showSelect || !timeSelect) {
        console.error('Show or time select not found');
        return;
    }

    console.log('Show select value:', showSelect.value);
    console.log('Show select options:', showSelect.options.length);
    console.log('Time select value:', timeSelect.value);
    console.log('Time select options:', timeSelect.options.length);
    console.log('Time select disabled:', timeSelect.disabled);

    // Test API URL
    console.log('API URLs:', window.wizardApiUrls);

    // Test show data
    console.log('Shows data:', window.showsData);

    // If a show is selected, try to load time slots
    if (showSelect.value) {
        console.log('Triggering time slot loading...');
        loadTimeSlots(showSelect.value, dayNumber, timeSelect);
    } else {
        console.log('No show selected, adding test options to time select...');

        // Add some test options to see if the dropdown works at all
        timeSelect.innerHTML = '<option value="">Select time slot</option>';
        for (let i = 0; i < 5; i++) {
            const option = document.createElement('option');
            option.value = `test-${i}`;
            option.textContent = `Test Option ${i + 1}`;
            option.style.color = '#374151';
            option.style.backgroundColor = '#FFFFFF';
            timeSelect.appendChild(option);
        }
        timeSelect.disabled = false;

        console.log('Added test options, total options now:', timeSelect.options.length);
    }
}

// Make test function globally available
window.testTimeSlotLoading = testTimeSlotLoading;

/**
 * Validate time selection and check for conflicts
 */
function validateTimeSelection(timeSelect) {
    const scheduleRow = timeSelect.closest('.schedule-row');
    const showSelect = scheduleRow.querySelector('.show-select');
    const selectedTimeOption = timeSelect.options[timeSelect.selectedIndex];
    const validationMessage = scheduleRow.querySelector('.time-validation-message');
    const conflictIndicator = scheduleRow.querySelector('.conflict-indicator');

    // Clear previous styling
    timeSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    showSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    scheduleRow.classList.remove('duplicate-highlight', 'conflict-highlight');

    if (!timeSelect.value || !showSelect.value) {
        if (validationMessage) validationMessage.style.display = 'none';
        if (conflictIndicator) conflictIndicator.style.display = 'none';
        updateScheduleSummary();
        return;
    }

    // Handle manual time input
    if (timeSelect.value === 'MANUAL_INPUT') {
        handleManualTimeInput(timeSelect, showSelect);
        return;
    }

    const mentionTime = timeSelect.value;
    let isValid = true;
    let errorMessage = '';

    // Check for duplicates on the same day
    const duplicateInfo = checkForDuplicatesInWizard(scheduleRow);

    if (duplicateInfo && duplicateInfo.hasDuplicate) {
        isValid = false;
        errorMessage = `🚫 Duplicate: ${showSelect.options[showSelect.selectedIndex].text.split('(')[0].trim()} at ${mentionTime} already scheduled`;

        // Highlight the duplicate row
        if (duplicateInfo.duplicateRow) {
            duplicateInfo.duplicateRow.classList.add('duplicate-highlight');
            duplicateInfo.duplicateRow.querySelector('.show-select').classList.add('border-yellow-300');
            duplicateInfo.duplicateRow.querySelector('.time-select').classList.add('border-yellow-300');
        }
    }

    // Check if the selected time slot has conflicts or warnings (from API response)
    const conflicts = selectedTimeOption.dataset.conflicts ? JSON.parse(selectedTimeOption.dataset.conflicts) : [];
    const warnings = selectedTimeOption.dataset.warnings ? JSON.parse(selectedTimeOption.dataset.warnings) : [];

    if (conflicts.length > 0) {
        isValid = false;
        errorMessage = `🚫 ${conflicts[0].message}`;
    }

    // Apply styling based on validation result
    if (isValid) {
        if (warnings.length > 0) {
            // Warning - orange borders
            timeSelect.classList.add('border-orange-300');
            showSelect.classList.add('border-orange-300');
            if (conflictIndicator) {
                conflictIndicator.innerHTML = `<i class="fa-solid fa-exclamation-triangle text-orange-500"></i> ${warnings[0].message}`;
                conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-orange-600';
                conflictIndicator.style.display = 'block';
            }
        } else {
            // Valid - green borders
            timeSelect.classList.add('border-green-300');
            showSelect.classList.add('border-green-300');
            if (conflictIndicator) {
                conflictIndicator.innerHTML = `<i class="fa-solid fa-check-circle text-green-500"></i> Time slot available`;
                conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-green-600';
                conflictIndicator.style.display = 'block';
            }
        }
        if (validationMessage) validationMessage.style.display = 'none';
    } else {
        // Invalid - red borders and error message
        timeSelect.classList.add('border-red-300');
        showSelect.classList.add('border-red-300');
        if (validationMessage) {
            validationMessage.textContent = errorMessage;
            validationMessage.className = 'time-validation-message text-xs mt-1 text-red-600 font-medium';
            validationMessage.style.display = 'block';
        }
        if (conflictIndicator) conflictIndicator.style.display = 'none';

        if (duplicateInfo && duplicateInfo.hasDuplicate) {
            scheduleRow.classList.add('duplicate-highlight');
        }
    }

    // Also validate all other rows in the same day to clear any previous duplicate highlights
    validateAllRowsInDay(timeSelect.dataset.day);

    updateScheduleSummary();
}

function handleManualTimeInput(timeSelect, showSelect) {
    const scheduleRow = timeSelect.closest('.schedule-row');
    const selectedShowOption = showSelect.options[showSelect.selectedIndex];
    const startTime = selectedShowOption?.dataset?.startTime;
    const endTime = selectedShowOption?.dataset?.endTime;

    // Create a modal or prompt for time input
    const timeInput = prompt(
        `Enter custom time for ${selectedShowOption.text}\n\n` +
        `Show airs: ${startTime} - ${endTime}\n` +
        `Format: HH:MM:SS (e.g., 15:30:00 for 3:30:00 PM)\n` +
        `Or HH:MM (e.g., 15:30 for 3:30:00 PM)`,
        startTime || '12:00:00'
    );

    if (timeInput === null) {
        // User cancelled, reset to empty selection
        timeSelect.value = '';
        updateScheduleSummary();
        return;
    }

    // Validate and format the input
    const formattedTime = validateAndFormatTimeInput(timeInput, startTime, endTime);

    if (formattedTime.isValid) {
        // Add the custom time as a new option
        addCustomTimeOption(timeSelect, formattedTime.time, formattedTime.display);
        timeSelect.value = formattedTime.time;

        // Style as a custom input
        timeSelect.classList.add('border-blue-300');
        showSelect.classList.add('border-blue-300');

        // Show success message
        const conflictIndicator = scheduleRow.querySelector('.conflict-indicator');
        if (conflictIndicator) {
            conflictIndicator.innerHTML = `<i class="fa-solid fa-clock text-blue-500"></i> Custom time: ${formattedTime.display}`;
            conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-blue-600';
            conflictIndicator.style.display = 'block';
        }

        updateScheduleSummary();
    } else {
        // Show error and let user try again
        alert(`Invalid time format: ${formattedTime.error}\n\nPlease try again.`);
        // Trigger the manual input again
        setTimeout(() => handleManualTimeInput(timeSelect, showSelect), 100);
    }
}

function validateAndFormatTimeInput(input, showStartTime, showEndTime) {
    const timeInput = input.trim();

    // Try to parse different time formats
    let hours, minutes, seconds = 0;

    // Format: HH:MM:SS
    if (/^\d{1,2}:\d{2}:\d{2}$/.test(timeInput)) {
        const parts = timeInput.split(':');
        hours = parseInt(parts[0]);
        minutes = parseInt(parts[1]);
        seconds = parseInt(parts[2]);
    }
    // Format: HH:MM
    else if (/^\d{1,2}:\d{2}$/.test(timeInput)) {
        const parts = timeInput.split(':');
        hours = parseInt(parts[0]);
        minutes = parseInt(parts[1]);
        seconds = 0;
    }
    // Format: H:MM or HH:M
    else if (/^\d{1,2}:\d{1,2}$/.test(timeInput)) {
        const parts = timeInput.split(':');
        hours = parseInt(parts[0]);
        minutes = parseInt(parts[1]);
        seconds = 0;
    }
    else {
        return { isValid: false, error: 'Invalid format. Use HH:MM or HH:MM:SS' };
    }

    // Validate ranges
    if (hours < 0 || hours > 23) {
        return { isValid: false, error: 'Hours must be between 0 and 23' };
    }
    if (minutes < 0 || minutes > 59) {
        return { isValid: false, error: 'Minutes must be between 0 and 59' };
    }
    if (seconds < 0 || seconds > 59) {
        return { isValid: false, error: 'Seconds must be between 0 and 59' };
    }

    // Format the time
    const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    const displayTime = new Date(2000, 0, 1, hours, minutes, seconds).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    });

    // Optional: Check if time is within show hours (warning only)
    let warning = '';
    if (showStartTime && showEndTime) {
        const inputTime = new Date(2000, 0, 1, hours, minutes, seconds);
        const startParts = showStartTime.split(':');
        const endParts = showEndTime.split(':');
        const startTime = new Date(2000, 0, 1, parseInt(startParts[0]), parseInt(startParts[1]), parseInt(startParts[2] || 0));
        const endTime = new Date(2000, 0, 1, parseInt(endParts[0]), parseInt(endParts[1]), parseInt(endParts[2] || 0));

        if (inputTime < startTime || inputTime > endTime) {
            warning = `Time is outside show hours (${showStartTime} - ${showEndTime})`;
        }
    }

    return {
        isValid: true,
        time: formattedTime,
        display: displayTime,
        warning: warning
    };
}

function addCustomTimeOption(timeSelect, timeValue, displayText) {
    // Remove any existing custom options
    const existingCustomOptions = Array.from(timeSelect.options).filter(option =>
        option.dataset.isCustom === 'true'
    );
    existingCustomOptions.forEach(option => option.remove());

    // Create new custom option
    const customOption = document.createElement('option');
    customOption.value = timeValue;
    customOption.textContent = `✏️ ${displayText} (Custom)`;
    customOption.dataset.isCustom = 'true';
    customOption.style.color = '#2563EB';
    customOption.style.backgroundColor = '#EFF6FF';
    customOption.style.fontWeight = 'bold';

    // Insert after the manual input option
    const manualInputOption = Array.from(timeSelect.options).find(option =>
        option.value === 'MANUAL_INPUT'
    );

    if (manualInputOption) {
        timeSelect.insertBefore(customOption, manualInputOption.nextSibling);
    } else {
        // Insert after the first option if manual input option is not found
        if (timeSelect.options.length > 1) {
            timeSelect.insertBefore(customOption, timeSelect.options[1]);
        } else {
            timeSelect.appendChild(customOption);
        }
    }
}

/**
 * Check for duplicate show/time combinations within the wizard
 */
function checkForDuplicatesInWizard(currentRow) {
    const currentShowSelect = currentRow.querySelector('.show-select');
    const currentTimeSelect = currentRow.querySelector('.time-select');

    if (!currentShowSelect.value || !currentTimeSelect.value || currentTimeSelect.value === 'MANUAL_INPUT') {
        return { hasDuplicate: false, duplicateRow: null };
    }

    const dayNumber = currentTimeSelect.dataset.day;
    const dayContainer = document.getElementById(`day-${dayNumber}-schedules`);
    const allRows = dayContainer ? dayContainer.querySelectorAll('.schedule-row') : document.querySelectorAll('.schedule-row');
    let duplicateRow = null;
    let hasDuplicate = false;

    // Clear previous duplicate highlights for this day
    allRows.forEach(row => {
        if (row !== currentRow) {
            row.classList.remove('duplicate-highlight', 'conflict-highlight');
            row.querySelector('.show-select').classList.remove('border-yellow-300', 'border-orange-300');
            const timeSelect = row.querySelector('.time-select');
            if (timeSelect) {
                timeSelect.classList.remove('border-yellow-300', 'border-orange-300');
            }
        }
    });

    // Check for duplicates
    allRows.forEach(row => {
        if (row !== currentRow) {
            const rowShowSelect = row.querySelector('.show-select');
            const rowTimeSelect = row.querySelector('.time-select');

            if (rowShowSelect.value === currentShowSelect.value &&
                rowTimeSelect.value === currentTimeSelect.value &&
                currentShowSelect.value && currentTimeSelect.value &&
                rowTimeSelect.value !== 'MANUAL_INPUT') {
                hasDuplicate = true;
                duplicateRow = row;
            }
        }
    });

    return { hasDuplicate, duplicateRow };
}

/**
 * Validate all rows in a specific day
 */
function validateAllRowsInDay(dayNumber) {
    const dayContainer = document.getElementById(`day-${dayNumber}-schedules`);
    if (!dayContainer) return;

    const allRows = dayContainer.querySelectorAll('.schedule-row');

    // Re-validate all rows to ensure proper duplicate detection
    allRows.forEach(row => {
        const timeSelect = row.querySelector('.time-select');
        if (timeSelect && timeSelect.value) {
            validateTimeSelection(timeSelect);
        }
    });
}

/**
 * Add a new schedule row for a specific day
 */
function addScheduleRow(dayNumber) {
    const dayContainer = document.getElementById(`day-${dayNumber}-schedules`);
    if (!dayContainer) return;

    const existingRow = dayContainer.querySelector('.schedule-row');
    if (!existingRow) return;

    // Clone the existing row
    const newRow = existingRow.cloneNode(true);

    // Clear the values
    const showSelect = newRow.querySelector('.show-select');
    const timeSelect = newRow.querySelector('.time-select');

    showSelect.value = '';
    timeSelect.innerHTML = '<option value="">Select a show first</option>';
    timeSelect.disabled = true;

    // Clear validation styling
    showSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    timeSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    newRow.classList.remove('duplicate-highlight', 'conflict-highlight');

    // Clear validation messages
    const validationMessage = newRow.querySelector('.time-validation-message');
    const conflictIndicator = newRow.querySelector('.conflict-indicator');
    const timeHint = newRow.querySelector('.time-hint');

    if (validationMessage) validationMessage.style.display = 'none';
    if (conflictIndicator) conflictIndicator.style.display = 'none';
    if (timeHint) timeHint.style.display = 'none';

    // Add event listeners to the new row
    showSelect.addEventListener('change', function() {
        validateShowTime(this);
        updateScheduleSummary();
    });

    timeSelect.addEventListener('change', function() {
        validateTimeSelection(this);
        updateScheduleSummary();
    });

    // Add remove button
    const removeButton = document.createElement('button');
    removeButton.type = 'button';
    removeButton.className = 'mt-2 text-sm text-red-600 hover:text-red-800 flex items-center';
    removeButton.innerHTML = '<i class="fa-solid fa-trash mr-1"></i> Remove this slot';
    removeButton.onclick = function() {
        removeScheduleRow(newRow);
    };

    newRow.appendChild(removeButton);

    // Insert the new row
    dayContainer.appendChild(newRow);

    updateScheduleSummary();
}

/**
 * Remove a schedule row
 */
function removeScheduleRow(row) {
    const dayContainer = row.closest('[id*="day-"][id*="-schedules"]');
    const remainingRows = dayContainer.querySelectorAll('.schedule-row');

    // Don't remove if it's the last row
    if (remainingRows.length <= 1) {
        alert('At least one time slot is required per day.');
        return;
    }

    row.remove();
    updateScheduleSummary();
}

/**
 * Update the schedule summary
 */
function updateScheduleSummary() {
    const summaryContainer = document.getElementById('schedule-summary');
    const summaryContent = document.getElementById('summary-content');

    if (!summaryContainer || !summaryContent) return;

    let totalSlots = 0;
    let validSlots = 0;
    const daysSummary = [];

    // Check each day
    document.querySelectorAll('[id*="day-"][id*="-schedules"]').forEach(dayContainer => {
        const dayNumber = dayContainer.id.match(/day-(\d+)/)[1];
        const dayName = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][dayNumber];

        const rows = dayContainer.querySelectorAll('.schedule-row');
        let daySlots = 0;
        let dayValidSlots = 0;

        rows.forEach(row => {
            const showSelect = row.querySelector('.show-select');
            const timeSelect = row.querySelector('.time-select');

            if (showSelect.value && timeSelect.value) {
                daySlots++;
                totalSlots++;

                // Check if valid (no red borders indicating errors)
                if (!timeSelect.classList.contains('border-red-300')) {
                    dayValidSlots++;
                    validSlots++;
                }
            }
        });

        if (daySlots > 0) {
            daysSummary.push(`${dayName}: ${dayValidSlots}/${daySlots}`);
        }

        // Update individual day summary
        const daySummary = document.getElementById(`day-${dayNumber}-summary`);
        if (daySummary) {
            const summaryText = daySummary.querySelector('.summary-text');
            if (daySlots > 0) {
                summaryText.textContent = `${dayValidSlots}/${daySlots} valid time slots`;
                daySummary.style.display = 'block';
            } else {
                summaryText.textContent = 'No time slots configured';
                daySummary.style.display = 'none';
            }
        }
    });



    // Update overall summary
    if (totalSlots > 0) {
        summaryContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <strong>Total Slots:</strong> ${validSlots}/${totalSlots} valid
                </div>
                <div>
                    <strong>Per Week:</strong> ${validSlots} mentions
                </div>
            </div>
            <div class="mt-2">
                <strong>By Day:</strong> ${daysSummary.join(', ')}
            </div>
        `;
        summaryContainer.style.display = 'block';
    } else {
        summaryContainer.style.display = 'none';
    }
}

/**
 * Validate step 2 form before proceeding
 */
// function validateStep2Form() {
//     let hasValidSlots = false;
//     let hasErrors = false;
//     const errors = [];

//     // Check each day for valid slots
//     document.querySelectorAll('[id*="day-"][id*="-schedules"]').forEach(dayContainer => {
//         const dayNumber = dayContainer.id.match(/day-(\d+)/)[1];
//         const dayName = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][dayNumber];

//         const rows = dayContainer.querySelectorAll('.schedule-row');

//         rows.forEach(row => {
//             const showSelect = row.querySelector('.show-select');
//             const timeSelect = row.querySelector('.time-select');

//             if (showSelect.value && timeSelect.value) {
//                 hasValidSlots = true;

//                 // Check for validation errors
//                 if (timeSelect.classList.contains('border-red-300')) {
//                     hasErrors = true;
//                     errors.push(`${dayName}: Invalid time selection`);
//                 }
//             } else if (showSelect.value || timeSelect.value) {
//                 // Incomplete selection
//                 hasErrors = true;
//                 errors.push(`${dayName}: Incomplete show/time selection`);
//             }
//         });
//     });

//     if (!hasValidSlots) {
//         alert('Please schedule at least one show.');
//         return false;
//     }

//     if (hasErrors) {
//         alert('Please fix the following errors:\n\n' + errors.join('\n'));
//         return false;
//     }

//     return true;
// }
function validateStep2Form() {
    let hasValidSlots = false;
    let hasErrors = false;
    const errors = [];

    const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    // Check each day container for valid schedule rows
    document.querySelectorAll('[id^="day-"][id$="-schedules"]').forEach(dayContainer => {
        const match = dayContainer.id.match(/day-(\d+)/);
        if (!match) return;

        const dayNumber = parseInt(match[1], 10);
        const dayName = dayNames[dayNumber] || `Day ${dayNumber}`;

        const rows = dayContainer.querySelectorAll('.schedule-row');

        rows.forEach(row => {
            const showSelect = row.querySelector('.show-select');
            const timeSelect = row.querySelector('.time-select');

            const showValue = showSelect?.value?.trim();
            const timeValue = timeSelect?.value?.trim();

            if (showValue && timeValue && timeValue !== 'MANUAL_INPUT') {
                hasValidSlots = true;

                if (timeSelect.classList.contains('border-red-300')) {
                    hasErrors = true;
                    errors.push(`${dayName}: Invalid time selection`);
                }
            } else if (showValue || (timeValue && timeValue !== 'MANUAL_INPUT')) {
                hasErrors = true;
                errors.push(`${dayName}: Incomplete show/time selection`);
            } else if (timeValue === 'MANUAL_INPUT') {
                hasErrors = true;
                errors.push(`${dayName}: Please complete manual time input`);
            }
        });
    });

    if (!hasValidSlots) {
        alert('⚠️ Please schedule at least one complete show & time slot.');
        return false;
    }

    if (hasErrors) {
        alert('🚫 Please fix the following before proceeding:\n\n' + errors.join('\n'));
        return false;
    }

    return true;
}
