/**
 * Recurring Mentions Wizard - Step 1: Basic Information
 * Handles form validation and user interaction for the first step
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeStep1();
});

/**
 * Initialize Step 1 functionality
 */
function initializeStep1() {
    // Add form validation
    addFormValidation();
    
    // Add character counters
    addCharacterCounters();
    
    // Add day selection helpers
    addDaySelectionHelpers();
    
    // Auto-save form data (optional)
    addAutoSave();
}

/**
 * Add form validation
 */
function addFormValidation() {
    const form = document.querySelector('form');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        if (!validateStep1Form()) {
            e.preventDefault();
        }
    });

    // Real-time validation
    const titleInput = document.getElementById('title');
    const contentInput = document.getElementById('content');
    const clientSelect = document.getElementById('client_id');

    if (titleInput) {
        titleInput.addEventListener('blur', validateTitle);
        titleInput.addEventListener('input', validateTitle);
    }

    if (contentInput) {
        contentInput.addEventListener('blur', validateContent);
        contentInput.addEventListener('input', validateContent);
    }

    if (clientSelect) {
        clientSelect.addEventListener('change', validateClient);
    }
}

/**
 * Validate the entire Step 1 form
 */
function validateStep1Form() {
    let isValid = true;
    const errors = [];

    // Validate title
    if (!validateTitle()) {
        isValid = false;
        errors.push('Please enter a valid title');
    }

    // Validate content
    if (!validateContent()) {
        isValid = false;
        errors.push('Please enter valid content');
    }

    // Validate client selection
    if (!validateClient()) {
        isValid = false;
        errors.push('Please select a client');
    }

    // Validate day selection
    if (!validateDaySelection()) {
        isValid = false;
        errors.push('Please select at least one day');
    }

    if (!isValid) {
        alert('Please fix the following errors:\n\n' + errors.join('\n'));
    }

    return isValid;
}

/**
 * Validate title field
 */
function validateTitle() {
    const titleInput = document.getElementById('title');
    if (!titleInput) return true;

    const title = titleInput.value.trim();
    const isValid = title.length >= 3 && title.length <= 200;

    if (isValid) {
        titleInput.classList.remove('border-red-300');
        titleInput.classList.add('border-green-300');
    } else {
        titleInput.classList.remove('border-green-300');
        titleInput.classList.add('border-red-300');
    }

    return isValid;
}

/**
 * Validate content field
 */
function validateContent() {
    const contentInput = document.getElementById('content');
    if (!contentInput) return true;

    const content = contentInput.value.trim();
    const isValid = content.length >= 10 && content.length <= 1000;

    if (isValid) {
        contentInput.classList.remove('border-red-300');
        contentInput.classList.add('border-green-300');
    } else {
        contentInput.classList.remove('border-green-300');
        contentInput.classList.add('border-red-300');
    }

    return isValid;
}

/**
 * Validate client selection
 */
function validateClient() {
    const clientSelect = document.getElementById('client_id');
    if (!clientSelect) return true;

    const isValid = clientSelect.value !== '';

    if (isValid) {
        clientSelect.classList.remove('border-red-300');
        clientSelect.classList.add('border-green-300');
    } else {
        clientSelect.classList.remove('border-green-300');
        clientSelect.classList.add('border-red-300');
    }

    return isValid;
}

/**
 * Validate day selection
 */
function validateDaySelection() {
    const dayCheckboxes = document.querySelectorAll('input[name="selected_days"]:checked');
    const isValid = dayCheckboxes.length > 0;

    // Visual feedback for day selection
    const dayContainer = document.querySelector('.grid.grid-cols-2.md\\:grid-cols-4.lg\\:grid-cols-7');
    if (dayContainer) {
        if (isValid) {
            dayContainer.classList.remove('border-red-300');
        } else {
            dayContainer.classList.add('border-red-300');
        }
    }

    return isValid;
}

/**
 * Add character counters to text fields
 */
function addCharacterCounters() {
    addCharacterCounter('title', 200);
    addCharacterCounter('content', 1000);
}

/**
 * Add character counter to a specific field
 */
function addCharacterCounter(fieldId, maxLength) {
    const field = document.getElementById(fieldId);
    if (!field) return;

    // Create counter element
    const counter = document.createElement('div');
    counter.className = 'text-xs text-gray-500 mt-1';
    counter.id = fieldId + '-counter';

    // Insert after the field
    field.parentNode.insertBefore(counter, field.nextSibling);

    // Update counter function
    function updateCounter() {
        const currentLength = field.value.length;
        const remaining = maxLength - currentLength;
        
        counter.textContent = `${currentLength}/${maxLength} characters`;
        
        if (remaining < 20) {
            counter.className = 'text-xs text-red-500 mt-1';
        } else if (remaining < 50) {
            counter.className = 'text-xs text-yellow-600 mt-1';
        } else {
            counter.className = 'text-xs text-gray-500 mt-1';
        }
    }

    // Add event listeners
    field.addEventListener('input', updateCounter);
    field.addEventListener('paste', function() {
        setTimeout(updateCounter, 10);
    });

    // Initial update
    updateCounter();
}

/**
 * Add day selection helpers
 */
function addDaySelectionHelpers() {
    // Add "Select All" and "Clear All" buttons
    const dayContainer = document.querySelector('.grid.grid-cols-2.md\\:grid-cols-4.lg\\:grid-cols-7');
    if (!dayContainer) return;

    const helperDiv = document.createElement('div');
    helperDiv.className = 'flex space-x-2 mt-2';
    helperDiv.innerHTML = `
        <button type="button" onclick="selectAllDays()" class="text-xs text-blue-600 hover:text-blue-800">
            Select All
        </button>
        <span class="text-xs text-gray-400">|</span>
        <button type="button" onclick="clearAllDays()" class="text-xs text-blue-600 hover:text-blue-800">
            Clear All
        </button>
        <span class="text-xs text-gray-400">|</span>
        <button type="button" onclick="selectWeekdays()" class="text-xs text-blue-600 hover:text-blue-800">
            Weekdays Only
        </button>
        <span class="text-xs text-gray-400">|</span>
        <button type="button" onclick="selectWeekends()" class="text-xs text-blue-600 hover:text-blue-800">
            Weekends Only
        </button>
    `;

    dayContainer.parentNode.insertBefore(helperDiv, dayContainer.nextSibling);
}

/**
 * Select all days
 */
function selectAllDays() {
    const checkboxes = document.querySelectorAll('input[name="selected_days"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    validateDaySelection();
}

/**
 * Clear all days
 */
function clearAllDays() {
    const checkboxes = document.querySelectorAll('input[name="selected_days"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    validateDaySelection();
}

/**
 * Select weekdays only (Monday-Friday)
 */
function selectWeekdays() {
    const checkboxes = document.querySelectorAll('input[name="selected_days"]');
    checkboxes.forEach(checkbox => {
        const value = parseInt(checkbox.value);
        checkbox.checked = value >= 0 && value <= 4; // Monday (0) to Friday (4)
    });
    validateDaySelection();
}

/**
 * Select weekends only (Saturday-Sunday)
 */
function selectWeekends() {
    const checkboxes = document.querySelectorAll('input[name="selected_days"]');
    checkboxes.forEach(checkbox => {
        const value = parseInt(checkbox.value);
        checkbox.checked = value === 5 || value === 6; // Saturday (5) and Sunday (6)
    });
    validateDaySelection();
}

/**
 * Add auto-save functionality (optional)
 */
function addAutoSave() {
    const form = document.querySelector('form');
    if (!form) return;

    const inputs = form.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('change', saveFormData);
        if (input.type === 'text' || input.tagName === 'TEXTAREA') {
            input.addEventListener('input', debounce(saveFormData, 1000));
        }
    });

    // Load saved data on page load
    loadFormData();
}

/**
 * Save form data to localStorage
 */
function saveFormData() {
    const form = document.querySelector('form');
    if (!form) return;

    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        if (data[key]) {
            // Handle multiple values (like checkboxes)
            if (Array.isArray(data[key])) {
                data[key].push(value);
            } else {
                data[key] = [data[key], value];
            }
        } else {
            data[key] = value;
        }
    }

    localStorage.setItem('wizard_step1_data', JSON.stringify(data));
}

/**
 * Load form data from localStorage
 */
function loadFormData() {
    const savedData = localStorage.getItem('wizard_step1_data');
    if (!savedData) return;

    try {
        const data = JSON.parse(savedData);
        
        for (let [key, value] of Object.entries(data)) {
            const elements = document.querySelectorAll(`[name="${key}"]`);
            
            elements.forEach(element => {
                if (element.type === 'checkbox' || element.type === 'radio') {
                    if (Array.isArray(value)) {
                        element.checked = value.includes(element.value);
                    } else {
                        element.checked = element.value === value;
                    }
                } else {
                    element.value = Array.isArray(value) ? value[0] : value;
                }
            });
        }
    } catch (e) {
        console.warn('Failed to load saved form data:', e);
    }
}

/**
 * Debounce function to limit how often a function can be called
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Clear saved form data
 */
function clearSavedData() {
    localStorage.removeItem('wizard_step1_data');
}

// Clear saved data when form is successfully submitted
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (validateStep1Form()) {
                clearSavedData();
            }
        });
    }
});
