/**
 * Browser Compatibility Test Suite
 * Comprehensive testing for all browser compatibility features
 */

(function() {
    'use strict';
    
    var testResults = {
        polyfills: {},
        features: {},
        ajax: {},
        ui: {},
        overall: 'unknown'
    };
    
    // Test polyfills
    function testPolyfills() {
        console.group('Testing Polyfills');
        
        // Test fetch polyfill
        testResults.polyfills.fetch = typeof fetch === 'function';
        console.log('fetch API:', testResults.polyfills.fetch ? '✅' : '❌');
        
        // Test Promise polyfill
        testResults.polyfills.promise = typeof Promise === 'function';
        console.log('Promise:', testResults.polyfills.promise ? '✅' : '❌');
        
        // Test Map polyfill
        testResults.polyfills.map = typeof Map === 'function';
        console.log('Map:', testResults.polyfills.map ? '✅' : '❌');
        
        // Test Object.assign polyfill
        testResults.polyfills.objectAssign = typeof Object.assign === 'function';
        console.log('Object.assign:', testResults.polyfills.objectAssign ? '✅' : '❌');
        
        // Test Array.from polyfill
        testResults.polyfills.arrayFrom = typeof Array.from === 'function';
        console.log('Array.from:', testResults.polyfills.arrayFrom ? '✅' : '❌');
        
        console.groupEnd();
    }
    
    // Test AJAX functionality
    function testAjax() {
        console.group('Testing AJAX Functionality');
        
        // Test AJAX manager availability
        testResults.ajax.manager = typeof window.ajax === 'object';
        console.log('AJAX Manager:', testResults.ajax.manager ? '✅' : '❌');
        
        // Test AJAX methods
        if (window.ajax) {
            testResults.ajax.get = typeof window.ajax.get === 'function';
            testResults.ajax.post = typeof window.ajax.post === 'function';
            testResults.ajax.put = typeof window.ajax.put === 'function';
            testResults.ajax.patch = typeof window.ajax.patch === 'function';
            testResults.ajax.delete = typeof window.ajax.delete === 'function';
            
            console.log('GET method:', testResults.ajax.get ? '✅' : '❌');
            console.log('POST method:', testResults.ajax.post ? '✅' : '❌');
            console.log('PUT method:', testResults.ajax.put ? '✅' : '❌');
            console.log('PATCH method:', testResults.ajax.patch ? '✅' : '❌');
            console.log('DELETE method:', testResults.ajax.delete ? '✅' : '❌');
            
            // Test AJAX performance methods
            testResults.ajax.metrics = typeof window.ajax.metrics === 'function';
            console.log('Metrics:', testResults.ajax.metrics ? '✅' : '❌');
        }
        
        console.groupEnd();
    }
    
    // Test UI functionality
    function testUI() {
        console.group('Testing UI Functionality');
        
        // Test RadioMention global object
        testResults.ui.radioMention = typeof window.RadioMention === 'object';
        console.log('RadioMention object:', testResults.ui.radioMention ? '✅' : '❌');
        
        if (window.RadioMention) {
            testResults.ui.openModal = typeof window.RadioMention.openModal === 'function';
            testResults.ui.closeModal = typeof window.RadioMention.closeModal === 'function';
            testResults.ui.showNotification = typeof window.RadioMention.showNotification === 'function';
            testResults.ui.makeRequest = typeof window.RadioMention.makeRequest === 'function';
            
            console.log('openModal:', testResults.ui.openModal ? '✅' : '❌');
            console.log('closeModal:', testResults.ui.closeModal ? '✅' : '❌');
            console.log('showNotification:', testResults.ui.showNotification ? '✅' : '❌');
            console.log('makeRequest:', testResults.ui.makeRequest ? '✅' : '❌');
        }
        
        // Test browser compatibility detection
        testResults.ui.browserCompat = typeof window.browserCompatibility === 'object';
        console.log('Browser Compatibility:', testResults.ui.browserCompat ? '✅' : '❌');
        
        console.groupEnd();
    }
    
    // Test specific features
    function testFeatures() {
        console.group('Testing JavaScript Features');
        
        // Test event listeners
        try {
            var testElement = document.createElement('div');
            testElement.addEventListener('click', function() {});
            testResults.features.eventListeners = true;
        } catch (e) {
            testResults.features.eventListeners = false;
        }
        console.log('Event Listeners:', testResults.features.eventListeners ? '✅' : '❌');
        
        // Test DOM manipulation
        try {
            var testDiv = document.createElement('div');
            testDiv.className = 'test';
            testDiv.textContent = 'test';
            document.body.appendChild(testDiv);
            document.body.removeChild(testDiv);
            testResults.features.domManipulation = true;
        } catch (e) {
            testResults.features.domManipulation = false;
        }
        console.log('DOM Manipulation:', testResults.features.domManipulation ? '✅' : '❌');
        
        // Test JSON
        try {
            var testObj = { test: 'value' };
            var jsonStr = JSON.stringify(testObj);
            var parsedObj = JSON.parse(jsonStr);
            testResults.features.json = parsedObj.test === 'value';
        } catch (e) {
            testResults.features.json = false;
        }
        console.log('JSON:', testResults.features.json ? '✅' : '❌');
        
        // Test localStorage
        try {
            localStorage.setItem('test', 'value');
            var value = localStorage.getItem('test');
            localStorage.removeItem('test');
            testResults.features.localStorage = value === 'value';
        } catch (e) {
            testResults.features.localStorage = false;
        }
        console.log('localStorage:', testResults.features.localStorage ? '✅' : '❌');
        
        console.groupEnd();
    }
    
    // Run live tests
    function runLiveTests() {
        console.group('Running Live Tests');
        
        // Test notification system
        if (window.RadioMention && window.RadioMention.showNotification) {
            try {
                window.RadioMention.showNotification('Browser compatibility test notification', 'info');
                console.log('Notification test: ✅');
            } catch (e) {
                console.log('Notification test: ❌', e.message);
            }
        }
        
        // Test AJAX if available (non-blocking)
        if (window.ajax && window.ajax.get) {
            window.ajax.get('/static/js/browser-test-suite.js')
                .then(function() {
                    console.log('AJAX test: ✅');
                })
                .catch(function(error) {
                    console.log('AJAX test: ❌', error.message);
                });
        }
        
        console.groupEnd();
    }
    
    // Calculate overall compatibility score
    function calculateOverallScore() {
        var totalTests = 0;
        var passedTests = 0;
        
        // Count polyfill tests
        for (var key in testResults.polyfills) {
            totalTests++;
            if (testResults.polyfills[key]) passedTests++;
        }
        
        // Count feature tests
        for (var key in testResults.features) {
            totalTests++;
            if (testResults.features[key]) passedTests++;
        }
        
        // Count AJAX tests
        for (var key in testResults.ajax) {
            totalTests++;
            if (testResults.ajax[key]) passedTests++;
        }
        
        // Count UI tests
        for (var key in testResults.ui) {
            totalTests++;
            if (testResults.ui[key]) passedTests++;
        }
        
        var score = Math.round((passedTests / totalTests) * 100);
        
        if (score >= 90) {
            testResults.overall = 'excellent';
        } else if (score >= 75) {
            testResults.overall = 'good';
        } else if (score >= 50) {
            testResults.overall = 'fair';
        } else {
            testResults.overall = 'poor';
        }
        
        return {
            score: score,
            passed: passedTests,
            total: totalTests,
            rating: testResults.overall
        };
    }
    
    // Display test results
    function displayResults() {
        var score = calculateOverallScore();
        
        console.group('🧪 Browser Compatibility Test Results');
        console.log('Overall Score:', score.score + '%', '(' + score.passed + '/' + score.total + ')');
        console.log('Rating:', score.rating.toUpperCase());
        
        if (score.rating === 'excellent') {
            console.log('🎉 Excellent! All features are working properly.');
        } else if (score.rating === 'good') {
            console.log('👍 Good! Most features are working with minor issues.');
        } else if (score.rating === 'fair') {
            console.log('⚠️ Fair. Some features may not work properly.');
        } else {
            console.log('❌ Poor. Many features are not working. Consider using a modern browser.');
        }
        
        console.log('Detailed Results:', testResults);
        console.groupEnd();
        
        // Store results globally for debugging
        window.browserTestResults = {
            results: testResults,
            score: score
        };
    }
    
    // Main test runner
    function runAllTests() {
        console.log('🚀 Starting Browser Compatibility Tests...');
        
        testPolyfills();
        testFeatures();
        testAjax();
        testUI();
        runLiveTests();
        
        // Display results after a short delay to allow async tests
        setTimeout(displayResults, 1000);
    }
    
    // Export test functions for manual testing
    window.browserTests = {
        runAll: runAllTests,
        testPolyfills: testPolyfills,
        testFeatures: testFeatures,
        testAjax: testAjax,
        testUI: testUI,
        getResults: function() { return testResults; }
    };
    
    // Auto-run tests when DOM is ready (but only in development)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(runAllTests, 2000); // Wait for other scripts to load
            });
        } else {
            setTimeout(runAllTests, 2000);
        }
    }
    
})();
