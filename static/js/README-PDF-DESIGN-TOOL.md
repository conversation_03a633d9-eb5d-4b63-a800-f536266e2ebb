# Enhanced PDF Design Tool

A professional drag-and-drop template builder with modern UI/UX patterns for creating PDF report templates.

## Features

### 🎨 Modern Design Interface
- **Professional Toolbar**: Organized tool groups with familiar design patterns
- **Component Palette**: Drag-and-drop elements from sidebar to canvas
- **Properties Panel**: Context-sensitive property editing
- **Layers Panel**: Visual layer management with reordering

### 🖱️ Advanced Drag & Drop
- **Element Creation**: Click tools then click canvas, or drag from palette
- **Precise Positioning**: Snap-to-grid with configurable grid size
- **Visual Feedback**: Hover states, selection indicators, and resize handles
- **Multi-Selection**: Select multiple elements for batch operations (planned)

### ✏️ Interactive Editing
- **In-Place Editing**: Double-click text elements to edit inline
- **Resize Handles**: 8-point resize handles for precise sizing
- **Visual Selection**: Clear selection indicators with handles
- **Context Menu**: Right-click for element-specific actions

### ⌨️ Keyboard Shortcuts
- **Ctrl/Cmd + Z**: Undo
- **Ctrl/Cmd + Shift + Z**: Redo
- **Ctrl/Cmd + D**: Duplicate selected element
- **Ctrl/Cmd + S**: Save template
- **Delete/Backspace**: Delete selected element
- **Escape**: Deselect all / Switch to select tool
- **V**: Select tool
- **T**: Text tool
- **I**: Image tool
- **R**: Rectangle/Shape tool

### 🔄 State Management
- **Undo/Redo**: Full undo/redo stack with 50-action history
- **Auto-Save**: Automatic state saving on element changes
- **Template Versioning**: Save and load different template versions

### 📱 Responsive Design
- **Mobile Support**: Responsive layout for tablet and mobile editing
- **Touch Gestures**: Touch-friendly interactions for mobile devices
- **Adaptive UI**: Interface adapts to different screen sizes

## Architecture

### Class Structure
```javascript
class PDFDesignTool {
  // Core state management
  currentTemplate, selectedElement, elements[]
  
  // Tool and interaction state
  currentTool, zoomLevel, isDragging
  
  // History management
  undoStack[], redoStack[]
  
  // Configuration
  snapToGrid, gridSize, API_BASE
}
```

### Key Methods
- `setTool(toolName)`: Switch between design tools
- `createElement(type, x, y)`: Create new elements on canvas
- `selectElement(div, data)`: Handle element selection
- `saveState()`: Save current state for undo functionality
- `renderCanvasElements()`: Re-render all canvas elements

### Event Handling
- **Mouse Events**: Click, drag, resize, context menu
- **Keyboard Events**: Shortcuts and tool switching
- **Touch Events**: Mobile gesture support
- **API Events**: Template loading and saving

## Element Types

### Text Elements
- **Properties**: content, font_size, font_weight, color
- **Editing**: Double-click for inline editing
- **Styling**: Font family, size, weight, alignment

### Table Elements
- **Properties**: rows, columns, styling options
- **Configuration**: Header fonts, data fonts, borders
- **Data Binding**: Connect to mention data sources

### Image Elements
- **Properties**: src, alt, sizing options
- **Upload**: Drag & drop image files
- **Positioning**: Precise placement and scaling

### Shape Elements
- **Types**: Rectangle, circle, line
- **Styling**: Fill color, border color, opacity
- **Geometry**: Width, height, border radius

## API Integration

### Template Management
```javascript
// Load templates
GET /api/v1/pdf-templates/

// Save template
POST /api/v1/pdf-templates/
PUT /api/v1/pdf-templates/{id}/

// Clone template
POST /api/v1/pdf-templates/{id}/clone/
```

### Data Structure
```json
{
  "name": "Template Name",
  "description": "Template description",
  "paper_size": "A4",
  "orientation": "portrait",
  "layout_config": {
    "elements": [
      {
        "type": "text",
        "position": { "x": 100, "y": 50 },
        "size": { "width": 200, "height": 30 },
        "content": "Header Text",
        "style": {
          "font_size": 16,
          "font_weight": "bold",
          "color": "#000000"
        }
      }
    ]
  }
}
```

## Usage Examples

### Creating a New Element
```javascript
// Programmatically create element
pdfDesignTool.createElement('text', 100, 50)

// Or use drag & drop from palette
// User drags "Text" tool to canvas
```

### Customizing Element Properties
```javascript
// Select element and update properties
const element = pdfDesignTool.selectedElement
element.data.style.font_size = 14
element.data.content = "Updated text"
pdfDesignTool.updateElementProperties(element.data)
```

### Saving Template
```javascript
// Save current template
await pdfDesignTool.saveCurrentTemplate()

// Save with specific data
await pdfDesignTool.saveTemplate({
  name: "My Template",
  description: "Custom report template",
  layout_config: { elements: pdfDesignTool.elements }
})
```

## Browser Support

- **Modern Browsers**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Mobile Browsers**: iOS Safari 13+, Chrome Mobile 80+
- **Features Used**: ES6 classes, async/await, CSS Grid, Flexbox

## Performance Considerations

- **Element Limit**: Optimized for up to 100 elements per template
- **Undo Stack**: Limited to 50 actions to prevent memory issues
- **Rendering**: Efficient DOM updates with minimal reflows
- **Event Delegation**: Optimized event handling for large numbers of elements

## Future Enhancements

- **Multi-Selection**: Select and manipulate multiple elements
- **Grouping**: Group elements for easier management
- **Alignment Guides**: Smart guides for element alignment
- **Component Library**: Reusable component templates
- **Real-time Collaboration**: Multiple users editing simultaneously
- **Advanced Animations**: Element transitions and effects
