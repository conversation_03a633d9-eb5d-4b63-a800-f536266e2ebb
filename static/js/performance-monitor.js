/**
 * Performance Monitor for RadioMention App
 * Tracks and optimizes JavaScript performance
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            pageLoadTime: 0,
            domContentLoadedTime: 0,
            firstPaintTime: 0,
            firstContentfulPaintTime: 0,
            ajaxRequests: [],
            jsErrors: [],
            memoryUsage: [],
            slowOperations: []
        };
        
        this.thresholds = {
            slowAjax: 2000, // 2 seconds
            slowOperation: 1000, // 1 second
            memoryWarning: 50 * 1024 * 1024 // 50MB
        };
        
        this.init();
    }

    init() {
        this.trackPageLoad();
        this.trackAjaxPerformance();
        this.trackJSErrors();
        this.trackMemoryUsage();
        this.setupPerformanceObserver();
    }

    trackPageLoad() {
        // Track page load metrics
        window.addEventListener('load', () => {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
                this.metrics.domContentLoadedTime = navigation.domContentLoadedEventEnd - navigation.fetchStart;
            }
        });

        // Track paint metrics
        if ('PerformanceObserver' in window) {
            const paintObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name === 'first-paint') {
                        this.metrics.firstPaintTime = entry.startTime;
                    } else if (entry.name === 'first-contentful-paint') {
                        this.metrics.firstContentfulPaintTime = entry.startTime;
                    }
                }
            });
            paintObserver.observe({ entryTypes: ['paint'] });
        }
    }

    trackAjaxPerformance() {
        // Override fetch to track AJAX performance
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const startTime = performance.now();
            const url = args[0];
            
            try {
                const response = await originalFetch(...args);
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                this.recordAjaxRequest({
                    url,
                    method: args[1]?.method || 'GET',
                    duration,
                    status: response.status,
                    success: response.ok,
                    timestamp: Date.now()
                });
                
                // Warn about slow requests
                if (duration > this.thresholds.slowAjax) {
                    console.warn(`Slow AJAX request detected: ${url} took ${duration.toFixed(2)}ms`);
                }
                
                return response;
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                this.recordAjaxRequest({
                    url,
                    method: args[1]?.method || 'GET',
                    duration,
                    status: 0,
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                });
                
                throw error;
            }
        };
    }

    recordAjaxRequest(requestData) {
        this.metrics.ajaxRequests.push(requestData);
        
        // Keep only last 100 requests
        if (this.metrics.ajaxRequests.length > 100) {
            this.metrics.ajaxRequests.shift();
        }
    }

    trackJSErrors() {
        window.addEventListener('error', (event) => {
            this.metrics.jsErrors.push({
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack,
                timestamp: Date.now()
            });
            
            // Keep only last 50 errors
            if (this.metrics.jsErrors.length > 50) {
                this.metrics.jsErrors.shift();
            }
        });

        window.addEventListener('unhandledrejection', (event) => {
            this.metrics.jsErrors.push({
                message: 'Unhandled Promise Rejection: ' + event.reason,
                type: 'promise',
                timestamp: Date.now()
            });
        });
    }

    trackMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const memoryData = {
                    used: memory.usedJSHeapSize,
                    total: memory.totalJSHeapSize,
                    limit: memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                };
                
                this.metrics.memoryUsage.push(memoryData);
                
                // Keep only last 60 measurements (5 minutes at 5-second intervals)
                if (this.metrics.memoryUsage.length > 60) {
                    this.metrics.memoryUsage.shift();
                }
                
                // Warn about high memory usage
                if (memory.usedJSHeapSize > this.thresholds.memoryWarning) {
                    console.warn(`High memory usage detected: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
                }
            }, 5000); // Check every 5 seconds
        }
    }

    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            // Track long tasks
            const longTaskObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.duration > this.thresholds.slowOperation) {
                        this.metrics.slowOperations.push({
                            name: entry.name,
                            duration: entry.duration,
                            startTime: entry.startTime,
                            timestamp: Date.now()
                        });
                        
                        console.warn(`Long task detected: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
                    }
                }
            });
            
            try {
                longTaskObserver.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                // longtask not supported in all browsers
            }
        }
    }

    // Utility method to time operations
    timeOperation(name, operation) {
        const startTime = performance.now();
        
        try {
            const result = operation();
            
            // Handle promises
            if (result && typeof result.then === 'function') {
                return result.finally(() => {
                    const duration = performance.now() - startTime;
                    this.recordOperation(name, duration);
                });
            } else {
                const duration = performance.now() - startTime;
                this.recordOperation(name, duration);
                return result;
            }
        } catch (error) {
            const duration = performance.now() - startTime;
            this.recordOperation(name, duration, error);
            throw error;
        }
    }

    recordOperation(name, duration, error = null) {
        if (duration > this.thresholds.slowOperation) {
            this.metrics.slowOperations.push({
                name,
                duration,
                error: error?.message,
                timestamp: Date.now()
            });
        }
    }

    // Get performance summary
    getSummary() {
        const ajaxStats = this.getAjaxStats();
        const memoryStats = this.getMemoryStats();
        
        return {
            pageLoad: {
                totalTime: this.metrics.pageLoadTime,
                domContentLoaded: this.metrics.domContentLoadedTime,
                firstPaint: this.metrics.firstPaintTime,
                firstContentfulPaint: this.metrics.firstContentfulPaintTime
            },
            ajax: ajaxStats,
            memory: memoryStats,
            errors: {
                total: this.metrics.jsErrors.length,
                recent: this.metrics.jsErrors.slice(-5)
            },
            slowOperations: this.metrics.slowOperations.slice(-10)
        };
    }

    getAjaxStats() {
        const requests = this.metrics.ajaxRequests;
        if (requests.length === 0) return null;
        
        const successful = requests.filter(r => r.success);
        const failed = requests.filter(r => !r.success);
        const durations = requests.map(r => r.duration);
        
        return {
            total: requests.length,
            successful: successful.length,
            failed: failed.length,
            averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
            slowRequests: requests.filter(r => r.duration > this.thresholds.slowAjax).length
        };
    }

    getMemoryStats() {
        const measurements = this.metrics.memoryUsage;
        if (measurements.length === 0) return null;
        
        const latest = measurements[measurements.length - 1];
        const peak = Math.max(...measurements.map(m => m.used));
        
        return {
            current: latest.used,
            peak: peak,
            limit: latest.limit,
            currentMB: (latest.used / 1024 / 1024).toFixed(2),
            peakMB: (peak / 1024 / 1024).toFixed(2)
        };
    }

    // Export metrics for debugging
    exportMetrics() {
        return JSON.stringify(this.getSummary(), null, 2);
    }

    // Clear old metrics
    clearMetrics() {
        this.metrics.ajaxRequests = [];
        this.metrics.jsErrors = [];
        this.metrics.memoryUsage = [];
        this.metrics.slowOperations = [];
    }
}

// Initialize performance monitor
window.performanceMonitor = new PerformanceMonitor();

// Add console commands for debugging
window.perf = {
    summary: () => console.table(window.performanceMonitor.getSummary()),
    export: () => console.log(window.performanceMonitor.exportMetrics()),
    clear: () => window.performanceMonitor.clearMetrics(),
    ajax: () => window.ajax?.metrics?.() || 'AJAX manager not available'
};

// Log performance summary on page unload (for debugging)
window.addEventListener('beforeunload', () => {
    if (window.console && console.log) {
        console.log('Performance Summary:', window.performanceMonitor.getSummary());
    }
});
