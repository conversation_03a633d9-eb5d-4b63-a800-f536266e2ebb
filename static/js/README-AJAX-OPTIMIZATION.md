# AJAX Optimization Guide for RadioMention App

## Overview

Your app now includes advanced AJAX optimization features that will significantly improve JavaScript performance. Here's what has been added and how to use it.

## New Features Added

### 1. AJAX Manager (`static/js/ajax-manager.js`)
- **Request caching** - Automatically caches GET requests for 30 seconds
- **Request deduplication** - Prevents duplicate requests
- **Retry logic** - Automatically retries failed requests
- **Batch operations** - Combine multiple requests
- **Performance monitoring** - Tracks request times and success rates

### 2. Performance Monitor (`static/js/performance-monitor.js`)
- **Page load tracking** - Monitors page load times
- **AJAX performance** - Tracks all AJAX requests
- **Memory usage** - Monitors JavaScript memory consumption
- **Error tracking** - Captures JavaScript errors
- **Long task detection** - Identifies slow operations

## How to Use

### Basic AJAX Operations

Instead of using `fetch()` directly, use the optimized `window.ajax` methods:

```javascript
// Old way
fetch('/api/notifications/unread-count/')
  .then(response => response.json())
  .then(data => console.log(data));

// New optimized way
window.ajax.get('/api/notifications/unread-count/')
  .then(data => console.log(data));
```

### Available Methods

```javascript
// GET request (automatically cached)
window.ajax.get('/api/endpoint/')

// POST request
window.ajax.post('/api/endpoint/', { key: 'value' })

// PUT request
window.ajax.put('/api/endpoint/', { key: 'value' })

// PATCH request
window.ajax.patch('/api/endpoint/', { key: 'value' })

// DELETE request
window.ajax.delete('/api/endpoint/')

// Debounced GET (prevents rapid-fire requests)
window.ajax.getDebounced('/api/search/', {}, 500) // 500ms delay

// Batch multiple requests
window.ajax.batch([
  { url: '/api/endpoint1/', options: { method: 'GET' } },
  { url: '/api/endpoint2/', options: { method: 'GET' } }
])
```

### Advanced Options

```javascript
// Disable caching for specific request
window.ajax.get('/api/endpoint/', { noCache: true })

// Custom cache duration (in milliseconds)
window.ajax.get('/api/endpoint/', { cacheMaxAge: 60000 }) // 1 minute

// Custom headers
window.ajax.post('/api/endpoint/', data, {
  headers: { 'Custom-Header': 'value' }
})
```

## Performance Monitoring

### Console Commands

Open browser console and use these commands:

```javascript
// View performance summary
window.perf.summary()

// Export detailed metrics
window.perf.export()

// View AJAX manager metrics
window.perf.ajax()

// Clear performance data
window.perf.clear()
```

### What Gets Monitored

1. **Page Load Metrics**
   - Total page load time
   - DOM content loaded time
   - First paint time
   - First contentful paint time

2. **AJAX Performance**
   - Request duration
   - Success/failure rates
   - Slow request detection (>2 seconds)
   - Cache hit rates

3. **Memory Usage**
   - Current JavaScript heap size
   - Peak memory usage
   - Memory warnings (>50MB)

4. **Error Tracking**
   - JavaScript errors
   - Unhandled promise rejections
   - Error frequency

## Migration Guide

### For Existing Templates

Replace existing `fetch()` calls with optimized versions:

```javascript
// Before
fetch('/api/notifications/sync/')
  .then(response => response.json())
  .then(data => {
    // handle data
  })
  .catch(error => {
    console.error('Error:', error);
  });

// After
if (window.ajax) {
  window.ajax.get('/api/notifications/sync/')
    .then(data => {
      // handle data (already parsed JSON)
    })
    .catch(error => {
      console.error('Error:', error);
    });
} else {
  // Fallback for compatibility
  fetch('/api/notifications/sync/')
    .then(response => response.json())
    .then(data => {
      // handle data
    });
}
```

### For Form Submissions

```javascript
// Before
fetch('/api/endpoint/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-CSRFToken': getCsrfToken()
  },
  body: JSON.stringify(formData)
})

// After
window.ajax.post('/api/endpoint/', formData)
```

## Performance Benefits

### 1. Reduced Server Load
- **Request caching** reduces duplicate API calls
- **Request deduplication** prevents multiple identical requests
- **Batch operations** combine multiple requests into fewer calls

### 2. Faster User Experience
- **Cached responses** load instantly
- **Retry logic** handles temporary network issues
- **Debounced requests** prevent UI lag from rapid user actions

### 3. Better Error Handling
- **Automatic retries** for network failures
- **Centralized error handling** with consistent behavior
- **Performance monitoring** helps identify bottlenecks

### 4. Memory Optimization
- **Automatic cache cleanup** prevents memory leaks
- **Memory monitoring** alerts to high usage
- **Request queue management** prevents memory buildup

## Best Practices

### 1. Use Appropriate Methods
- Use `window.ajax.get()` for data retrieval
- Use `window.ajax.getDebounced()` for search/filter operations
- Use `window.ajax.batch()` for multiple related requests

### 2. Handle Fallbacks
Always provide fallback for compatibility:
```javascript
if (window.ajax) {
  // Use optimized AJAX
} else {
  // Use regular fetch as fallback
}
```

### 3. Monitor Performance
- Check console for performance warnings
- Use `window.perf.summary()` to identify slow operations
- Monitor memory usage in long-running pages

### 4. Cache Strategy
- Let GET requests cache automatically
- Use `noCache: true` for real-time data
- Adjust `cacheMaxAge` based on data freshness needs

## Troubleshooting

### Common Issues

1. **CSRF Token Issues**
   - The AJAX manager automatically handles CSRF tokens
   - Ensure your templates include `{% csrf_token %}`

2. **Cache Issues**
   - Use `window.ajax.clearCache()` to clear all cached data
   - Use `noCache: true` option for requests that need fresh data

3. **Performance Issues**
   - Check `window.perf.summary()` for slow operations
   - Look for memory warnings in console
   - Use debounced requests for user input

### Debug Mode

Enable debug logging:
```javascript
window.ajaxManager.debug = true; // Enable detailed logging
```

## Browser Support

- **Modern browsers**: Full feature support
- **Older browsers**: Automatic fallback to regular fetch/XMLHttpRequest
- **IE11+**: Basic functionality with polyfills

## Next Steps

1. **Update existing templates** to use the new AJAX methods
2. **Monitor performance** using the console commands
3. **Optimize slow operations** identified by the performance monitor
4. **Consider implementing WebSocket** for real-time features to reduce AJAX polling
