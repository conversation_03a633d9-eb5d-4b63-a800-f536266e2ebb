/**
 * Recurring Mentions Wizard - Step 3: Preview and Finalization
 * Handles period settings, preview updates, and final submission
 */

// Global variables for this step
let previewUpdateTimer = null;
let sessionMonitorTimer = null;
let isSubmitting = false;

document.addEventListener('DOMContentLoaded', function() {
    initializeStep3();
});

/**
 * Initialize Step 3 functionality
 */
function initializeStep3() {
    console.log('Initializing Step 3...');

    // Test DOM elements immediately
    testDOMElements();

    // Initialize form elements
    initializeFormElements();

    // Add event listeners
    addStep3EventListeners();

    // Initialize preview
    initializePreview();

    // Start session monitoring
    startSessionMonitoring();

    // Set up auto-save
    setupAutoSave();

    console.log('Step 3 initialization complete');
}

/**
 * Test if DOM elements exist
 */
function testDOMElements() {
    console.log('Testing DOM elements...');

    const elements = {
        'mentions-per-week': document.getElementById('mentions-per-week'),
        'total-mentions': document.getElementById('total-mentions'),
        'total-days': document.getElementById('total-days'),
        'cost-estimate': document.getElementById('cost-estimate'),
        'schedule-preview-container': document.getElementById('schedule-preview-container'),
        'step3-form': document.getElementById('step3-form')
    };

    for (const [id, element] of Object.entries(elements)) {
        console.log(`Element ${id}:`, !!element, element?.textContent?.trim());
    }
}

/**
 * Initialize form elements with proper defaults
 */
function initializeFormElements() {
    const startDateInput = document.querySelector('input[name="start_date"]');
    const weeksInput = document.querySelector('input[name="weeks"]');
    
    // Set minimum date for start date
    if (startDateInput && !startDateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        startDateInput.min = today;
        startDateInput.value = today;
    }
    
    // Ensure weeks has a default value
    if (weeksInput && !weeksInput.value) {
        weeksInput.value = 1;
    }
}

/**
 * Add event listeners for Step 3
 */
function addStep3EventListeners() {
    // Date and duration change handlers
    const startDateInput = document.querySelector('input[name="start_date"]');
    const weeksInput = document.querySelector('input[name="weeks"]');
    const endDateInput = document.querySelector('input[name="end_date"]');
    
    if (startDateInput) {
        startDateInput.addEventListener('change', debouncedUpdatePreview);
    }
    
    if (weeksInput) {
        weeksInput.addEventListener('input', debouncedUpdatePreview);
        weeksInput.addEventListener('change', debouncedUpdatePreview);
    }
    
    if (endDateInput) {
        endDateInput.addEventListener('change', updateFromEndDate);
        endDateInput.addEventListener('input', updateFromEndDate);  // Also trigger on input
    }
    
    // Form submission handler with minimal interference
    const form = document.getElementById('step3-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            // Simple validation check
            if (!validateStep3Form()) {
                e.preventDefault();
                return false;
            }

            // Simple double-click prevention
            if (isSubmitting) {
                e.preventDefault();
                console.log('Form submission blocked - already submitting');
                return false;
            }

            // Mark as submitting and update button
            isSubmitting = true;
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Creating...';
            }

            console.log('Form submission allowed - processing...');
            return true;
        });
    }
}

/**
 * Initialize preview container and load initial preview
 */
function initializePreview() {
    console.log('Initializing Step 3 preview...');

    const startDateInput = document.querySelector('input[name="start_date"]');
    const weeksInput = document.querySelector('input[name="weeks"]');

    console.log('Start date input:', startDateInput?.value);
    console.log('Weeks input:', weeksInput?.value);

    // Initialize date range display
    updateDateRangeDisplay();

    if (startDateInput && weeksInput && startDateInput.value && weeksInput.value) {
        console.log('Triggering initial preview update...');
        // Trigger initial preview update
        setTimeout(function() {
            updatePreview();
        }, 500); // Small delay to ensure DOM is ready
    } else {
        console.log('Using default values for initial statistics');
        // Initialize with default values if not set
        const weeks = weeksInput ? parseInt(weeksInput.value) || 1 : 1;
        updateStatistics(weeks);
    }
}

/**
 * Debounced update preview function
 */
function debouncedUpdatePreview() {
    // Update date range display immediately (no debounce needed for this)
    updateDateRangeDisplay();

    // Clear existing timer
    if (previewUpdateTimer) {
        clearTimeout(previewUpdateTimer);
    }

    // Set new timer
    previewUpdateTimer = setTimeout(function() {
        updatePreview();
    }, 500);
}

/**
 * Update preview via AJAX
 */
function updatePreview() {
    const form = document.getElementById('step3-form');
    if (!form) {
        console.error('Step 3 form not found');
        return;
    }

    const formData = new FormData(form);
    formData.append('preview_only', 'true');

    // Log form data for debugging
    console.log('Updating preview with form data:');
    for (let [key, value] of formData.entries()) {
        console.log(`  ${key}: ${value}`);
    }

    // Show loading state
    const previewContainer = document.getElementById('schedule-preview-container');
    if (previewContainer) {
        previewContainer.innerHTML = `
            <div class="text-center py-8">
                <div class="text-blue-500 text-lg mb-2">
                    <i class="fa-solid fa-spinner fa-spin"></i>
                </div>
                <p class="text-gray-500">Updating preview...</p>
            </div>
        `;
    }

    // Make AJAX request
    console.log('Making AJAX request to:', window.location.href);
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: formData
    })
    .then(response => {
        console.log('AJAX response status:', response.status);
        console.log('AJAX response headers:', response.headers);
        return response.json();
    })
    .then(data => {
        console.log('AJAX response data:', data);

        if (data.success) {
            // Update preview HTML
            if (previewContainer && data.preview_html) {
                console.log('Updating preview HTML, length:', data.preview_html.length);
                previewContainer.innerHTML = data.preview_html;
                console.log('Preview HTML updated successfully');
            } else {
                console.warn('No preview container or preview HTML in response');
            }

            // Update statistics
            if (data.statistics) {
                console.log('Updating statistics:', data.statistics);
                console.log('About to call updateStatisticsDisplay...');
                updateStatisticsDisplay(data.statistics);
                console.log('updateStatisticsDisplay completed');
            } else {
                console.warn('No statistics in AJAX response');
            }
        } else {
            console.error('AJAX request failed:', data.errors);

            // Check if we need to redirect
            if (data.redirect) {
                console.log('Redirecting to:', data.redirect);
                window.location.href = data.redirect;
                return;
            }

            // Show error
            if (previewContainer) {
                previewContainer.innerHTML = `
                    <div class="text-center py-8">
                        <div class="text-red-500 text-lg mb-2">
                            <i class="fa-solid fa-exclamation-triangle"></i>
                        </div>
                        <p class="text-red-600">Error updating preview</p>
                        <p class="text-sm text-gray-500 mt-1">${data.errors ? data.errors.join(', ') : 'Unknown error'}</p>
                        <div class="mt-4">
                            <a href="/mentions/recurring/wizard/schedule/" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
                                <i class="fa-solid fa-arrow-left mr-1"></i>
                                Back to Step 2
                            </a>
                        </div>
                    </div>
                `;
            }
        }
    })
    .catch(error => {
        console.error('Error updating preview:', error);
        if (previewContainer) {
            previewContainer.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-red-500 text-lg mb-2">
                        <i class="fa-solid fa-exclamation-triangle"></i>
                    </div>
                    <p class="text-red-600">Network error updating preview</p>
                    <p class="text-sm text-gray-500 mt-1">Check console for details</p>
                </div>
            `;
        }
    });
}

/**
 * Update statistics display
 */
function updateStatisticsDisplay(statistics) {
    console.log('Updating statistics display with:', statistics);

    // Update mentions per week (ensure it's a whole number)
    const mentionsPerWeekElement = document.getElementById('mentions-per-week');
    if (mentionsPerWeekElement) {
        const roundedMentions = Math.round(statistics.mentions_per_week || 0);
        console.log('Updating mentions per week from', mentionsPerWeekElement.textContent, 'to', roundedMentions);
        mentionsPerWeekElement.textContent = roundedMentions;
    } else {
        console.error('Mentions per week element not found');
    }

    // Update total mentions
    const totalMentionsElement = document.getElementById('total-mentions');
    if (totalMentionsElement) {
        console.log('Updating total mentions from', totalMentionsElement.textContent, 'to', statistics.estimated_total_mentions || 0);
        totalMentionsElement.textContent = statistics.estimated_total_mentions || 0;
    } else {
        console.error('Total mentions element not found');
    }

    // Update duration text
    const durationElement = document.getElementById('duration-text');
    if (durationElement) {
        console.log('Updating duration from', durationElement.textContent, 'to', statistics.duration_text || '0 days');
        durationElement.textContent = statistics.duration_text || '0 days';
    } else {
        console.error('Duration text element not found');
    }

    // Update preview status
    const previewStatusElement = document.getElementById('preview-status');
    if (previewStatusElement && statistics.duration_text) {
        let statusText = `Campaign will run for ${statistics.duration_text}`;
        previewStatusElement.textContent = statusText;
    }

    // Update date range display
    updateDateRangeDisplay();

    // Update cost estimate
    const costElement = document.getElementById('cost-estimate');
    if (costElement && statistics.cost_estimate) {
        const newCost = `$${statistics.cost_estimate.toFixed(2)}`;
        console.log('Updating cost estimate from', costElement.textContent, 'to', newCost);
        costElement.textContent = newCost;
        costElement.className = 'text-3xl font-bold text-orange-600';
    } else if (costElement && !statistics.cost_estimate) {
        console.log('Cost estimate not available in statistics, showing placeholder');
        costElement.textContent = '--';
        costElement.className = 'text-3xl font-bold text-gray-400';
    } else {
        console.error('Cost element not found');
    }
}

/**
 * Update date range display in the preview section
 */
function updateDateRangeDisplay() {
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');
    const dateRangeElement = document.getElementById('date-range');

    if (dateRangeElement && startDateInput && endDateInput && startDateInput.value && endDateInput.value) {
        try {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);

            // Format dates as "Mon DD, YYYY"
            const options = { month: 'short', day: 'numeric', year: 'numeric' };
            const startFormatted = startDate.toLocaleDateString('en-US', options);
            const endFormatted = endDate.toLocaleDateString('en-US', options);

            dateRangeElement.textContent = `${startFormatted} - ${endFormatted}`;
            console.log('Updated date range display:', dateRangeElement.textContent);
        } catch (error) {
            console.warn('Error formatting dates for display:', error);
            dateRangeElement.textContent = '';
        }
    } else if (dateRangeElement) {
        dateRangeElement.textContent = '';
    }
}

/**
 * Update statistics with given weeks count
 */
function updateStatistics(weeks) {
    // This is a simplified version for when we don't have full preview data
    const mentionsPerWeekElement = document.getElementById('mentions-per-week');
    const totalMentionsElement = document.getElementById('total-mentions');
    const totalWeeksElement = document.getElementById('total-weeks');

    if (mentionsPerWeekElement) {
        const mentionsPerWeek = parseInt(mentionsPerWeekElement.textContent) || 0;

        if (totalMentionsElement) {
            totalMentionsElement.textContent = mentionsPerWeek * weeks;
        }

        if (totalWeeksElement) {
            totalWeeksElement.textContent = weeks;
        }
    }
}

/**
 * Update preview when end date changes
 */
function updateFromEndDate() {
    // Update date range display immediately
    updateDateRangeDisplay();
    // Trigger immediate preview update when end date changes
    console.log('End date changed, triggering immediate preview update...');
    updatePreview();
}

/**
 * Set duration in days and update end date
 */
function setDuration(days) {
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');

    if (startDateInput && endDateInput && startDateInput.value) {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + days - 1); // -1 because we include both start and end dates

        // Format as YYYY-MM-DD
        const endDateStr = endDate.toISOString().split('T')[0];
        endDateInput.value = endDateStr;

        // Update date range display immediately
        updateDateRangeDisplay();

        // Trigger immediate update for quick buttons
        console.log('Quick duration button clicked, triggering immediate preview update...');
        updatePreview();
    }
}

/**
 * Validate step 3 form
 */
function validateStep3Form() {
    const startDate = document.querySelector('input[name="start_date"]').value;
    const endDate = document.querySelector('input[name="end_date"]').value;

    if (!startDate) {
        alert('Please select a start date.');
        return false;
    }

    if (!endDate) {
        alert('Please select an end date.');
        return false;
    }

    // Check if start date is not in the past
    const today = new Date();
    const selectedStartDate = new Date(startDate);
    const selectedEndDate = new Date(endDate);
    today.setHours(0, 0, 0, 0);
    selectedStartDate.setHours(0, 0, 0, 0);
    selectedEndDate.setHours(0, 0, 0, 0);

    if (selectedStartDate < today) {
        alert('Start date cannot be in the past.');
        return false;
    }

    if (selectedEndDate <= selectedStartDate) {
        alert('End date must be after start date.');
        return false;
    }

    return true;
}

/**
 * Validate and preview (for manual preview button)
 */
function validateAndPreview() {
    if (validateStep3Form()) {
        updatePreview();
    }
}

/**
 * Confirm creation before submission
 */
function confirmCreation() {
    // Prevent double-click during confirmation
    if (isSubmitting) {
        return false;
    }

    const totalMentionsElement = document.getElementById('total-mentions');
    const startDateInput = document.querySelector('input[name="start_date"]');
    const endDateInput = document.querySelector('input[name="end_date"]');

    const totalMentions = totalMentionsElement ? totalMentionsElement.textContent : '0';
    const startDate = startDateInput ? startDateInput.value : '';
    const endDate = endDateInput ? endDateInput.value : '';

    return confirm(
        `Are you sure you want to create this recurring mention?\n\n` +
        `This will generate ${totalMentions} individual mentions from ${startDate} to ${endDate}.\n\n` +
        `Click OK to proceed or Cancel to review.`
    );
}

/**
 * Start session monitoring
 */
function startSessionMonitoring() {
    // Check session every 5 minutes
    sessionMonitorTimer = setInterval(checkSession, 5 * 60 * 1000);
}

/**
 * Check if session is still active
 */
function checkSession() {
    // This would typically make an AJAX call to check session status
    // For now, we'll just show a visual indicator
    const sessionStatus = document.getElementById('session-status');
    if (sessionStatus) {
        sessionStatus.style.opacity = '1';
        setTimeout(() => {
            sessionStatus.style.opacity = '0';
        }, 2000);
    }
}

/**
 * Setup auto-save functionality
 */
function setupAutoSave() {
    const form = document.getElementById('step3-form');
    if (!form) return;
    
    const inputs = form.querySelectorAll('input[type="date"], input[type="number"]');
    
    inputs.forEach(input => {
        input.addEventListener('change', saveFormData);
    });
}

/**
 * Save form data to session storage
 */
function saveFormData() {
    const form = document.getElementById('step3-form');
    if (!form) return;
    
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    sessionStorage.setItem('wizard_step3_data', JSON.stringify(data));
}

/**
 * Load form data from session storage
 */
function loadFormData() {
    const savedData = sessionStorage.getItem('wizard_step3_data');
    if (!savedData) return;
    
    try {
        const data = JSON.parse(savedData);
        
        for (let [key, value] of Object.entries(data)) {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                element.value = value;
            }
        }
    } catch (e) {
        console.warn('Failed to load saved form data:', e);
    }
}

/**
 * Debug function for Step 3
 */
function debugStep3() {
    console.log('=== STEP 3 DEBUG ===');

    // Check form elements
    const form = document.getElementById('step3-form');
    console.log('Form found:', !!form);

    if (form) {
        const formData = new FormData(form);
        console.log('Form data:');
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }
    }

    // Check statistics elements
    console.log('Statistics elements:');
    const mentionsPerWeek = document.getElementById('mentions-per-week');
    const totalMentions = document.getElementById('total-mentions');
    const totalWeeks = document.getElementById('total-weeks');
    const costEstimate = document.getElementById('cost-estimate');

    console.log('  Mentions per week element:', !!mentionsPerWeek, mentionsPerWeek?.textContent);
    console.log('  Total mentions element:', !!totalMentions, totalMentions?.textContent);
    console.log('  Total weeks element:', !!totalWeeks, totalWeeks?.textContent);
    console.log('  Cost estimate element:', !!costEstimate, costEstimate?.textContent);

    // Check preview container
    const previewContainer = document.getElementById('schedule-preview-container');
    console.log('Preview container found:', !!previewContainer);
    if (previewContainer) {
        console.log('Preview container content length:', previewContainer.innerHTML.length);
    }

    // Test manual update
    console.log('Testing manual preview update...');
    updatePreview();
}

// Make debug function globally available
window.debugStep3 = debugStep3;

// Clean up timers when page unloads
window.addEventListener('beforeunload', function() {
    if (previewUpdateTimer) {
        clearTimeout(previewUpdateTimer);
    }
    if (sessionMonitorTimer) {
        clearInterval(sessionMonitorTimer);
    }
});
