/**
 * Enhanced Live Show Interface with Chat-style Mentions and Advanced Drag & Drop
 * Features:
 * - Chat-style alignment based on client industry
 * - Enhanced drag and drop with visual feedback
 * - Industry-based color coding
 * - Smooth animations and transitions
 */

// Global variables - IE11 compatible
// Check if variables are already defined in main template, if not define them
if (typeof currentSelectedMention === 'undefined') {
    var currentSelectedMention = null;
}
if (typeof keyboardNavActive === 'undefined') {
    var keyboardNavActive = false;
}
if (typeof draggedElement === 'undefined') {
    var draggedElement = null;
}
if (typeof draggedOverElement === 'undefined') {
    var draggedOverElement = null;
}
if (typeof isLive === 'undefined') {
    var isLive = false;
}
if (typeof showEndedEarly === 'undefined') {
    var showEndedEarly = false;
}

// Industry alignment mapping - IE11 compatible
var INDUSTRY_ALIGNMENT = {
    'technology': 'left',
    'healthcare': 'left', 
    'education': 'left',
    'banking_finance': 'left',
    'entertainment': 'right',
    'media_advertising': 'right',
    'retail': 'right',
    'fashion_retail': 'right',
    'government': 'center',
    'non_profit': 'center',
    'legal_services': 'center',
    'other': 'center'
};

// Industry icons mapping - IE11 compatible
var INDUSTRY_ICONS = {
    'technology': '🔧',
    'healthcare': '🏥',
    'education': '🎓',
    'entertainment': '🎭',
    'retail': '🛍️',
    'automotive': '🚗',
    'food_beverage': '🍽️',
    'government': '🏛️',
    'banking_finance': '🏦',
    'construction': '🏗️',
    'media_advertising': '📺',
    'fashion_retail': '👗',
    'hospitality_tourism': '🏨',
    'insurance': '🛡️',
    'legal_services': '⚖️',
    'manufacturing': '🏭',
    'non_profit': '❤️',
    'real_estate': '🏠',
    'sports_recreation': '⚽',
    'telecommunications': '📡',
    'transportation': '🚛',
    'utilities': '⚡',
    'other': '📢'
};

// Initialize enhanced live show features
function initializeEnhancedLiveShow() {
    console.log('Initializing Enhanced Live Show Interface...');

    try {
        // Initialize drag and drop
        initializeEnhancedDragAndDrop();

        // Initialize mention animations
        initializeMentionAnimations();

        // Initialize industry indicators
        initializeIndustryIndicators();

        // Initialize keyboard shortcuts
        initializeKeyboardShortcuts();

        console.log('Enhanced Live Show Interface initialized successfully');
    } catch (error) {
        console.error('Error initializing Enhanced Live Show Interface:', error);
        // Fallback: continue without enhanced features
    }
}

// Enhanced drag and drop functionality
function initializeEnhancedDragAndDrop() {
    try {
        if (!isLive) return;

        const mentionsList = document.getElementById('mentions-list');
        if (!mentionsList) return;

        // Add draggable attribute to unread mentions during live shows
        const draggableMentions = document.querySelectorAll('.draggable-mention');

        // IE11 compatible forEach
        for (var i = 0; i < draggableMentions.length; i++) {
            var mention = draggableMentions[i];
            // Set draggable attribute
            mention.setAttribute('draggable', 'true');

            // Add enhanced drag events
            mention.addEventListener('dragstart', handleEnhancedDragStart);
            mention.addEventListener('dragover', handleEnhancedDragOver);
            mention.addEventListener('dragenter', handleEnhancedDragEnter);
            mention.addEventListener('dragleave', handleEnhancedDragLeave);
            mention.addEventListener('drop', handleEnhancedDrop);
            mention.addEventListener('dragend', handleEnhancedDragEnd);

            // Add touch support for mobile only if touch is supported (IE11 compatible)
            if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
                mention.addEventListener('touchstart', handleTouchStart, false);
                mention.addEventListener('touchmove', handleTouchMove, false);
                mention.addEventListener('touchend', handleTouchEnd, false);
            }
        }
    } catch (error) {
        console.error('Error initializing enhanced drag and drop:', error);
    }
}

// Enhanced drag start with visual feedback
function handleEnhancedDragStart(e) {
    draggedElement = this;
    this.classList.add('dragging');
    
    // Create drag image with enhanced styling
    const dragImage = this.cloneNode(true);
    dragImage.style.transform = 'rotate(5deg) scale(1.05)';
    dragImage.style.opacity = '0.8';
    document.body.appendChild(dragImage);
    
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', this.outerHTML);
    
    // Add visual feedback to other mentions - IE11 compatible
    var allMentions = document.querySelectorAll('.draggable-mention');
    for (var i = 0; i < allMentions.length; i++) {
        if (allMentions[i] !== this) {
            allMentions[i].classList.add('drop-zone');
        }
    }
    
    setTimeout(() => {
        if (dragImage.parentNode) {
            document.body.removeChild(dragImage);
        }
    }, 0);
}

function handleEnhancedDragOver(e) {
    if (e.preventDefault) {
        e.preventDefault();
    }
    e.dataTransfer.dropEffect = 'move';
    return false;
}

function handleEnhancedDragEnter(e) {
    if (this !== draggedElement) {
        this.classList.add('drag-over', 'active');
        draggedOverElement = this;
        
        // Add pulsing animation
        this.style.animation = 'pulse-border 0.5s infinite';
    }
}

function handleEnhancedDragLeave(e) {
    this.classList.remove('drag-over', 'active');
    this.style.animation = '';
}

function handleEnhancedDrop(e) {
    if (e.stopPropagation) {
        e.stopPropagation();
    }
    
    if (draggedElement !== this) {
        const mentionsList = document.getElementById('mentions-list');
        const allMentions = Array.from(mentionsList.children);
        
        // Find wrapper elements (for chat-style layout)
        const draggedWrapper = draggedElement.closest('.mention-wrapper') || draggedElement;
        const targetWrapper = this.closest('.mention-wrapper') || this;
        
        const draggedIndex = allMentions.indexOf(draggedWrapper);
        const targetIndex = allMentions.indexOf(targetWrapper);
        
        // Animate the reorder
        if (draggedIndex < targetIndex) {
            mentionsList.insertBefore(draggedWrapper, targetWrapper.nextSibling);
        } else {
            mentionsList.insertBefore(draggedWrapper, targetWrapper);
        }
        
        // Add reorder animation
        draggedWrapper.style.animation = 'slideIn 0.5s ease-out';
        setTimeout(() => {
            draggedWrapper.style.animation = '';
        }, 500);
        
        // Update server
        updateMentionOrder();
        
        // Show success notification
        showNotification('Mention reordered successfully!', 'success');
    }
    
    this.classList.remove('drag-over', 'active');
    this.style.animation = '';
    return false;
}

function handleEnhancedDragEnd(e) {
    this.classList.remove('dragging');
    
    // Clean up all visual feedback - IE11 compatible
    var allMentions = document.querySelectorAll('.draggable-mention');
    for (var i = 0; i < allMentions.length; i++) {
        allMentions[i].classList.remove('drag-over', 'drop-zone', 'active');
        allMentions[i].style.animation = '';
    }
    
    draggedElement = null;
    draggedOverElement = null;
}

// Touch support for mobile drag and drop - IE11 compatible
var touchStartY = 0;
var touchElement = null;

function handleTouchStart(e) {
    // Only handle touch if not already handling mouse events
    if (draggedElement) return;

    touchStartY = e.touches[0].clientY;
    touchElement = this;
    this.classList.add('touch-dragging');
}

function handleTouchMove(e) {
    if (!touchElement || draggedElement) return;

    e.preventDefault();
    const touchY = e.touches[0].clientY;
    const deltaY = touchY - touchStartY;

    // Visual feedback for touch drag
    touchElement.style.transform = 'translateY(' + deltaY + 'px) rotate(2deg)';
    touchElement.style.opacity = '0.8';
}

function handleTouchEnd(e) {
    if (!touchElement || draggedElement) return;

    touchElement.classList.remove('touch-dragging');
    touchElement.style.transform = '';
    touchElement.style.opacity = '';
    touchElement = null;
}

// Initialize mention animations
function initializeMentionAnimations() {
    // Add entrance animations to new mentions - IE11 compatible
    var mentions = document.querySelectorAll('.mention-card');
    for (var i = 0; i < mentions.length; i++) {
        var mention = mentions[i];
        mention.style.animationDelay = (i * 0.1) + 's';
        mention.classList.add('new');

        // Remove animation class after animation completes
        (function(mentionElement, index) {
            setTimeout(function() {
                mentionElement.classList.remove('new');
            }, 500 + (index * 100));
        })(mention, i);
    }
}

// Initialize industry indicators - IE11 compatible
function initializeIndustryIndicators() {
    var mentions = document.querySelectorAll('.mention-card[data-industry]');

    for (var i = 0; i < mentions.length; i++) {
        var mention = mentions[i];
        var industry = mention.getAttribute('data-industry');
        var indicator = mention.querySelector('.industry-indicator');

        if (indicator && INDUSTRY_ICONS[industry]) {
            indicator.textContent = INDUSTRY_ICONS[industry];
            indicator.title = 'Industry: ' + industry.replace('_', ' ').toUpperCase();
        }
    }
}

// Enhanced keyboard shortcuts
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        const modalOpen = !document.getElementById('mentionModal').classList.contains('hidden');
        
        // Don't handle shortcuts if modal is open or user is typing
        if (modalOpen || e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }
        
        switch(e.key) {
            case 'ArrowUp':
                e.preventDefault();
                navigateMentions('up');
                break;
            case 'ArrowDown':
                e.preventDefault();
                navigateMentions('down');
                break;
            case 'Enter':
                e.preventDefault();
                if (currentSelectedMention) {
                    const viewButton = currentSelectedMention.querySelector('button[onclick*="viewMention"]');
                    if (viewButton) viewButton.click();
                }
                break;
            case ' ':
                e.preventDefault();
                if (currentSelectedMention) {
                    const markButton = currentSelectedMention.querySelector('button[onclick*="markComplete"]');
                    if (markButton) markButton.click();
                }
                break;
            case 'n':
            case 'N':
                e.preventDefault();
                selectNextMention();
                break;
            case 'r':
            case 'R':
                e.preventDefault();
                location.reload();
                break;
        }
    });
}

// Update mention order on server - IE11 compatible
function updateMentionOrder() {
    var mentionsList = document.getElementById('mentions-list');
    var mentions = Array.from(mentionsList.querySelectorAll('.mention-card[data-reading-id]'));
    var mentionOrder = [];

    // IE11 compatible map
    for (var i = 0; i < mentions.length; i++) {
        mentionOrder.push(parseInt(mentions[i].getAttribute('data-reading-id')));
    }

    // Use the existing URL pattern from the original template
    var reorderUrl = window.location.origin + '/live-show/reorder-mentions/';

    fetch(reorderUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            mention_order: mentionOrder
        })
    })
    .then(function(response) {
        return response.json();
    })
    .then(function(data) {
        if (!data.success) {
            showNotification('Failed to save mention order: ' + (data.error || 'Unknown error'), 'error');
            // Reload page to restore original order
            setTimeout(function() {
                location.reload();
            }, 2000);
        }
    })
    .catch(function(error) {
        console.error('Error updating mention order:', error);
        showNotification('Error saving mention order', 'error');
    });
}

// Navigation functions (integrate with existing)
function selectNextMention() {
    const mentions = document.querySelectorAll('.mention-card:not(.completed)');
    if (mentions.length > 0) {
        // Clear previous selection
        if (currentSelectedMention) {
            currentSelectedMention.classList.remove('ring-2', 'ring-blue-500');
        }

        // Select first unread mention
        currentSelectedMention = mentions[0];
        currentSelectedMention.classList.add('ring-2', 'ring-blue-500');
        currentSelectedMention.scrollIntoView({ behavior: 'smooth', block: 'center' });
        keyboardNavActive = true;
    }
}

function navigateMentions(direction) {
    const mentions = Array.from(document.querySelectorAll('.mention-card:not(.completed)'));
    if (mentions.length === 0) return;

    if (!currentSelectedMention) {
        selectNextMention();
        return;
    }

    const currentIndex = mentions.indexOf(currentSelectedMention);
    let newIndex;

    if (direction === 'up') {
        newIndex = currentIndex > 0 ? currentIndex - 1 : mentions.length - 1;
    } else {
        newIndex = currentIndex < mentions.length - 1 ? currentIndex + 1 : 0;
    }

    // Clear previous selection
    currentSelectedMention.classList.remove('ring-2', 'ring-blue-500');

    // Select new mention
    currentSelectedMention = mentions[newIndex];
    currentSelectedMention.classList.add('ring-2', 'ring-blue-500');
    currentSelectedMention.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// Enhanced notification system
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    let bgClass = 'bg-blue-100 text-blue-800 border border-blue-200';
    let iconClass = 'info';
    
    switch(type) {
        case 'success':
            bgClass = 'bg-green-100 text-green-800 border border-green-200';
            iconClass = 'check';
            break;
        case 'error':
            bgClass = 'bg-red-100 text-red-800 border border-red-200';
            iconClass = 'exclamation-triangle';
            break;
        case 'warning':
            bgClass = 'bg-yellow-100 text-yellow-800 border border-yellow-200';
            iconClass = 'exclamation';
            break;
    }
    
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${bgClass}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fa-solid fa-${iconClass} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-500 hover:text-gray-700">
                <i class="fa-solid fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Slide in animation
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto-remove
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.transform = 'translateX(full)';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }, duration);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Get Django variables
    isLive = window.isLive || false;
    showEndedEarly = window.showEndedEarly || false;

    // Initialize enhanced features with a small delay to ensure main template loads first
    setTimeout(function() {
        initializeEnhancedLiveShow();
    }, 100);
});
