/**
 * Recurring Mentions Wizard JavaScript
 * Handles the step-by-step wizard for creating recurring mentions
 */

// Global variables
let showsData = {};
let csrfToken = '';

/**
 * Initialize the recurring wizard
 */
function initializeRecurringWizard(shows, csrf) {
    showsData = shows;
    csrfToken = csrf;

    // Clear any previous validation state first
    clearAllValidationState();

    // Add event listeners
    addWizardEventListeners();

    // Initialize based on current step
    initializeCurrentStep();
}

/**
 * Add event listeners for wizard functionality
 */
function addWizardEventListeners() {
    // Show select handlers
    document.querySelectorAll('.show-select').forEach(select => {
        select.addEventListener('change', function() {
            validateShowTime(this);
            // Trigger statistics update for step 3
            if (document.getElementById('step3-form')) {
                debouncedUpdatePreview();
            }
        });
    });

    // Time select handlers
    document.querySelectorAll('.time-select').forEach(select => {
        select.addEventListener('change', function() {
            validateTimeSelection(this);
            // Trigger statistics update for step 3
            if (document.getElementById('step3-form')) {
                debouncedUpdatePreview();
            }
        });
    });

    // Time input handlers (backward compatibility)
    document.querySelectorAll('.time-input').forEach(input => {
        input.addEventListener('change', function() {
            validateTimeRange(this);
            // Trigger statistics update for step 3
            if (document.getElementById('step3-form')) {
                debouncedUpdatePreview();
            }
        });
    });
}

/**
 * Initialize current step specific functionality
 */
function initializeCurrentStep() {
    // Clear any previous validation state on step 2
    if (document.querySelector('.schedule-row')) {
        clearAllValidationState();
        updateScheduleSummary();
    }

    // Set minimum date for step 3
    const startDateInput = document.querySelector('input[name="start_date"]');
    if (startDateInput && !startDateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        startDateInput.min = today;
        startDateInput.value = today;
    }
    
    // Add enhanced preview update listeners for step 3
    if (document.getElementById('step3-form')) {
        const previewInputs = ['input[name="start_date"]', 'input[name="weeks"]', 'input[name="end_date"]'];
        previewInputs.forEach(selector => {
            const input = document.querySelector(selector);
            if (input) {
                // Add multiple event types for better responsiveness
                ['change', 'input', 'blur'].forEach(eventType => {
                    input.addEventListener(eventType, function() {
                        debouncedUpdatePreview();
                    });
                });
            }
        });

        // Add automatic session monitoring for step 3
        startSessionMonitoring();

        // Check for changes from previous steps on page load
        checkForPreviousStepChanges();

        // Initial preview update
        setTimeout(() => {
            updatePreview();
        }, 100);

        initializePreviewContainer();

        // Initialize statistics and preview on page load
        const startDateInput = document.querySelector('input[name="start_date"]');
        const weeksInput = document.querySelector('input[name="weeks"]');



        if (startDateInput && weeksInput && startDateInput.value && weeksInput.value) {
            // Trigger initial preview update
            setTimeout(function() {
                updatePreview();
            }, 500); // Small delay to ensure DOM is ready
        } else {
            // Initialize with default values if not set
            const weeks = weeksInput ? parseInt(weeksInput.value) || 4 : 4;
            updateStatistics(weeks);
        }
    }

    // Add listeners for step 1 changes that affect statistics
    if (document.querySelector('form[action*="step/1"]')) {
        // Listen for client changes (affects cost estimates)
        const clientSelect = document.querySelector('select[name="client_id"]');
        if (clientSelect) {
            clientSelect.addEventListener('change', function() {
                // Store client change for step 3 updates
                localStorage.setItem('wizard_client_changed', Date.now().toString());
            });
        }

        // Listen for day selection changes (affects mentions per week)
        document.querySelectorAll('input[name="selected_days"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Store day selection change for step 3 updates
                localStorage.setItem('wizard_days_changed', Date.now().toString());
            });
        });
    }

    // Add listeners for step 2 changes that affect statistics
    if (document.querySelector('form[action*="step/2"]')) {
        // Listen for show/time changes (affects mentions per week)
        document.addEventListener('change', function(event) {
            if (event.target.classList.contains('show-select') ||
                event.target.classList.contains('time-select')) {
                // Store schedule change for step 3 updates
                localStorage.setItem('wizard_schedule_changed', Date.now().toString());
            }
        });

        // Listen for add/remove schedule row changes
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('add-time-slot') ||
                event.target.classList.contains('remove-schedule-row')) {
                // Store schedule change for step 3 updates
                localStorage.setItem('wizard_schedule_changed', Date.now().toString());
            }
        });
    }
}

/**
 * Add a new schedule row for a specific day
 */
function addScheduleRow(dayNumber) {
    const container = document.getElementById(`day-${dayNumber}-schedules`);
    const firstRow = container.querySelector('.schedule-row');
    const newRow = firstRow.cloneNode(true);

    // Clear the values and reset validation
    newRow.querySelectorAll('select, input').forEach(input => {
        input.value = '';
        input.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    });

    // Reset time select to default state
    const timeSelect = newRow.querySelector('.time-select');
    if (timeSelect) {
        timeSelect.innerHTML = '<option value="">Select a show first</option>';
        timeSelect.disabled = true;
    }

    // Clear duplicate highlighting
    newRow.classList.remove('duplicate-highlight', 'conflict-highlight');

    // Clear validation messages
    newRow.querySelectorAll('.time-validation-message').forEach(msg => {
        msg.style.display = 'none';
        msg.textContent = '';
    });

    // Clear conflict indicators
    newRow.querySelectorAll('.conflict-indicator').forEach(indicator => {
        indicator.style.display = 'none';
        indicator.textContent = '';
    });

    // Add some spacing
    newRow.classList.add('mt-3');

    // Update event handlers
    const showSelect = newRow.querySelector('.show-select');

    if (showSelect) {
        showSelect.addEventListener('change', function() { validateShowTime(this); });
    }
    if (timeSelect) {
        timeSelect.addEventListener('change', function() { validateTimeSelection(this); });
    }

    // Append to container
    container.appendChild(newRow);
    
    // Update summary
    updateScheduleSummary();
}

/**
 * Remove a schedule row
 */
function removeScheduleRow(button) {
    const row = button.closest('.schedule-row');
    const container = row.closest('[id*="schedules"]');
    
    // Don't remove if it's the only row
    if (container.querySelectorAll('.schedule-row').length > 1) {
        row.remove();
        updateScheduleSummary();
    }
}

/**
 * Generate time slots for a given time range
 */
function generateTimeSlots(startTime, endTime) {
    const slots = [];
    const start = startTime.split(':').map(Number);
    const end = endTime.split(':').map(Number);

    let startMinutes = start[0] * 60 + start[1];
    let endMinutes = end[0] * 60 + end[1];

    // Handle shows that cross midnight
    if (endMinutes < startMinutes) {
        endMinutes += 24 * 60; // Add 24 hours
    }

    // Generate slots every 30 seconds (0.5 minutes)
    for (let minutes = startMinutes; minutes <= endMinutes; minutes += 0.5) {
        const actualMinutes = minutes % (24 * 60); // Handle overflow past midnight
        const hours = Math.floor(actualMinutes / 60);
        const mins = Math.floor(actualMinutes % 60);
        const secs = (actualMinutes % 1) * 60;

        const timeStr = `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        const displayStr = secs === 0 ? `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}` : timeStr;

        slots.push({
            value: timeStr,
            display: displayStr,
            minutes: actualMinutes
        });
    }

    return slots;
}

/**
 * Load time slots for a specific show (wizard version with API call)
 */
function loadTimeSlots(showId, dayNumber, timeSelect) {
    // Show loading state
    timeSelect.innerHTML = '<option value="">Loading time slots...</option>';
    timeSelect.disabled = true;

    // Make API call to get available time slots (this URL needs to be passed from template)
    const apiUrl = window.wizardApiUrls?.getAvailableTimeSlots || '/mentions/get-available-time-slots/';

    fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            show_id: showId,
            weekday: parseInt(dayNumber)  // dayNumber is already 0-based (Monday=0, Tuesday=1, etc.)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            populateTimeSlots(timeSelect, data.time_slots);
        } else {
            timeSelect.innerHTML = '<option value="">Error loading time slots</option>';
            console.error('Error loading time slots:', data.error);
        }
    })
    .catch(error => {
        console.error('Error loading time slots:', error);
        timeSelect.innerHTML = '<option value="">Error loading time slots</option>';
    });
}

/**
 * Load time slots for edit form (generates slots locally)
 */
function loadTimeSlotsEdit(showId, showSelectElement) {
    const show = showsData[showId];
    if (!show || !show.startTime || !show.endTime) return;

    const scheduleRow = showSelectElement.closest('.schedule-row');
    const timeSelect = scheduleRow.querySelector('.time-select');
    const currentValue = timeSelect.value;

    // Generate time slots
    const timeSlots = generateTimeSlots(show.startTime, show.endTime);

    // Clear existing options
    timeSelect.innerHTML = '';

    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Select mention time';
    timeSelect.appendChild(defaultOption);

    // Add time slot options
    timeSlots.forEach(slot => {
        const option = document.createElement('option');
        option.value = slot.value;
        option.textContent = slot.display;
        option.className = 'available-slot';

        // Restore previous selection if it matches
        if (currentValue && (currentValue === slot.value || currentValue === slot.display)) {
            option.selected = true;
        }

        timeSelect.appendChild(option);
    });

    // Enable the time select
    timeSelect.disabled = false;

    // Validate the current selection
    if (timeSelect.value) {
        validateTimeSelection(timeSelect);
    }
}

/**
 * Populate time slots from API response
 */
function populateTimeSlots(timeSelect, timeSlots) {
    // Clear existing options
    timeSelect.innerHTML = '<option value="">Select time slot</option>';

    // Add time slot options
    timeSlots.forEach(slot => {
        const option = document.createElement('option');
        option.value = slot.time;

        // Create display text with availability indicator
        let displayText = slot.time_display;
        let optionClass = '';

        if (!slot.is_available) {
            displayText += ' (Unavailable)';
            option.disabled = true;
            optionClass = 'unavailable-slot';
        } else if (slot.warnings && slot.warnings.length > 0) {
            displayText += ' ⚠️';
            optionClass = 'warning-slot';
        } else {
            displayText += ' ✓';
            optionClass = 'available-slot';
        }

        option.textContent = displayText;
        option.className = optionClass;

        // Store conflict/warning data
        if (slot.conflicts) {
            option.dataset.conflicts = JSON.stringify(slot.conflicts);
        }
        if (slot.warnings) {
            option.dataset.warnings = JSON.stringify(slot.warnings);
        }

        timeSelect.appendChild(option);
    });

    // Enable the select
    timeSelect.disabled = false;
}

/**
 * Clear all validation state from the form
 */
function clearAllValidationState() {
    // Clear all border colors
    document.querySelectorAll('.show-select, .time-select').forEach(element => {
        element.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300');
    });

    // Clear all validation messages
    document.querySelectorAll('.time-validation-message, .conflict-indicator').forEach(element => {
        element.style.display = 'none';
    });

    // Clear all highlight classes
    document.querySelectorAll('.schedule-row').forEach(row => {
        row.classList.remove('duplicate-highlight', 'conflict-highlight');
    });

    // Clear time hints
    document.querySelectorAll('.time-hint').forEach(hint => {
        hint.style.display = 'none';
    });
}

/**
 * Validate show and time selection (wizard version)
 */
function validateShowTime(selectElement) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const timeSelect = selectElement.closest('.schedule-row').querySelector('.time-select');
    const timeHint = selectElement.closest('.schedule-row').querySelector('.time-hint');
    const dayNumber = selectElement.dataset.day;

    // Clear previous validation styling for this row and remove any highlight classes
    const scheduleRow = selectElement.closest('.schedule-row');
    selectElement.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    timeSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    scheduleRow.classList.remove('duplicate-highlight', 'conflict-highlight');

    // Clear validation messages for this row
    const validationMessage = scheduleRow.querySelector('.time-validation-message');
    const conflictIndicator = scheduleRow.querySelector('.conflict-indicator');
    if (validationMessage) validationMessage.style.display = 'none';
    if (conflictIndicator) conflictIndicator.style.display = 'none';

    if (selectedOption.value && selectedOption.dataset.startTime && selectedOption.dataset.endTime) {
        // Show time range hint
        if (timeHint) {
            timeHint.textContent = `Show airs: ${selectedOption.dataset.startTime} - ${selectedOption.dataset.endTime}`;
            timeHint.style.display = 'inline';
        }

        // Load available time slots for this show (wizard version with API call)
        loadTimeSlots(selectedOption.value, dayNumber, timeSelect);
    } else {
        if (timeHint) {
            timeHint.style.display = 'none';
        }
        // Clear and disable time select
        timeSelect.innerHTML = '<option value="">Select a show first</option>';
        timeSelect.disabled = true;
        timeSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300');

        const validationMessage = selectElement.closest('.schedule-row').querySelector('.time-validation-message');
        if (validationMessage) {
            validationMessage.style.display = 'none';
        }

        const conflictIndicator = selectElement.closest('.schedule-row').querySelector('.conflict-indicator');
        if (conflictIndicator) {
            conflictIndicator.style.display = 'none';
        }
    }

    // Update summary after changes
    updateScheduleSummary();
}

/**
 * Validate show and time selection (edit form version)
 */
function validateShowTimeEdit(selectElement) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const timeSelect = selectElement.closest('.schedule-row').querySelector('.time-select');
    const timeHint = selectElement.closest('.schedule-row').querySelector('.time-hint');

    // Clear previous validation styling
    selectElement.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300');

    if (selectedOption.value && selectedOption.dataset.startTime && selectedOption.dataset.endTime) {
        // Show time range hint
        if (timeHint) {
            timeHint.textContent = `Show airs: ${selectedOption.dataset.startTime} - ${selectedOption.dataset.endTime}`;
            timeHint.style.display = 'inline';
        }

        // Load available time slots for this show (edit form version)
        loadTimeSlotsEdit(selectedOption.value, selectElement);
    } else {
        if (timeHint) {
            timeHint.style.display = 'none';
        }
        // Clear and disable time select
        timeSelect.innerHTML = '<option value="">Select a show first</option>';
        timeSelect.disabled = true;
        timeSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300');
    }
}

/**
 * Convert time string to minutes
 */
function timeToMinutes(timeStr) {
    const parts = timeStr.split(':');
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    const seconds = parts[2] ? parseInt(parts[2], 10) : 0;
    
    return hours * 60 + minutes + (seconds / 60);
}

/**
 * Validate that selected time is within show's time range
 */
function validateTimeWithinShowRange(timeSelect, showSelect) {
    const selectedTime = timeSelect.value;
    const showId = showSelect.value;
    const show = showsData[showId];

    if (!show || !selectedTime) return false;

    const scheduleRow = timeSelect.closest('.schedule-row');
    const conflictIndicator = scheduleRow.querySelector('.conflict-indicator');

    // Convert times to minutes for comparison
    const selectedMinutes = timeToMinutes(selectedTime);
    let startMinutes = timeToMinutes(show.startTime);
    let endMinutes = timeToMinutes(show.endTime);

    // Special handling for shows ending at midnight (00:00)
    // Convert 00:00 end time to 24:00 (1440 minutes) for proper comparison
    if (show.endTime === '00:00' || endMinutes === 0) {
        endMinutes = 1440; // 24:00 in minutes
    }

    let isValid = false;

    // Handle shows that cross midnight (e.g., 22:00 - 02:00)
    if (endMinutes < startMinutes) {
        // Show crosses midnight - time is valid if it's after start OR before end
        isValid = selectedMinutes >= startMinutes || selectedMinutes <= (endMinutes % 1440);
    } else {
        // Normal show or show ending at midnight - time must be between start and end
        isValid = selectedMinutes >= startMinutes && selectedMinutes <= endMinutes;
    }

    if (!isValid && conflictIndicator) {
        // Time is outside show range
        timeSelect.classList.add('border-red-300');
        showSelect.classList.add('border-red-300');
        scheduleRow.classList.add('conflict-highlight');

        const displayEndTime = show.endTime === '00:00' ? '24:00' : show.endTime;
        conflictIndicator.innerHTML = `❌ Time ${selectedTime} is outside show "${show.name}" time frame (${show.startTime} - ${displayEndTime})`;
        conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-red-600';
        conflictIndicator.style.display = 'block';

        return false;
    }

    return true;
}

/**
 * Validate time selection and check for conflicts (wizard version)
 */
function validateTimeSelection(timeSelect) {
    const scheduleRow = timeSelect.closest('.schedule-row');
    const showSelect = scheduleRow.querySelector('.show-select');
    const selectedTimeOption = timeSelect.options[timeSelect.selectedIndex];
    const validationMessage = scheduleRow.querySelector('.time-validation-message');
    const conflictIndicator = scheduleRow.querySelector('.conflict-indicator');

    // Clear previous styling
    timeSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    showSelect.classList.remove('border-red-300', 'border-green-300', 'border-yellow-300', 'border-orange-300');
    scheduleRow.classList.remove('duplicate-highlight', 'conflict-highlight');

    if (!timeSelect.value || !showSelect.value) {
        if (validationMessage) validationMessage.style.display = 'none';
        if (conflictIndicator) conflictIndicator.style.display = 'none';
        updateScheduleSummary();
        return;
    }

    const mentionTime = timeSelect.value;
    const dayNumber = timeSelect.dataset.day;
    let isValid = true;
    let errorMessage = '';

    // Check for duplicates on the same day (only if overlapping is not allowed)
    const allowOverlapping = window.organizationSettings?.allowOverlappingMentions || false;

    if (!allowOverlapping) {
        const duplicateInfo = checkForDuplicatesInWizard(scheduleRow);

        if (duplicateInfo && duplicateInfo.hasDuplicate) {
            isValid = false;
            errorMessage = `🚫 Duplicate: ${showSelect.options[showSelect.selectedIndex].text.split('(')[0].trim()} at ${mentionTime} already scheduled`;

            // Highlight the duplicate row
            if (duplicateInfo.duplicateRow) {
                duplicateInfo.duplicateRow.classList.add('duplicate-highlight');
                duplicateInfo.duplicateRow.querySelector('.show-select').classList.add('border-yellow-300');
                duplicateInfo.duplicateRow.querySelector('.time-select').classList.add('border-yellow-300');
            }
        }
    }

    // Check if the selected time slot has conflicts or warnings (from API response)
    const conflicts = selectedTimeOption.dataset.conflicts ? JSON.parse(selectedTimeOption.dataset.conflicts) : [];
    const warnings = selectedTimeOption.dataset.warnings ? JSON.parse(selectedTimeOption.dataset.warnings) : [];

    if (conflicts.length > 0) {
        isValid = false;
        errorMessage = `🚫 ${conflicts[0].message}`;
    }

    // Apply styling based on validation result
    if (isValid) {
        if (warnings.length > 0) {
            // Warning - orange borders
            timeSelect.classList.add('border-orange-300');
            showSelect.classList.add('border-orange-300');
            if (conflictIndicator) {
                conflictIndicator.innerHTML = `<i class="fa-solid fa-exclamation-triangle text-orange-500"></i> ${warnings[0].message}`;
                conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-orange-600';
                conflictIndicator.style.display = 'block';
            }
        } else {
            // Valid - green borders
            timeSelect.classList.add('border-green-300');
            showSelect.classList.add('border-green-300');
            if (conflictIndicator) {
                conflictIndicator.innerHTML = `<i class="fa-solid fa-check-circle text-green-500"></i> Time slot available`;
                conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-green-600';
                conflictIndicator.style.display = 'block';
            }
        }
        if (validationMessage) validationMessage.style.display = 'none';
    } else {
        // Invalid - red borders and error message
        timeSelect.classList.add('border-red-300');
        showSelect.classList.add('border-red-300');
        if (validationMessage) {
            validationMessage.textContent = errorMessage;
            validationMessage.className = 'time-validation-message text-xs mt-1 text-red-600 font-medium';
            validationMessage.style.display = 'block';
        }
        if (conflictIndicator) conflictIndicator.style.display = 'none';

        if (duplicateInfo && duplicateInfo.hasDuplicate) {
            scheduleRow.classList.add('duplicate-highlight');
        }
    }

    // Also validate all other rows in the same day to clear any previous duplicate highlights
    validateAllRowsInDay(timeSelect.dataset.day);

    updateScheduleSummary();
}

/**
 * Check for duplicate show/time combinations within the wizard
 */
function checkForDuplicatesInWizard(currentRow) {
    const currentShowSelect = currentRow.querySelector('.show-select');
    const currentTimeSelect = currentRow.querySelector('.time-select');

    if (!currentShowSelect.value || !currentTimeSelect.value) return { hasDuplicate: false, duplicateRow: null };

    const dayNumber = currentTimeSelect.dataset.day;
    const dayContainer = document.getElementById(`day-${dayNumber}-schedules`);
    const allRows = dayContainer ? dayContainer.querySelectorAll('.schedule-row') : document.querySelectorAll('.schedule-row');
    let duplicateRow = null;
    let hasDuplicate = false;

    // Clear previous duplicate highlights for this day
    allRows.forEach(row => {
        if (row !== currentRow) {
            row.classList.remove('duplicate-highlight', 'conflict-highlight');
            row.querySelector('.show-select').classList.remove('border-yellow-300', 'border-orange-300');
            const timeSelect = row.querySelector('.time-select');
            if (timeSelect) {
                timeSelect.classList.remove('border-yellow-300', 'border-orange-300');
            }
        }
    });

    // Check for duplicates
    allRows.forEach(row => {
        if (row !== currentRow) {
            const rowShowSelect = row.querySelector('.show-select');
            const rowTimeSelect = row.querySelector('.time-select');

            if (rowShowSelect.value === currentShowSelect.value &&
                rowTimeSelect.value === currentTimeSelect.value &&
                currentShowSelect.value && currentTimeSelect.value) {
                hasDuplicate = true;
                duplicateRow = row;
            }
        }
    });

    return { hasDuplicate, duplicateRow };
}

/**
 * Validate all rows in a specific day
 */
function validateAllRowsInDay(dayNumber) {
    const dayContainer = document.getElementById(`day-${dayNumber}-schedules`);
    if (!dayContainer) return;

    const allRows = dayContainer.querySelectorAll('.schedule-row');

    // Re-validate all rows to ensure proper duplicate detection
    allRows.forEach(row => {
        const timeSelect = row.querySelector('.time-select');
        const timeInput = row.querySelector('.time-input'); // For backward compatibility

        if (timeSelect && timeSelect.value) {
            validateTimeSelection(timeSelect);
        } else if (timeInput && timeInput.value) {
            validateTimeRange(timeInput);
        }
    });
}

/**
 * Check for external conflicts with existing mentions
 */
function checkExternalConflicts(showId, time, dayNumber, scheduleRow) {
    if (!showId || !time) return;

    const conflictIndicator = scheduleRow.querySelector('.conflict-indicator');
    if (!conflictIndicator) return;

    // Show loading state
    conflictIndicator.innerHTML = '<i class="fa-solid fa-spinner fa-spin text-blue-500"></i> Checking conflicts...';
    conflictIndicator.style.display = 'block';
    conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-blue-600';

    // Get API URL from window object or use default
    const apiUrl = window.wizardApiUrls?.checkTimeSlotConflicts || '/mentions/check-time-slot-conflicts/';

    // Make API call to check conflicts
    fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            show_id: showId,
            time: time,
            weekday: parseInt(dayNumber)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayConflictResults(data, conflictIndicator, scheduleRow);
        } else {
            conflictIndicator.innerHTML = '<i class="fa-solid fa-exclamation-triangle text-yellow-500"></i> Could not check conflicts';
            conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-yellow-600';
        }
    })
    .catch(error => {
        console.error('Error checking conflicts:', error);
        conflictIndicator.innerHTML = '<i class="fa-solid fa-exclamation-triangle text-yellow-500"></i> Error checking conflicts';
        conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-yellow-600';
    });
}

/**
 * Display conflict check results
 */
function displayConflictResults(data, conflictIndicator, scheduleRow) {
    const timeSelect = scheduleRow.querySelector('.time-select');
    const showSelect = scheduleRow.querySelector('.show-select');

    if (data.conflicts && data.conflicts.length > 0) {
        // Has conflicts
        const conflict = data.conflicts[0];
        conflictIndicator.innerHTML = `⚠️ Conflict: ${conflict.type} "${conflict.title}" at ${conflict.time}`;
        conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-orange-600';

        // Add warning styling
        timeSelect.classList.remove('border-green-300');
        timeSelect.classList.add('border-orange-300');
        showSelect.classList.remove('border-green-300');
        showSelect.classList.add('border-orange-300');
        scheduleRow.classList.add('duplicate-highlight');
    } else {
        // No conflicts
        conflictIndicator.innerHTML = '<i class="fa-solid fa-check text-green-500"></i> No conflicts';
        conflictIndicator.className = 'conflict-indicator text-xs mt-1 text-green-600';

        // Keep green styling
        timeSelect.classList.add('border-green-300');
        showSelect.classList.add('border-green-300');
    }
}

/**
 * Update the schedule summary
 */
function updateScheduleSummary() {
    const summaryContainer = document.getElementById('schedule-summary');
    const summaryContent = document.getElementById('summary-content');

    if (!summaryContainer || !summaryContent) return;

    let totalSlots = 0;
    let validSlots = 0;
    const daysSummary = [];

    // Check each day
    document.querySelectorAll('[id*="day-"][id*="-schedules"]').forEach(dayContainer => {
        const dayNumber = dayContainer.id.match(/day-(\d+)/)[1];
        const dayName = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][dayNumber];

        const rows = dayContainer.querySelectorAll('.schedule-row');
        let daySlots = 0;
        let dayValidSlots = 0;

        rows.forEach(row => {
            const showSelect = row.querySelector('.show-select');
            const timeSelect = row.querySelector('.time-select');

            if (showSelect.value && timeSelect.value) {
                daySlots++;
                totalSlots++;

                // Check if valid (no red borders indicating errors)
                if (!timeSelect.classList.contains('border-red-300')) {
                    dayValidSlots++;
                    validSlots++;
                }
            }
        });

        if (daySlots > 0) {
            daysSummary.push(`${dayName}: ${dayValidSlots}/${daySlots}`);
        }

        // Update individual day summary
        const daySummary = document.getElementById(`day-${dayNumber}-summary`);
        if (daySummary) {
            const summaryText = daySummary.querySelector('.summary-text');
            if (daySlots > 0) {
                summaryText.textContent = `${dayValidSlots}/${daySlots} valid time slots`;
                daySummary.style.display = 'block';
            } else {
                summaryText.textContent = 'No time slots configured';
                daySummary.style.display = 'none';
            }
        }
    });

    // Update overall summary
    if (totalSlots > 0) {
        summaryContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <strong>Total Slots:</strong> ${validSlots}/${totalSlots} valid
                </div>
                <div>
                    <strong>Per Week:</strong> ${validSlots} mentions
                </div>
            </div>
            <div class="mt-2">
                <strong>By Day:</strong> ${daysSummary.join(', ')}
            </div>
        `;
        summaryContainer.style.display = 'block';
    } else {
        summaryContainer.style.display = 'none';
    }
}

/**
 * Validate step 2 form before proceeding
 */
function validateStep2Form() {
    let hasValidSlots = false;
    let hasErrors = false;
    const errors = [];

    // Check each day for valid slots
    document.querySelectorAll('[id*="day-"][id*="-schedules"]').forEach(dayContainer => {
        const dayNumber = dayContainer.id.match(/day-(\d+)/)[1];
        const dayName = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][dayNumber];

        const rows = dayContainer.querySelectorAll('.schedule-row');

        rows.forEach(row => {
            const showSelect = row.querySelector('.show-select');
            const timeSelect = row.querySelector('.time-select');

            if (showSelect.value && timeSelect.value) {
                hasValidSlots = true;

                // Check for validation errors
                if (timeSelect.classList.contains('border-red-300')) {
                    hasErrors = true;
                    errors.push(`${dayName}: Invalid time selection`);
                }
            } else if (showSelect.value || timeSelect.value) {
                // Incomplete selection
                hasErrors = true;
                errors.push(`${dayName}: Incomplete show/time selection`);
            }
        });
    });

    if (!hasValidSlots) {
        alert('Please schedule at least one show.');
        return false;
    }

    if (hasErrors) {
        alert('Please fix the following errors:\n\n' + errors.join('\n'));
        return false;
    }

    return true;
}

// Debounce timer for preview updates
let previewUpdateTimer = null;
let sessionMonitorTimer = null;

/**
 * Debounced update preview function
 */
function debouncedUpdatePreview() {
    // Clear existing timer
    if (previewUpdateTimer) {
        clearTimeout(previewUpdateTimer);
    }

    // Show loading indicator
    showPreviewLoading();

    // Set new timer
    previewUpdateTimer = setTimeout(() => {
        updatePreview();
    }, 500); // 500ms debounce
}

/**
 * Show loading indicator in preview area
 */
function showPreviewLoading() {
    const previewContainer = document.querySelector('#schedule-preview-container');
    const sessionStatus = document.getElementById('session-status');

    if (previewContainer) {
        // Add loading overlay
        const loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'preview-loading-overlay';
        loadingOverlay.className = 'absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10';
        loadingOverlay.innerHTML = `
            <div class="flex items-center space-x-2 text-blue-600">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span class="text-sm">Updating preview...</span>
            </div>
        `;

        // Make preview container relative if not already
        if (getComputedStyle(previewContainer).position === 'static') {
            previewContainer.style.position = 'relative';
        }

        // Remove existing overlay
        const existingOverlay = document.getElementById('preview-loading-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        previewContainer.appendChild(loadingOverlay);
    }

    // Show session status
    if (sessionStatus) {
        sessionStatus.style.opacity = '1';
        sessionStatus.innerHTML = '<i class="fa-solid fa-sync fa-spin mr-1"></i>Updating...';
        sessionStatus.className = 'text-xs text-blue-600 transition-opacity';
    }
}

/**
 * Hide loading indicator
 */
function hidePreviewLoading() {
    const loadingOverlay = document.getElementById('preview-loading-overlay');
    const sessionStatus = document.getElementById('session-status');

    if (loadingOverlay) {
        loadingOverlay.remove();
    }

    if (sessionStatus) {
        sessionStatus.innerHTML = '<i class="fa-solid fa-circle-check mr-1"></i>Updated';
        sessionStatus.className = 'text-xs text-green-600 transition-opacity';

        // Fade out after 2 seconds
        setTimeout(() => {
            sessionStatus.style.opacity = '0';
        }, 2000);
    }
}

/**
 * Start session monitoring for automatic updates
 */
function startSessionMonitoring() {
    // Clear existing timer
    if (sessionMonitorTimer) {
        clearInterval(sessionMonitorTimer);
    }

    // Check for session changes every 30 seconds
    sessionMonitorTimer = setInterval(() => {
        checkSessionChanges();
    }, 30000);
}

/**
 * Check for session changes and update preview if needed
 */
function checkSessionChanges() {
    // Only check if we're on step 3
    if (!document.getElementById('step3-form')) {
        return;
    }

    // Check for localStorage changes from previous steps
    const clientChanged = localStorage.getItem('wizard_client_changed');
    const daysChanged = localStorage.getItem('wizard_days_changed');
    const scheduleChanged = localStorage.getItem('wizard_schedule_changed');

    let shouldUpdate = false;
    const now = Date.now();
    const updateThreshold = 5000; // 5 seconds

    // Check if any changes occurred recently
    if (clientChanged && (now - parseInt(clientChanged)) < updateThreshold) {
        shouldUpdate = true;
        localStorage.removeItem('wizard_client_changed');
    }

    if (daysChanged && (now - parseInt(daysChanged)) < updateThreshold) {
        shouldUpdate = true;
        localStorage.removeItem('wizard_days_changed');
    }

    if (scheduleChanged && (now - parseInt(scheduleChanged)) < updateThreshold) {
        shouldUpdate = true;
        localStorage.removeItem('wizard_schedule_changed');
    }

    if (shouldUpdate) {
        console.log('Detected changes from previous steps, updating preview...');
        debouncedUpdatePreview();
        return;
    }

    // Make a lightweight request to check session status
    fetch('/mentions/wizard/session/debug/', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.wizard_data_exists) {
            // Session is still valid
            console.log('Session active, wizard data exists');
        } else {
            // Session expired or data lost
            console.log('Session expired or wizard data lost');
            showSessionExpiredMessage();
        }
    })
    .catch(error => {
        console.log('Session check failed:', error);
        // Don't show error for session checks, just log it
    });
}

/**
 * Show session expired message
 */
function showSessionExpiredMessage() {
    const previewContainer = document.querySelector('#schedule-preview-container');
    if (previewContainer) {
        previewContainer.innerHTML = `
            <div class="text-center py-8">
                <div class="text-yellow-400 text-lg mb-2">⚠️</div>
                <p class="text-yellow-600 mb-4">Your session has expired or wizard data was lost.</p>
                <a href="/mentions/recurring/wizard/step/1/" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i class="fa-solid fa-refresh mr-2"></i>
                    Start Over
                </a>
            </div>
        `;
    }
}

/**
 * Check for changes from previous steps when loading step 3
 */
function checkForPreviousStepChanges() {
    const clientChanged = localStorage.getItem('wizard_client_changed');
    const daysChanged = localStorage.getItem('wizard_days_changed');
    const scheduleChanged = localStorage.getItem('wizard_schedule_changed');

    if (clientChanged || daysChanged || scheduleChanged) {
        console.log('Detected pending changes from previous steps, will update preview...');

        // Show a brief notification
        const sessionStatus = document.getElementById('session-status');
        if (sessionStatus) {
            sessionStatus.style.opacity = '1';
            sessionStatus.innerHTML = '<i class="fa-solid fa-info-circle mr-1"></i>Updating from changes...';
            sessionStatus.className = 'text-xs text-blue-600 transition-opacity';

            setTimeout(() => {
                sessionStatus.style.opacity = '0';
            }, 3000);
        }

        // Clear the change flags
        localStorage.removeItem('wizard_client_changed');
        localStorage.removeItem('wizard_days_changed');
        localStorage.removeItem('wizard_schedule_changed');

        // Trigger an update after a short delay
        setTimeout(() => {
            debouncedUpdatePreview();
        }, 500);
    }
}

/**
 * Update preview for step 3
 */
function updatePreview() {
    const form = document.getElementById('step3-form');
    if (!form) return;

    const startDate = form.querySelector('input[name="start_date"]').value;
    const weeks = form.querySelector('input[name="weeks"]').value;
    const endDate = form.querySelector('input[name="end_date"]').value;

    if (!startDate || weeks <= 0) {
        // Show a message in the preview container
        const previewContainer = document.querySelector('#schedule-preview-container');
        if (previewContainer) {
            previewContainer.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-gray-400 text-lg mb-2">📅</div>
                    <p class="text-gray-500">Please set a start date and duration to see the schedule preview.</p>
                </div>
            `;
        }
        hidePreviewLoading();
        return;
    }

    // Update statistics
    updateStatistics(weeks);

    // Update schedule preview via AJAX
    updateSchedulePreview(startDate, weeks, endDate);
}

/**
 * Update statistics display
 */
function updateStatistics(weeks) {
    // Get mentions per week from the data element
    const mentionsPerWeekElement = document.querySelector('[data-mentions-per-week]');
    const mentionsPerWeek = mentionsPerWeekElement ?
        parseInt(mentionsPerWeekElement.dataset.mentionsPerWeek || '0') : 0;

    const estimatedTotal = mentionsPerWeek * weeks;

    // Update statistics display - target the specific statistic elements
    const statsElements = {
        mentionsPerWeek: document.querySelector('.text-blue-600'),
        estimatedTotal: document.querySelector('.text-green-600'),
        totalWeeks: document.querySelector('.text-purple-600'),
        costElement: document.querySelector('.text-orange-600')
    };

    if (statsElements.mentionsPerWeek) {
        statsElements.mentionsPerWeek.textContent = mentionsPerWeek;
    }

    if (statsElements.estimatedTotal) {
        statsElements.estimatedTotal.textContent = estimatedTotal;
    }

    if (statsElements.totalWeeks) {
        statsElements.totalWeeks.textContent = weeks;
    }

    // Update cost estimate if available
    if (statsElements.costElement) {
        const ratePerMention = parseFloat(statsElements.costElement.dataset.rate) || 0;
        if (ratePerMention > 0) {
            const totalCost = estimatedTotal * ratePerMention;
            statsElements.costElement.textContent = '$' + totalCost.toFixed(2);
        }
    }
}

/**
 * Set duration using quick buttons
 */
function setWeeks(weeks) {
    const weeksInput = document.querySelector('input[name="weeks"]');
    const endDateInput = document.querySelector('input[name="end_date"]');
    if (weeksInput) weeksInput.value = weeks;
    if (endDateInput) endDateInput.value = '';

    // Use debounced update for better performance
    if (typeof debouncedUpdatePreview === 'function') {
        debouncedUpdatePreview();
    } else {
        updatePreview();
    }
}

/**
 * Update duration from end date
 */
function updateFromEndDate() {
    const startDate = document.querySelector('input[name="start_date"]').value;
    const endDate = document.querySelector('input[name="end_date"]').value;

    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = Math.abs(end - start);
        const diffWeeks = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));

        if (diffWeeks > 0) {
            const weeksInput = document.querySelector('input[name="weeks"]');
            if (weeksInput) weeksInput.value = diffWeeks;

            // Use debounced update for better performance
            if (typeof debouncedUpdatePreview === 'function') {
                debouncedUpdatePreview();
            } else {
                updatePreview();
            }
        }
    }
}

/**
 * Confirm creation with template data
 */
function confirmCreationWithTemplate(mentionsPerWeek) {
    if (!validateAndPreview()) {
        return false;
    }

    const weeks = document.querySelector('input[name="weeks"]').value;
    const estimatedMentions = mentionsPerWeek * weeks;

    const message = `This will create a recurring mention campaign with:\n\n` +
                   `• Duration: ${weeks} weeks\n` +
                   `• Estimated mentions: ${estimatedMentions}\n` +
                   `• All mentions will be created in "pending" status\n\n` +
                   `Are you sure you want to proceed?`;

    return confirm(message);
}

/**
 * Update schedule preview via AJAX (template version with Django URL)
 */
function updateSchedulePreviewTemplate(startDate, weeks, endDate) {
    const form = document.getElementById('step3-form');
    const formData = new FormData(form);

    // Add preview flag
    formData.append('preview_only', 'true');
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.status === 302 || response.redirected) {
            // Session expired, redirect to step 1
            window.location.href = '/mentions/recurring/wizard/step/1/';
            return;
        }
        return response.json();
    })
    .then(data => {
        if (data && data.preview_html) {
            const previewContainer = document.querySelector('#schedule-preview-container');
            if (previewContainer) {
                previewContainer.innerHTML = data.preview_html;
            }
        }

        // Update statistics if provided in the response
        if (data && data.statistics) {
            updateStatisticsFromResponse(data.statistics);
        }

        // Hide loading indicator
        hidePreviewLoading();
    })
    .catch(error => {
        console.error('Error updating preview:', error);
        const previewContainer = document.querySelector('#schedule-preview-container');
        if (previewContainer) {
            previewContainer.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-red-400 text-lg mb-2">⚠️</div>
                    <p class="text-red-500">Error loading preview. Please try again.</p>
                </div>
            `;
        }

        // Hide loading indicator
        hidePreviewLoading();
    });
}

/**
 * Update schedule preview via AJAX (external version)
 */
function updateSchedulePreview(startDate, weeks, endDate) {
    const form = document.getElementById('step3-form');
    const formData = new FormData(form);

    // Add preview flag
    formData.append('preview_only', 'true');
    formData.append('csrfmiddlewaretoken', csrfToken);

    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (response.status === 302 || response.redirected) {
            // Session expired, redirect to step 1
            window.location.href = '/mentions/recurring/wizard/step/1/';
            return;
        }
        return response.json();
    })
    .then(data => {
        if (data && data.preview_html) {
            const previewContainer = document.querySelector('#schedule-preview-container');
            if (previewContainer) {
                previewContainer.innerHTML = data.preview_html;
            }
        }

        // Update statistics if provided in the response
        if (data && data.statistics) {
            updateStatisticsFromResponse(data.statistics);
        }

        // Hide loading indicator
        hidePreviewLoading();
    })
    .catch(error => {
        console.error('Error updating preview:', error);
        const previewContainer = document.querySelector('#schedule-preview-container');
        if (previewContainer) {
            previewContainer.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-red-400 text-lg mb-2">⚠️</div>
                    <p class="text-red-500">Error loading preview. Please try again.</p>
                </div>
            `;
        }

        // Hide loading indicator
        hidePreviewLoading();
    });
}

/**
 * Update statistics from AJAX response data
 */
function updateStatisticsFromResponse(statistics) {
    // Update statistics display with data from server response
    const statsElements = {
        mentionsPerWeek: document.querySelector('.text-blue-600'),
        estimatedTotal: document.querySelector('.text-green-600'),
        totalWeeks: document.querySelector('.text-purple-600'),
        costElement: document.querySelector('.text-orange-600')
    };

    if (statsElements.mentionsPerWeek && statistics.mentions_per_week !== undefined) {
        statsElements.mentionsPerWeek.textContent = statistics.mentions_per_week;
    }

    if (statsElements.estimatedTotal && statistics.estimated_total_mentions !== undefined) {
        statsElements.estimatedTotal.textContent = statistics.estimated_total_mentions;
    }

    if (statsElements.totalWeeks && statistics.total_weeks !== undefined) {
        statsElements.totalWeeks.textContent = statistics.total_weeks;
    }

    // Update cost estimate if available
    if (statsElements.costElement && statistics.cost_estimate !== undefined) {
        if (statistics.cost_estimate !== null) {
            statsElements.costElement.textContent = '$' + statistics.cost_estimate.toFixed(2);
        } else {
            statsElements.costElement.textContent = '--';
        }
    }
}

/**
 * Initialize preview container for step 3
 */
function initializePreviewContainer() {
    const previewContainer = document.querySelector('#schedule-preview-container');
    if (previewContainer) {
        // Move existing preview content into the container
        const existingPreview = previewContainer.nextElementSibling;
        if (existingPreview && existingPreview.classList.contains('overflow-x-auto')) {
            previewContainer.innerHTML = existingPreview.outerHTML;
            existingPreview.remove();
        } else if (!previewContainer.innerHTML.trim()) {
            // If no existing content, show placeholder
            previewContainer.innerHTML = `
                <div class="text-center py-8">
                    <div class="text-gray-400 text-lg mb-2">📅</div>
                    <p class="text-gray-500">Configure the period above to see the schedule preview.</p>
                </div>
            `;
        }
    }
}

/**
 * Validate and preview for step 3
 */
function validateAndPreview() {
    const form = document.getElementById('step3-form');
    const startDate = form.querySelector('input[name="start_date"]').value;
    const weeks = form.querySelector('input[name="weeks"]').value;

    if (!startDate) {
        alert('Please select a start date.');
        return false;
    }

    if (!weeks || weeks < 1 || weeks > 52) {
        alert('Please enter a valid duration (1-52 weeks).');
        return false;
    }

    // Check if start date is not in the past
    const today = new Date();
    const selectedDate = new Date(startDate);
    today.setHours(0, 0, 0, 0);
    selectedDate.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
        alert('Start date cannot be in the past.');
        return false;
    }

    // If validation passes, update the preview
    if (typeof debouncedUpdatePreview === 'function') {
        debouncedUpdatePreview();
    } else {
        updatePreview();
    }

    return true;
}

/**
 * Confirm creation of recurring mention
 */
function confirmCreation() {
    if (!validateAndPreview()) {
        return false;
    }

    const weeks = document.querySelector('input[name="weeks"]').value;
    const mentionsPerWeek = parseInt(document.querySelector('[data-mentions-per-week]')?.dataset.mentionsPerWeek || '0');
    const estimatedMentions = mentionsPerWeek * weeks;

    const message = `This will create a recurring mention campaign with:\n\n` +
                   `• Duration: ${weeks} weeks\n` +
                   `• Estimated mentions: ${estimatedMentions}\n` +
                   `• All mentions will be created in "pending" status\n\n` +
                   `Are you sure you want to proceed?`;

    return confirm(message);
}

/**
 * Backward compatibility function for time range validation
 */
function validateTimeRange(input) {
    // This function can be implemented for backward compatibility
    // with any remaining time input fields
    console.log('validateTimeRange called for:', input);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a recurring wizard page
    if (document.querySelector('.schedule-row') || document.getElementById('step3-form')) {
        console.log('Recurring wizard page detected, waiting for initialization...');

        // Clear any stale validation state on page load
        setTimeout(function() {
            if (typeof clearAllValidationState === 'function') {
                clearAllValidationState();
            }
        }, 100);
    }
});

// Clear validation state when page becomes visible (user returns from another tab/page)
document.addEventListener('visibilitychange', function() {
    if (!document.hidden && (document.querySelector('.schedule-row') || document.getElementById('step3-form'))) {
        if (typeof clearAllValidationState === 'function') {
            clearAllValidationState();
        }
    }
});
