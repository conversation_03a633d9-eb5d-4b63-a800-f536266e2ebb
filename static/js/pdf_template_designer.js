// PDF Template Designer JavaScript

let filterCount = 0;
let columnCount = 0;
let currentTemplate = null;

// Field options for different models
const fieldOptions = {
    'Mention': [
        { value: 'client', label: 'Client' },
        { value: 'client.name', label: 'Client Name' },
        { value: 'title', label: 'Title' },
        { value: 'content', label: 'Content' },
        { value: 'status', label: 'Status' },
        { value: 'priority', label: 'Priority' },
        { value: 'duration_seconds', label: 'Duration' },
        { value: 'created_at', label: 'Created Date' },
        { value: 'created_by.username', label: 'Created By' },
        { value: 'approved_by.username', label: 'Approved By' }
    ],
    'MentionReading': [
        { value: 'mention.title', label: 'Mention Title' },
        { value: 'mention.client.name', label: 'Client' },
        { value: 'show.name', label: 'Show' },
        { value: 'presenter.name', label: 'Presenter' },
        { value: 'actual_read_time', label: 'Read Time' },
        { value: 'mention.status', label: 'Status' },
        { value: 'mention.duration_seconds', label: 'Duration' }
    ]
};

// Transform options
const transformOptions = [
    { value: 'str', label: 'Text' },
    { value: 'datetime', label: 'Date/Time' },
    { value: 'truncate', label: 'Truncated Text' },
    { value: 'duration', label: 'Duration' },
    { value: 'priority', label: 'Priority' },
    { value: 'show_slot', label: 'Show Time Slot' }
];

function addFilter(name = '', field = '', type = 'str') {
    filterCount++;
    const container = document.getElementById('filtersContainer');
    const filterDiv = document.createElement('div');
    filterDiv.className = 'column-item';
    filterDiv.id = `filter-${filterCount}`;
    
    filterDiv.innerHTML = `
        <div class="row">
            <div class="col-md-4">
                <label class="small">Filter Name</label>
                <input type="text" class="form-control form-control-sm" 
                       value="${name}" placeholder="start_date">
            </div>
            <div class="col-md-4">
                <label class="small">Database Field</label>
                <input type="text" class="form-control form-control-sm" 
                       value="${field}" placeholder="created_at__date__gte">
            </div>
            <div class="col-md-3">
                <label class="small">Type</label>
                <select class="form-control form-control-sm">
                    <option value="str" ${type === 'str' ? 'selected' : ''}>String</option>
                    <option value="int" ${type === 'int' ? 'selected' : ''}>Integer</option>
                    <option value="date" ${type === 'date' ? 'selected' : ''}>Date</option>
                </select>
            </div>
            <div class="col-md-1">
                <label class="small">&nbsp;</label>
                <button type="button" class="btn btn-remove btn-sm d-block" 
                        onclick="removeFilter(${filterCount})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(filterDiv);
}

function removeFilter(id) {
    const element = document.getElementById(`filter-${id}`);
    if (element) {
        element.remove();
    }
}

function addColumn(header = '', field = '', transform = 'str', width = 30) {
    columnCount++;
    const container = document.getElementById('columnsContainer');
    const columnDiv = document.createElement('div');
    columnDiv.className = 'column-item';
    columnDiv.id = `column-${columnCount}`;
    
    const modelFields = fieldOptions[document.getElementById('dataModel').value] || fieldOptions['Mention'];
    const fieldOptionsHtml = modelFields.map(opt => 
        `<option value="${opt.value}" ${field === opt.value ? 'selected' : ''}>${opt.label}</option>`
    ).join('');
    
    const transformOptionsHtml = transformOptions.map(opt => 
        `<option value="${opt.value}" ${transform === opt.value ? 'selected' : ''}>${opt.label}</option>`
    ).join('');
    
    columnDiv.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <label class="small">Header</label>
                <input type="text" class="form-control form-control-sm" 
                       value="${header}" placeholder="Column Title">
            </div>
            <div class="col-md-3">
                <label class="small">Field</label>
                <select class="form-control form-control-sm">
                    ${fieldOptionsHtml}
                </select>
            </div>
            <div class="col-md-2">
                <label class="small">Transform</label>
                <select class="form-control form-control-sm">
                    ${transformOptionsHtml}
                </select>
            </div>
            <div class="col-md-2">
                <label class="small">Width (mm)</label>
                <input type="number" class="form-control form-control-sm" 
                       value="${width}" min="10" max="100">
            </div>
            <div class="col-md-1">
                <label class="small">Options</label>
                <input type="text" class="form-control form-control-sm" 
                       placeholder="length:40" title="Transform options like length:40 for truncate">
            </div>
            <div class="col-md-1">
                <label class="small">&nbsp;</label>
                <button type="button" class="btn btn-remove btn-sm d-block" 
                        onclick="removeColumn(${columnCount})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(columnDiv);
}

function removeColumn(id) {
    const element = document.getElementById(`column-${id}`);
    if (element) {
        element.remove();
    }
}

function generateJSON() {
    const templateName = document.getElementById('templateName').value;
    const reportKey = document.getElementById('reportKey').value;
    const description = document.getElementById('description').value;
    const orientation = document.getElementById('orientation').value;
    const dataModel = document.getElementById('dataModel').value;
    const ordering = document.getElementById('ordering').value;
    
    // Collect filters
    const filters = {};
    document.querySelectorAll('#filtersContainer .column-item').forEach(item => {
        const inputs = item.querySelectorAll('input, select');
        const name = inputs[0].value;
        const field = inputs[1].value;
        const type = inputs[2].value;
        
        if (name && field) {
            filters[name] = { field: field, type: type };
        }
    });
    
    // Collect columns
    const headers = [];
    const colWidths = [];
    const rowData = [];
    
    document.querySelectorAll('#columnsContainer .column-item').forEach(item => {
        const inputs = item.querySelectorAll('input, select');
        const header = inputs[0].value;
        const field = inputs[1].value;
        const transform = inputs[2].value;
        const width = parseInt(inputs[3].value) || 30;
        const options = inputs[4].value;
        
        if (header && field) {
            headers.push(header);
            colWidths.push(width);
            
            const rowConfig = { field: field, transform: transform };
            
            // Parse options
            if (options) {
                const optionPairs = options.split(',');
                optionPairs.forEach(pair => {
                    const [key, value] = pair.split(':');
                    if (key && value) {
                        if (key.trim() === 'length') {
                            rowConfig.length = parseInt(value.trim());
                        } else if (key.trim() === 'format') {
                            rowConfig.format = value.trim();
                        } else if (key.trim() === 'default') {
                            rowConfig.default = value.trim();
                        }
                    }
                });
            }
            
            rowData.push(rowConfig);
        }
    });
    
    // Generate the complete configuration
    const config = {
        [reportKey]: {
            name: templateName,
            description: description,
            model: dataModel,
            filters: filters,
            ordering: ordering,
            pdf_config: {
                title: templateName,
                orientation: orientation,
                headers: headers,
                col_widths: colWidths,
                row_data: rowData
            }
        }
    };
    
    // Display JSON
    const jsonPreview = document.getElementById('jsonPreview');
    jsonPreview.textContent = JSON.stringify(config, null, 2);
    
    currentTemplate = config;
    return config;
}

function copyJSON() {
    const jsonText = document.getElementById('jsonPreview').textContent;
    navigator.clipboard.writeText(jsonText).then(() => {
        alert('JSON copied to clipboard!');
    });
}

function saveTemplate() {
    if (!currentTemplate) {
        generateJSON();
    }
    
    const templateName = document.getElementById('templateName').value;
    if (!templateName) {
        alert('Please enter a template name');
        return;
    }
    
    // Save to localStorage for now (you can implement server-side saving later)
    const savedTemplates = JSON.parse(localStorage.getItem('pdfTemplates') || '{}');
    savedTemplates[templateName] = currentTemplate;
    localStorage.setItem('pdfTemplates', JSON.stringify(savedTemplates));
    
    alert('Template saved successfully!');
    loadTemplates();
}

function loadTemplates() {
    const savedTemplates = JSON.parse(localStorage.getItem('pdfTemplates') || '{}');
    const templateList = document.getElementById('templateList');
    
    templateList.innerHTML = '';
    
    Object.keys(savedTemplates).forEach(templateName => {
        const templateItem = document.createElement('div');
        templateItem.className = 'template-item';
        templateItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${templateName}</strong>
                    <br><small class="text-muted">${Object.keys(savedTemplates[templateName])[0]}</small>
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="loadTemplate('${templateName}')">
                        <i class="fas fa-upload"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteTemplate('${templateName}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        templateList.appendChild(templateItem);
    });
}

function loadTemplate(templateName) {
    const savedTemplates = JSON.parse(localStorage.getItem('pdfTemplates') || '{}');
    const template = savedTemplates[templateName];
    
    if (!template) {
        alert('Template not found');
        return;
    }
    
    const config = template[Object.keys(template)[0]];
    
    // Load basic settings
    document.getElementById('templateName').value = config.name;
    document.getElementById('reportKey').value = Object.keys(template)[0];
    document.getElementById('description').value = config.description;
    document.getElementById('orientation').value = config.pdf_config.orientation;
    document.getElementById('dataModel').value = config.model;
    document.getElementById('ordering').value = config.ordering;
    
    // Clear and reload filters
    document.getElementById('filtersContainer').innerHTML = '';
    filterCount = 0;
    Object.entries(config.filters).forEach(([name, filterConfig]) => {
        addFilter(name, filterConfig.field, filterConfig.type);
    });
    
    // Clear and reload columns
    document.getElementById('columnsContainer').innerHTML = '';
    columnCount = 0;
    config.pdf_config.headers.forEach((header, index) => {
        const rowConfig = config.pdf_config.row_data[index];
        const width = config.pdf_config.col_widths[index];
        addColumn(header, rowConfig.field, rowConfig.transform, width);
    });
    
    generateJSON();
    alert('Template loaded successfully!');
}

function deleteTemplate(templateName) {
    if (confirm(`Are you sure you want to delete template "${templateName}"?`)) {
        const savedTemplates = JSON.parse(localStorage.getItem('pdfTemplates') || '{}');
        delete savedTemplates[templateName];
        localStorage.setItem('pdfTemplates', JSON.stringify(savedTemplates));
        loadTemplates();
    }
}

function testTemplate() {
    if (!currentTemplate) {
        generateJSON();
    }
    
    const reportKey = Object.keys(currentTemplate)[0];
    const testUrl = `/reports/dynamic/${reportKey}/generate/?start_date=2024-01-01&end_date=2024-12-31`;
    
    // First, we need to register this template temporarily
    fetch('/reports/register-temp-template/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(currentTemplate)
    }).then(response => {
        if (response.ok) {
            window.open(testUrl, '_blank');
        } else {
            alert('Error registering template for testing');
        }
    }).catch(error => {
        console.error('Error:', error);
        alert('Error testing template');
    });
}

// Update field options when model changes
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('dataModel').addEventListener('change', function() {
        // Update existing column field dropdowns
        document.querySelectorAll('#columnsContainer .column-item select:nth-of-type(1)').forEach(select => {
            const currentValue = select.value;
            const modelFields = fieldOptions[this.value] || fieldOptions['Mention'];
            select.innerHTML = modelFields.map(opt => 
                `<option value="${opt.value}" ${currentValue === opt.value ? 'selected' : ''}>${opt.label}</option>`
            ).join('');
        });
    });
});
