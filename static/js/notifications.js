/**
 * RadioMention Notification System - IE11 Compatible
 * Enhanced notification handling and real-time updates
 */

function RadioMentionNotifications() {
    this.wsConnection = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.notificationQueue = [];
    this.isOnline = navigator.onLine;

    this.init();
}

RadioMentionNotifications.prototype.init = function() {
        // Initialize notification system
        this.setupEventListeners();
        this.requestNotificationPermission();
        this.startPeriodicSync();

        // WebSocket disabled - using polling fallback
        // this.connectWebSocket();
        
    console.log('RadioMention Notification System initialized');
};

RadioMentionNotifications.prototype.setupEventListeners = function() {
        // Online/offline detection
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.processQueuedNotifications();
            // WebSocket disabled - using polling fallback
            // this.connectWebSocket();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
        });

        // Page visibility change
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isOnline) {
                this.syncNotifications();
            }
        });
    }

    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    this.showBrowserNotification('Notifications Enabled', 'You will now receive RadioMention notifications');
                }
            });
        }
    }

    connectWebSocket() {
        // WebSocket connection for real-time notifications
        // Currently disabled - WebSocket support not implemented yet
        // Using polling fallback instead
        console.log('WebSocket not available, using polling fallback');
        return;

        // TODO: Implement WebSocket support with Django Channels
        // try {
        //     const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        //     const wsUrl = `${protocol}//${window.location.host}/ws/notifications/`;
        //
        //     this.wsConnection = new WebSocket(wsUrl);
        //
        //     this.wsConnection.onopen = () => {
        //         console.log('WebSocket connected');
        //         this.reconnectAttempts = 0;
        //     };
        //
        //     this.wsConnection.onmessage = (event) => {
        //         const data = JSON.parse(event.data);
        //         this.handleRealtimeNotification(data);
        //     };
        //
        //     this.wsConnection.onclose = () => {
        //         console.log('WebSocket disconnected');
        //         this.scheduleReconnect();
        //     };
        //
        //     this.wsConnection.onerror = (error) => {
        //         console.error('WebSocket error:', error);
        //     };
        // } catch (error) {
        //     console.log('WebSocket not available, using polling fallback');
        // }
    }

    scheduleReconnect() {
        // WebSocket reconnection disabled - using polling fallback
        console.log('WebSocket reconnection disabled, using polling fallback');
        return;

        // TODO: Re-enable when WebSocket support is implemented
        // if (this.reconnectAttempts < this.maxReconnectAttempts) {
        //     setTimeout(() => {
        //         this.reconnectAttempts++;
        //         this.connectWebSocket();
        //     }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
        // }
    }

    handleRealtimeNotification(data) {
        // Handle real-time notification from WebSocket
        this.processNotification(data);
    }

    processNotification(notification) {
        // Process incoming notification
        if (!this.isOnline) {
            this.notificationQueue.push(notification);
            return;
        }

        // Show in-app notification
        if (window.notificationSystem) {
            const type = this.mapNotificationType(notification.type);
            window.notificationSystem.show(notification.message, type, 5000);
        }

        // Show browser notification if permission granted
        if (this.shouldShowBrowserNotification(notification)) {
            this.showBrowserNotification(notification.title, notification.message, notification.type);
        }

        // Update notification badge
        this.updateNotificationBadge();

        // Play notification sound if enabled
        this.playNotificationSound(notification.type);

        // Update any notification widgets on the page
        this.updateNotificationWidgets();
    }

    mapNotificationType(type) {
        const typeMap = {
            'show_alert': 'error',
            'system_alert': 'error',
            'approval_reminder': 'warning',
            'conflict_alert': 'warning',
            'mention_update': 'info',
            'schedule_change': 'info',
            'deadline_alert': 'warning',
            'performance_report': 'success'
        };
        return typeMap[type] || 'info';
    }

    shouldShowBrowserNotification(notification) {
        // Check if browser notifications are enabled and permission granted
        if ('Notification' in window && Notification.permission === 'granted') {
            // Don't show if page is visible (user is actively using the app)
            if (!document.hidden) {
                return false;
            }
            
            // Check user preferences
            const userPrefs = this.getUserNotificationPreferences();
            if (!userPrefs.browser_notifications) {
                return false;
            }
            
            // Check quiet hours
            if (this.isQuietHours()) {
                return false;
            }
            
            return true;
        }
        return false;
    }

    showBrowserNotification(title, message, type = 'info') {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: message,
                icon: this.getNotificationIcon(type),
                badge: '/static/images/badge.png',
                tag: `radiomention-${type}`,
                requireInteraction: type === 'show_alert' || type === 'system_alert'
            });

            notification.onclick = () => {
                window.focus();
                notification.close();
            };

            // Auto-close after 5 seconds for non-critical notifications
            if (type !== 'show_alert' && type !== 'system_alert') {
                setTimeout(() => notification.close(), 5000);
            }
        }
    }

    getNotificationIcon(type) {
        const icons = {
            'show_alert': '/static/images/alert.png',
            'system_alert': '/static/images/alert.png',
            'approval_reminder': '/static/images/clock.png',
            'mention_update': '/static/images/bullhorn.png',
            'schedule_change': '/static/images/calendar.png'
        };
        return icons[type] || '/static/images/notification.png';
    }

    playNotificationSound(type) {
        // Play notification sound based on type
        const userPrefs = this.getUserNotificationPreferences();
        if (!userPrefs.notification_sounds) {
            return;
        }

        const sounds = {
            'show_alert': '/static/sounds/alert.mp3',
            'system_alert': '/static/sounds/alert.mp3',
            'approval_reminder': '/static/sounds/reminder.mp3',
            'mention_update': '/static/sounds/notification.mp3'
        };

        const soundFile = sounds[type];
        if (soundFile) {
            const audio = new Audio(soundFile);
            audio.volume = 0.3;
            audio.play().catch(error => {
                console.log('Could not play notification sound:', error);
            });
        }
    }

    updateNotificationBadge() {
        // Update notification badge in header using optimized AJAX
        if (window.ajax) {
            window.ajax.get('/api/notifications/unread-count/')
                .then(data => {
                    const badge = document.getElementById('notificationBadge');
                    if (badge && data.count !== undefined) {
                        if (data.count > 0) {
                            badge.textContent = data.count > 99 ? '99+' : data.count;
                            badge.classList.remove('hidden');
                        } else {
                            badge.classList.add('hidden');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error updating notification badge:', error);
                });
        } else {
            // Fallback to regular fetch if AJAX manager not available
            fetch('/api/notifications/unread-count/')
                .then(response => response.json())
                .then(data => {
                    const badge = document.getElementById('notificationBadge');
                    if (badge && data.count !== undefined) {
                        if (data.count > 0) {
                            badge.textContent = data.count > 99 ? '99+' : data.count;
                            badge.classList.remove('hidden');
                        } else {
                            badge.classList.add('hidden');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error updating notification badge:', error);
                });
        }
    }

    updateNotificationWidgets() {
        // Refresh any notification widgets on the page
        if (typeof refreshNotifications === 'function') {
            refreshNotifications();
        }
    }

    getUserNotificationPreferences() {
        // Get user notification preferences from localStorage or API
        const stored = localStorage.getItem('notification_preferences');
        if (stored) {
            return JSON.parse(stored);
        }
        
        // Default preferences
        return {
            browser_notifications: true,
            email_notifications: true,
            notification_sounds: true,
            quiet_hours_start: '22:00',
            quiet_hours_end: '08:00'
        };
    }

    isQuietHours() {
        const prefs = this.getUserNotificationPreferences();
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        
        const startTime = this.parseTime(prefs.quiet_hours_start);
        const endTime = this.parseTime(prefs.quiet_hours_end);
        
        if (startTime > endTime) {
            // Quiet hours span midnight
            return currentTime >= startTime || currentTime <= endTime;
        } else {
            return currentTime >= startTime && currentTime <= endTime;
        }
    }

    parseTime(timeString) {
        const [hours, minutes] = timeString.split(':').map(Number);
        return hours * 60 + minutes;
    }

    startPeriodicSync() {
        // Sync notifications every 30 seconds
        setInterval(() => {
            if (this.isOnline) {
                this.syncNotifications();
            }
        }, 30000);
    }

    syncNotifications() {
        // Sync with server for new notifications using optimized AJAX
        if (window.ajax) {
            window.ajax.get('/api/notifications/sync/')
                .then(data => {
                    if (data.notifications) {
                        data.notifications.forEach(notification => {
                            this.processNotification(notification);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error syncing notifications:', error);
                });
        } else {
            // Fallback to regular fetch
            fetch('/api/notifications/sync/')
                .then(response => response.json())
                .then(data => {
                    if (data.notifications) {
                        data.notifications.forEach(notification => {
                            this.processNotification(notification);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error syncing notifications:', error);
                });
        }
    }

    processQueuedNotifications() {
        // Process any queued notifications when coming back online
        while (this.notificationQueue.length > 0) {
            const notification = this.notificationQueue.shift();
            this.processNotification(notification);
        }
    }

    markAsRead(notificationId) {
        // Mark notification as read using optimized AJAX
        if (window.ajax) {
            window.ajax.post(`/api/notifications/${notificationId}/mark-read/`, {})
                .then(data => {
                    if (data.success) {
                        this.updateNotificationBadge();
                        this.updateNotificationWidgets();
                    }
                })
                .catch(error => {
                    console.error('Error marking notification as read:', error);
                });
        } else {
            // Fallback to regular fetch
            fetch(`/api/notifications/${notificationId}/mark-read/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateNotificationBadge();
                    this.updateNotificationWidgets();
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }
    }
}

// Initialize notification system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.radioMentionNotifications = new RadioMentionNotifications();
});
