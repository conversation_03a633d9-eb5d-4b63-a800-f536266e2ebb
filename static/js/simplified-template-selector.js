/**
 * Simplified Template Selector System
 * Provides streamlined template selection with Preview and Download actions
 */

// Simplified template selector for specific report type
function showTemplateSelector(reportType) {
  console.log('showTemplateSelector called with:', reportType)

  // Get current URL parameters to capture all active filters
  const currentUrlParams = new URLSearchParams(window.location.search)
  
  // Store current parameters for template selector (using actual current values)
  window.currentReportParams = {
    reportType: reportType,
    startDate: currentUrlParams.get('start_date') || getDefaultStartDate(),
    endDate: currentUrlParams.get('end_date') || getDefaultEndDate(),
    clientId: currentUrlParams.get('client_id') || '',
    status: currentUrlParams.get('status') || 'all',
    showSummary: currentUrlParams.get('show_summary') || '',
    compactView: currentUrlParams.get('compact_view') || '',
    showContent: currentUrlParams.get('show_content') || '',
    timeRange: currentUrlParams.get('time_range') || '',
    showStats: currentUrlParams.get('show_stats') || ''
  }

  console.log('currentReportParams with URL filters:', window.currentReportParams)

  // Load and show templates for this specific report type
  loadTemplatesForReportType(reportType)
}

// Load templates filtered by report type
async function loadTemplatesForReportType(reportType) {
  try {
    const response = await fetch(`/reports/api/v1/pdf-templates/?report_type=${reportType}`)
    const data = await response.json()
    
    if (data.results && data.results.length > 0) {
      showSimplifiedTemplateModal(data.results, reportType)
    } else {
      alert(`No templates found for ${reportType}. Please create a template first.`)
    }
  } catch (error) {
    console.error('Failed to load templates:', error)
    alert('Failed to load templates. Please try again.')
  }
}

// Show simplified template modal with only Preview and Download options
function showSimplifiedTemplateModal(templates, reportType) {
  let templateCards = ''
  templates.forEach(template => {
    templateCards += 
      '<div class="template-card border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 hover:shadow-md transition-all" data-template-id="' + template.id + '">' +
        '<h5 class="font-medium text-gray-900 mb-2">' + template.name + '</h5>' +
        '<p class="text-sm text-gray-600 mb-4">' + (template.description || 'No description') + '</p>' +
        '<div class="flex space-x-2">' +
          '<button onclick="previewTemplate(' + template.id + ')" class="flex-1 bg-blue-100 text-blue-800 px-3 py-2 rounded text-sm hover:bg-blue-200 transition-colors">' +
            '👁️ Preview' +
          '</button>' +
          '<button onclick="downloadTemplate(' + template.id + ')" class="flex-1 bg-green-100 text-green-800 px-3 py-2 rounded text-sm hover:bg-green-200 transition-colors">' +
            '🔽 Download' +
          '</button>' +
        '</div>' +
      '</div>'
  })

  const modalHtml = 
    '<div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center" id="simplified-template-modal">' +
      '<div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">' +
        '<div class="px-6 py-4 border-b border-gray-200">' +
          '<h3 class="text-lg font-medium text-gray-900">Select Template for ' + reportType + '</h3>' +
        '</div>' +
        '<div class="p-6 overflow-y-auto max-h-[70vh]">' +
          '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="simplified-template-grid">' +
            templateCards +
          '</div>' +
        '</div>' +
        '<div class="px-6 py-4 border-t border-gray-200 flex justify-end">' +
          '<button onclick="closeSimplifiedTemplateModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">' +
            'Cancel' +
          '</button>' +
        '</div>' +
      '</div>' +
    '</div>'

  // Remove existing modal if any
  const existingModal = document.getElementById('simplified-template-modal')
  if (existingModal) {
    existingModal.remove()
  }

  // Add new modal to body
  document.body.insertAdjacentHTML('beforeend', modalHtml)
}

// Simplified preview function
function previewTemplate(templateId) {
  console.log('Preview template:', templateId)
  
  // Close modal
  closeSimplifiedTemplateModal()
  
  // Show loading indicator
  showLoadingIndicator('Generating PDF preview...')
  
  // Build URL with current filters
  const url = buildTemplateUrl(templateId)
  
  // Open PDF in current tab for preview
  window.location.href = url
}

// Simplified download function
function downloadTemplate(templateId) {
  console.log('Download template:', templateId)
  
  // Close modal
  closeSimplifiedTemplateModal()
  
  // Show loading indicator
  showLoadingIndicator('Generating PDF download...')
  
  // Build URL with current filters
  const url = buildTemplateUrl(templateId)
  
  // Open PDF in new tab for download
  window.open(url, '_blank')
  
  // Hide loading indicator after a short delay
  setTimeout(() => {
    hideLoadingIndicator()
    showSuccessMessage('PDF download started!')
  }, 1000)
}

// Build template URL with current page filters
function buildTemplateUrl(templateId) {
  const currentUrlParams = new URLSearchParams(window.location.search)
  
  const params = new URLSearchParams({
    template_id: templateId,
    report_type: window.currentReportParams.reportType
  })

  // Add all current filter parameters
  const filterParams = [
    'start_date', 'end_date', 'client_id', 'status', 'show_summary', 
    'compact_view', 'show_content', 'time_range', 'show_stats'
  ]

  filterParams.forEach(param => {
    const value = currentUrlParams.get(param)
    if (value) {
      params.append(param, value)
    }
  })

  // Add default dates if not present
  if (!params.get('start_date')) {
    params.append('start_date', getDefaultStartDate())
  }
  if (!params.get('end_date')) {
    params.append('end_date', getDefaultEndDate())
  }

  return '/reports/templated-pdf/?' + params.toString()
}

// Close simplified template modal
function closeSimplifiedTemplateModal() {
  const modal = document.getElementById('simplified-template-modal')
  if (modal) {
    modal.remove()
  }
}

// Show loading indicator
function showLoadingIndicator(message) {
  // Remove existing indicator
  hideLoadingIndicator()
  
  const loadingHtml = 
    '<div id="loading-indicator" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">' +
      '<div class="bg-white rounded-lg shadow-xl p-6 max-w-sm mx-4">' +
        '<div class="flex items-center">' +
          '<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>' +
          '<div>' +
            '<div class="font-medium text-gray-900">Processing...</div>' +
            '<div class="text-sm text-gray-600">' + message + '</div>' +
          '</div>' +
        '</div>' +
      '</div>' +
    '</div>'
  
  document.body.insertAdjacentHTML('beforeend', loadingHtml)
}

// Hide loading indicator
function hideLoadingIndicator() {
  const indicator = document.getElementById('loading-indicator')
  if (indicator) {
    indicator.remove()
  }
}

// Show success message
function showSuccessMessage(message) {
  // Remove existing message
  const existingMessage = document.getElementById('success-message')
  if (existingMessage) {
    existingMessage.remove()
  }
  
  const successHtml = 
    '<div id="success-message" class="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50">' +
      '<div class="flex items-center">' +
        '<i class="fa-solid fa-check-circle mr-2"></i>' +
        '<span>' + message + '</span>' +
      '</div>' +
    '</div>'
  
  document.body.insertAdjacentHTML('beforeend', successHtml)

  setTimeout(() => {
    const message = document.getElementById('success-message')
    if (message) {
      message.remove()
    }
  }, 4000)
}

// Helper functions to get default dates (can be overridden by individual pages)
function getDefaultStartDate() {
  // Default to 30 days ago
  const date = new Date()
  date.setDate(date.getDate() - 30)
  return date.toISOString().split('T')[0]
}

function getDefaultEndDate() {
  // Default to today
  return new Date().toISOString().split('T')[0]
}
