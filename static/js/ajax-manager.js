/**
 * AJAX Manager - Centralized AJAX handling for RadioMention App
 * Provides optimized, cached, and batched AJAX operations
 */

class AjaxManager {
    constructor() {
        this.cache = new Map();
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.pendingRequests = new Map();
        this.retryAttempts = 3;
        this.retryDelay = 1000;
        
        // Initialize CSRF token
        this.csrfToken = this.getCSRFToken();
        
        // Request interceptor for common headers
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'X-CSRFToken': this.csrfToken,
            'X-Requested-With': 'XMLHttpRequest'
        };
        
        // Performance monitoring
        this.performanceMetrics = {
            totalRequests: 0,
            cacheHits: 0,
            averageResponseTime: 0,
            failedRequests: 0
        };
    }

    /**
     * Get CSRF token from various sources
     */
    getCSRFToken() {
        // Try meta tag first
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken) return metaToken.getAttribute('content');
        
        // Try form input
        const formToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (formToken) return formToken.value;
        
        // Try cookie
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') return value;
        }
        
        return '';
    }

    /**
     * Generate cache key for request
     */
    getCacheKey(url, method, data) {
        const dataStr = data ? JSON.stringify(data) : '';
        return `${method}:${url}:${dataStr}`;
    }

    /**
     * Check if request should be cached
     */
    shouldCache(method, url, options = {}) {
        if (options.noCache) return false;
        if (method !== 'GET') return false;
        
        // Cache API endpoints for a short time
        const cacheablePatterns = [
            '/api/notifications/',
            '/api/shows/',
            '/api/mentions/',
            '/api/presenters/'
        ];
        
        return cacheablePatterns.some(pattern => url.includes(pattern));
    }

    /**
     * Get cached response if available and not expired
     */
    getCachedResponse(cacheKey, maxAge = 30000) { // 30 seconds default
        const cached = this.cache.get(cacheKey);
        if (!cached) return null;
        
        if (Date.now() - cached.timestamp > maxAge) {
            this.cache.delete(cacheKey);
            return null;
        }
        
        this.performanceMetrics.cacheHits++;
        return cached.data;
    }

    /**
     * Cache response
     */
    setCachedResponse(cacheKey, data) {
        this.cache.set(cacheKey, {
            data: data,
            timestamp: Date.now()
        });
        
        // Limit cache size
        if (this.cache.size > 100) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
    }

    /**
     * Debounced request - prevents duplicate requests
     */
    debouncedRequest(url, options = {}, delay = 300) {
        const key = this.getCacheKey(url, options.method || 'GET', options.body);
        
        // Cancel existing timeout for this request
        if (this.pendingRequests.has(key)) {
            clearTimeout(this.pendingRequests.get(key));
        }
        
        return new Promise((resolve, reject) => {
            const timeoutId = setTimeout(() => {
                this.pendingRequests.delete(key);
                this.request(url, options).then(resolve).catch(reject);
            }, delay);
            
            this.pendingRequests.set(key, timeoutId);
        });
    }

    /**
     * Batch multiple requests
     */
    batchRequests(requests) {
        return Promise.all(requests.map(req => 
            this.request(req.url, req.options)
        ));
    }

    /**
     * Main request method with optimizations
     */
    async request(url, options = {}) {
        const startTime = performance.now();
        const method = options.method || 'GET';
        const cacheKey = this.getCacheKey(url, method, options.body);
        
        // Check cache for GET requests
        if (this.shouldCache(method, url, options)) {
            const cached = this.getCachedResponse(cacheKey, options.cacheMaxAge);
            if (cached) {
                return Promise.resolve(cached);
            }
        }
        
        // Prepare headers
        const headers = {
            ...this.defaultHeaders,
            ...options.headers
        };
        
        // Handle different content types
        let body = options.body;
        if (body && typeof body === 'object' && !(body instanceof FormData)) {
            body = JSON.stringify(body);
        }
        
        const fetchOptions = {
            method,
            headers,
            body,
            ...options
        };
        
        try {
            this.performanceMetrics.totalRequests++;
            
            const response = await this.fetchWithRetry(url, fetchOptions);
            const data = await this.parseResponse(response);
            
            // Cache successful GET responses
            if (this.shouldCache(method, url, options) && response.ok) {
                this.setCachedResponse(cacheKey, data);
            }
            
            // Update performance metrics
            const responseTime = performance.now() - startTime;
            this.updatePerformanceMetrics(responseTime, true);
            
            return data;
            
        } catch (error) {
            this.performanceMetrics.failedRequests++;
            this.updatePerformanceMetrics(performance.now() - startTime, false);
            throw error;
        }
    }

    /**
     * Fetch with retry logic
     */
    async fetchWithRetry(url, options, attempt = 1) {
        try {
            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response;
            
        } catch (error) {
            if (attempt < this.retryAttempts && this.shouldRetry(error)) {
                await this.delay(this.retryDelay * attempt);
                return this.fetchWithRetry(url, options, attempt + 1);
            }
            throw error;
        }
    }

    /**
     * Check if error should trigger retry
     */
    shouldRetry(error) {
        // Retry on network errors or 5xx server errors
        return error.name === 'TypeError' || 
               (error.message.includes('HTTP 5'));
    }

    /**
     * Parse response based on content type
     */
    async parseResponse(response) {
        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else if (contentType && contentType.includes('text/')) {
            return await response.text();
        } else {
            return await response.blob();
        }
    }

    /**
     * Update performance metrics
     */
    updatePerformanceMetrics(responseTime, success) {
        if (success) {
            const currentAvg = this.performanceMetrics.averageResponseTime;
            const totalRequests = this.performanceMetrics.totalRequests;
            this.performanceMetrics.averageResponseTime = 
                (currentAvg * (totalRequests - 1) + responseTime) / totalRequests;
        }
    }

    /**
     * Utility delay function
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            cacheHitRate: this.performanceMetrics.cacheHits / this.performanceMetrics.totalRequests,
            cacheSize: this.cache.size
        };
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Preload common endpoints
     */
    preloadEndpoints(endpoints) {
        endpoints.forEach(endpoint => {
            this.request(endpoint, { method: 'GET' }).catch(() => {
                // Ignore preload errors
            });
        });
    }
}

// Create global instance
window.ajaxManager = new AjaxManager();

// Convenience methods for common operations
window.ajax = {
    get: (url, options = {}) => window.ajaxManager.request(url, { ...options, method: 'GET' }),
    post: (url, data, options = {}) => window.ajaxManager.request(url, { ...options, method: 'POST', body: data }),
    put: (url, data, options = {}) => window.ajaxManager.request(url, { ...options, method: 'PUT', body: data }),
    patch: (url, data, options = {}) => window.ajaxManager.request(url, { ...options, method: 'PATCH', body: data }),
    delete: (url, options = {}) => window.ajaxManager.request(url, { ...options, method: 'DELETE' }),
    
    // Debounced versions
    getDebounced: (url, options = {}, delay = 300) => window.ajaxManager.debouncedRequest(url, { ...options, method: 'GET' }, delay),
    
    // Batch operations
    batch: (requests) => window.ajaxManager.batchRequests(requests),
    
    // Performance
    metrics: () => window.ajaxManager.getPerformanceMetrics(),
    clearCache: () => window.ajaxManager.clearCache(),
    preload: (endpoints) => window.ajaxManager.preloadEndpoints(endpoints)
};

// Auto-preload common endpoints on page load
document.addEventListener('DOMContentLoaded', function() {
    const commonEndpoints = [
        '/api/notifications/unread-count/',
        '/api/notifications/recent/'
    ];
    
    // Delay preloading to not interfere with page load
    setTimeout(() => {
        window.ajax.preload(commonEndpoints);
    }, 1000);
});
