/**
 * Print Reports JavaScript
 * Handles print options and different paper sizes
 */

class PrintManager {
    constructor() {
        this.currentPaperSize = 'A4';
        this.currentOrientation = 'portrait';
        this.includeCharts = true;
        this.init();
    }

    init() {
        this.createPrintModal();
        this.bindEvents();
    }

    createPrintModal() {
        const modalHTML = `
            <div id="printOptionsModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Print Options</h3>
                        <div class="space-y-4">
                            <!-- Paper Size -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Paper Size</label>
                                <select id="paperSize" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    <option value="A4">A4 (210mm × 297mm)</option>
                                    <option value="A5">A5 (148mm × 210mm)</option>
                                    <option value="Letter">Letter (8.5" × 11")</option>
                                    <option value="Legal">Legal (8.5" × 14")</option>
                                </select>
                            </div>

                            <!-- Orientation -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Orientation</label>
                                <div class="flex space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="orientation" value="portrait" checked class="mr-2">
                                        <span class="text-sm">Portrait</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="orientation" value="landscape" class="mr-2">
                                        <span class="text-sm">Landscape</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Include Charts -->
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="includeCharts" checked class="mr-2">
                                    <span class="text-sm font-medium text-gray-700">Include Charts and Graphics</span>
                                </label>
                            </div>

                            <!-- Print Quality -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Print Quality</label>
                                <select id="printQuality" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    <option value="normal">Normal</option>
                                    <option value="high">High Quality</option>
                                    <option value="draft">Draft</option>
                                </select>
                            </div>

                            <!-- Color Options -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Color</label>
                                <div class="flex space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="colorMode" value="color" checked class="mr-2">
                                        <span class="text-sm">Color</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="colorMode" value="grayscale" class="mr-2">
                                        <span class="text-sm">Grayscale</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 mt-6">
                            <button type="button" onclick="printManager.closePrintModal()" 
                                    class="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
                                Cancel
                            </button>
                            <button type="button" onclick="printManager.printReport()" 
                                    class="px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-md hover:bg-primary-700">
                                <i class="fa-solid fa-print mr-1"></i>
                                Print Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to body if it doesn't exist
        if (!document.getElementById('printOptionsModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }
    }

    bindEvents() {
        // Replace existing print buttons with our custom handler
        document.addEventListener('click', (e) => {
            if (e.target.matches('button[onclick*="window.print"]') || 
                e.target.closest('button[onclick*="window.print"]')) {
                e.preventDefault();
                e.stopPropagation();
                this.showPrintModal();
            }
        });

        // Handle paper size changes
        document.addEventListener('change', (e) => {
            if (e.target.id === 'paperSize') {
                this.currentPaperSize = e.target.value;
                this.updatePrintStyles();
            }
        });

        // Handle orientation changes
        document.addEventListener('change', (e) => {
            if (e.target.name === 'orientation') {
                this.currentOrientation = e.target.value;
                this.updatePrintStyles();
            }
        });

        // Handle charts inclusion
        document.addEventListener('change', (e) => {
            if (e.target.id === 'includeCharts') {
                this.includeCharts = e.target.checked;
                this.toggleCharts();
            }
        });
    }

    showPrintModal() {
        document.getElementById('printOptionsModal').classList.remove('hidden');
    }

    closePrintModal() {
        document.getElementById('printOptionsModal').classList.add('hidden');
    }

    updatePrintStyles() {
        // Remove existing print size classes
        document.body.classList.remove('print-a4', 'print-a5', 'print-letter', 'print-legal');
        document.body.classList.remove('print-portrait', 'print-landscape');

        // Add new classes
        document.body.classList.add(`print-${this.currentPaperSize.toLowerCase()}`);
        document.body.classList.add(`print-${this.currentOrientation}`);

        // Update CSS custom properties for dynamic sizing
        const style = document.createElement('style');
        style.id = 'dynamic-print-styles';
        
        // Remove existing dynamic styles
        const existingStyle = document.getElementById('dynamic-print-styles');
        if (existingStyle) {
            existingStyle.remove();
        }

        let pageSize = 'A4';
        let margins = '0.5in';

        switch (this.currentPaperSize) {
            case 'A4':
                pageSize = 'A4';
                margins = '0.5in';
                break;
            case 'A5':
                pageSize = 'A5';
                margins = '0.4in';
                break;
            case 'Letter':
                pageSize = 'letter';
                margins = '0.5in';
                break;
            case 'Legal':
                pageSize = 'legal';
                margins = '0.5in';
                break;
        }

        style.textContent = `
            @media print {
                @page {
                    size: ${pageSize} ${this.currentOrientation};
                    margin: ${margins};
                }
            }
        `;

        document.head.appendChild(style);
    }

    toggleCharts() {
        const charts = document.querySelectorAll('canvas, .chart, .graph, .analytics-chart');
        charts.forEach(chart => {
            if (this.includeCharts) {
                chart.classList.remove('print-hide');
            } else {
                chart.classList.add('print-hide');
            }
        });
    }

    addPrintHeader() {
        // Add print-only header if it doesn't exist
        if (!document.querySelector('.print-header')) {
            const reportTitle = document.querySelector('h1')?.textContent || 'Report';
            const dateRange = document.querySelector('.text-sm.text-gray-600')?.textContent || '';
            
            const printHeader = document.createElement('div');
            printHeader.className = 'print-header print-only';
            printHeader.innerHTML = `
                <h1>${reportTitle}</h1>
                <div class="date-range">${dateRange}</div>
                <div class="generated-date">Generated on ${new Date().toLocaleDateString()}</div>
            `;
            
            document.body.insertBefore(printHeader, document.body.firstChild);
        }
    }

    addPrintFooter() {
        // Add print-only footer if it doesn't exist
        if (!document.querySelector('.print-footer')) {
            const printFooter = document.createElement('div');
            printFooter.className = 'print-footer print-only';
            printFooter.innerHTML = `
                <div>RadioMention Report - Page <span class="page-number"></span></div>
                <div>Generated on ${new Date().toLocaleString()}</div>
            `;
            
            document.body.appendChild(printFooter);
        }
    }

    printReport() {
        // Apply current settings
        this.updatePrintStyles();
        this.toggleCharts();
        this.addPrintHeader();
        this.addPrintFooter();

        // Close modal
        this.closePrintModal();

        // Add print-specific classes to body
        document.body.classList.add('printing');

        // Trigger print
        setTimeout(() => {
            window.print();
            
            // Clean up after print
            setTimeout(() => {
                document.body.classList.remove('printing');
            }, 1000);
        }, 500);
    }

    // Quick print functions for different sizes
    quickPrintA4() {
        this.currentPaperSize = 'A4';
        this.currentOrientation = 'portrait';
        this.printReport();
    }

    quickPrintA5() {
        this.currentPaperSize = 'A5';
        this.currentOrientation = 'portrait';
        this.printReport();
    }

    quickPrintLetter() {
        this.currentPaperSize = 'Letter';
        this.currentOrientation = 'portrait';
        this.printReport();
    }
}

// Initialize print manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.printManager = new PrintManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PrintManager;
}
