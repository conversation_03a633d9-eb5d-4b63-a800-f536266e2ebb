/**
 * AJAX Manager - IE11 Compatible Version
 * Centralized AJAX handling for RadioMention App with IE11 support
 */

function AjaxManager() {
    this.cache = new Map();
    this.requestQueue = [];
    this.isProcessingQueue = false;
    this.pendingRequests = new Map();
    this.retryAttempts = 3;
    this.retryDelay = 1000;
    
    // Initialize CSRF token
    this.csrfToken = this.getCSRFToken();
    
    // Request interceptor for common headers
    this.defaultHeaders = {
        'Content-Type': 'application/json',
        'X-CSRFToken': this.csrfToken,
        'X-Requested-With': 'XMLHttpRequest'
    };
    
    // Performance monitoring
    this.performanceMetrics = {
        totalRequests: 0,
        cacheHits: 0,
        averageResponseTime: 0,
        failedRequests: 0
    };
}

/**
 * Get CSRF token from various sources
 */
AjaxManager.prototype.getCSRFToken = function() {
    // Try meta tag first
    var metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) return metaToken.getAttribute('content');
    
    // Try form input
    var formToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (formToken) return formToken.value;
    
    // Try cookie
    var cookies = document.cookie.split(';');
    for (var i = 0; i < cookies.length; i++) {
        var cookie = cookies[i].trim().split('=');
        if (cookie[0] === 'csrftoken') return cookie[1];
    }
    
    return '';
};

/**
 * Generate cache key for request
 */
AjaxManager.prototype.getCacheKey = function(url, method, data) {
    var dataStr = data ? JSON.stringify(data) : '';
    return method + ':' + url + ':' + dataStr;
};

/**
 * Check if request should be cached
 */
AjaxManager.prototype.shouldCache = function(method, url, options) {
    options = options || {};
    if (options.noCache) return false;
    if (method !== 'GET') return false;
    
    // Cache API endpoints for a short time
    var cacheablePatterns = [
        '/api/notifications/',
        '/api/shows/',
        '/api/mentions/',
        '/api/presenters/'
    ];
    
    for (var i = 0; i < cacheablePatterns.length; i++) {
        if (url.indexOf(cacheablePatterns[i]) !== -1) {
            return true;
        }
    }
    return false;
};

/**
 * Get cached response if available and not expired
 */
AjaxManager.prototype.getCachedResponse = function(cacheKey, maxAge) {
    maxAge = maxAge || 30000; // 30 seconds default
    var cached = this.cache.get(cacheKey);
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > maxAge) {
        this.cache.delete(cacheKey);
        return null;
    }
    
    this.performanceMetrics.cacheHits++;
    return cached.data;
};

/**
 * Cache response
 */
AjaxManager.prototype.setCachedResponse = function(cacheKey, data) {
    this.cache.set(cacheKey, {
        data: data,
        timestamp: Date.now()
    });
    
    // Limit cache size
    if (this.cache.size > 100) {
        var firstKey = this.cache.keys().next().value;
        this.cache.delete(firstKey);
    }
};

/**
 * Debounced request - prevents duplicate requests
 */
AjaxManager.prototype.debouncedRequest = function(url, options, delay) {
    var self = this;
    options = options || {};
    delay = delay || 300;
    var key = this.getCacheKey(url, options.method || 'GET', options.body);
    
    // Cancel existing timeout for this request
    if (this.pendingRequests.has(key)) {
        clearTimeout(this.pendingRequests.get(key));
    }
    
    return new Promise(function(resolve, reject) {
        var timeoutId = setTimeout(function() {
            self.pendingRequests.delete(key);
            self.request(url, options).then(resolve).catch(reject);
        }, delay);
        
        self.pendingRequests.set(key, timeoutId);
    });
};

/**
 * Batch multiple requests
 */
AjaxManager.prototype.batchRequests = function(requests) {
    var self = this;
    var promises = [];
    for (var i = 0; i < requests.length; i++) {
        promises.push(self.request(requests[i].url, requests[i].options));
    }
    return Promise.all(promises);
};

/**
 * Main request method with optimizations
 */
AjaxManager.prototype.request = function(url, options) {
    var self = this;
    options = options || {};
    var startTime = performance.now();
    var method = options.method || 'GET';
    var cacheKey = this.getCacheKey(url, method, options.body);
    
    // Check cache for GET requests
    if (this.shouldCache(method, url, options)) {
        var cached = this.getCachedResponse(cacheKey, options.cacheMaxAge);
        if (cached) {
            return Promise.resolve(cached);
        }
    }
    
    // Prepare headers using Object.assign polyfill
    var headers = Object.assign({}, this.defaultHeaders, options.headers || {});
    
    // Handle different content types
    var body = options.body;
    if (body && typeof body === 'object' && !(body instanceof FormData)) {
        body = JSON.stringify(body);
    }
    
    var fetchOptions = Object.assign({
        method: method,
        headers: headers,
        body: body
    }, options);
    
    this.performanceMetrics.totalRequests++;
    
    return this.fetchWithRetry(url, fetchOptions)
        .then(function(response) {
            return self.parseResponse(response);
        })
        .then(function(data) {
            // Cache successful GET responses
            if (self.shouldCache(method, url, options)) {
                self.setCachedResponse(cacheKey, data);
            }
            
            // Update performance metrics
            var responseTime = performance.now() - startTime;
            self.updatePerformanceMetrics(responseTime, true);
            
            return data;
        })
        .catch(function(error) {
            self.performanceMetrics.failedRequests++;
            self.updatePerformanceMetrics(performance.now() - startTime, false);
            throw error;
        });
};

/**
 * Fetch with retry logic
 */
AjaxManager.prototype.fetchWithRetry = function(url, options, attempt) {
    var self = this;
    attempt = attempt || 1;
    
    return fetch(url, options)
        .then(function(response) {
            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }
            return response;
        })
        .catch(function(error) {
            if (attempt < self.retryAttempts && self.shouldRetry(error)) {
                return self.delay(self.retryDelay * attempt)
                    .then(function() {
                        return self.fetchWithRetry(url, options, attempt + 1);
                    });
            }
            throw error;
        });
};

/**
 * Check if error should trigger retry
 */
AjaxManager.prototype.shouldRetry = function(error) {
    // Retry on network errors or 5xx server errors
    return error.name === 'TypeError' || 
           (error.message.indexOf('HTTP 5') !== -1);
};

/**
 * Parse response based on content type
 */
AjaxManager.prototype.parseResponse = function(response) {
    var contentType = response.headers.get('content-type');
    
    if (contentType && contentType.indexOf('application/json') !== -1) {
        return response.json();
    } else if (contentType && contentType.indexOf('text/') !== -1) {
        return response.text();
    } else {
        return response.blob();
    }
};

/**
 * Update performance metrics
 */
AjaxManager.prototype.updatePerformanceMetrics = function(responseTime, success) {
    if (success) {
        var currentAvg = this.performanceMetrics.averageResponseTime;
        var totalRequests = this.performanceMetrics.totalRequests;
        this.performanceMetrics.averageResponseTime = 
            (currentAvg * (totalRequests - 1) + responseTime) / totalRequests;
    }
};

/**
 * Utility delay function
 */
AjaxManager.prototype.delay = function(ms) {
    return new Promise(function(resolve) {
        setTimeout(resolve, ms);
    });
};

/**
 * Get performance metrics
 */
AjaxManager.prototype.getPerformanceMetrics = function() {
    return Object.assign({}, this.performanceMetrics, {
        cacheHitRate: this.performanceMetrics.cacheHits / this.performanceMetrics.totalRequests,
        cacheSize: this.cache.size
    });
};

/**
 * Clear cache
 */
AjaxManager.prototype.clearCache = function() {
    this.cache.clear();
};

/**
 * Preload common endpoints
 */
AjaxManager.prototype.preloadEndpoints = function(endpoints) {
    var self = this;
    for (var i = 0; i < endpoints.length; i++) {
        this.request(endpoints[i], { method: 'GET' }).catch(function() {
            // Ignore preload errors
        });
    }
};

// Create global instance
window.ajaxManager = new AjaxManager();

// Convenience methods for common operations
window.ajax = {
    get: function(url, options) {
        options = options || {};
        return window.ajaxManager.request(url, Object.assign(options, { method: 'GET' }));
    },
    post: function(url, data, options) {
        options = options || {};
        return window.ajaxManager.request(url, Object.assign(options, { method: 'POST', body: data }));
    },
    put: function(url, data, options) {
        options = options || {};
        return window.ajaxManager.request(url, Object.assign(options, { method: 'PUT', body: data }));
    },
    patch: function(url, data, options) {
        options = options || {};
        return window.ajaxManager.request(url, Object.assign(options, { method: 'PATCH', body: data }));
    },
    delete: function(url, options) {
        options = options || {};
        return window.ajaxManager.request(url, Object.assign(options, { method: 'DELETE' }));
    },
    
    // Debounced versions
    getDebounced: function(url, options, delay) {
        options = options || {};
        delay = delay || 300;
        return window.ajaxManager.debouncedRequest(url, Object.assign(options, { method: 'GET' }), delay);
    },
    
    // Batch operations
    batch: function(requests) {
        return window.ajaxManager.batchRequests(requests);
    },
    
    // Performance
    metrics: function() {
        return window.ajaxManager.getPerformanceMetrics();
    },
    clearCache: function() {
        return window.ajaxManager.clearCache();
    },
    preload: function(endpoints) {
        return window.ajaxManager.preloadEndpoints(endpoints);
    }
};

// Auto-preload common endpoints on page load
document.addEventListener('DOMContentLoaded', function() {
    var commonEndpoints = [
        '/api/notifications/unread-count/',
        '/api/notifications/recent/'
    ];
    
    // Delay preloading to not interfere with page load
    setTimeout(function() {
        window.ajax.preload(commonEndpoints);
    }, 1000);
});
