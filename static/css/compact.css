/* Compact styling overrides */

/* Reduce default spacing */
.compact-spacing {
    padding: 0.75rem !important;
}

.compact-spacing-sm {
    padding: 0.5rem !important;
}

/* Smaller text sizes */
.text-compact {
    font-size: 0.875rem !important;
}

.text-compact-sm {
    font-size: 0.75rem !important;
}

/* Compact tables */
.table-compact td,
.table-compact th {
    padding: 0.5rem 0.75rem !important;
}

/* Compact cards */
.card-compact {
    padding: 1rem !important;
}

/* Compact buttons */
.btn-compact {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
}

/* Compact forms */
.form-compact input,
.form-compact select,
.form-compact textarea {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
}

/* Compact navigation */
.nav-compact a {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
}

/* Compact icons */
.icon-compact {
    width: 1rem !important;
    height: 1rem !important;
}

/* Compact grid gaps */
.grid-compact {
    gap: 0.75rem !important;
}

/* Compact sections */
.section-compact {
    margin-bottom: 1.5rem !important;
}

/* Compact headers */
.header-compact {
    padding: 0.75rem 1rem !important;
}

/* FontAwesome Icon Fixes */
.fa-solid, .fas, .fa-regular, .far, .fa-brands, .fab {
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
}

/* Calendar specific fixes */
.calendar-container .mention-item {
    position: relative !important;
    z-index: 1 !important;
}

.calendar-container .notification {
    /* Prevent calendar items from being treated as notifications */
    pointer-events: auto !important;
}

.calendar-day .notification {
    /* Prevent calendar day items from being auto-hidden */
    pointer-events: auto !important;
}

/* Prevent auto-hide on calendar elements */
.calendar-container .mention-item,
.calendar-day .schedule-item,
.mention-detail-content {
    /* These should never be auto-hidden */
    position: relative !important;
}

/* Auto-refresh indicator */
.refresh-indicator {
    animation: spin 2s linear infinite !important;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Protect calendar and UI elements from auto-hide scripts */
.calendar-container,
.calendar-day,
.mention-item,
.recurring-mention,
.stats-card,
.schedule-item {
    /* Mark these as persistent to prevent auto-hide */
    position: relative !important;
}

.calendar-container *,
.calendar-day *,
.mention-item *,
.recurring-mention *,
.stats-card * {
    /* Prevent child elements from being auto-hidden */
    pointer-events: auto !important;
}

/* Ensure calendar items maintain their styling */
.bg-blue-100.mention-item,
.bg-green-100.mention-item,
.bg-yellow-100.mention-item,
.bg-red-100.mention-item {
    opacity: 1 !important;
    transition: none !important;
}







/* Responsive compact adjustments */
@media (max-width: 768px) {
    .compact-spacing {
        padding: 0.5rem !important;
    }

    .card-compact {
        padding: 0.75rem !important;
    }

    /* Mobile-specific sidebar styling */
    #sidebar nav {
        padding-bottom: 2rem !important;
    }

    /* Improve touch targets on mobile */
    #sidebar nav a {
        padding: 0.75rem 1rem !important;
        margin: 0.125rem 0 !important;
        position: relative !important;
    }

    /* Make section headers more compact on mobile */
    #sidebar nav h3 {
        padding: 0.5rem 1rem !important;
        font-size: 0.75rem !important;
    }

    /* Add visual feedback for touch interactions */
    #sidebar nav a:active {
        background-color: rgba(0, 0, 0, 0.05) !important;
        transform: scale(0.98) !important;
        transition: all 0.1s ease !important;
    }

    /* Ensure proper spacing for badges and indicators */
    #sidebar nav a .ml-auto {
        margin-left: auto !important;
        flex-shrink: 0 !important;
    }
}


