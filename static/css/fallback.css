/**
 * Comprehensive Fallback CSS - Complete Tailwind CSS replacement
 * Provides full styling when Tailwind CSS fails to load
 * Covers all classes used in the RadioMention application
 */

/* Reset and base styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
    background-color: #f9fafb;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Color Variables */
:root {
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;
}

/* Layout - Flexbox */
.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

/* Flex alignment */
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.items-baseline { align-items: baseline; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Grid */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
.grid-cols-8 { grid-template-columns: repeat(8, minmax(0, 1fr)); }

.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }

/* Gap */
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-px { gap: 1px; }

.overflow-y-auto {
    overflow-y: auto;
}

/* Spacing - Padding */
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }

.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-9 { padding-left: 2.25rem; padding-right: 2.25rem; }

.py-0\.5 { padding-top: 0.125rem; padding-bottom: 0.125rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-2\.5 { padding-top: 0.625rem; padding-bottom: 0.625rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }

.pl-9 { padding-left: 2.25rem; }
.pr-4 { padding-right: 1rem; }
.pb-4 { padding-bottom: 1rem; }

/* Spacing - Margin */
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }

.mx-auto { margin-left: auto; margin-right: auto; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }

/* Space between elements */
.space-x-4 > * + * { margin-left: 1rem; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-4 > * + * { margin-top: 1rem; }

/* Width and Height */
.w-2 { width: 0.5rem; }
.w-5 { width: 1.25rem; }
.w-8 { width: 2rem; }
.w-20 { width: 5rem; }
.w-64 { width: 16rem; }
.w-80 { width: 20rem; }
.w-full { width: 100%; }

.h-2 { height: 0.5rem; }
.h-5 { height: 1.25rem; }
.h-8 { height: 2rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-24 { height: 6rem; }
.h-64 { height: 16rem; }
.h-screen { height: 100vh; }

.min-h-16 { min-height: 4rem; }
.min-h-24 { min-height: 6rem; }

.max-h-64 { max-height: 16rem; }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.top-0 { top: 0; }
.right-0 { right: 0; }
.top-1\/2 { top: 50%; }
.left-3 { left: 0.75rem; }
.transform { transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.-translate-y-1\/2 { --tw-translate-y: -50%; transform: translateY(-50%); }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-y-auto { overflow-y: auto; }

/* Colors - Background */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }

.bg-primary-50 { background-color: var(--primary-50); }
.bg-primary-100 { background-color: var(--primary-100); }
.bg-primary-500 { background-color: var(--primary-500); }
.bg-primary-600 { background-color: var(--primary-600); }

.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-blue-200 { background-color: #bfdbfe; }
.bg-blue-600 { background-color: #2563eb; }

.bg-green-100 { background-color: #dcfce7; }
.bg-yellow-100 { background-color: #fef3c7; }
.bg-red-50 { background-color: #fef2f2; }
.bg-red-100 { background-color: #fee2e2; }
.bg-red-500 { background-color: #ef4444; }

.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.from-primary-500 { --tw-gradient-from: var(--primary-500); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(14, 165, 233, 0)); }
.to-primary-600 { --tw-gradient-to: var(--primary-600); }

/* Colors - Text */
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1f2937; }
.text-gray-900 { color: #111827; }

.text-white { color: #ffffff; }
.text-primary-600 { color: var(--primary-600); }
.text-primary-700 { color: var(--primary-700); }

.text-blue-500 { color: #3b82f6; }
.text-blue-600 { color: #2563eb; }
.text-blue-700 { color: #1d4ed8; }
.text-blue-800 { color: #1e40af; }

.text-green-600 { color: #16a34a; }
.text-green-800 { color: #166534; }
.text-yellow-600 { color: #ca8a04; }
.text-yellow-800 { color: #92400e; }
.text-red-600 { color: #dc2626; }
.text-red-800 { color: #991b1b; }

/* Borders */
.border { border: 1px solid #e5e7eb; }
.border-b { border-bottom: 1px solid #e5e7eb; }
.border-r { border-right: 1px solid #e5e7eb; }
.border-l { border-left: 1px solid #e5e7eb; }
.border-r-2 { border-right: 2px solid; }

.border-gray-100 { border-color: #f3f4f6; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }

.border-primary-500 { border-color: var(--primary-500); }
.border-blue-200 { border-color: #bfdbfe; }
.border-blue-300 { border-color: #93c5fd; }
.border-green-300 { border-color: #86efac; }
.border-yellow-300 { border-color: #fde047; }
.border-red-300 { border-color: #fca5a5; }

.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

/* Typography */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.uppercase { text-transform: uppercase; }
.tracking-wider { letter-spacing: 0.05em; }

.truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Focus and Interaction */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); }
.focus\:ring-primary-500:focus { box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.5); }
.focus\:border-primary-500:focus { border-color: var(--primary-500); }

.hover\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\:bg-red-50:hover { background-color: #fef2f2; }
.hover\:text-gray-600:hover { color: #4b5563; }
.hover\:text-gray-700:hover { color: #374151; }
.hover\:text-gray-900:hover { color: #111827; }
.hover\:text-red-900:hover { color: #7f1d1d; }

.group:hover .group-hover\:text-gray-600 { color: #4b5563; }

/* Transitions and Animations */
.transition-all { transition: all 0.15s ease-in-out; }
.duration-200 { transition-duration: 200ms; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Cursor */
.cursor-pointer { cursor: pointer; }

/* Display */
.hidden { display: none !important; }
.block { display: block; }
.inline-flex { display: inline-flex; }

/* Z-index */
.z-50 { z-index: 50; }
.z-1000 { z-index: 1000; }

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #2563eb;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #1d4ed8;
}

.btn-secondary {
    background-color: #6b7280;
    color: #ffffff;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

/* Forms */
.form-input {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.form-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Cards */
.card {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Sidebar */
.sidebar {
    width: 16rem;
    background-color: #ffffff;
    border-right: 1px solid #e5e7eb;
    height: 100vh;
    overflow-y: auto;
}

/* Navigation */
.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #4b5563;
    text-decoration: none;
    transition: all 0.2s;
}

.nav-link:hover {
    background-color: #f3f4f6;
    color: #1f2937;
}

.nav-link.active {
    background-color: #eff6ff;
    color: #2563eb;
}

/* Calendar */
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #e5e7eb;
}

.calendar-day {
    background-color: #ffffff;
    padding: 0.25rem;
    min-height: 6rem;
}

/* Time slots */
.time-slot {
    position: relative;
    height: 4rem;
    border-bottom: 1px solid #f3f4f6;
}

/* Mention items */
.mention-item {
    background-color: #dbeafe;
    border: 1px solid #93c5fd;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    margin-bottom: 0.25rem;
    cursor: pointer;
    transition: all 0.2s;
}

.mention-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Status colors */
.mention-completed {
    background-color: #dcfce7;
    border-color: #86efac;
    color: #166534;
}

.mention-pending {
    background-color: #fef3c7;
    border-color: #fde047;
    color: #92400e;
}

.mention-cancelled {
    background-color: #fee2e2;
    border-color: #fca5a5;
    color: #991b1b;
}

/* Shadows */
.shadow-sm { box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1); }

/* Form Controls */
input[type="text"], input[type="email"], input[type="password"], textarea, select {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #374151;
    background-color: #ffffff;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Responsive Design */
@media (min-width: 768px) {
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
    .lg\:col-span-2 { grid-column: span 2 / span 2; }
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
    }

    .text-xs { font-size: 0.625rem; }
    .p-4 { padding: 0.5rem; }

    .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
    .gap-4 { gap: 1rem; }
}
