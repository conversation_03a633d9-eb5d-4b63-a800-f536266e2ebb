 /* Override base styles for full-screen design tool */
    .main-content {
      margin: 0 !important;
      padding: 0 !important;
      max-width: none !important;
    }
    
    .container {
      max-width: none !important;
      padding: 0 !important;
    }
    
    /* Design tool specific styles */
    .design-tool-container {
      height: calc(100vh - 60px); /* Account for top navigation */
      display: flex;
      flex-direction: column;
      background: #ffffff;
      overflow: hidden;
    }
    
    /* Header Toolbar */
    .header-toolbar {
      height: 60px;
      background: #ffffff;
      border-bottom: 1px solid #e5e7eb;
      display: flex;
      align-items: center;
      padding: 0 16px;
      justify-content: space-between;
      flex-shrink: 0;
    }
    
    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 24px;
    }
    
    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 8px;
      padding-right: 24px;
      border-right: 1px solid #e5e7eb;
    }
    
    .toolbar-group:last-child {
      border-right: none;
      padding-right: 0;
    }
    
    .tool-btn {
      width: 36px;
      height: 36px;
      border: 1px solid #e5e7eb;
      background: #ffffff;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      color: #6b7280;
    }
    
    .tool-btn:hover {
      background: #f3f4f6;
      border-color: #d1d5db;
    }
    
    .tool-btn.active {
      background: #3b82f6;
      border-color: #3b82f6;
      color: #ffffff;
    }
    
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    .page-size-select {
      padding: 8px 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      background: #ffffff;
      font-size: 14px;
    }
    
    .action-btn {
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      border: 1px solid transparent;
    }
    
    .btn-secondary {
      background: #f3f4f6;
      color: #374151;
      border-color: #e5e7eb;
    }
    
    .btn-secondary:hover {
      background: #e5e7eb;
    }
    
    .btn-primary {
      background: #3b82f6;
      color: #ffffff;
    }
    
    .btn-primary:hover {
      background: #2563eb;
    }
    
    /* Main Content */
    .main-design-content {
      flex: 1;
      display: flex;
      overflow: hidden;
    }
    
    /* Left Sidebar */
    .left-sidebar {
      width: 240px;
      background: #ffffff;
      border-right: 1px solid #e5e7eb;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
    }
    
    .sidebar-section {
      padding: 16px;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .sidebar-section h3 {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 12px;
    }
    
    .tools-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }
    
    .tool-item {
      padding: 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s;
      background: #ffffff;
    }
    
    .tool-item:hover {
      background: #f9fafb;
      border-color: #d1d5db;
    }
    
    .tool-item.active {
      background: #eff6ff;
      border-color: #3b82f6;
      color: #3b82f6;
    }
    
    .tool-item i {
      font-size: 18px;
      margin-bottom: 4px;
      display: block;
    }
    
    .tool-item span {
      font-size: 12px;
      font-weight: 500;
    }
    
    .template-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    
    .template-item {
      padding: 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;
      background: #ffffff;
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    .template-item:hover {
      background: #f9fafb;
      border-color: #d1d5db;
    }
    
    .template-icon {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      color: #ffffff;
    }
    
    .template-info h4 {
      font-size: 13px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 2px;
    }
    
    .template-info p {
      font-size: 11px;
      color: #6b7280;
    }
    
    .layers-list {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
    
    .layer-item {
      padding: 8px 12px;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      background: #ffffff;
    }
    
    .layer-left {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    /* Canvas Area */
    .canvas-area {
      flex: 1;
      background: #f8fafc;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }
    
    .canvas-page {
      width: 595px;
      height: 842px;
      background: #ffffff;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
      position: relative;
      transform: scale(0.7);
      transform-origin: center;
    }
    
    .page-content {
      position: absolute;
      top: 20px;
      left: 20px;
      right: 20px;
      bottom: 20px;
      border: 2px dashed #cbd5e1;
      border-radius: 4px;
    }
    
    .zoom-controls {
      position: absolute;
      bottom: 20px;
      right: 20px;
      background: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      padding: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .zoom-btn {
      width: 28px;
      height: 28px;
      border: none;
      background: #f9fafb;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;
    }
    
    .zoom-btn:hover {
      background: #f3f4f6;
    }
    
    .zoom-level {
      font-size: 12px;
      font-weight: 500;
      color: #374151;
      min-width: 40px;
      text-align: center;
    }
    
    /* Right Properties Panel */
    .properties-panel {
      width: 300px;
      background: #ffffff;
      border-left: 1px solid #e5e7eb;
      overflow-y: auto;
    }
    
    .property-section {
      padding: 16px;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .property-section h3 {
      font-size: 14px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 12px;
    }
    
    .property-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
    }
    
    .property-row label {
      font-size: 12px;
      color: #6b7280;
      font-weight: 500;
    }
    
    .property-input {
      width: 80px;
      padding: 4px 8px;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
      font-size: 12px;
    }
    
    .color-input {
      width: 32px;
      height: 24px;
      border: 1px solid #e5e7eb;
      border-radius: 4px;
      cursor: pointer;
    }
    
    /* Canvas Elements */
    .canvas-element {
      transition: all 0.2s ease;
    }
    
    .canvas-element:hover {
      border-color: #3b82f6 !important;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
    
    .canvas-element.selected {
      border-color: #3b82f6 !important;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }

    /* Text editing styles */
    .canvas-element textarea {
      font-family: inherit;
      resize: both;
      min-width: 100px;
      min-height: 30px;
      max-width: 500px;
      max-height: 300px;
      border: none;
      background: transparent;
      outline: 2px solid #3b82f6;
      outline-offset: -2px;
      border-radius: 2px;
      padding: 4px;
      box-sizing: border-box;
    }

    /* Multi-line text display */
    .canvas-element .element-content span {
      display: block;
      width: 100%;
      text-align: center;
      line-height: 1.4;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    
    /* Notifications */
    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    .notification {
      animation: slideIn 0.3s ease;
    }

    /* Template Loading State */
    .template-loading {
      text-align: center;
      padding: 20px;
      color: #6b7280;
    }

    .template-loading i {
      font-size: 16px;
      margin-bottom: 8px;
    }

    .template-loading p {
      margin: 8px 0 0 0;
      font-size: 12px;
    }
