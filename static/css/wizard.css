/* Signup Wizard Styles */

/* Plan Radio Button Styling */
.plan-radio input[type="radio"] {
    display: none;
}

.plan-radio label {
    display: block;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.plan-radio input[type="radio"]:checked + label {
    border-color: #3b82f6;
    background-color: #eff6ff;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.plan-radio label:hover {
    border-color: #93c5fd;
    background-color: #f8fafc;
}

/* Form Field Enhancements */
.form-field {
    position: relative;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
    outline: none;
    ring: 2px;
    ring-color: #3b82f6;
    border-color: #3b82f6;
}

/* Checkbox Styling */
input[type="checkbox"] {
    appearance: none;
    background-color: #fff;
    margin: 0;
    font: inherit;
    color: currentColor;
    width: 1rem;
    height: 1rem;
    border: 2px solid #d1d5db;
    border-radius: 0.25rem;
    transform: translateY(-0.075em);
    display: grid;
    place-content: center;
}

input[type="checkbox"]:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

input[type="checkbox"]:checked::before {
    content: "✓";
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

/* Progress Bar Animation */
.progress-bar {
    transition: width 0.5s ease-in-out;
}

/* Step Indicator */
.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.step-indicator .step {
    flex: 1;
    text-align: center;
    position: relative;
}

.step-indicator .step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 1rem;
    left: 50%;
    width: 100%;
    height: 2px;
    background-color: #e5e7eb;
    z-index: -1;
}

.step-indicator .step.completed::after {
    background-color: #10b981;
}

.step-indicator .step.active::after {
    background-color: #3b82f6;
}

.step-indicator .step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: #e5e7eb;
    color: #6b7280;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.step-indicator .step.completed .step-number {
    background-color: #10b981;
    color: white;
}

.step-indicator .step.active .step-number {
    background-color: #3b82f6;
    color: white;
}

/* File Upload Styling */
input[type="file"] {
    padding: 0.75rem;
    border: 2px dashed #d1d5db;
    border-radius: 0.5rem;
    background-color: #f9fafb;
    transition: all 0.2s ease;
}

input[type="file"]:hover {
    border-color: #9ca3af;
    background-color: #f3f4f6;
}

input[type="file"]:focus {
    outline: none;
    border-color: #3b82f6;
    background-color: #eff6ff;
}

/* Summary Cards */
.summary-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-in-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .plan-radio label {
        padding: 0.75rem;
        font-size: 0.875rem;
    }
    
    .summary-card {
        padding: 1rem;
    }
    
    .step-indicator {
        flex-direction: column;
        gap: 1rem;
    }
    
    .step-indicator .step:not(:last-child)::after {
        display: none;
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success States */
.success-highlight {
    background-color: #dcfce7;
    border-color: #16a34a;
    color: #15803d;
}

/* Error States */
.error-highlight {
    background-color: #fef2f2;
    border-color: #dc2626;
    color: #dc2626;
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #1f2937;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.875rem;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}
