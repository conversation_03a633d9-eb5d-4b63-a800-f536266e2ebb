/**
 * Print Styles for RadioMention Reports
 * Supports A4, A5, Letter, and Legal paper sizes
 */

/* Base print styles */
@media print {
    /* Reset margins and padding */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* Hide elements that shouldn't be printed */
    .no-print,
    .print-hide,
    nav,
    .sidebar,
    .header,
    .navigation,
    .btn,
    button,
    .modal,
    .dropdown,
    .tooltip,
    .popover,
    .alert-dismissible .close,
    .pagination,
    .breadcrumb,
    .navbar,
    .footer,
    .print-button,
    .back-button,
    .action-buttons,
    .filter-controls,
    .search-box,
    .sort-controls {
        display: none !important;
    }

    /* Show print-only elements */
    .print-only,
    .print-show {
        display: block !important;
    }

    /* Base page setup */
    @page {
        margin: 0.5in;
        size: A4;
    }

    /* Body styles for print */
    body {
        font-family: 'Times New Roman', serif !important;
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: #000 !important;
        background: white !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Container adjustments */
    .container,
    .max-w-7xl,
    .max-w-6xl,
    .max-w-5xl {
        max-width: none !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Typography for print */
    h1 {
        font-size: 18pt !important;
        font-weight: bold !important;
        margin: 0 0 12pt 0 !important;
        page-break-after: avoid !important;
    }

    h2 {
        font-size: 16pt !important;
        font-weight: bold !important;
        margin: 12pt 0 8pt 0 !important;
        page-break-after: avoid !important;
    }

    h3 {
        font-size: 14pt !important;
        font-weight: bold !important;
        margin: 10pt 0 6pt 0 !important;
        page-break-after: avoid !important;
    }

    h4, h5, h6 {
        font-size: 12pt !important;
        font-weight: bold !important;
        margin: 8pt 0 4pt 0 !important;
        page-break-after: avoid !important;
    }

    p {
        font-size: 12pt !important;
        margin: 0 0 6pt 0 !important;
        orphans: 3 !important;
        widows: 3 !important;
    }

    /* Table styles for print */
    table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 12pt 0 !important;
        page-break-inside: avoid !important;
    }

    th, td {
        border: 1pt solid #000 !important;
        padding: 4pt 6pt !important;
        font-size: 11pt !important;
        text-align: left !important;
        vertical-align: top !important;
    }

    th {
        background-color: #f0f0f0 !important;
        font-weight: bold !important;
        page-break-after: avoid !important;
    }

    tr {
        page-break-inside: avoid !important;
    }

    /* Card layouts for print */
    .bg-white,
    .card,
    .report-section {
        background: white !important;
        border: 1pt solid #ccc !important;
        margin: 12pt 0 !important;
        padding: 12pt !important;
        page-break-inside: avoid !important;
        box-shadow: none !important;
    }

    /* Grid layouts */
    .grid,
    .grid-cols-1,
    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4,
    .md\\:grid-cols-2,
    .lg\\:grid-cols-3,
    .lg\\:grid-cols-4 {
        display: block !important;
    }

    .grid > * {
        margin-bottom: 12pt !important;
        page-break-inside: avoid !important;
    }

    /* Flexbox adjustments */
    .flex {
        display: block !important;
    }

    .flex-1 {
        width: 100% !important;
    }

    /* Spacing adjustments */
    .space-y-4 > * + *,
    .space-y-6 > * + * {
        margin-top: 8pt !important;
    }

    /* Page breaks */
    .page-break-before {
        page-break-before: always !important;
    }

    .page-break-after {
        page-break-after: always !important;
    }

    .page-break-inside-avoid {
        page-break-inside: avoid !important;
    }

    /* Print header */
    .print-header {
        display: block !important;
        text-align: center !important;
        margin-bottom: 24pt !important;
        border-bottom: 2pt solid #000 !important;
        padding-bottom: 12pt !important;
    }

    .print-header h1 {
        font-size: 20pt !important;
        margin: 0 !important;
    }

    .print-header .date-range {
        font-size: 12pt !important;
        color: #666 !important;
        margin-top: 6pt !important;
    }

    /* Print footer */
    .print-footer {
        display: block !important;
        text-align: center !important;
        margin-top: 24pt !important;
        border-top: 1pt solid #ccc !important;
        padding-top: 12pt !important;
        font-size: 10pt !important;
        color: #666 !important;
    }
}

/* A4 Paper Size (210mm × 297mm) */
@media print {
    @page {
        size: A4;
        margin: 0.5in;
    }
}

/* A5 Paper Size (148mm × 210mm) */
@media print and (max-width: 148mm) {
    @page {
        size: A5;
        margin: 0.4in;
    }
    
    body {
        font-size: 10pt !important;
    }
    
    h1 {
        font-size: 14pt !important;
    }
    
    h2 {
        font-size: 12pt !important;
    }
    
    h3 {
        font-size: 11pt !important;
    }
    
    th, td {
        font-size: 9pt !important;
        padding: 2pt 4pt !important;
    }
}

/* Letter Paper Size (8.5" × 11") */
.print-letter {
    @page {
        size: letter;
        margin: 0.5in;
    }
}

/* Legal Paper Size (8.5" × 14") */
.print-legal {
    @page {
        size: legal;
        margin: 0.5in;
    }
}
