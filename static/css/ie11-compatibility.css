/**
 * IE11 Compatibility Styles
 * Fallback styles for Internet Explorer 11
 */

/* IE11 specific styles */
.browser-ie {
    /* Add IE-specific overrides here */
}

/* Unsupported browser styles */
.browser-unsupported {
    /* Add styles for unsupported browsers */
}

/* Flexbox fallbacks for IE11 */
.browser-ie .flex {
    display: -ms-flexbox;
    display: flex;
}

.browser-ie .flex-1 {
    -ms-flex: 1 1 0%;
    flex: 1 1 0%;
}

.browser-ie .flex-col {
    -ms-flex-direction: column;
    flex-direction: column;
}

.browser-ie .flex-row {
    -ms-flex-direction: row;
    flex-direction: row;
}

.browser-ie .items-center {
    -ms-flex-align: center;
    align-items: center;
}

.browser-ie .justify-center {
    -ms-flex-pack: center;
    justify-content: center;
}

.browser-ie .justify-between {
    -ms-flex-pack: justify;
    justify-content: space-between;
}

/* Grid fallbacks for IE11 */
.browser-ie .grid {
    display: -ms-grid;
    display: grid;
}

/* CSS Variables fallback for IE11 */
.browser-ie {
    /* Define fallback colors */
    --primary-color: #3b82f6;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

/* Animation fallbacks */
.browser-ie .animate-pulse {
    /* Simplified pulse animation for IE11 */
    animation: ie-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes ie-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

.browser-ie .animate-spin {
    /* Simplified spin animation for IE11 */
    animation: ie-spin 1s linear infinite;
}

@keyframes ie-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Transform fallbacks */
.browser-ie .transform {
    -ms-transform: translateZ(0);
    transform: translateZ(0);
}

.browser-ie .scale-105 {
    -ms-transform: scale(1.05);
    transform: scale(1.05);
}

.browser-ie .rotate-180 {
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

/* Transition fallbacks */
.browser-ie .transition {
    -ms-transition: all 0.15s ease-in-out;
    transition: all 0.15s ease-in-out;
}

.browser-ie .transition-colors {
    -ms-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

/* Object-fit fallbacks */
.browser-ie .object-cover {
    /* IE11 doesn't support object-fit, use background-image instead */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* Backdrop-filter fallbacks */
.browser-ie .backdrop-blur {
    /* IE11 doesn't support backdrop-filter */
    background-color: rgba(255, 255, 255, 0.8);
}

/* Custom properties fallbacks */
.browser-ie .bg-primary {
    background-color: #3b82f6;
}

.browser-ie .text-primary {
    color: #3b82f6;
}

.browser-ie .border-primary {
    border-color: #3b82f6;
}

/* Focus styles for IE11 */
.browser-ie button:focus,
.browser-ie input:focus,
.browser-ie select:focus,
.browser-ie textarea:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Drag and drop visual feedback for IE11 */
.browser-ie .dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.browser-ie .drag-over {
    border: 2px dashed #3b82f6;
    background-color: rgba(59, 130, 246, 0.1);
}

.browser-ie .drop-zone {
    border: 1px dashed #6b7280;
    background-color: rgba(107, 114, 128, 0.05);
}

/* Notification styles for IE11 */
.browser-ie .notification-toast {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease-in-out;
}

/* Loading states for IE11 */
.browser-ie .loading {
    position: relative;
    overflow: hidden;
}

.browser-ie .loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: ie-loading 1.5s infinite;
}

@keyframes ie-loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Print styles for IE11 */
@media print {
    .browser-ie {
        color-adjust: exact;
        -webkit-print-color-adjust: exact;
    }
}

/* High contrast mode support for IE11 */
@media screen and (-ms-high-contrast: active) {
    .browser-ie .bg-primary {
        background-color: ButtonText;
    }
    
    .browser-ie .text-primary {
        color: ButtonText;
    }
    
    .browser-ie .border-primary {
        border-color: ButtonText;
    }
}
