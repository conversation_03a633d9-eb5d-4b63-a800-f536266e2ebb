#!/bin/bash
# Script to enable HTTPS for the appradio application

set -e

# Check if domain name is provided
if [ -z "$1" ]; then
  echo "Please provide your domain name as an argument"
  echo "Usage: ./enable-https.sh yourdomain.com"
  exit 1
fi

DOMAIN_NAME=$1
export DOMAIN_NAME

# Load environment variables from .env.production if it exists
if [ -f ".env.production" ]; then
    echo "Loading environment variables from .env.production..."
    set -a  # automatically export all variables
    source .env.production
    set +a
fi

# Create required directories
echo "Creating directories for certificates..."
mkdir -p docker/certbot/conf
mkdir -p docker/certbot/www
mkdir -p docker/ssl
mkdir -p logs/nginx

# Export environment variables
echo "Setting domain name to $DOMAIN_NAME"
export NGINX_SERVER_NAME=$DOMAIN_NAME

# Stop any running services first
echo "Stopping any running services..."
DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose down 2>/dev/null || true

# Backup current nginx config and use HTTP-only config for initial setup
echo "Setting up HTTP-only nginx configuration for certificate request..."
if [ -f "docker/nginx/default.conf" ]; then
    cp docker/nginx/default.conf docker/nginx/default.conf.ssl-backup-temp
fi
cp docker/nginx/default.conf.http-only docker/nginx/default.conf

# Start services with HTTP-only configuration
echo "Starting services with HTTP-only configuration..."
DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml up -d web db redis nginx certbot

# Wait for services to start
echo "Waiting for services to start..."
sleep 15

# Check if nginx is running
if ! DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml ps nginx | grep -q "Up"; then
    echo "Error: Nginx failed to start. Checking logs..."
    DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml logs nginx
    exit 1
fi

echo "Services started successfully. Nginx is running on HTTP mode."

# Request Let's Encrypt certificate
echo "Requesting SSL certificate from Let's Encrypt..."
DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml run --rm certbot \
  certonly --webroot \
  --webroot-path=/var/www/certbot \
  --email admin@$DOMAIN_NAME \
  --agree-tos \
  --no-eff-email \
  --force-renewal \
  -d $DOMAIN_NAME

# Check if certificate was created successfully
if [ ! -f "docker/certbot/conf/live/$DOMAIN_NAME/fullchain.pem" ]; then
    echo "Error: SSL certificate was not created successfully"
    echo "Please check the certbot logs and ensure your domain is pointing to this server"

    # Restore original nginx config if it exists
    if [ -f "docker/nginx/default.conf.ssl-backup-temp" ]; then
        mv docker/nginx/default.conf.ssl-backup-temp docker/nginx/default.conf
    fi
    exit 1
fi

echo "SSL certificate created successfully!"

# Restore SSL nginx configuration
echo "Restoring SSL nginx configuration..."
if [ -f "docker/nginx/default.conf.ssl-backup-temp" ]; then
    mv docker/nginx/default.conf.ssl-backup-temp docker/nginx/default.conf
else
    # If no backup exists, copy from the SSL backup file
    cp docker/nginx/default.conf.ssl-backup docker/nginx/default.conf
fi

# Restart services with SSL
echo "Restarting all services with SSL enabled..."
DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml down
DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml up -d

# Wait for services to fully start
echo "Waiting for services to fully start with SSL..."
sleep 10

# Verify SSL setup
echo "Verifying SSL setup..."
if DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml ps | grep -q "Up"; then
    echo "✅ Services are running"
else
    echo "⚠️  Some services may not be running properly"
    DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml ps
fi

echo ""
echo "======================================================"
echo "HTTPS has been enabled for $DOMAIN_NAME"
echo "Your site should now be accessible at https://$DOMAIN_NAME"
echo ""
echo "Certificate location: docker/certbot/conf/live/$DOMAIN_NAME/"
echo "Certificate will auto-renew via certbot-cron service"
echo ""
echo "To check SSL certificate status:"
echo "  DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml run --rm certbot certificates"
echo "======================================================"
