# Production Environment Configuration for AppRadio

# Django Core Settings
SECRET_KEY=django-insecure-f*fjv_s7!(4s8&z$_06=ug77%4(qq9hfvuw8zfm4o9@=4tq_t_
DEBUG=False
DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production

# Domain Configuration (Required for SSL)
DOMAIN=radiocity.ugapp.net
NGINX_SERVER_NAME=radiocity.ugapp.net
ALLOWED_HOSTS=radiocity.ugapp.net,www.radiocity.ugapp.net,localhost,127.0.0.1

# SSL Configuration
USE_TLS=True
DISABLE_SSL_REDIRECT=False

# Database Configuration
POSTGRES_DB=radio_mentions
POSTGRES_USER=postgres
POSTGRES_PASSWORD=SecureP@ssw0rd2025!
POSTGRES_HOST=db
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=SecureP@ssw0rd2025!

# Email Configuration (for SSL certificate notifications)
EMAIL_HOST=smtp.mailgun.org
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-mailgun-app-password
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=<EMAIL>

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# Gunicorn Configuration
GUNICORN_WORKERS=4
GUNICORN_TIMEOUT=120
GUNICORN_MAX_REQUESTS=1000

# Celery Configuration
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# Logging
LOG_LEVEL=INFO

# Backup Configuration
BACKUP_RETENTION_DAYS=30

# Monitoring
HEALTH_CHECK_URL=https://radiocity.ugapp.net/health/

# SSL Certificate Email (for Let's Encrypt)
SSL_EMAIL=<EMAIL>
