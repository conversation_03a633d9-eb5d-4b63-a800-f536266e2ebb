# =============================================================================
# RadioMention Docker Compose Configuration
# =============================================================================
# Simplified configuration with hardcoded values

services:
  web:
    build:
      context: .
      target: production
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - logs_volume:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production
      - GUNICORN_WORKERS=2
      - GUNICORN_TIMEOUT=120
      - GUNICORN_MAX_REQUESTS=500
      - DISABLE_SSL_REDIRECT=true  # This will be overridden by docker-compose.ssl.yml
      - DEBUG=false  # ✅ Fixed: Use false for production settings
      - POSTGRES_DB=radio_mentions
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=SecureP@ssw0rd2025!
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - db
      - redis
    networks:
      - app-network
    restart: unless-stopped
    # No need to expose port 8000 when using nginx
    # ports:
    #   - "8000:8000"

  # Nginx service for web server and reverse proxy
  nginx:
    image: nginx:1.25-alpine
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./logs/nginx:/var/log/nginx
    environment:
      - NGINX_SERVER_NAME=${NGINX_SERVER_NAME:-localhost}
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - web
    networks:
      - app-network
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    environment:
      - POSTGRES_DB=radio_mentions
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=SecureP@ssw0rd2025!
    networks:
      - app-network
    restart: unless-stopped
    # ports:
    #   - "5432:5432"  # REMOVED: Never expose database to internet!

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - app-network
    restart: unless-stopped
    # ports:
    #   - "6379:6379"  # REMOVED: Never expose Redis to internet!

  # Celery worker
  celery:
    build:
      context: .
    command: celery -A radio_mentions_project worker --loglevel=info --concurrency=2 --max-tasks-per-child=100
    volumes:
      - media_volume:/app/media
      - logs_volume:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production
      - CELERY_WORKER=1
      - DEBUG=false  # ✅ Consistent with web service
      - POSTGRES_DB=radio_mentions
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=SecureP@ssw0rd2025!
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - db
      - redis
    networks:
      - app-network
    restart: unless-stopped

  # Celery beat scheduler
  celery-beat:
    build:
      context: .
    command: celery -A radio_mentions_project beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    volumes:
      - media_volume:/app/media
      - logs_volume:/app/logs
    environment:
      - DJANGO_SETTINGS_MODULE=radio_mentions_project.settings_production
      - CELERY_BEAT=1
      - DEBUG=false  # ✅ Consistent with web service
      - POSTGRES_DB=radio_mentions
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=SecureP@ssw0rd2025!
      - POSTGRES_HOST=db
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - db
      - redis
    networks:
      - app-network
    restart: unless-stopped

  # # Certbot service for Let's Encrypt SSL certificates
  # certbot:
  #   image: certbot/certbot
  #   volumes:
  #     - ./docker/certbot/conf:/etc/letsencrypt
  #     - ./docker/certbot/www:/var/www/certbot
  #   command: echo "Certbot service ready for SSL certificate management"
  #   networks:
  #     - app-network

  # Enhanced Certbot service for Let's Encrypt SSL certificates
  certbot:
    image: certbot/certbot:latest
    container_name: certbot
    volumes:
      - ./docker/certbot/conf:/etc/letsencrypt
      - ./docker/certbot/www:/var/www/certbot
      - ./docker/certbot/logs:/var/log/letsencrypt
      - ./scripts:/scripts:ro
    networks:
      - app-network
    environment:
      - CERTBOT_EMAIL=${CERTBOT_EMAIL:-<EMAIL>}
      - DOMAIN=${DOMAIN:-example.com}
    # Default command - can be overridden
    command: >
      sh -c "
      echo 'Certbot container started successfully';
      echo 'Available commands:';
      echo '  - docker-compose run --rm certbot obtain';
      echo '  - docker-compose run --rm certbot renew';
      echo '  - docker-compose run --rm certbot revoke';
      echo '  - docker-compose run --rm certbot status';
      echo 'Sleeping...';
      sleep infinity
      "
    restart: "no"
    profiles:
      - tools

  # Certbot cron service for automatic renewal
  certbot-cron:
    image: certbot/certbot:latest
    container_name: certbot-cron
    volumes:
      - ./docker/certbot/conf:/etc/letsencrypt
      - ./docker/certbot/www:/var/www/certbot
      - ./docker/certbot/logs:/var/log/letsencrypt
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - app-network
    environment:
      - CERTBOT_EMAIL=${CERTBOT_EMAIL:-<EMAIL>}
      - DOMAIN=${DOMAIN:-example.com}
      - NGINX_CONTAINER_NAME=${COMPOSE_PROJECT_NAME:-app}_nginx_1
    command: >
      sh -c "
      echo '0 3 * * 0 /usr/local/bin/certbot renew --quiet --post-hook \"docker kill -s HUP \$$NGINX_CONTAINER_NAME\"' > /var/spool/cron/crontabs/root &&
      echo 'Certbot auto-renewal cron job installed. Checking certificates weekly at 3 AM.' &&
      crond -f -l 2
      "
    restart: unless-stopped
    profiles:
      - production

# volumes:
#   static_volume:
#   media_volume:

# networks:
#   app-network:
#     driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  static_volume:
    driver: local
  media_volume:
    driver: local
  logs_volume:
    driver: local

networks:
  app-network:
    driver: bridge
