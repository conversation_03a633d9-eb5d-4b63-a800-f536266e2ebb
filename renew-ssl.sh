#!/bin/bash
# <PERSON><PERSON>t to renew SSL certificates for the appradio application

set -e

# Check if domain name is provided
if [ -z "$1" ]; then
  echo "Please provide your domain name as an argument"
  echo "Usage: ./renew-ssl.sh yourdomain.com"
  exit 1
fi

DOMAIN_NAME=$1
export DOMAIN_NAME
export NGINX_SERVER_NAME=$DOMAIN_NAME

echo "Renewing SSL certificate for $DOMAIN_NAME..."

# Renew certificate
docker compose -f docker-compose.yml -f docker-compose.ssl.yml run --rm certbot \
  certbot renew --webroot \
  --webroot-path=/var/www/certbot

# Reload nginx to use the new certificate
docker compose -f docker-compose.yml -f docker-compose.ssl.yml exec nginx nginx -s reload

echo "SSL certificate renewal completed."
