#!/bin/bash

# Simple Docker Build Script
# Builds the AppRadio application with SSL support

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARN] $1${NC}"
}

# Check Docker
if ! command -v docker &> /dev/null; then
    error "Docker is not installed"
fi

if ! docker info &> /dev/null; then
    error "Docker daemon is not running"
fi

# Detect docker compose
if command -v docker-compose &> /dev/null; then
    COMPOSE="docker-compose"
elif docker compose version &> /dev/null; then
    COMPOSE="docker compose"
else
    error "Docker Compose not available"
fi

log "Using: $COMPOSE"

# Build based on command
case "${1:-build}" in
    "build")
        log "Building all services..."
        $COMPOSE build
        ;;
    "web")
        log "Building web service..."
        $COMPOSE build web
        ;;
    "clean")
        log "Clean build (no cache)..."
        $COMPOSE build --no-cache
        ;;
    "up")
        log "Building and starting services..."
        $COMPOSE up -d --build
        ;;
    "ssl")
        log "Building with SSL configuration..."
        if [ -f "docker-compose.ssl.yml" ]; then
            $COMPOSE -f docker-compose.yml -f docker-compose.ssl.yml build
        else
            $COMPOSE build
        fi
        ;;
    *)
        echo "Usage: $0 [build|web|clean|up|ssl]"
        echo ""
        echo "  build  - Build all services"
        echo "  web    - Build web service only"
        echo "  clean  - Clean build (no cache)"
        echo "  up     - Build and start services"
        echo "  ssl    - Build with SSL support"
        ;;
esac
