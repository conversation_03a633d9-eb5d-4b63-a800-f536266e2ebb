#!/bin/bash

# Simplified Docker entrypoint script for Django Radio Mentions App
set -e

# Simple logging
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

error() {
    echo "[ERROR] $1"
    exit 1
}

# Wait for database to be ready
wait_for_db() {
    if [[ "$SKIP_DB_CHECK" == "true" ]]; then
        log "Skipping database check"
        return 0
    fi

    log "Waiting for database..."
    DB_HOST=${POSTGRES_HOST:-db}
    DB_PORT=${POSTGRES_PORT:-5432}

    for i in {1..30}; do
        if nc -z "$DB_HOST" "$DB_PORT"; then
            log "Database is ready!"
            return 0
        fi
        echo "Waiting for database... ($i/30)"
        sleep 2
    done

    error "Database not available after 60 seconds"
}

# Wait for Redis to be ready
wait_for_redis() {
    if [[ "$SKIP_REDIS_CHECK" == "true" ]]; then
        log "Skipping Redis check"
        return 0
    fi

    log "Waiting for Redis..."
    REDIS_HOST=${REDIS_HOST:-redis}
    REDIS_PORT=${REDIS_PORT:-6379}

    for i in {1..15}; do
        if nc -z "$REDIS_HOST" "$REDIS_PORT"; then
            log "Redis is ready!"
            return 0
        fi
        echo "Waiting for Redis... ($i/15)"
        sleep 2
    done

    error "Redis not available after 30 seconds"
}

# Run database migrations
run_migrations() {
    log "Running migrations..."
    python manage.py migrate --noinput
    log "Migrations completed"
}

# Collect static files
collect_static() {
    log "Collecting static files..."
    python manage.py collectstatic --noinput --clear
    log "Static files collected"
}

# Create superuser if specified
create_superuser() {
    if [[ -n "$DJANGO_SUPERUSER_USERNAME" && -n "$DJANGO_SUPERUSER_EMAIL" && -n "$DJANGO_SUPERUSER_PASSWORD" ]]; then
        log "Creating superuser..."
        python manage.py shell -c "
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='$DJANGO_SUPERUSER_USERNAME').exists():
    User.objects.create_superuser('$DJANGO_SUPERUSER_USERNAME', '$DJANGO_SUPERUSER_EMAIL', '$DJANGO_SUPERUSER_PASSWORD')
    print('Superuser created')
else:
    print('Superuser exists')
"
    fi
}

# Main entrypoint logic
main() {
    log "Starting application..."

    # Wait for dependencies
    wait_for_db
    wait_for_redis

    # Only run setup for web container (not celery)
    if [[ "$CELERY_WORKER" != "1" && "$CELERY_BEAT" != "1" ]]; then
        if [[ "$SKIP_DB_CHECK" != "true" ]]; then
            run_migrations
            create_superuser
        fi
        collect_static
        log "Setup completed"

        # Start Gunicorn for web container
        log "Starting Gunicorn..."
        exec gunicorn \
            --bind 0.0.0.0:8000 \
            --workers ${GUNICORN_WORKERS:-2} \
            --timeout ${GUNICORN_TIMEOUT:-120} \
            --max-requests ${GUNICORN_MAX_REQUESTS:-500} \
            --preload \
            --access-logfile /app/logs/gunicorn_access.log \
            --error-logfile /app/logs/gunicorn_error.log \
            --log-level info \
            radio_mentions_project.wsgi:application
    else
        # For celery containers, execute the passed command
        exec "$@"
    fi
}

# Run main function
main "$@"
