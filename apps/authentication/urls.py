from django.urls import path
from . import views

app_name = 'authentication'

urlpatterns = [
    # Profile management
    path('profile/', views.profile_view, name='profile'),

    # User management (admin/staff only)
    path('', views.user_manager, name='user_manager'),
    path('create/', views.user_create, name='user_create'),
    path('invite/', views.user_invite, name='user_invite'),
    path('<int:pk>/', views.user_detail, name='user_detail'),
    path('<int:pk>/edit/', views.user_edit, name='user_edit'),
    path('<int:pk>/permissions/', views.user_permissions, name='user_permissions'),
    path('<int:user_id>/toggle-permission/', views.toggle_user_permission, name='toggle_user_permission'),
    path('<int:pk>/delete/', views.user_delete, name='user_delete'),

    # Bulk operations
    path('bulk-permissions/', views.bulk_permissions, name='bulk_permissions'),

    # Invitation system
    path('invitations/<uuid:token>/accept/', views.accept_invitation, name='accept_invitation'),
]
