from django.contrib import admin
from .models import UserProfile, UserInvitation, UserPermissionOverride


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'phone', 'job_title', 'department', 'created_at']
    list_filter = ['theme', 'language', 'created_at']
    search_fields = ['user__username', 'user__email', 'phone', 'job_title']


@admin.register(UserInvitation)
class UserInvitationAdmin(admin.ModelAdmin):
    list_display = ['email', 'organization', 'role', 'invited_by', 'is_accepted', 'created_at']
    list_filter = ['role', 'is_accepted', 'is_expired', 'organization', 'created_at']
    search_fields = ['email', 'organization__name', 'invited_by__username']
    readonly_fields = ['token', 'created_at', 'accepted_at']


@admin.register(UserPermissionOverride)
class UserPermissionOverrideAdmin(admin.ModelAdmin):
    list_display = ['user', 'organization', 'permission', 'state', 'granted_by', 'created_at']
    list_filter = ['state', 'permission', 'organization', 'created_at']
    search_fields = ['user__username', 'user__email', 'organization__name', 'permission']
    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'organization', 'granted_by')
