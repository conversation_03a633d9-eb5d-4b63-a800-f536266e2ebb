# Generated by Django 4.2.7 on 2025-06-26 23:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("organizations", "0003_add_news_reader_role"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("authentication", "0002_add_news_reader_role"),
    ]

    operations = [
        migrations.CreateModel(
            name="UserPermissionOverride",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "permission",
                    models.CharField(
                        help_text="Permission key (e.g., 'manage_users', 'edit_mentions')",
                        max_length=50,
                    ),
                ),
                (
                    "state",
                    models.CharField(
                        choices=[("granted", "Granted"), ("denied", "Denied")],
                        help_text="Whether this permission is granted or denied",
                        max_length=10,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "granted_by",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who granted/denied this permission",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="permission_grants_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="permission_overrides",
                        to="organizations.organization",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="permission_overrides",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Permission Override",
                "verbose_name_plural": "User Permission Overrides",
                "db_table": "auth_user_permission_override",
                "indexes": [
                    models.Index(
                        fields=["user", "organization"], name="auth_perm_user_org_idx"
                    ),
                    models.Index(
                        fields=["permission"], name="auth_perm_permission_idx"
                    ),
                    models.Index(fields=["state"], name="auth_perm_state_idx"),
                ],
                "unique_together": {("user", "organization", "permission")},
            },
        ),
    ]
