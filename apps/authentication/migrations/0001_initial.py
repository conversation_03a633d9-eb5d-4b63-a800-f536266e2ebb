# Generated by Django 4.2.7 on 2025-06-11 07:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("organizations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("phone", models.CharField(blank=True, max_length=20, null=True)),
                ("bio", models.TextField(blank=True, max_length=500, null=True)),
                (
                    "avatar",
                    models.ImageField(blank=True, null=True, upload_to="avatars/"),
                ),
                ("timezone", models.CharField(default="UTC", max_length=50)),
                ("language", models.CharField(default="en", max_length=10)),
                (
                    "theme",
                    models.CharField(
                        choices=[
                            ("light", "Light"),
                            ("dark", "Dark"),
                            ("auto", "Auto"),
                        ],
                        default="light",
                        max_length=10,
                    ),
                ),
                ("email_notifications", models.BooleanField(default=True)),
                ("browser_notifications", models.BooleanField(default=True)),
                ("mention_reminders", models.BooleanField(default=True)),
                ("conflict_alerts", models.BooleanField(default=True)),
                ("job_title", models.CharField(blank=True, max_length=100, null=True)),
                ("department", models.CharField(blank=True, max_length=100, null=True)),
                ("hire_date", models.DateField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
                "db_table": "auth_user_profile",
            },
        ),
        migrations.CreateModel(
            name="UserInvitation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("token", models.UUIDField(default=uuid.uuid4, unique=True)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("viewer", "Viewer"),
                            ("editor", "Editor"),
                            ("presenter", "Presenter"),
                            ("manager", "Manager"),
                            ("admin", "Administrator"),
                            ("owner", "Owner"),
                        ],
                        default="viewer",
                        max_length=20,
                    ),
                ),
                ("message", models.TextField(blank=True, null=True)),
                ("is_accepted", models.BooleanField(default=False)),
                ("is_expired", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("accepted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "invited_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_invitations_sent",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="invitations",
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Invitation",
                "verbose_name_plural": "User Invitations",
                "db_table": "auth_user_invitation",
                "unique_together": {("email", "organization")},
            },
        ),
    ]
