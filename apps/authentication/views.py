from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login
from django.contrib.auth.models import User
from django.contrib.auth.decorators import login_required, user_passes_test
from apps.core.decorators import require_permission, owner_or_admin_required, organization_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.http import JsonResponse, HttpResponseForbidden
from django.utils import timezone
from django.urls import reverse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.core.mail import send_mail
from django.conf import settings
from datetime import timedelta
import json

from .models import UserProfile, UserInvitation, UserPermissionOverride
from apps.organizations.models import Organization, OrganizationMembership


# Authentication functions removed - using Django-allauth for enhanced security


@login_required
def profile_view(request):
    """User profile view and edit"""
    profile, created = UserProfile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        # Update user basic info
        request.user.first_name = request.POST.get('first_name', '')
        request.user.last_name = request.POST.get('last_name', '')
        request.user.email = request.POST.get('email', '')
        request.user.save()

        # Update profile
        profile.phone = request.POST.get('phone', '')
        profile.bio = request.POST.get('bio', '')
        profile.timezone = request.POST.get('timezone', 'UTC')
        profile.language = request.POST.get('language', 'en')
        profile.theme = request.POST.get('theme', 'light')
        profile.job_title = request.POST.get('job_title', '')
        profile.department = request.POST.get('department', '')

        # Notification preferences
        profile.email_notifications = request.POST.get('email_notifications') == 'on'
        profile.browser_notifications = request.POST.get('browser_notifications') == 'on'
        profile.mention_reminders = request.POST.get('mention_reminders') == 'on'
        profile.conflict_alerts = request.POST.get('conflict_alerts') == 'on'

        profile.save()
        messages.success(request, 'Profile updated successfully!')
        return redirect('authentication:profile')

    context = {
        'profile': profile,
        'user': request.user
    }
    return render(request, 'authentication/profile.html', context)


def is_staff_or_admin(user):
    """Check if user is staff or admin"""
    return user.is_staff or user.is_superuser


@require_permission('manage_users')
def user_manager(request):
    """Enhanced user management view - filtered by organization"""
    from apps.organizations.middleware import get_current_organization
    from apps.organizations.models import OrganizationMembership

    # Get current organization
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization to manage users.')
        return redirect('organizations:list')

    # Get users who are members of the current organization with their membership data
    users_queryset = User.objects.filter(
        organizationmembership__organization=current_org,
        organizationmembership__is_active=True
    ).select_related('profile').prefetch_related(
        'organizationmembership_set'
    ).distinct()

    search = request.GET.get('search')
    role = request.GET.get('role')
    status = request.GET.get('status')

    if search:
        users_queryset = users_queryset.filter(
            Q(username__icontains=search) |
            Q(first_name__icontains=search) |
            Q(last_name__icontains=search) |
            Q(email__icontains=search)
        )

    # Filter by organization role instead of global role
    if role == 'owner':
        owner_user_ids = OrganizationMembership.objects.filter(
            organization=current_org,
            role='owner',
            is_active=True
        ).values_list('user_id', flat=True)
        users_queryset = users_queryset.filter(
            organizationmembership__role='owner'
        )
    elif role == 'admin':
        users_queryset = users_queryset.filter(
            organizationmembership__role='admin'
        )
    elif role == 'manager':
        users_queryset = users_queryset.filter(
            organizationmembership__role='manager'
        )
    elif role == 'editor':
        users_queryset = users_queryset.filter(
            organizationmembership__role='editor'
        )
    elif role == 'presenter':
        users_queryset = users_queryset.filter(
            organizationmembership__role='presenter'
        )
    elif role == 'viewer':
        users_queryset = users_queryset.filter(
            organizationmembership__role='viewer'
        )

    if status == 'active':
        users_queryset = users_queryset.filter(is_active=True)
    elif status == 'inactive':
        users_queryset = users_queryset.filter(is_active=False)

    users_queryset = users_queryset.order_by('date_joined')

    # Add organization membership information to each user
    users_with_membership = []
    for user in users_queryset:
        try:
            membership = OrganizationMembership.objects.get(
                user=user,
                organization=current_org,
                is_active=True
            )
            user.organization_membership = membership
        except OrganizationMembership.DoesNotExist:
            user.organization_membership = None
        users_with_membership.append(user)

    # Pagination
    paginator = Paginator(users_with_membership, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Calculate organization-specific statistics
    total_users = OrganizationMembership.objects.filter(
        organization=current_org,
        is_active=True
    ).count()

    active_users = OrganizationMembership.objects.filter(
        organization=current_org,
        is_active=True,
        user__is_active=True
    ).count()

    admin_users = OrganizationMembership.objects.filter(
        organization=current_org,
        role__in=['owner', 'admin'],
        is_active=True
    ).count()

    pending_invites = UserInvitation.objects.filter(
        organization=current_org,
        is_accepted=False,
        is_expired=False
    ).count()

    context = {
        'page_obj': page_obj,
        'users': page_obj,
        'total_users': total_users,
        'active_users': active_users,
        'admin_users': admin_users,
        'pending_invites': pending_invites,
        'search': search,
        'role': role,
        'status': status,
        'current_organization': current_org,
    }
    return render(request, 'authentication/user_manager.html', context)


@login_required
@user_passes_test(is_staff_or_admin)
def user_detail(request, pk):
    """User detail view"""
    user = get_object_or_404(User, pk=pk)
    profile, created = UserProfile.objects.get_or_create(user=user)

    # Get user's organization memberships
    memberships = OrganizationMembership.objects.filter(user=user).select_related('organization')

    context = {
        'user': user,
        'profile': profile,
        'memberships': memberships,
    }
    return render(request, 'authentication/user_detail.html', context)


@require_permission('manage_users')
def user_create(request):
    """Create new user in current organization"""
    from apps.organizations.middleware import get_current_organization
    from apps.organizations.models import OrganizationMembership

    # Get current organization
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization to create users.')
        return redirect('organizations:list')

    if request.method == 'POST':
        username = request.POST.get('username')
        email = request.POST.get('email')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        password = request.POST.get('password')
        role = request.POST.get('role', 'viewer')

        # Validate required fields
        if not all([username, email, password]):
            messages.error(request, 'Username, email, and password are required.')
            context = {'current_organization': current_org}
            return render(request, 'authentication/user_create.html', context)

        # Check if username or email already exists
        if User.objects.filter(username=username).exists():
            messages.error(request, 'Username already exists.')
            context = {'current_organization': current_org}
            return render(request, 'authentication/user_create.html', context)

        if User.objects.filter(email=email).exists():
            messages.error(request, 'Email already exists.')
            context = {'current_organization': current_org}
            return render(request, 'authentication/user_create.html', context)

        try:
            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name=first_name,
                last_name=last_name
            )

            user.save()

            # Create profile
            profile = UserProfile.objects.create(
                user=user,
                job_title=request.POST.get('job_title', ''),
                department=request.POST.get('department', ''),
            )

            # Create organization membership
            membership = OrganizationMembership.objects.create(
                user=user,
                organization=current_org,
                role=role,
                is_active=True,
                invited_by=request.user
            )

            # If user is assigned presenter role, create presenter profile immediately
            if role == 'presenter':
                from apps.core.models import Presenter
                presenter = Presenter.objects.create(
                    user=user,
                    organization=current_org,
                    first_name=first_name,
                    last_name=last_name,
                    email=email,
                    is_active=True
                )
                messages.success(request, f'User {username} created successfully and added to {current_org.name} as a presenter!')
            else:
                messages.success(request, f'User {username} created successfully and added to {current_org.name}!')

            return redirect('authentication:user_detail', pk=user.pk)

        except Exception as e:
            messages.error(request, f'Error creating user: {str(e)}')

    context = {'current_organization': current_org}
    return render(request, 'authentication/user_create.html', context)


@login_required
def user_edit(request, pk):
    """Edit user - enhanced with organization permissions management"""
    from apps.organizations.middleware import get_current_organization, get_current_membership

    user = get_object_or_404(User, pk=pk)
    profile, created = UserProfile.objects.get_or_create(user=user)

    # Get organization context
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization.')
        return redirect('organizations:list')

    current_membership = get_current_membership(request)
    if not current_membership:
        messages.error(request, 'No organization membership found.')
        return redirect('organizations:list')

    # Get user's organization membership
    try:
        user_membership = OrganizationMembership.objects.get(
            user=user,
            organization=current_org,
            is_active=True
        )
    except OrganizationMembership.DoesNotExist:
        messages.error(request, 'User is not a member of this organization.')
        return redirect('authentication:user_manager')

    # Permission check - users can edit themselves, admins can edit others
    can_edit_permissions = current_membership.has_permission('manage_users')
    can_edit_basic = (request.user == user) or can_edit_permissions

    if not can_edit_basic:
        return HttpResponseForbidden("You don't have permission to edit this user.")

    if request.method == 'POST':
        # Update user basic info
        user.first_name = request.POST.get('first_name', '')
        user.last_name = request.POST.get('last_name', '')
        user.email = request.POST.get('email', '')

        # Only admins can change active status and role
        if can_edit_permissions:
            user.is_active = request.POST.get('is_active') == 'on'

            # Update organization role
            new_role = request.POST.get('organization_role')
            if new_role and new_role != user_membership.role:
                # Prevent non-owners from changing owner roles
                if user_membership.role == 'owner' and current_membership.role != 'owner':
                    messages.error(request, 'Only organization owners can change owner roles.')
                elif new_role == 'owner' and current_membership.role != 'owner':
                    messages.error(request, 'Only organization owners can assign owner role.')
                else:
                    old_role = user_membership.role
                    user_membership.role = new_role
                    user_membership.save()

                    # Handle presenter profile creation/removal
                    from apps.core.models import Presenter

                    if new_role == 'presenter' and old_role != 'presenter':
                        # User is being assigned presenter role - create presenter profile if it doesn't exist
                        presenter, created = Presenter.objects.get_or_create(
                            user=user,
                            organization=current_org,
                            defaults={
                                'first_name': user.first_name or user.username,
                                'last_name': user.last_name or '',
                                'email': user.email,
                                'is_active': True
                            }
                        )
                        if created:
                            messages.success(request, f'User role updated to {user_membership.get_role_display()} and presenter profile created!')
                        else:
                            messages.success(request, f'User role updated to {user_membership.get_role_display()}!')
                    elif old_role == 'presenter' and new_role != 'presenter':
                        # User is being removed from presenter role - optionally deactivate presenter profile
                        try:
                            presenter = Presenter.objects.get(user=user, organization=current_org)
                            presenter.is_active = False
                            presenter.save()
                            messages.success(request, f'User role updated to {user_membership.get_role_display()} and presenter profile deactivated!')
                        except Presenter.DoesNotExist:
                            messages.success(request, f'User role updated to {user_membership.get_role_display()}!')
                    else:
                        messages.success(request, f'User role updated to {user_membership.get_role_display()}')

        user.save()

        # Update profile
        profile.phone = request.POST.get('phone', '')
        profile.bio = request.POST.get('bio', '')
        profile.job_title = request.POST.get('job_title', '')
        profile.department = request.POST.get('department', '')
        profile.timezone = request.POST.get('timezone', 'UTC')
        profile.language = request.POST.get('language', 'en')
        profile.theme = request.POST.get('theme', 'light')

        # Notification preferences
        profile.email_notifications = request.POST.get('email_notifications') == 'on'
        profile.browser_notifications = request.POST.get('browser_notifications') == 'on'
        profile.mention_reminders = request.POST.get('mention_reminders') == 'on'
        profile.conflict_alerts = request.POST.get('conflict_alerts') == 'on'

        profile.save()

        messages.success(request, 'User updated successfully!')
        return redirect('authentication:user_detail', pk=user.pk)

    # Get available roles for dropdown
    available_roles = []
    for role_code, role_name in OrganizationMembership.ROLE_CHOICES:
        # Only owners can assign owner role
        if role_code == 'owner' and current_membership.role != 'owner':
            continue
        available_roles.append((role_code, role_name))

    context = {
        'user': user,
        'profile': profile,
        'user_membership': user_membership,
        'current_membership': current_membership,
        'current_organization': current_org,
        'can_edit_permissions': can_edit_permissions,
        'can_edit_basic': can_edit_basic,
        'available_roles': available_roles,
    }
    return render(request, 'authentication/user_edit.html', context)


@require_permission('manage_users')
def user_permissions(request, pk):
    """Manage user permissions within organization"""
    from apps.organizations.middleware import get_current_organization, get_current_membership

    user = get_object_or_404(User, pk=pk)

    # Get organization context
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization.')
        return redirect('organizations:list')

    current_membership = get_current_membership(request)
    if not current_membership:
        messages.error(request, 'No organization membership found.')
        return redirect('organizations:list')

    # Check if current user can manage permissions
    if not current_membership.has_permission('manage_users'):
        return HttpResponseForbidden("You don't have permission to manage user permissions.")

    # Get user's organization membership
    try:
        user_membership = OrganizationMembership.objects.get(
            user=user,
            organization=current_org,
            is_active=True
        )
    except OrganizationMembership.DoesNotExist:
        messages.error(request, 'User is not a member of this organization.')
        return redirect('authentication:user_manager')

    # Define permission categories and their permissions
    permission_categories = {
        'General Access': {
            'view': 'Basic view access to the system',
            'edit': 'Edit content and information',
            'delete': 'Delete content and information',
        },
        'User Management': {
            'manage_users': 'Create, edit, and delete users',
            'invite_users': 'Send user invitations',
            'view_user_details': 'View detailed user information',
            'manage_user_roles': 'Change user roles and permissions',
        },
        'Mentions Management': {
            'view_mentions': 'View mentions and schedules',
            'create_mentions': 'Create new mentions',
            'edit_mentions': 'Edit existing mentions',
            'delete_mentions': 'Delete mentions',
            'approve_mentions': 'Approve pending mentions',
            'schedule_mentions': 'Schedule mentions for shows',
            'mark_mentions_read': 'Mark mentions as read/completed',
            'manage_mentions': 'Full mentions management access',
        },
        'Shows Management': {
            'view_shows': 'View show schedules and details',
            'create_shows': 'Create new shows',
            'edit_shows': 'Edit show information',
            'delete_shows': 'Delete shows',
            'manage_presenters': 'Assign presenters to shows',
            'view_presenter_dashboard': 'Access presenter dashboard',
            'manage_own_shows': 'Manage own assigned shows',
            'manage_shows': 'Full shows management access',
            'view_schedule': 'View show schedules',
        },
        'Client Management': {
            'manage_clients': 'Manage client information and relationships',
            'view_clients': 'View client details and information',
        },
        'Reports & Analytics': {
            'view_reports': 'View reports and analytics',
            'export_data': 'Export data and reports',
            'view_analytics': 'View detailed analytics',
            'manage_conflicts': 'Manage scheduling conflicts',
        },
        'News Reader': {
            'view_news_reader_dashboard': 'Access news reader dashboard',
            'manage_reading_notes': 'Create and manage reading notes',
            'update_reading_status': 'Update reading status of mentions',
            'view_assigned_mentions': 'View assigned mentions for reading',
            'access_live_reading_tools': 'Access live reading tools and features',
        },
        'Organization Settings': {
            'manage_settings': 'Manage organization settings',
            'manage_organization': 'Full organization management',
            'manage_billing': 'Manage billing and subscriptions',
            'manage_integrations': 'Manage third-party integrations',
            'manage_api_keys': 'Manage API keys and access',
            'manage_branches': 'Manage organization branches',
            'admin_access': 'Full administrative access',
        }
    }

    if request.method == 'POST':
        # Handle role change
        new_role = request.POST.get('role')
        if new_role and new_role != user_membership.role:
            # Prevent non-owners from changing owner roles
            if user_membership.role == 'owner' and current_membership.role != 'owner':
                messages.error(request, 'Only organization owners can change owner roles.')
            elif new_role == 'owner' and current_membership.role != 'owner':
                messages.error(request, 'Only organization owners can assign owner role.')
            else:
                old_role = user_membership.role
                user_membership.role = new_role
                user_membership.save()

                # Handle presenter profile creation/removal
                from apps.core.models import Presenter

                if new_role == 'presenter' and old_role != 'presenter':
                    # Create presenter profile if it doesn't exist
                    presenter, created = Presenter.objects.get_or_create(
                        user=user_membership.user,
                        organization=current_org,
                        defaults={
                            'first_name': user_membership.user.first_name or user_membership.user.username,
                            'last_name': user_membership.user.last_name or '',
                            'email': user_membership.user.email,
                            'is_active': True
                        }
                    )
                    if created:
                        messages.success(request, f'User role updated to {user_membership.get_role_display()} and presenter profile created!')
                    else:
                        messages.success(request, f'User role updated to {user_membership.get_role_display()}!')
                elif old_role == 'presenter' and new_role != 'presenter':
                    # Deactivate presenter profile
                    try:
                        presenter = Presenter.objects.get(user=user_membership.user, organization=current_org)
                        presenter.is_active = False
                        presenter.save()
                        messages.success(request, f'User role updated to {user_membership.get_role_display()} and presenter profile deactivated!')
                    except Presenter.DoesNotExist:
                        messages.success(request, f'User role updated to {user_membership.get_role_display()}!')
                else:
                    messages.success(request, f'User role updated to {user_membership.get_role_display()}')

                return redirect('authentication:user_permissions', pk=pk)

    # Get available roles
    available_roles = []
    for role_code, role_name in OrganizationMembership.ROLE_CHOICES:
        if role_code == 'owner' and current_membership.role != 'owner':
            continue
        available_roles.append((role_code, role_name))

    # Get current user permissions based on role
    current_permissions = []
    role_permissions = {
        'owner': [
            'view', 'edit', 'delete', 'manage_users', 'manage_settings',
            'manage_organization', 'manage_billing', 'manage_integrations',
            'view_analytics', 'export_data', 'manage_api_keys',
            'manage_branches', 'manage_shows', 'manage_mentions',
            'approve_mentions', 'schedule_mentions', 'manage_presenters',
            'manage_clients', 'view_reports', 'manage_conflicts',
            'admin_access'
        ],
        'admin': [
            'view', 'edit', 'delete', 'manage_users', 'manage_settings',
            'manage_shows', 'manage_mentions', 'approve_mentions',
            'schedule_mentions', 'manage_presenters', 'manage_clients',
            'view_reports', 'manage_conflicts'
        ],
        'manager': [
            'view', 'edit', 'manage_mentions', 'approve_mentions',
            'schedule_mentions', 'manage_presenters', 'view_reports'
        ],
        'editor': ['view', 'edit', 'manage_mentions', 'schedule_mentions'],
        'presenter': [
            'mark_mentions_read', 'view_presenter_dashboard', 'view_schedule'
        ],
        'news_reader': [
            'view', 'view_news_reader_dashboard', 'manage_reading_notes',
            'update_reading_status', 'view_assigned_mentions', 'view_schedule',
            'access_live_reading_tools'
        ],
        'viewer': ['view'],
    }

    # Get role-based permissions
    role_based_permissions = role_permissions.get(user_membership.role, [])

    # Get permission overrides for this user
    permission_overrides = {}
    overrides = UserPermissionOverride.objects.filter(
        user=user,
        organization=current_org
    )
    for override in overrides:
        permission_overrides[override.permission] = override.state

    # Calculate effective permissions (role + overrides)
    current_permissions = []
    all_possible_permissions = set()
    for category_perms in permission_categories.values():
        all_possible_permissions.update(category_perms.keys())

    for permission in all_possible_permissions:
        # Check if there's an override
        if permission in permission_overrides:
            if permission_overrides[permission] == 'granted':
                current_permissions.append(permission)
        else:
            # Use role-based permission
            if permission in role_based_permissions:
                current_permissions.append(permission)

    context = {
        'user': user,
        'user_membership': user_membership,
        'current_membership': current_membership,
        'current_organization': current_org,
        'available_roles': available_roles,
        'permission_categories': permission_categories,
        'current_permissions': current_permissions,
        'role_based_permissions': role_based_permissions,
        'permission_overrides': permission_overrides,
    }
    return render(request, 'authentication/user_permissions.html', context)


@require_permission('manage_users')
def bulk_permissions(request):
    """Bulk permissions management for multiple users"""
    from apps.organizations.middleware import get_current_organization, get_current_membership

    # Get organization context
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization.')
        return redirect('organizations:list')

    current_membership = get_current_membership(request)
    if not current_membership:
        messages.error(request, 'No organization membership found.')
        return redirect('organizations:list')

    # Check if current user can manage permissions
    if not current_membership.has_permission('manage_users'):
        return HttpResponseForbidden("You don't have permission to manage user permissions.")

    # Get all users in the organization
    organization_user_ids = OrganizationMembership.objects.filter(
        organization=current_org,
        is_active=True
    ).values_list('user_id', flat=True)

    users_with_memberships = []
    for user_id in organization_user_ids:
        user = User.objects.get(id=user_id)
        membership = OrganizationMembership.objects.get(
            user=user,
            organization=current_org,
            is_active=True
        )
        users_with_memberships.append({
            'user': user,
            'membership': membership
        })

    if request.method == 'POST':
        action = request.POST.get('action')
        selected_users = request.POST.getlist('selected_users')

        if not selected_users:
            messages.error(request, 'Please select at least one user.')
            return redirect('authentication:bulk_permissions')

        if action == 'change_role':
            new_role = request.POST.get('new_role')
            if new_role:
                updated_count = 0
                for user_id in selected_users:
                    try:
                        membership = OrganizationMembership.objects.get(
                            user_id=user_id,
                            organization=current_org,
                            is_active=True
                        )
                        # Prevent changing owner roles unless current user is owner
                        if membership.role == 'owner' and current_membership.role != 'owner':
                            continue
                        if new_role == 'owner' and current_membership.role != 'owner':
                            continue

                        old_role = membership.role
                        membership.role = new_role
                        membership.save()

                        # Handle presenter profile creation/removal for bulk updates
                        from apps.core.models import Presenter
                        user = User.objects.get(id=user_id)

                        if new_role == 'presenter' and old_role != 'presenter':
                            # Create presenter profile if it doesn't exist
                            Presenter.objects.get_or_create(
                                user=user,
                                organization=current_org,
                                defaults={
                                    'first_name': user.first_name or user.username,
                                    'last_name': user.last_name or '',
                                    'email': user.email,
                                    'is_active': True
                                }
                            )
                        elif old_role == 'presenter' and new_role != 'presenter':
                            # Deactivate presenter profile
                            try:
                                presenter = Presenter.objects.get(user=user, organization=current_org)
                                presenter.is_active = False
                                presenter.save()
                            except Presenter.DoesNotExist:
                                pass

                        updated_count += 1
                    except OrganizationMembership.DoesNotExist:
                        continue

                messages.success(request, f'Updated role for {updated_count} users.')
                return redirect('authentication:bulk_permissions')

        elif action == 'activate_users':
            updated_count = 0
            for user_id in selected_users:
                try:
                    user = User.objects.get(id=user_id)
                    user.is_active = True
                    user.save()
                    updated_count += 1
                except User.DoesNotExist:
                    continue

            messages.success(request, f'Activated {updated_count} users.')
            return redirect('authentication:bulk_permissions')

        elif action == 'deactivate_users':
            updated_count = 0
            for user_id in selected_users:
                try:
                    user = User.objects.get(id=user_id)
                    # Prevent deactivating self
                    if user == request.user:
                        continue
                    user.is_active = False
                    user.save()
                    updated_count += 1
                except User.DoesNotExist:
                    continue

            messages.success(request, f'Deactivated {updated_count} users.')
            return redirect('authentication:bulk_permissions')

    # Get available roles
    available_roles = []
    for role_code, role_name in OrganizationMembership.ROLE_CHOICES:
        if role_code == 'owner' and current_membership.role != 'owner':
            continue
        available_roles.append((role_code, role_name))

    context = {
        'users_with_memberships': users_with_memberships,
        'current_membership': current_membership,
        'current_organization': current_org,
        'available_roles': available_roles,
    }
    return render(request, 'authentication/bulk_permissions.html', context)


@login_required
@user_passes_test(is_staff_or_admin)
@require_http_methods(["POST"])
def user_delete(request, pk):
    """Delete user from organization - organization admin only"""
    from apps.organizations.middleware import get_current_organization, get_current_membership

    # Get current organization and membership
    current_org = get_current_organization(request)
    if not current_org:
        return JsonResponse({'success': False, 'error': 'No organization context'})

    current_membership = get_current_membership(request)
    if not current_membership:
        return JsonResponse({'success': False, 'error': 'No organization membership found'})

    # Check if user has permission to delete users in this organization
    if not current_membership.has_permission('manage_users'):
        return JsonResponse({'success': False, 'error': 'Permission denied. You need admin or owner role to delete users.'})

    user = get_object_or_404(User, pk=pk)

    # Prevent self-deletion
    if user == request.user:
        return JsonResponse({'success': False, 'error': 'Cannot delete your own account'})

    # Check if user belongs to current organization
    try:
        user_membership = OrganizationMembership.objects.get(
            user=user,
            organization=current_org,
            is_active=True
        )
    except OrganizationMembership.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'User is not a member of this organization'})

    # Prevent deleting organization owners (unless you're also an owner)
    if user_membership.role == 'owner' and current_membership.role != 'owner':
        return JsonResponse({'success': False, 'error': 'Only organization owners can delete other owners'})

    try:
        username = user.username
        user_role = user_membership.get_role_display()

        # Option 1: Remove from organization only (recommended for multi-org systems)
        # user_membership.delete()
        # return JsonResponse({'success': True, 'message': f'User {username} ({user_role}) removed from {current_org.name} successfully'})

        # Option 2: Delete user completely (use with caution)
        # Check if user has memberships in other organizations
        other_memberships = OrganizationMembership.objects.filter(
            user=user,
            is_active=True
        ).exclude(organization=current_org)

        if other_memberships.exists():
            # User has other organization memberships, just remove from current org
            user_membership.delete()
            return JsonResponse({
                'success': True,
                'message': f'User {username} ({user_role}) removed from {current_org.name} successfully'
            })
        else:
            # User only belongs to this organization, delete completely
            user.delete()
            return JsonResponse({
                'success': True,
                'message': f'User {username} ({user_role}) deleted completely'
            })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@user_passes_test(is_staff_or_admin)
def user_invite(request):
    """Invite new user via email to current organization"""
    from apps.organizations.middleware import get_current_organization

    # Get current organization
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization to invite users.')
        return redirect('organizations:list')

    if request.method == 'POST':
        email = request.POST.get('email')
        role = request.POST.get('role', 'viewer')
        message = request.POST.get('message', '')

        if not email:
            messages.error(request, 'Email is required.')
            context = {'current_organization': current_org}
            return render(request, 'authentication/user_invite.html', context)

        # Check if user already exists
        if User.objects.filter(email=email).exists():
            messages.error(request, 'User with this email already exists.')
            context = {'current_organization': current_org}
            return render(request, 'authentication/user_invite.html', context)

        # Check if invitation already exists
        if UserInvitation.objects.filter(email=email, organization=current_org, is_accepted=False).exists():
            messages.error(request, 'Invitation already sent to this email for this organization.')
            context = {'current_organization': current_org}
            return render(request, 'authentication/user_invite.html', context)

        try:
            # Create invitation
            invitation = UserInvitation.objects.create(
                email=email,
                role=role,
                invited_by=request.user,
                organization=current_org,
                message=message,
                expires_at=timezone.now() + timedelta(days=7)
            )

            # Send invitation email
            subject = f'Invitation to join {current_org.name}'
            message_body = f"""
            You have been invited to join {current_org.name} as a {role}.

            {message}

            Click the link below to accept the invitation:
            {request.build_absolute_uri(invitation.get_accept_url())}

            This invitation expires in 7 days.
            """

            send_mail(
                subject,
                message_body,
                settings.DEFAULT_FROM_EMAIL,
                [email],
                fail_silently=False,
            )

            messages.success(request, f'Invitation sent to {email} for {current_org.name}!')
            return redirect('authentication:user_manager')

        except Exception as e:
            messages.error(request, f'Error sending invitation: {str(e)}')

    context = {'current_organization': current_org}
    return render(request, 'authentication/user_invite.html', context)


def accept_invitation(request, token):
    """Accept user invitation and create account"""
    invitation = get_object_or_404(UserInvitation, token=token)

    if not invitation.is_valid():
        messages.error(request, 'This invitation has expired or is no longer valid.')
        return redirect('authentication:login')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        password_confirm = request.POST.get('password_confirm')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')

        # Validate
        if not all([username, password, password_confirm]):
            messages.error(request, 'All fields are required.')
            return render(request, 'authentication/accept_invitation.html', {'invitation': invitation})

        if password != password_confirm:
            messages.error(request, 'Passwords do not match.')
            return render(request, 'authentication/accept_invitation.html', {'invitation': invitation})

        if User.objects.filter(username=username).exists():
            messages.error(request, 'Username already exists.')
            return render(request, 'authentication/accept_invitation.html', {'invitation': invitation})

        try:
            # Create user
            user = User.objects.create_user(
                username=username,
                email=invitation.email,
                password=password,
                first_name=first_name,
                last_name=last_name
            )

            user.save()

            # Create profile
            UserProfile.objects.create(user=user)

            # Create organization membership
            OrganizationMembership.objects.create(
                user=user,
                organization=invitation.organization,
                role=invitation.role,
                is_active=True
            )

            # Mark invitation as accepted
            invitation.is_accepted = True
            invitation.accepted_at = timezone.now()
            invitation.save()

            # Log in the user
            login(request, user)

            messages.success(request, 'Account created successfully! Welcome to the team.')
            return redirect('core:dashboard')

        except Exception as e:
            messages.error(request, f'Error creating account: {str(e)}')

    context = {'invitation': invitation}
    return render(request, 'authentication/accept_invitation.html', context)


@require_permission('manage_users')
@require_http_methods(["POST"])
def toggle_user_permission(request, user_id):
    """AJAX endpoint to toggle individual user permissions"""
    from apps.organizations.middleware import get_current_organization, get_current_membership

    try:
        # Parse JSON data
        data = json.loads(request.body)
        permission = data.get('permission')
        enabled = data.get('enabled', False)

        if not permission:
            return JsonResponse({'success': False, 'error': 'Permission not specified'}, status=400)

        # Get organization context
        current_org = get_current_organization(request)
        if not current_org:
            return JsonResponse({'success': False, 'error': 'No organization context'}, status=400)

        current_membership = get_current_membership(request)
        if not current_membership:
            return JsonResponse({'success': False, 'error': 'No organization membership found'}, status=400)

        # Get target user
        target_user = get_object_or_404(User, pk=user_id)

        # Check if target user is in the same organization
        try:
            target_membership = OrganizationMembership.objects.get(
                user=target_user,
                organization=current_org,
                is_active=True
            )
        except OrganizationMembership.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'User not found in organization'}, status=404)

        # Prevent modifying owner permissions unless current user is also owner
        if target_membership.role == 'owner' and current_membership.role != 'owner':
            return JsonResponse({'success': False, 'error': 'Cannot modify owner permissions'}, status=403)

        # Get or create permission override
        override, created = UserPermissionOverride.objects.get_or_create(
            user=target_user,
            organization=current_org,
            permission=permission,
            defaults={
                'state': 'granted' if enabled else 'denied',
                'granted_by': request.user
            }
        )

        if not created:
            # Update existing override
            override.state = 'granted' if enabled else 'denied'
            override.granted_by = request.user
            override.save()

        # If the permission is being set to match the role default, remove the override
        role_permissions = get_role_permissions(target_membership.role)
        role_has_permission = permission in role_permissions

        if (enabled and role_has_permission) or (not enabled and not role_has_permission):
            # Permission matches role default, remove override
            override.delete()
            override_exists = False
        else:
            override_exists = True

        return JsonResponse({
            'success': True,
            'permission': permission,
            'enabled': enabled,
            'override_exists': override_exists,
            'message': f'Permission {"granted" if enabled else "denied"} successfully'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


def get_role_permissions(role):
    """Get default permissions for a role"""
    role_permissions = {
        'owner': [
            'view', 'edit', 'delete', 'manage_users', 'manage_settings',
            'manage_organization', 'manage_billing', 'manage_integrations',
            'view_analytics', 'export_data', 'manage_api_keys',
            'manage_branches', 'manage_shows', 'manage_mentions',
            'approve_mentions', 'schedule_mentions', 'manage_presenters',
            'manage_clients', 'view_reports', 'manage_conflicts',
            'admin_access'
        ],
        'admin': [
            'view', 'edit', 'delete', 'manage_users', 'manage_settings',
            'manage_shows', 'manage_mentions', 'approve_mentions',
            'schedule_mentions', 'manage_presenters', 'manage_clients',
            'view_reports', 'manage_conflicts', 'view_user_details',
            'invite_users', 'manage_user_roles', 'manage_api_keys'
        ],
        'manager': [
            'view', 'edit', 'manage_mentions', 'approve_mentions',
            'schedule_mentions', 'manage_presenters', 'view_reports',
            'manage_shows', 'manage_clients'
        ],
        'editor': [
            'view', 'edit', 'manage_mentions', 'schedule_mentions',
            'manage_shows', 'manage_clients'
        ],
        'presenter': [
            'mark_mentions_read', 'view_presenter_dashboard', 'view_schedule'
        ],
        'news_reader': [
            'view', 'view_news_reader_dashboard', 'manage_reading_notes',
            'update_reading_status', 'view_assigned_mentions', 'view_schedule',
            'access_live_reading_tools'
        ],
        'viewer': ['view'],
    }
    return role_permissions.get(role, [])
