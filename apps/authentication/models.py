from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
import uuid


class UserProfile(models.Model):
    """Extended user profile with additional fields"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    phone = models.CharField(max_length=20, blank=True, null=True)
    bio = models.TextField(max_length=500, blank=True, null=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    timezone = models.CharField(max_length=50, default='UTC')
    language = models.CharField(max_length=10, default='en')
    theme = models.CharField(max_length=10, choices=[
        ('light', 'Light'),
        ('dark', 'Dark'),
        ('auto', 'Auto')
    ], default='light')

    # Notification preferences
    email_notifications = models.BooleanField(default=True)
    browser_notifications = models.BooleanField(default=True)
    mention_reminders = models.BooleanField(default=True)
    conflict_alerts = models.BooleanField(default=True)

    # Professional information
    job_title = models.CharField(max_length=100, blank=True, null=True)
    department = models.CharField(max_length=100, blank=True, null=True)
    hire_date = models.DateField(blank=True, null=True)

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'auth_user_profile'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username}'s Profile"

    def get_absolute_url(self):
        return reverse('authentication:profile')


class UserInvitation(models.Model):
    """User invitation system"""
    email = models.EmailField()
    token = models.UUIDField(default=uuid.uuid4, unique=True)
    role = models.CharField(max_length=20, choices=[
        ('viewer', 'Viewer'),
        ('editor', 'Editor'),
        ('presenter', 'Presenter'),
        ('news_reader', 'News Reader'),
        ('manager', 'Manager'),
        ('admin', 'Administrator'),
        ('owner', 'Owner')
    ], default='viewer')

    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_invitations_sent')
    organization = models.ForeignKey('organizations.Organization', on_delete=models.CASCADE, related_name='invitations')

    message = models.TextField(blank=True, null=True)
    is_accepted = models.BooleanField(default=False)
    is_expired = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    accepted_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = 'auth_user_invitation'
        verbose_name = 'User Invitation'
        verbose_name_plural = 'User Invitations'
        unique_together = ['email', 'organization']

    def __str__(self):
        return f"Invitation for {self.email} to {self.organization.name}"

    def is_valid(self):
        return not self.is_expired and not self.is_accepted and timezone.now() < self.expires_at

    def get_accept_url(self):
        return reverse('authentication:accept_invitation', kwargs={'token': self.token})


class UserPermissionOverride(models.Model):
    """Individual permission overrides for users within organizations"""
    PERMISSION_STATE_CHOICES = [
        ('granted', 'Granted'),
        ('denied', 'Denied'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='permission_overrides')
    organization = models.ForeignKey('organizations.Organization', on_delete=models.CASCADE, related_name='permission_overrides')
    permission = models.CharField(max_length=50, help_text="Permission key (e.g., 'manage_users', 'edit_mentions')")
    state = models.CharField(max_length=10, choices=PERMISSION_STATE_CHOICES, help_text="Whether this permission is granted or denied")

    # Audit fields
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='permission_grants_made',
        help_text="User who granted/denied this permission"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'auth_user_permission_override'
        verbose_name = 'User Permission Override'
        verbose_name_plural = 'User Permission Overrides'
        unique_together = ['user', 'organization', 'permission']
        indexes = [
            models.Index(fields=['user', 'organization'], name='auth_perm_user_org_idx'),
            models.Index(fields=['permission'], name='auth_perm_permission_idx'),
            models.Index(fields=['state'], name='auth_perm_state_idx'),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.organization.name} - {self.permission}: {self.state}"

