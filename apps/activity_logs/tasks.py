"""
Background tasks for system maintenance, cleanup, and health monitoring.
"""

from celery import shared_task
from django.utils import timezone
from django.db.models import Count, Q
from datetime import datetime, timedelta
from apps.organizations.models import Organization
from apps.activity_logs.models import ActivityLog, ConflictLog
from apps.mentions.models import Mention, MentionReading
from apps.shows.models import ShowSession
from apps.core.notifications import notify_admins
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def cleanup_old_logs(self):
    """
    Clean up old activity logs and maintain database performance
    """
    try:
        current_time = timezone.now()
        
        # Define retention periods
        retention_periods = {
            'info': timedelta(days=90),      # Keep info logs for 90 days
            'warning': timedelta(days=180),   # Keep warnings for 6 months
            'error': timedelta(days=365),     # Keep errors for 1 year
            'critical': timedelta(days=730),  # Keep critical logs for 2 years
        }
        
        total_deleted = 0
        cleanup_summary = {}
        
        for level, retention_period in retention_periods.items():
            cutoff_date = current_time - retention_period
            
            # Delete old logs of this level
            old_logs = ActivityLog.objects.filter(
                level=level,
                created_at__lt=cutoff_date
            )
            
            count = old_logs.count()
            old_logs.delete()
            
            cleanup_summary[level] = count
            total_deleted += count
        
        # Clean up old conflict logs (keep for 1 year)
        conflict_cutoff = current_time - timedelta(days=365)
        old_conflicts = ConflictLog.objects.filter(created_at__lt=conflict_cutoff)
        conflict_count = old_conflicts.count()
        old_conflicts.delete()
        
        cleanup_summary['conflicts'] = conflict_count
        total_deleted += conflict_count
        
        # Log the cleanup operation
        ActivityLog.log_activity(
            user=None,
            organization=None,  # System-wide operation
            action='log_cleanup',
            description=f"Cleaned up {total_deleted} old log entries",
            level='info',
            metadata=cleanup_summary
        )
        
        logger.info(f"Log cleanup completed. Deleted {total_deleted} entries")
        return f"Cleaned up {total_deleted} log entries"
        
    except Exception as exc:
        logger.error(f"Log cleanup failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=3600)


@shared_task(bind=True, max_retries=3)
def monitor_database_health(self):
    """
    Monitor database health and performance metrics
    """
    try:
        current_time = timezone.now()
        health_issues = []
        
        # Check for organizations with excessive log volume
        week_ago = current_time - timedelta(days=7)
        org_log_counts = ActivityLog.objects.filter(
            created_at__gte=week_ago
        ).values('organization').annotate(
            log_count=Count('id')
        ).filter(log_count__gt=10000)  # More than 10k logs per week
        
        if org_log_counts.exists():
            health_issues.append(f"{org_log_counts.count()} organizations with excessive log volume")
        
        # Check for stuck show sessions (active for more than 24 hours)
        day_ago = current_time - timedelta(days=1)
        stuck_sessions = ShowSession.objects.filter(
            actual_start_time__lt=day_ago,
            actual_end_time__isnull=True
        ).count()
        
        if stuck_sessions > 0:
            health_issues.append(f"{stuck_sessions} show sessions stuck open for over 24 hours")
        
        # Check for old pending mentions (over 1 week)
        old_pending = Mention.objects.filter(
            status='pending',
            created_at__lt=current_time - timedelta(days=7)
        ).count()
        
        if old_pending > 0:
            health_issues.append(f"{old_pending} mentions pending approval for over 1 week")
        
        # Check for orphaned mention readings
        orphaned_readings = MentionReading.objects.filter(
            mention__isnull=True
        ).count()
        
        if orphaned_readings > 0:
            health_issues.append(f"{orphaned_readings} orphaned mention readings found")
        
        # If health issues found, notify system administrators
        if health_issues:
            # Notify all organization admins about system health
            organizations = Organization.objects.filter(is_active=True)
            for organization in organizations:
                notify_admins(
                    organization=organization,
                    notification_type='database_health_alert',
                    context={
                        'issues': health_issues,
                        'check_time': current_time,
                        'severity': 'warning'
                    }
                )
        
        # Log health check results
        ActivityLog.log_activity(
            user=None,
            organization=None,
            action='database_health_check',
            description=f"Database health check completed. Found {len(health_issues)} issues",
            level='warning' if health_issues else 'info',
            metadata={
                'issues_found': len(health_issues),
                'issues': health_issues
            }
        )
        
        logger.info(f"Database health check completed. Found {len(health_issues)} issues")
        return f"Database health check completed. Issues: {len(health_issues)}"
        
    except Exception as exc:
        logger.error(f"Database health monitoring failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=1800)


@shared_task(bind=True, max_retries=3)
def cleanup_orphaned_data(self):
    """
    Clean up orphaned data and maintain referential integrity
    """
    try:
        cleanup_summary = {}
        
        # Clean up orphaned mention readings
        orphaned_readings = MentionReading.objects.filter(mention__isnull=True)
        reading_count = orphaned_readings.count()
        orphaned_readings.delete()
        cleanup_summary['orphaned_readings'] = reading_count
        
        # Clean up old show sessions for inactive shows
        inactive_sessions = ShowSession.objects.filter(
            show__is_active=False,
            date__lt=timezone.now().date() - timedelta(days=30)
        )
        session_count = inactive_sessions.count()
        inactive_sessions.delete()
        cleanup_summary['inactive_sessions'] = session_count
        
        # Clean up activity logs for deleted organizations
        # (This would be handled by CASCADE, but let's be explicit)
        orphaned_logs = ActivityLog.objects.filter(organization__isnull=True)
        log_count = orphaned_logs.count()
        orphaned_logs.delete()
        cleanup_summary['orphaned_logs'] = log_count
        
        total_cleaned = sum(cleanup_summary.values())
        
        # Log cleanup results
        ActivityLog.log_activity(
            user=None,
            organization=None,
            action='orphaned_data_cleanup',
            description=f"Cleaned up {total_cleaned} orphaned records",
            level='info',
            metadata=cleanup_summary
        )
        
        logger.info(f"Orphaned data cleanup completed. Cleaned {total_cleaned} records")
        return f"Cleaned up {total_cleaned} orphaned records"
        
    except Exception as exc:
        logger.error(f"Orphaned data cleanup failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=3600)


@shared_task(bind=True, max_retries=3)
def generate_system_health_report(self):
    """
    Generate comprehensive system health report
    """
    try:
        current_time = timezone.now()
        
        # Gather system-wide statistics
        health_data = _gather_system_health_data(current_time)
        
        # Send health report to system administrators
        organizations = Organization.objects.filter(is_active=True)
        reports_sent = 0
        
        for organization in organizations:
            # Add organization-specific data
            org_health_data = _gather_organization_health_data(organization, current_time)
            combined_data = {**health_data, 'organization_data': org_health_data}
            
            # Send to organization admins
            notify_admins(
                organization=organization,
                notification_type='system_health_report',
                context={
                    'health_data': combined_data,
                    'organization': organization,
                    'report_time': current_time
                }
            )
            
            reports_sent += 1
        
        logger.info(f"System health reports sent to {reports_sent} organizations")
        return f"Generated system health reports for {reports_sent} organizations"
        
    except Exception as exc:
        logger.error(f"System health report generation failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=7200)


@shared_task(bind=True, max_retries=3)
def optimize_database_performance(self):
    """
    Perform database optimization tasks
    """
    try:
        optimization_results = {}
        
        # Update statistics for query optimization
        # Note: This is database-specific and would need to be adapted
        # for different database backends
        
        # For PostgreSQL, we could run ANALYZE
        # For now, we'll just log that optimization was attempted
        
        # Clean up old temporary data
        temp_cleanup_count = _cleanup_temporary_data()
        optimization_results['temp_cleanup'] = temp_cleanup_count
        
        # Rebuild indexes if needed (database-specific)
        # This would typically be done during maintenance windows
        # SAFETY: Skip automatic reindexing in production to avoid locks
        optimization_results['index_maintenance'] = 'skipped_for_safety'
        optimization_results['reindex_note'] = 'Automatic reindexing disabled for production safety. Use manual maintenance windows.'
        
        # Log optimization results
        ActivityLog.log_activity(
            user=None,
            organization=None,
            action='database_optimization',
            description="Database optimization tasks completed",
            level='info',
            metadata=optimization_results
        )
        
        logger.info("Database optimization completed")
        return f"Database optimization completed: {optimization_results}"
        
    except Exception as exc:
        logger.error(f"Database optimization failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=7200)


@shared_task(bind=True, max_retries=2)
def monthly_database_reindex(self):
    """
    PRODUCTION SAFETY: This task only logs a reminder for manual maintenance
    Actual reindexing should be done during scheduled maintenance windows
    """
    try:
        logger.info("Monthly database maintenance reminder triggered...")

        # Instead of automatic reindexing, log a maintenance reminder
        ActivityLog.log_activity(
            user=None,
            organization=None,
            action='monthly_maintenance_reminder',
            description="Monthly database maintenance reminder: Consider scheduling reindexing during next maintenance window",
            level='warning',
            metadata={
                'maintenance_type': 'database_reindexing',
                'recommendation': 'Schedule manual reindexing during low-traffic hours',
                'command': 'python manage.py optimize_database --reindex',
                'execution_time': timezone.now().isoformat()
            }
        )

        logger.warning("Monthly maintenance reminder: Database reindexing should be performed manually during maintenance window")
        return "Monthly maintenance reminder logged - manual reindexing recommended"

    except Exception as exc:
        logger.error(f"Monthly maintenance reminder failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=86400)  # Retry after 24 hours


@shared_task(bind=True, max_retries=1)
def safe_database_analyze(self):
    """
    Safe database maintenance: Run ANALYZE instead of REINDEX
    ANALYZE updates statistics without locking tables
    """
    try:
        from django.db import connection

        logger.info("Starting safe database statistics update...")

        with connection.cursor() as cursor:
            if connection.vendor == 'postgresql':
                # ANALYZE is safe - it doesn't lock tables
                cursor.execute("ANALYZE;")

                ActivityLog.log_activity(
                    user=None,
                    organization=None,
                    action='database_analyze_completed',
                    description="Database statistics updated successfully (ANALYZE)",
                    level='info',
                    metadata={
                        'operation': 'ANALYZE',
                        'safety_level': 'safe',
                        'execution_time': timezone.now().isoformat()
                    }
                )

                logger.info("Database ANALYZE completed successfully")
                return "Database statistics updated safely"
            else:
                logger.warning("ANALYZE operation only supported for PostgreSQL")
                return "ANALYZE skipped - not PostgreSQL"

    except Exception as exc:
        logger.error(f"Database ANALYZE failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=3600)  # Retry after 1 hour


@shared_task(bind=True, max_retries=1)
def automated_maintenance_reindex(self):
    """
    Automated database reindexing during low-traffic hours
    Runs the maintenance script automatically with safety checks
    """
    try:
        import subprocess
        import os
        from django.conf import settings

        logger.info("Starting automated maintenance reindex...")

        # Safety check: Only run during low-traffic hours (2-6 AM server time)
        current_hour = timezone.now().hour
        if not (2 <= current_hour <= 6):
            logger.info(f"Skipping reindex - not in maintenance window (current hour: {current_hour})")
            ActivityLog.log_activity(
                user=None,
                organization=None,
                action='maintenance_reindex_skipped',
                description=f"Automated reindex skipped - outside maintenance window (hour: {current_hour})",
                level='info',
                metadata={'current_hour': current_hour, 'maintenance_window': '2-6 AM'}
            )
            return "Reindex skipped - outside maintenance window"

        # Check database load before proceeding
        from django.db import connection
        with connection.cursor() as cursor:
            if connection.vendor == 'postgresql':
                # Check active connections
                cursor.execute("""
                    SELECT count(*) FROM pg_stat_activity
                    WHERE state = 'active' AND pid != pg_backend_pid()
                """)
                active_connections = cursor.fetchone()[0]

                # Skip if too many active connections (high load)
                if active_connections > 5:
                    logger.warning(f"Skipping reindex - high database load ({active_connections} active connections)")
                    ActivityLog.log_activity(
                        user=None,
                        organization=None,
                        action='maintenance_reindex_skipped_load',
                        description=f"Automated reindex skipped - high database load ({active_connections} connections)",
                        level='warning',
                        metadata={'active_connections': active_connections, 'threshold': 5}
                    )
                    return f"Reindex skipped - high load ({active_connections} connections)"

        # Get the script path
        base_dir = getattr(settings, 'BASE_DIR', '/app')
        script_path = os.path.join(base_dir, 'scripts', 'maintenance-reindex.sh')

        if not os.path.exists(script_path):
            logger.error(f"Maintenance script not found: {script_path}")
            raise Exception(f"Maintenance script not found: {script_path}")

        # Run the maintenance script with automated flags
        logger.info("Executing maintenance reindex script...")
        start_time = timezone.now()

        # Run script with no-backup flag (since we have regular backups)
        # and automated mode (skip user confirmations)
        result = subprocess.run([
            'bash', script_path,
            '--no-backup',  # Skip backup (regular backups already exist)
            '--automated'   # Skip user confirmations
        ],
        capture_output=True,
        text=True,
        timeout=3600  # 1 hour timeout
        )

        end_time = timezone.now()
        duration = (end_time - start_time).total_seconds()

        if result.returncode == 0:
            # Success
            logger.info(f"Automated maintenance reindex completed successfully in {duration} seconds")

            ActivityLog.log_activity(
                user=None,
                organization=None,
                action='automated_maintenance_reindex_success',
                description="Automated database reindexing completed successfully during maintenance window",
                level='info',
                metadata={
                    'duration_seconds': duration,
                    'execution_hour': current_hour,
                    'stdout': result.stdout[-1000:],  # Last 1000 chars
                    'execution_time': start_time.isoformat()
                }
            )

            return f"Automated reindex completed successfully in {duration:.1f} seconds"
        else:
            # Failure
            logger.error(f"Automated maintenance reindex failed: {result.stderr}")

            ActivityLog.log_activity(
                user=None,
                organization=None,
                action='automated_maintenance_reindex_failed',
                description=f"Automated database reindexing failed: {result.stderr}",
                level='error',
                metadata={
                    'duration_seconds': duration,
                    'return_code': result.returncode,
                    'stderr': result.stderr,
                    'stdout': result.stdout
                }
            )

            raise Exception(f"Maintenance script failed with return code {result.returncode}: {result.stderr}")

    except subprocess.TimeoutExpired:
        logger.error("Automated maintenance reindex timed out after 1 hour")
        ActivityLog.log_activity(
            user=None,
            organization=None,
            action='automated_maintenance_reindex_timeout',
            description="Automated database reindexing timed out after 1 hour",
            level='error',
            metadata={'timeout_seconds': 3600}
        )
        raise Exception("Maintenance reindex timed out")

    except Exception as exc:
        logger.error(f"Automated maintenance reindex failed: {str(exc)}")

        ActivityLog.log_activity(
            user=None,
            organization=None,
            action='automated_maintenance_reindex_error',
            description=f"Automated database reindexing error: {str(exc)}",
            level='error',
            metadata={'error': str(exc)}
        )

        # Don't retry automatically - wait for next scheduled run
        raise exc


# Helper functions
def _gather_system_health_data(current_time):
    """Gather system-wide health statistics"""
    week_ago = current_time - timedelta(days=7)
    
    return {
        'total_organizations': Organization.objects.filter(is_active=True).count(),
        'total_active_shows': ShowSession.objects.filter(
            date=current_time.date(),
            actual_end_time__isnull=True
        ).count(),
        'weekly_activity_logs': ActivityLog.objects.filter(
            created_at__gte=week_ago
        ).count(),
        'weekly_error_logs': ActivityLog.objects.filter(
            created_at__gte=week_ago,
            level__in=['error', 'critical']
        ).count(),
        'system_uptime': _calculate_system_uptime(),
        'database_size': _estimate_database_size(),
    }


def _gather_organization_health_data(organization, current_time):
    """Gather organization-specific health data"""
    week_ago = current_time - timedelta(days=7)
    
    return {
        'active_presenters': organization.presenters.filter(is_active=True).count(),
        'active_shows': organization.shows.filter(is_active=True).count(),
        'weekly_mentions': MentionReading.objects.filter(
            mention__client__organization=organization,
            scheduled_date__gte=week_ago.date()
        ).count(),
        'pending_approvals': Mention.objects.filter(
            client__organization=organization,
            status='pending'
        ).count(),
        'recent_errors': ActivityLog.objects.filter(
            organization=organization,
            created_at__gte=week_ago,
            level__in=['error', 'critical']
        ).count(),
    }


def _calculate_system_uptime():
    """Calculate system uptime based on activity logs"""
    # This is a simplified calculation
    # In a real system, you'd track actual uptime
    recent_activity = ActivityLog.objects.filter(
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).exists()
    
    return "operational" if recent_activity else "unknown"


def _estimate_database_size():
    """Estimate database size based on record counts"""
    # This is a rough estimation
    # In production, you'd query actual database size
    total_records = (
        ActivityLog.objects.count() +
        MentionReading.objects.count() +
        Mention.objects.count() +
        ShowSession.objects.count()
    )
    
    # Rough estimate: 1KB per record average
    estimated_mb = total_records / 1024
    
    if estimated_mb > 1024:
        return f"{estimated_mb/1024:.1f} GB"
    else:
        return f"{estimated_mb:.1f} MB"


def _cleanup_temporary_data():
    """Clean up temporary data and cache"""
    # This would clean up any temporary files, cache entries, etc.
    # For now, just return a placeholder count
    return 0
