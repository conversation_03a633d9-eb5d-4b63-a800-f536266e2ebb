from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.core.cache import cache
from datetime import datetime, timedelta
from .models import ActivityLog, ConflictLog
from apps.mentions.services import ConflictDetectionService
from apps.organizations.middleware import get_current_organization


@login_required
def activity_logs(request):
    """Activity logs view"""
    organization = get_current_organization(request)

    # Get filter parameters
    action_filter = request.GET.get('action', '')
    level_filter = request.GET.get('level', '')
    user_filter = request.GET.get('user', '')
    date_filter = request.GET.get('date', '')
    search = request.GET.get('search', '')

    # Base queryset with select_related for better performance
    logs = ActivityLog.objects.filter(organization=organization).select_related('user')

    # Apply filters
    if action_filter:
        logs = logs.filter(action=action_filter)

    if level_filter:
        logs = logs.filter(level=level_filter)

    if user_filter:
        logs = logs.filter(user__username__icontains=user_filter)

    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            logs = logs.filter(created_at__date=filter_date)
        except ValueError:
            pass

    if search:
        logs = logs.filter(
            Q(description__icontains=search) |
            Q(user__username__icontains=search) |
            Q(action__icontains=search)
        )

    # Pagination - reduced page size for better performance
    paginator = Paginator(logs, 25)  # Reduced from 50 to 25
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get statistics - cached for better performance
    stats_cache_key = f'activity_logs_stats_{organization.id}_{hash(str(sorted(request.GET.items())))}'
    stats = cache.get(stats_cache_key)

    if stats is None:
        stats = {
            'total_logs': logs.count(),
            'today_logs': logs.filter(created_at__date=timezone.now().date()).count(),
            'error_logs': logs.filter(level='error').count(),
            'warning_logs': logs.filter(level='warning').count(),
        }
        # Cache for 2 minutes
        cache.set(stats_cache_key, stats, 120)

    # Get unique actions and users for filters - cached
    filters_cache_key = f'activity_logs_filters_{organization.id}'
    filter_data = cache.get(filters_cache_key)

    if filter_data is None:
        actions = ActivityLog.objects.filter(organization=organization).values_list('action', flat=True).distinct()
        users = ActivityLog.objects.filter(organization=organization).values_list('user__username', flat=True).distinct()
        filter_data = {
            'actions': sorted(set(actions)),
            'users': sorted(set(filter(None, users)))
        }
        # Cache for 10 minutes
        cache.set(filters_cache_key, filter_data, 600)

    context = {
        'page_obj': page_obj,
        'stats': stats,
        'actions': filter_data['actions'],
        'users': filter_data['users'],
        'filters': {
            'action': action_filter,
            'level': level_filter,
            'user': user_filter,
            'date': date_filter,
            'search': search,
        }
    }
    return render(request, 'activity_logs/activity_logs.html', context)


@login_required
def conflict_logs(request):
    """Conflict logs view"""
    organization = get_current_organization(request)

    # Get filter parameters
    status_filter = request.GET.get('status', '')
    type_filter = request.GET.get('type', '')
    severity_filter = request.GET.get('severity', '')

    # Base queryset
    conflicts = ConflictLog.objects.filter(organization=organization)

    # Apply filters
    if status_filter:
        conflicts = conflicts.filter(status=status_filter)

    if type_filter:
        conflicts = conflicts.filter(conflict_type=type_filter)

    if severity_filter:
        conflicts = conflicts.filter(severity=severity_filter)

    # Pagination
    paginator = Paginator(conflicts, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get statistics
    stats = {
        'total_conflicts': conflicts.count(),
        'unresolved_conflicts': conflicts.filter(status='detected').count(),
        'resolved_conflicts': conflicts.filter(status='resolved').count(),
        'high_severity': conflicts.filter(severity='high').count(),
    }

    context = {
        'page_obj': page_obj,
        'stats': stats,
        'filters': {
            'status': status_filter,
            'type': type_filter,
            'severity': severity_filter,
        }
    }
    return render(request, 'activity_logs/conflict_logs.html', context)


@login_required
def detect_conflicts(request):
    """Manually trigger conflict detection"""
    if request.method == 'POST':
        organization = get_current_organization(request)

        try:
            # Run conflict detection
            conflicts = ConflictDetectionService.detect_conflicts(organization=organization)

            # Log detected conflicts
            conflict_count = 0
            for conflict in conflicts:
                ConflictDetectionService.log_conflict(conflict, organization, request.user)
                conflict_count += 1

            # Log the activity
            ActivityLog.log_activity(
                user=request.user,
                organization=organization,
                action='conflict_detected',
                description=f'Manual conflict detection run. Found {conflict_count} conflicts.',
                level='info' if conflict_count == 0 else 'warning',
                request=request
            )

            return JsonResponse({
                'success': True,
                'message': f'Conflict detection completed. Found {conflict_count} conflicts.',
                'conflicts_found': conflict_count
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Error during conflict detection: {str(e)}'
            })

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
def resolve_conflict(request, conflict_id):
    """Resolve a specific conflict"""
    if request.method == 'POST':
        organization = get_current_organization(request)
        conflict = get_object_or_404(ConflictLog, pk=conflict_id, organization=organization)

        try:
            data = request.POST
            resolution_strategy = data.get('strategy', 'manual')

            if resolution_strategy == 'manual':
                # Manual resolution
                conflict.status = 'resolved'
                conflict.resolved_by = request.user
                conflict.resolved_at = timezone.now()
                conflict.resolution_notes = data.get('notes', '')
                conflict.save()

                # Log the activity
                ActivityLog.log_activity(
                    user=request.user,
                    organization=organization,
                    action='conflict_resolved',
                    description=f'Conflict manually resolved: {conflict.description}',
                    content_object=conflict,
                    request=request
                )

                return JsonResponse({
                    'success': True,
                    'message': 'Conflict resolved manually.'
                })

            else:
                # Automatic resolution
                success = ConflictDetectionService.resolve_conflict(
                    conflict, resolution_strategy, request.user
                )

                if success:
                    # Log the activity
                    ActivityLog.log_activity(
                        user=request.user,
                        organization=organization,
                        action='conflict_resolved',
                        description=f'Conflict automatically resolved using {resolution_strategy} strategy',
                        content_object=conflict,
                        request=request
                    )

                    return JsonResponse({
                        'success': True,
                        'message': f'Conflict resolved using {resolution_strategy} strategy.'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'Failed to resolve conflict automatically.'
                    })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Error resolving conflict: {str(e)}'
            })

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
def activity_dashboard(request):
    """Activity dashboard with charts and statistics"""
    organization = get_current_organization(request)

    # Get date range (last 30 days)
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)

    # Activity statistics
    recent_logs = ActivityLog.objects.filter(
        organization=organization,
        created_at__date__gte=start_date
    )

    # Activity by day - optimized with single query
    cache_key = f'activity_by_day_{organization.id}_{start_date}_{end_date}'
    activity_by_day = cache.get(cache_key)

    if activity_by_day is None:
        # Use database aggregation instead of multiple queries - PostgreSQL compatible
        from django.db.models import DateField
        from django.db.models.functions import Cast
        daily_counts = recent_logs.annotate(
            day=Cast('created_at', DateField())
        ).values('day').annotate(count=Count('id')).order_by('day')

        # Convert to dictionary format
        activity_by_day = {}
        daily_dict = {item['day']: item['count'] for item in daily_counts}

        for i in range(30):
            date = start_date + timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            activity_by_day[date_str] = daily_dict.get(date_str, 0)

        # Cache for 5 minutes
        cache.set(cache_key, activity_by_day, 300)

    # Activity by action
    activity_by_action = recent_logs.values('action').annotate(
        count=Count('action')
    ).order_by('-count')[:10]

    # Activity by user
    activity_by_user = recent_logs.values('user__username').annotate(
        count=Count('user')
    ).order_by('-count')[:10]

    # Recent conflicts
    recent_conflicts = ConflictLog.objects.filter(
        organization=organization,
        created_at__date__gte=start_date
    ).order_by('-created_at')[:10]

    # System health metrics - cached for better performance
    health_cache_key = f'health_metrics_{organization.id}'
    health_metrics = cache.get(health_cache_key)

    if health_metrics is None:
        health_metrics = {
            'total_users': organization.organizationmembership_set.filter(is_active=True).count(),
            'active_shows': organization.shows.filter(is_active=True).count(),
            'pending_mentions': organization.clients.aggregate(
                total=Count('mention', filter=Q(mention__status='pending'))
            )['total'] or 0,
            'unresolved_conflicts': ConflictLog.objects.filter(
                organization=organization,
                status='detected'
            ).count(),
        }
        # Cache for 10 minutes
        cache.set(health_cache_key, health_metrics, 600)

    context = {
        'activity_by_day': activity_by_day,
        'activity_by_action': list(activity_by_action),
        'activity_by_user': list(activity_by_user),
        'recent_conflicts': recent_conflicts,
        'health_metrics': health_metrics,
        'date_range': {
            'start': start_date,
            'end': end_date,
        }
    }
    return render(request, 'activity_logs/dashboard.html', context)


@login_required
def load_more_activities(request):
    """API endpoint for loading more activities via AJAX"""
    organization = get_current_organization(request)

    # Get pagination parameters
    page = int(request.GET.get('page', 1))
    page_size = min(int(request.GET.get('page_size', 10)), 20)  # Limit max page size

    # Get filter parameters
    action_filter = request.GET.get('action', '')
    level_filter = request.GET.get('level', '')
    date_filter = request.GET.get('date', '')

    # Base queryset with select_related for better performance
    logs = ActivityLog.objects.filter(
        organization=organization
    ).select_related('user').order_by('-created_at')

    # Apply filters
    if action_filter:
        logs = logs.filter(action=action_filter)
    if level_filter:
        logs = logs.filter(level=level_filter)
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            logs = logs.filter(created_at__date=filter_date)
        except ValueError:
            pass

    # Pagination
    paginator = Paginator(logs, page_size)
    page_obj = paginator.get_page(page)

    # Serialize data
    activities = []
    for log in page_obj:
        activities.append({
            'id': log.id,
            'action': log.action,
            'action_display': log.get_action_display(),
            'description': log.description,
            'level': log.level,
            'level_display': log.get_level_display(),
            'user': log.user.username if log.user else 'System',
            'created_at': log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'created_at_relative': log.created_at.strftime('%b %d, %Y at %I:%M %p'),
            'ip_address': log.ip_address,
            'metadata': log.metadata,
        })

    return JsonResponse({
        'activities': activities,
        'has_next': page_obj.has_next(),
        'has_previous': page_obj.has_previous(),
        'current_page': page,
        'total_pages': paginator.num_pages,
        'total_count': paginator.count,
    })
