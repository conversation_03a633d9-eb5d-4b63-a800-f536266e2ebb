from django.db import models
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from apps.core.models import TimeStampedModel
import json


class ActivityLog(TimeStampedModel):
    """Model for tracking user activities and system events"""

    ACTION_CHOICES = [
        # Mention actions
        ('mention_created', 'Mention Created'),
        ('mention_updated', 'Mention Updated'),
        ('mention_deleted', 'Mention Deleted'),
        ('mention_approved', 'Mention Approved'),
        ('mention_rejected', 'Mention Rejected'),
        ('mention_scheduled', 'Mention Scheduled'),
        ('mention_completed', 'Mention Completed'),
        ('mention_cancelled', 'Mention Cancelled'),

        # Show actions
        ('show_created', 'Show Created'),
        ('show_updated', 'Show Updated'),
        ('show_deleted', 'Show Deleted'),
        ('presenter_assigned', 'Presenter Assigned'),
        ('presenter_removed', 'Presenter Removed'),

        # Client actions
        ('client_created', 'Client Created'),
        ('client_updated', 'Client Updated'),
        ('client_deleted', 'Client Deleted'),

        # Presenter actions
        ('presenter_created', 'Presenter Created'),
        ('presenter_updated', 'Presenter Updated'),
        ('presenter_deleted', 'Presenter Deleted'),

        # User actions
        ('user_login', 'User Login'),
        ('user_logout', 'User Logout'),
        ('user_created', 'User Created'),
        ('user_updated', 'User Updated'),
        ('user_deleted', 'User Deleted'),
        ('password_changed', 'Password Changed'),

        # System actions
        ('settings_updated', 'Settings Updated'),
        ('conflict_detected', 'Conflict Detected'),
        ('conflict_resolved', 'Conflict Resolved'),
        ('backup_created', 'Backup Created'),
        ('data_exported', 'Data Exported'),
        ('data_imported', 'Data Imported'),

        # Security actions
        ('failed_login', 'Failed Login'),
        ('permission_denied', 'Permission Denied'),
        ('suspicious_activity', 'Suspicious Activity'),
    ]

    LEVEL_CHOICES = [
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('critical', 'Critical'),
    ]

    # Who performed the action
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='activity_logs'
    )

    # What action was performed
    action = models.CharField(max_length=50, choices=ACTION_CHOICES)
    level = models.CharField(max_length=10, choices=LEVEL_CHOICES, default='info')
    description = models.TextField()

    # What object was affected (generic foreign key)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')

    # Additional context data
    metadata = models.JSONField(default=dict, blank=True)

    # Request information
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['organization', '-created_at']),
            models.Index(fields=['user', '-created_at']),
            models.Index(fields=['action', '-created_at']),
            models.Index(fields=['level', '-created_at']),
        ]

    def __str__(self):
        user_str = self.user.username if self.user else 'System'
        return f"{user_str} - {self.get_action_display()} at {self.created_at}"

    @classmethod
    def log_activity(cls, user, organization, action, description, content_object=None,
                    level='info', metadata=None, request=None):
        """Helper method to create activity logs"""
        log_data = {
            'user': user,
            'organization': organization,
            'action': action,
            'description': description,
            'level': level,
            'metadata': metadata or {},
        }

        if content_object:
            log_data['content_object'] = content_object

        if request:
            log_data['ip_address'] = cls._get_client_ip(request)
            log_data['user_agent'] = request.META.get('HTTP_USER_AGENT', '')

        return cls.objects.create(**log_data)

    @staticmethod
    def _get_client_ip(request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ConflictLog(TimeStampedModel):
    """Model for tracking scheduling conflicts"""

    CONFLICT_TYPES = [
        ('time_overlap', 'Time Overlap'),
        ('presenter_double_booking', 'Presenter Double Booking'),
        ('show_capacity_exceeded', 'Show Capacity Exceeded'),
        ('resource_conflict', 'Resource Conflict'),
    ]

    RESOLUTION_STATUS = [
        ('detected', 'Detected'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('ignored', 'Ignored'),
    ]

    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='conflict_logs'
    )

    conflict_type = models.CharField(max_length=30, choices=CONFLICT_TYPES)
    status = models.CharField(max_length=20, choices=RESOLUTION_STATUS, default='detected')

    # Conflicting mentions
    mention1 = models.ForeignKey('mentions.Mention', on_delete=models.CASCADE, related_name='conflicts_as_first')
    mention2 = models.ForeignKey('mentions.Mention', on_delete=models.CASCADE, related_name='conflicts_as_second')

    # Conflict details
    description = models.TextField()
    severity = models.CharField(
        max_length=10,
        choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High')],
        default='medium'
    )

    # Resolution information
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['organization', 'status', '-created_at']),
            models.Index(fields=['conflict_type', '-created_at']),
        ]

    def __str__(self):
        return f"{self.get_conflict_type_display()} - {self.mention1.title} vs {self.mention2.title}"
