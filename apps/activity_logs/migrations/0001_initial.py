# Generated by Django 4.2.7 on 2025-06-11 07:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("mentions", "0001_initial"),
        ("organizations", "0001_initial"),
        ("contenttypes", "0002_remove_content_type_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ConflictLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "conflict_type",
                    models.CharField(
                        choices=[
                            ("time_overlap", "Time Overlap"),
                            ("presenter_double_booking", "Presenter Double Booking"),
                            ("show_capacity_exceeded", "Show Capacity Exceeded"),
                            ("resource_conflict", "Resource Conflict"),
                        ],
                        max_length=30,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("detected", "Detected"),
                            ("acknowledged", "Acknowledged"),
                            ("resolved", "Resolved"),
                            ("ignored", "Ignored"),
                        ],
                        default="detected",
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="medium",
                        max_length=10,
                    ),
                ),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("resolution_notes", models.TextField(blank=True)),
                (
                    "mention1",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conflicts_as_first",
                        to="mentions.mention",
                    ),
                ),
                (
                    "mention2",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conflicts_as_second",
                        to="mentions.mention",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conflict_logs",
                        to="organizations.organization",
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["organization", "status", "-created_at"],
                        name="activity_lo_organiz_a33828_idx",
                    ),
                    models.Index(
                        fields=["conflict_type", "-created_at"],
                        name="activity_lo_conflic_2c2912_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="ActivityLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "action",
                    models.CharField(
                        choices=[
                            ("mention_created", "Mention Created"),
                            ("mention_updated", "Mention Updated"),
                            ("mention_deleted", "Mention Deleted"),
                            ("mention_approved", "Mention Approved"),
                            ("mention_rejected", "Mention Rejected"),
                            ("mention_scheduled", "Mention Scheduled"),
                            ("mention_completed", "Mention Completed"),
                            ("mention_cancelled", "Mention Cancelled"),
                            ("show_created", "Show Created"),
                            ("show_updated", "Show Updated"),
                            ("show_deleted", "Show Deleted"),
                            ("presenter_assigned", "Presenter Assigned"),
                            ("presenter_removed", "Presenter Removed"),
                            ("client_created", "Client Created"),
                            ("client_updated", "Client Updated"),
                            ("client_deleted", "Client Deleted"),
                            ("presenter_created", "Presenter Created"),
                            ("presenter_updated", "Presenter Updated"),
                            ("presenter_deleted", "Presenter Deleted"),
                            ("user_login", "User Login"),
                            ("user_logout", "User Logout"),
                            ("user_created", "User Created"),
                            ("user_updated", "User Updated"),
                            ("user_deleted", "User Deleted"),
                            ("password_changed", "Password Changed"),
                            ("settings_updated", "Settings Updated"),
                            ("conflict_detected", "Conflict Detected"),
                            ("conflict_resolved", "Conflict Resolved"),
                            ("backup_created", "Backup Created"),
                            ("data_exported", "Data Exported"),
                            ("data_imported", "Data Imported"),
                            ("failed_login", "Failed Login"),
                            ("permission_denied", "Permission Denied"),
                            ("suspicious_activity", "Suspicious Activity"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("info", "Info"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("critical", "Critical"),
                        ],
                        default="info",
                        max_length=10,
                    ),
                ),
                ("description", models.TextField()),
                ("object_id", models.PositiveIntegerField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                (
                    "content_type",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="contenttypes.contenttype",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activity_logs",
                        to="organizations.organization",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["organization", "-created_at"],
                        name="activity_lo_organiz_59133f_idx",
                    ),
                    models.Index(
                        fields=["user", "-created_at"],
                        name="activity_lo_user_id_04b3d2_idx",
                    ),
                    models.Index(
                        fields=["action", "-created_at"],
                        name="activity_lo_action_182b05_idx",
                    ),
                    models.Index(
                        fields=["level", "-created_at"],
                        name="activity_lo_level_a8d253_idx",
                    ),
                ],
            },
        ),
    ]
