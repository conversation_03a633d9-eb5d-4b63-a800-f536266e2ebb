# PDF Template Designer Guide

## 🎨 **Complete Template Design System**

The PDF Template Designer allows you to create custom PDF layouts through a visual interface that generates JSON configurations. This gives you complete control over your report layouts without writing code.

## 🚀 **How to Access**

1. **From Reports Dashboard:** Click "Template Designer" button
2. **From Dynamic Reports:** Click "Template Designer" button  
3. **Direct URL:** `/reports/template-designer/`

## 🛠️ **Template Designer Features**

### **1. Basic Settings**
- **Template Name:** Human-readable name for your template
- **Report Type Key:** Unique identifier (used in URLs)
- **Description:** What this report shows

### **2. PDF Layout Configuration**
- **Orientation:** Portrait (P) or Landscape (L)
- **Data Model:** Choose between Mention or MentionReading
- **Ordering:** How to sort the data

### **3. Dynamic Filters**
Add filters that users can apply when generating reports:
- **Filter Name:** Parameter name (e.g., 'start_date')
- **Database Field:** Django field lookup (e.g., 'created_at__date__gte')
- **Type:** String, Integer, or Date

### **4. Table Columns**
Design your PDF table structure:
- **Header:** Column title in PDF
- **Field:** Database field to display
- **Transform:** How to format the data
- **Width:** Column width in millimeters
- **Options:** Additional formatting options

## 📊 **Available Field Transforms**

### **Text Transforms**
- **str:** Plain text
- **truncate:** Shortened text (use options: `length:40`)

### **Date/Time Transforms**
- **datetime:** Formatted date/time (use options: `format:%Y-%m-%d`)

### **Special Transforms**
- **duration:** Adds 's' suffix for seconds
- **priority:** Converts numbers to Low/Normal/High/Urgent
- **show_slot:** Formats show time slots

## 🎯 **Step-by-Step Template Creation**

### **Step 1: Basic Setup**
```
Template Name: "My Custom Client Report"
Report Key: "my-client-report"
Description: "Custom client mentions with specific layout"
Orientation: Portrait
Data Model: Mention
```

### **Step 2: Add Filters**
```
Filter 1: start_date → created_at__date__gte → date
Filter 2: end_date → created_at__date__lte → date
Filter 3: client_id → client_id → int
Filter 4: status → status → str
```

### **Step 3: Design Columns**
```
Column 1: "Client" → client → str → 40mm
Column 2: "Date" → created_at → datetime → 30mm → format:%m/%d/%Y
Column 3: "Title" → title → truncate → 60mm → length:30
Column 4: "Status" → status → str → 25mm
```

### **Step 4: Generate & Test**
1. Click "Generate JSON" to see configuration
2. Click "Save Template" to store it
3. Click "Test PDF" to generate a sample

## 📁 **JSON Output Format**

The designer generates JSON in this format:

```json
{
  "my-client-report": {
    "name": "My Custom Client Report",
    "description": "Custom client mentions with specific layout",
    "model": "Mention",
    "filters": {
      "start_date": {"field": "created_at__date__gte", "type": "date"},
      "end_date": {"field": "created_at__date__lte", "type": "date"},
      "client_id": {"field": "client_id", "type": "int"},
      "status": {"field": "status", "type": "str"}
    },
    "ordering": "-created_at",
    "pdf_config": {
      "title": "My Custom Client Report",
      "orientation": "P",
      "headers": ["Client", "Date", "Title", "Status"],
      "col_widths": [40, 30, 60, 25],
      "row_data": [
        {"field": "client", "transform": "str"},
        {"field": "created_at", "transform": "datetime", "format": "%m/%d/%Y"},
        {"field": "title", "transform": "truncate", "length": 30},
        {"field": "status", "transform": "str"}
      ]
    }
  }
}
```

## 💾 **Template Storage**

### **Local Storage (Browser)**
- Templates saved in browser localStorage
- Available only on the same browser/computer
- Good for testing and personal use

### **Server Storage (Future)**
- Templates saved to database
- Available across all users
- Persistent and shareable

## 🔧 **Advanced Customization**

### **Custom Field Options**
For truncate transform:
```
Options: length:40,default:N/A
```

For datetime transform:
```
Options: format:%B %d %Y,default:Not Set
```

### **Available Database Fields**

**Mention Model:**
- `client` / `client.name`
- `title`, `content`, `status`, `priority`
- `duration_seconds`
- `created_at`, `created_by.username`
- `approved_by.username`

**MentionReading Model:**
- `mention.title`, `mention.client.name`
- `show.name`, `presenter.name`
- `actual_read_time`
- `mention.status`, `mention.duration_seconds`

## 🎨 **Layout Tips**

### **Portrait Layout (P)**
- Total width: ~175mm
- Good for 3-5 columns
- Better for detailed content

### **Landscape Layout (L)**
- Total width: ~250mm
- Good for 6-9 columns
- Better for wide tables

### **Column Width Guidelines**
- Short text (Status): 20-25mm
- Dates: 30-35mm
- Names/Titles: 40-60mm
- Long content: 60-80mm

## 🚀 **Using Your Templates**

### **1. Test Templates**
- Use "Test PDF" button in designer
- Generates sample PDF with current data

### **2. Generate Reports**
- Access via: `/reports/dynamic/{your-report-key}/generate/`
- Add filters: `?start_date=2024-01-01&client_id=5`

### **3. Share Templates**
- Copy JSON from preview
- Share with other developers
- Import into other systems

## 🔄 **Template Management**

### **Save Template**
- Stores template for future use
- Appears in "Saved Templates" list

### **Load Template**
- Click template in saved list
- Loads all settings into designer
- Modify and save as new template

### **Delete Template**
- Click trash icon in saved list
- Permanently removes template

## 🎯 **Best Practices**

1. **Start Simple:** Begin with basic columns, add complexity gradually
2. **Test Early:** Use "Test PDF" frequently during design
3. **Consistent Naming:** Use clear, descriptive template names
4. **Width Planning:** Ensure column widths add up appropriately
5. **Filter Wisely:** Add filters users will actually need

## 🔧 **Troubleshooting**

### **PDF Generation Errors**
- Check field names match database
- Verify column widths aren't too large
- Ensure transform options are valid

### **Missing Data**
- Check model relationships
- Verify field paths (use dots for relations)
- Add default values for optional fields

### **Layout Issues**
- Adjust column widths
- Consider orientation change
- Reduce content length with truncate

The Template Designer gives you complete control over PDF layouts while maintaining the power and flexibility of the dynamic reports system! 🎉
