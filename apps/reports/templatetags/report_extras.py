from django import template

register = template.Library()

@register.filter
def lookup(dictionary, key):
    """Template filter to lookup dictionary values by key"""
    if isinstance(dictionary, dict):
        return dictionary.get(key, [])
    return []

@register.filter
def dict_length(dictionary):
    """Template filter to get total length of all values in a dictionary"""
    if isinstance(dictionary, dict):
        total = 0
        for value in dictionary.values():
            if isinstance(value, list):
                total += len(value)
            else:
                total += 1
        return total
    return 0

@register.filter
def time_slot_count(schedule_data, time_slot):
    """Count mentions for a specific time slot across all dates"""
    count = 0
    for date_data in schedule_data.values():
        if time_slot in date_data:
            count += len(date_data[time_slot])
    return count

@register.filter
def div(value, divisor):
    """Template filter to divide two numbers"""
    try:
        return float(value) / float(divisor)
    except (ValueError, ZeroDivisionError, TypeError):
        return 0

@register.filter
def percentage(value, total):
    """Template filter to calculate percentage"""
    try:
        if total and float(total) > 0:
            return round((float(value) / float(total)) * 100, 1)
        return 0
    except (ValueError, ZeroDivisionError, TypeError):
        return 0
