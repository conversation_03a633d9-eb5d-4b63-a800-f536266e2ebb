from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, JsonResponse
from django.db.models import Count, Avg, Q, Sum, DateField
from django.db.models.functions import Cast, Extract
from django.contrib.auth.models import User
from django.utils import timezone
from django.contrib import messages
from datetime import datetime, timedelta
import json
from .models import PDFTemplate, PDFDocument
from .pdf_generator import PDFTemplateEngine, create_pdf_response
from apps.mentions.models import Mention, MentionReading
from apps.core.models import Client, Presenter
from apps.shows.models import Show
from django.db.models import Q
from django.utils.dateparse import parse_date
from fpdf import FPDF


@login_required
def report_list(request):
    """List available reports"""
    # Calculate basic statistics
    total_mentions = Mention.objects.count()
    completed_mentions = MentionReading.objects.filter(actual_read_time__isnull=False).count()
    pending_mentions = Mention.objects.filter(status='pending').count()
    active_clients = Client.objects.filter(is_active=True).count()

    context = {
        'total_mentions': total_mentions,
        'completed_mentions': completed_mentions,
        'pending_mentions': pending_mentions,
        'active_clients': active_clients,
    }
    return render(request, 'reports/report_list.html', context)


@login_required
def analytics(request):
    """Enhanced Analytics dashboard"""
    # Get time range from request (default to last 30 days)
    time_range = request.GET.get('time_range', '30')
    try:
        days = int(time_range)
    except ValueError:
        days = 30

    # Calculate date range
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=days)

    # Calculate key metrics for the time period
    mentions_in_period = Mention.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    total_mentions = mentions_in_period.count()
    approved_mentions = mentions_in_period.filter(status='approved').count()
    pending_mentions = mentions_in_period.filter(status='pending').count()
    rejected_mentions = mentions_in_period.filter(status='rejected').count()

    # Completion metrics
    completed_readings = MentionReading.objects.filter(
        mention__in=mentions_in_period,
        actual_read_time__isnull=False
    ).count()
    total_readings = MentionReading.objects.filter(mention__in=mentions_in_period).count()
    completion_rate = (completed_readings / total_readings * 100) if total_readings > 0 else 0

    # Client metrics
    active_clients = Client.objects.filter(is_active=True).count()
    clients_with_mentions = mentions_in_period.values('client').distinct().count()

    # Duration metrics
    avg_duration = mentions_in_period.aggregate(avg=Avg('duration_seconds'))['avg'] or 0

    # Top clients by mention count in period
    top_clients = Client.objects.annotate(
        mention_count=Count('mention', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date
        ))
    ).filter(mention_count__gt=0).order_by('-mention_count')[:5]

    # Priority breakdown for the period
    high_priority = mentions_in_period.filter(priority__in=[3, 4]).count()
    medium_priority = mentions_in_period.filter(priority=2).count()
    low_priority = mentions_in_period.filter(priority=1).count()

    # Show performance in period
    show_performance = Show.objects.annotate(
        mention_count=Count('mentionreading__mention', filter=Q(
            mentionreading__mention__created_at__date__gte=start_date,
            mentionreading__mention__created_at__date__lte=end_date
        )),
        completed_count=Count('mentionreading', filter=Q(
            mentionreading__mention__created_at__date__gte=start_date,
            mentionreading__mention__created_at__date__lte=end_date,
            mentionreading__actual_read_time__isnull=False
        ))
    ).filter(mention_count__gt=0).order_by('-completed_count')[:4]

    # Daily breakdown for charts
    daily_data = []
    for i in range(days):
        current_date = start_date + timedelta(days=i)
        daily_mentions = mentions_in_period.filter(created_at__date=current_date).count()
        daily_completed = MentionReading.objects.filter(
            actual_read_time__date=current_date,
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date
        ).count()
        daily_data.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'day_name': current_date.strftime('%a'),
            'mentions': daily_mentions,
            'completed': daily_completed
        })

    # Recent activities with more detail
    recent_activities = []
    recent_mentions = Mention.objects.select_related('client').order_by('-created_at')[:10]
    for mention in recent_mentions:
        # Check if it has been read
        reading = MentionReading.objects.filter(mention=mention).first()
        if reading and reading.actual_read_time:
            activity_type = 'completed'
            description = f'Mention "{mention.title}" completed on {reading.show.name if reading.show else "Unknown Show"}'
            timestamp = reading.actual_read_time
        else:
            activity_type = 'created'
            description = f'New mention "{mention.title}" created for {mention.client.name}'
            timestamp = mention.created_at

        recent_activities.append({
            'type': activity_type,
            'description': description,
            'timestamp': timestamp.strftime('%Y-%m-%d %H:%M'),
            'relative_time': _get_relative_time(timestamp)
        })

    # Calculate trends (comparison with previous period)
    prev_start_date = start_date - timedelta(days=days)
    prev_end_date = start_date

    prev_mentions = Mention.objects.filter(
        created_at__date__gte=prev_start_date,
        created_at__date__lt=prev_end_date
    ).count()

    prev_completed = MentionReading.objects.filter(
        actual_read_time__date__gte=prev_start_date,
        actual_read_time__date__lt=prev_end_date
    ).count()

    # Calculate percentage changes
    mentions_change = ((total_mentions - prev_mentions) / prev_mentions * 100) if prev_mentions > 0 else 0
    completed_change = ((completed_readings - prev_completed) / prev_completed * 100) if prev_completed > 0 else 0

    context = {
        'time_range': time_range,
        'start_date': start_date,
        'end_date': end_date,
        'total_mentions': total_mentions,
        'approved_mentions': approved_mentions,
        'pending_mentions': pending_mentions,
        'rejected_mentions': rejected_mentions,
        'completion_rate': round(completion_rate, 1),
        'completed_readings': completed_readings,
        'active_clients': active_clients,
        'clients_with_mentions': clients_with_mentions,
        'avg_duration': round(avg_duration, 1),
        'top_clients': top_clients,
        'high_priority': high_priority,
        'medium_priority': medium_priority,
        'low_priority': low_priority,
        'show_performance': show_performance,
        'daily_data': daily_data,
        'recent_activities': recent_activities,
        'mentions_change': round(mentions_change, 1),
        'completed_change': round(completed_change, 1),
    }
    return render(request, 'reports/analytics.html', context)


def _get_relative_time(timestamp):
    """Helper function to get relative time string"""
    now = timezone.now()

    # Ensure both timestamps are timezone-aware
    if timestamp.tzinfo is None:
        timestamp = timezone.make_aware(timestamp)

    diff = now - timestamp

    if diff.days > 0:
        return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hour{'s' if hours > 1 else ''} ago"
    elif diff.seconds > 60:
        minutes = diff.seconds // 60
        return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
    else:
        return "Just now"


@login_required

def generate_pdf_report(request, report_type):
    pdf = FPDF()
    pdf.add_page()
    pdf.set_font("Arial", size=12)
    pdf.cell(200, 10, txt=f"{report_type.title()} Report", ln=True, align='C')

    # Output as string and encode to bytes
    pdf_output = pdf.output(dest='S').encode('latin-1')

    response = HttpResponse(pdf_output, content_type='application/pdf')
    response['Content-Disposition'] = f'inline; filename="{report_type}.pdf"'
    return response

@login_required
def export_report(request):
    """Export report in various formats including PDF"""
    # Get report type and format from request
    report_type = request.GET.get('type', 'mentions-summary')
    export_format = request.GET.get('format', 'csv')

    if export_format not in ['csv', 'pdf', 'excel']:
        return JsonResponse({'error': 'Invalid export format'}, status=400)

    # Handle PDF export using the enhanced PDF generation
    if export_format == 'pdf':
        # Use the enhanced PDF generation function
        return generate_pdf_report(request, report_type)

    # For CSV and Excel, return development message
    return JsonResponse({
        'message': f'Export functionality for {report_type} in {export_format} format is being developed.',
        'status': 'pending'
    })


@login_required
def generate_pdf_report(request, report_type=None):
    """Generate PDF report using enhanced direct FPDF generation"""
    if not report_type:
        report_type = request.GET.get('type', 'mentions-summary')

    # Get PDF options from request
    pdf_options = {
        'orientation': 'landscape' if request.GET.get('pageOrientation') == 'landscape' else 'portrait',
        'font_size': int(request.GET.get('fontSize', 12)),
        'include_logo': request.GET.get('includeLogo', 'true').lower() == 'true',
        'include_summary': request.GET.get('includeSummary', 'true').lower() == 'true',
        'include_charts': request.GET.get('includeCharts', 'false').lower() == 'true'
    }

    # Generate report data based on type
    data = _get_report_data(request, report_type)

    # Check for errors in data
    if data.get('error'):
        return JsonResponse({'error': data['error']}, status=400)

    # Generate PDF using direct enhanced FPDF
    from .pdf_generator import create_direct_pdf, create_pdf_response
    from apps.organizations.middleware import get_current_organization

    # Get current organization for header information
    current_org = get_current_organization(request)
    if current_org:
        pdf_options['organization'] = {
            'name': current_org.name,
            'address': current_org.address,
            'phone': current_org.phone,
            'logo_path': current_org.logo.path if current_org.logo else None
        }
    else:
        # Default organization information for template placeholders
        pdf_options['organization'] = {
            'name': 'Radiocity',
            'address': 'Located in: Nkrumah Nasser\nLink Plaza\nAddress: Nkrumah Nasser\nLink Plaza, Kampala',
            'phone': '0700 487299',
            'logo_path': None
        }

    # Create custom title if needed
    title = request.GET.get('title')
    if not title:
        title = f"{report_type.replace('-', ' ').title()} Report"

    # Generate enhanced PDF with options
    pdf = create_direct_pdf(report_type, data, title, pdf_options)

    # Return PDF response
    filename = f"{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
    return create_pdf_response(pdf, filename)


@login_required
def test_pdf_generation(request):
    """Simple test to verify PDF generation is working"""
    from .pdf_generator import EnhancedPDF, create_pdf_response

    try:
        # Create a simple test PDF
        pdf = EnhancedPDF()
        pdf.add_page()

        # Add simple content
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, 'PDF Generation Test', 0, 1, 'C')
        pdf.ln(10)

        pdf.set_font('Arial', '', 12)
        pdf.cell(0, 10, 'This is a test PDF to verify generation is working.', 0, 1)
        pdf.cell(0, 10, f'Generated at: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 0, 1)
        pdf.ln(10)

        # Add some test metrics
        pdf.add_section_header('Test Metrics', level=1)
        current_y = pdf.get_y()

        test_metrics = {
            'Test 1': '100%',
            'Test 2': '50',
            'Test 3': 'OK',
            'Test 4': '25'
        }

        for i, (key, value) in enumerate(test_metrics.items()):
            x = 10 + i * 47.5
            pdf.add_metric_card(key, value, x, current_y, 45, 25)

        pdf.set_y(current_y + 35)

        filename = f"test_pdf_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        return create_pdf_response(pdf, filename)

    except Exception as e:
        return JsonResponse({
            'error': f'PDF test failed: {str(e)}',
            'traceback': str(e.__class__.__name__)
        }, status=500)


@login_required
def test_pdf_inline(request):
    """Test PDF generation with inline display"""
    from .pdf_generator import EnhancedPDF

    try:
        # Create a simple test PDF
        pdf = EnhancedPDF()
        pdf.add_page()

        # Add simple content
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, 'Inline PDF Test', 0, 1, 'C')
        pdf.ln(10)

        pdf.set_font('Arial', '', 12)
        pdf.cell(0, 10, 'This PDF should display inline in the browser.', 0, 1)
        pdf.cell(0, 10, f'Generated at: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 0, 1)

        # Generate PDF output
        pdf_output = pdf.output(dest='S')

        # Create inline response
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="test_inline.pdf"'
        response['Content-Length'] = len(pdf_output)

        if isinstance(pdf_output, (bytes, bytearray)):
            response.write(pdf_output)
        else:
            response.write(pdf_output.encode('latin-1'))

        return response

    except Exception as e:
        return JsonResponse({
            'error': f'Inline PDF test failed: {str(e)}',
            'traceback': str(e.__class__.__name__)
        }, status=500)



@login_required
def pdf_test_page(request):
    """Simple HTML page with PDF test links"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>PDF Test Page</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .test-link { display: block; margin: 10px 0; padding: 10px; background: #f0f0f0; text-decoration: none; color: #333; border-radius: 5px; }
            .test-link:hover { background: #e0e0e0; }
            h1 { color: #333; }
            .status { margin: 20px 0; padding: 10px; background: #e8f5e8; border-radius: 5px; }
        </style>
    </head>
    <body>
        <h1>PDF Generation Test Page</h1>
        <div class="status">
            <strong>Status:</strong> PDF generation is working correctly on the server.<br>
            If you see "Failed to load PDF document" errors, try the download links below.
        </div>

        <h2>Test Links:</h2>
        <a href="/reports/test-pdf/" class="test-link">📄 Test PDF (Download)</a>
        <a href="/reports/test-pdf-inline/" class="test-link">📄 Test PDF (Inline)</a>
        <a href="/reports/export/?type=mentions-summary&format=pdf" class="test-link">📊 Mentions Summary PDF</a>
        <a href="/reports/export/?type=analytics&format=pdf" class="test-link">📈 Analytics PDF</a>
        <a href="/reports/export/?type=client-activity&format=pdf" class="test-link">👥 Client Activity PDF</a>
        <a href="/reports/export/?type=daily-summary&format=pdf" class="test-link">📅 Daily Summary PDF</a>

        <h2>Troubleshooting:</h2>
        <ul>
            <li>If PDFs don't display inline, they should download automatically</li>
            <li>Check your browser's download folder</li>
            <li>Try right-clicking and "Save Link As..." if needed</li>
            <li>All PDFs are generated successfully (check server logs)</li>
        </ul>

        <p><a href="/reports/">← Back to Reports</a></p>
    </body>
    </html>
    """
    return HttpResponse(html_content)


@login_required
def enhanced_pdf_tool(request):
    """Enhanced PDF generation tool interface - handles any report type dynamically"""
    from datetime import datetime, timedelta
    from apps.core.models import Client
    import inspect

    # Dynamically discover all available report types by scanning _get_report_data function
    def get_all_available_report_types():
        """Dynamically discover all supported report types"""
        report_types = {}

        # Get the source code of _get_report_data to find all supported types
        source_lines = inspect.getsource(_get_report_data).split('\n')

        # Parse the function to extract report types
        for line in source_lines:
            line = line.strip()
            if line.startswith("if report_type == '") or line.startswith("elif report_type == '"):
                # Extract report type from condition
                start = line.find("'") + 1
                end = line.find("'", start)
                if start > 0 and end > start:
                    report_type = line[start:end]
                    report_types[report_type] = {
                        'name': report_type.replace('-', ' ').title(),
                        'description': f'Generated {report_type.replace("-", " ")} report',
                        'icon': _get_report_icon(report_type)
                    }
            elif line.startswith("elif report_type in ["):
                # Extract multiple report types from list
                start = line.find("[") + 1
                end = line.find("]")
                if start > 0 and end > start:
                    types_str = line[start:end]
                    # Parse the list of types
                    import re
                    types = re.findall(r"'([^']+)'", types_str)
                    for report_type in types:
                        report_types[report_type] = {
                            'name': report_type.replace('-', ' ').title(),
                            'description': f'Generated {report_type.replace("-", " ")} report',
                            'icon': _get_report_icon(report_type)
                        }

        # Add some manual overrides for better descriptions
        manual_descriptions = {
            'mentions-summary': 'Key metrics and recent mentions overview',
            'analytics': 'Performance trends and KPI analysis',
            'client-activity': 'Client performance and engagement metrics',
            'mentions-by-client': 'Client-specific mention breakdown',
            'client-mentions-detailed': 'Comprehensive client mention analysis',
            'show-performance': 'Individual show performance metrics',
            'show-schedule': 'Upcoming show schedule overview',
            'presenter-activity': 'Presenter performance and activity',
            'client-revenue': 'Revenue analysis by client',
            'client-retention': 'Client retention and loyalty metrics',
            'client-order-schedule': 'Scheduled orders and delivery timeline',
            'daily-summary': 'Daily performance snapshot',
            'weekly-trends': 'Weekly performance trends and patterns',
            'monthly-overview': 'Monthly performance comprehensive review',
            'mentions-by-priority': 'Priority-based mention analysis'
        }

        # Update descriptions with manual overrides
        for report_type, description in manual_descriptions.items():
            if report_type in report_types:
                report_types[report_type]['description'] = description

        return report_types

    # Get all available report types dynamically
    report_types = get_all_available_report_types()

    # Get default date range (last 30 days)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)

    # Get all clients for filtering
    clients = Client.objects.all().order_by('name')

    context = {
        'report_types': report_types,
        'clients': clients,
        'default_start_date': start_date.strftime('%Y-%m-%d'),
        'default_end_date': end_date.strftime('%Y-%m-%d'),
    }

    return render(request, 'reports/enhanced_pdf_tool.html', context)


def _get_report_icon(report_type):
    """Get appropriate icon for report type"""
    icon_mapping = {
        'mentions-summary': 'fas fa-bullhorn',
        'analytics': 'fas fa-chart-line',
        'client-activity': 'fas fa-users',
        'mentions-by-client': 'fas fa-user-tie',
        'client-mentions-detailed': 'fas fa-list-alt',
        'show-performance': 'fas fa-microphone',
        'show-schedule': 'fas fa-calendar-alt',
        'presenter-activity': 'fas fa-user-circle',
        'client-revenue': 'fas fa-dollar-sign',
        'client-retention': 'fas fa-heart',
        'client-order-schedule': 'fas fa-clipboard-list',
        'daily-summary': 'fas fa-calendar-day',
        'weekly-trends': 'fas fa-chart-area',
        'monthly-overview': 'fas fa-calendar',
        'mentions-by-priority': 'fas fa-exclamation-triangle'
    }

    # Default icons based on keywords
    if 'client' in report_type:
        return icon_mapping.get(report_type, 'fas fa-user')
    elif 'show' in report_type or 'presenter' in report_type:
        return icon_mapping.get(report_type, 'fas fa-microphone')
    elif 'mention' in report_type:
        return icon_mapping.get(report_type, 'fas fa-bullhorn')
    elif 'analytics' in report_type or 'trend' in report_type:
        return icon_mapping.get(report_type, 'fas fa-chart-line')
    elif 'daily' in report_type or 'weekly' in report_type or 'monthly' in report_type:
        return icon_mapping.get(report_type, 'fas fa-calendar')
    else:
        return icon_mapping.get(report_type, 'fas fa-file-alt')


def _get_report_data(request, report_type):
    """Get data for PDF report generation"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    if report_type == 'mentions-summary':
        return _get_mentions_summary_data(start_date, end_date)
    elif report_type in ['mentions-by-client', 'client-report']:
        client_id = request.GET.get('client_id')
        return _get_client_report_data(start_date, end_date, client_id)
    elif report_type == 'client-mentions-detailed':
        client_id = request.GET.get('client_id')
        status_filter = request.GET.get('status', 'all')
        return _get_client_mentions_detailed_data(start_date, end_date, client_id, status_filter)
    elif report_type == 'mentions-by-priority':
        return _get_mentions_by_priority_data(start_date, end_date)
    elif report_type == 'show-performance':
        return _get_show_performance_data(start_date, end_date)
    elif report_type == 'show-schedule':
        return _get_show_schedule_data()
    elif report_type == 'show-readings':
        show_id = request.GET.get('show_id')
        client_id = request.GET.get('client_id')
        presenter_id = request.GET.get('presenter_id')
        status = request.GET.get('status')
        priority = request.GET.get('priority')
        return _get_show_readings_data(start_date, end_date, show_id, client_id, presenter_id, status, priority)
    elif report_type == 'presenter-activity':
        return _get_presenter_activity_data(start_date, end_date)
    elif report_type == 'client-activity':
        return _get_client_activity_data(start_date, end_date)
    elif report_type == 'client-revenue':
        return _get_client_revenue_data(start_date, end_date)
    elif report_type == 'client-retention':
        return _get_client_retention_data(start_date, end_date)
    elif report_type == 'client-order-schedule':
        client_id = request.GET.get('client_id')
        return _get_client_order_schedule_data(start_date, end_date, client_id)
    elif report_type == 'analytics':
        return _get_analytics_data(start_date, end_date)
    elif report_type == 'daily-summary':
        report_date = request.GET.get('date', end_date)
        if isinstance(report_date, str):
            report_date = datetime.strptime(report_date, '%Y-%m-%d').date()
        return _get_daily_summary_data(report_date)
    elif report_type == 'weekly-trends':
        return _get_weekly_trends_data(start_date, end_date)
    elif report_type == 'monthly-overview':
        month = request.GET.get('month', end_date.month)
        year = request.GET.get('year', end_date.year)
        return _get_monthly_overview_data(int(year), int(month))
    else:
        # Try to find a specific data function for this report type
        data_function_name = f'_get_{report_type.replace("-", "_")}_data'

        # Check if specific data function exists
        current_module = globals()
        if data_function_name in current_module:
            try:
                data_function = current_module[data_function_name]
                # Try calling with common parameters
                if 'client' in report_type:
                    client_id = request.GET.get('client_id')
                    return data_function(start_date, end_date, client_id)
                else:
                    return data_function(start_date, end_date)
            except Exception as e:
                print(f"Error calling {data_function_name}: {e}")
                return _get_generic_report_data(report_type, start_date, end_date, request)
        else:
            # Fallback to generic data collection
            return _get_generic_report_data(report_type, start_date, end_date, request)


def _get_generic_report_data(report_type, start_date, end_date, request):
    """Generate generic data for any report type"""
    from apps.core.models import Client, Presenter
    from apps.shows.models import Show
    from apps.mentions.models import Mention, MentionReading
    from django.db.models import Count, Q, Avg

    # Basic data collection based on report type keywords
    data = {
        'report_type': report_type,
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'generated_at': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    # Collect relevant data based on report type keywords
    if 'mention' in report_type:
        mentions = Mention.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        )
        data['mention_stats'] = {
            'total_mentions': mentions.count(),
            'approved_mentions': mentions.filter(status='approved').count(),
            'pending_mentions': mentions.filter(status='pending').count(),
            'rejected_mentions': mentions.filter(status='rejected').count()
        }

        # Add recent mentions sample
        recent_mentions = mentions.order_by('-created_at')[:10]
        data['recent_mentions'] = [
            {
                'title': mention.title,
                'client': mention.client.name if mention.client else 'N/A',
                'status': mention.status,
                'created_at': mention.created_at.strftime('%Y-%m-%d %H:%M')
            }
            for mention in recent_mentions
        ]

    if 'client' in report_type:
        clients = Client.objects.annotate(
            mention_count=Count('mention', filter=Q(
                mention__created_at__date__gte=start_date,
                mention__created_at__date__lte=end_date
            ))
        ).filter(mention_count__gt=0)

        data['client_stats'] = {
            'total_clients': clients.count(),
            'active_clients': clients.filter(mention_count__gt=0).count()
        }

        # Add top clients
        data['top_clients'] = [
            {
                'name': client.name,
                'mention_count': client.mention_count,
                'industry': getattr(client, 'industry', 'N/A')
            }
            for client in clients.order_by('-mention_count')[:10]
        ]

    if 'show' in report_type or 'presenter' in report_type:
        shows = Show.objects.annotate(
            mention_count=Count('mentionreading__mention', filter=Q(
                mentionreading__actual_read_time__date__gte=start_date,
                mentionreading__actual_read_time__date__lte=end_date
            ))
        ).filter(mention_count__gt=0)

        data['show_stats'] = {
            'total_shows': shows.count(),
            'active_shows': shows.filter(mention_count__gt=0).count()
        }

        # Add top shows
        data['top_shows'] = [
            {
                'name': show.name,
                'mention_count': show.mention_count,
                'presenter': getattr(show, 'presenter', 'N/A')
            }
            for show in shows.order_by('-mention_count')[:10]
        ]

    # Add time-based data if relevant
    if any(time_word in report_type for time_word in ['daily', 'weekly', 'monthly', 'trend']):
        # Add basic time series data
        from datetime import timedelta

        daily_data = []
        current_date = start_date
        while current_date <= end_date:
            day_mentions = Mention.objects.filter(created_at__date=current_date).count()
            day_readings = MentionReading.objects.filter(actual_read_time__date=current_date).count()

            daily_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'day_name': current_date.strftime('%A'),
                'mentions': day_mentions,
                'readings': day_readings
            })
            current_date += timedelta(days=1)

        data['daily_data'] = daily_data

    # Add any additional parameters from request
    for param in ['client_id', 'show_id', 'status', 'priority']:
        value = request.GET.get(param)
        if value:
            data[f'filter_{param}'] = value

    return data


def _get_mentions_summary_data(start_date, end_date):
    """Get data for mentions summary report"""
    mentions = Mention.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    # Calculate summary statistics
    total_mentions = mentions.count()
    approved_mentions = mentions.filter(status='approved').count()
    pending_mentions = mentions.filter(status='pending').count()
    rejected_mentions = mentions.filter(status='rejected').count()

    completed_readings = MentionReading.objects.filter(
        mention__in=mentions,
        actual_read_time__isnull=False
    ).count()

    completion_rate = (completed_readings / total_mentions * 100) if total_mentions > 0 else 0

    # Get recent mentions for table
    recent_mentions = mentions.select_related('client').order_by('-created_at')[:20]
    mentions_data = []
    for mention in recent_mentions:
        mentions_data.append({
            'date': mention.created_at.strftime('%Y-%m-%d'),
            'client_name': mention.client.name,
            'show_name': 'Various',  # Could be enhanced to show specific shows
            'status': mention.status.title()
        })

    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_mentions': total_mentions,
            'approved_mentions': approved_mentions,
            'pending_mentions': pending_mentions,
            'rejected_mentions': rejected_mentions,
            'completed_readings': completed_readings,
            'completion_rate': f'{completion_rate:.1f}%'
        },
        'mentions': mentions_data
    }


def _get_client_report_data(start_date, end_date, client_id=None):
    """Get data for client report"""
    if client_id:
        client = Client.objects.get(id=client_id)
        mentions = Mention.objects.filter(
            client=client,
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        )
        client_name = client.name
    else:
        mentions = Mention.objects.filter(
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        )
        client_name = "All Clients"

    # Calculate client statistics
    total_mentions = mentions.count()
    approved_mentions = mentions.filter(status='approved').count()
    completed_readings = MentionReading.objects.filter(
        mention__in=mentions,
        actual_read_time__isnull=False
    ).count()

    # Get mentions by show
    mentions_by_show = []
    shows_data = mentions.values('mentionreading__show__name').annotate(
        total_mentions=Count('id'),
        completed=Count('mentionreading', filter=Q(mentionreading__actual_read_time__isnull=False)),
        pending=Count('id') - Count('mentionreading', filter=Q(mentionreading__actual_read_time__isnull=False))
    ).filter(mentionreading__show__name__isnull=False)

    for show_data in shows_data:
        mentions_by_show.append({
            'show_name': show_data['mentionreading__show__name'] or 'Unknown',
            'total_mentions': show_data['total_mentions'],
            'completed': show_data['completed'],
            'pending': show_data['pending']
        })

    return {
        'client_name': client_name,
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'client_stats': {
            'total_mentions': total_mentions,
            'approved_mentions': approved_mentions,
            'completed_readings': completed_readings,
            'completion_rate': f'{(completed_readings / total_mentions * 100):.1f}%' if total_mentions > 0 else '0%'
        },
        'mentions_by_show': mentions_by_show
    }


def _get_analytics_data(start_date, end_date):
    """Get data for analytics report"""
    mentions = Mention.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    total_mentions = mentions.count()
    completed_readings = MentionReading.objects.filter(
        mention__in=mentions,
        actual_read_time__isnull=False
    ).count()

    avg_duration = mentions.aggregate(avg=Avg('duration_seconds'))['avg'] or 0

    # Get top clients
    top_clients = Client.objects.annotate(
        mention_count=Count('mention', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date
        ))
    ).filter(mention_count__gt=0).order_by('-mention_count')[:5]

    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'key_metrics': {
            'total_mentions': total_mentions,
            'completed_readings': completed_readings,
            'completion_rate': f'{(completed_readings / total_mentions * 100):.1f}%' if total_mentions > 0 else '0%',
            'avg_duration_seconds': f'{avg_duration:.1f}',
            'active_clients': top_clients.count()
        },
        'trends': [
            f'Total mentions: {total_mentions}',
            f'Completion rate: {(completed_readings / total_mentions * 100):.1f}%' if total_mentions > 0 else 'Completion rate: 0%',
            f'Average duration: {avg_duration:.1f} seconds'
        ]
    }


def _get_daily_summary_data(report_date):
    """Get data for daily summary report"""
    mentions_created = Mention.objects.filter(created_at__date=report_date).count()
    mentions_approved = Mention.objects.filter(
        created_at__date=report_date,
        status='approved'
    ).count()

    readings_completed = MentionReading.objects.filter(
        actual_read_time__date=report_date
    ).count()

    # Get hourly breakdown
    hourly_breakdown = []
    for hour in range(24):
        hour_readings = MentionReading.objects.filter(
            actual_read_time__date=report_date,
            actual_read_time__hour=hour
        ).count()
        if hour_readings > 0:
            hourly_breakdown.append({
                'hour': f'{hour:02d}:00',
                'count': hour_readings
            })

    return {
        'report_date': report_date.strftime('%Y-%m-%d'),
        'daily_stats': {
            'mentions_created': mentions_created,
            'mentions_approved': mentions_approved,
            'readings_completed': readings_completed
        },
        'hourly_breakdown': hourly_breakdown
    }


def _get_weekly_trends_data(start_date, end_date):
    """Get data for weekly trends report"""
    mentions = Mention.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    total_mentions = mentions.count()
    completed_readings = MentionReading.objects.filter(
        mention__in=mentions,
        actual_read_time__isnull=False
    ).count()

    # Get daily breakdown
    daily_breakdown = []
    current_date = start_date
    while current_date <= end_date:
        daily_mentions = mentions.filter(created_at__date=current_date).count()
        daily_completed = MentionReading.objects.filter(
            actual_read_time__date=current_date,
            mention__in=mentions
        ).count()

        daily_breakdown.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'mentions': daily_mentions,
            'completed': daily_completed
        })
        current_date += timedelta(days=1)

    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'week_summary': {
            'total_mentions': total_mentions,
            'completed_readings': completed_readings,
            'completion_rate': f'{(completed_readings / total_mentions * 100):.1f}%' if total_mentions > 0 else '0%'
        },
        'daily_breakdown': daily_breakdown
    }


def _get_monthly_overview_data(year, month):
    """Get data for monthly overview report"""
    from calendar import monthrange

    month_start = datetime(year, month, 1).date()
    month_end = datetime(year, month, monthrange(year, month)[1]).date()

    mentions = Mention.objects.filter(
        created_at__date__gte=month_start,
        created_at__date__lte=month_end
    )

    total_mentions = mentions.count()
    completed_readings = MentionReading.objects.filter(
        mention__in=mentions,
        actual_read_time__isnull=False
    ).count()

    # Get top clients for the month
    top_clients = Client.objects.annotate(
        mentions_count=Count('mention', filter=Q(
            mention__created_at__date__gte=month_start,
            mention__created_at__date__lte=month_end
        ))
    ).filter(mentions_count__gt=0).order_by('-mentions_count')[:5]

    top_clients_data = []
    for client in top_clients:
        top_clients_data.append({
            'name': client.name,
            'mentions_count': client.mentions_count
        })

    return {
        'month_name': month_start.strftime('%B'),
        'year': year,
        'monthly_stats': {
            'total_mentions': total_mentions,
            'completed_readings': completed_readings,
            'completion_rate': f'{(completed_readings / total_mentions * 100):.1f}%' if total_mentions > 0 else '0%'
        },
        'top_clients': top_clients_data
    }


def _get_client_mentions_detailed_data(start_date, end_date, client_id=None, status_filter='all'):
    """Get data for detailed client mentions report"""
    mentions_query = Mention.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    ).select_related('client', 'created_by', 'approved_by')

    if client_id:
        mentions_query = mentions_query.filter(client_id=client_id)
        client_name = Client.objects.get(id=client_id).name
    else:
        client_name = "All Clients"

    if status_filter != 'all':
        if status_filter == 'completed':
            mentions_query = mentions_query.filter(mentionreading__actual_read_time__isnull=False).distinct()
        elif status_filter == 'scheduled':
            mentions_query = mentions_query.filter(status='approved')
        elif status_filter == 'pending':
            mentions_query = mentions_query.filter(status='pending')

    mentions = mentions_query.order_by('-created_at')[:50]  # Limit for PDF

    mentions_data = []
    for mention in mentions:
        mentions_data.append({
            'date': mention.created_at.strftime('%Y-%m-%d'),
            'title': mention.title,
            'client_name': mention.client.name,
            'status': mention.status.title(),
            'priority': mention.get_priority_display(),
            'duration': f"{mention.duration_seconds}s" if mention.duration_seconds else 'N/A'
        })

    return {
        'client_name': client_name,
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'status_filter': status_filter,
        'summary_stats': {
            'total_mentions': mentions.count(),
            'completed_mentions': mentions.filter(mentionreading__actual_read_time__isnull=False).distinct().count(),
            'approved_mentions': mentions.filter(status='approved').count(),
            'pending_mentions': mentions.filter(status='pending').count()
        },
        'mentions': mentions_data
    }


def _get_mentions_by_priority_data(start_date, end_date):
    """Get data for mentions by priority report"""
    priority_choices = {1: 'Low', 2: 'Normal', 3: 'High', 4: 'Urgent'}
    priority_stats = []

    for priority_value, priority_name in priority_choices.items():
        mentions = Mention.objects.filter(
            priority=priority_value,
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        )

        total = mentions.count()
        approved = mentions.filter(status='approved').count()
        completed = MentionReading.objects.filter(
            mention__in=mentions,
            actual_read_time__isnull=False
        ).count()

        priority_stats.append({
            'priority': priority_name,
            'total': total,
            'approved': approved,
            'completed': completed,
            'completion_rate': f'{(completed / total * 100):.1f}%' if total > 0 else '0%'
        })

    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_mentions': Mention.objects.filter(
                created_at__date__gte=start_date,
                created_at__date__lte=end_date
            ).count()
        },
        'priority_breakdown': priority_stats
    }


def _get_show_performance_data(start_date, end_date):
    """Get data for show performance report"""
    shows_data = []
    shows = Show.objects.annotate(
        period_mentions=Count('mentionreading__mention', filter=Q(
            mentionreading__mention__created_at__date__gte=start_date,
            mentionreading__mention__created_at__date__lte=end_date
        )),
        completed_mentions=Count('mentionreading', filter=Q(
            mentionreading__mention__created_at__date__gte=start_date,
            mentionreading__mention__created_at__date__lte=end_date,
            mentionreading__actual_read_time__isnull=False
        ))
    ).filter(period_mentions__gt=0).order_by('-completed_mentions')

    for show in shows:
        completion_rate = (show.completed_mentions / show.period_mentions * 100) if show.period_mentions > 0 else 0
        shows_data.append({
            'name': show.name,
            'total_mentions': show.period_mentions,
            'completed_mentions': show.completed_mentions,
            'completion_rate': f'{completion_rate:.1f}%'
        })

    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_shows': shows.count(),
            'active_shows': shows.filter(period_mentions__gt=0).count()
        },
        'shows_performance': shows_data
    }


def _get_show_schedule_data():
    """Get data for show schedule report"""
    shows = Show.objects.all().order_by('name')
    shows_data = []

    for show in shows:
        upcoming_mentions = MentionReading.objects.filter(
            show=show,
            scheduled_time__gte=timezone.now(),
            actual_read_time__isnull=True
        ).count()

        shows_data.append({
            'name': show.name,
            'description': show.description or 'No description',
            'upcoming_mentions': upcoming_mentions,
            'is_active': show.is_active
        })

    return {
        'date_generated': datetime.now().strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_shows': shows.count(),
            'active_shows': shows.filter(is_active=True).count()
        },
        'shows_schedule': shows_data
    }


def _get_show_readings_data(start_date, end_date, show_id=None, client_id=None, presenter_id=None, status=None, priority=None):
    """Get data for show readings report with enhanced filtering"""
    shows_data = []

    # Build the base filter for readings that match all criteria
    base_reading_filter = Q(
        mentionreading__scheduled_date__gte=start_date,
        mentionreading__scheduled_date__lte=end_date
    )

    # Apply additional filters to the base reading filter
    if client_id:
        try:
            client_id = int(client_id)
            base_reading_filter &= Q(mentionreading__mention__client_id=client_id)
        except (ValueError, TypeError):
            pass

    if presenter_id:
        try:
            presenter_id = int(presenter_id)
            base_reading_filter &= Q(mentionreading__presenter_id=presenter_id)
        except (ValueError, TypeError):
            pass

    if priority:
        try:
            priority = int(priority)
            base_reading_filter &= Q(mentionreading__mention__priority=priority)
        except (ValueError, TypeError):
            pass

    # Build status-specific filters
    completed_filter = base_reading_filter & Q(mentionreading__actual_read_time__isnull=False)
    pending_filter = base_reading_filter & Q(mentionreading__actual_read_time__isnull=True)

    # Apply status filter to base filter if specified
    if status:
        if status == 'completed':
            base_reading_filter = completed_filter
        elif status == 'pending':
            base_reading_filter = pending_filter

    shows_queryset = Show.objects.annotate(
        total_readings=Count('mentionreading', filter=base_reading_filter),
        completed_readings=Count('mentionreading', filter=completed_filter),
        pending_readings=Count('mentionreading', filter=pending_filter),
        avg_reading_duration=Avg('mentionreading__duration_seconds', filter=completed_filter)
    ).filter(total_readings__gt=0)

    # Filter by specific show if requested
    if show_id:
        try:
            show_id = int(show_id)
            shows_queryset = shows_queryset.filter(id=show_id)
        except (ValueError, TypeError):
            pass

    shows = shows_queryset.order_by('-completed_readings')

    for show in shows:
        completion_rate = round((show.completed_readings / show.total_readings) * 100, 1) if show.total_readings > 0 else 0
        avg_duration = round(show.avg_reading_duration, 1) if show.avg_reading_duration else 0

        shows_data.append({
            'name': show.name,
            'description': show.description or 'No description',
            'total_readings': show.total_readings,
            'completed_readings': show.completed_readings,
            'pending_readings': show.pending_readings,
            'completion_rate': f'{completion_rate}%',
            'avg_duration': f'{avg_duration}s',
            'schedule': f"{show.start_time.strftime('%H:%M') if show.start_time else 'N/A'} - {show.end_time.strftime('%H:%M') if show.end_time else 'N/A'}",
            'days': show.weekdays_display
        })

    # Get recent readings for detailed data
    recent_readings_queryset = MentionReading.objects.filter(
        scheduled_date__gte=start_date,
        scheduled_date__lte=end_date
    )

    # Apply filters
    if show_id:
        try:
            show_id = int(show_id)
            recent_readings_queryset = recent_readings_queryset.filter(show_id=show_id)
        except (ValueError, TypeError):
            pass

    if client_id:
        try:
            client_id = int(client_id)
            recent_readings_queryset = recent_readings_queryset.filter(mention__client_id=client_id)
        except (ValueError, TypeError):
            pass

    if presenter_id:
        try:
            presenter_id = int(presenter_id)
            recent_readings_queryset = recent_readings_queryset.filter(presenter_id=presenter_id)
        except (ValueError, TypeError):
            pass

    if status:
        if status == 'completed':
            recent_readings_queryset = recent_readings_queryset.filter(actual_read_time__isnull=False)
        elif status == 'pending':
            recent_readings_queryset = recent_readings_queryset.filter(actual_read_time__isnull=True)

    if priority:
        try:
            priority = int(priority)
            recent_readings_queryset = recent_readings_queryset.filter(mention__priority=priority)
        except (ValueError, TypeError):
            pass

    recent_readings = recent_readings_queryset.select_related(
        'mention', 'mention__client', 'show', 'presenter'
    ).order_by('-scheduled_date', '-scheduled_time')[:50]

    readings_data = []
    for reading in recent_readings:
        readings_data.append({
            'date': reading.scheduled_date.strftime('%Y-%m-%d'),
            'time': reading.scheduled_time.strftime('%H:%M'),
            'show': reading.show.name,
            'mention_title': reading.mention.title,
            'mention_content': reading.mention.content,  # Add the actual mention content
            'client': reading.mention.client.name,
            'presenter': f"{reading.presenter.first_name} {reading.presenter.last_name}" if reading.presenter else 'Not assigned',
            'status': 'Completed' if reading.actual_read_time else 'Pending',
            'actual_read_time': reading.actual_read_time.strftime('%Y-%m-%d %H:%M') if reading.actual_read_time else 'N/A',
            'duration': f'{reading.duration_seconds}s' if reading.duration_seconds else 'N/A'
        })

    # Calculate summary statistics from filtered data
    total_readings = sum(show.total_readings for show in shows)
    total_completed = sum(show.completed_readings for show in shows)
    total_pending = sum(show.pending_readings for show in shows)
    overall_completion_rate = round((total_completed / total_readings) * 100, 1) if total_readings > 0 else 0

    # Build filter context for the report
    filter_context = {}
    if show_id:
        try:
            show = Show.objects.get(id=int(show_id))
            filter_context['show_filter'] = show.name
        except (Show.DoesNotExist, ValueError, TypeError):
            pass

    if client_id:
        try:
            client = Client.objects.get(id=int(client_id))
            filter_context['client_filter'] = client.name
        except (Client.DoesNotExist, ValueError, TypeError):
            pass

    if presenter_id:
        try:
            presenter = User.objects.get(id=int(presenter_id))
            filter_context['presenter_filter'] = f"{presenter.first_name} {presenter.last_name}".strip() or presenter.username
        except (User.DoesNotExist, ValueError, TypeError):
            pass

    if status:
        filter_context['status_filter'] = status.title()

    if priority:
        filter_context['priority_filter'] = priority

    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_readings': total_readings,
            'total_completed': total_completed,
            'total_pending': total_pending,
            'overall_completion_rate': f'{overall_completion_rate}%',
            'active_shows': shows.count()
        },
        'filter_context': filter_context,
        'shows_readings': shows_data,
        'recent_readings': readings_data
    }


def _get_presenter_activity_data(start_date, end_date):
    """Get data for presenter activity report"""
    presenter_stats = []
    presenters = User.objects.annotate(
        completed_readings=Count('presenter_profile__mentionreading', filter=Q(
            presenter_profile__mentionreading__actual_read_time__isnull=False,
            presenter_profile__mentionreading__actual_read_time__date__gte=start_date,
            presenter_profile__mentionreading__actual_read_time__date__lte=end_date
        )),
        total_assigned=Count('presenter_profile__mentionreading', filter=Q(
            presenter_profile__mentionreading__mention__created_at__date__gte=start_date,
            presenter_profile__mentionreading__mention__created_at__date__lte=end_date
        ))
    ).filter(completed_readings__gt=0).order_by('-completed_readings')

    for presenter in presenters:
        completion_rate = (presenter.completed_readings / presenter.total_assigned * 100) if presenter.total_assigned > 0 else 0
        presenter_stats.append({
            'name': f"{presenter.first_name} {presenter.last_name}".strip() or presenter.username,
            'username': presenter.username,
            'completed_readings': presenter.completed_readings,
            'total_assigned': presenter.total_assigned,
            'completion_rate': f'{completion_rate:.1f}%'
        })

    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_presenters': presenters.count(),
            'active_presenters': presenters.filter(completed_readings__gt=0).count()
        },
        'presenter_stats': presenter_stats
    }


def _get_client_activity_data(start_date, end_date):
    """Get data for client activity report"""
    client_stats = []
    clients = Client.objects.annotate(
        mentions_created=Count('mention', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date
        )),
        mentions_approved=Count('mention', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date,
            mention__status='approved'
        )),
        mentions_completed=Count('mention__mentionreading', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date,
            mention__mentionreading__actual_read_time__isnull=False
        ))
    ).filter(mentions_created__gt=0).order_by('-mentions_created')

    for client in clients:
        approval_rate = (client.mentions_approved / client.mentions_created * 100) if client.mentions_created > 0 else 0
        completion_rate = (client.mentions_completed / client.mentions_created * 100) if client.mentions_created > 0 else 0

        client_stats.append({
            'name': client.name,
            'mentions_created': client.mentions_created,
            'mentions_approved': client.mentions_approved,
            'mentions_completed': client.mentions_completed,
            'approval_rate': f'{approval_rate:.1f}%',
            'completion_rate': f'{completion_rate:.1f}%'
        })

    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_clients': clients.count(),
            'active_clients': clients.filter(mentions_created__gt=0).count()
        },
        'client_stats': client_stats
    }


def _get_client_revenue_data(start_date, end_date):
    """Get data for client revenue report (placeholder - requires pricing model)"""
    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_revenue': 'N/A - Pricing model required',
            'average_revenue_per_client': 'N/A',
            'top_revenue_client': 'N/A'
        },
        'revenue_breakdown': []
    }


def _get_client_retention_data(start_date, end_date):
    """Get data for client retention report"""
    return {
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_clients': Client.objects.filter(is_active=True).count(),
            'new_clients': 'N/A - Requires signup tracking',
            'retention_rate': 'N/A - Requires historical data'
        },
        'retention_breakdown': []
    }


def _get_client_order_schedule_data(start_date, end_date, client_id=None):
    """Get data for client order schedule report"""
    if client_id:
        try:
            client = Client.objects.get(id=client_id)
            mentions = MentionReading.objects.filter(
                mention__client=client,
                scheduled_date__gte=start_date,
                scheduled_date__lte=end_date
            ).select_related('mention', 'show')
            client_name = client.name
        except Client.DoesNotExist:
            return {'error': f'Client with ID {client_id} not found'}
    else:
        mentions = MentionReading.objects.filter(
            scheduled_date__gte=start_date,
            scheduled_date__lte=end_date
        ).select_related('mention', 'show', 'mention__client')
        client_name = "All Clients"

    schedule_data = []
    for reading in mentions.order_by('scheduled_date', 'scheduled_time'):
        schedule_data.append({
            'date': reading.scheduled_date.strftime('%Y-%m-%d') if reading.scheduled_date else 'N/A',
            'time': reading.scheduled_time.strftime('%H:%M') if reading.scheduled_time else 'N/A',
            'client_name': reading.mention.client.name,
            'mention_title': reading.mention.title,
            'show_name': reading.show.name if reading.show else 'N/A',
            'status': 'Completed' if reading.actual_read_time else 'Scheduled'
        })

    return {
        'client_name': client_name,
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'summary_stats': {
            'total_scheduled': mentions.count(),
            'completed': mentions.filter(actual_read_time__isnull=False).count(),
            'pending': mentions.filter(actual_read_time__isnull=True).count()
        },
        'schedule_data': schedule_data
    }


# =============================================================================
# MENTION REPORTS
# =============================================================================

@login_required
def mentions_summary(request):
    """Mentions summary report"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Filter mentions by date range
    mentions = Mention.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    # Calculate metrics
    total_mentions = mentions.count()
    approved_mentions = mentions.filter(status='approved').count()
    pending_mentions = mentions.filter(status='pending').count()
    rejected_mentions = mentions.filter(status='rejected').count()

    # Completion metrics
    completed_readings = MentionReading.objects.filter(
        mention__in=mentions,
        actual_read_time__isnull=False
    ).count()

    completion_rate = (completed_readings / total_mentions * 100) if total_mentions > 0 else 0

    # Priority breakdown
    priority_breakdown = mentions.values('priority').annotate(count=Count('id'))

    # Daily breakdown - PostgreSQL compatible
    daily_breakdown = mentions.annotate(
        day=Cast('created_at', DateField())
    ).values('day').annotate(count=Count('id')).order_by('day')

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'total_mentions': total_mentions,
        'approved_mentions': approved_mentions,
        'pending_mentions': pending_mentions,
        'rejected_mentions': rejected_mentions,
        'completed_readings': completed_readings,
        'completion_rate': round(completion_rate, 1),
        'priority_breakdown': priority_breakdown,
        'daily_breakdown': daily_breakdown,
    }

    return render(request, 'reports/mentions_summary.html', context)


@login_required
def mentions_by_client(request):
    """Mentions by client report"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get client statistics
    client_stats = Client.objects.annotate(
        period_mentions=Count('mention', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date
        )),
        approved_mentions=Count('mention', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date,
            mention__status='approved'
        )),
        completed_mentions=Count('mention__mentionreading', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date,
            mention__mentionreading__actual_read_time__isnull=False
        ))
    ).filter(period_mentions__gt=0).order_by('-period_mentions')

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'client_stats': client_stats,
    }

    return render(request, 'reports/mentions_by_client.html', context)


@login_required
def test_simple(request):
    """Simple test view"""
    return render(request, 'reports/test_simple.html', {})

@login_required
def client_mentions_detailed(request):
    """Detailed client mentions report with individual mention details"""
    from apps.mentions.models import Mention, MentionReading
    from django.db.models import Prefetch

    # Get parameters
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    client_id = request.GET.get('client_id')
    status_filter = request.GET.get('status', 'all')

    # Get display options
    show_summary = request.GET.get('show_summary', '1') == '1'  # Default to show
    compact_view = request.GET.get('compact_view') == '1'
    show_content = request.GET.get('show_content') == '1'
    hide_actions = request.GET.get('hide_actions') == '1'

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Base query for mentions in date range
    mentions_query = Mention.objects.filter(
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    ).select_related(
        'client', 'created_by', 'approved_by'
    ).prefetch_related(
        Prefetch(
            'mentionreading_set',
            queryset=MentionReading.objects.select_related('show', 'presenter')
        )
    )

    # Apply client filter if specified
    if client_id:
        mentions_query = mentions_query.filter(client_id=client_id)

    # Apply status filter
    if status_filter != 'all':
        if status_filter == 'completed':
            mentions_query = mentions_query.filter(mentionreading__actual_read_time__isnull=False).distinct()
        elif status_filter == 'scheduled':
            mentions_query = mentions_query.filter(status='approved')
        elif status_filter == 'pending':
            mentions_query = mentions_query.filter(status='pending')

    mentions = mentions_query.order_by('-created_at')

    # Get all clients for filter dropdown
    clients = Client.objects.filter(
        mention__created_at__date__gte=start_date,
        mention__created_at__date__lte=end_date
    ).distinct().order_by('name')

    # Calculate summary statistics
    total_mentions = mentions.count()
    completed_mentions = mentions.filter(mentionreading__actual_read_time__isnull=False).distinct().count()
    approved_mentions = mentions.filter(status='approved').count()
    pending_mentions = mentions.filter(status='pending').count()

    # Calculate total duration for completed mentions
    completed_readings = MentionReading.objects.filter(
        mention__in=mentions,
        actual_read_time__isnull=False
    )
    total_duration = sum([reading.duration_seconds or reading.mention.duration_seconds for reading in completed_readings])

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'mentions': mentions,
        'clients': clients,
        'selected_client_id': int(client_id) if client_id else None,
        'status_filter': status_filter,
        'total_mentions': total_mentions,
        'completed_mentions': completed_mentions,
        'approved_mentions': approved_mentions,
        'pending_mentions': pending_mentions,
        'completion_rate': round((completed_mentions / total_mentions * 100) if total_mentions > 0 else 0, 1),
        'total_duration_minutes': round(total_duration / 60, 1) if total_duration else 0,
        # Display options
        'show_summary': show_summary,
        'compact_view': compact_view,
        'show_content': show_content,
        'hide_actions': hide_actions,
    }

    return render(request, 'reports/client_mentions_detailed.html', context)


@login_required
def mentions_by_priority(request):
    """Mentions by priority report"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Priority mapping
    priority_choices = {
        1: 'Low',
        2: 'Normal',
        3: 'High',
        4: 'Urgent'
    }

    # Get priority statistics
    priority_stats = []
    for priority_value, priority_name in priority_choices.items():
        mentions = Mention.objects.filter(
            priority=priority_value,
            created_at__date__gte=start_date,
            created_at__date__lte=end_date
        )

        total = mentions.count()
        approved = mentions.filter(status='approved').count()
        completed = MentionReading.objects.filter(
            mention__in=mentions,
            actual_read_time__isnull=False
        ).count()

        completion_rate = (completed / total * 100) if total > 0 else 0

        priority_stats.append({
            'priority': priority_value,
            'name': priority_name,
            'total': total,
            'approved': approved,
            'completed': completed,
            'completion_rate': round(completion_rate, 1)
        })

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'priority_stats': priority_stats,
    }

    return render(request, 'reports/mentions_by_priority.html', context)


# =============================================================================
# SHOW REPORTS
# =============================================================================

@login_required
def show_performance(request):
    """Show performance report"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get show statistics
    show_stats_raw = Show.objects.annotate(
        period_mentions=Count('mentionreading__mention', filter=Q(
            mentionreading__mention__created_at__date__gte=start_date,
            mentionreading__mention__created_at__date__lte=end_date
        )),
        completed_mentions=Count('mentionreading', filter=Q(
            mentionreading__mention__created_at__date__gte=start_date,
            mentionreading__mention__created_at__date__lte=end_date,
            mentionreading__actual_read_time__isnull=False
        )),
        avg_duration=Avg('mentionreading__mention__duration_seconds', filter=Q(
            mentionreading__mention__created_at__date__gte=start_date,
            mentionreading__mention__created_at__date__lte=end_date
        ))
    ).filter(period_mentions__gt=0).order_by('-completed_mentions')

    # Process the data and calculate completion rates
    show_stats = []
    for show in show_stats_raw:
        completion_rate = round((show.completed_mentions / show.period_mentions) * 100, 1) if show.period_mentions > 0 else 0
        avg_duration = round(show.avg_duration, 1) if show.avg_duration else 0

        show_stats.append({
            'show': show,
            'name': show.name,
            'total_mentions': show.period_mentions,
            'completed_mentions': show.completed_mentions,
            'completion_rate': completion_rate,
            'avg_duration': avg_duration,
        })

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'show_stats': show_stats,
    }

    return render(request, 'reports/show_performance.html', context)


@login_required
def show_schedule(request):
    """Show schedule report"""
    # Get all shows with their schedule information
    shows = Show.objects.all().order_by('name')

    # Get upcoming mentions for each show
    upcoming_mentions = {}
    for show in shows:
        upcoming = MentionReading.objects.filter(
            show=show,
            scheduled_time__gte=timezone.now(),
            actual_read_time__isnull=True
        ).select_related('mention').order_by('scheduled_time')[:5]
        upcoming_mentions[show.id] = upcoming

    context = {
        'shows': shows,
        'upcoming_mentions': upcoming_mentions,
    }

    return render(request, 'reports/show_schedule.html', context)


@login_required
def show_readings(request):
    """Show readings report - detailed analysis of mention readings by show"""
    # Get filter parameters from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    show_id = request.GET.get('show_id')
    client_id = request.GET.get('client_id')
    presenter_id = request.GET.get('presenter_id')
    status = request.GET.get('status')
    priority = request.GET.get('priority')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Base queryset for readings in the date range
    readings_queryset = MentionReading.objects.filter(
        scheduled_date__gte=start_date,
        scheduled_date__lte=end_date
    ).select_related('mention', 'mention__client', 'show', 'presenter')

    # Apply filters
    if show_id:
        try:
            show_id = int(show_id)
            readings_queryset = readings_queryset.filter(show_id=show_id)
        except (ValueError, TypeError):
            pass

    if client_id:
        try:
            client_id = int(client_id)
            readings_queryset = readings_queryset.filter(mention__client_id=client_id)
        except (ValueError, TypeError):
            pass

    if presenter_id:
        try:
            presenter_id = int(presenter_id)
            readings_queryset = readings_queryset.filter(presenter_id=presenter_id)
        except (ValueError, TypeError):
            pass

    if status:
        if status == 'completed':
            readings_queryset = readings_queryset.filter(actual_read_time__isnull=False)
        elif status == 'pending':
            readings_queryset = readings_queryset.filter(actual_read_time__isnull=True)

    if priority:
        try:
            priority = int(priority)
            readings_queryset = readings_queryset.filter(mention__priority=priority)
        except (ValueError, TypeError):
            pass

    # Get show statistics with reading details
    show_stats_raw = Show.objects.annotate(
        total_readings=Count('mentionreading', filter=Q(
            mentionreading__scheduled_date__gte=start_date,
            mentionreading__scheduled_date__lte=end_date
        )),
        completed_readings=Count('mentionreading', filter=Q(
            mentionreading__scheduled_date__gte=start_date,
            mentionreading__scheduled_date__lte=end_date,
            mentionreading__actual_read_time__isnull=False
        )),
        pending_readings=Count('mentionreading', filter=Q(
            mentionreading__scheduled_date__gte=start_date,
            mentionreading__scheduled_date__lte=end_date,
            mentionreading__actual_read_time__isnull=True
        )),
        avg_reading_duration=Avg('mentionreading__duration_seconds', filter=Q(
            mentionreading__scheduled_date__gte=start_date,
            mentionreading__scheduled_date__lte=end_date,
            mentionreading__actual_read_time__isnull=False
        ))
    ).filter(total_readings__gt=0).order_by('-completed_readings')

    # Process the data and calculate rates
    show_stats = []
    for show in show_stats_raw:
        completion_rate = round((show.completed_readings / show.total_readings) * 100, 1) if show.total_readings > 0 else 0
        avg_duration = round(show.avg_reading_duration, 1) if show.avg_reading_duration else 0

        show_stats.append({
            'show': show,
            'name': show.name,
            'description': show.description,
            'total_readings': show.total_readings,
            'completed_readings': show.completed_readings,
            'pending_readings': show.pending_readings,
            'completion_rate': completion_rate,
            'avg_duration': avg_duration,
            'start_time': show.start_time,
            'end_time': show.end_time,
            'days_of_week': show.weekdays_display,
        })

    # Get recent readings for detailed view
    recent_readings = readings_queryset.order_by('-scheduled_date', '-scheduled_time')[:20]

    # Get all shows for filter dropdown
    all_shows = Show.objects.filter(is_active=True).order_by('name')

    # Get all clients for filter dropdown
    all_clients = Client.objects.filter(is_active=True).order_by('name')

    # Get all presenters for filter dropdown
    all_presenters = Presenter.objects.filter(is_active=True).order_by('first_name', 'last_name')

    # Calculate summary statistics
    total_readings = sum(stat['total_readings'] for stat in show_stats)
    total_completed = sum(stat['completed_readings'] for stat in show_stats)
    total_pending = sum(stat['pending_readings'] for stat in show_stats)
    overall_completion_rate = round((total_completed / total_readings) * 100, 1) if total_readings > 0 else 0

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'show_id': show_id,
        'client_id': client_id,
        'presenter_id': presenter_id,
        'status': status,
        'priority': priority,
        'show_stats': show_stats,
        'recent_readings': recent_readings,
        'all_shows': all_shows,
        'all_clients': all_clients,
        'all_presenters': all_presenters,
        'summary_stats': {
            'total_readings': total_readings,
            'total_completed': total_completed,
            'total_pending': total_pending,
            'overall_completion_rate': overall_completion_rate,
            'active_shows': len(show_stats),
        }
    }

    return render(request, 'reports/show_readings.html', context)


@login_required
def presenter_activity(request):
    """Presenter activity report"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get presenter statistics (users who have completed readings)
    presenter_stats_raw = User.objects.annotate(
        completed_readings=Count('presenter_profile__mentionreading', filter=Q(
            presenter_profile__mentionreading__actual_read_time__isnull=False,
            presenter_profile__mentionreading__actual_read_time__date__gte=start_date,
            presenter_profile__mentionreading__actual_read_time__date__lte=end_date
        )),
        total_assigned=Count('presenter_profile__mentionreading', filter=Q(
            presenter_profile__mentionreading__mention__created_at__date__gte=start_date,
            presenter_profile__mentionreading__mention__created_at__date__lte=end_date
        ))
    ).filter(completed_readings__gt=0).order_by('-completed_readings')

    # Process the data and calculate completion rates
    presenter_stats = []
    for presenter in presenter_stats_raw:
        completion_rate = round((presenter.completed_readings / presenter.total_assigned) * 100, 1) if presenter.total_assigned > 0 else 0

        presenter_stats.append({
            'presenter': presenter,
            'username': presenter.username,
            'first_name': presenter.first_name,
            'last_name': presenter.last_name,
            'completed_readings': presenter.completed_readings,
            'total_assigned': presenter.total_assigned,
            'completion_rate': completion_rate,
        })

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'presenter_stats': presenter_stats,
    }

    return render(request, 'reports/presenter_activity.html', context)


# =============================================================================
# CLIENT REPORTS
# =============================================================================

@login_required
def client_activity(request):
    """Client activity report"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get client activity statistics
    client_stats_raw = Client.objects.annotate(
        mentions_created=Count('mention', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date
        )),
        mentions_approved=Count('mention', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date,
            mention__status='approved'
        )),
        mentions_completed=Count('mention__mentionreading', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date,
            mention__mentionreading__actual_read_time__isnull=False
        )),
        avg_duration=Avg('mention__duration_seconds', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date
        ))
    ).filter(mentions_created__gt=0).order_by('-mentions_created')

    # Process the data and calculate rates
    client_stats = []
    for client in client_stats_raw:
        approval_rate = round((client.mentions_approved / client.mentions_created) * 100, 1) if client.mentions_created > 0 else 0
        completion_rate = round((client.mentions_completed / client.mentions_created) * 100, 1) if client.mentions_created > 0 else 0
        avg_duration = round(client.avg_duration, 1) if client.avg_duration else 0

        client_stats.append({
            'client': client,
            'name': client.name,
            'mentions_created': client.mentions_created,
            'mentions_approved': client.mentions_approved,
            'mentions_completed': client.mentions_completed,
            'approval_rate': approval_rate,
            'completion_rate': completion_rate,
            'avg_duration': avg_duration,
        })

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'client_stats': client_stats,
    }

    return render(request, 'reports/client_activity.html', context)


@login_required
def client_revenue(request):
    """Client revenue analysis report"""
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # Default to last 30 days if no dates provided
    if not start_date or not end_date:
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Note: Revenue calculation would require additional pricing fields in the models

    client_revenue_raw = Client.objects.annotate(
        period_mentions=Count('mention', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date,
            mention__status='approved'
        )),
        total_duration=Sum('mention__duration_seconds', filter=Q(
            mention__created_at__date__gte=start_date,
            mention__created_at__date__lte=end_date,
            mention__status='approved'
        ))
    ).filter(period_mentions__gt=0).order_by('-period_mentions')

    # Process duration data
    client_revenue = []
    for client in client_revenue_raw:
        total_duration = round(client.total_duration / 60, 1) if client.total_duration else 0  # Convert to minutes

        client_revenue.append({
            'client': client,
            'name': client.name,
            'period_mentions': client.period_mentions,
            'total_duration': total_duration,
        })

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'client_revenue': client_revenue,
    }

    return render(request, 'reports/client_revenue.html', context)


@login_required
def client_retention(request):
    """Client retention report"""
    # Get all clients and their activity over time
    clients = Client.objects.filter(is_active=True)

    # Calculate retention metrics
    retention_data = []
    for client in clients:
        # Get first and last mention dates
        first_mention = client.mention_set.order_by('created_at').first()
        last_mention = client.mention_set.order_by('-created_at').first()

        if first_mention and last_mention:
            days_active = (last_mention.created_at.date() - first_mention.created_at.date()).days
            total_mentions = client.mention_set.count()

            # Calculate activity periods (months with mentions) - PostgreSQL compatible
            monthly_activity = client.mention_set.annotate(
                year=Extract('created_at', 'year'),
                month=Extract('created_at', 'month')
            ).values('year', 'month').distinct().count()

            retention_data.append({
                'client': client,
                'first_mention': first_mention.created_at.date(),
                'last_mention': last_mention.created_at.date(),
                'days_active': days_active,
                'total_mentions': total_mentions,
                'monthly_activity': monthly_activity,
                'avg_mentions_per_month': round(total_mentions / max(monthly_activity, 1), 1)
            })

    # Sort by days active (retention)
    retention_data.sort(key=lambda x: x['days_active'], reverse=True)

    context = {
        'retention_data': retention_data,
    }

    return render(request, 'reports/client_retention.html', context)


@login_required
def client_order_schedule(request):
    """Client order schedule report - shows detailed scheduling for client mentions"""
    from apps.mentions.forms import ClientOrderScheduleFilterForm
    from apps.organizations.middleware import get_current_organization

    organization = get_current_organization(request)

    # Get parameters from request
    client_id = request.GET.get('client_id')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    show_filter = request.GET.get('show')
    status_filter = request.GET.get('status')
    mention_title_filter = request.GET.get('mention_title')

    # Initialize filter form
    filter_form = ClientOrderScheduleFilterForm(request.GET, organization=organization)

    # Default to next 30 days if no dates provided
    if not start_date or not end_date:
        start_date = timezone.now().date()
        end_date = start_date + timedelta(days=30)
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get client or show error
    if not client_id:
        if organization:
            clients = Client.objects.filter(organization=organization, is_active=True).order_by('name')
        else:
            clients = Client.objects.filter(is_active=True).order_by('name')
        context = {
            'clients': clients,
            'start_date': start_date,
            'end_date': end_date,
            'show_client_selection': True,
            'filter_form': filter_form,
        }
        return render(request, 'reports/client_order_schedule.html', context)

    try:
        client = Client.objects.get(id=client_id, is_active=True)
    except Client.DoesNotExist:
        messages.error(request, 'Client not found.')
        return redirect('reports:client_order_schedule')

    # Get all scheduled mention readings for this client in the date range
    mention_readings = MentionReading.objects.filter(
        mention__client=client,
        scheduled_date__gte=start_date,
        scheduled_date__lte=end_date,
        mention__status__in=['scheduled', 'read']  # Only approved mentions
    ).select_related(
        'mention', 'show', 'presenter'
    )

    # Apply additional filters
    if show_filter:
        try:
            show_id = int(show_filter)
            mention_readings = mention_readings.filter(show_id=show_id)
        except (ValueError, TypeError):
            pass

    if mention_title_filter:
        mention_readings = mention_readings.filter(mention__title__icontains=mention_title_filter)

    if status_filter:
        if status_filter == 'completed':
            mention_readings = mention_readings.filter(actual_read_time__isnull=False)
        elif status_filter == 'pending':
            mention_readings = mention_readings.filter(actual_read_time__isnull=True)

    mention_readings = mention_readings.order_by('scheduled_date', 'scheduled_time')

    # Group readings by date and time for calendar display
    schedule_data = {}
    total_slots = 0

    # Create date range for calendar
    current_date = start_date
    while current_date <= end_date:
        schedule_data[current_date] = {}
        current_date += timedelta(days=1)

    # Organize readings by date and time
    for reading in mention_readings:
        date_key = reading.scheduled_date
        time_key = reading.scheduled_time.strftime('%H:%M:%S')

        if date_key not in schedule_data:
            schedule_data[date_key] = {}

        if time_key not in schedule_data[date_key]:
            schedule_data[date_key][time_key] = []

        schedule_data[date_key][time_key].append({
            'reading': reading,
            'mention': reading.mention,
            'show': reading.show,
            'presenter': reading.presenter,
            'status': reading.mention.status,
            'is_completed': reading.actual_read_time is not None,
        })
        total_slots += 1

    # Get unique time slots for header
    all_times = set()
    for date_data in schedule_data.values():
        all_times.update(date_data.keys())

    sorted_times = sorted(list(all_times))

    # Calculate summary statistics
    completed_slots = mention_readings.filter(actual_read_time__isnull=False).count()
    pending_slots = total_slots - completed_slots

    # Get campaign duration in human readable format
    duration_days = (end_date - start_date).days + 1
    if duration_days <= 7:
        duration_text = f"{duration_days} day{'s' if duration_days != 1 else ''}"
    elif duration_days <= 31:
        weeks = duration_days // 7
        remaining_days = duration_days % 7
        if remaining_days == 0:
            duration_text = f"{weeks} week{'s' if weeks != 1 else ''}"
        else:
            duration_text = f"{weeks} week{'s' if weeks != 1 else ''} and {remaining_days} day{'s' if remaining_days != 1 else ''}"
    else:
        months = duration_days // 30
        remaining_days = duration_days % 30
        if remaining_days == 0:
            duration_text = f"{months} month{'s' if months != 1 else ''}"
        else:
            duration_text = f"{months} month{'s' if months != 1 else ''} and {remaining_days} day{'s' if remaining_days != 1 else ''}"

    context = {
        'client': client,
        'start_date': start_date,
        'end_date': end_date,
        'schedule_data': schedule_data,
        'sorted_times': sorted_times,
        'total_slots': total_slots,
        'completed_slots': completed_slots,
        'pending_slots': pending_slots,
        'duration_text': duration_text,
        'show_client_selection': False,
        'filter_form': filter_form,
        'show_filter': show_filter,
        'status_filter': status_filter,
        'mention_title_filter': mention_title_filter,
    }

    return render(request, 'reports/client_order_schedule.html', context)


# =============================================================================
# TIME-BASED REPORTS
# =============================================================================

@login_required
def daily_summary(request):
    """Daily summary report"""
    # Get specific date or default to today
    report_date = request.GET.get('date')
    if report_date:
        report_date = datetime.strptime(report_date, '%Y-%m-%d').date()
    else:
        report_date = timezone.now().date()

    # Get daily statistics
    mentions_created = Mention.objects.filter(created_at__date=report_date).count()
    mentions_approved = Mention.objects.filter(
        created_at__date=report_date,
        status='approved'
    ).count()

    readings_completed = MentionReading.objects.filter(
        actual_read_time__date=report_date
    ).count()

    # Get hourly breakdown - PostgreSQL compatible
    hourly_breakdown = MentionReading.objects.filter(
        actual_read_time__date=report_date
    ).annotate(
        hour=Extract('actual_read_time', 'hour')
    ).values('hour').annotate(count=Count('id')).order_by('hour')

    # Get show activity for the day
    show_activity = Show.objects.annotate(
        readings_count=Count('mentionreading', filter=Q(
            mentionreading__actual_read_time__date=report_date
        ))
    ).filter(readings_count__gt=0).order_by('-readings_count')

    context = {
        'report_date': report_date,
        'mentions_created': mentions_created,
        'mentions_approved': mentions_approved,
        'readings_completed': readings_completed,
        'hourly_breakdown': hourly_breakdown,
        'show_activity': show_activity,
    }

    return render(request, 'reports/daily_summary.html', context)


# Dynamic Report Configuration System - REMOVED






# def _transform_field_value(obj, field_config):
#     """Transform field value based on configuration"""
#     field_path = field_config['field']
#     transform_type = field_config.get('transform', 'str')
#     default_value = field_config.get('default', '')

#     # Navigate through field path (e.g., 'client.name')
#     try:
#         value = obj
#         for field_part in field_path.split('.'):
#             if hasattr(value, field_part):
#                 value = getattr(value, field_part)
#             else:
#                 value = default_value
#                 break

#         if value is None:
#             return default_value

#         # Apply transformations
#         if transform_type == 'str':
#             return str(value)
#         elif transform_type == 'datetime':
#             format_str = field_config.get('format', '%Y-%m-%d %H:%M')
#             return value.strftime(format_str) if hasattr(value, 'strftime') else str(value)
#         elif transform_type == 'truncate':
#             length = field_config.get('length', 50)
#             text = str(value)
#             return text[:length] + '...' if len(text) > length else text
#         elif transform_type == 'duration':
#             if isinstance(value, int):
#                 return f"{value}s"
#             return str(value)
#         elif transform_type == 'priority':
#             priority_map = {1: 'Low', 2: 'Normal', 3: 'High', 4: 'Urgent'}
#             return priority_map.get(value, str(value))
#         elif transform_type == 'show_slot':
#             if hasattr(value, 'start_time') and hasattr(value, 'end_time'):
#                 return f"{value.start_time} - {value.end_time}"
#             return default_value
#         else:
#             return str(value)

#     except Exception:
#         return default_value


def _calculate_text_height(pdf, text, width_mm, line_height=5.5):
    """
    Calculate the height needed for text using PyFPDF's get_string_width method
    Returns the number of lines and total height needed
    """
    text = str(text).strip() if text else ""
    if not text:
        return 1, line_height

    # Use PyFPDF's built-in method to get accurate text width
    words = text.split()
    lines = []
    current_line = ""

    for word in words:
        test_line = current_line + (" " if current_line else "") + word
        test_width = pdf.get_string_width(test_line)

        if test_width <= width_mm:
            current_line = test_line
        else:
            if current_line:
                lines.append(current_line)
                current_line = word
            else:
                # Single word is too long, we'll need to break it
                # For now, just add it as is - PyFPDF will handle it
                current_line = word

    if current_line:
        lines.append(current_line)

    num_lines = len(lines) if lines else 1
    return num_lines, num_lines * line_height


def _wrap_text_for_cell(text, width_mm, font_size=9):
    """
    Enhanced text wrapping function for PDF cells
    Returns list of lines that fit within the specified width
    """
    # More conservative character calculation for font size 9
    chars_per_mm = 1.2  # Conservative for font size 9 with padding
    chars_per_line = int(width_mm * chars_per_mm)
    if chars_per_line < 30:  # Minimum reasonable line length
        chars_per_line = 30

    text = str(text).strip() if text else ""
    if not text:
        return [""]

    # Handle very long text more aggressively
    words = text.split()
    lines = []
    current_line = ""

    for word in words:
        # If adding this word would exceed the line length
        test_line = current_line + (" " if current_line else "") + word
        if len(test_line) <= chars_per_line:
            current_line = test_line
        else:
            # Save current line if it has content
            if current_line:
                lines.append(current_line)

            # Handle the new word
            if len(word) <= chars_per_line:
                current_line = word
            else:
                # Word is too long, break it into chunks
                while len(word) > chars_per_line:
                    lines.append(word[:chars_per_line])
                    word = word[chars_per_line:]
                current_line = word if word else ""

    # Add the last line if it has content
    if current_line:
        lines.append(current_line)

    return lines if lines else [""]


# Dynamic reporting system completely removed



















# Dynamic reporting system functions removed


@login_required
def pdf_template_designer(request):
    """PDF Template Designer UI"""
    context = {
        'clients': Client.objects.filter(is_active=True),
        'shows': Show.objects.all(),
        'presenters': Presenter.objects.all(),
    }
    return render(request, 'reports/pdf_template_designer.html', context)


@login_required
def register_temp_template(request):
    """Register a temporary template for testing"""
    if request.method == 'POST':
        try:
            import json
            template_config = json.loads(request.body)

            # Store in session for temporary use
            if 'temp_templates' not in request.session:
                request.session['temp_templates'] = {}

            # Add to session
            request.session['temp_templates'].update(template_config)
            request.session.modified = True

            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=400)

    return JsonResponse({'error': 'Invalid method'}, status=405)


@login_required
def save_pdf_template(request):
    """Save PDF template to database"""
    if request.method == 'POST':
        try:
            import json
            template_data = json.loads(request.body)

            template_name = template_data.get('name')
            template_config = template_data.get('config')

            if not template_name or not template_config:
                return JsonResponse({'error': 'Missing template name or config'}, status=400)

            # Save to PDFTemplate model (you can create this model)
            # For now, we'll save to a JSON file
            import os
            templates_dir = os.path.join('apps', 'reports', 'saved_templates')
            os.makedirs(templates_dir, exist_ok=True)

            template_file = os.path.join(templates_dir, f'{template_name}.json')
            with open(template_file, 'w') as f:
                json.dump(template_config, f, indent=2)

            return JsonResponse({'success': True, 'message': 'Template saved successfully'})

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=400)

    return JsonResponse({'error': 'Invalid method'}, status=405)


@login_required
def load_pdf_templates(request):
    """Load saved PDF templates"""
    try:
        import os
        import json

        templates_dir = os.path.join('apps', 'reports', 'saved_templates')
        templates = {}

        if os.path.exists(templates_dir):
            for filename in os.listdir(templates_dir):
                if filename.endswith('.json'):
                    template_name = filename[:-5]  # Remove .json extension
                    template_path = os.path.join(templates_dir, filename)

                    try:
                        with open(template_path, 'r') as f:
                            template_config = json.load(f)
                            templates[template_name] = template_config
                    except Exception as e:
                        continue

        return JsonResponse({'templates': templates})

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)


@login_required
def weekly_trends(request):
    """Weekly trends report"""
    # Get week start date or default to current week
    week_start = request.GET.get('week_start')
    if week_start:
        week_start = datetime.strptime(week_start, '%Y-%m-%d').date()
    else:
        today = timezone.now().date()
        week_start = today - timedelta(days=today.weekday())

    week_end = week_start + timedelta(days=6)

    # Get daily data for the week
    daily_data = []
    for i in range(7):
        current_date = week_start + timedelta(days=i)

        mentions_created = Mention.objects.filter(created_at__date=current_date).count()
        readings_completed = MentionReading.objects.filter(
            actual_read_time__date=current_date
        ).count()

        daily_data.append({
            'date': current_date,
            'day_name': current_date.strftime('%A'),
            'mentions_created': mentions_created,
            'readings_completed': readings_completed,
        })

    # Calculate week totals
    week_mentions = sum(day['mentions_created'] for day in daily_data)
    week_readings = sum(day['readings_completed'] for day in daily_data)

    # Get top performing shows for the week
    top_shows = Show.objects.annotate(
        week_readings=Count('mentionreading', filter=Q(
            mentionreading__actual_read_time__date__gte=week_start,
            mentionreading__actual_read_time__date__lte=week_end
        ))
    ).filter(week_readings__gt=0).order_by('-week_readings')[:5]

    context = {
        'week_start': week_start,
        'week_end': week_end,
        'daily_data': daily_data,
        'week_mentions': week_mentions,
        'week_readings': week_readings,
        'top_shows': top_shows,
    }

    return render(request, 'reports/weekly_trends.html', context)


@login_required
def monthly_overview(request):
    """Monthly overview report"""
    # Get month/year or default to current month
    month = request.GET.get('month')
    year = request.GET.get('year')

    if month and year:
        report_month = int(month)
        report_year = int(year)
    else:
        today = timezone.now()
        report_month = today.month
        report_year = today.year

    # Calculate month start and end dates
    month_start = datetime(report_year, report_month, 1).date()
    if report_month == 12:
        month_end = datetime(report_year + 1, 1, 1).date() - timedelta(days=1)
    else:
        month_end = datetime(report_year, report_month + 1, 1).date() - timedelta(days=1)

    # Get monthly statistics
    monthly_mentions = Mention.objects.filter(
        created_at__date__gte=month_start,
        created_at__date__lte=month_end
    ).count()

    monthly_readings = MentionReading.objects.filter(
        actual_read_time__date__gte=month_start,
        actual_read_time__date__lte=month_end
    ).count()

    # Get weekly breakdown
    weekly_data = []
    current_week_start = month_start
    week_number = 1

    while current_week_start <= month_end:
        current_week_end = min(current_week_start + timedelta(days=6), month_end)

        week_mentions = Mention.objects.filter(
            created_at__date__gte=current_week_start,
            created_at__date__lte=current_week_end
        ).count()

        week_readings = MentionReading.objects.filter(
            actual_read_time__date__gte=current_week_start,
            actual_read_time__date__lte=current_week_end
        ).count()

        weekly_data.append({
            'week_number': week_number,
            'start_date': current_week_start,
            'end_date': current_week_end,
            'mentions': week_mentions,
            'readings': week_readings,
        })

        current_week_start = current_week_end + timedelta(days=1)
        week_number += 1

    # Get top clients for the month
    top_clients = Client.objects.annotate(
        month_mentions=Count('mention', filter=Q(
            mention__created_at__date__gte=month_start,
            mention__created_at__date__lte=month_end
        ))
    ).filter(month_mentions__gt=0).order_by('-month_mentions')[:5]

    context = {
        'report_month': report_month,
        'report_year': report_year,
        'month_name': month_start.strftime('%B'),
        'month_start': month_start,
        'month_end': month_end,
        'monthly_mentions': monthly_mentions,
        'monthly_readings': monthly_readings,
        'weekly_data': weekly_data,
        'top_clients': top_clients,
    }

    return render(request, 'reports/monthly_overview.html', context)


# =============================================================================
# CUSTOM REPORTS
# =============================================================================

@login_required
def custom_report_builder(request):
    """Custom report builder interface"""
    from .models import ReportTemplate

    if request.method == 'POST':
        # Handle template saving
        action = request.POST.get('action')

        if action == 'save_template':
            template_name = request.POST.get('template_name')
            template_description = request.POST.get('template_description', '')
            report_type = request.POST.get('report_type')

            # Parse filters and metrics from form data
            filters = {}
            metrics = []

            # Extract filters from POST data
            for key, value in request.POST.items():
                if key.startswith('filter_') and value:
                    filter_name = key.replace('filter_', '')
                    filters[filter_name] = value

            # Extract metrics from POST data
            for key in request.POST.keys():
                if key.startswith('metric_'):
                    metric_name = key.replace('metric_', '')
                    metrics.append(metric_name)

            # Create template
            template = ReportTemplate.objects.create(
                name=template_name,
                description=template_description,
                report_type=report_type,
                filters=filters,
                metrics=metrics,
                created_by=request.user
            )

            messages.success(request, f'Report template "{template_name}" saved successfully!')
            return redirect('reports:custom_report_builder')

    # Get available data sources
    clients = Client.objects.filter(is_active=True)
    shows = Show.objects.all()

    # Get user's saved templates
    saved_templates = ReportTemplate.objects.filter(
        Q(created_by=request.user) | Q(is_public=True)
    )

    context = {
        'clients': clients,
        'shows': shows,
        'saved_templates': saved_templates,
    }

    return render(request, 'reports/custom_report_builder.html', context)


@login_required
def saved_reports(request):
    """Saved reports management"""
    # Placeholder for saved reports functionality
    # In a real implementation, you would have a SavedReport model

    context = {
        'saved_reports': [],  # Placeholder
    }

    return render(request, 'reports/saved_reports.html', context)


@login_required
def schedule_report(request):
    """Comprehensive schedule details and analysis report"""
    from apps.mentions.models import RecurringMention, MentionAuditLog
    from apps.organizations.middleware import get_current_organization

    organization = get_current_organization(request)

    # Get all recurring mentions with their details
    if organization:
        recurring_mentions = RecurringMention.objects.filter(
            client__organization=organization
        ).select_related('client', 'created_by').prefetch_related(
            'recurringmentionshow_set__show',
            'mention_set'
        ).order_by('-created_at')
    else:
        recurring_mentions = RecurringMention.objects.all().select_related(
            'client', 'created_by'
        ).prefetch_related(
            'recurringmentionshow_set__show',
            'mention_set'
        ).order_by('-created_at')

    # Get recent audit logs for schedule changes
    recent_audit_logs = MentionAuditLog.objects.filter(
        recurring_mention__isnull=False
    ).select_related('recurring_mention', 'changed_by')

    if organization:
        recent_audit_logs = recent_audit_logs.filter(
            recurring_mention__client__organization=organization
        )

    recent_audit_logs = recent_audit_logs.order_by('-created_at')[:20]

    # Calculate statistics
    total_patterns = recurring_mentions.count()
    active_patterns = recurring_mentions.filter(is_active=True).count()
    total_generated_mentions = sum(rm.generated_mentions_count for rm in recurring_mentions)

    # Group by frequency
    frequency_stats = {}
    for rm in recurring_mentions:
        freq = rm.get_frequency_display()
        if freq not in frequency_stats:
            frequency_stats[freq] = {'count': 0, 'active': 0}
        frequency_stats[freq]['count'] += 1
        if rm.is_active:
            frequency_stats[freq]['active'] += 1

    # Find split schedule relationships
    split_schedules = []
    for log in recent_audit_logs:
        if log.metadata and log.metadata.get('split_action'):
            split_schedules.append({
                'log': log,
                'original_id': log.metadata.get('original_mention_id'),
                'original_title': log.metadata.get('original_mention_title'),
                'split_method': log.metadata.get('split_method'),
                'split_label': log.metadata.get('split_label'),
            })

    context = {
        'recurring_mentions': recurring_mentions,
        'recent_audit_logs': recent_audit_logs,
        'split_schedules': split_schedules,
        'stats': {
            'total_patterns': total_patterns,
            'active_patterns': active_patterns,
            'inactive_patterns': total_patterns - active_patterns,
            'total_generated_mentions': total_generated_mentions,
            'frequency_stats': frequency_stats,
        },
        'organization': organization,
    }

    return render(request, 'reports/schedule_details.html', context)


# =============================================================================
# PDF TEMPLATE MANAGEMENT (COMMENTED OUT - USING DIRECT FPDF GENERATION)
# =============================================================================

# @login_required
# def create_pdf_template(request):
#     """Create new PDF template"""
#     if request.method == 'POST':
#         template = PDFTemplate(
#             name=request.POST['name'],
#             template_type=request.POST['template_type'],
#             description=request.POST.get('description', ''),
#             header_config=json.loads(request.POST.get('header_config', '{}')),
#             footer_config=json.loads(request.POST.get('footer_config', '{}')),
#             body_config=json.loads(request.POST.get('body_config', '{}')),
#             style_config=json.loads(request.POST.get('style_config', '{}')),
#             created_by=request.user
#         )
#         template.save()
#         messages.success(request, f'PDF template "{template.name}" created successfully!')
#         return JsonResponse({'success': True, 'template_id': template.id})
#
#     return render(request, 'reports/pdf_templates/create.html')
#
#
# @login_required
# def list_pdf_templates(request):
#     """List all available PDF templates"""
#     templates = PDFTemplate.objects.filter(is_active=True).order_by('-created_at')
#     return render(request, 'reports/pdf_templates/list.html', {'templates': templates})
#
#
# @login_required
# def generate_pdf_from_template(request, template_id):
#     """Generate PDF using specific template"""
#     template = get_object_or_404(PDFTemplate, id=template_id)
#
#     if request.method == 'POST':
#         data = json.loads(request.body)
#
#         # Generate PDF
#         engine = PDFTemplateEngine(template)
#         pdf = engine.generate(data)
#
#         # Save document record
#         doc = PDFDocument(
#             template=template,
#             title=data.get('title', f'{template.name} - {datetime.now().strftime("%Y%m%d")}'),
#             data=data,
#             created_by=request.user
#         )
#         doc.save()
#
#         # Return PDF response
#         filename = f"{template.name}_{doc.id}.pdf"
#         return create_pdf_response(pdf, filename)
#
#     # Show template preview/form
#     return render(request, 'reports/pdf_templates/generate.html', {'template': template})
#
#
# @login_required
# def template_preview(request, template_id):
#     """Preview template with sample data"""
#     template = get_object_or_404(PDFTemplate, id=template_id)
#
#     # Sample data for preview based on template type
#     sample_data = _get_sample_data_for_template(template.template_type)
#
#     engine = PDFTemplateEngine(template)
#     pdf = engine.generate(sample_data)
#
#     filename = f"preview_{template.name}.pdf"
#     response = HttpResponse(content_type='application/pdf')
#     response['Content-Disposition'] = f'inline; filename="{filename}"'
#
#     pdf_output = pdf.output(dest='S').encode('latin-1')
#     response.write(pdf_output)
#
#     return response


# Template designer functions commented out - using direct FPDF generation


# =============================================================================
# API ENDPOINTS
# =============================================================================