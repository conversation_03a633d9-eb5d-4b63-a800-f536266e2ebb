"""
Enhanced PDF Generation Engine for AppRadio Reports
Uses FPDF2 to generate professional PDF reports with advanced styling and features.
Focused on direct PDF generation without template designer complexity.
"""

from fpdf import FPDF
from django.http import HttpResponse
from django.conf import settings
import os
from datetime import datetime
import json
import re
import math


class EnhancedPDF(FPDF):
    """Enhanced FPDF class with professional styling and features"""

    def __init__(self, template_config=None, orientation='P', unit='mm', format='A4'):
        super().__init__(orientation=orientation, unit=unit, format=format)
        self.template_config = template_config or {}
        self.header_config = self.template_config.get('header_config', {})
        self.footer_config = self.template_config.get('footer_config', {})
        self.style_config = self.template_config.get('style_config', {})

        # Enhanced styling defaults
        self.primary_color = (41, 128, 185)  # Professional blue
        self.secondary_color = (52, 73, 94)  # Dark gray
        self.accent_color = (231, 76, 60)    # Red for highlights
        self.light_gray = (236, 240, 241)    # Light background
        self.dark_gray = (127, 140, 141)     # Medium gray

        # Typography settings
        self.title_font_size = 18
        self.subtitle_font_size = 14
        self.body_font_size = 10
        self.small_font_size = 8
        
    def header(self):
        """Enhanced professional header with populated template placeholders"""
        if not self.header_config.get('enabled', True):
            return

        # Only show header on first page
        if self.page_no() > 1:
            return

        # Professional header background with gradient effect
        # Get page width dynamically (210mm for portrait, 297mm for landscape)
        page_width = self.w
        self.set_fill_color(*self.primary_color)
        self.rect(0, 0, page_width, 25, 'F')

        # White line separator
        self.set_fill_color(255, 255, 255)
        self.rect(0, 25, page_width, 1, 'F')

        # Get organization info for populating placeholders
        org_info = self.template_config.get('organization', {})

        # Left side: {{ logo }} - Create styled radio station logo
        self._draw_styled_radio_logo(org_info)

        # Center: Dynamic title (replaces static "Report")
        # Calculate responsive positioning based on page width
        page_width = self.w
        center_x = page_width / 2 - 35  # Center position minus half title width
        right_x = page_width - 60  # Right position with margin

        self.set_font('Arial', 'B', self.header_config.get('font_size', 16))
        self.set_text_color(255, 255, 255)  # White text for header
        self.set_xy(center_x, 8)
        # Get dynamic title based on report data
        dynamic_title = self._get_dynamic_title()
        self.cell(70, 10, dynamic_title, 0, 0, 'C')

        # Right side: Date only
        self.set_font('Arial', '', 12)
        self.set_text_color(255, 255, 255)  # White text for date
        self.set_xy(right_x, 8)
        current_date = datetime.now().strftime('%B %d, %Y')
        self.cell(50, 10, current_date, 0, 0, 'R')

        # Reset text color and move to content area
        self.set_text_color(0, 0, 0)
        self.ln(20)

        # Below header: {{ address }} and {{ title }} placeholders
        self._add_address_and_title_section()

        self.ln(10)  # Space before main content

    def _draw_styled_radio_logo(self, org_info):
        """Draw logo image or styled radio station logo in the header"""
        try:
            # First try to add actual logo image
            logo_path = None
            if org_info.get('logo_path') and os.path.exists(org_info['logo_path']):
                logo_path = org_info['logo_path']
            else:
                # Try default logo
                default_logo_path = os.path.join(settings.STATIC_ROOT or settings.BASE_DIR / 'static', 'images', 'radio_logo.png')
                if os.path.exists(default_logo_path):
                    logo_path = default_logo_path

            if logo_path:
                try:
                    # Add actual logo image
                    self.image(logo_path, x=10, y=6, w=25, h=13)
                    return  # Successfully added image, no need for styled fallback
                except Exception:
                    pass  # Fall through to styled logo

            # Fallback: Create styled radio station logo
            # Create red background for logo
            self.set_fill_color(220, 53, 69)  # Red background
            self.rect(10, 6, 45, 13, 'F')

            # Add white text on red background
            self.set_text_color(255, 255, 255)
            self.set_font('Arial', 'B', 10)
            self.set_xy(12, 8)

            # Station name
            station_name = org_info.get('name', 'Radiocity')
            self.cell(20, 5, station_name, 0, 0, 'L')

            # Frequency below
            self.set_font('Arial', 'B', 8)
            self.set_xy(12, 13)
            self.cell(20, 4, '97fm', 0, 0, 'L')

            # Add small decorative elements
            self.set_fill_color(255, 255, 255)
            self.ellipse(35, 8, 2, 2, 'F')
            self.ellipse(35, 11, 2, 2, 'F')
            self.ellipse(35, 14, 2, 2, 'F')

        except Exception:
            # Final fallback to simple text
            self.set_text_color(255, 255, 255)
            self.set_font('Arial', 'B', 12)
            self.set_xy(10, 8)
            self.cell(45, 10, 'RADIOCITY', 0, 0, 'L')

    def _add_address_and_title_section(self):
        """Add the address and title section below the header"""
        try:
            org_info = self.template_config.get('organization', {})

            # Left side: Organization address from database
            self.set_font('Arial', '', 10)
            self.set_text_color(0, 0, 0)

            # Get address from database or use default
            address = org_info.get('address', '')
            if address:
                # Parse address lines
                address_lines = address.strip().split('\n')
                for i, line in enumerate(address_lines[:3]):  # Limit to 3 lines
                    if line.strip():
                        if i == 0:
                            self.cell(0, 5, f"Located in: {line.strip()}", 0, 1, 'L')
                        else:
                            self.cell(0, 5, f"Address: {line.strip()}", 0, 1, 'L')
            else:
                pass
                # Default address when no database address
                # self.cell(0, 5, "Located in: Nkrumah Nasser Link Plaza", 0, 1, 'L')
                # self.cell(0, 5, "Address: Nkrumah Nasser Link Plaza, Kampala", 0, 1, 'L')

            # Phone number from database
            phone = org_info.get('phone', {})
            self.cell(0, 5, f"Phone: {phone}", 0, 1, 'L')

            # Right side: Static "Report" text
            title_y = self.get_y() - 15  # Position for title
            self.set_xy(150, title_y)
            self.set_font('Arial', '', 12)
            self.set_text_color(128, 128, 128)  # Gray color

            # Static "Report" text (dynamic title is now in header center)
            self.cell(50, 8, 'Report', 0, 0, 'C')

        except Exception:
            # Fallback content
            self.set_font('Arial', '', 10)
            self.cell(0, 5, "Organization Address", 0, 1, 'L')
            self.cell(0, 5, "Phone: Contact Information", 0, 1, 'L')

    def _get_dynamic_title(self):
        """Generate dynamic title based on report data and context"""
        try:
            # Get report data if available
            report_data = getattr(self, 'report_data', {})
            report_type = getattr(self, 'report_type', '')

            # Generate dynamic title based on report type and data
            if report_type == 'client-order-schedule':
                client_name = report_data.get('client_name', 'Client')
                return f"{client_name} Schedule"

            elif report_type == 'analytics':
                # Check if there's specific analytics data
                if 'total_mentions' in report_data:
                    total = report_data.get('total_mentions', 0)
                    return f"Analytics ({total} mentions)"
                return "Analytics Dashboard"

            elif report_type == 'mentions-summary':
                # Dynamic mentions summary title
                summary_stats = report_data.get('summary_stats', {})
                if summary_stats:
                    total = summary_stats.get('total_scheduled', 0)
                    return f"Mentions Summary ({total} total)"
                return "Mentions Summary"

            elif 'client_name' in report_data:
                # Generic client-based title
                client_name = report_data['client_name']
                return f"{client_name} Report"

            elif 'summary_stats' in report_data:
                # Generic stats-based title
                stats = report_data['summary_stats']
                if 'total_scheduled' in stats:
                    total = stats['total_scheduled']
                    return f"Report ({total} items)"

            # Fallback to header config title
            return self.header_config.get('title', 'Report')

        except Exception:
            # Final fallback
            return self.header_config.get('title', 'Report')

    def _get_organization_address(self):
        """Get organization address from template config or default"""
        try:
            # Get organization info from template config
            org_info = self.template_config.get('organization', {})
            if org_info.get('address'):
                # Truncate address if too long for header
                address = org_info['address'].strip()
                if len(address) > 50:
                    # Take first line or first 50 characters
                    lines = address.split('\n')
                    if len(lines) > 1:
                        return lines[0][:50]
                    else:
                        return address[:47] + "..."
                return address

            # Default fallback address
            return "123 Radio Station Blvd, City, ST 12345"

        except Exception:
            return "Radio Station Address"
    
    def footer(self):
        """Enhanced professional footer"""
        if not self.footer_config.get('enabled', True):
            return

        # Footer separator line
        self.set_y(-20)
        self.set_fill_color(*self.light_gray)
        self.rect(10, -20, 190, 0.5, 'F')

        # Footer content
        self.set_y(-15)
        self.set_font('Arial', '', self.footer_config.get('font_size', 8))
        self.set_text_color(*self.dark_gray)

        # Left side - Company info
        self.set_x(10)
        self.cell(60, 10, 'Radio Mention Manager', 0, 0, 'L')

        # Center - Generation timestamp
        generation_time = datetime.now().strftime('%Y-%m-%d %H:%M')
        self.cell(70, 10, f'Generated: {generation_time}', 0, 0, 'C')

        # Right side - Page number with proper formatting
        page_text = f'Page {self.page_no()} of {{nb}}'
        self.cell(60, 10, page_text, 0, 0, 'R')

        # Reset text color
        self.set_text_color(0, 0, 0)

    def add_section_header(self, title, level=1):
        """Add a styled section header with reduced spacing"""
        if level == 1:
            # Main section header with reduced spacing
            self.ln(4)  # Reduced from 8 to 4
            self.set_fill_color(*self.primary_color)
            self.set_text_color(255, 255, 255)
            self.set_font('Arial', 'B', self.subtitle_font_size)
            self.cell(0, 10, f'  {title}', 0, 1, 'L', True)  # Reduced height from 12 to 10
            self.set_text_color(0, 0, 0)
            self.ln(3)  # Reduced from 5 to 3
        elif level == 2:
            # Sub-section header with reduced spacing
            self.ln(3)  # Reduced from 5 to 3
            self.set_font('Arial', 'B', 12)
            self.set_text_color(*self.secondary_color)
            self.cell(0, 6, title, 0, 1, 'L')  # Reduced height from 8 to 6
            self.set_text_color(0, 0, 0)
            self.ln(1)  # Reduced from 2 to 1

    def add_metric_card(self, title, value, x, y, width=45, height=25):
        """Add a styled metric card"""
        # Card background
        self.set_fill_color(*self.light_gray)
        self.rect(x, y, width, height, 'F')

        # Card border
        self.set_draw_color(*self.primary_color)
        self.set_line_width(0.5)
        self.rect(x, y, width, height)

        # Value (large text)
        self.set_xy(x + 2, y + 3)
        self.set_font('Arial', 'B', 16)
        self.set_text_color(*self.primary_color)
        self.cell(width - 4, 12, str(value), 0, 0, 'C')

        # Title (small text)
        self.set_xy(x + 2, y + 15)
        self.set_font('Arial', '', 8)
        self.set_text_color(*self.secondary_color)
        self.cell(width - 4, 8, title, 0, 0, 'C')

        # Reset colors
        self.set_text_color(0, 0, 0)
        self.set_draw_color(0, 0, 0)
        self.set_line_width(0.2)


class PDFTemplateEngine:
    """Main engine for generating PDFs from templates and data"""
    
    def __init__(self, template):
        self.template = template
        
    def generate(self, data):
        """Generate enhanced PDF using template and data"""

        # Create PDF with template configuration
        pdf = EnhancedPDF({
            'header_config': self.template.header_config,
            'footer_config': self.template.footer_config,
            'style_config': self.template.style_config,
        })
        
        pdf.add_page()
        
        # Apply global styles
        self._apply_global_styles(pdf)
        
        # Generate content based on template type
        if self.template.template_type == 'mentions_summary':
            self._generate_mentions_summary(pdf, data)
        elif self.template.template_type == 'client_report':
            self._generate_client_report(pdf, data)
        elif self.template.template_type == 'analytics_report':
            self._generate_analytics_report(pdf, data)
        elif self.template.template_type == 'daily_summary':
            self._generate_daily_summary(pdf, data)
        elif self.template.template_type == 'weekly_trends':
            self._generate_weekly_trends(pdf, data)
        elif self.template.template_type == 'monthly_overview':
            self._generate_monthly_overview(pdf, data)
        else:
            self._generate_custom(pdf, data)
        
        return pdf
    
    def _apply_global_styles(self, pdf):
        """Apply global styling from template"""
        style = self.template.style_config
        
        # Set default font
        font_family = style.get('font_family', 'Arial')
        font_size = style.get('font_size', 12)
        pdf.set_font(font_family, size=font_size)
        
        # Set default text color
        if style.get('text_color'):
            color = style['text_color']
            pdf.set_text_color(color['r'], color['g'], color['b'])
        else:
            pdf.set_text_color(0, 0, 0)  # Default black
    
    def _generate_mentions_summary(self, pdf, data):
        """Generate enhanced mentions summary report"""
        # Report title with professional styling
        pdf.set_font('Arial', 'B', pdf.title_font_size)
        pdf.set_text_color(*pdf.primary_color)
        pdf.cell(0, 15, 'Mentions Summary Report', 0, 1, 'C')
        pdf.set_text_color(0, 0, 0)

        # Date range with subtle styling
        pdf.set_font('Arial', '', 11)
        pdf.set_text_color(*pdf.dark_gray)
        date_range = f"Period: {data.get('start_date', 'N/A')} to {data.get('end_date', 'N/A')}"
        pdf.cell(0, 8, date_range, 0, 1, 'C')
        pdf.set_text_color(0, 0, 0)
        pdf.ln(10)

        # Enhanced summary statistics with metric cards
        self._create_enhanced_summary_section(pdf, data.get('summary_stats', {}))

        # Mentions table with enhanced styling
        if data.get('mentions'):
            pdf.add_section_header('Recent Mentions', level=1)

            self._create_enhanced_table(pdf, data['mentions'], {
                'columns': [
                    {'header': 'Date', 'width': 35, 'field': 'date'},
                    {'header': 'Client', 'width': 55, 'field': 'client_name'},
                    {'header': 'Show', 'width': 65, 'field': 'show_name'},
                    {'header': 'Status', 'width': 35, 'field': 'status'},
                ]
            })
    
    def _generate_client_report(self, pdf, data):
        """Generate client-specific report"""
        # Client name and report title
        pdf.set_font('Arial', 'B', 18)
        client_name = data.get('client_name', 'Client')
        pdf.cell(0, 15, f'{client_name} - Performance Report', 0, 1, 'C')
        pdf.ln(10)
        
        # Date range
        pdf.set_font('Arial', size=11)
        date_range = f"Period: {data.get('start_date', 'N/A')} to {data.get('end_date', 'N/A')}"
        pdf.cell(0, 8, date_range, 0, 1, 'C')
        pdf.ln(10)
        
        # Client statistics
        self._create_summary_section(pdf, data.get('client_stats', {}))
        
        # Mentions breakdown
        if data.get('mentions_by_show'):
            pdf.ln(5)
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, 'Mentions by Show', 0, 1)
            
            self._create_table(pdf, data['mentions_by_show'], {
                'columns': [
                    {'header': 'Show', 'width': 80, 'field': 'show_name'},
                    {'header': 'Total', 'width': 30, 'field': 'total_mentions', 'align': 'C'},
                    {'header': 'Completed', 'width': 30, 'field': 'completed', 'align': 'C'},
                    {'header': 'Pending', 'width': 30, 'field': 'pending', 'align': 'C'},
                ]
            })
    
    def _generate_analytics_report(self, pdf, data):
        """Generate analytics report"""
        pdf.set_font('Arial', 'B', 18)
        pdf.cell(0, 15, 'Analytics Report', 0, 1, 'C')
        pdf.ln(10)
        
        # Key metrics
        self._create_summary_section(pdf, data.get('key_metrics', {}))
        
        # Trends section
        if data.get('trends'):
            pdf.ln(5)
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, 'Performance Trends', 0, 1)
            pdf.set_font('Arial', size=10)

            for trend in data['trends']:
                pdf.cell(0, 6, f"- {trend}", 0, 1)
    
    def _generate_daily_summary(self, pdf, data):
        """Generate daily summary report"""
        pdf.set_font('Arial', 'B', 18)
        report_date = data.get('report_date', datetime.now().strftime('%Y-%m-%d'))
        pdf.cell(0, 15, f'Daily Summary - {report_date}', 0, 1, 'C')
        pdf.ln(10)
        
        # Daily statistics
        self._create_summary_section(pdf, data.get('daily_stats', {}))
        
        # Hourly breakdown if available
        if data.get('hourly_breakdown'):
            pdf.ln(5)
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, 'Hourly Activity', 0, 1)
            
            self._create_table(pdf, data['hourly_breakdown'], {
                'columns': [
                    {'header': 'Hour', 'width': 40, 'field': 'hour'},
                    {'header': 'Mentions Read', 'width': 50, 'field': 'count', 'align': 'C'},
                ]
            })
    
    def _generate_weekly_trends(self, pdf, data):
        """Generate weekly trends report"""
        pdf.set_font('Arial', 'B', 18)
        pdf.cell(0, 15, 'Weekly Trends Report', 0, 1, 'C')
        pdf.ln(10)
        
        # Week summary
        self._create_summary_section(pdf, data.get('week_summary', {}))
        
        # Daily breakdown
        if data.get('daily_breakdown'):
            pdf.ln(5)
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, 'Daily Breakdown', 0, 1)
            
            self._create_table(pdf, data['daily_breakdown'], {
                'columns': [
                    {'header': 'Date', 'width': 40, 'field': 'date'},
                    {'header': 'Mentions', 'width': 30, 'field': 'mentions', 'align': 'C'},
                    {'header': 'Completed', 'width': 30, 'field': 'completed', 'align': 'C'},
                ]
            })
    
    def _generate_monthly_overview(self, pdf, data):
        """Generate monthly overview report"""
        pdf.set_font('Arial', 'B', 18)
        month_name = data.get('month_name', 'Month')
        year = data.get('year', datetime.now().year)
        pdf.cell(0, 15, f'{month_name} {year} - Monthly Overview', 0, 1, 'C')
        pdf.ln(10)
        
        # Monthly statistics
        self._create_summary_section(pdf, data.get('monthly_stats', {}))
        
        # Top clients
        if data.get('top_clients'):
            pdf.ln(5)
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 10, 'Top Clients', 0, 1)
            
            self._create_table(pdf, data['top_clients'], {
                'columns': [
                    {'header': 'Client', 'width': 80, 'field': 'name'},
                    {'header': 'Mentions', 'width': 30, 'field': 'mentions_count', 'align': 'C'},
                ]
            })

    def _generate_custom(self, pdf, data):
        """Generate custom PDF based on body_config"""
        body_config = self.template.body_config

        for element in body_config.get('elements', []):
            self._render_element(pdf, element, data)

    def _render_element(self, pdf, element, data):
        """Render individual template element"""
        element_type = element.get('type')

        if element_type == 'text':
            pdf.set_font(element.get('font', 'Arial'),
                        element.get('style', ''),
                        element.get('size', 12))
            text = self._replace_variables(element.get('content', ''), data)
            pdf.cell(0, element.get('height', 8), text, 0, 1, element.get('align', 'L'))

        elif element_type == 'multiline':
            pdf.set_font(element.get('font', 'Arial'),
                        element.get('style', ''),
                        element.get('size', 12))
            text = self._replace_variables(element.get('content', ''), data)
            pdf.multi_cell(0, element.get('line_height', 6), text)

        elif element_type == 'table':
            table_data = data.get(element.get('data_key', 'table_data'), [])
            self._create_table(pdf, table_data, element.get('config', {}))

        elif element_type == 'line_break':
            pdf.ln(element.get('height', 5))

    def _replace_variables(self, text, data):
        """Replace {{variable}} placeholders with actual data"""
        def replace_var(match):
            var_name = match.group(1)
            keys = var_name.split('.')
            value = data

            try:
                for key in keys:
                    value = value[key]
                return str(value)
            except (KeyError, TypeError):
                return f"{{{{{var_name}}}}}"  # Return original if not found

        return re.sub(r'\{\{([^}]+)\}\}', replace_var, text)

    def _create_enhanced_summary_section(self, pdf, stats):
        """Create enhanced summary statistics with metric cards"""
        if not stats:
            return

        pdf.add_section_header('Summary Statistics', level=1)

        # Create metric cards layout (4 cards per row)
        current_y = pdf.get_y()
        card_width = 45
        card_height = 25
        margin = 5
        start_x = 10

        stats_items = list(stats.items())
        cards_per_row = 4

        for i, (key, value) in enumerate(stats_items):
            row = i // cards_per_row
            col = i % cards_per_row

            x = start_x + col * (card_width + margin)
            y = current_y + row * (card_height + margin)

            # Format the title
            title = key.replace('_', ' ').title()
            if len(title) > 12:
                title = title[:12] + '...'

            pdf.add_metric_card(title, value, x, y, card_width, card_height)

        # Move cursor below the cards
        rows_needed = (len(stats_items) + cards_per_row - 1) // cards_per_row
        pdf.set_y(current_y + rows_needed * (card_height + margin) + 10)

    def _create_summary_section(self, pdf, stats):
        """Fallback method for backward compatibility"""
        return self._create_enhanced_summary_section(pdf, stats)

    def _create_enhanced_table(self, pdf, data, config):
        """Create an enhanced table with professional styling"""
        if not data or not config.get('columns'):
            return

        columns = config['columns']

        # Table header with enhanced styling
        pdf.set_fill_color(*pdf.primary_color)
        pdf.set_text_color(255, 255, 255)
        pdf.set_font('Arial', 'B', config.get('header_font_size', 10))

        for col in columns:
            pdf.cell(col['width'], 10, col['header'], 1, 0, col.get('align', 'L'), True)
        pdf.ln()

        # Table rows with alternating colors
        pdf.set_text_color(0, 0, 0)
        pdf.set_font('Arial', size=config.get('row_font_size', 9))

        for i, row in enumerate(data):
            # Alternating row colors
            if i % 2 == 0:
                pdf.set_fill_color(255, 255, 255)  # White
            else:
                pdf.set_fill_color(*pdf.light_gray)  # Light gray

            for col in columns:
                value = row.get(col['field'], '')

                # Format value if needed
                if col.get('format') == 'currency':
                    try:
                        value = f"${float(value):.2f}" if value else '$0.00'
                    except (ValueError, TypeError):
                        value = '$0.00'
                elif col.get('format') == 'percentage':
                    try:
                        value = f"{float(value):.1f}%" if value else '0.0%'
                    except (ValueError, TypeError):
                        value = '0.0%'

                pdf.cell(col['width'], 8, str(value), 1, 0, col.get('align', 'L'), True)
            pdf.ln()

        # Reset colors
        pdf.set_fill_color(255, 255, 255)
        pdf.set_text_color(0, 0, 0)

    def _create_table(self, pdf, data, config):
        """Fallback method for backward compatibility"""
        return self._create_enhanced_table(pdf, data, config)


def create_direct_pdf(report_type, data, title=None, options=None):
    """Create PDF directly without template system - Enhanced FPDF approach"""

    # Set default options
    if options is None:
        options = {}

    # Set default orientation based on report type
    default_orientation = 'landscape' if report_type == 'show-readings' else 'portrait'
    orientation = options.get('orientation', default_orientation)
    font_size = options.get('font_size', 12)

    # Create enhanced PDF with orientation and pass organization info
    orientation_code = orientation[0].upper()  # 'P' for portrait, 'L' for landscape
    template_config = {
        'organization': options.get('organization', {}),
        'header_config': {},
        'footer_config': {},
        'style_config': {}
    }
    pdf = EnhancedPDF(template_config=template_config, orientation=orientation_code, unit='mm', format='A4')

    # Store options and data in PDF BEFORE adding page (so header can access it)
    pdf.pdf_options = options
    pdf.report_data = data
    pdf.report_type = report_type

    # Set header title
    if title:
        pdf.header_config['title'] = title
    else:
        pdf.header_config['title'] = f"{report_type.replace('-', ' ').title()} Report"

    # Enable page numbering
    pdf.alias_nb_pages()

    # Now add page (header will have access to data)
    pdf.add_page()

    # Generate content based on report type - Dynamic approach
    try:
        # Try to find a specific generator function for this report type
        generator_function_name = f'_generate_direct_{report_type.replace("-", "_")}'

        # Check if specific generator exists in current module
        current_module = globals()
        if generator_function_name in current_module:
            generator_function = current_module[generator_function_name]
            generator_function(pdf, data)
        else:
            # Use category-based generators for flexibility
            if report_type == 'mentions-summary':
                _generate_direct_mentions_summary(pdf, data)
            elif report_type in ['mentions-by-client', 'client-mentions-detailed', 'client-activity', 'client-revenue', 'client-retention', 'client-order-schedule']:
                _generate_direct_client_report(pdf, data, report_type)
            elif report_type in ['show-performance', 'show-schedule', 'show-readings', 'presenter-activity']:
                _generate_direct_show_report(pdf, data, report_type)
            elif report_type == 'analytics':
                _generate_direct_analytics_report(pdf, data)
            elif report_type == 'daily-summary':
                _generate_direct_daily_summary(pdf, data)
            elif report_type == 'weekly-trends':
                _generate_direct_weekly_trends(pdf, data)
            elif report_type == 'monthly-overview':
                _generate_direct_monthly_overview(pdf, data)
            elif report_type == 'news-reader-schedule':
                _generate_news_reader_schedule_pdf(pdf, data)
            elif report_type == 'news-reader-performance':
                _generate_news_reader_performance_pdf(pdf, data)
            else:
                # Fallback for any unknown report types
                _generate_direct_generic_report(pdf, data, report_type)
    except Exception as e:
        # If specific generator fails, use generic fallback
        print(f"Error generating {report_type} report: {e}")
        _generate_direct_generic_report(pdf, data, report_type)

    return pdf


def _generate_direct_mentions_summary(pdf, data):
    """Generate mentions summary with enhanced styling"""
    # Report title
    pdf.set_font('Arial', 'B', pdf.title_font_size)
    pdf.set_text_color(*pdf.primary_color)
    pdf.cell(0, 15, 'Mentions Summary Report', 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)

    # Date range
    pdf.set_font('Arial', '', 11)
    pdf.set_text_color(*pdf.dark_gray)
    date_range = f"Period: {data.get('start_date', 'N/A')} to {data.get('end_date', 'N/A')}"
    pdf.cell(0, 8, date_range, 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)
    pdf.ln(10)

    # Key metrics with cards
    stats = {
        'Total Mentions': data.get('total_mentions', 0),
        'Approved': data.get('approved_mentions', 0),
        'Pending': data.get('pending_mentions', 0),
        'Completion Rate': f"{data.get('completion_rate', 0)}%"
    }

    pdf.add_section_header('Key Metrics', level=1)
    current_y = pdf.get_y()

    for i, (key, value) in enumerate(stats.items()):
        x = 10 + i * 47.5
        pdf.add_metric_card(key, value, x, current_y, 45, 25)

    pdf.set_y(current_y + 35)

    # Recent mentions table
    if data.get('mentions'):
        pdf.add_section_header('Recent Mentions', level=1)

        _create_enhanced_table(pdf, data['mentions'][:10], {
            'columns': [
                {'header': 'Date', 'width': 35, 'field': 'date'},
                {'header': 'Client', 'width': 55, 'field': 'client_name'},
                {'header': 'Show', 'width': 65, 'field': 'show_name'},
                {'header': 'Status', 'width': 35, 'field': 'status'},
            ]
        })


def _generate_direct_client_report(pdf, data, report_type):
    """Generate client-focused reports"""
    # Title based on report type
    titles = {
        'mentions-by-client': 'Mentions by Client',
        'client-mentions-detailed': 'Client Mentions - Detailed View',
        'client-activity': 'Client Activity Report',
        'client-revenue': 'Client Revenue Report',
        'client-retention': 'Client Retention Analysis',
        'client-order-schedule': 'Client Order Schedule'
    }

    title = titles.get(report_type, 'Client Report')

    pdf.set_font('Arial', 'B', pdf.title_font_size)
    pdf.set_text_color(*pdf.primary_color)
    pdf.cell(0, 15, title, 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)
    pdf.ln(10)

    # Handle client order schedule report specially
    if report_type == 'client-order-schedule':
        _generate_client_order_schedule_pdf(pdf, data)
        return

    # Client statistics for other reports
    if data.get('client_stats'):
        stats = data['client_stats']
        if isinstance(stats, list):
            # Multiple clients
            pdf.add_section_header('Client Performance Overview', level=1)
            _create_enhanced_table(pdf, stats, {
                'columns': [
                    {'header': 'Client', 'width': 60, 'field': 'name'},
                    {'header': 'Mentions', 'width': 30, 'field': 'mentions_created', 'align': 'C'},
                    {'header': 'Approved', 'width': 30, 'field': 'mentions_approved', 'align': 'C'},
                    {'header': 'Completed', 'width': 30, 'field': 'mentions_completed', 'align': 'C'},
                    {'header': 'Rate', 'width': 40, 'field': 'completion_rate', 'align': 'C'},
                ]
            })
        else:
            # Single client metrics
            metrics = {
                'Total Mentions': stats.get('total_mentions', 0),
                'Approved': stats.get('approved_mentions', 0),
                'Completed': stats.get('completed_readings', 0),
                'Success Rate': stats.get('completion_rate', '0%')
            }

            pdf.add_section_header('Performance Metrics', level=1)
            current_y = pdf.get_y()

            for i, (key, value) in enumerate(metrics.items()):
                x = 10 + i * 47.5
                pdf.add_metric_card(key, value, x, current_y, 45, 25)

            pdf.set_y(current_y + 35)


def _generate_direct_show_report(pdf, data, report_type):
    """Generate show/presenter reports"""
    titles = {
        'show-performance': 'Show Performance Report',
        'show-schedule': 'Show Schedule Report',
        'show-readings': 'Show Readings Report',
        'presenter-activity': 'Presenter Activity Report'
    }

    title = titles.get(report_type, 'Show Report')

    pdf.set_font('Arial', 'B', pdf.title_font_size)
    pdf.set_text_color(*pdf.primary_color)
    pdf.cell(0, 15, title, 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)
    pdf.ln(10)

    # Handle show-readings report specifically
    if report_type == 'show-readings':
        _generate_show_readings_content(pdf, data)
        return

    # Show basic metrics if available
    if data.get('show_stats'):
        pdf.add_section_header('Performance Overview', level=1)
        stats = data['show_stats']

        current_y = pdf.get_y()
        metrics = {
            'Total Shows': stats.get('total_shows', 0),
            'Active Shows': stats.get('active_shows', 0),
            'Avg Duration': f"{stats.get('avg_duration', 0)} min",
            'Success Rate': f"{stats.get('success_rate', 0)}%"
        }

        for i, (key, value) in enumerate(metrics.items()):
            x = 10 + i * 47.5
            pdf.add_metric_card(key, value, x, current_y, 45, 25)

        pdf.set_y(current_y + 35)


def _generate_show_readings_content(pdf, data):
    """Generate show readings report content"""
    # Show applied filters if any
    if data.get('filter_context'):
        pdf.add_section_header('Applied Filters', level=2)
        pdf.set_font('Arial', '', 10)

        filter_context = data['filter_context']
        filter_text = []

        if filter_context.get('show_filter'):
            filter_text.append(f"Show: {filter_context['show_filter']}")
        if filter_context.get('client_filter'):
            filter_text.append(f"Client: {filter_context['client_filter']}")
        if filter_context.get('presenter_filter'):
            filter_text.append(f"Presenter: {filter_context['presenter_filter']}")
        if filter_context.get('status_filter'):
            filter_text.append(f"Status: {filter_context['status_filter']}")
        if filter_context.get('priority_filter'):
            filter_text.append(f"Priority: {filter_context['priority_filter']}")

        if filter_text:
            pdf.cell(0, 8, " | ".join(filter_text), 0, 1)
        else:
            pdf.cell(0, 8, "No filters applied", 0, 1)

        pdf.ln(5)

    # Summary statistics section
    if data.get('summary_stats'):
        pdf.add_section_header('Summary Statistics', level=1)
        stats = data['summary_stats']

        current_y = pdf.get_y()
        metrics = {
            'Active Shows': stats.get('active_shows', 0),
            'Total Readings': stats.get('total_readings', 0),
            'Completed': stats.get('total_completed', 0),
            'Completion Rate': stats.get('overall_completion_rate', '0%')
        }

        for i, (key, value) in enumerate(metrics.items()):
            x = 10 + i * 47.5
            pdf.add_metric_card(key, value, x, current_y, 45, 25)

        pdf.set_y(current_y + 28)  # Reduced spacing from 35 to 28

    # Individual readings table (main focus)
    if data.get('recent_readings'):
        pdf.add_section_header('Show Readings Details', level=1)
        _create_show_readings_table(pdf, data['recent_readings'])

    # Show summary table (if space permits and not too many individual readings)
    if data.get('shows_readings') and len(data.get('recent_readings', [])) < 20:
        if pdf.get_y() < 200:  # Only if there's space
            pdf.add_section_header('Show Summary', level=2)
            _create_generic_table(pdf, data['shows_readings'], 'Show Performance Summary', 'show-summary')


def _print_table_header(pdf, columns):
    """Print table header with consistent formatting - helper function"""
    pdf.set_font('Arial', 'B', 9)
    pdf.set_fill_color(70, 130, 180)  # Steel blue background
    pdf.set_text_color(255, 255, 255)  # White text

    # Ensure we start at the left margin
    pdf.set_xy(10, pdf.get_y())

    for col in columns:
        pdf.cell(col['width'], 10, col['title'], 1, 0, 'C', True)
    pdf.ln()

    # Reset formatting for data rows
    pdf.set_text_color(0, 0, 0)
    pdf.set_font('Arial', '', 8)


def _create_show_readings_table(pdf, readings_data):
    """Create a detailed table for show readings with proper text wrapping and column constraints"""
    if not readings_data:
        pdf.set_font('Arial', '', 10)
        pdf.cell(0, 10, 'No readings data available for the selected period.', 0, 1)
        return

    # Define columns with proper widths for A4 landscape (280mm usable width)
    columns = [
        {'key': 'client', 'title': 'Client', 'width': 30},
        {'key': 'mention_content', 'title': 'Mention Content', 'width': 100},  # Reduced from 140mm to 110mm
        {'key': 'actual_read_time', 'title': 'Read Time', 'width': 35},
        {'key': 'show', 'title': 'Show', 'width': 30},
        {'key': 'presenter', 'title': 'Read By', 'width': 30},
        {'key': 'duration', 'title': 'Duration', 'width': 15},
        {'key': 'status', 'title': 'Status', 'width': 20}
    ]

    # Print initial table header
    _print_table_header(pdf, columns)

    for i, reading in enumerate(readings_data):
        # Get the full mention content
        mention_content = str(reading.get('mention_content', reading.get('mention_title', 'N/A')))

        # Prepare other row data
        row_data = {
            'client': str(reading.get('client', 'N/A')),
            'actual_read_time': _format_read_time(reading.get('actual_read_time', 'N/A')),
            'show': str(reading.get('show', 'N/A')),
            'presenter': str(reading.get('presenter', reading.get('read_by', 'N/A'))),
            'duration': _format_duration(reading.get('duration', 'N/A')),
            'status': str(reading.get('status', 'N/A'))
        }

        # Calculate optimal row height based on mention content - ensure space for 5 lines
        content_width_mm = columns[1]['width'] - 4  # Account for padding
        content_lines = _wrap_text_for_cell(mention_content, content_width_mm, 8)

        # Row height: optimized for 5 lines with reduced spacing
        line_height = 3.2  # Reduced from 4mm to 3.2mm per line
        min_height_for_5_lines = (5 * line_height) + 4  # 5 lines + reduced padding = 20mm
        base_height = 16  # Reduced base height from 20mm to 16mm
        content_height = max(len(content_lines) * line_height, min_height_for_5_lines)
        row_height = max(base_height, min(content_height, 35))  # Reduced cap from 50mm to 35mm

        # Alternate row colors
        if i % 2 == 0:
            pdf.set_fill_color(248, 249, 250)  # Light gray
        else:
            pdf.set_fill_color(255, 255, 255)  # White

        # Store current position
        start_y = pdf.get_y()
        current_x = 10

        # Draw row background first
        for j, col in enumerate(columns):
            pdf.rect(current_x, start_y, col['width'], row_height, 'F')  # Fill only
            current_x += col['width']

        # Reset position and draw cell borders and content
        current_x = 10
        for j, col in enumerate(columns):
            # Draw cell border
            pdf.rect(current_x, start_y, col['width'], row_height, 'D')  # Draw border only

            if col['key'] == 'mention_content':
                # Use multiline text for mention content (5 lines)
                _draw_multiline_text_in_cell(pdf, mention_content, current_x, start_y, col['width'], row_height, 8)
            elif col['key'] == 'client':
                # Use multiline text for client names to allow wrapping
                client_name = str(row_data.get(col['key'], 'N/A'))
                _draw_multiline_text_in_cell_limited(pdf, client_name, current_x, start_y, col['width'], row_height, 8, max_lines=3)
            else:
                # Use single-line text for other columns
                value = str(row_data.get(col['key'], 'N/A'))
                _draw_text_in_cell(pdf, value, current_x, start_y, col['width'], row_height, 8)

            current_x += col['width']

        # Move to next row
        pdf.set_xy(10, start_y + row_height)

        # Check if we need a new page - dynamic based on page orientation
        page_height = pdf.h  # Get actual page height (210mm portrait, 297mm landscape)
        bottom_margin = 30   # Leave space for footer
        max_y = page_height - bottom_margin

        if pdf.get_y() > max_y:  # Near bottom of page
            pdf.add_page()

            # Position cursor for next data row (no table header on subsequent pages)
            pdf.set_xy(10, pdf.get_y())


def _print_multiline_cell(pdf, text, width, height, border=True):
    """Print a cell with multi-line text content"""
    # Store current position
    x = pdf.get_x()
    y = pdf.get_y()

    # Draw cell border if requested
    if border:
        pdf.rect(x, y, width, height)

    # Set margins for text within cell
    margin = 1
    text_width = width - (2 * margin)

    # Wrap text to fit in cell
    lines = _wrap_text_for_cell(text, text_width, 8)

    # Print each line
    line_height = 3
    current_y = y + margin

    for line in lines:
        if current_y + line_height <= y + height - margin:  # Check if line fits
            pdf.set_xy(x + margin, current_y)
            pdf.cell(text_width, line_height, line, 0, 0, 'L')
            current_y += line_height
        else:
            # Add ellipsis if text doesn't fit
            pdf.set_xy(x + margin, current_y)
            pdf.cell(text_width, line_height, '...', 0, 0, 'L')
            break

    # Move cursor to end of cell
    pdf.set_xy(x + width, y)


def _print_enhanced_multiline_cell(pdf, text, width, height, x, y, fill=True):
    """Print an enhanced multi-line cell with better text wrapping and spacing"""
    # Draw cell background and border
    if fill:
        pdf.rect(x, y, width, height, 'DF')  # Draw and fill
    else:
        pdf.rect(x, y, width, height, 'D')   # Draw only

    # Set margins for better text spacing
    margin = 2
    text_width = width - (2 * margin)
    text_height = height - (2 * margin)

    # Wrap text to fit in cell with improved algorithm
    lines = _wrap_text_for_cell(text, text_width, 8)

    # Calculate optimal line spacing
    line_height = 3.5
    max_lines = int(text_height / line_height)

    # Limit lines to what fits in the cell
    display_lines = lines[:max_lines]

    # If text is truncated, replace last line with ellipsis
    if len(lines) > max_lines and max_lines > 0:
        if max_lines > 1:
            display_lines[-1] = display_lines[-1][:30] + '...'
        else:
            display_lines = ['...']

    # Center text vertically in cell
    total_text_height = len(display_lines) * line_height
    start_y = y + (height - total_text_height) / 2

    # Print each line
    current_y = start_y
    for line in display_lines:
        if line.strip():  # Only print non-empty lines
            pdf.set_xy(x + margin, current_y)
            pdf.cell(text_width, line_height, line.strip(), 0, 0, 'L')
        current_y += line_height


def _print_centered_cell(pdf, text, width, height, x, y, fill=True):
    """Print a single-line cell with vertically centered text"""
    # Draw cell background and border
    if fill:
        pdf.rect(x, y, width, height, 'DF')  # Draw and fill
    else:
        pdf.rect(x, y, width, height, 'D')   # Draw only

    # Set margins
    margin = 2
    text_width = width - (2 * margin)

    # Truncate text if too long
    max_chars = int(text_width * 2.5)  # Approximate characters that fit
    display_text = str(text)
    if len(display_text) > max_chars:
        display_text = display_text[:max_chars-3] + '...'

    # Center text vertically
    text_y = y + (height - 3.5) / 2  # 3.5 is approximate text height

    # Print text
    pdf.set_xy(x + margin, text_y)
    pdf.cell(text_width, 3.5, display_text, 0, 0, 'L')


def _wrap_text_for_cell(text, width_mm, font_size=8):
    """
    Enhanced text wrapping for mention content with better word breaking
    Returns list of lines that fit within the specified width
    """
    # Conservative character calculation to prevent column overflow
    if font_size <= 8:
        chars_per_mm = 2.2  # More conservative for small fonts to prevent overflow
    elif font_size <= 10:
        chars_per_mm = 1.9
    else:
        chars_per_mm = 1.7

    chars_per_line = int(width_mm * chars_per_mm)

    if chars_per_line < 15:  # Minimum reasonable line length
        chars_per_line = 15

    text = str(text).strip() if text else ""
    if not text:
        return [""]

    # Clean up text - remove extra whitespace and normalize
    text = ' '.join(text.split())

    words = text.split()
    lines = []
    current_line = ""

    for word in words:
        # Check if adding this word would exceed the line length
        test_line = current_line + (" " if current_line else "") + word

        if len(test_line) <= chars_per_line:
            current_line = test_line
        else:
            # If current line has content, save it and start new line
            if current_line:
                lines.append(current_line)

            # Handle very long words that don't fit on a single line
            if len(word) > chars_per_line:
                # Break the word across multiple lines at reasonable points
                remaining_word = word
                while len(remaining_word) > chars_per_line:
                    # Try to break at natural points (hyphens, underscores, etc.)
                    break_point = chars_per_line - 1
                    for i in range(max(0, chars_per_line - 5), chars_per_line):
                        if i < len(remaining_word) and remaining_word[i] in '-_./':
                            break_point = i + 1
                            break

                    lines.append(remaining_word[:break_point] + ("-" if break_point < len(remaining_word) and remaining_word[break_point-1] not in '-_' else ""))
                    remaining_word = remaining_word[break_point:]

                current_line = remaining_word
            else:
                current_line = word

    # Add the last line if it has content
    if current_line:
        lines.append(current_line)

    return lines if lines else [""]


def _format_read_time(read_time_str):
    """Format read time to match dynamic reports format: 'Jan 25, 2025 02:30 PM'"""
    if not read_time_str or read_time_str == 'N/A':
        return 'N/A'

    try:
        # Try to parse the datetime string and reformat it
        from datetime import datetime
        if isinstance(read_time_str, str):
            # Handle different possible formats
            if ' ' in read_time_str and ':' in read_time_str:
                # Assume it's already in a datetime format
                try:
                    dt = datetime.strptime(read_time_str, '%Y-%m-%d %H:%M')
                    return dt.strftime('%b %d, %Y %I:%M %p')
                except ValueError:
                    pass
        return str(read_time_str)
    except:
        return str(read_time_str)


def _format_duration(duration_str):
    """Format duration to match dynamic reports format"""
    if not duration_str or duration_str == 'N/A':
        return 'N/A'

    # If it's already formatted (e.g., "30s"), return as is
    if isinstance(duration_str, str) and duration_str.endswith('s'):
        return duration_str

    # If it's a number, add 's' suffix
    try:
        duration_num = int(str(duration_str).replace('s', ''))
        return f'{duration_num}s'
    except:
        return str(duration_str)


def _generate_direct_analytics_report(pdf, data):
    """Generate analytics report"""
    # No title here - it's now in the header
    pdf.set_text_color(0, 0, 0)
    pdf.ln(5)

    # Key metrics
    if data.get('key_metrics'):
        metrics = data['key_metrics']

        pdf.add_section_header('Key Performance Indicators', level=1)
        current_y = pdf.get_y()

        kpi_data = {
            'Total Mentions': metrics.get('total_mentions', 0),
            'Completed': metrics.get('completed_readings', 0),
            'Success Rate': metrics.get('completion_rate', '0%'),
            'Active Clients': metrics.get('active_clients', 0)
        }

        for i, (key, value) in enumerate(kpi_data.items()):
            x = 10 + i * 47.5
            pdf.add_metric_card(key, value, x, current_y, 45, 25)

        pdf.set_y(current_y + 35)

    # Trends section
    if data.get('trends'):
        pdf.add_section_header('Performance Trends', level=1)
        pdf.set_font('Arial', size=pdf.body_font_size)

        for trend in data['trends']:
            pdf.cell(0, 6, f"- {trend}", 0, 1)


def _generate_direct_daily_summary(pdf, data):
    """Generate daily summary report"""
    pdf.set_font('Arial', 'B', pdf.title_font_size)
    pdf.set_text_color(*pdf.primary_color)
    report_date = data.get('report_date', datetime.now().strftime('%Y-%m-%d'))
    pdf.cell(0, 15, f'Daily Summary - {report_date}', 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)
    pdf.ln(10)

    # Daily metrics
    if data.get('daily_stats'):
        stats = data['daily_stats']

        pdf.add_section_header('Daily Performance', level=1)
        current_y = pdf.get_y()

        daily_metrics = {
            'Created': stats.get('mentions_created', 0),
            'Approved': stats.get('mentions_approved', 0),
            'Completed': stats.get('readings_completed', 0),
            'Success Rate': f"{stats.get('success_rate', 0)}%"
        }

        for i, (key, value) in enumerate(daily_metrics.items()):
            x = 10 + i * 47.5
            pdf.add_metric_card(key, value, x, current_y, 45, 25)

        pdf.set_y(current_y + 35)


def _generate_direct_weekly_trends(pdf, data):
    """Generate weekly trends report"""
    pdf.set_font('Arial', 'B', pdf.title_font_size)
    pdf.set_text_color(*pdf.primary_color)
    pdf.cell(0, 15, 'Weekly Trends Report', 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)
    pdf.ln(10)

    # Week summary
    if data.get('week_summary'):
        summary = data['week_summary']

        pdf.add_section_header('Week Overview', level=1)
        current_y = pdf.get_y()

        weekly_metrics = {
            'Total Mentions': summary.get('total_mentions', 0),
            'Completed': summary.get('completed_readings', 0),
            'Success Rate': summary.get('completion_rate', '0%'),
            'Avg Daily': summary.get('avg_daily', 0)
        }

        for i, (key, value) in enumerate(weekly_metrics.items()):
            x = 10 + i * 47.5
            pdf.add_metric_card(key, value, x, current_y, 45, 25)

        pdf.set_y(current_y + 35)


def _generate_direct_monthly_overview(pdf, data):
    """Generate monthly overview report"""
    pdf.set_font('Arial', 'B', pdf.title_font_size)
    pdf.set_text_color(*pdf.primary_color)
    month_name = data.get('month_name', 'Month')
    year = data.get('year', datetime.now().year)
    pdf.cell(0, 15, f'{month_name} {year} - Monthly Overview', 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)
    pdf.ln(10)

    # Monthly statistics
    if data.get('monthly_stats'):
        stats = data['monthly_stats']

        pdf.add_section_header('Monthly Performance', level=1)
        current_y = pdf.get_y()

        monthly_metrics = {
            'Total Mentions': stats.get('total_mentions', 0),
            'Completed': stats.get('completed_readings', 0),
            'Success Rate': stats.get('completion_rate', '0%'),
            'Growth': f"{stats.get('growth_rate', 0)}%"
        }

        for i, (key, value) in enumerate(monthly_metrics.items()):
            x = 10 + i * 47.5
            pdf.add_metric_card(key, value, x, current_y, 45, 25)

        pdf.set_y(current_y + 35)


def _generate_direct_generic_report(pdf, data, report_type):
    """Generate intelligent generic report for any report type"""
    pdf.set_font('Arial', 'B', pdf.title_font_size)
    pdf.set_text_color(*pdf.primary_color)
    title = report_type.replace('-', ' ').title() + ' Report'
    pdf.cell(0, 15, title, 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)
    pdf.ln(10)

    # Intelligently process data structure
    _process_generic_data(pdf, data, report_type)


def _process_generic_data(pdf, data, report_type):
    """Intelligently process any data structure for PDF generation"""
    if not data:
        pdf.add_section_header('No Data Available', level=1)
        pdf.set_font('Arial', size=pdf.body_font_size)
        pdf.cell(0, 10, 'No data available for this report.', 0, 1)
        return

    # Process different data structures
    for key, value in data.items():
        if key in ['error']:
            continue  # Skip error keys

        section_title = key.replace('_', ' ').title()

        if isinstance(value, dict):
            # Handle dictionary data (like stats, metrics)
            _process_dict_section(pdf, section_title, value)
        elif isinstance(value, list):
            # Handle list data (like mentions, clients, shows)
            _process_list_section(pdf, section_title, value, report_type)
        elif isinstance(value, (str, int, float)):
            # Handle simple values
            if not hasattr(pdf, '_simple_values'):
                pdf._simple_values = []
            pdf._simple_values.append((section_title, value))

    # Display simple values if any were collected
    if hasattr(pdf, '_simple_values') and pdf._simple_values:
        pdf.add_section_header('Summary Information', level=1)
        for title, value in pdf._simple_values:
            pdf.set_font('Arial', size=pdf.body_font_size)
            pdf.cell(0, 6, f"{title}: {value}", 0, 1)
        pdf.ln(5)


def _process_dict_section(pdf, title, data_dict):
    """Process dictionary data as metrics or stats"""
    pdf.add_section_header(title, level=1)

    # Check if this looks like metrics (numeric values)
    numeric_items = [(k, v) for k, v in data_dict.items() if isinstance(v, (int, float)) or (isinstance(v, str) and any(char.isdigit() for char in str(v)))]

    if len(numeric_items) >= 2 and len(numeric_items) <= 6:
        # Display as metric cards
        current_y = pdf.get_y()
        cards_per_row = min(4, len(numeric_items))
        card_width = (pdf.w - 20) / cards_per_row - 5

        for i, (key, value) in enumerate(numeric_items):
            x = 10 + (i % cards_per_row) * (card_width + 5)
            if i > 0 and i % cards_per_row == 0:
                current_y += 30
            pdf.add_metric_card(key.replace('_', ' ').title(), str(value), x, current_y, card_width, 25)

        pdf.set_y(current_y + 35)
    else:
        # Display as simple list
        pdf.set_font('Arial', size=pdf.body_font_size)
        for key, value in data_dict.items():
            pdf.cell(0, 6, f"{key.replace('_', ' ').title()}: {value}", 0, 1)
        pdf.ln(5)


def _process_list_section(pdf, title, data_list, report_type):
    """Process list data as tables or summaries"""
    if not data_list:
        return

    pdf.add_section_header(title, level=1)

    # Check if list contains dictionaries (table data)
    if isinstance(data_list[0], dict):
        _create_generic_table(pdf, data_list, title, report_type)
    else:
        # Simple list display
        pdf.set_font('Arial', size=pdf.body_font_size)
        for item in data_list[:10]:  # Limit to first 10 items
            pdf.cell(0, 6, f"• {str(item)}", 0, 1)
        if len(data_list) > 10:
            pdf.cell(0, 6, f"... and {len(data_list) - 10} more items", 0, 1)
        pdf.ln(5)


def _create_generic_table(pdf, data_list, title, report_type):
    """Create a generic table from list of dictionaries"""
    if not data_list:
        return

    # Get all unique keys from the data
    all_keys = set()
    for item in data_list:
        if isinstance(item, dict):
            all_keys.update(item.keys())

    # Filter out common non-display keys
    excluded_keys = {'id', 'created_at', 'updated_at', 'password', 'token'}
    display_keys = [key for key in all_keys if key not in excluded_keys]

    # Limit columns for readability
    if len(display_keys) > 6:
        # Prioritize important columns based on report type and common patterns
        priority_keys = []
        for key in display_keys:
            if any(important in key.lower() for important in ['name', 'title', 'count', 'total', 'status', 'date']):
                priority_keys.append(key)

        display_keys = priority_keys[:6] if priority_keys else display_keys[:6]

    # Calculate column widths
    available_width = pdf.w - 20
    col_width = available_width / len(display_keys)

    # Create table configuration
    columns = []
    for key in display_keys:
        columns.append({
            'header': key.replace('_', ' ').title(),
            'field': key,
            'width': col_width,
            'align': 'L'
        })

    # Create the table
    config = {
        'columns': columns,
        'header_font_size': 9,
        'row_font_size': 8
    }

    # Limit rows for PDF readability
    display_data = data_list[:20]  # Show first 20 rows
    _create_enhanced_table(pdf, display_data, config)

    if len(data_list) > 20:
        pdf.ln(5)
        pdf.set_font('Arial', 'I', 8)
        pdf.cell(0, 5, f"Showing first 20 of {len(data_list)} total records", 0, 1)

    pdf.ln(10)


# =============================================================================
# STANDALONE HELPER FUNCTIONS FOR DIRECT PDF GENERATION
# =============================================================================

def _create_enhanced_table(pdf, data, config):
    """Create an enhanced table with professional styling (standalone function)"""
    if not data or not config.get('columns'):
        return

    columns = config['columns']

    # Table header with enhanced styling
    pdf.set_fill_color(*pdf.primary_color)
    pdf.set_text_color(255, 255, 255)
    pdf.set_font('Arial', 'B', config.get('header_font_size', 10))

    for col in columns:
        pdf.cell(col['width'], 10, col['header'], 1, 0, col.get('align', 'L'), True)
    pdf.ln()

    # Table rows with alternating colors
    pdf.set_text_color(0, 0, 0)
    pdf.set_font('Arial', size=config.get('row_font_size', 9))

    for i, row in enumerate(data):
        # Alternating row colors
        if i % 2 == 0:
            pdf.set_fill_color(255, 255, 255)  # White
        else:
            pdf.set_fill_color(*pdf.light_gray)  # Light gray

        for col in columns:
            value = row.get(col['field'], '')

            # Format value if needed
            if col.get('format') == 'currency':
                try:
                    value = f"${float(value):.2f}" if value else '$0.00'
                except (ValueError, TypeError):
                    value = '$0.00'
            elif col.get('format') == 'percentage':
                try:
                    value = f"{float(value):.1f}%" if value else '0.0%'
                except (ValueError, TypeError):
                    value = '0.0%'

            pdf.cell(col['width'], 8, str(value), 1, 0, col.get('align', 'L'), True)
        pdf.ln()

    # Reset colors
    pdf.set_fill_color(255, 255, 255)
    pdf.set_text_color(0, 0, 0)


def _create_enhanced_summary_section(pdf, stats):
    """Create enhanced summary statistics with metric cards (standalone function)"""
    if not stats:
        return

    pdf.add_section_header('Summary Statistics', level=1)

    # Create metric cards layout (4 cards per row)
    current_y = pdf.get_y()
    card_width = 45
    card_height = 25
    margin = 5
    start_x = 10

    stats_items = list(stats.items())
    cards_per_row = 4

    for i, (key, value) in enumerate(stats_items):
        row = i // cards_per_row
        col = i % cards_per_row

        x = start_x + col * (card_width + margin)
        y = current_y + row * (card_height + margin)

        # Format the title
        title = key.replace('_', ' ').title()
        if len(title) > 12:
            title = title[:12] + '...'

        pdf.add_metric_card(title, value, x, y, card_width, card_height)

    # Move cursor below the cards
    rows_needed = (len(stats_items) + cards_per_row - 1) // cards_per_row
    pdf.set_y(current_y + rows_needed * (card_height + margin) + 10)


def create_pdf_response(pdf, filename):
    """Create HTTP response for PDF download using the same pattern as working dynamic reports"""
    try:
        # Use the exact same pattern as the working dynamic reports
        pdf_output = pdf.output(dest='S')
        pdf_bytes = pdf_output.encode('latin-1') if isinstance(pdf_output, str) else bytes(pdf_output)

        response = HttpResponse(pdf_bytes, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="{filename}"'
        response['Content-Length'] = len(pdf_bytes)
        return response
    except Exception as e:
        # Return error response if PDF generation fails
        from django.http import JsonResponse
        import traceback
        return JsonResponse({
            'error': f'PDF generation failed: {str(e)}',
            'type': 'pdf_generation_error',
            'details': str(e.__class__.__name__),
            'traceback': traceback.format_exc()
        }, status=500)


# =============================================================================
# NEWS READER SPECIFIC PDF GENERATORS
# =============================================================================

def _generate_news_reader_schedule_pdf(pdf, data):
    """Generate News Reader Schedule PDF Report"""

    # Title
    pdf.set_font('Arial', 'B', pdf.title_font_size)
    pdf.set_text_color(*pdf.primary_color)
    pdf.cell(0, 15, 'Reading Schedule Report', 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)
    pdf.ln(5)

    # User and date info
    pdf.set_font('Arial', '', pdf.body_font_size)
    user = data.get('user')
    if user:
        pdf.cell(0, 8, f"News Reader: {user.get_full_name() or user.username}", 0, 1)
    pdf.cell(0, 8, f"Report Date: {data.get('report_date', 'N/A')}", 0, 1)
    pdf.ln(5)

    # Summary metrics
    pdf.add_section_header('Summary', level=1)
    current_y = pdf.get_y()

    metrics = {
        'Total Assignments': str(data.get('total_assignments', 0)),
        'Completed': str(data.get('completed_assignments', 0)),
        'Overdue': str(data.get('overdue_assignments', 0)),
        'Pending': str(data.get('total_assignments', 0) - data.get('completed_assignments', 0) - data.get('overdue_assignments', 0))
    }

    for i, (key, value) in enumerate(metrics.items()):
        x = 10 + i * 47.5
        pdf.add_metric_card(key, value, x, current_y, 45, 25)

    pdf.set_y(current_y + 30)

    # Assignments table
    assignments = data.get('assignments', [])
    if assignments:
        pdf.add_section_header('Reading Assignments', level=1)

        # Table configuration
        table_config = {
            'columns': [
                {'header': 'Mention', 'width': 50, 'align': 'L'},
                {'header': 'Client', 'width': 35, 'align': 'L'},
                {'header': 'Show', 'width': 30, 'align': 'L'},
                {'header': 'Scheduled', 'width': 30, 'align': 'C'},
                {'header': 'Due Date', 'width': 30, 'align': 'C'},
                {'header': 'Status', 'width': 20, 'align': 'C'},
            ],
            'header_font_size': 9,
            'body_font_size': 8
        }

        # Create table data
        table_data = []
        for assignment in assignments:
            scheduled_str = f"{assignment['scheduled_date'].strftime('%m/%d')} {assignment['scheduled_time'].strftime('%H:%M')}"
            due_str = assignment['due_date'].strftime('%m/%d %H:%M')

            table_data.append([
                assignment['mention_title'][:25] + ('...' if len(assignment['mention_title']) > 25 else ''),
                assignment['client_name'][:15] + ('...' if len(assignment['client_name']) > 15 else ''),
                assignment['show_name'][:12] + ('...' if len(assignment['show_name']) > 12 else ''),
                scheduled_str,
                due_str,
                assignment['status'][:8]
            ])

        _create_enhanced_table(pdf, table_data, table_config)

        # Assignment details section
        pdf.ln(10)
        pdf.add_section_header('Assignment Details', level=1)

        for assignment in assignments[:10]:  # Limit to first 10 for space
            pdf.set_font('Arial', 'B', 9)
            pdf.cell(0, 6, f"• {assignment['mention_title']}", 0, 1)

            pdf.set_font('Arial', '', 8)
            pdf.cell(10, 5, '', 0, 0)  # Indent
            pdf.cell(0, 5, f"Client: {assignment['client_name']} | Show: {assignment['show_name']}", 0, 1)

            if assignment.get('assignment_notes'):
                pdf.cell(10, 5, '', 0, 0)  # Indent
                notes = assignment['assignment_notes'][:100] + ('...' if len(assignment['assignment_notes']) > 100 else '')
                pdf.cell(0, 5, f"Notes: {notes}", 0, 1)

            pdf.ln(2)
    else:
        pdf.add_section_header('No Assignments', level=1)
        pdf.set_font('Arial', '', pdf.body_font_size)
        pdf.cell(0, 10, 'No reading assignments found for this period.', 0, 1)


def _generate_news_reader_performance_pdf(pdf, data):
    """Generate News Reader Performance PDF Report"""

    # Title
    pdf.set_font('Arial', 'B', pdf.title_font_size)
    pdf.set_text_color(*pdf.primary_color)
    pdf.cell(0, 15, 'Reading Performance Report', 0, 1, 'C')
    pdf.set_text_color(0, 0, 0)
    pdf.ln(5)

    # User and date range info
    pdf.set_font('Arial', '', pdf.body_font_size)
    user = data.get('user')
    if user:
        pdf.cell(0, 8, f"News Reader: {user.get_full_name() or user.username}", 0, 1)
    pdf.cell(0, 8, f"Period: {data.get('start_date', 'N/A')} to {data.get('end_date', 'N/A')}", 0, 1)
    pdf.ln(5)

    # Performance metrics
    pdf.add_section_header('Performance Summary', level=1)
    current_y = pdf.get_y()

    performance_data = data.get('performance_data', {})
    metrics = {
        'Total Sessions': str(performance_data.get('total_sessions', 0)),
        'Prep Time (hrs)': f"{performance_data.get('total_prep_time', 0):.1f}",
        'Notes Created': str(performance_data.get('total_notes', 0)),
        'Completed': str(performance_data.get('completed_notes', 0))
    }

    for i, (key, value) in enumerate(metrics.items()):
        x = 10 + i * 47.5
        pdf.add_metric_card(key, value, x, current_y, 45, 25)

    pdf.set_y(current_y + 30)

    # Recent sessions
    sessions = data.get('sessions', [])
    if sessions:
        pdf.add_section_header('Recent Sessions', level=1)

        table_config = {
            'columns': [
                {'header': 'Date', 'width': 30, 'align': 'L'},
                {'header': 'Type', 'width': 40, 'align': 'L'},
                {'header': 'Duration', 'width': 30, 'align': 'C'},
                {'header': 'Prepared', 'width': 25, 'align': 'C'},
                {'header': 'Read', 'width': 25, 'align': 'C'},
                {'header': 'Status', 'width': 25, 'align': 'C'},
            ],
            'header_font_size': 9,
            'body_font_size': 8
        }

        table_data = []
        for session in sessions:
            duration = session.duration.total_seconds() / 60 if hasattr(session, 'duration') and session.duration else 0
            table_data.append([
                session.start_time.strftime('%m/%d/%Y'),
                session.get_session_type_display(),
                f"{duration:.0f}m",
                str(session.mentions_prepared),
                str(session.mentions_read),
                'Active' if session.is_active else 'Complete'
            ])

        _create_enhanced_table(pdf, table_data, table_config)

    # Recent notes
    notes = data.get('recent_notes', [])
    if notes:
        pdf.ln(10)
        pdf.add_section_header('Recent Notes', level=1)

        for note in notes[:8]:  # Limit to 8 notes
            pdf.set_font('Arial', 'B', 9)
            title = note.title or f"Note for {note.mention.title}"
            pdf.cell(0, 6, f"• {title[:50]}{'...' if len(title) > 50 else ''}", 0, 1)

            pdf.set_font('Arial', '', 8)
            pdf.cell(10, 5, '', 0, 0)  # Indent
            pdf.cell(0, 5, f"Status: {note.get_preparation_status_display()} | Created: {note.created_at.strftime('%m/%d/%Y')}", 0, 1)
            pdf.ln(2)


def _generate_client_order_schedule_pdf(pdf, data):
    """Generate client order schedule PDF with simple table layout"""
    from datetime import datetime, timedelta

    # Client and period information
    client_name = data.get('client_name', 'Unknown Client')
    start_date_str = data.get('start_date', '')
    end_date_str = data.get('end_date', '')

    # Parse dates safely
    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    except:
        start_date = datetime.now().date()
        end_date = start_date + timedelta(days=30)

    # Summary statistics
    summary_stats = data.get('summary_stats', {})
    total_scheduled = summary_stats.get('total_scheduled', 0)

    # Compact header section
    # pdf.set_font('Arial', 'B', 14)
    # pdf.cell(0, 8, 'Client Order Schedule Report', 0, 1, 'C')
    # pdf.ln(2)

    # Compact client information section
    pdf.set_font('Arial', 'B', 11)
    pdf.cell(0, 6, f'Client: {client_name}', 0, 1)
    pdf.set_font('Arial', '', 9)
    pdf.cell(0, 5, f'Period: {start_date.strftime("%Y-%m-%d")} to {end_date.strftime("%Y-%m-%d")}', 0, 1)
    pdf.cell(0, 5, f'Total Scheduled Mentions: {total_scheduled}', 0, 1)
    pdf.ln(4)

    # Process schedule data with calendar-style layout (matching web page)
    schedule_data = data.get('schedule_data', [])

    if schedule_data:
        # Group data by date and time for calendar-style display (like web page)
        calendar_data = {}
        time_slots = set()

        for item in schedule_data:
            date_str = item.get('date', '')
            time_str = item.get('time', '')

            if date_str and time_str:
                if date_str not in calendar_data:
                    calendar_data[date_str] = {}
                if time_str not in calendar_data[date_str]:
                    calendar_data[date_str][time_str] = []

                calendar_data[date_str][time_str].append(item)
                time_slots.add(time_str)

        # Sort time slots and dates
        sorted_times = sorted(list(time_slots))
        sorted_dates = sorted(calendar_data.keys())

        if sorted_dates and sorted_times:
            # Create calendar-style table header
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(0, 6, 'Detailed Schedule:', 0, 1)
            pdf.ln(1)

            # Calculate column widths for calendar layout based on orientation
            orientation = getattr(pdf, 'pdf_options', {}).get('orientation', 'portrait')

            # Available width calculation (A4 page minus margins)
            if orientation == 'landscape':
                page_width = 277  # A4 landscape: 297mm - 20mm margins
            else:
                page_width = 170  # A4 portrait: 210mm - 40mm margins (more conservative)

            # Column width calculations
            date_col_width = 22  # Date column (slightly smaller)
            total_col_width = 18  # Daily total column (smaller)

            # Calculate time slot column width
            available_for_time_slots = page_width - date_col_width - total_col_width
            if sorted_times:
                time_col_width = max(12, min(20, available_for_time_slots / len(sorted_times)))
            else:
                time_col_width = 15

            # Ensure total width doesn't exceed page width
            total_calculated_width = date_col_width + (time_col_width * len(sorted_times)) + total_col_width
            if total_calculated_width > page_width:
                # Reduce time column width to fit
                time_col_width = (page_width - date_col_width - total_col_width) / len(sorted_times)
                time_col_width = max(10, time_col_width)  # Minimum 10mm per column

            # Header row
            pdf.set_font('Arial', 'B', 8)
            pdf.set_fill_color(200, 200, 200)
            pdf.cell(date_col_width, 8, 'Date', 1, 0, 'C', True)

            # Time slot headers
            for time_str in sorted_times:
                time_display = time_str[:5] if len(time_str) > 5 else time_str  # Show HH:MM only
                pdf.cell(time_col_width, 8, time_display, 1, 0, 'C', True)

            pdf.cell(total_col_width, 8, 'Daily Total', 1, 1, 'C', True)

            # Data rows for each date
            pdf.set_fill_color(255, 255, 255)
            pdf.set_font('Arial', '', 7)

            for date_str in sorted_dates:
                # Format date for display (like web page: "Mon 6/25")
                try:
                    from datetime import datetime
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                    date_display = date_obj.strftime('%a %m/%d')
                except:
                    date_display = date_str[-5:]  # Fallback to last 5 chars

                # Use fixed row height since we're only showing numbers
                row_height = 8

                # Date column
                pdf.cell(date_col_width, row_height, date_display, 1, 0, 'C')

                # Time slot columns - show number of slots instead of details
                for time_slot in sorted_times:
                    slot_count = 0
                    if date_str in calendar_data and time_slot in calendar_data[date_str]:
                        slot_count = len(calendar_data[date_str][time_slot])

                    # Display slot count or empty cell
                    cell_content = str(slot_count) if slot_count > 0 else ""

                    # Simple cell with centered number
                    pdf.cell(time_col_width, row_height, cell_content, 1, 0, 'C')

                # Daily total column
                daily_total = sum(len(calendar_data[date_str].get(ts, [])) for ts in sorted_times if date_str in calendar_data)
                pdf.cell(total_col_width, row_height, str(daily_total), 1, 1, 'C')

            # Total row (footer)
            pdf.set_font('Arial', 'B', 8)
            pdf.set_fill_color(240, 240, 240)
            pdf.cell(date_col_width, 8, 'Total', 1, 0, 'C', True)

            # Calculate totals for each time slot
            for time_slot in sorted_times:
                time_total = sum(len(calendar_data[date_str].get(time_slot, []))
                               for date_str in sorted_dates if date_str in calendar_data)
                pdf.cell(time_col_width, 8, str(time_total), 1, 0, 'C', True)

            # Grand total
            grand_total = sum(len(items) for date_data in calendar_data.values()
                            for items in date_data.values())
            pdf.cell(total_col_width, 8, str(grand_total), 1, 1, 'C', True)

        else:
            pdf.set_font('Arial', '', 12)
            pdf.cell(0, 10, 'No scheduled mentions found for this period.', 0, 1)
    else:
        pdf.set_font('Arial', '', 12)
        pdf.cell(0, 10, 'No scheduled mentions found for this period.', 0, 1)

    # Summary section at the end
    pdf.ln(10)
    pdf.set_font('Arial', 'B', 12)
    pdf.cell(0, 8, f'Total Scheduled Mentions: {total_scheduled}', 0, 1)


def _draw_text_in_cell(pdf, text, x, y, width, height, font_size=8):
    """Draw text in a cell with proper clipping and wrapping"""
    pdf.set_font('Arial', '', font_size)

    # Calculate text that fits in the cell width
    text = str(text) if text else ""

    # Use FPDF's get_string_width to calculate actual text width
    max_width = width - 4  # Account for padding

    # If text is too wide, truncate it
    if pdf.get_string_width(text) > max_width:
        # Binary search to find the maximum text that fits
        left, right = 0, len(text)
        while left < right:
            mid = (left + right + 1) // 2
            test_text = text[:mid] + "..." if mid < len(text) else text[:mid]
            if pdf.get_string_width(test_text) <= max_width:
                left = mid
            else:
                right = mid - 1
        text = text[:left] + "..." if left < len(text) else text[:left]

    # Center text vertically in cell
    text_y = y + (height - font_size * 0.35) / 2  # Approximate vertical centering
    pdf.set_xy(x + 2, text_y)  # 2mm left padding
    pdf.cell(max_width, font_size * 0.35, text, 0, 0, 'L')


def _draw_multiline_text_in_cell(pdf, text, x, y, width, height, font_size=8):
    """Draw multi-line text in a cell with proper clipping - 5 lines for mention content"""
    pdf.set_font('Arial', '', font_size)

    text = str(text) if text else ""
    if not text:
        return

    # Calculate available space with reduced padding
    padding = 1  # Reduced from 2mm to 1mm
    available_width = width - (2 * padding)
    available_height = height - (2 * padding)
    line_height = font_size * 0.4  # Reduced line spacing from 0.5 to 0.4

    # Force 5 lines for mention content instead of calculating from height
    max_lines = 5  # Changed from dynamic calculation to fixed 5 lines

    if max_lines < 1:
        return

    # Split text into words and create lines that fit
    words = text.split()
    lines = []
    current_line = ""

    for word in words:
        test_line = current_line + (" " if current_line else "") + word
        if pdf.get_string_width(test_line) <= available_width:
            current_line = test_line
        else:
            if current_line:
                lines.append(current_line)
                current_line = word
            else:
                # Single word is too long, truncate it
                while pdf.get_string_width(word + "...") > available_width and len(word) > 3:
                    word = word[:-1]
                lines.append(word + "...")
                current_line = ""

            if len(lines) >= max_lines:
                break

    # Add remaining text
    if current_line and len(lines) < max_lines:
        lines.append(current_line)

    # Truncate to max lines
    lines = lines[:max_lines]

    # If we had to truncate, add ellipsis to last line
    if len(lines) == max_lines and (current_line or len(words) > sum(len(line.split()) for line in lines)):
        if lines:
            last_line = lines[-1]
            ellipsis_width = pdf.get_string_width("...")
            while pdf.get_string_width(last_line + "...") > available_width and len(last_line) > 0:
                last_line = last_line[:-1]
            lines[-1] = last_line + "..."

    # Draw the lines
    current_y = y + padding
    for line in lines:
        if current_y + line_height <= y + height - padding:
            pdf.set_xy(x + padding, current_y)
            pdf.cell(available_width, line_height, line, 0, 0, 'L')
            current_y += line_height
        else:
            break


def _draw_multiline_text_in_cell_limited(pdf, text, x, y, width, height, font_size=8, max_lines=3):
    """Draw multi-line text in a cell with a specific line limit (for client names, etc.)"""
    pdf.set_font('Arial', '', font_size)

    text = str(text) if text else ""
    if not text:
        return

    # Calculate available space with reduced padding
    padding = 1
    available_width = width - (2 * padding)
    available_height = height - (2 * padding)
    line_height = font_size * 0.4  # Reduced line spacing

    # Use the specified max_lines limit
    if max_lines < 1:
        return

    # Split text into words and create lines that fit
    words = text.split()
    lines = []
    current_line = ""

    for word in words:
        test_line = current_line + (" " if current_line else "") + word
        if pdf.get_string_width(test_line) <= available_width:
            current_line = test_line
        else:
            if current_line:
                lines.append(current_line)
                current_line = word
            else:
                # Single word is too long, truncate it
                while pdf.get_string_width(word + "...") > available_width and len(word) > 3:
                    word = word[:-1]
                lines.append(word + "...")
                current_line = ""

            if len(lines) >= max_lines:
                break

    # Add remaining text
    if current_line and len(lines) < max_lines:
        lines.append(current_line)

    # Truncate to max lines
    lines = lines[:max_lines]

    # If we had to truncate, add ellipsis to last line
    if len(lines) == max_lines and (current_line or len(words) > sum(len(line.split()) for line in lines)):
        if lines:
            last_line = lines[-1]
            while pdf.get_string_width(last_line + "...") > available_width and len(last_line) > 0:
                last_line = last_line[:-1]
            lines[-1] = last_line + "..."

    # Draw the lines
    current_y = y + padding
    for line in lines:
        if current_y + line_height <= y + height - padding:
            pdf.set_xy(x + padding, current_y)
            pdf.cell(available_width, line_height, line, 0, 0, 'L')
            current_y += line_height
        else:
            break


def _calculate_text_lines(text, width, font_size=8):
    """Calculate how many lines the text will take in the given width"""
    if not text:
        return 1

    # Rough estimation - this is used for row height calculation
    chars_per_mm = 2.0 if font_size <= 8 else 1.8
    chars_per_line = int((width - 4) * chars_per_mm)  # Account for padding

    if chars_per_line < 10:
        chars_per_line = 10

    words = str(text).split()
    lines = 1
    current_line_length = 0

    for word in words:
        word_length = len(word) + 1  # +1 for space
        if current_line_length + word_length > chars_per_line:
            lines += 1
            current_line_length = len(word)
        else:
            current_line_length += word_length

    return lines