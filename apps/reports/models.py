from django.db import models
from django.contrib.auth.models import User
import json


class ReportTemplate(models.Model):
    """Model to store custom report templates"""
    REPORT_TYPE_CHOICES = [
        ('mentions', 'Mentions Report'),
        ('clients', 'Client Report'),
        ('shows', 'Show Report'),
        ('presenters', 'Presenter Report'),
        ('analytics', 'Analytics Report'),
    ]

    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    report_type = models.CharField(max_length=50, choices=REPORT_TYPE_CHOICES)

    # Store configuration as JSON
    filters = models.J<PERSON><PERSON>ield(default=dict, blank=True)
    metrics = models.JSONField(default=list, blank=True)
    date_range_type = models.CharField(max_length=50, default='custom')

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_public = models.BooleanField(default=False)  # Can be shared with other users

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_report_type_display()})"


    def get_filters_display(self):
        """Return a human-readable version of filters"""
        if not self.filters:
            return "No filters"

        display_parts = []
        for key, value in self.filters.items():
            if value:
                display_parts.append(f"{key.replace('_', ' ').title()}: {value}")

        return ", ".join(display_parts) if display_parts else "No filters"


class ScheduledReport(models.Model):
    """Model to store scheduled report configurations"""
    FREQUENCY_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
    ]

    name = models.CharField(max_length=200)
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE)
    frequency = models.CharField(max_length=20, choices=FREQUENCY_CHOICES)

    # Recipients
    recipients = models.JSONField(default=list)  # List of email addresses

    # Schedule settings
    is_active = models.BooleanField(default=True)
    next_run = models.DateTimeField()
    last_run = models.DateTimeField(null=True, blank=True)

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_frequency_display()})"


class PDFTemplate(models.Model):
    """Model to store PDF template configurations for report generation"""
    TEMPLATE_TYPES = [
        ('mentions_summary', 'Mentions Summary'),
        ('client_report', 'Client Report'),
        ('analytics_report', 'Analytics Report'),
        ('daily_summary', 'Daily Summary'),
        ('weekly_trends', 'Weekly Trends'),
        ('monthly_overview', 'Monthly Overview'),
        ('custom', 'Custom Report'),
    ]

    name = models.CharField(max_length=200)
    template_type = models.CharField(max_length=30, choices=TEMPLATE_TYPES)
    description = models.TextField(blank=True)

    # Template configuration as JSON
    header_config = models.JSONField(default=dict, help_text="Header settings (logo, title, colors)")
    footer_config = models.JSONField(default=dict, help_text="Footer settings (text, page numbers)")
    body_config = models.JSONField(default=dict, help_text="Body layout and structure")
    style_config = models.JSONField(default=dict, help_text="Colors, fonts, and styling")

    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"


class PDFDocument(models.Model):
    """Model to store generated PDF documents"""
    template = models.ForeignKey(PDFTemplate, on_delete=models.CASCADE)
    title = models.CharField(max_length=200)
    data = models.JSONField(help_text="Data used to generate PDF")
    file_path = models.CharField(max_length=500, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.created_at.strftime('%Y-%m-%d')}"

