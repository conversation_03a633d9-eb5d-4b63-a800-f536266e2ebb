from django.urls import path, include
from . import views

app_name = 'reports'

urlpatterns = [
    path('', views.report_list, name='report_list'),
    path('analytics/', views.analytics, name='analytics'),
    path('export/', views.export_report, name='export_report'),
    path('generate-pdf/', views.generate_pdf_report, name='generate_pdf_report'),



    # Mention Reports
    path('mentions-summary/', views.mentions_summary, name='mentions_summary'),
    path('mentions-by-client/', views.mentions_by_client, name='mentions_by_client'),
    path('client-mentions-detailed/', views.client_mentions_detailed, name='client_mentions_detailed'),
    path('mentions-by-priority/', views.mentions_by_priority, name='mentions_by_priority'),

    # Show Reports
    path('show-performance/', views.show_performance, name='show_performance'),
    path('show-schedule/', views.show_schedule, name='show_schedule'),
    path('show-readings/', views.show_readings, name='show_readings'),
    path('presenter-activity/', views.presenter_activity, name='presenter_activity'),

    # Client Reports
    path('client-activity/', views.client_activity, name='client_activity'),
    path('client-revenue/', views.client_revenue, name='client_revenue'),
    path('client-retention/', views.client_retention, name='client_retention'),
    path('client-order-schedule/', views.client_order_schedule, name='client_order_schedule'),

    # Time Reports
    path('daily-summary/', views.daily_summary, name='daily_summary'),
    path('weekly-trends/', views.weekly_trends, name='weekly_trends'),
    path('monthly-overview/', views.monthly_overview, name='monthly_overview'),

    # Custom Reports
    path('custom/', views.custom_report_builder, name='custom_report_builder'),
    path('saved/', views.saved_reports, name='saved_reports'),
    path('schedule/', views.schedule_report, name='schedule_report'),
    path('schedule-details/', views.schedule_report, name='schedule_details'),



    # PDF Template Designer
    path('template-designer/', views.pdf_template_designer, name='pdf_template_designer'),
    path('api/save-template/', views.save_pdf_template, name='save_pdf_template'),
    path('enhanced-pdf-tool/', views.enhanced_pdf_tool, name='enhanced_pdf_tool'),

    # # PDF Template Management
    # path('pdf-templates/', views.list_pdf_templates, name='list_pdf_templates'),
    # path('pdf-templates/create/', views.create_pdf_template, name='create_pdf_template'),
    # path('pdf-templates/<int:template_id>/generate/', views.generate_pdf_from_template, name='generate_pdf_from_template'),
    # path('pdf-templates/<int:template_id>/preview/', views.template_preview, name='template_preview'),

    # REST API endpoints
    path('api/v1/', include('apps.reports.api_urls')),
]
