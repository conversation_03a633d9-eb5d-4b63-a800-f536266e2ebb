# Generated by Django 4.2.7 on 2025-06-20 19:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("reports", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PDFTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("mentions_summary", "Mentions Summary"),
                            ("client_report", "Client Report"),
                            ("analytics_report", "Analytics Report"),
                            ("daily_summary", "Daily Summary"),
                            ("weekly_trends", "Weekly Trends"),
                            ("monthly_overview", "Monthly Overview"),
                            ("custom", "Custom Report"),
                        ],
                        max_length=30,
                    ),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "header_config",
                    models.JSONField(
                        default=dict, help_text="Header settings (logo, title, colors)"
                    ),
                ),
                (
                    "footer_config",
                    models.JSONField(
                        default=dict, help_text="Footer settings (text, page numbers)"
                    ),
                ),
                (
                    "body_config",
                    models.JSONField(
                        default=dict, help_text="Body layout and structure"
                    ),
                ),
                (
                    "style_config",
                    models.JSONField(
                        default=dict, help_text="Colors, fonts, and styling"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PDFDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("data", models.JSONField(help_text="Data used to generate PDF")),
                ("file_path", models.CharField(blank=True, max_length=500)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="reports.pdftemplate",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
