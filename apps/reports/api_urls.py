"""
API URL patterns for the reports app.
Provides REST API endpoints for template management with comprehensive CRUD operations.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import api_views

# Create router and register viewsets
router = DefaultRouter()
router.register(r'report-templates', api_views.ReportTemplateViewSet, basename='report-template')
router.register(r'scheduled-reports', api_views.ScheduledReportViewSet, basename='scheduled-report')

# URL patterns
urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
]

# The router automatically creates the following endpoints:
#
# PDF Design Templates:
# GET    /api/v1/pdf-templates/                    - List templates
# POST   /api/v1/pdf-templates/                    - Create template
# GET    /api/v1/pdf-templates/{id}/               - Retrieve template
# PUT    /api/v1/pdf-templates/{id}/               - Update template
# PATCH  /api/v1/pdf-templates/{id}/               - Partial update template
# DELETE /api/v1/pdf-templates/{id}/               - Delete template
#
# Custom actions:
# POST   /api/v1/pdf-templates/{id}/clone/         - Clone template
# GET    /api/v1/pdf-templates/{id}/export/        - Export template
# POST   /api/v1/pdf-templates/import_template/    - Import template
# POST   /api/v1/pdf-templates/{id}/set_default/   - Set as default
# GET    /api/v1/pdf-templates/get_default/        - Get default template
# GET    /api/v1/pdf-templates/{id}/version_history/ - Get version history
# POST   /api/v1/pdf-templates/{id}/create_version/ - Create version
# POST   /api/v1/pdf-templates/{id}/rollback/      - Rollback to version
# GET    /api/v1/pdf-templates/{id}/compare_versions/ - Compare versions
# POST   /api/v1/pdf-templates/import_from_file/   - Import from file
# POST   /api/v1/pdf-templates/import_multiple/    - Import multiple templates
# POST   /api/v1/pdf-templates/export_multiple/    - Export multiple templates
#
# Report Templates:
# GET    /api/v1/report-templates/                 - List report templates
# POST   /api/v1/report-templates/                 - Create report template
# GET    /api/v1/report-templates/{id}/            - Retrieve report template
# PUT    /api/v1/report-templates/{id}/            - Update report template
# PATCH  /api/v1/report-templates/{id}/            - Partial update report template
# DELETE /api/v1/report-templates/{id}/            - Delete report template
#
# Scheduled Reports:
# GET    /api/v1/scheduled-reports/                - List scheduled reports
# POST   /api/v1/scheduled-reports/                - Create scheduled report
# GET    /api/v1/scheduled-reports/{id}/           - Retrieve scheduled report
# PUT    /api/v1/scheduled-reports/{id}/           - Update scheduled report
# PATCH  /api/v1/scheduled-reports/{id}/           - Partial update scheduled report
# DELETE /api/v1/scheduled-reports/{id}/           - Delete scheduled report
