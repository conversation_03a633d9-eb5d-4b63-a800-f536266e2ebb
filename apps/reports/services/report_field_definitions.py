"""
Report Field Definitions Service
Defines available data fields for each report type that can be used in PDF templates.
"""

from typing import Dict, List, Any
from datetime import datetime


class ReportFieldDefinitions:
    """Service to provide available data fields for each report type"""
    
    def __init__(self):
        self.field_definitions = self._initialize_field_definitions()
    
    def get_available_fields(self, report_type: str) -> List[Dict[str, Any]]:
        """Get all available fields for a specific report type"""
        return self.field_definitions.get(report_type, [])
    
    def get_field_categories(self, report_type: str) -> Dict[str, List[Dict[str, Any]]]:
        """Get fields organized by categories for a specific report type"""
        fields = self.get_available_fields(report_type)
        categories = {}
        
        for field in fields:
            category = field.get('category', 'General')
            if category not in categories:
                categories[category] = []
            categories[category].append(field)
        
        return categories
    
    def _initialize_field_definitions(self) -> Dict[str, List[Dict[str, Any]]]:
        """Initialize field definitions for all report types"""
        return {
            'client-mentions-detailed': self._get_client_mentions_detailed_fields(),
            'mentions-summary': self._get_mentions_summary_fields(),
            'client-summary': self._get_client_summary_fields(),
            'show-summary': self._get_show_summary_fields(),
            'presenter-summary': self._get_presenter_summary_fields(),
            'analytics-dashboard': self._get_analytics_dashboard_fields(),
        }
    
    def _get_client_mentions_detailed_fields(self) -> List[Dict[str, Any]]:
        """Fields available for Client Mentions Detailed reports"""
        return [
            # Report Header Fields
            {
                'id': 'report_title',
                'name': 'Report Title',
                'description': 'Main title of the report',
                'category': 'Report Header',
                'data_type': 'text',
                'placeholder': '{{ report_title }}',
                'sample_value': 'Client Mentions Detailed Report',
                'icon': 'fas fa-heading'
            },
            {
                'id': 'report_date',
                'name': 'Report Date',
                'description': 'Date when the report was generated',
                'category': 'Report Header',
                'data_type': 'date',
                'placeholder': '{{ report_date }}',
                'sample_value': datetime.now().strftime('%Y-%m-%d'),
                'icon': 'fas fa-calendar'
            },
            {
                'id': 'date_range',
                'name': 'Date Range',
                'description': 'Period covered by the report',
                'category': 'Report Header',
                'data_type': 'text',
                'placeholder': '{{ date_range }}',
                'sample_value': '2025-05-21 to 2025-06-20',
                'icon': 'fas fa-calendar-alt'
            },
            
            # Client Information
            {
                'id': 'client_name',
                'name': 'Client Name',
                'description': 'Name of the selected client',
                'category': 'Client Information',
                'data_type': 'text',
                'placeholder': '{{ client_name }}',
                'sample_value': 'Sample Client Ltd.',
                'icon': 'fas fa-building'
            },
            {
                'id': 'client_industry',
                'name': 'Client Industry',
                'description': 'Industry sector of the client',
                'category': 'Client Information',
                'data_type': 'text',
                'placeholder': '{{ client_industry }}',
                'sample_value': 'Technology',
                'icon': 'fas fa-industry'
            },
            
            # Filter Information
            {
                'id': 'status_filter',
                'name': 'Status Filter',
                'description': 'Applied status filter',
                'category': 'Filter Information',
                'data_type': 'text',
                'placeholder': '{{ status_filter }}',
                'sample_value': 'Completed',
                'icon': 'fas fa-filter'
            },
            {
                'id': 'show_summary',
                'name': 'Show Summary',
                'description': 'Whether summary is shown',
                'category': 'Filter Information',
                'data_type': 'boolean',
                'placeholder': '{{ show_summary }}',
                'sample_value': 'Yes',
                'icon': 'fas fa-list'
            },
            
            # Statistics
            {
                'id': 'total_mentions',
                'name': 'Total Mentions',
                'description': 'Total number of mentions',
                'category': 'Statistics',
                'data_type': 'number',
                'placeholder': '{{ total_mentions }}',
                'sample_value': '45',
                'icon': 'fas fa-hashtag'
            },
            {
                'id': 'completed_mentions',
                'name': 'Completed Mentions',
                'description': 'Number of completed mentions',
                'category': 'Statistics',
                'data_type': 'number',
                'placeholder': '{{ completed_mentions }}',
                'sample_value': '38',
                'icon': 'fas fa-check-circle'
            },
            {
                'id': 'pending_mentions',
                'name': 'Pending Mentions',
                'description': 'Number of pending mentions',
                'category': 'Statistics',
                'data_type': 'number',
                'placeholder': '{{ pending_mentions }}',
                'sample_value': '7',
                'icon': 'fas fa-clock'
            },
            
            # Mentions Table
            {
                'id': 'mentions_table',
                'name': 'Mentions Table',
                'description': 'Table containing all mention details',
                'category': 'Data Tables',
                'data_type': 'table',
                'placeholder': '{{ mentions_table }}',
                'sample_value': 'Detailed mentions data table',
                'icon': 'fas fa-table',
                'columns': [
                    {'id': 'date', 'name': 'Date', 'type': 'date'},
                    {'id': 'time', 'name': 'Time', 'type': 'time'},
                    {'id': 'show', 'name': 'Show', 'type': 'text'},
                    {'id': 'presenter', 'name': 'Presenter', 'type': 'text'},
                    {'id': 'content', 'name': 'Content', 'type': 'text'},
                    {'id': 'status', 'name': 'Status', 'type': 'text'},
                    {'id': 'duration', 'name': 'Duration', 'type': 'text'},
                ]
            }
        ]
    
    def _get_mentions_summary_fields(self) -> List[Dict[str, Any]]:
        """Fields available for Mentions Summary reports"""
        return [
            # Report Header Fields
            {
                'id': 'report_title',
                'name': 'Report Title',
                'description': 'Main title of the report',
                'category': 'Report Header',
                'data_type': 'text',
                'placeholder': '{{ report_title }}',
                'sample_value': 'Mentions Summary Report',
                'icon': 'fas fa-heading'
            },
            {
                'id': 'report_date',
                'name': 'Report Date',
                'description': 'Date when the report was generated',
                'category': 'Report Header',
                'data_type': 'date',
                'placeholder': '{{ report_date }}',
                'sample_value': datetime.now().strftime('%Y-%m-%d'),
                'icon': 'fas fa-calendar'
            },
            {
                'id': 'date_range',
                'name': 'Date Range',
                'description': 'Period covered by the report',
                'category': 'Report Header',
                'data_type': 'text',
                'placeholder': '{{ date_range }}',
                'sample_value': '2025-05-21 to 2025-06-20',
                'icon': 'fas fa-calendar-alt'
            },
            
            # Summary Statistics
            {
                'id': 'total_mentions',
                'name': 'Total Mentions',
                'description': 'Total number of mentions across all clients',
                'category': 'Summary Statistics',
                'data_type': 'number',
                'placeholder': '{{ total_mentions }}',
                'sample_value': '156',
                'icon': 'fas fa-hashtag'
            },
            {
                'id': 'total_clients',
                'name': 'Total Clients',
                'description': 'Number of clients with mentions',
                'category': 'Summary Statistics',
                'data_type': 'number',
                'placeholder': '{{ total_clients }}',
                'sample_value': '12',
                'icon': 'fas fa-users'
            },
            {
                'id': 'total_shows',
                'name': 'Total Shows',
                'description': 'Number of shows with mentions',
                'category': 'Summary Statistics',
                'data_type': 'number',
                'placeholder': '{{ total_shows }}',
                'sample_value': '8',
                'icon': 'fas fa-broadcast-tower'
            },
            
            # Summary Table
            {
                'id': 'summary_table',
                'name': 'Summary Table',
                'description': 'Table with summary data by client/show',
                'category': 'Data Tables',
                'data_type': 'table',
                'placeholder': '{{ summary_table }}',
                'sample_value': 'Summary data table',
                'icon': 'fas fa-table',
                'columns': [
                    {'id': 'client', 'name': 'Client', 'type': 'text'},
                    {'id': 'mentions_count', 'name': 'Mentions', 'type': 'number'},
                    {'id': 'total_duration', 'name': 'Total Duration', 'type': 'text'},
                    {'id': 'avg_duration', 'name': 'Avg Duration', 'type': 'text'},
                ]
            }
        ]
    
    def _get_client_summary_fields(self) -> List[Dict[str, Any]]:
        """Fields available for Client Summary reports"""
        return [
            # Basic fields for client summary
            {
                'id': 'report_title',
                'name': 'Report Title',
                'description': 'Main title of the report',
                'category': 'Report Header',
                'data_type': 'text',
                'placeholder': '{{ report_title }}',
                'sample_value': 'Client Summary Report',
                'icon': 'fas fa-heading'
            },
            # Add more fields as needed
        ]
    
    def _get_show_summary_fields(self) -> List[Dict[str, Any]]:
        """Fields available for Show Summary reports"""
        return [
            # Basic fields for show summary
            {
                'id': 'report_title',
                'name': 'Report Title',
                'description': 'Main title of the report',
                'category': 'Report Header',
                'data_type': 'text',
                'placeholder': '{{ report_title }}',
                'sample_value': 'Show Summary Report',
                'icon': 'fas fa-heading'
            },
            # Add more fields as needed
        ]
    
    def _get_presenter_summary_fields(self) -> List[Dict[str, Any]]:
        """Fields available for Presenter Summary reports"""
        return [
            # Basic fields for presenter summary
            {
                'id': 'report_title',
                'name': 'Report Title',
                'description': 'Main title of the report',
                'category': 'Report Header',
                'data_type': 'text',
                'placeholder': '{{ report_title }}',
                'sample_value': 'Presenter Summary Report',
                'icon': 'fas fa-heading'
            },
            # Add more fields as needed
        ]
    
    def _get_analytics_dashboard_fields(self) -> List[Dict[str, Any]]:
        """Fields available for Analytics Dashboard reports"""
        return [
            # Basic fields for analytics dashboard
            {
                'id': 'report_title',
                'name': 'Report Title',
                'description': 'Main title of the report',
                'category': 'Report Header',
                'data_type': 'text',
                'placeholder': '{{ report_title }}',
                'sample_value': 'Analytics Dashboard Report',
                'icon': 'fas fa-heading'
            },
            # Add more fields as needed
        ]


# Global service instance
report_field_definitions = ReportFieldDefinitions()
