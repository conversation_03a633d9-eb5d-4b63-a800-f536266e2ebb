"""
Template caching service for optimizing template operations.
Provides intelligent caching for compiled templates, frequently used data, and user preferences.
"""

import json
import hashlib
import base64
from typing import Dict, List, Any, Optional, Union
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
import pickle
import logging

logger = logging.getLogger(__name__)


class TemplateCacheService:
    """
    Comprehensive caching service for template operations.
    Handles compiled templates, user preferences, and frequently accessed data.
    """
    
    # Cache timeout configurations (in seconds)
    CACHE_TIMEOUTS = {
        'compiled_template': 3600,      # 1 hour
        'user_templates': 1800,         # 30 minutes
        'template_metadata': 7200,      # 2 hours
        'default_template': 3600,       # 1 hour
        'template_preview': 900,        # 15 minutes
        'user_preferences': 86400,      # 24 hours
        'template_stats': 1800,         # 30 minutes
        'popular_templates': 3600,      # 1 hour
    }
    
    # Cache key prefixes
    CACHE_PREFIXES = {
        'compiled': 'compiled_template_',
        'user_templates': 'user_templates_',
        'metadata': 'template_metadata_',
        'default': 'default_template_',
        'preview': 'template_preview_',
        'preferences': 'user_preferences_',
        'stats': 'template_stats_',
        'popular': 'popular_templates',
        'version_history': 'template_versions_',
    }
    
    def __init__(self):
        self.cache_backend = cache
    
    def cache_compiled_template(self, template_id: int, compiled_data: Dict[str, Any], 
                              template_hash: str = None) -> bool:
        """
        Cache compiled template data.
        
        Args:
            template_id: Template ID
            compiled_data: Compiled template data
            template_hash: Optional hash for cache invalidation
            
        Returns:
            True if cached successfully
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['compiled']}{template_id}"
            
            # Add metadata to cached data
            cache_data = {
                'compiled_data': compiled_data,
                'cached_at': timezone.now().isoformat(),
                'template_hash': template_hash,
                'cache_version': '1.0'
            }
            
            timeout = self.CACHE_TIMEOUTS['compiled_template']
            success = self.cache_backend.set(cache_key, cache_data, timeout)
            
            if success:
                # Also cache the hash for quick validation
                hash_key = f"{cache_key}_hash"
                self.cache_backend.set(hash_key, template_hash, timeout)
                logger.debug(f"Cached compiled template {template_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to cache compiled template {template_id}: {str(e)}")
            return False
    
    def get_compiled_template(self, template_id: int, template_hash: str = None) -> Optional[Dict[str, Any]]:
        """
        Retrieve compiled template from cache.
        
        Args:
            template_id: Template ID
            template_hash: Optional hash for validation
            
        Returns:
            Compiled template data or None if not cached/invalid
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['compiled']}{template_id}"
            cached_data = self.cache_backend.get(cache_key)
            
            if not cached_data:
                return None
            
            # Validate hash if provided
            if template_hash:
                cached_hash = cached_data.get('template_hash')
                if cached_hash != template_hash:
                    logger.debug(f"Template {template_id} hash mismatch, invalidating cache")
                    self.invalidate_compiled_template(template_id)
                    return None
            
            logger.debug(f"Retrieved compiled template {template_id} from cache")
            return cached_data.get('compiled_data')
            
        except Exception as e:
            logger.error(f"Failed to retrieve compiled template {template_id}: {str(e)}")
            return None
    
    def invalidate_compiled_template(self, template_id: int) -> bool:
        """
        Invalidate cached compiled template.
        
        Args:
            template_id: Template ID to invalidate
            
        Returns:
            True if invalidated successfully
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['compiled']}{template_id}"
            hash_key = f"{cache_key}_hash"
            
            self.cache_backend.delete(cache_key)
            self.cache_backend.delete(hash_key)
            
            logger.debug(f"Invalidated compiled template cache for {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to invalidate compiled template {template_id}: {str(e)}")
            return False
    
    def cache_user_templates(self, user_id: int, templates_data: List[Dict[str, Any]]) -> bool:
        """
        Cache user's template list.
        
        Args:
            user_id: User ID
            templates_data: List of template data
            
        Returns:
            True if cached successfully
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['user_templates']}{user_id}"
            
            cache_data = {
                'templates': templates_data,
                'cached_at': timezone.now().isoformat(),
                'count': len(templates_data)
            }
            
            timeout = self.CACHE_TIMEOUTS['user_templates']
            success = self.cache_backend.set(cache_key, cache_data, timeout)
            
            if success:
                logger.debug(f"Cached {len(templates_data)} templates for user {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to cache user templates for {user_id}: {str(e)}")
            return False
    
    def get_user_templates(self, user_id: int) -> Optional[List[Dict[str, Any]]]:
        """
        Retrieve user's cached template list.
        
        Args:
            user_id: User ID
            
        Returns:
            List of template data or None if not cached
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['user_templates']}{user_id}"
            cached_data = self.cache_backend.get(cache_key)
            
            if cached_data:
                logger.debug(f"Retrieved {cached_data.get('count', 0)} templates for user {user_id} from cache")
                return cached_data.get('templates')
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve user templates for {user_id}: {str(e)}")
            return None
    
    def invalidate_user_templates(self, user_id: int) -> bool:
        """
        Invalidate user's template cache.
        
        Args:
            user_id: User ID
            
        Returns:
            True if invalidated successfully
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['user_templates']}{user_id}"
            self.cache_backend.delete(cache_key)
            
            logger.debug(f"Invalidated user templates cache for {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to invalidate user templates for {user_id}: {str(e)}")
            return False
    
    def cache_template_preview(self, template_id: int, preview_data: bytes, 
                             settings_hash: str = None) -> bool:
        """
        Cache template preview (PDF or image).
        
        Args:
            template_id: Template ID
            preview_data: Preview data (PDF bytes or image)
            settings_hash: Hash of preview settings for validation
            
        Returns:
            True if cached successfully
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['preview']}{template_id}"

            if settings_hash:
                cache_key += f"_{settings_hash[:8]}"  # Use first 8 chars of hash

            # Encode bytes data as base64 string for JSON serialization
            encoded_data = base64.b64encode(preview_data).decode('utf-8') if isinstance(preview_data, bytes) else preview_data

            cache_data = {
                'preview_data': encoded_data,
                'cached_at': timezone.now().isoformat(),
                'settings_hash': settings_hash,
                'size': len(preview_data),
                'is_base64_encoded': isinstance(preview_data, bytes)
            }

            timeout = self.CACHE_TIMEOUTS['template_preview']
            success = self.cache_backend.set(cache_key, cache_data, timeout)

            if success:
                logger.debug(f"Cached preview for template {template_id} ({len(preview_data)} bytes)")

            return success

        except Exception as e:
            logger.error(f"Failed to cache template preview {template_id}: {str(e)}")
            # Don't break the application if caching fails
            return False
    
    def get_template_preview(self, template_id: int, settings_hash: str = None) -> Optional[bytes]:
        """
        Retrieve cached template preview.
        
        Args:
            template_id: Template ID
            settings_hash: Hash of preview settings
            
        Returns:
            Preview data or None if not cached
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['preview']}{template_id}"
            
            if settings_hash:
                cache_key += f"_{settings_hash[:8]}"
            
            cached_data = self.cache_backend.get(cache_key)

            if cached_data:
                # Validate settings hash if provided
                if settings_hash:
                    cached_hash = cached_data.get('settings_hash')
                    if cached_hash != settings_hash:
                        return None

                logger.debug(f"Retrieved preview for template {template_id} from cache")
                preview_data = cached_data.get('preview_data')

                # Decode base64 data if it was encoded
                if cached_data.get('is_base64_encoded', False) and isinstance(preview_data, str):
                    preview_data = base64.b64decode(preview_data.encode('utf-8'))

                return preview_data
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve template preview {template_id}: {str(e)}")
            # Don't break the application if cache retrieval fails
            return None
    
    def cache_user_preferences(self, user_id: int, preferences: Dict[str, Any]) -> bool:
        """
        Cache user preferences for template operations.
        
        Args:
            user_id: User ID
            preferences: User preferences dictionary
            
        Returns:
            True if cached successfully
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['preferences']}{user_id}"
            
            cache_data = {
                'preferences': preferences,
                'cached_at': timezone.now().isoformat()
            }
            
            timeout = self.CACHE_TIMEOUTS['user_preferences']
            success = self.cache_backend.set(cache_key, cache_data, timeout)
            
            if success:
                logger.debug(f"Cached preferences for user {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to cache user preferences for {user_id}: {str(e)}")
            return False
    
    def get_user_preferences(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached user preferences.
        
        Args:
            user_id: User ID
            
        Returns:
            User preferences or None if not cached
        """
        try:
            cache_key = f"{self.CACHE_PREFIXES['preferences']}{user_id}"
            cached_data = self.cache_backend.get(cache_key)
            
            if cached_data:
                logger.debug(f"Retrieved preferences for user {user_id} from cache")
                return cached_data.get('preferences')
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve user preferences for {user_id}: {str(e)}")
            return None
    
    def cache_template_stats(self, stats_data: Dict[str, Any]) -> bool:
        """
        Cache template usage statistics.
        
        Args:
            stats_data: Statistics data
            
        Returns:
            True if cached successfully
        """
        try:
            cache_key = self.CACHE_PREFIXES['stats']
            
            cache_data = {
                'stats': stats_data,
                'cached_at': timezone.now().isoformat()
            }
            
            timeout = self.CACHE_TIMEOUTS['template_stats']
            success = self.cache_backend.set(cache_key, cache_data, timeout)
            
            if success:
                logger.debug("Cached template statistics")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to cache template stats: {str(e)}")
            return False
    
    def get_template_stats(self) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached template statistics.
        
        Returns:
            Statistics data or None if not cached
        """
        try:
            cache_key = self.CACHE_PREFIXES['stats']
            cached_data = self.cache_backend.get(cache_key)
            
            if cached_data:
                logger.debug("Retrieved template statistics from cache")
                return cached_data.get('stats')
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve template stats: {str(e)}")
            return None
    
    def invalidate_all_template_caches(self, template_id: int) -> bool:
        """
        Invalidate all caches related to a specific template.
        
        Args:
            template_id: Template ID
            
        Returns:
            True if all caches invalidated successfully
        """
        try:
            # Invalidate compiled template
            self.invalidate_compiled_template(template_id)
            
            # Invalidate preview cache (all variants)
            preview_pattern = f"{self.CACHE_PREFIXES['preview']}{template_id}"
            # Note: Django cache doesn't support pattern deletion, so we'd need Redis directly
            # For now, we'll just delete the base key
            self.cache_backend.delete(preview_pattern)
            
            # Invalidate metadata
            metadata_key = f"{self.CACHE_PREFIXES['metadata']}{template_id}"
            self.cache_backend.delete(metadata_key)
            
            # Invalidate version history
            version_key = f"{self.CACHE_PREFIXES['version_history']}{template_id}"
            self.cache_backend.delete(version_key)
            
            logger.debug(f"Invalidated all caches for template {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to invalidate all caches for template {template_id}: {str(e)}")
            return False
    
    def generate_template_hash(self, template_data: Dict[str, Any]) -> str:
        """
        Generate hash for template data to detect changes.
        
        Args:
            template_data: Template data dictionary
            
        Returns:
            SHA256 hash of template data
        """
        try:
            # Create a normalized JSON string for hashing
            normalized_json = json.dumps(template_data, sort_keys=True, separators=(',', ':'))
            return hashlib.sha256(normalized_json.encode('utf-8')).hexdigest()
            
        except Exception as e:
            logger.error(f"Failed to generate template hash: {str(e)}")
            return ""
    
    def warm_cache(self, template_ids: List[int]) -> Dict[str, int]:
        """
        Pre-warm cache for frequently used templates.
        
        Args:
            template_ids: List of template IDs to warm
            
        Returns:
            Dictionary with warming results
        """
        results = {
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
        
        for template_id in template_ids:
            try:
                # Check if already cached
                cache_key = f"{self.CACHE_PREFIXES['compiled']}{template_id}"
                if self.cache_backend.get(cache_key):
                    results['skipped'] += 1
                    continue
                
                # Here you would typically load and compile the template
                # For now, we'll just mark as attempted
                results['success'] += 1
                
            except Exception as e:
                logger.error(f"Failed to warm cache for template {template_id}: {str(e)}")
                results['failed'] += 1
        
        logger.info(f"Cache warming completed: {results}")
        return results


# Global cache service instance
template_cache = TemplateCacheService()


# Convenience functions
def cache_compiled_template(template_id: int, compiled_data: Dict[str, Any], 
                          template_hash: str = None) -> bool:
    """Cache compiled template data"""
    return template_cache.cache_compiled_template(template_id, compiled_data, template_hash)


def get_compiled_template(template_id: int, template_hash: str = None) -> Optional[Dict[str, Any]]:
    """Get compiled template from cache"""
    return template_cache.get_compiled_template(template_id, template_hash)


def invalidate_template_cache(template_id: int) -> bool:
    """Invalidate all caches for a template"""
    return template_cache.invalidate_all_template_caches(template_id)
