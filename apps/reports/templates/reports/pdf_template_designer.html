{% extends 'base.html' %}
{% load static %}

{% block title %}
  PDF Template Designer
{% endblock %}

{% block extra_css %}
  <style>
    .container-compact {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 20px;
    }
    .designer-container {
      background: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;
    }
    .designer-header {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
      padding: 20px;
      border-radius: 8px 8px 0 0;
    }
    .form-section {
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;
      background-color: #f8f9fa;
    }
    .section-title {
      font-weight: 600;
      color: #374151;
      margin-bottom: 12px;
      border-bottom: 2px solid #3b82f6;
      padding-bottom: 4px;
      font-size: 0.95em;
    }
    .form-control {
      font-size: 0.9em;
      padding: 8px 12px;
      border-radius: 6px;
      border: 1px solid #d1d5db;
    }
    .form-control:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    .btn-add {
      background: #10b981;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 0.85em;
    }
    .btn-remove {
      background: #ef4444;
      color: white;
      border: none;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.8em;
    }
    .column-item {
      background: white;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 8px;
    }
    .json-preview {
      background: #1f2937;
      color: #f9fafb;
      border-radius: 6px;
      padding: 16px;
      font-family: 'Courier New', monospace;
      font-size: 0.85em;
      max-height: 400px;
      overflow-y: auto;
    }
    .template-list {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      padding: 8px;
    }
    .template-item {
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 4px;
      padding: 8px;
      margin-bottom: 4px;
      cursor: pointer;
      transition: all 0.2s;
    }
    .template-item:hover {
      background: #f3f4f6;
      border-color: #3b82f6;
    }
    .template-item.selected {
      background: #dbeafe;
      border-color: #3b82f6;
    }
  </style>
{% endblock %}

{% block content %}
  <div class="container-compact">
    <div class="designer-container">
      <div class="designer-header">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h2 class="mb-1"><i class="fas fa-paint-brush"></i> PDF Template Designer</h2>
            <p class="mb-0 opacity-90">Create custom PDF layouts with JSON configuration</p>
          </div>
          <a href="{% url 'reports:dynamic_reports_list' %}" class="btn btn-light btn-sm"><i class="fas fa-arrow-left"></i> Back to Reports</a>
        </div>
      </div>

      <div class="p-4">
        <div class="row">
          <!-- Template Designer Form -->
          <div class="col-lg-8">
            <form id="templateForm">
              <!-- Basic Settings -->
              <div class="form-section">
                <div class="section-title">
                  <i class="fas fa-cog"></i> Basic Settings
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label>Template Name</label>
                      <input type="text" class="form-control" id="templateName" placeholder="My Custom Report" />
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label>Report Type Key</label>
                      <input type="text" class="form-control" id="reportKey" placeholder="my-custom-report" />
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label>Description</label>
                  <input type="text" class="form-control" id="description" placeholder="Description of your custom report" />
                </div>
              </div>

              <!-- PDF Layout Settings -->
              <div class="form-section">
                <div class="section-title">
                  <i class="fas fa-file-pdf"></i> PDF Layout
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label>Orientation</label>
                      <select class="form-control" id="orientation">
                        <option value="P">Portrait</option>
                        <option value="L">Landscape</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label>Data Model</label>
                      <select class="form-control" id="dataModel">
                        <option value="Mention">Mention</option>
                        <option value="MentionReading">MentionReading</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label>Ordering</label>
                      <select class="form-control" id="ordering">
                        <option value="-created_at">Newest First</option>
                        <option value="created_at">Oldest First</option>
                        <option value="-priority">Priority High to Low</option>
                        <option value="client">Client Name A-Z</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Filters Configuration -->
              <div class="form-section">
                <div class="section-title">
                  <i class="fas fa-filter"></i> Available Filters
                </div>
                <div id="filtersContainer">
                  <!-- Filters will be added here dynamically -->
                </div>
                <button type="button" class="btn btn-add" onclick="addFilter()"><i class="fas fa-plus"></i> Add Filter</button>
              </div>

              <!-- Columns Configuration -->
              <div class="form-section">
                <div class="section-title">
                  <i class="fas fa-columns"></i> Table Columns
                </div>
                <div id="columnsContainer">
                  <!-- Columns will be added here dynamically -->
                </div>
                <button type="button" class="btn btn-add" onclick="addColumn()"><i class="fas fa-plus"></i> Add Column</button>
              </div>

              <!-- Action Buttons -->
              <div class="text-center mt-4">
                <button type="button" class="btn btn-primary" onclick="generateJSON()"><i class="fas fa-code"></i> Generate JSON</button>
                <button type="button" class="btn btn-success" onclick="saveTemplate()"><i class="fas fa-save"></i> Save Template</button>
                <button type="button" class="btn btn-info" onclick="testTemplate()"><i class="fas fa-eye"></i> Test PDF</button>
              </div>
            </form>
          </div>

          <!-- JSON Preview & Saved Templates -->
          <div class="col-lg-4">
            <!-- JSON Preview -->
            <div class="form-section">
              <div class="section-title">
                <i class="fas fa-code"></i> JSON Preview
              </div>
              <div class="json-preview" id="jsonPreview">Click "Generate JSON" to see the configuration</div>
              <div class="mt-2">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="copyJSON()"><i class="fas fa-copy"></i> Copy JSON</button>
              </div>
            </div>

            <!-- Saved Templates -->
            <div class="form-section">
              <div class="section-title">
                <i class="fas fa-bookmark"></i> Saved Templates
              </div>
              <div class="template-list" id="templateList">
                <!-- Saved templates will appear here -->
              </div>
              <div class="mt-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="loadTemplates()"><i class="fas fa-refresh"></i> Refresh</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script src="{% static 'js/pdf_template_designer.js' %}"></script>
  <script>
    // Initialize with default filters and columns
    document.addEventListener('DOMContentLoaded', function () {
      // Add default filters
      addFilter('start_date', 'created_at__date__gte', 'date')
      addFilter('end_date', 'created_at__date__lte', 'date')
      addFilter('client_id', 'client_id', 'int')
    
      // Add default columns
      addColumn('Client', 'client', 'str', 30)
      addColumn('Date', 'created_at', 'datetime', 35)
      addColumn('Title', 'title', 'str', 50)
      addColumn('Status', 'status', 'str', 25)
    
      loadTemplates()
    })
  </script>
{% endblock %}
