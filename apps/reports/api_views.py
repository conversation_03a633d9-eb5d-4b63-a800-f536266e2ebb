"""
API Views for template management using Django REST Framework.
Provides comprehensive CRUD operations with proper permissions and validation.
"""

from rest_framework import viewsets, permissions
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q

from .models import ReportTemplate, ScheduledReport
from .serializers import (
    ReportTemplateSerializer, ScheduledReportSerializer
)


class IsOwnerOrPublicReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners to edit templates,
    but allow read access to public templates.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for public templates or owner
        if request.method in permissions.SAFE_METHODS:
            return obj.is_public or obj.created_by == request.user
        
        # Write permissions only for owner
        return obj.created_by == request.user









class ReportTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Report Templates.
    """
    
    serializer_class = ReportTemplateSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrPublicReadOnly]
    
    def get_queryset(self):
        """Filter templates based on user permissions"""
        user = self.request.user
        return ReportTemplate.objects.filter(
            Q(created_by=user) | Q(is_public=True)
        ).select_related('created_by').order_by('-created_at')
    
    def perform_create(self, serializer):
        """Set the creator when creating a new template"""
        serializer.save(created_by=self.request.user)


class ScheduledReportViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Scheduled Reports.
    """
    
    serializer_class = ScheduledReportSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Only show user's own scheduled reports"""
        return ScheduledReport.objects.filter(
            created_by=self.request.user
        ).select_related('template', 'created_by').order_by('-created_at')
    
    def perform_create(self, serializer):
        """Set the creator when creating a new scheduled report"""
        serializer.save(created_by=self.request.user)
