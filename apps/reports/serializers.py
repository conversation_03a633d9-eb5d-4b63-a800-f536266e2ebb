"""
Serializers for the reports app, focusing on template management and PDF design.
"""

from rest_framework import serializers
from .models import ReportTemplate, ScheduledReport






class ReportTemplateSerializer(serializers.ModelSerializer):
    """Serializer for Report Templates"""
    
    created_by_username = serializers.Char<PERSON>ield(source='created_by.username', read_only=True)
    is_owner = serializers.SerializerMethodField()
    filters_display = serializers.CharField(source='get_filters_display', read_only=True)
    
    class Meta:
        model = ReportTemplate
        fields = [
            'id', 'name', 'description', 'report_type', 'filters', 'metrics',
            'date_range_type', 'created_by', 'created_by_username', 'created_at',
            'updated_at', 'is_public', 'is_owner', 'filters_display'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at', 'created_by_username', 'is_owner', 'filters_display']
    
    def get_is_owner(self, obj):
        """Check if current user is the owner of the template"""
        request = self.context.get('request')
        if request and request.user:
            return obj.created_by == request.user
        return False


class ScheduledReportSerializer(serializers.ModelSerializer):
    """Serializer for Scheduled Reports"""
    
    template_name = serializers.CharField(source='template.name', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = ScheduledReport
        fields = [
            'id', 'name', 'template', 'template_name', 'frequency', 'recipients',
            'is_active', 'next_run', 'last_run', 'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at', 'template_name', 'created_by_username']
    
    def validate_recipients(self, value):
        """Validate email addresses in recipients list"""
        if not isinstance(value, list):
            raise serializers.ValidationError("Recipients must be a list of email addresses")
        
        for email in value:
            if not isinstance(email, str) or '@' not in email:
                raise serializers.ValidationError(f"Invalid email address: {email}")
        
        return value



