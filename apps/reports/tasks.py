"""
Background tasks for generating reports, analytics, and performance metrics.
"""

from celery import shared_task
from django.utils import timezone
from django.db.models import Q, Count, Avg, Sum
from datetime import datetime, timedelta
from apps.organizations.models import Organization
from apps.mentions.models import Mention, MentionReading
from apps.shows.models import Show, ShowSession
from apps.core.models import Presenter
from apps.core.notifications import notify_managers, notify_admins
from apps.activity_logs.models import ActivityLog
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def generate_daily_reports(self):
    """
    Generate daily performance reports for all organizations
    """
    try:
        current_date = timezone.now().date()
        yesterday = current_date - timedelta(days=1)
        
        organizations = Organization.objects.filter(is_active=True)
        reports_generated = 0
        
        for organization in organizations:
            # Generate comprehensive daily report
            report_data = _generate_daily_report_data(organization, yesterday)
            
            # Send report to managers and admins
            notify_managers(
                organization=organization,
                notification_type='daily_report',
                context={
                    'report_data': report_data,
                    'date': yesterday,
                    'organization': organization
                }
            )
            
            # Log report generation
            ActivityLog.log_activity(
                user=None,
                organization=organization,
                action='daily_report_generated',
                description=f"Daily report generated for {yesterday}",
                level='info',
                metadata=report_data
            )
            
            reports_generated += 1
        
        logger.info(f"Generated daily reports for {reports_generated} organizations")
        return f"Generated {reports_generated} daily reports"
        
    except Exception as exc:
        logger.error(f"Daily report generation failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=3600)


@shared_task(bind=True, max_retries=3)
def generate_weekly_analytics(self):
    """
    Generate weekly analytics and performance insights
    """
    try:
        current_date = timezone.now().date()
        week_start = current_date - timedelta(days=7)
        
        organizations = Organization.objects.filter(is_active=True)
        analytics_generated = 0
        
        for organization in organizations:
            # Generate weekly analytics
            analytics_data = _generate_weekly_analytics_data(organization, week_start, current_date)
            
            # Send analytics to admins
            notify_admins(
                organization=organization,
                notification_type='weekly_analytics',
                context={
                    'analytics_data': analytics_data,
                    'week_start': week_start,
                    'week_end': current_date,
                    'organization': organization
                }
            )
            
            analytics_generated += 1
        
        logger.info(f"Generated weekly analytics for {analytics_generated} organizations")
        return f"Generated {analytics_generated} weekly analytics reports"
        
    except Exception as exc:
        logger.error(f"Weekly analytics generation failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=7200)


@shared_task(bind=True, max_retries=3)
def generate_presenter_performance_reports(self):
    """
    Generate individual presenter performance reports
    """
    try:
        current_date = timezone.now().date()
        month_start = current_date.replace(day=1)
        
        organizations = Organization.objects.filter(is_active=True)
        reports_generated = 0
        
        for organization in organizations:
            # Get all active presenters
            presenters = Presenter.objects.filter(
                organization=organization,
                is_active=True
            )
            
            for presenter in presenters:
                # Generate presenter performance data
                performance_data = _generate_presenter_performance_data(
                    presenter, month_start, current_date
                )
                
                # Send performance report to presenter and managers
                if performance_data['total_mentions'] > 0:
                    # Send to presenter
                    from apps.core.notifications import NotificationManager
                    notification_manager = NotificationManager()
                    notification_manager.send_notification(
                        notification_type='performance_report',
                        recipients=[presenter.user],
                        context={
                            'performance_data': performance_data,
                            'presenter': presenter,
                            'month_start': month_start,
                            'month_end': current_date
                        },
                        organization=organization
                    )
                    
                    # Send summary to managers
                    notify_managers(
                        organization=organization,
                        notification_type='presenter_performance',
                        context={
                            'performance_data': performance_data,
                            'presenter': presenter,
                            'month_start': month_start,
                            'month_end': current_date
                        }
                    )
                    
                    reports_generated += 1
        
        logger.info(f"Generated {reports_generated} presenter performance reports")
        return f"Generated {reports_generated} presenter performance reports"
        
    except Exception as exc:
        logger.error(f"Presenter performance report generation failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=7200)


@shared_task(bind=True, max_retries=3)
def analyze_system_performance(self):
    """
    Analyze overall system performance and identify trends
    """
    try:
        current_time = timezone.now()
        analysis_start = current_time - timedelta(days=30)
        
        organizations = Organization.objects.filter(is_active=True)
        analyses_completed = 0
        
        for organization in organizations:
            # Perform comprehensive system analysis
            analysis_data = _analyze_system_performance_data(
                organization, analysis_start, current_time
            )
            
            # Send analysis to owners and admins if significant findings
            if analysis_data['needs_attention']:
                notify_admins(
                    organization=organization,
                    notification_type='system_performance_analysis',
                    context={
                        'analysis_data': analysis_data,
                        'analysis_period': 30,
                        'organization': organization
                    }
                )
            
            analyses_completed += 1
        
        logger.info(f"Completed system performance analysis for {analyses_completed} organizations")
        return f"Analyzed system performance for {analyses_completed} organizations"
        
    except Exception as exc:
        logger.error(f"System performance analysis failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=7200)


@shared_task(bind=True, max_retries=3)
def generate_client_reports(self):
    """
    Generate client-specific performance reports
    """
    try:
        current_date = timezone.now().date()
        month_start = current_date.replace(day=1)
        
        organizations = Organization.objects.filter(is_active=True)
        reports_generated = 0
        
        for organization in organizations:
            # Get clients with mentions in the current month
            from apps.core.models import Client
            clients = Client.objects.filter(
                organization=organization,
                is_active=True,
                mentions__mentionreading__scheduled_date__gte=month_start
            ).distinct()
            
            for client in clients:
                # Generate client performance data
                client_data = _generate_client_performance_data(
                    client, month_start, current_date
                )
                
                if client_data['total_mentions'] > 0:
                    # Log client report data (for internal use)
                    ActivityLog.log_activity(
                        user=None,
                        organization=organization,
                        action='client_report_generated',
                        description=f"Monthly report generated for client {client.name}",
                        level='info',
                        content_object=client,
                        metadata=client_data
                    )
                    
                    reports_generated += 1
        
        logger.info(f"Generated {reports_generated} client reports")
        return f"Generated {reports_generated} client reports"
        
    except Exception as exc:
        logger.error(f"Client report generation failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=7200)


# Helper functions for report generation
def _generate_daily_report_data(organization, date):
    """Generate comprehensive daily report data"""
    # Get mentions for the day
    daily_mentions = MentionReading.objects.filter(
        mention__client__organization=organization,
        scheduled_date=date
    )
    
    # Get shows for the day
    daily_shows = Show.objects.filter(
        organization=organization,
        is_active=True,
        days_of_week__contains=[date.weekday()]
    )
    
    # Calculate metrics
    total_mentions = daily_mentions.count()
    completed_mentions = daily_mentions.filter(actual_read_time__isnull=False).count()
    missed_mentions = total_mentions - completed_mentions
    
    # Show metrics
    total_shows = daily_shows.count()
    active_sessions = ShowSession.objects.filter(
        show__organization=organization,
        date=date
    ).count()
    
    # Performance metrics
    completion_rate = (completed_mentions / total_mentions * 100) if total_mentions > 0 else 0
    
    return {
        'date': date,
        'total_mentions': total_mentions,
        'completed_mentions': completed_mentions,
        'missed_mentions': missed_mentions,
        'completion_rate': round(completion_rate, 2),
        'total_shows': total_shows,
        'active_sessions': active_sessions,
        'performance_grade': _calculate_performance_grade(completion_rate),
    }


def _generate_weekly_analytics_data(organization, start_date, end_date):
    """Generate weekly analytics data"""
    # Get weekly mentions
    weekly_mentions = MentionReading.objects.filter(
        mention__client__organization=organization,
        scheduled_date__gte=start_date,
        scheduled_date__lt=end_date
    )
    
    # Calculate trends
    total_mentions = weekly_mentions.count()
    completed_mentions = weekly_mentions.filter(actual_read_time__isnull=False).count()
    
    # Daily breakdown
    daily_breakdown = []
    current_date = start_date
    while current_date < end_date:
        day_mentions = weekly_mentions.filter(scheduled_date=current_date)
        day_completed = day_mentions.filter(actual_read_time__isnull=False).count()
        
        daily_breakdown.append({
            'date': current_date,
            'total': day_mentions.count(),
            'completed': day_completed,
            'rate': (day_completed / day_mentions.count() * 100) if day_mentions.count() > 0 else 0
        })
        
        current_date += timedelta(days=1)
    
    # Top performing presenters
    top_presenters = weekly_mentions.filter(
        actual_read_time__isnull=False
    ).values('presenter__display_name').annotate(
        completed_count=Count('id')
    ).order_by('-completed_count')[:5]
    
    return {
        'total_mentions': total_mentions,
        'completed_mentions': completed_mentions,
        'completion_rate': (completed_mentions / total_mentions * 100) if total_mentions > 0 else 0,
        'daily_breakdown': daily_breakdown,
        'top_presenters': list(top_presenters),
        'trends': _calculate_weekly_trends(daily_breakdown),
    }


def _generate_presenter_performance_data(presenter, start_date, end_date):
    """Generate presenter performance data"""
    # Get presenter's mentions
    presenter_mentions = MentionReading.objects.filter(
        presenter=presenter,
        scheduled_date__gte=start_date,
        scheduled_date__lt=end_date
    )
    
    total_mentions = presenter_mentions.count()
    completed_mentions = presenter_mentions.filter(actual_read_time__isnull=False).count()
    
    # Show sessions
    show_sessions = ShowSession.objects.filter(
        presenter=presenter,
        date__gte=start_date,
        date__lt=end_date
    )
    
    return {
        'total_mentions': total_mentions,
        'completed_mentions': completed_mentions,
        'missed_mentions': total_mentions - completed_mentions,
        'completion_rate': (completed_mentions / total_mentions * 100) if total_mentions > 0 else 0,
        'total_shows': show_sessions.count(),
        'performance_grade': _calculate_performance_grade(
            (completed_mentions / total_mentions * 100) if total_mentions > 0 else 0
        ),
    }


def _generate_client_performance_data(client, start_date, end_date):
    """Generate client performance data"""
    # Get client's mentions
    client_mentions = MentionReading.objects.filter(
        mention__client=client,
        scheduled_date__gte=start_date,
        scheduled_date__lt=end_date
    )
    
    total_mentions = client_mentions.count()
    completed_mentions = client_mentions.filter(actual_read_time__isnull=False).count()
    
    return {
        'total_mentions': total_mentions,
        'completed_mentions': completed_mentions,
        'missed_mentions': total_mentions - completed_mentions,
        'completion_rate': (completed_mentions / total_mentions * 100) if total_mentions > 0 else 0,
    }


def _analyze_system_performance_data(organization, start_date, end_date):
    """Analyze system performance and identify issues"""
    # Get overall metrics
    total_mentions = MentionReading.objects.filter(
        mention__client__organization=organization,
        scheduled_date__gte=start_date.date(),
        scheduled_date__lt=end_date.date()
    ).count()
    
    completed_mentions = MentionReading.objects.filter(
        mention__client__organization=organization,
        scheduled_date__gte=start_date.date(),
        scheduled_date__lt=end_date.date(),
        actual_read_time__isnull=False
    ).count()
    
    completion_rate = (completed_mentions / total_mentions * 100) if total_mentions > 0 else 0
    
    # Identify issues
    needs_attention = False
    issues = []
    
    if completion_rate < 80:
        needs_attention = True
        issues.append(f"Low completion rate: {completion_rate:.1f}%")
    
    if total_mentions < 10:
        needs_attention = True
        issues.append("Very low mention volume")
    
    return {
        'completion_rate': completion_rate,
        'total_mentions': total_mentions,
        'needs_attention': needs_attention,
        'issues': issues,
    }


def _calculate_performance_grade(completion_rate):
    """Calculate performance grade based on completion rate"""
    if completion_rate >= 95:
        return 'A+'
    elif completion_rate >= 90:
        return 'A'
    elif completion_rate >= 85:
        return 'B+'
    elif completion_rate >= 80:
        return 'B'
    elif completion_rate >= 75:
        return 'C+'
    elif completion_rate >= 70:
        return 'C'
    else:
        return 'D'


def _calculate_weekly_trends(daily_breakdown):
    """Calculate weekly trends from daily breakdown"""
    if len(daily_breakdown) < 2:
        return {'trend': 'insufficient_data'}
    
    # Calculate trend based on first half vs second half of week
    first_half = daily_breakdown[:len(daily_breakdown)//2]
    second_half = daily_breakdown[len(daily_breakdown)//2:]
    
    first_avg = sum(day['rate'] for day in first_half) / len(first_half)
    second_avg = sum(day['rate'] for day in second_half) / len(second_half)
    
    if second_avg > first_avg + 5:
        return {'trend': 'improving', 'change': second_avg - first_avg}
    elif second_avg < first_avg - 5:
        return {'trend': 'declining', 'change': first_avg - second_avg}
    else:
        return {'trend': 'stable', 'change': abs(second_avg - first_avg)}
