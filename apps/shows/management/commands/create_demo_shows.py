from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import time
import random

from apps.shows.models import Show, ShowPresenter
from apps.core.models import Presenter
from apps.organizations.models import Organization, Branch


class Command(BaseCommand):
    help = 'Create demo shows for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--organization',
            type=str,
            help='Organization slug to create shows for',
        )
        parser.add_argument(
            '--count',
            type=int,
            default=5,
            help='Number of shows to create (default: 5)',
        )

    def handle(self, *args, **options):
        organization_slug = options.get('organization')
        count = options.get('count', 5)

        if organization_slug:
            try:
                organization = Organization.objects.get(slug=organization_slug)
            except Organization.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Organization with slug "{organization_slug}" not found')
                )
                return
        else:
            organization = Organization.objects.first()
            if not organization:
                self.stdout.write(
                    self.style.ERROR('No organizations found. Please create an organization first.')
                )
                return

        # Get or create a branch
        branch = Branch.objects.filter(organization=organization).first()
        if not branch:
            branch = Branch.objects.create(
                organization=organization,
                name="Main Branch",
                slug="main",
                address="123 Radio Street",
                is_active=True
            )

        # Get presenters for this organization
        presenters = list(Presenter.objects.filter(organization=organization, is_active=True))
        if not presenters:
            self.stdout.write(
                self.style.WARNING('No presenters found. Creating demo presenters...')
            )
            # Create some demo presenters
            demo_presenters = [
                {'first_name': 'John', 'last_name': 'Smith', 'stage_name': 'Johnny Radio'},
                {'first_name': 'Sarah', 'last_name': 'Johnson', 'stage_name': 'Sarah J'},
                {'first_name': 'Mike', 'last_name': 'Davis', 'stage_name': 'Mike D'},
                {'first_name': 'Lisa', 'last_name': 'Wilson', 'stage_name': 'Lisa W'},
            ]
            
            for presenter_data in demo_presenters:
                presenter = Presenter.objects.create(
                    organization=organization,
                    first_name=presenter_data['first_name'],
                    last_name=presenter_data['last_name'],
                    stage_name=presenter_data['stage_name'],
                    email=f"{presenter_data['first_name'].lower()}@{organization.slug}.com",
                    phone='+****************',
                    bio=f"Radio host at {organization.name}",
                    is_active=True
                )
                presenters.append(presenter)

        # Demo show data
        demo_shows = [
            {
                'name': 'Morning Drive',
                'description': 'Start your day with the best music and news',
                'start_time': time(6, 0),
                'end_time': time(10, 0),
                'days_of_week': [0, 1, 2, 3, 4],  # Monday to Friday
            },
            {
                'name': 'Lunch Hour Mix',
                'description': 'Perfect music for your lunch break',
                'start_time': time(12, 0),
                'end_time': time(13, 0),
                'days_of_week': [0, 1, 2, 3, 4],  # Monday to Friday
            },
            {
                'name': 'Afternoon Drive',
                'description': 'Your commute companion',
                'start_time': time(16, 0),
                'end_time': time(19, 0),
                'days_of_week': [0, 1, 2, 3, 4],  # Monday to Friday
            },
            {
                'name': 'Weekend Vibes',
                'description': 'Relaxing music for your weekend',
                'start_time': time(10, 0),
                'end_time': time(14, 0),
                'days_of_week': [5, 6],  # Saturday and Sunday
            },
            {
                'name': 'Late Night Jazz',
                'description': 'Smooth jazz for late night listeners',
                'start_time': time(22, 0),
                'end_time': time(2, 0),
                'days_of_week': [4, 5],  # Friday and Saturday
            },
            {
                'name': 'Sports Talk',
                'description': 'All the latest sports news and analysis',
                'start_time': time(18, 0),
                'end_time': time(20, 0),
                'days_of_week': [0, 2, 4],  # Monday, Wednesday, Friday
            },
            {
                'name': 'Classic Rock Hour',
                'description': 'The best classic rock hits',
                'start_time': time(20, 0),
                'end_time': time(21, 0),
                'days_of_week': [0, 1, 2, 3, 4, 5, 6],  # Every day
            },
            {
                'name': 'Community Corner',
                'description': 'Local news and community events',
                'start_time': time(8, 0),
                'end_time': time(9, 0),
                'days_of_week': [0, 2, 4],  # Monday, Wednesday, Friday
            },
        ]

        created_count = 0
        for i in range(min(count, len(demo_shows))):
            show_data = demo_shows[i]
            
            # Check if show already exists
            if Show.objects.filter(
                organization=organization,
                name=show_data['name']
            ).exists():
                self.stdout.write(
                    self.style.WARNING(f'Show "{show_data["name"]}" already exists, skipping...')
                )
                continue

            # Create the show
            show = Show.objects.create(
                organization=organization,
                branch=branch if random.choice([True, False]) else None,
                name=show_data['name'],
                description=show_data['description'],
                start_time=show_data['start_time'],
                end_time=show_data['end_time'],
                days_of_week=show_data['days_of_week'],
                is_active=True
            )

            # Assign random presenters
            num_presenters = random.randint(1, min(3, len(presenters)))
            selected_presenters = random.sample(presenters, num_presenters)
            
            for j, presenter in enumerate(selected_presenters):
                ShowPresenter.objects.create(
                    show=show,
                    presenter=presenter,
                    role='Host' if j == 0 else random.choice(['Co-Host', 'Producer', 'Guest']),
                    is_primary=j == 0,
                    is_active=True
                )

            created_count += 1
            self.stdout.write(
                self.style.SUCCESS(f'Created show: {show.name}')
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_count} demo shows for {organization.name}'
            )
        )
