"""
Management command to generate time intervals for existing shows.
This command should be run after the migration to populate time intervals for all existing shows.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.shows.models import Show
from apps.organizations.models import Organization


class Command(BaseCommand):
    help = 'Generate time intervals for existing shows to optimize scheduling performance'

    def add_arguments(self, parser):
        parser.add_argument(
            '--organization',
            type=str,
            help='Generate intervals for shows in a specific organization (by slug)',
        )
        parser.add_argument(
            '--interval-seconds',
            type=int,
            default=30,
            help='Interval in seconds between time slots (default: 30)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration of existing intervals',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually creating intervals',
        )

    def handle(self, *args, **options):
        organization_slug = options.get('organization')
        interval_seconds = options.get('interval_seconds', 30)
        force = options.get('force', False)
        dry_run = options.get('dry_run', False)

        # Get shows to process
        shows_queryset = Show.objects.filter(is_active=True)
        
        if organization_slug:
            try:
                organization = Organization.objects.get(slug=organization_slug, is_active=True)
                shows_queryset = shows_queryset.filter(organization=organization)
                self.stdout.write(f"Processing shows for organization: {organization.name}")
            except Organization.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f"Organization with slug '{organization_slug}' not found")
                )
                return
        else:
            self.stdout.write("Processing shows for all organizations")

        shows = shows_queryset.select_related('organization')
        total_shows = shows.count()

        if total_shows == 0:
            self.stdout.write(self.style.WARNING("No active shows found"))
            return

        self.stdout.write(f"Found {total_shows} active shows")
        self.stdout.write(f"Interval: {interval_seconds} seconds")
        self.stdout.write(f"Force regeneration: {force}")
        self.stdout.write(f"Dry run: {dry_run}")
        self.stdout.write("-" * 50)

        total_intervals_created = 0
        shows_processed = 0
        shows_skipped = 0

        for show in shows:
            # Check if intervals already exist
            existing_intervals = show.time_intervals.count()
            
            if existing_intervals > 0 and not force:
                self.stdout.write(
                    f"SKIP: {show.organization.name} - {show.name} "
                    f"(already has {existing_intervals} intervals)"
                )
                shows_skipped += 1
                continue

            if dry_run:
                # Calculate how many intervals would be created
                from datetime import datetime, timedelta
                start_datetime = datetime.combine(datetime.today(), show.start_time)
                end_datetime = datetime.combine(datetime.today(), show.end_time)
                
                if show.end_time <= show.start_time:
                    end_datetime += timedelta(days=1)
                
                duration_seconds = (end_datetime - start_datetime).total_seconds()
                estimated_intervals = int(duration_seconds // interval_seconds)
                
                self.stdout.write(
                    f"DRY RUN: {show.organization.name} - {show.name} "
                    f"(would create ~{estimated_intervals} intervals)"
                )
                shows_processed += 1
                total_intervals_created += estimated_intervals
                continue

            try:
                with transaction.atomic():
                    # Use show's configured interval if not overridden by command line
                    actual_interval = interval_seconds if interval_seconds != 30 else show.time_interval_seconds
                    intervals_created = show.generate_time_intervals(
                        interval_seconds=actual_interval,
                        force_regenerate=force
                    )
                    
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"SUCCESS: {show.organization.name} - {show.name} "
                            f"({intervals_created} intervals created)"
                        )
                    )
                    total_intervals_created += intervals_created
                    shows_processed += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"ERROR: {show.organization.name} - {show.name} "
                        f"({str(e)})"
                    )
                )

        # Summary
        self.stdout.write("-" * 50)
        self.stdout.write(f"Summary:")
        self.stdout.write(f"  Shows processed: {shows_processed}")
        self.stdout.write(f"  Shows skipped: {shows_skipped}")
        self.stdout.write(f"  Total intervals created: {total_intervals_created}")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("This was a dry run - no intervals were actually created"))
        else:
            self.stdout.write(self.style.SUCCESS("Time interval generation completed!"))
