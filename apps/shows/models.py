from django.db import models
from django.urls import reverse
from django.core.cache import cache
from datetime import datetime, timedelta
from apps.core.models import TimeStampedModel, Presenter


class Show(TimeStampedModel):
    """Model for radio shows"""
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='shows'
    )
    branch = models.ForeignKey(
        'organizations.Branch',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='shows'
    )
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    start_time = models.TimeField()
    end_time = models.TimeField()
    days_of_week = models.JSONField(default=list, help_text="List of weekday numbers (0=Monday, 6=Sunday)")

    # Time interval configuration for scheduling optimization
    time_interval_seconds = models.PositiveIntegerField(
        default=30,
        choices=[
            (5, '5 seconds'),
            (10, '10 seconds'),
            (15, '15 seconds'),
            (20, '20 seconds'),
            (30, '30 seconds'),
            (60, '1 minute'),
            (120, '2 minutes'),
            (300, '5 minutes'),
        ],
        help_text="Time interval between available mention slots (affects scheduling precision)"
    )

    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['organization', 'start_time']
        unique_together = ['organization', 'name']
        indexes = [
            models.Index(fields=['organization'], name='show_organization_idx'),
            models.Index(fields=['branch'], name='show_branch_idx'),
            models.Index(fields=['is_active'], name='show_is_active_idx'),
            models.Index(fields=['start_time'], name='show_start_time_idx'),
            models.Index(fields=['end_time'], name='show_end_time_idx'),
            models.Index(fields=['organization', 'is_active'], name='show_org_active_idx'),
            models.Index(fields=['name'], name='show_name_idx'),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('shows:show_detail', kwargs={'pk': self.pk})

    @property
    def duration_minutes(self):
        """Calculate show duration in minutes"""
        from datetime import datetime, timedelta
        start = datetime.combine(datetime.today(), self.start_time)
        end = datetime.combine(datetime.today(), self.end_time)

        # Handle shows that cross midnight
        if end < start:
            end += timedelta(days=1)

        duration = end - start
        return int(duration.total_seconds() / 60)

    @property
    def duration_display(self):
        """Return human-readable duration"""
        minutes = self.duration_minutes
        if minutes >= 60:
            hours = minutes // 60
            remaining_minutes = minutes % 60
            if remaining_minutes > 0:
                return f"{hours}h {remaining_minutes}m"
            else:
                return f"{hours}h"
        else:
            return f"{minutes}m"

    @property
    def weekdays_display(self):
        """Return human-readable weekdays"""
        weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        return ', '.join([weekday_names[day] for day in sorted(self.days_of_week)])

    @property
    def time_interval_display(self):
        """Return human-readable time interval"""
        seconds = self.time_interval_seconds
        if seconds < 60:
            return f"{seconds} seconds"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''}"
        else:
            hours = seconds // 3600
            return f"{hours} hour{'s' if hours != 1 else ''}"

    @property
    def total_mentions(self):
        return self.mentionreading_set.count()

    @property
    def active_presenters(self):
        return self.showpresenter_set.filter(is_active=True)

    def get_primary_presenter(self):
        """Get the primary presenter for this show"""
        try:
            # First try to get the primary presenter
            primary = self.showpresenter_set.filter(is_primary=True, is_active=True).first()
            if primary:
                return primary.presenter

            # If no primary, get any active presenter
            any_presenter = self.showpresenter_set.filter(is_active=True).first()
            if any_presenter:
                return any_presenter.presenter

            # If no presenters assigned to show, return None
            return None
        except:
            return None

    def is_time_within_show(self, time):
        """Check if a given time falls within the show's time frame"""
        from datetime import datetime, timedelta, time as time_obj

        # Special handling for shows ending at midnight (00:00)
        # Treat 00:00 as 24:00 for shows that end at midnight
        end_time = self.end_time
        if self.end_time == time_obj(0, 0, 0):  # 00:00:00
            # For shows ending at midnight, treat as end of day
            if self.start_time > time_obj(0, 0, 0):  # Start time is not midnight
                # This is a show that runs until midnight (e.g., 19:00 - 00:00)
                # Check if time is between start_time and midnight (23:59:59)
                return self.start_time <= time <= time_obj(23, 59, 59)

        # Handle shows that cross midnight (e.g., 22:00 - 02:00)
        if self.end_time < self.start_time:
            # Show crosses midnight
            # Time is valid if it's after start_time OR before end_time
            return time >= self.start_time or time <= self.end_time
        else:
            # Normal show (e.g., 9 AM to 12 PM) or show ending at midnight
            return self.start_time <= time <= self.end_time

    def is_day_valid_for_show(self, date):
        """Check if the given date's weekday is valid for this show"""
        weekday = date.weekday()  # 0=Monday, 6=Sunday

        # If days_of_week is not configured or empty, assume show airs all days
        if not self.days_of_week or not isinstance(self.days_of_week, list) or len(self.days_of_week) == 0:
            return True

        return weekday in self.days_of_week

    def get_time_frame_display(self):
        """Return human-readable time frame"""
        start = self.start_time.strftime('%I:%M %p')
        end = self.end_time.strftime('%I:%M %p')
        return f"{start} - {end}"

    def validate_mention_time(self, scheduled_date, scheduled_time):
        """Validate that a mention can be scheduled at the given date and time"""
        errors = []

        # Check if the day is valid for this show
        if not self.is_day_valid_for_show(scheduled_date):
            weekday_name = scheduled_date.strftime('%A')
            errors.append(f"Show '{self.name}' does not air on {weekday_name}s. Valid days: {self.weekdays_display}")

        # Check if the time is within the show's time frame
        if not self.is_time_within_show(scheduled_time):
            time_str = scheduled_time.strftime('%I:%M %p')
            errors.append(f"Time {time_str} is outside show '{self.name}' time frame ({self.get_time_frame_display()})")

        return errors

    def generate_time_intervals(self, interval_seconds=None, force_regenerate=False):
        """Generate and store pre-calculated time intervals for this show"""
        # Use show's configured interval if not specified
        if interval_seconds is None:
            interval_seconds = self.time_interval_seconds

        # Check if intervals already exist and force_regenerate is False
        if not force_regenerate and self.time_intervals.exists():
            return self.time_intervals.count()

        # Clear existing intervals if regenerating
        if force_regenerate:
            self.time_intervals.all().delete()

        # Generate time slots
        start_datetime = datetime.combine(datetime.today(), self.start_time)
        end_datetime = datetime.combine(datetime.today(), self.end_time)

        # Handle shows that cross midnight
        if self.end_time <= self.start_time:
            end_datetime += timedelta(days=1)

        # Limit maximum slots to prevent excessive data
        max_duration_hours = 12  # Maximum 12-hour shows
        max_slots = (max_duration_hours * 3600) // interval_seconds

        current_time = start_datetime
        slot_count = 0
        intervals_created = 0

        time_intervals_to_create = []

        while current_time < end_datetime and slot_count < max_slots:
            time_obj = current_time.time()

            # Create time interval object
            time_interval = ShowTimeInterval(
                show=self,
                time_slot=time_obj,
                interval_seconds=interval_seconds,
                slot_order=slot_count,
                is_available=True  # Default to available, will be updated by availability checks
            )
            time_intervals_to_create.append(time_interval)

            current_time += timedelta(seconds=interval_seconds)
            slot_count += 1

        # Bulk create for performance
        if time_intervals_to_create:
            ShowTimeInterval.objects.bulk_create(time_intervals_to_create, batch_size=100)
            intervals_created = len(time_intervals_to_create)

        # Clear cache for this show
        self._clear_time_intervals_cache()

        return intervals_created

    def get_cached_time_intervals(self, weekday=None, exclude_recurring_id=None):
        """Get cached time intervals for this show with availability checking"""
        cache_key = f"show_time_intervals_{self.id}_{weekday}_{exclude_recurring_id}"
        cached_intervals = cache.get(cache_key)

        if cached_intervals is not None:
            return cached_intervals

        # Get pre-calculated intervals
        intervals = self.time_intervals.all().order_by('slot_order')

        # Batch availability checking for better performance
        unavailable_times = self._get_unavailable_times_batch(weekday, exclude_recurring_id)

        # Convert to the format expected by the frontend
        time_slots = []
        for interval in intervals:
            is_available = interval.time_slot not in unavailable_times

            time_slots.append({
                'time': interval.time_slot.strftime('%H:%M:%S'),
                'time_display': interval.time_display_short,
                'is_available': is_available,
                'conflicts': [],  # Will be populated by conflict checking if needed
                'warnings': []
            })

        # Cache for 5 minutes
        cache.set(cache_key, time_slots, 300)
        return time_slots

    def _get_unavailable_times_batch(self, weekday, exclude_recurring_id=None):
        """Get all unavailable times for this show in a batch query for better performance"""
        from apps.mentions.models import RecurringMentionShow, MentionReading

        unavailable_times = set()

        # Batch query for recurring mentions
        recurring_assignments = RecurringMentionShow.objects.filter(
            show=self,
            recurring_mention__client__organization=self.organization,
            recurring_mention__is_active=True,
            is_active=True
        ).select_related('recurring_mention')

        if exclude_recurring_id:
            recurring_assignments = recurring_assignments.exclude(
                recurring_mention_id=exclude_recurring_id
            )

        for assignment in recurring_assignments:
            unavailable_times.add(assignment.scheduled_time)

        # Batch query for existing mentions (limited to next 7 days for performance)
        if weekday is not None:
            today = datetime.now().date()
            check_dates = []
            for days_ahead in range(0, 7):  # Reduced from 30 to 7 days for performance
                check_date = today + timedelta(days=days_ahead)
                if check_date.weekday() == weekday:
                    check_dates.append(check_date)

            if check_dates:
                existing_readings = MentionReading.objects.filter(
                    show=self,
                    scheduled_date__in=check_dates,
                    actual_read_time__isnull=True,
                    mention__client__organization=self.organization
                ).values_list('scheduled_time', flat=True)

                unavailable_times.update(existing_readings)

        return unavailable_times

    def _check_interval_availability(self, time_obj, weekday, exclude_recurring_id=None):
        """Check if a specific time interval is available"""
        from apps.mentions.models import RecurringMentionShow, MentionReading

        # Check for existing recurring mentions on this show/day/time
        recurring_assignments = RecurringMentionShow.objects.filter(
            show=self,
            scheduled_time=time_obj,
            recurring_mention__client__organization=self.organization,
            recurring_mention__is_active=True,
            is_active=True
        )

        if exclude_recurring_id:
            recurring_assignments = recurring_assignments.exclude(
                recurring_mention_id=exclude_recurring_id
            )

        if recurring_assignments.exists():
            return False

        # Quick check for existing mentions (limited to next 7 days for performance)
        if weekday is not None:
            today = datetime.now().date()
            for days_ahead in range(0, 7):  # Reduced from 30 to 7 days for performance
                check_date = today + timedelta(days=days_ahead)
                if check_date.weekday() == weekday:
                    existing_readings = MentionReading.objects.filter(
                        show=self,
                        scheduled_date=check_date,
                        scheduled_time=time_obj,
                        actual_read_time__isnull=True,
                        mention__client__organization=self.organization
                    ).exists()

                    if existing_readings:
                        return False

        return True

    def _clear_time_intervals_cache(self):
        """Clear cached time intervals for this show"""
        # Clear all possible cache variations
        for weekday in range(7):  # 0-6 for Monday-Sunday
            for exclude_id in [None, 'any']:  # Common cache variations
                cache_key = f"show_time_intervals_{self.id}_{weekday}_{exclude_id}"
                cache.delete(cache_key)

    def update_time_intervals_on_change(self):
        """Update time intervals when show times change"""
        # Regenerate intervals if the show times have changed
        if self.time_intervals.exists():
            # Check if we need to regenerate based on time changes
            first_interval = self.time_intervals.first()
            last_interval = self.time_intervals.last()

            # If start/end times don't match the intervals, regenerate
            if (first_interval and first_interval.time_slot != self.start_time) or \
               (last_interval and not self.is_time_within_show(last_interval.time_slot)):
                self.generate_time_intervals(force_regenerate=True)
        else:
            # No intervals exist, generate them
            self.generate_time_intervals()


class ShowPresenter(TimeStampedModel):
    """Through model for Show-Presenter relationship"""
    show = models.ForeignKey(Show, on_delete=models.CASCADE)
    presenter = models.ForeignKey(Presenter, on_delete=models.CASCADE)
    role = models.CharField(max_length=50, default='Host')
    is_primary = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['show', 'presenter']
        ordering = ['-is_primary', 'role']

    def __str__(self):
        return f"{self.presenter.display_name} - {self.show.name} ({self.role})"


class ShowSession(TimeStampedModel):
    """Track actual show sessions with start/end times"""
    show = models.ForeignKey(Show, on_delete=models.CASCADE, related_name='sessions')
    presenter = models.ForeignKey('core.Presenter', on_delete=models.CASCADE)
    date = models.DateField()
    actual_start_time = models.DateTimeField()
    actual_end_time = models.DateTimeField(null=True, blank=True)
    ended_early = models.BooleanField(default=False)
    end_reason = models.CharField(
        max_length=50,
        choices=[
            ('natural', 'Natural End'),
            ('early', 'Ended Early'),
            ('technical', 'Technical Issues'),
            ('emergency', 'Emergency'),
        ],
        default='natural'
    )
    notes = models.TextField(blank=True)

    class Meta:
        unique_together = ['show', 'presenter', 'date']
        ordering = ['-date', '-actual_start_time']

    def __str__(self):
        return f"{self.show.name} - {self.presenter.display_name} ({self.date})"

    @property
    def duration_minutes(self):
        """Calculate actual duration in minutes"""
        if not self.actual_end_time:
            return None
        duration = self.actual_end_time - self.actual_start_time
        return int(duration.total_seconds() / 60)

    @property
    def is_active(self):
        """Check if session is currently active (started but not ended)"""
        return self.actual_start_time and not self.actual_end_time


class ShowTimeInterval(TimeStampedModel):
    """Pre-calculated time intervals for shows to optimize scheduling performance"""
    show = models.ForeignKey(Show, on_delete=models.CASCADE, related_name='time_intervals')
    time_slot = models.TimeField(help_text="Pre-calculated time slot within the show")
    interval_seconds = models.PositiveIntegerField(default=30, help_text="Interval in seconds (e.g., 30 for 30-second intervals)")
    is_available = models.BooleanField(default=True, help_text="Whether this time slot is generally available")

    # Metadata for optimization
    slot_order = models.PositiveIntegerField(help_text="Order of this slot within the show (for sorting)")

    class Meta:
        unique_together = ['show', 'time_slot']
        ordering = ['show', 'slot_order', 'time_slot']
        indexes = [
            models.Index(fields=['show'], name='show_time_int_show_idx'),
            models.Index(fields=['show', 'is_available'], name='show_time_int_avail_idx'),
            models.Index(fields=['time_slot'], name='show_time_int_time_idx'),
            models.Index(fields=['slot_order'], name='show_time_int_order_idx'),
        ]

    def __str__(self):
        return f"{self.show.name} - {self.time_slot.strftime('%H:%M:%S')}"

    @property
    def time_display(self):
        """Return formatted time for display"""
        return self.time_slot.strftime('%I:%M:%S %p')

    @property
    def time_display_short(self):
        """Return short formatted time for display (no seconds if :00)"""
        if self.time_slot.second == 0:
            return self.time_slot.strftime('%I:%M %p')
        return self.time_slot.strftime('%I:%M:%S %p')
