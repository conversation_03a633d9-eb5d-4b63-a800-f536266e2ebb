from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from datetime import time

from .models import Show, ShowPresenter
from .forms import ShowForm, ShowFilterForm
from apps.core.models import Presenter
from apps.organizations.models import Organization, Branch, OrganizationMembership


class ShowModelTest(TestCase):
    """Test Show model functionality"""

    def setUp(self):
        self.organization = Organization.objects.create(
            name="Test Radio Station",
            slug="test-radio"
        )
        self.branch = Branch.objects.create(
            organization=self.organization,
            name="Main Branch",
            slug="main"
        )
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        OrganizationMembership.objects.create(
            user=self.user,
            organization=self.organization,
            role='admin',
            is_active=True,
            is_default=True
        )

    def test_show_creation(self):
        """Test creating a show"""
        show = Show.objects.create(
            organization=self.organization,
            branch=self.branch,
            name="Morning Drive",
            description="Popular morning show",
            start_time=time(6, 0),
            end_time=time(10, 0),
            days_of_week=[0, 1, 2, 3, 4],  # Monday to Friday
            is_active=True
        )

        self.assertEqual(show.name, "Morning Drive")
        self.assertEqual(show.organization, self.organization)
        self.assertEqual(show.branch, self.branch)
        self.assertEqual(show.duration_minutes, 240)  # 4 hours
        self.assertEqual(show.duration_display, "4h")
        self.assertIn("Monday", show.weekdays_display)
        self.assertIn("Friday", show.weekdays_display)

    def test_show_str_representation(self):
        """Test string representation of show"""
        show = Show.objects.create(
            organization=self.organization,
            name="Test Show",
            start_time=time(12, 0),
            end_time=time(13, 0),
            days_of_week=[0]
        )
        self.assertEqual(str(show), "Test Show")

    def test_show_absolute_url(self):
        """Test get_absolute_url method"""
        show = Show.objects.create(
            organization=self.organization,
            name="Test Show",
            start_time=time(12, 0),
            end_time=time(13, 0),
            days_of_week=[0]
        )
        expected_url = reverse('shows:show_detail', kwargs={'pk': show.pk})
        self.assertEqual(show.get_absolute_url(), expected_url)


class ShowFormTest(TestCase):
    """Test Show forms"""

    def setUp(self):
        self.organization = Organization.objects.create(
            name="Test Radio Station",
            slug="test-radio"
        )
        self.branch = Branch.objects.create(
            organization=self.organization,
            name="Main Branch",
            slug="main"
        )
        self.presenter = Presenter.objects.create(
            organization=self.organization,
            first_name="John",
            last_name="Doe",
            email="<EMAIL>"
        )

    def test_show_form_valid_data(self):
        """Test form with valid data"""
        form_data = {
            'name': 'Test Show',
            'description': 'A test show',
            'start_time': '09:00',
            'end_time': '10:00',
            'days_of_week': ['0', '1', '2'],  # Mon, Tue, Wed
            'branch': self.branch.pk,
            'is_active': True,
            'presenters': [self.presenter.pk]
        }

        form = ShowForm(data=form_data, organization=self.organization)
        self.assertTrue(form.is_valid())

    def test_show_form_invalid_time_range(self):
        """Test form with invalid time range"""
        form_data = {
            'name': 'Test Show',
            'start_time': '10:00',
            'end_time': '09:00',  # End before start
            'days_of_week': ['0'],
            'is_active': True
        }

        form = ShowForm(data=form_data, organization=self.organization)
        self.assertFalse(form.is_valid())

    def test_show_form_no_days_selected(self):
        """Test form with no days selected"""
        form_data = {
            'name': 'Test Show',
            'start_time': '09:00',
            'end_time': '10:00',
            'days_of_week': [],  # No days selected
            'is_active': True
        }

        form = ShowForm(data=form_data, organization=self.organization)
        self.assertFalse(form.is_valid())
        self.assertIn('Please select at least one day for the show', str(form.errors))


class ShowViewTest(TestCase):
    """Test Show views"""

    def setUp(self):
        self.client = Client()
        self.organization = Organization.objects.create(
            name="Test Radio Station",
            slug="test-radio"
        )
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        OrganizationMembership.objects.create(
            user=self.user,
            organization=self.organization,
            role='admin',
            is_active=True,
            is_default=True
        )

        self.show = Show.objects.create(
            organization=self.organization,
            name="Test Show",
            description="A test show",
            start_time=time(9, 0),
            end_time=time(10, 0),
            days_of_week=[0, 1, 2],
            is_active=True
        )

    def test_show_list_view_requires_login(self):
        """Test that show list requires login"""
        response = self.client.get(reverse('shows:show_list'))
        self.assertEqual(response.status_code, 302)  # Redirect to login

    def test_show_list_view_authenticated(self):
        """Test show list view when authenticated"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('shows:show_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Show")

    def test_show_detail_view(self):
        """Test show detail view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('shows:show_detail', kwargs={'pk': self.show.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.show.name)

    def test_show_create_view_get(self):
        """Test show create view GET request"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('shows:show_create'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Create New Show")

    def test_show_analytics_view(self):
        """Test show analytics view"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('shows:show_analytics', kwargs={'pk': self.show.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"{self.show.name} Analytics")
