# Generated by Django 4.2.7 on 2025-07-03 19:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("shows", "0004_add_production_indexes"),
    ]

    operations = [
        migrations.CreateModel(
            name="ShowTimeInterval",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "time_slot",
                    models.TimeField(
                        help_text="Pre-calculated time slot within the show"
                    ),
                ),
                (
                    "interval_seconds",
                    models.PositiveIntegerField(
                        default=30,
                        help_text="Interval in seconds (e.g., 30 for 30-second intervals)",
                    ),
                ),
                (
                    "is_available",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this time slot is generally available",
                    ),
                ),
                (
                    "slot_order",
                    models.PositiveIntegerField(
                        help_text="Order of this slot within the show (for sorting)"
                    ),
                ),
                (
                    "show",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="time_intervals",
                        to="shows.show",
                    ),
                ),
            ],
            options={
                "ordering": ["show", "slot_order", "time_slot"],
                "indexes": [
                    models.Index(fields=["show"], name="show_time_int_show_idx"),
                    models.Index(
                        fields=["show", "is_available"], name="show_time_int_avail_idx"
                    ),
                    models.Index(fields=["time_slot"], name="show_time_int_time_idx"),
                    models.Index(fields=["slot_order"], name="show_time_int_order_idx"),
                ],
                "unique_together": {("show", "time_slot")},
            },
        ),
    ]
