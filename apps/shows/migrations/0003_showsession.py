# Generated by Django 4.2.7 on 2025-06-14 21:49

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0001_initial"),
        ("shows", "0002_fix_days_of_week"),
    ]

    operations = [
        migrations.CreateModel(
            name="ShowSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("date", models.DateField()),
                ("actual_start_time", models.DateTimeField()),
                ("actual_end_time", models.DateTimeField(blank=True, null=True)),
                ("ended_early", models.BooleanField(default=False)),
                (
                    "end_reason",
                    models.CharField(
                        choices=[
                            ("natural", "Natural End"),
                            ("early", "Ended Early"),
                            ("technical", "Technical Issues"),
                            ("emergency", "Emergency"),
                        ],
                        default="natural",
                        max_length=50,
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                (
                    "presenter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.presenter"
                    ),
                ),
                (
                    "show",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="shows.show",
                    ),
                ),
            ],
            options={
                "ordering": ["-date", "-actual_start_time"],
                "unique_together": {("show", "presenter", "date")},
            },
        ),
    ]
