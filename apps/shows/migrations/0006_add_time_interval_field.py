# Generated by Django 4.2.7 on 2025-07-03 19:47

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("shows", "0005_add_show_time_intervals"),
    ]

    operations = [
        migrations.AddField(
            model_name="show",
            name="time_interval_seconds",
            field=models.PositiveIntegerField(
                choices=[
                    (5, "5 seconds"),
                    (10, "10 seconds"),
                    (15, "15 seconds"),
                    (20, "20 seconds"),
                    (30, "30 seconds"),
                    (60, "1 minute"),
                    (120, "2 minutes"),
                    (300, "5 minutes"),
                ],
                default=30,
                help_text="Time interval between available mention slots (affects scheduling precision)",
            ),
        ),
    ]
