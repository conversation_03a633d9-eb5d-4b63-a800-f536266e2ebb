# Generated by Django 4.2.7 on 2025-07-09 21:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0010_add_color_to_industry"),
        ("shows", "0006_add_time_interval_field"),
    ]

    operations = [
        migrations.CreateModel(
            name="PresenterShowNote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "date",
                    models.DateField(help_text="Date for which this note applies"),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True,
                        help_text="Presenter's preparation notes for this show",
                    ),
                ),
                (
                    "presenter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="show_notes",
                        to="core.presenter",
                    ),
                ),
                (
                    "show",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="presenter_notes",
                        to="shows.show",
                    ),
                ),
            ],
            options={
                "ordering": ["-date", "show__start_time"],
                "indexes": [
                    models.Index(fields=["presenter"], name="pres_show_note_pres_idx"),
                    models.Index(fields=["show"], name="pres_show_note_show_idx"),
                    models.Index(fields=["date"], name="pres_show_note_date_idx"),
                    models.Index(
                        fields=["presenter", "date"],
                        name="pres_show_note_pres_date_idx",
                    ),
                ],
                "unique_together": {("presenter", "show", "date")},
            },
        ),
    ]
