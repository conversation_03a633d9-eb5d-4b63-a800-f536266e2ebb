# Generated migration to fix days_of_week field issues

from django.db import migrations


def fix_days_of_week(apps, schema_editor):
    """Fix shows that have invalid days_of_week configurations"""
    Show = apps.get_model('shows', 'Show')
    
    for show in Show.objects.all():
        needs_update = False
        
        # Check if days_of_week is None, not a list, or empty
        if show.days_of_week is None:
            # Set to all days (0-6 for Monday-Sunday)
            show.days_of_week = [0, 1, 2, 3, 4, 5, 6]
            needs_update = True
        elif not isinstance(show.days_of_week, list):
            # Convert to list if it's not already
            show.days_of_week = [0, 1, 2, 3, 4, 5, 6]
            needs_update = True
        elif len(show.days_of_week) == 0:
            # If empty list, set to all days
            show.days_of_week = [0, 1, 2, 3, 4, 5, 6]
            needs_update = True
        else:
            # Check for invalid weekday numbers
            valid_days = []
            for day in show.days_of_week:
                if isinstance(day, int) and 0 <= day <= 6:
                    valid_days.append(day)
                else:
                    needs_update = True
            
            if needs_update:
                if valid_days:
                    show.days_of_week = valid_days
                else:
                    # If no valid days found, set to all days
                    show.days_of_week = [0, 1, 2, 3, 4, 5, 6]
        
        if needs_update:
            show.save(update_fields=['days_of_week'])


def reverse_fix_days_of_week(apps, schema_editor):
    """Reverse migration - no action needed"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('shows', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(fix_days_of_week, reverse_fix_days_of_week),
    ]
