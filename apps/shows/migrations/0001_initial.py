# Generated by Django 4.2.7 on 2025-06-11 07:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("core", "0001_initial"),
        ("organizations", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Show",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                ("start_time", models.TimeField()),
                ("end_time", models.TimeField()),
                (
                    "days_of_week",
                    models.JSONField(
                        default=list,
                        help_text="List of weekday numbers (0=Monday, 6=Sunday)",
                    ),
                ),
                ("is_active", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                (
                    "branch",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="shows",
                        to="organizations.branch",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="shows",
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "ordering": ["organization", "start_time"],
                "unique_together": {("organization", "name")},
            },
        ),
        migrations.CreateModel(
            name="ShowPresenter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("role", models.CharField(default="Host", max_length=50)),
                ("is_primary", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "presenter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.presenter"
                    ),
                ),
                (
                    "show",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="shows.show"
                    ),
                ),
            ],
            options={
                "ordering": ["-is_primary", "role"],
                "unique_together": {("show", "presenter")},
            },
        ),
    ]
