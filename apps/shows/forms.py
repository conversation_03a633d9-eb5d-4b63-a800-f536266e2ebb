from django import forms
from django.core.exceptions import ValidationError
from datetime import datetime, timedelta

from .models import Show, ShowPresenter
from apps.core.models import Presenter
from apps.organizations.models import Organization, Branch


class ShowForm(forms.ModelForm):
    """Form for creating and editing shows"""
    
    # Custom fields for better UI
    days_of_week = forms.MultipleChoiceField(
        choices=[
            (0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'),
            (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')
        ],
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'day-checkbox-input'
        }),
        required=True,
        help_text="Select the days when this show airs"
    )
    
    presenters = forms.ModelMultipleChoiceField(
        queryset=Presenter.objects.none(),  # Will be set in __init__
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'presenter-checkbox'
        }),
        required=False,
        help_text="Select presenters for this show"
    )
    
    branch = forms.ModelChoiceField(
        queryset=Branch.objects.none(),  # Will be set in __init__
        required=False,
        empty_label="No specific branch",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )

    class Meta:
        model = Show
        fields = ['name', 'description', 'start_time', 'end_time', 'days_of_week', 'time_interval_seconds', 'branch', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter show name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Brief description of the show...',
                'rows': 3
            }),
            'start_time': forms.TimeInput(attrs={
                'type': 'time',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'end_time': forms.TimeInput(attrs={
                'type': 'time',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'time_interval_seconds': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            })
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        if self.organization:
            # Filter presenters by organization
            self.fields['presenters'].queryset = Presenter.objects.filter(
                organization=self.organization,
                is_active=True
            ).order_by('first_name', 'last_name')
            
            # Filter branches by organization
            self.fields['branch'].queryset = Branch.objects.filter(
                organization=self.organization,
                is_active=True
            ).order_by('name')
        
        # Convert days_of_week from JSONField to list for form
        if self.instance and self.instance.pk and self.instance.days_of_week:
            self.initial['days_of_week'] = [str(day) for day in self.instance.days_of_week]
        
        # Set initial presenters if editing
        if self.instance and self.instance.pk:
            self.initial['presenters'] = self.instance.showpresenter_set.filter(
                is_active=True
            ).values_list('presenter__pk', flat=True)

    def clean_days_of_week(self):
        """Convert string values back to integers"""
        days = self.cleaned_data.get('days_of_week', [])
        try:
            return [int(day) for day in days]
        except (ValueError, TypeError):
            raise ValidationError("Invalid day selection")

    def clean(self):
        """Custom validation for the form"""
        cleaned_data = super().clean()
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')
        name = cleaned_data.get('name')
        days_of_week = cleaned_data.get('days_of_week')

        # Validate time range
        if start_time and end_time:
            # Convert to datetime for comparison
            start_dt = datetime.combine(datetime.today(), start_time)
            end_dt = datetime.combine(datetime.today(), end_time)
            
            # Handle overnight shows
            if end_dt <= start_dt:
                end_dt += timedelta(days=1)
            
            duration = end_dt - start_dt
            duration_minutes = int(duration.total_seconds() / 60)
            
            # Validate reasonable duration (15 minutes to 12 hours)
            if duration_minutes < 15:
                raise ValidationError("Show duration must be at least 15 minutes")
            elif duration_minutes > 720:  # 12 hours
                raise ValidationError("Show duration cannot exceed 12 hours")

        # Validate unique name within organization
        if name and self.organization:
            existing_shows = Show.objects.filter(
                organization=self.organization,
                name__iexact=name
            )
            if self.instance and self.instance.pk:
                existing_shows = existing_shows.exclude(pk=self.instance.pk)
            
            if existing_shows.exists():
                raise ValidationError("A show with this name already exists in your organization")

        # Validate at least one day selected
        if not days_of_week:
            raise ValidationError("Please select at least one day for the show")

        return cleaned_data

    def save(self, commit=True):
        """Save the show and handle presenter relationships"""
        show = super().save(commit=False)
        
        # Set organization if not already set
        if self.organization and not show.organization_id:
            show.organization = self.organization
        
        if commit:
            show.save()
            
            # Handle presenter relationships
            if 'presenters' in self.cleaned_data:
                # Clear existing relationships
                ShowPresenter.objects.filter(show=show).delete()
                
                # Create new relationships
                for presenter in self.cleaned_data['presenters']:
                    ShowPresenter.objects.create(
                        show=show,
                        presenter=presenter,
                        role='Host',
                        is_active=True
                    )
        
        return show


class ShowFilterForm(forms.Form):
    """Form for filtering shows in list view"""
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
            'placeholder': 'Search shows...'
        })
    )
    
    status = forms.ChoiceField(
        choices=[
            ('', 'All Shows'),
            ('active', 'Active'),
            ('inactive', 'Inactive')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )
    
    branch = forms.ModelChoiceField(
        queryset=Branch.objects.none(),
        required=False,
        empty_label="All Branches",
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )
    
    presenter = forms.ModelChoiceField(
        queryset=Presenter.objects.none(),
        required=False,
        empty_label="All Presenters",
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        
        if organization:
            self.fields['branch'].queryset = Branch.objects.filter(
                organization=organization,
                is_active=True
            ).order_by('name')
            
            self.fields['presenter'].queryset = Presenter.objects.filter(
                organization=organization,
                is_active=True
            ).order_by('first_name', 'last_name')
