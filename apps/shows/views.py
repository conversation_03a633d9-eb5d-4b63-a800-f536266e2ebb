from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Count, Avg, Q, Case, When, Value, CharField
from django.db.models.functions import Extract
from django.db import models
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from datetime import datetime, timedelta

from .models import Show, ShowPresenter
from .forms import ShowForm, ShowFilterForm
from apps.core.models import Presenter
from apps.mentions.models import MentionReading
from apps.organizations.middleware import get_current_organization, OrganizationDataFilterMixin
from apps.core.decorators import require_permission, require_any_permission


@method_decorator(require_any_permission(['view', 'manage_shows']), name='dispatch')
class ShowListView(LoginRequiredMixin, OrganizationDataFilterMixin, ListView):
    """List shows with filtering and search"""
    model = Show
    template_name = 'shows/show_list.html'
    context_object_name = 'shows'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()

        # Get filter parameters
        search = self.request.GET.get('search', '')
        status = self.request.GET.get('status', '')
        branch_id = self.request.GET.get('branch', '')
        presenter_id = self.request.GET.get('presenter', '')

        # Apply search filter
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        # Apply status filter
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)

        # Apply branch filter
        if branch_id:
            queryset = queryset.filter(branch_id=branch_id)

        # Apply presenter filter
        if presenter_id:
            queryset = queryset.filter(
                showpresenter__presenter_id=presenter_id,
                showpresenter__is_active=True
            )

        return queryset.select_related('organization', 'branch').prefetch_related(
            'showpresenter_set__presenter'
        ).order_by('start_time')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        organization = get_current_organization(self.request)

        # Add filter form
        context['filter_form'] = ShowFilterForm(
            data=self.request.GET or None,
            organization=organization
        )

        # Add statistics
        shows = context['shows']
        context['total_shows'] = shows.count() if hasattr(shows, 'count') else len(shows)
        context['active_shows'] = Show.objects.filter(
            organization=organization,
            is_active=True
        ).count() if organization else 0

        return context


# Keep the function-based view for backward compatibility
@login_required
@require_any_permission(['view', 'manage_shows'])
def show_list(request):
    """List all shows - redirects to class-based view"""
    return ShowListView.as_view()(request)


@method_decorator(require_any_permission(['view', 'manage_shows']), name='dispatch')
class ShowDetailView(LoginRequiredMixin, OrganizationDataFilterMixin, DetailView):
    """Show detail view with organization filtering"""
    model = Show
    template_name = 'shows/show_detail.html'
    context_object_name = 'show'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        show = self.object

        # Get recent mentions for this show
        recent_mentions = show.mentionreading_set.select_related(
            'mention', 'mention__client', 'presenter'
        ).order_by('-scheduled_date')[:10]

        # Calculate statistics
        total_mentions = show.mentionreading_set.count()
        completed_mentions = show.mentionreading_set.filter(actual_read_time__isnull=False).count()
        scheduled_mentions = show.mentionreading_set.filter(actual_read_time__isnull=True).count()
        avg_duration = show.mentionreading_set.aggregate(
            avg=Avg('mention__duration_seconds')
        )['avg'] or 0

        # Days of week for template
        days_of_week = [
            (0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'),
            (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')
        ]

        context.update({
            'recent_mentions': recent_mentions,
            'total_mentions': total_mentions,
            'completed_mentions': completed_mentions,
            'scheduled_mentions': scheduled_mentions,
            'avg_duration': avg_duration,
            'days_of_week': days_of_week,
        })
        return context


# Keep the function-based view for backward compatibility
@login_required
@require_any_permission(['view', 'manage_shows'])
def show_detail(request, pk):
    """Show detail view - redirects to class-based view"""
    return ShowDetailView.as_view()(request, pk=pk)


@method_decorator(require_permission('manage_shows'), name='dispatch')
class ShowCreateView(LoginRequiredMixin, OrganizationDataFilterMixin, CreateView):
    """Create new show with organization filtering"""
    model = Show
    form_class = ShowForm
    template_name = 'shows/show_form.html'
    success_url = reverse_lazy('shows:show_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['organization'] = get_current_organization(self.request)
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Show created successfully!')
        return super().form_valid(form)

    def form_invalid(self, form):
        messages.error(self.request, 'Please correct the errors below.')
        return super().form_invalid(form)

    def get_success_url(self):
        return reverse_lazy('shows:show_detail', kwargs={'pk': self.object.pk})


# Keep the function-based view for backward compatibility
@login_required
@require_permission('manage_shows')
def show_create(request):
    """Create new show - redirects to class-based view"""
    return ShowCreateView.as_view()(request)


@method_decorator(require_permission('manage_shows'), name='dispatch')
class ShowUpdateView(LoginRequiredMixin, OrganizationDataFilterMixin, UpdateView):
    """Update show with organization filtering"""
    model = Show
    form_class = ShowForm
    template_name = 'shows/show_form.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['organization'] = get_current_organization(self.request)
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        messages.success(self.request, 'Show updated successfully!')
        return super().form_valid(form)

    def form_invalid(self, form):
        messages.error(self.request, 'Please correct the errors below.')
        return super().form_invalid(form)

    def get_success_url(self):
        return reverse_lazy('shows:show_detail', kwargs={'pk': self.object.pk})


# Keep the function-based view for backward compatibility
@login_required
@require_permission('manage_shows')
def show_edit(request, pk):
    """Edit show - redirects to class-based view"""
    return ShowUpdateView.as_view()(request, pk=pk)


@method_decorator(require_permission('manage_shows'), name='dispatch')
class ShowDeleteView(LoginRequiredMixin, OrganizationDataFilterMixin, DeleteView):
    """Delete show with organization filtering"""
    model = Show
    template_name = 'shows/show_confirm_delete.html'
    success_url = reverse_lazy('shows:show_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Show deleted successfully!')
        return super().delete(request, *args, **kwargs)


# Keep the function-based view for backward compatibility
@login_required
@require_permission('manage_shows')
def show_delete(request, pk):
    """Delete show - redirects to class-based view"""
    return ShowDeleteView.as_view()(request, pk=pk)


# Additional views for enhanced functionality
class ShowAnalyticsView(LoginRequiredMixin, OrganizationDataFilterMixin, DetailView):
    """Show analytics view"""
    model = Show
    template_name = 'shows/show_analytics.html'
    context_object_name = 'show'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        show = self.object

        # Get analytics data
        mentions = show.mentionreading_set.all()

        # Time-based analytics - PostgreSQL compatible
        context['mentions_by_month'] = mentions.annotate(
            year=Extract('scheduled_date', 'year'),
            month=Extract('scheduled_date', 'month')
        ).values('year', 'month').annotate(
            count=Count('id'),
            month_name=models.Case(
                models.When(month=1, then=models.Value('Jan')),
                models.When(month=2, then=models.Value('Feb')),
                models.When(month=3, then=models.Value('Mar')),
                models.When(month=4, then=models.Value('Apr')),
                models.When(month=5, then=models.Value('May')),
                models.When(month=6, then=models.Value('Jun')),
                models.When(month=7, then=models.Value('Jul')),
                models.When(month=8, then=models.Value('Aug')),
                models.When(month=9, then=models.Value('Sep')),
                models.When(month=10, then=models.Value('Oct')),
                models.When(month=11, then=models.Value('Nov')),
                models.When(month=12, then=models.Value('Dec')),
                default=models.Value('Unknown'),
                output_field=models.CharField()
            )
        ).order_by('year', 'month')

        # Performance analytics
        total_mentions = mentions.count()
        context['completion_rate'] = (
            mentions.filter(actual_read_time__isnull=False).count() /
            max(total_mentions, 1) * 100
        )

        # Additional analytics
        context['total_mentions'] = total_mentions
        context['completed_mentions'] = mentions.filter(actual_read_time__isnull=False).count()
        context['pending_mentions'] = mentions.filter(actual_read_time__isnull=True).count()

        # Presenter analytics
        context['presenter_stats'] = show.showpresenter_set.filter(
            is_active=True
        ).annotate(
            mention_count=Count('presenter__mentionreading')
        ).order_by('-mention_count')

        return context


@login_required
def show_analytics(request, pk):
    """Show analytics - redirects to class-based view"""
    return ShowAnalyticsView.as_view()(request, pk=pk)
