"""
Django signals for the shows app.
Handles automatic time interval generation when shows are created or updated.
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.core.cache import cache
from .models import Show, ShowTimeInterval
import logging

logger = logging.getLogger(__name__)


@receiver(pre_save, sender=Show)
def show_pre_save(sender, instance, **kwargs):
    """Store original show times before saving to detect changes"""
    if instance.pk:
        try:
            original = Show.objects.get(pk=instance.pk)
            instance._original_start_time = original.start_time
            instance._original_end_time = original.end_time
        except Show.DoesNotExist:
            instance._original_start_time = None
            instance._original_end_time = None
    else:
        instance._original_start_time = None
        instance._original_end_time = None


@receiver(post_save, sender=Show)
def show_post_save(sender, instance, created, **kwargs):
    """Generate time intervals when show is created or time changes"""
    try:
        # Check if this is a new show or if times have changed
        should_generate_intervals = False
        
        if created:
            # New show - always generate intervals
            should_generate_intervals = True
            logger.info(f"New show created: {instance.name} - generating time intervals")
        else:
            # Existing show - check if times changed
            original_start = getattr(instance, '_original_start_time', None)
            original_end = getattr(instance, '_original_end_time', None)
            
            if (original_start != instance.start_time or 
                original_end != instance.end_time):
                should_generate_intervals = True
                logger.info(f"Show times changed for {instance.name} - regenerating time intervals")
        
        if should_generate_intervals:
            # Generate time intervals using the show's configured interval
            intervals_created = instance.generate_time_intervals(
                force_regenerate=True
            )
            logger.info(f"Generated {intervals_created} time intervals for show: {instance.name}")
            
            # Clear any cached time intervals for this show
            instance._clear_time_intervals_cache()
            
    except Exception as e:
        logger.error(f"Error generating time intervals for show {instance.name}: {str(e)}")


@receiver(post_save, sender=ShowTimeInterval)
def show_time_interval_post_save(sender, instance, created, **kwargs):
    """Clear cache when time intervals are modified"""
    if created:
        # Clear cache for the show when new intervals are added
        instance.show._clear_time_intervals_cache()


# Optional: Add signal to handle show deletion
from django.db.models.signals import post_delete

@receiver(post_delete, sender=Show)
def show_post_delete(sender, instance, **kwargs):
    """Clean up when show is deleted"""
    try:
        # Clear any cached time intervals for this show
        instance._clear_time_intervals_cache()
        logger.info(f"Cleaned up cache for deleted show: {instance.name}")
    except Exception as e:
        logger.error(f"Error cleaning up after show deletion: {str(e)}")
