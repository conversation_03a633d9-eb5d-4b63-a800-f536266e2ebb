from django.contrib import admin
from .models import Show, ShowPresenter


class ShowPresenterInline(admin.TabularInline):
    model = ShowPresenter
    extra = 1
    fields = ['presenter', 'role', 'is_primary', 'is_active']


@admin.register(Show)
class ShowAdmin(admin.ModelAdmin):
    list_display = ['name', 'organization', 'branch', 'start_time', 'end_time', 'weekdays_display', 'is_active', 'created_at']
    list_filter = ['is_active', 'organization', 'branch', 'start_time', 'created_at']
    search_fields = ['name', 'description', 'organization__name', 'branch__name']
    list_editable = ['is_active']
    ordering = ['organization', 'start_time']
    readonly_fields = ['created_at', 'updated_at', 'duration_minutes']
    inlines = [ShowPresenterInline]

    fieldsets = (
        ('Organization', {
            'fields': ('organization', 'branch')
        }),
        ('Show Information', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Schedule', {
            'fields': ('start_time', 'end_time', 'days_of_week', 'duration_minutes')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ShowPresenter)
class ShowPresenterAdmin(admin.ModelAdmin):
    list_display = ['show', 'presenter', 'role', 'is_primary', 'is_active', 'created_at']
    list_filter = ['role', 'is_primary', 'is_active', 'created_at']
    search_fields = ['show__name', 'presenter__first_name', 'presenter__last_name']
    list_editable = ['is_primary', 'is_active']
    ordering = ['show__name', '-is_primary', 'presenter__first_name']
