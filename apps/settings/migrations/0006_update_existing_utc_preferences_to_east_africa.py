# Generated by Django 4.2.7 on 2025-06-26 03:12

from django.db import migrations


def update_utc_preferences_to_east_africa(apps, schema_editor):
    """
    Update existing user preferences that are set to UTC to use East Africa Time.
    This helps existing users automatically get the new default timezone.
    """
    UserPreferences = apps.get_model('settings', 'UserPreferences')

    # Update all preferences that are currently set to UTC
    updated_count = UserPreferences.objects.filter(timezone='UTC').update(timezone='Africa/Nairobi')

    if updated_count > 0:
        print(f"Updated {updated_count} user preferences from UTC to Africa/Nairobi timezone")


def reverse_update_utc_preferences(apps, schema_editor):
    """
    Reverse the migration by changing East Africa Time back to UTC for preferences
    that were updated in the forward migration.
    """
    UserPreferences = apps.get_model('settings', 'UserPreferences')

    # This is a simple reverse - change all Africa/Nairobi back to UTC
    # Note: This might affect preferences that were manually set to Africa/Nairobi
    updated_count = UserPreferences.objects.filter(timezone='Africa/Nairobi').update(timezone='UTC')

    if updated_count > 0:
        print(f"Reverted {updated_count} user preferences from Africa/Nairobi back to UTC timezone")


class Migration(migrations.Migration):
    dependencies = [
        ("settings", "0005_change_default_timezone_to_east_africa"),
    ]

    operations = [
        migrations.RunPython(
            update_utc_preferences_to_east_africa,
            reverse_update_utc_preferences,
        ),
    ]
