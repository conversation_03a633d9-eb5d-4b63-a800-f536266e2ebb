# Generated by Django 4.2.7 on 2025-06-19 18:17

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("settings", "0002_organizationsettings_industry_alignment_settings_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="userpreferences",
            name="date_format",
            field=models.CharField(
                choices=[
                    ("MM/DD/YYYY", "MM/DD/YYYY (US Format)"),
                    ("DD/MM/YYYY", "DD/MM/YYYY (European Format)"),
                    ("YYYY-MM-DD", "YYYY-MM-DD (ISO Format)"),
                    ("MMM DD, YYYY", "MMM DD, YYYY (e.g., Jan 15, 2024)"),
                    ("DD MMM YYYY", "DD MMM YYYY (e.g., 15 Jan 2024)"),
                ],
                default="MM/DD/YYYY",
                help_text="How dates should be displayed throughout the application",
                max_length=20,
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="userpreferences",
            name="language",
            field=models.CharField(
                choices=[
                    ("en", "English"),
                    ("es", "Spanish"),
                    ("fr", "French"),
                    ("de", "German"),
                ],
                default="en",
                help_text="Interface language preference",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="userpreferences",
            name="time_format",
            field=models.CharField(
                choices=[("12", "12-hour (AM/PM)"), ("24", "24-hour (Military Time)")],
                default="12",
                help_text="Whether to display time in 12-hour or 24-hour format",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="userpreferences",
            name="timezone",
            field=models.CharField(
                choices=[
                    ("UTC", "UTC"),
                    ("America/New_York", "Eastern Time (ET)"),
                    ("America/Chicago", "Central Time (CT)"),
                    ("America/Denver", "Mountain Time (MT)"),
                    ("America/Los_Angeles", "Pacific Time (PT)"),
                    ("America/Anchorage", "Alaska Time (AKT)"),
                    ("Pacific/Honolulu", "Hawaii Time (HT)"),
                ],
                default="UTC",
                help_text="Your preferred timezone for displaying dates and times",
                max_length=50,
            ),
        ),
    ]
