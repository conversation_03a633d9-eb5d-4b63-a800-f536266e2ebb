# Generated by Django 4.2.7 on 2025-06-18 13:52

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("settings", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="organizationsettings",
            name="industry_alignment_settings",
            field=models.JSONField(
                blank=True,
                default=dict,
                help_text="JSON mapping of industry codes to alignment (left/right)",
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="mention_alignment_mode",
            field=models.CharField(
                choices=[
                    ("alternating", "Alternating (First Right, Then Left)"),
                    ("industry_based", "Based on Industry Settings"),
                    ("all_left", "All Left Aligned"),
                    ("all_right", "All Right Aligned"),
                ],
                default="alternating",
                help_text="How mentions should be aligned in live show view",
                max_length=20,
            ),
        ),
    ]
