# Generated by Django 4.2.7 on 2025-06-26 03:11

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("settings", "0004_alter_userpreferences_timezone"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="userpreferences",
            name="timezone",
            field=models.CharField(
                choices=[
                    ("UTC", "UTC"),
                    ("Europe/Moscow", "Moscow Time (UTC+3)"),
                    ("Europe/Istanbul", "Turkey Time (UTC+3)"),
                    ("Africa/Nairobi", "East Africa Time (UTC+3)"),
                    ("Asia/Riyadh", "Arabia Standard Time (UTC+3)"),
                    ("Europe/Athens", "Eastern European Time (UTC+2/+3)"),
                    ("Europe/London", "Greenwich Mean Time (UTC+0/+1)"),
                    ("Europe/Paris", "Central European Time (UTC+1/+2)"),
                    ("America/New_York", "Eastern Time (UTC-5/-4)"),
                    ("America/Chicago", "Central Time (UTC-6/-5)"),
                    ("America/Denver", "Mountain Time (UTC-7/-6)"),
                    ("America/Los_Angeles", "Pacific Time (UTC-8/-7)"),
                    ("America/Anchorage", "Alaska Time (UTC-9/-8)"),
                    ("Pacific/Honolulu", "Hawaii Time (UTC-10)"),
                    ("Asia/Tokyo", "Japan Standard Time (UTC+9)"),
                    ("Australia/Sydney", "Australian Eastern Time (UTC+10/+11)"),
                ],
                default="Africa/Nairobi",
                help_text="Your preferred timezone for displaying dates and times",
                max_length=50,
            ),
        ),
    ]
