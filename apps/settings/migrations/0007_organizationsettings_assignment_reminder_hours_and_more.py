# Generated by Django 4.2.7 on 2025-06-26 23:27

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("settings", "0006_update_existing_utc_preferences_to_east_africa"),
    ]

    operations = [
        migrations.AddField(
            model_name="organizationsettings",
            name="assignment_reminder_hours",
            field=models.PositiveIntegerField(
                default=2, help_text="Hours before due date to send reminders"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="auto_assign_breaking_news",
            field=models.BooleanField(
                default=True, help_text="Auto-assign breaking news to available readers"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="auto_save_interval",
            field=models.PositiveIntegerField(
                default=30, help_text="Auto-save interval in seconds"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="default_article_length_minutes",
            field=models.PositiveIntegerField(
                default=2, help_text="Default article length in minutes"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="default_reading_speed",
            field=models.PositiveIntegerField(
                default=3, help_text="Default reading speed (1-5 scale)"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="enable_live_reading_mode",
            field=models.BooleanField(
                default=True, help_text="Enable live reading interface"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="enable_reading_analytics",
            field=models.BooleanField(
                default=True, help_text="Enable reading performance analytics"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="enable_teleprompter_mode",
            field=models.BooleanField(
                default=True, help_text="Enable teleprompter mode"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="live_reading_timeout_minutes",
            field=models.PositiveIntegerField(
                default=30, help_text="Live reading session timeout"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="max_articles_per_session",
            field=models.PositiveIntegerField(
                default=10, help_text="Maximum articles per reading session"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="require_article_approval",
            field=models.BooleanField(
                default=False, help_text="Require approval before publishing articles"
            ),
        ),
        migrations.AddField(
            model_name="organizationsettings",
            name="teleprompter_font_size",
            field=models.PositiveIntegerField(
                default=32, help_text="Default teleprompter font size"
            ),
        ),
        migrations.AddField(
            model_name="userpreferences",
            name="articles_per_page",
            field=models.PositiveIntegerField(
                default=20, help_text="Number of articles to show per page"
            ),
        ),
        migrations.AddField(
            model_name="userpreferences",
            name="default_article_view",
            field=models.CharField(
                choices=[
                    ("list", "List View"),
                    ("grid", "Grid View"),
                    ("compact", "Compact View"),
                ],
                default="list",
                help_text="Default view for article lists",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="userpreferences",
            name="enable_reading_notifications",
            field=models.BooleanField(
                default=True, help_text="Enable notifications for reading assignments"
            ),
        ),
        migrations.AddField(
            model_name="userpreferences",
            name="preferred_reading_speed",
            field=models.PositiveIntegerField(
                default=3, help_text="Preferred reading speed (1-5 scale)"
            ),
        ),
        migrations.AddField(
            model_name="userpreferences",
            name="show_pronunciation_guide",
            field=models.BooleanField(
                default=True, help_text="Show pronunciation guide in articles"
            ),
        ),
    ]
