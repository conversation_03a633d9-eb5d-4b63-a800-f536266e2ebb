# Generated by Django 4.2.7 on 2025-06-11 07:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("organizations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SystemSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("key", models.CharField(max_length=100, unique=True)),
                ("value", models.TextField()),
                ("description", models.TextField(blank=True)),
                ("is_active", models.BooleanField(default=True)),
            ],
            options={
                "verbose_name": "System Setting",
                "verbose_name_plural": "System Settings",
            },
        ),
        migrations.CreateModel(
            name="OrganizationSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "default_mention_duration",
                    models.PositiveIntegerField(
                        default=30, help_text="Default duration in seconds"
                    ),
                ),
                ("max_mentions_per_hour", models.PositiveIntegerField(default=6)),
                (
                    "min_gap_between_mentions",
                    models.PositiveIntegerField(
                        default=300, help_text="Minimum gap in seconds"
                    ),
                ),
                ("allow_overlapping_mentions", models.BooleanField(default=False)),
                ("enable_conflict_detection", models.BooleanField(default=True)),
                ("auto_resolve_conflicts", models.BooleanField(default=False)),
                (
                    "conflict_resolution_strategy",
                    models.CharField(
                        choices=[
                            ("priority", "By Priority"),
                            ("first_come", "First Come First Serve"),
                            ("manual", "Manual Resolution"),
                        ],
                        default="priority",
                        max_length=20,
                    ),
                ),
                ("enable_email_notifications", models.BooleanField(default=True)),
                ("enable_conflict_alerts", models.BooleanField(default=True)),
                ("enable_deadline_reminders", models.BooleanField(default=True)),
                ("reminder_hours_before", models.PositiveIntegerField(default=24)),
                ("require_mention_approval", models.BooleanField(default=True)),
                ("auto_approve_recurring", models.BooleanField(default=False)),
                ("approval_timeout_hours", models.PositiveIntegerField(default=48)),
                ("auto_archive_completed", models.BooleanField(default=True)),
                ("archive_after_days", models.PositiveIntegerField(default=30)),
                (
                    "delete_archived_after_days",
                    models.PositiveIntegerField(default=365),
                ),
                (
                    "organization",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="settings",
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Organization Settings",
                "verbose_name_plural": "Organization Settings",
            },
        ),
        migrations.CreateModel(
            name="APISettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "openweather_api_key",
                    models.CharField(
                        blank=True,
                        help_text="API key from OpenWeatherMap.org",
                        max_length=255,
                    ),
                ),
                ("openweather_enabled", models.BooleanField(default=False)),
                (
                    "weather_location",
                    models.CharField(
                        blank=True,
                        help_text="City name for weather data (e.g., 'New York, NY, US')",
                        max_length=100,
                    ),
                ),
                (
                    "weather_units",
                    models.CharField(
                        choices=[
                            ("metric", "Celsius"),
                            ("imperial", "Fahrenheit"),
                            ("kelvin", "Kelvin"),
                        ],
                        default="imperial",
                        max_length=10,
                    ),
                ),
                (
                    "organization",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="api_settings",
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "API Settings",
                "verbose_name_plural": "API Settings",
            },
        ),
        migrations.CreateModel(
            name="UserPreferences",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "default_dashboard_view",
                    models.CharField(
                        choices=[
                            ("calendar", "Calendar View"),
                            ("list", "List View"),
                            ("kanban", "Kanban Board"),
                        ],
                        default="calendar",
                        max_length=20,
                    ),
                ),
                ("items_per_page", models.PositiveIntegerField(default=20)),
                ("show_completed_mentions", models.BooleanField(default=False)),
                (
                    "calendar_default_view",
                    models.CharField(
                        choices=[("day", "Day"), ("week", "Week"), ("month", "Month")],
                        default="week",
                        max_length=10,
                    ),
                ),
                ("calendar_start_hour", models.PositiveIntegerField(default=6)),
                ("calendar_end_hour", models.PositiveIntegerField(default=24)),
                ("email_notifications", models.BooleanField(default=True)),
                ("browser_notifications", models.BooleanField(default=True)),
                ("mobile_notifications", models.BooleanField(default=True)),
                ("notify_mention_assigned", models.BooleanField(default=True)),
                ("notify_mention_approved", models.BooleanField(default=True)),
                ("notify_mention_rejected", models.BooleanField(default=True)),
                ("notify_conflicts", models.BooleanField(default=True)),
                ("notify_deadlines", models.BooleanField(default=True)),
                ("notify_schedule_changes", models.BooleanField(default=True)),
                (
                    "theme",
                    models.CharField(
                        choices=[
                            ("light", "Light"),
                            ("dark", "Dark"),
                            ("auto", "Auto"),
                        ],
                        default="light",
                        max_length=10,
                    ),
                ),
                ("sidebar_collapsed", models.BooleanField(default=False)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="user_preferences",
                        to="organizations.organization",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="preferences",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Preferences",
                "verbose_name_plural": "User Preferences",
                "unique_together": {("user", "organization")},
            },
        ),
    ]
