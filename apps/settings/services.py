from django.utils import timezone
from datetime import datetime, time
import pytz
from .models import UserPreferences


class TimeFormattingService:
    """Service for formatting time according to user preferences"""
    
    @staticmethod
    def get_user_preferences(user, organization=None):
        """
        Get user preferences for the given organization.
        
        Args:
            user: User object
            organization: Organization object (optional, will use first if not provided)
        
        Returns:
            UserPreferences object or None
        """
        if not user or not user.is_authenticated:
            return None
        
        try:
            if not organization:
                membership = user.organizationmembership_set.first()
                if not membership:
                    return None
                organization = membership.organization
            
            return UserPreferences.objects.filter(
                user=user,
                organization=organization
            ).first()
        except:
            return None
    
    @staticmethod
    def format_time(time_obj, user=None, organization=None):
        """
        Format time according to user's time format preference.
        
        Args:
            time_obj: datetime.time or datetime.datetime object
            user: User object to get preferences from
            organization: Organization object (optional)
        
        Returns:
            Formatted time string
        """
        if not time_obj:
            return ''
        
        # Get user preferences
        time_format = '12'  # Default to 12-hour format
        prefs = TimeFormattingService.get_user_preferences(user, organization)
        if prefs:
            time_format = prefs.time_format
        
        # Extract time if datetime object
        if isinstance(time_obj, datetime):
            time_obj = time_obj.time()
        
        # Format according to preference
        if time_format == '24':
            return time_obj.strftime('%H:%M')
        else:
            return time_obj.strftime('%I:%M %p')
    
    @staticmethod
    def format_datetime(datetime_obj, user=None, organization=None):
        """
        Format datetime according to user's date and time format preferences.
        
        Args:
            datetime_obj: datetime.datetime object
            user: User object to get preferences from
            organization: Organization object (optional)
        
        Returns:
            Formatted datetime string
        """
        if not datetime_obj:
            return ''
        
        # Get user preferences
        date_format = 'MM/DD/YYYY'
        time_format = '12'
        user_timezone = 'Africa/Nairobi'
        
        prefs = TimeFormattingService.get_user_preferences(user, organization)
        if prefs:
            date_format = prefs.date_format
            time_format = prefs.time_format
            user_timezone = prefs.timezone
        
        # Convert to user's timezone
        if timezone.is_aware(datetime_obj):
            try:
                user_tz = pytz.timezone(user_timezone)
                datetime_obj = datetime_obj.astimezone(user_tz)
            except:
                pass  # Use original timezone if conversion fails
        
        # Format date part
        if date_format == 'DD/MM/YYYY':
            date_str = datetime_obj.strftime('%d/%m/%Y')
        elif date_format == 'YYYY-MM-DD':
            date_str = datetime_obj.strftime('%Y-%m-%d')
        elif date_format == 'MMM DD, YYYY':
            date_str = datetime_obj.strftime('%b %d, %Y')
        elif date_format == 'DD MMM YYYY':
            date_str = datetime_obj.strftime('%d %b %Y')
        else:  # MM/DD/YYYY
            date_str = datetime_obj.strftime('%m/%d/%Y')
        
        # Format time part
        if time_format == '24':
            time_str = datetime_obj.strftime('%H:%M')
        else:
            time_str = datetime_obj.strftime('%I:%M %p')
        
        return f"{date_str} {time_str}"
    
    @staticmethod
    def format_date(date_obj, user=None, organization=None):
        """
        Format date according to user's date format preference.
        
        Args:
            date_obj: datetime.date or datetime.datetime object
            user: User object to get preferences from
            organization: Organization object (optional)
        
        Returns:
            Formatted date string
        """
        if not date_obj:
            return ''
        
        # Get user preferences
        date_format = 'MM/DD/YYYY'
        prefs = TimeFormattingService.get_user_preferences(user, organization)
        if prefs:
            date_format = prefs.date_format
        
        # Extract date if datetime object
        if isinstance(date_obj, datetime):
            date_obj = date_obj.date()
        
        # Format according to preference
        if date_format == 'DD/MM/YYYY':
            return date_obj.strftime('%d/%m/%Y')
        elif date_format == 'YYYY-MM-DD':
            return date_obj.strftime('%Y-%m-%d')
        elif date_format == 'MMM DD, YYYY':
            return date_obj.strftime('%b %d, %Y')
        elif date_format == 'DD MMM YYYY':
            return date_obj.strftime('%d %b %Y')
        else:  # MM/DD/YYYY
            return date_obj.strftime('%m/%d/%Y')
    
    @staticmethod
    def convert_to_user_timezone(datetime_obj, user=None, organization=None):
        """
        Convert datetime to user's timezone.
        
        Args:
            datetime_obj: datetime.datetime object
            user: User object to get preferences from
            organization: Organization object (optional)
        
        Returns:
            datetime object in user's timezone
        """
        if not datetime_obj or not timezone.is_aware(datetime_obj):
            return datetime_obj
        
        # Get user timezone preference
        user_timezone = 'Africa/Nairobi'
        prefs = TimeFormattingService.get_user_preferences(user, organization)
        if prefs:
            user_timezone = prefs.timezone
        
        try:
            user_tz = pytz.timezone(user_timezone)
            return datetime_obj.astimezone(user_tz)
        except:
            return datetime_obj  # Return original if conversion fails
    
    @staticmethod
    def get_user_timezone(user=None, organization=None):
        """
        Get user's timezone preference.
        
        Args:
            user: User object to get preferences from
            organization: Organization object (optional)
        
        Returns:
            Timezone string
        """
        prefs = TimeFormattingService.get_user_preferences(user, organization)
        if prefs:
            return prefs.timezone
        return 'Africa/Nairobi'
    
    @staticmethod
    def get_user_time_format(user=None, organization=None):
        """
        Get user's time format preference.
        
        Args:
            user: User object to get preferences from
            organization: Organization object (optional)
        
        Returns:
            Time format string ('12' or '24')
        """
        prefs = TimeFormattingService.get_user_preferences(user, organization)
        if prefs:
            return prefs.time_format
        return '12'
    
    @staticmethod
    def get_user_date_format(user=None, organization=None):
        """
        Get user's date format preference.
        
        Args:
            user: User object to get preferences from
            organization: Organization object (optional)
        
        Returns:
            Date format string
        """
        prefs = TimeFormattingService.get_user_preferences(user, organization)
        if prefs:
            return prefs.date_format
        return 'MM/DD/YYYY'
