from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from .models import OrganizationSettings, UserPreferences, SystemSettings, APISettings
from .forms import APISettingsForm, IndustryAlignmentForm
from .services import TimeFormattingService
from apps.core.decorators import require_permission
from apps.organizations.middleware import get_current_organization
from apps.activity_logs.models import ActivityLog
import json
import requests


@login_required
def settings_dashboard(request):
    """Settings dashboard view"""
    organization = request.user.organizationmembership_set.first().organization

    # Get or create organization settings
    org_settings, created = OrganizationSettings.objects.get_or_create(
        organization=organization,
        defaults={
            'default_mention_duration': 30,
            'max_mentions_per_hour': 6,
            'enable_conflict_detection': True,
        }
    )

    # Get or create user preferences
    user_prefs, created = UserPreferences.objects.get_or_create(
        user=request.user,
        organization=organization,
        defaults={
            'default_dashboard_view': 'calendar',
            'email_notifications': True,
        }
    )

    # Get or create API settings
    api_settings, created = APISettings.objects.get_or_create(
        organization=organization
    )

    context = {
        'org_settings': org_settings,
        'user_preferences': user_prefs,
        'api_settings': api_settings,
    }
    return render(request, 'settings/dashboard.html', context)


@login_required
def organization_settings(request):
    """Organization settings view"""
    organization = request.user.organizationmembership_set.first().organization
    settings_obj, created = OrganizationSettings.objects.get_or_create(
        organization=organization
    )

    if request.method == 'POST':
        # Update organization settings
        settings_obj.default_mention_duration = int(request.POST.get('default_mention_duration', 30))
        settings_obj.max_mentions_per_hour = int(request.POST.get('max_mentions_per_hour', 6))
        settings_obj.min_gap_between_mentions = int(request.POST.get('min_gap_between_mentions', 300))
        settings_obj.allow_overlapping_mentions = request.POST.get('allow_overlapping_mentions') == 'on'

        # Conflict detection settings
        settings_obj.enable_conflict_detection = request.POST.get('enable_conflict_detection') == 'on'
        settings_obj.auto_resolve_conflicts = request.POST.get('auto_resolve_conflicts') == 'on'
        settings_obj.conflict_resolution_strategy = request.POST.get('conflict_resolution_strategy', 'priority')

        # Notification settings
        settings_obj.enable_email_notifications = request.POST.get('enable_email_notifications') == 'on'
        settings_obj.enable_conflict_alerts = request.POST.get('enable_conflict_alerts') == 'on'
        settings_obj.enable_deadline_reminders = request.POST.get('enable_deadline_reminders') == 'on'
        settings_obj.reminder_hours_before = int(request.POST.get('reminder_hours_before', 24))

        # Approval workflow settings
        settings_obj.require_mention_approval = request.POST.get('require_mention_approval') == 'on'
        settings_obj.auto_approve_recurring = request.POST.get('auto_approve_recurring') == 'on'
        settings_obj.approval_timeout_hours = int(request.POST.get('approval_timeout_hours', 48))

        # Archive settings
        settings_obj.auto_archive_completed = request.POST.get('auto_archive_completed') == 'on'
        settings_obj.archive_after_days = int(request.POST.get('archive_after_days', 30))
        settings_obj.delete_archived_after_days = int(request.POST.get('delete_archived_after_days', 365))

        settings_obj.save()

        # Log the activity
        ActivityLog.log_activity(
            user=request.user,
            organization=organization,
            action='settings_updated',
            description='Organization settings updated',
            content_object=settings_obj,
            request=request
        )

        messages.success(request, 'Organization settings updated successfully!')
        return redirect('settings:organization_settings')

    context = {
        'settings': settings_obj,
    }
    return render(request, 'settings/organization_settings.html', context)


@login_required
def user_preferences(request):
    """User preferences view"""
    organization = request.user.organizationmembership_set.first().organization
    prefs, created = UserPreferences.objects.get_or_create(
        user=request.user,
        organization=organization
    )

    if request.method == 'POST':
        # Update user preferences
        prefs.default_dashboard_view = request.POST.get('default_dashboard_view', 'calendar')
        prefs.items_per_page = int(request.POST.get('items_per_page', 20))
        prefs.show_completed_mentions = request.POST.get('show_completed_mentions') == 'on'

        # Calendar preferences
        prefs.calendar_default_view = request.POST.get('calendar_default_view', 'week')
        prefs.calendar_start_hour = int(request.POST.get('calendar_start_hour', 6))
        prefs.calendar_end_hour = int(request.POST.get('calendar_end_hour', 24))

        # Notification preferences
        prefs.email_notifications = request.POST.get('email_notifications') == 'on'
        prefs.browser_notifications = request.POST.get('browser_notifications') == 'on'
        prefs.mobile_notifications = request.POST.get('mobile_notifications') == 'on'

        # Notification types
        prefs.notify_mention_assigned = request.POST.get('notify_mention_assigned') == 'on'
        prefs.notify_mention_approved = request.POST.get('notify_mention_approved') == 'on'
        prefs.notify_mention_rejected = request.POST.get('notify_mention_rejected') == 'on'
        prefs.notify_conflicts = request.POST.get('notify_conflicts') == 'on'
        prefs.notify_deadlines = request.POST.get('notify_deadlines') == 'on'
        prefs.notify_schedule_changes = request.POST.get('notify_schedule_changes') == 'on'

        # Theme preferences
        prefs.theme = request.POST.get('theme', 'light')
        prefs.sidebar_collapsed = request.POST.get('sidebar_collapsed') == 'on'

        # Time and localization preferences
        prefs.timezone = request.POST.get('timezone', 'UTC')
        prefs.date_format = request.POST.get('date_format', 'MM/DD/YYYY')
        prefs.time_format = request.POST.get('time_format', '12')
        prefs.language = request.POST.get('language', 'en')

        prefs.save()

        # Log the activity
        ActivityLog.log_activity(
            user=request.user,
            organization=organization,
            action='settings_updated',
            description='User preferences updated',
            content_object=prefs,
            request=request
        )

        messages.success(request, 'Your preferences have been updated!')
        return redirect('settings:user_preferences')

    context = {
        'preferences': prefs,
    }
    return render(request, 'settings/user_preferences.html', context)


@login_required
@require_http_methods(["POST"])
def reset_preferences(request):
    """Reset user preferences to defaults"""
    organization = request.user.organizationmembership_set.first().organization

    try:
        prefs = UserPreferences.objects.get(user=request.user, organization=organization)
        prefs.delete()

        # Log the activity
        ActivityLog.log_activity(
            user=request.user,
            organization=organization,
            action='settings_updated',
            description='User preferences reset to defaults',
            request=request
        )

        return JsonResponse({'success': True, 'message': 'Preferences reset to defaults'})
    except UserPreferences.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'No preferences found'})


@login_required
def system_settings(request):
    """System settings view (admin only)"""
    if not request.user.is_superuser:
        messages.error(request, 'You do not have permission to access system settings.')
        return redirect('settings:settings_dashboard')

    settings = SystemSettings.objects.filter(is_active=True).order_by('key')

    context = {
        'system_settings': settings,
    }
    return render(request, 'settings/system_settings.html', context)


@login_required
@require_permission('manage_api_keys')
def api_settings(request):
    """API settings view"""
    organization = get_current_organization(request)
    if not organization:
        return redirect('organizations:list')

    settings, created = APISettings.objects.get_or_create(
        organization=organization
    )

    if request.method == 'POST':
        form = APISettingsForm(request.POST, instance=settings, organization=organization)
        if form.is_valid():
            form.save()

            # Log the activity
            ActivityLog.log_activity(
                user=request.user,
                organization=organization,
                action='settings_updated',
                description='API settings updated',
                content_object=settings,
                request=request
            )

            messages.success(request, 'API settings updated successfully!')
            return redirect('settings:api_settings')
    else:
        form = APISettingsForm(instance=settings, organization=organization)

    return render(request, 'settings/api_settings.html', {
        'form': form,
        'settings': settings,
        'organization': organization,
    })


@login_required
@require_permission('manage_settings')
def industry_alignment_settings(request):
    """Industry alignment settings view"""
    organization = get_current_organization(request)
    if not organization:
        return redirect('organizations:list')

    # Get or create organization settings
    org_settings, created = OrganizationSettings.objects.get_or_create(
        organization=organization
    )

    if request.method == 'POST':
        form = IndustryAlignmentForm(request.POST, organization=organization)

        if form.is_valid():
            # Update alignment mode
            org_settings.mention_alignment_mode = request.POST.get('mention_alignment_mode', 'alternating')

            # Update industry settings if industry_based mode is selected
            if org_settings.mention_alignment_mode == 'industry_based':
                org_settings.industry_alignment_settings = form.get_industry_settings()

            org_settings.save()

            # Log the activity
            ActivityLog.log_activity(
                user=request.user,
                organization=organization,
                action='settings_updated',
                description='Industry alignment settings updated',
                content_object=org_settings,
                request=request
            )

            messages.success(request, 'Industry alignment settings updated successfully!')
            return redirect('settings:industry_alignment')
    else:
        # Initialize form with current settings
        initial_data = {}
        if org_settings.industry_alignment_settings:
            for industry_code, alignment in org_settings.industry_alignment_settings.items():
                initial_data[f'industry_{industry_code}'] = alignment

        form = IndustryAlignmentForm(initial=initial_data, organization=organization)

    return render(request, 'settings/industry_alignment.html', {
        'form': form,
        'org_settings': org_settings,
        'organization': organization,
    })


@login_required
@require_permission('manage_api_keys')
def test_weather_api(request):
    """Test OpenWeatherMap API connection"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    organization = get_current_organization(request)
    if not organization:
        return JsonResponse({'error': 'No organization found'}, status=400)

    api_key = request.POST.get('api_key')
    location = request.POST.get('location')

    if not api_key or not location:
        return JsonResponse({'error': 'API key and location are required'}, status=400)

    try:
        # Test API call to OpenWeatherMap
        url = f"http://api.openweathermap.org/data/2.5/weather"
        params = {
            'q': location,
            'appid': api_key,
            'units': 'imperial'
        }

        response = requests.get(url, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()
            return JsonResponse({
                'success': True,
                'message': 'API connection successful!',
                'weather': {
                    'location': data['name'],
                    'country': data['sys']['country'],
                    'temperature': round(data['main']['temp']),
                    'description': data['weather'][0]['description'].title(),
                    'icon': data['weather'][0]['icon']
                }
            })
        elif response.status_code == 401:
            return JsonResponse({'error': 'Invalid API key'}, status=400)
        elif response.status_code == 404:
            return JsonResponse({'error': 'Location not found'}, status=400)
        else:
            return JsonResponse({'error': f'API error: {response.status_code}'}, status=400)

    except requests.exceptions.Timeout:
        return JsonResponse({'error': 'API request timed out'}, status=400)
    except requests.exceptions.RequestException as e:
        return JsonResponse({'error': f'Network error: {str(e)}'}, status=400)
    except Exception as e:
        return JsonResponse({'error': f'Unexpected error: {str(e)}'}, status=500)


@login_required
def news_reader_settings(request):
    """News reader settings view"""
    organization = get_current_organization(request)
    if not organization:
        return redirect('organizations:list')

    # Get or create organization settings
    org_settings, created = OrganizationSettings.objects.get_or_create(
        organization=organization
    )

    # Get user preferences
    user_prefs, created = UserPreferences.objects.get_or_create(
        user=request.user,
        organization=organization
    )

    if request.method == 'POST':
        # Update news reader specific settings

        # Article settings
        org_settings.default_article_length_minutes = int(request.POST.get('default_article_length_minutes', 2))
        org_settings.max_articles_per_session = int(request.POST.get('max_articles_per_session', 10))
        org_settings.auto_save_interval = int(request.POST.get('auto_save_interval', 30))

        # Reading settings
        org_settings.default_reading_speed = int(request.POST.get('default_reading_speed', 3))
        org_settings.enable_teleprompter_mode = request.POST.get('enable_teleprompter_mode') == 'on'
        org_settings.teleprompter_font_size = int(request.POST.get('teleprompter_font_size', 32))

        # Assignment settings
        org_settings.require_article_approval = request.POST.get('require_article_approval') == 'on'
        org_settings.auto_assign_breaking_news = request.POST.get('auto_assign_breaking_news') == 'on'
        org_settings.assignment_reminder_hours = int(request.POST.get('assignment_reminder_hours', 2))

        # Live reading settings
        org_settings.enable_live_reading_mode = request.POST.get('enable_live_reading_mode') == 'on'
        org_settings.live_reading_timeout_minutes = int(request.POST.get('live_reading_timeout_minutes', 30))
        org_settings.enable_reading_analytics = request.POST.get('enable_reading_analytics') == 'on'

        # User preferences for news reading
        user_prefs.default_article_view = request.POST.get('default_article_view', 'list')
        user_prefs.articles_per_page = int(request.POST.get('articles_per_page', 20))
        user_prefs.show_pronunciation_guide = request.POST.get('show_pronunciation_guide') == 'on'
        user_prefs.enable_reading_notifications = request.POST.get('enable_reading_notifications') == 'on'
        user_prefs.preferred_reading_speed = int(request.POST.get('preferred_reading_speed', 3))

        org_settings.save()
        user_prefs.save()

        # Log the activity
        ActivityLog.log_activity(
            user=request.user,
            organization=organization,
            action='news_reader_settings_updated',
            description='News reader settings updated',
            content_object=org_settings,
            request=request
        )

        messages.success(request, 'News reader settings have been updated!')
        return redirect('settings:news_reader_settings')

    context = {
        'org_settings': org_settings,
        'user_preferences': user_prefs,
        'organization': organization,
    }

    return render(request, 'settings/news_reader_settings.html', context)
