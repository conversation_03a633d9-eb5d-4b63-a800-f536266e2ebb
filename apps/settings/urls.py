from django.urls import path
from . import views

app_name = 'settings'

urlpatterns = [
    path('', views.settings_dashboard, name='settings_dashboard'),
    path('organization/', views.organization_settings, name='organization_settings'),
    path('preferences/', views.user_preferences, name='user_preferences'),
    path('preferences/reset/', views.reset_preferences, name='reset_preferences'),
    path('system/', views.system_settings, name='system_settings'),
    path('api/', views.api_settings, name='api_settings'),
    path('api/test-weather/', views.test_weather_api, name='test_weather_api'),
    path('industry-alignment/', views.industry_alignment_settings, name='industry_alignment'),
    path('news-reader/', views.news_reader_settings, name='news_reader_settings'),
]
