from django import template
from django.utils import timezone
from datetime import datetime, time, date
import pytz

register = template.Library()


@register.filter
def format_time_user(time_obj, user=None):
    """
    Format time according to user's time format preference.

    Args:
        time_obj: datetime.time, datetime.datetime object, or time string
        user: User object to get preferences from

    Returns:
        Formatted time string
    """
    if not time_obj:
        return ''

    # Get user preferences
    time_format = '12'  # Default to 12-hour format
    if user and hasattr(user, 'preferences'):
        try:
            prefs = user.preferences.filter(organization=user.organizationmembership_set.first().organization).first()
            if prefs:
                time_format = prefs.time_format
        except:
            pass

    # Handle string input (from {% now %} template tag)
    if isinstance(time_obj, str):
        try:
            # Try to parse time string in H:M format
            if ':' in time_obj:
                hour, minute = time_obj.split(':')
                time_obj = time(int(hour), int(minute))
            else:
                return time_obj  # Return as-is if can't parse
        except (ValueError, TypeError):
            return time_obj  # Return as-is if can't parse

    # Extract time if datetime object
    if isinstance(time_obj, datetime):
        time_obj = time_obj.time()

    # Format according to preference
    if time_format == '24':
        return time_obj.strftime('%H:%M')
    else:
        return time_obj.strftime('%I:%M %p')


@register.filter
def format_datetime_user(datetime_obj, user=None):
    """
    Format datetime according to user's date and time format preferences.
    
    Args:
        datetime_obj: datetime.datetime object
        user: User object to get preferences from
    
    Returns:
        Formatted datetime string
    """
    if not datetime_obj:
        return ''
    
    # Get user preferences
    date_format = 'MM/DD/YYYY'
    time_format = '12'
    user_timezone = 'Africa/Nairobi'
    
    if user and hasattr(user, 'preferences'):
        try:
            prefs = user.preferences.filter(organization=user.organizationmembership_set.first().organization).first()
            if prefs:
                date_format = prefs.date_format
                time_format = prefs.time_format
                user_timezone = prefs.timezone
        except:
            pass
    
    # Convert to user's timezone
    if timezone.is_aware(datetime_obj):
        user_tz = pytz.timezone(user_timezone)
        datetime_obj = datetime_obj.astimezone(user_tz)
    
    # Format date part
    if date_format == 'DD/MM/YYYY':
        date_str = datetime_obj.strftime('%d/%m/%Y')
    elif date_format == 'YYYY-MM-DD':
        date_str = datetime_obj.strftime('%Y-%m-%d')
    elif date_format == 'MMM DD, YYYY':
        date_str = datetime_obj.strftime('%b %d, %Y')
    elif date_format == 'DD MMM YYYY':
        date_str = datetime_obj.strftime('%d %b %Y')
    else:  # MM/DD/YYYY
        date_str = datetime_obj.strftime('%m/%d/%Y')
    
    # Format time part
    if time_format == '24':
        time_str = datetime_obj.strftime('%H:%M')
    else:
        time_str = datetime_obj.strftime('%I:%M %p')
    
    return f"{date_str} {time_str}"


@register.filter
def format_date_user(date_obj, user=None):
    """
    Format date according to user's date format preference.

    Args:
        date_obj: datetime.date, datetime.datetime object, or date string
        user: User object to get preferences from

    Returns:
        Formatted date string
    """
    if not date_obj:
        return ''

    # Get user preferences
    date_format = 'MM/DD/YYYY'
    if user and hasattr(user, 'preferences'):
        try:
            prefs = user.preferences.filter(organization=user.organizationmembership_set.first().organization).first()
            if prefs:
                date_format = prefs.date_format
        except:
            pass

    # Handle string input (from {% now %} template tag)
    if isinstance(date_obj, str):
        try:
            # Try to parse date string in Y-m-d format
            if '-' in date_obj:
                year, month, day = date_obj.split('-')
                date_obj = date(int(year), int(month), int(day))
            else:
                return date_obj  # Return as-is if can't parse
        except (ValueError, TypeError):
            return date_obj  # Return as-is if can't parse

    # Extract date if datetime object
    if isinstance(date_obj, datetime):
        date_obj = date_obj.date()

    # Format according to preference
    if date_format == 'DD/MM/YYYY':
        return date_obj.strftime('%d/%m/%Y')
    elif date_format == 'YYYY-MM-DD':
        return date_obj.strftime('%Y-%m-%d')
    elif date_format == 'MMM DD, YYYY':
        return date_obj.strftime('%b %d, %Y')
    elif date_format == 'DD MMM YYYY':
        return date_obj.strftime('%d %b %Y')
    else:  # MM/DD/YYYY
        return date_obj.strftime('%m/%d/%Y')


@register.simple_tag(takes_context=True)
def user_timezone(context):
    """
    Get the current user's timezone preference.
    
    Returns:
        Timezone string
    """
    user = context.get('user')
    if not user or not user.is_authenticated:
        return 'Africa/Nairobi'

    try:
        prefs = user.preferences.filter(organization=user.organizationmembership_set.first().organization).first()
        if prefs:
            return prefs.timezone
    except:
        pass

    return 'Africa/Nairobi'


@register.simple_tag(takes_context=True)
def user_time_format(context):
    """
    Get the current user's time format preference.
    
    Returns:
        Time format string ('12' or '24')
    """
    user = context.get('user')
    if not user or not user.is_authenticated:
        return '12'
    
    try:
        prefs = user.preferences.filter(organization=user.organizationmembership_set.first().organization).first()
        if prefs:
            return prefs.time_format
    except:
        pass
    
    return '12'


@register.simple_tag(takes_context=True)
def user_date_format(context):
    """
    Get the current user's date format preference.
    
    Returns:
        Date format string
    """
    user = context.get('user')
    if not user or not user.is_authenticated:
        return 'MM/DD/YYYY'
    
    try:
        prefs = user.preferences.filter(organization=user.organizationmembership_set.first().organization).first()
        if prefs:
            return prefs.date_format
    except:
        pass
    
    return 'MM/DD/YYYY'


@register.simple_tag(takes_context=True)
def current_time_formatted(context):
    """
    Get current time formatted according to user preferences.

    Returns:
        Formatted current time string
    """
    user = context.get('user')
    current_datetime = timezone.now()

    # Get user preferences
    time_format = '12'
    user_timezone = 'Africa/Nairobi'
    if user and user.is_authenticated:
        try:
            prefs = user.preferences.filter(organization=user.organizationmembership_set.first().organization).first()
            if prefs:
                time_format = prefs.time_format
                user_timezone = prefs.timezone
        except:
            pass

    # Convert to user's timezone
    try:
        user_tz = pytz.timezone(user_timezone)
        current_datetime = current_datetime.astimezone(user_tz)
    except:
        pass  # Use original datetime if timezone conversion fails

    # Format according to preference
    if time_format == '24':
        return current_datetime.strftime('%H:%M')
    else:
        return current_datetime.strftime('%I:%M %p')


@register.simple_tag(takes_context=True)
def current_date_formatted(context):
    """
    Get current date formatted according to user preferences.

    Returns:
        Formatted current date string
    """
    user = context.get('user')
    current_datetime = timezone.now()

    # Get user preferences
    date_format = 'MM/DD/YYYY'
    user_timezone = 'Africa/Nairobi'
    if user and user.is_authenticated:
        try:
            prefs = user.preferences.filter(organization=user.organizationmembership_set.first().organization).first()
            if prefs:
                date_format = prefs.date_format
                user_timezone = prefs.timezone
        except:
            pass

    # Convert to user's timezone
    try:
        user_tz = pytz.timezone(user_timezone)
        current_datetime = current_datetime.astimezone(user_tz)
    except:
        pass  # Use original datetime if timezone conversion fails

    current_date = current_datetime.date()

    # Format according to preference
    if date_format == 'DD/MM/YYYY':
        return current_date.strftime('%d/%m/%Y')
    elif date_format == 'YYYY-MM-DD':
        return current_date.strftime('%Y-%m-%d')
    elif date_format == 'MMM DD, YYYY':
        return current_date.strftime('%b %d, %Y')
    elif date_format == 'DD MMM YYYY':
        return current_date.strftime('%d %b %Y')
    else:  # MM/DD/YYYY
        return current_date.strftime('%m/%d/%Y')
