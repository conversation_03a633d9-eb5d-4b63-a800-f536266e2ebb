from django.test import TestCase
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, time, date
from apps.organizations.models import Organization, OrganizationMembership
from .models import UserPreferences
from .services import TimeFormattingService


class TimeFormattingServiceTest(TestCase):
    """Test cases for TimeFormattingService"""

    def setUp(self):
        """Set up test data"""
        # Create organization
        self.organization = Organization.objects.create(
            name="Test Radio Station",
            slug="test-radio",
            timezone="America/New_York"
        )

        # Create user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123"
        )

        # Create organization membership
        OrganizationMembership.objects.create(
            user=self.user,
            organization=self.organization,
            role='admin'
        )

        # Create user preferences
        self.preferences = UserPreferences.objects.create(
            user=self.user,
            organization=self.organization,
            timezone='America/New_York',
            date_format='MM/DD/YYYY',
            time_format='12',
            language='en'
        )

    def test_format_time_12_hour(self):
        """Test 12-hour time formatting"""
        test_time = time(14, 30)  # 2:30 PM
        formatted = TimeFormattingService.format_time(test_time, user=self.user)
        self.assertEqual(formatted, "02:30 PM")

    def test_format_time_24_hour(self):
        """Test 24-hour time formatting"""
        # Update preferences to 24-hour format
        self.preferences.time_format = '24'
        self.preferences.save()

        test_time = time(14, 30)  # 2:30 PM
        formatted = TimeFormattingService.format_time(test_time, user=self.user)
        self.assertEqual(formatted, "14:30")

    def test_format_date_us_format(self):
        """Test US date formatting"""
        test_date = date(2024, 1, 15)
        formatted = TimeFormattingService.format_date(test_date, user=self.user)
        self.assertEqual(formatted, "01/15/2024")

    def test_format_date_european_format(self):
        """Test European date formatting"""
        # Update preferences to European format
        self.preferences.date_format = 'DD/MM/YYYY'
        self.preferences.save()

        test_date = date(2024, 1, 15)
        formatted = TimeFormattingService.format_date(test_date, user=self.user)
        self.assertEqual(formatted, "15/01/2024")

    def test_get_user_preferences(self):
        """Test getting user preferences"""
        prefs = TimeFormattingService.get_user_preferences(self.user, self.organization)
        self.assertIsNotNone(prefs)
        self.assertEqual(prefs.time_format, '12')
        self.assertEqual(prefs.date_format, 'MM/DD/YYYY')
        self.assertEqual(prefs.timezone, 'America/New_York')

    def test_default_values_no_user(self):
        """Test default values when no user is provided"""
        test_time = time(14, 30)
        formatted = TimeFormattingService.format_time(test_time, user=None)
        self.assertEqual(formatted, "02:30 PM")  # Default 12-hour format

    def test_utc_plus_3_timezone(self):
        """Test UTC+3 timezone options"""
        # Test Moscow timezone (UTC+3)
        self.preferences.timezone = 'Europe/Moscow'
        self.preferences.save()

        tz = TimeFormattingService.get_user_timezone(user=self.user)
        self.assertEqual(tz, 'Europe/Moscow')

        # Test Turkey timezone (UTC+3)
        self.preferences.timezone = 'Europe/Istanbul'
        self.preferences.save()

        tz = TimeFormattingService.get_user_timezone(user=self.user)
        self.assertEqual(tz, 'Europe/Istanbul')

        # Test East Africa timezone (UTC+3)
        self.preferences.timezone = 'Africa/Nairobi'
        self.preferences.save()

        tz = TimeFormattingService.get_user_timezone(user=self.user)
        self.assertEqual(tz, 'Africa/Nairobi')

        # Test Arabia timezone (UTC+3)
        self.preferences.timezone = 'Asia/Riyadh'
        self.preferences.save()

        tz = TimeFormattingService.get_user_timezone(user=self.user)
        self.assertEqual(tz, 'Asia/Riyadh')
