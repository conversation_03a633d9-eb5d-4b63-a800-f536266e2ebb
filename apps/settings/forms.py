from django import forms
from .models import OrganizationSettings, UserPreferences, APISettings
from apps.core.models import Client, Industry


class OrganizationSettingsForm(forms.ModelForm):
    class Meta:
        model = OrganizationSettings
        exclude = ['organization', 'created_at', 'updated_at']
        widgets = {
            'default_mention_duration': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '10',
                'max': '300'
            }),
            'max_mentions_per_hour': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '1',
                'max': '20'
            }),
            'min_gap_between_mentions': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '0',
                'max': '3600'
            }),
            'reminder_hours_before': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '1',
                'max': '168'
            }),
            'approval_timeout_hours': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '1',
                'max': '720'
            }),
            'archive_after_days': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '1',
                'max': '365'
            }),
            'delete_archived_after_days': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '30',
                'max': '3650'
            }),
            'conflict_resolution_strategy': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'mention_alignment_mode': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
        }


class UserPreferencesForm(forms.ModelForm):
    class Meta:
        model = UserPreferences
        exclude = ['user', 'organization', 'created_at', 'updated_at']
        widgets = {
            'language': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'timezone': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'date_format': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'time_format': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'items_per_page': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '10',
                'max': '100'
            }),
            'calendar_start_hour': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '0',
                'max': '23'
            }),
            'calendar_end_hour': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'min': '1',
                'max': '24'
            }),
        }


class APISettingsForm(forms.ModelForm):
    class Meta:
        model = APISettings
        exclude = ['organization', 'created_at', 'updated_at']
        widgets = {
            'openweather_enabled': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
            }),
            'openweather_api_key': forms.PasswordInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'Enter your OpenWeatherMap API key'
            }),
            'weather_location': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'e.g., New York, NY, US'
            }),
            'weather_units': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        self.fields['openweather_api_key'].help_text = (
            'Get your free API key from <a href="https://openweathermap.org/api" target="_blank" '
            'class="text-blue-600 hover:text-blue-800">OpenWeatherMap.org</a>'
        )
        self.fields['weather_location'].help_text = (
            'Enter city name, state/country code (e.g., "London, UK" or "New York, NY, US")'
        )
        self.fields['openweather_enabled'].help_text = (
            'Enable weather display on live show dashboard'
        )


class IndustryAlignmentForm(forms.Form):
    """Form for configuring industry-based mention alignment"""

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        # Get all active industries for the organization
        if self.organization:
            industries = Industry.objects.filter(
                organization=self.organization,
                is_active=True
            ).order_by('name')
        else:
            industries = Industry.objects.none()

        # Create a field for each industry
        for industry in industries:
            field_name = f'industry_{industry.code}'
            self.fields[field_name] = forms.ChoiceField(
                label=industry.name,
                choices=[
                    ('left', 'Left Aligned'),
                    ('right', 'Right Aligned'),
                ],
                initial='left',
                widget=forms.Select(attrs={
                    'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                })
            )

    def get_industry_settings(self):
        """Convert form data to industry settings dictionary"""
        settings = {}
        for field_name, value in self.cleaned_data.items():
            if field_name.startswith('industry_'):
                industry_code = field_name.replace('industry_', '')
                settings[industry_code] = value
        return settings

    def clean_weather_location(self):
        location = self.cleaned_data.get('weather_location')
        if self.cleaned_data.get('openweather_enabled') and not location:
            raise forms.ValidationError('Weather location is required when weather is enabled.')
        return location

    def clean_openweather_api_key(self):
        api_key = self.cleaned_data.get('openweather_api_key')
        if self.cleaned_data.get('openweather_enabled') and not api_key:
            raise forms.ValidationError('API key is required when weather is enabled.')
        return api_key
