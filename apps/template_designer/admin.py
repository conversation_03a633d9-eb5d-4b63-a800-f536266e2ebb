from django.contrib import admin
from .models import TemplateCategory, Template, TemplateVersion, TemplateUsage


@admin.register(TemplateCategory)
class TemplateCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'description', 'icon', 'color', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'description']


@admin.register(Template)
class TemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'template_type', 'category', 'created_by', 'is_public', 'is_active', 'usage_count', 'created_at']
    list_filter = ['template_type', 'category', 'is_public', 'is_active', 'created_at']
    search_fields = ['name', 'description', 'content']
    readonly_fields = ['usage_count', 'created_at', 'updated_at']
    filter_horizontal = []

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'template_type', 'category')
        }),
        ('Content', {
            'fields': ('content', 'variables', 'layout_config', 'style_config')
        }),
        ('Settings', {
            'fields': ('is_public', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_by', 'usage_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(TemplateVersion)
class TemplateVersionAdmin(admin.ModelAdmin):
    list_display = ['template', 'version_number', 'created_by', 'created_at']
    list_filter = ['created_at', 'template__template_type']
    search_fields = ['template__name', 'version_number', 'change_summary']
    readonly_fields = ['created_at']


@admin.register(TemplateUsage)
class TemplateUsageAdmin(admin.ModelAdmin):
    list_display = ['template', 'used_by', 'used_at']
    list_filter = ['used_at', 'template__template_type']
    search_fields = ['template__name', 'used_by__username']
    readonly_fields = ['used_at']
