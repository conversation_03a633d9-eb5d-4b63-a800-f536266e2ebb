from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.contrib import messages
from django.db.models import Q
from .models import Template, TemplateCategory, TemplateVersion, TemplateUsage
import json


@login_required
def template_designer_dashboard(request):
    """Main dashboard for template designer"""
    # Get user's templates
    user_templates = Template.objects.filter(created_by=request.user, is_active=True)

    # Get public templates
    public_templates = Template.objects.filter(is_public=True, is_active=True)

    # Get categories
    categories = TemplateCategory.objects.all()

    # Recent activity
    recent_usage = TemplateUsage.objects.filter(used_by=request.user)[:5]

    context = {
        'user_templates': user_templates[:6],  # Show 6 most recent
        'public_templates': public_templates[:6],
        'categories': categories,
        'recent_usage': recent_usage,
        'total_templates': user_templates.count(),
        'total_public': public_templates.count(),
    }

    return render(request, 'template_designer/dashboard.html', context)


@login_required
def template_list(request):
    """List all templates with filtering"""
    templates = Template.objects.filter(
        Q(created_by=request.user) | Q(is_public=True),
        is_active=True
    )

    # Filter by category
    category_id = request.GET.get('category')
    if category_id:
        templates = templates.filter(category_id=category_id)

    # Filter by type
    template_type = request.GET.get('type')
    if template_type:
        templates = templates.filter(template_type=template_type)

    # Search
    search = request.GET.get('search')
    if search:
        templates = templates.filter(
            Q(name__icontains=search) | Q(description__icontains=search)
        )

    categories = TemplateCategory.objects.all()

    context = {
        'templates': templates,
        'categories': categories,
        'current_category': category_id,
        'current_type': template_type,
        'search_query': search,
        'template_types': Template.TEMPLATE_TYPES,
    }

    return render(request, 'template_designer/template_list.html', context)


@login_required
def template_detail(request, template_id):
    """View template details"""
    template = get_object_or_404(Template, id=template_id)

    # Check permissions
    if not template.is_public and template.created_by != request.user:
        messages.error(request, "You don't have permission to view this template.")
        return redirect('template_designer:dashboard')

    # Get versions
    versions = template.versions.all()[:10]

    context = {
        'template': template,
        'versions': versions,
        'can_edit': template.created_by == request.user,
    }

    return render(request, 'template_designer/template_detail.html', context)


@login_required
def template_editor(request, template_id=None):
    """Template editor interface"""
    template = None
    if template_id:
        template = get_object_or_404(Template, id=template_id)
        # Check permissions
        if template.created_by != request.user:
            messages.error(request, "You don't have permission to edit this template.")
            return redirect('template_designer:dashboard')

    categories = TemplateCategory.objects.all()

    context = {
        'template': template,
        'categories': categories,
        'template_types': Template.TEMPLATE_TYPES,
        'is_editing': template is not None,
    }

    return render(request, 'template_designer/editor.html', context)


@login_required
def save_template(request):
    """Save template via AJAX"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        data = json.loads(request.body)
        template_id = data.get('template_id')

        if template_id:
            # Update existing template
            template = get_object_or_404(Template, id=template_id, created_by=request.user)
        else:
            # Create new template
            template = Template(created_by=request.user)

        # Update fields
        template.name = data.get('name', '')
        template.description = data.get('description', '')
        template.template_type = data.get('template_type', 'custom')
        template.content = data.get('content', '')
        template.variables = data.get('variables', {})
        template.layout_config = data.get('layout_config', {})
        template.style_config = data.get('style_config', {})
        template.is_public = data.get('is_public', False)

        # Set category if provided
        category_id = data.get('category_id')
        if category_id:
            template.category_id = category_id

        template.save()

        # Create version if this is an update
        if template_id:
            version_number = f"1.{template.versions.count()}"
            TemplateVersion.objects.create(
                template=template,
                version_number=version_number,
                content=template.content,
                variables=template.variables,
                layout_config=template.layout_config,
                style_config=template.style_config,
                change_summary=data.get('change_summary', 'Template updated'),
                created_by=request.user
            )

        return JsonResponse({
            'success': True,
            'template_id': template.id,
            'message': 'Template saved successfully'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)
