from django.db import models
from django.contrib.auth.models import User


class TemplateCategory(models.Model):
    """Categories for organizing templates"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, default='fa-solid fa-file')
    color = models.CharField(max_length=7, default='#3B82F6')  # Hex color
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Template Categories"
        ordering = ['name']

    def __str__(self):
        return self.name


class Template(models.Model):
    """Main template model for the designer"""
    TEMPLATE_TYPES = [
        ('email', 'Email Template'),
        ('document', 'Document Template'),
        ('report', 'Report Template'),
        ('form', 'Form Template'),
        ('webpage', 'Web Page Template'),
        ('custom', 'Custom Template'),
    ]

    name = models.Char<PERSON><PERSON>(max_length=200)
    description = models.TextField(blank=True)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES, default='custom')
    category = models.ForeignKey(TemplateCategory, on_delete=models.SET_NULL, null=True, blank=True)

    # Template content and structure
    content = models.TextField(help_text="Template content with placeholders")
    variables = models.JSONField(default=dict, blank=True, help_text="Template variables and their types")
    layout_config = models.JSONField(default=dict, blank=True, help_text="Layout configuration")
    style_config = models.JSONField(default=dict, blank=True, help_text="Styling configuration")

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_public = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)

    # Usage tracking
    usage_count = models.PositiveIntegerField(default=0)

    class Meta:
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    def get_variables_list(self):
        """Return a list of variable names"""
        return list(self.variables.keys()) if self.variables else []


class TemplateVersion(models.Model):
    """Version history for templates"""
    template = models.ForeignKey(Template, on_delete=models.CASCADE, related_name='versions')
    version_number = models.CharField(max_length=20)
    content = models.TextField()
    variables = models.JSONField(default=dict, blank=True)
    layout_config = models.JSONField(default=dict, blank=True)
    style_config = models.JSONField(default=dict, blank=True)

    change_summary = models.TextField(blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        unique_together = ['template', 'version_number']

    def __str__(self):
        return f"{self.template.name} v{self.version_number}"


class TemplateUsage(models.Model):
    """Track template usage"""
    template = models.ForeignKey(Template, on_delete=models.CASCADE, related_name='usage_logs')
    used_by = models.ForeignKey(User, on_delete=models.CASCADE)
    used_at = models.DateTimeField(auto_now_add=True)
    context_data = models.JSONField(default=dict, blank=True, help_text="Data used when template was applied")

    class Meta:
        ordering = ['-used_at']

    def __str__(self):
        return f"{self.template.name} used by {self.used_by.username}"
