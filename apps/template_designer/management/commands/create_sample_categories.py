from django.core.management.base import BaseCommand
from apps.template_designer.models import TemplateCategory


class Command(BaseCommand):
    help = 'Create sample template categories'

    def handle(self, *args, **options):
        categories = [
            {
                'name': 'Email Templates',
                'description': 'Templates for email communications',
                'icon': 'fa-solid fa-envelope',
                'color': '#3B82F6'
            },
            {
                'name': 'Document Templates',
                'description': 'Templates for documents and reports',
                'icon': 'fa-solid fa-file-text',
                'color': '#10B981'
            },
            {
                'name': 'Form Templates',
                'description': 'Templates for forms and surveys',
                'icon': 'fa-solid fa-clipboard-list',
                'color': '#8B5CF6'
            },
            {
                'name': 'Web Templates',
                'description': 'Templates for web pages and content',
                'icon': 'fa-solid fa-globe',
                'color': '#F59E0B'
            },
            {
                'name': 'Report Templates',
                'description': 'Templates for business reports',
                'icon': 'fa-solid fa-chart-bar',
                'color': '#EF4444'
            },
            {
                'name': 'Marketing Templates',
                'description': 'Templates for marketing materials',
                'icon': 'fa-solid fa-bullhorn',
                'color': '#EC4899'
            }
        ]

        for category_data in categories:
            category, created = TemplateCategory.objects.get_or_create(
                name=category_data['name'],
                defaults=category_data
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created category: {category.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Category already exists: {category.name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample categories')
        )
