from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from apps.template_designer.models import Template, TemplateCategory


class Command(BaseCommand):
    help = 'Create radio industry specific templates'

    def handle(self, *args, **options):
        # Get or create a user for the templates
        user, created = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>', 'is_staff': True, 'is_superuser': True}
        )
        
        # Get categories
        email_category = TemplateCategory.objects.filter(name='Email Templates').first()
        document_category = TemplateCategory.objects.filter(name='Document Templates').first()
        report_category = TemplateCategory.objects.filter(name='Report Templates').first()
        marketing_category = TemplateCategory.objects.filter(name='Marketing Templates').first()

        templates = [
            # RADIO INDUSTRY TEMPLATES
            {
                'name': 'Radio Show Rundown',
                'description': 'Template for radio show planning and rundown',
                'template_type': 'document',
                'category': document_category,
                'content': '''<div class="p-8">
    <div class="text-center border-b border-gray-200 pb-6 mb-8">
        <h1 class="text-3xl font-bold text-gray-800">RADIO SHOW RUNDOWN</h1>
        <div class="grid grid-cols-3 gap-4 mt-4 text-sm">
            <div><strong>Show:</strong> {{show_name}}</div>
            <div><strong>Date:</strong> {{show_date}}</div>
            <div><strong>Host:</strong> {{host_name}}</div>
        </div>
        <div class="grid grid-cols-3 gap-4 mt-2 text-sm">
            <div><strong>Time:</strong> {{show_time}}</div>
            <div><strong>Duration:</strong> {{show_duration}}</div>
            <div><strong>Producer:</strong> {{producer_name}}</div>
        </div>
    </div>

    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Show Overview</h2>
        <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-gray-700">{{show_overview}}</p>
        </div>
    </div>

    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Segment Schedule</h2>
        <table class="w-full text-sm border-collapse border border-gray-200">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border border-gray-200 p-3 text-left">Time</th>
                    <th class="border border-gray-200 p-3 text-left">Segment</th>
                    <th class="border border-gray-200 p-3 text-left">Content</th>
                    <th class="border border-gray-200 p-3 text-left">Duration</th>
                    <th class="border border-gray-200 p-3 text-left">Notes</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="border border-gray-200 p-3">{{segment_1_time}}</td>
                    <td class="border border-gray-200 p-3">{{segment_1_name}}</td>
                    <td class="border border-gray-200 p-3">{{segment_1_content}}</td>
                    <td class="border border-gray-200 p-3">{{segment_1_duration}}</td>
                    <td class="border border-gray-200 p-3">{{segment_1_notes}}</td>
                </tr>
                <tr>
                    <td class="border border-gray-200 p-3">{{segment_2_time}}</td>
                    <td class="border border-gray-200 p-3">{{segment_2_name}}</td>
                    <td class="border border-gray-200 p-3">{{segment_2_content}}</td>
                    <td class="border border-gray-200 p-3">{{segment_2_duration}}</td>
                    <td class="border border-gray-200 p-3">{{segment_2_notes}}</td>
                </tr>
                <tr>
                    <td class="border border-gray-200 p-3">{{segment_3_time}}</td>
                    <td class="border border-gray-200 p-3">{{segment_3_name}}</td>
                    <td class="border border-gray-200 p-3">{{segment_3_content}}</td>
                    <td class="border border-gray-200 p-3">{{segment_3_duration}}</td>
                    <td class="border border-gray-200 p-3">{{segment_3_notes}}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Music Playlist</h3>
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="text-sm text-gray-700">{{music_playlist}}</div>
            </div>
        </div>
        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Sponsor Mentions</h3>
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="text-sm text-gray-700">{{sponsor_mentions}}</div>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Technical Requirements</h3>
        <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
            <div class="text-sm text-gray-700">{{technical_requirements}}</div>
        </div>
    </div>

    <div class="text-center text-sm text-gray-500 border-t pt-4">
        <p>Prepared by: {{prepared_by}} | Contact: {{contact_info}}</p>
    </div>
</div>''',
                'variables': {
                    'show_name': {'type': 'text', 'required': True},
                    'show_date': {'type': 'date', 'required': True},
                    'host_name': {'type': 'text', 'required': True},
                    'show_time': {'type': 'text', 'required': True},
                    'show_duration': {'type': 'text', 'required': True},
                    'producer_name': {'type': 'text', 'required': True},
                    'show_overview': {'type': 'text', 'required': True},
                    'segment_1_time': {'type': 'text', 'required': True},
                    'segment_1_name': {'type': 'text', 'required': True},
                    'segment_1_content': {'type': 'text', 'required': True},
                    'segment_1_duration': {'type': 'text', 'required': True},
                    'segment_1_notes': {'type': 'text', 'required': False},
                    'segment_2_time': {'type': 'text', 'required': True},
                    'segment_2_name': {'type': 'text', 'required': True},
                    'segment_2_content': {'type': 'text', 'required': True},
                    'segment_2_duration': {'type': 'text', 'required': True},
                    'segment_2_notes': {'type': 'text', 'required': False},
                    'segment_3_time': {'type': 'text', 'required': True},
                    'segment_3_name': {'type': 'text', 'required': True},
                    'segment_3_content': {'type': 'text', 'required': True},
                    'segment_3_duration': {'type': 'text', 'required': True},
                    'segment_3_notes': {'type': 'text', 'required': False},
                    'music_playlist': {'type': 'text', 'required': True},
                    'sponsor_mentions': {'type': 'text', 'required': True},
                    'technical_requirements': {'type': 'text', 'required': False},
                    'prepared_by': {'type': 'text', 'required': True},
                    'contact_info': {'type': 'text', 'required': True}
                },
                'is_public': True
            },
            {
                'name': 'Client Mention Report',
                'description': 'Report template for client mention tracking and analytics',
                'template_type': 'report',
                'category': report_category,
                'content': '''<div class="p-8">
    <div class="flex justify-between items-start border-b border-gray-200 pb-6 mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800">CLIENT MENTION REPORT</h1>
            <h2 class="text-xl text-gray-600 mt-2">{{client_name}}</h2>
        </div>
        <div class="text-right text-sm text-gray-600">
            <p><strong>Report Period:</strong> {{report_period}}</p>
            <p><strong>Generated:</strong> {{report_date}}</p>
            <p><strong>Account Manager:</strong> {{account_manager}}</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-3xl font-bold text-blue-600">{{total_mentions}}</div>
            <div class="text-sm text-blue-800">Total Mentions</div>
        </div>
        <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-3xl font-bold text-green-600">{{completed_mentions}}</div>
            <div class="text-sm text-green-800">Completed</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 rounded-lg">
            <div class="text-3xl font-bold text-yellow-600">{{pending_mentions}}</div>
            <div class="text-sm text-yellow-800">Pending</div>
        </div>
        <div class="text-center p-4 bg-purple-50 rounded-lg">
            <div class="text-3xl font-bold text-purple-600">{{total_airtime}}</div>
            <div class="text-sm text-purple-800">Total Airtime</div>
        </div>
    </div>

    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Mention Breakdown by Show</h2>
        <table class="w-full text-sm border-collapse border border-gray-200">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border border-gray-200 p-3 text-left">Show Name</th>
                    <th class="border border-gray-200 p-3 text-center">Mentions</th>
                    <th class="border border-gray-200 p-3 text-center">Airtime</th>
                    <th class="border border-gray-200 p-3 text-center">Completion Rate</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="border border-gray-200 p-3">{{show_1_name}}</td>
                    <td class="border border-gray-200 p-3 text-center">{{show_1_mentions}}</td>
                    <td class="border border-gray-200 p-3 text-center">{{show_1_airtime}}</td>
                    <td class="border border-gray-200 p-3 text-center">{{show_1_completion}}</td>
                </tr>
                <tr>
                    <td class="border border-gray-200 p-3">{{show_2_name}}</td>
                    <td class="border border-gray-200 p-3 text-center">{{show_2_mentions}}</td>
                    <td class="border border-gray-200 p-3 text-center">{{show_2_airtime}}</td>
                    <td class="border border-gray-200 p-3 text-center">{{show_2_completion}}</td>
                </tr>
                <tr>
                    <td class="border border-gray-200 p-3">{{show_3_name}}</td>
                    <td class="border border-gray-200 p-3 text-center">{{show_3_mentions}}</td>
                    <td class="border border-gray-200 p-3 text-center">{{show_3_airtime}}</td>
                    <td class="border border-gray-200 p-3 text-center">{{show_3_completion}}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Performance Highlights</h3>
            <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
                <div class="text-sm text-gray-700">{{performance_highlights}}</div>
            </div>
        </div>
        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Areas for Improvement</h3>
            <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                <div class="text-sm text-gray-700">{{improvement_areas}}</div>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Upcoming Schedule</h3>
        <div class="bg-blue-50 p-4 rounded-lg">
            <div class="text-sm text-gray-700">{{upcoming_schedule}}</div>
        </div>
    </div>

    <div class="text-center text-sm text-gray-500 border-t pt-4">
        <p>For questions about this report, contact {{account_manager}} at {{contact_email}}</p>
    </div>
</div>''',
                'variables': {
                    'client_name': {'type': 'text', 'required': True},
                    'report_period': {'type': 'text', 'required': True},
                    'report_date': {'type': 'date', 'required': True},
                    'account_manager': {'type': 'text', 'required': True},
                    'contact_email': {'type': 'email', 'required': True},
                    'total_mentions': {'type': 'number', 'required': True},
                    'completed_mentions': {'type': 'number', 'required': True},
                    'pending_mentions': {'type': 'number', 'required': True},
                    'total_airtime': {'type': 'text', 'required': True},
                    'show_1_name': {'type': 'text', 'required': True},
                    'show_1_mentions': {'type': 'number', 'required': True},
                    'show_1_airtime': {'type': 'text', 'required': True},
                    'show_1_completion': {'type': 'text', 'required': True},
                    'show_2_name': {'type': 'text', 'required': False},
                    'show_2_mentions': {'type': 'number', 'required': False},
                    'show_2_airtime': {'type': 'text', 'required': False},
                    'show_2_completion': {'type': 'text', 'required': False},
                    'show_3_name': {'type': 'text', 'required': False},
                    'show_3_mentions': {'type': 'number', 'required': False},
                    'show_3_airtime': {'type': 'text', 'required': False},
                    'show_3_completion': {'type': 'text', 'required': False},
                    'performance_highlights': {'type': 'text', 'required': True},
                    'improvement_areas': {'type': 'text', 'required': True},
                    'upcoming_schedule': {'type': 'text', 'required': True}
                },
                'is_public': True
            }
        ]

        for template_data in templates:
            template, created = Template.objects.get_or_create(
                name=template_data['name'],
                created_by=user,
                defaults=template_data
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Template already exists: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Successfully created radio industry templates')
        )
