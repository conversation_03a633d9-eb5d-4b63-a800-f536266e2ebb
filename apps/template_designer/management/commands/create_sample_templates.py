from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from apps.template_designer.models import Template, TemplateCategory


class Command(BaseCommand):
    help = 'Create sample templates for demonstration'

    def handle(self, *args, **options):
        # Get or create a user for the templates
        user, created = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>', 'is_staff': True, 'is_superuser': True}
        )
        
        # Get categories
        email_category = TemplateCategory.objects.filter(name='Email Templates').first()
        document_category = TemplateCategory.objects.filter(name='Document Templates').first()
        report_category = TemplateCategory.objects.filter(name='Report Templates').first()

        templates = [
            # EMAIL TEMPLATES
            {
                'name': 'Welcome Email Template',
                'description': 'A friendly welcome email template for new users',
                'template_type': 'email',
                'category': email_category,
                'content': '''<div class="p-8">
    <div class="text-center mb-8">
        <div class="w-32 h-12 bg-primary-100 flex items-center justify-center text-primary-600 border border-dashed border-primary-400 mx-auto mb-4">
            <i class="fa-solid fa-building mr-2"></i>
            <span class="text-sm">{{company_name}}</span>
        </div>
    </div>

    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-4">Welcome to {{company_name}}, {{user_name}}!</h1>
        <p class="text-gray-600 mb-4">We're thrilled to have you join our community.</p>
    </div>

    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h3 class="font-semibold text-gray-700 mb-2">Account Details:</h3>
        <ul class="text-sm text-gray-600">
            <li><strong>Username:</strong> {{username}}</li>
            <li><strong>Email:</strong> {{email}}</li>
            <li><strong>Registration Date:</strong> {{registration_date}}</li>
        </ul>
    </div>

    <div class="mb-6">
        <h3 class="font-semibold text-gray-700 mb-2">Next Steps:</h3>
        <ol class="list-decimal list-inside text-gray-600 space-y-1">
            <li>Complete your profile setup</li>
            <li>Explore our features</li>
            <li>Join our community forum</li>
        </ol>
    </div>

    <div class="text-center mb-6">
        <p class="text-gray-600">Questions? Contact us at <a href="mailto:{{support_email}}" class="text-primary-600">{{support_email}}</a></p>
    </div>

    <div class="text-center text-sm text-gray-500 border-t pt-4">
        <p>Best regards,<br>The {{company_name}} Team</p>
        <p class="mt-2">This email was sent to {{email}}.</p>
    </div>
</div>''',
                'variables': {
                    'company_name': {'type': 'text', 'required': True},
                    'user_name': {'type': 'text', 'required': True},
                    'username': {'type': 'text', 'required': True},
                    'email': {'type': 'email', 'required': True},
                    'registration_date': {'type': 'date', 'required': True},
                    'support_email': {'type': 'email', 'required': True}
                },
                'is_public': True
            },
            {
                'name': 'Newsletter Template',
                'description': 'Professional newsletter template for regular communications',
                'template_type': 'email',
                'category': email_category,
                'content': '''<div class="p-8">
    <div class="text-center border-b border-gray-200 pb-6 mb-6">
        <h1 class="text-3xl font-bold text-gray-800">{{newsletter_title}}</h1>
        <p class="text-gray-600 mt-2">{{issue_date}} | Issue #{{issue_number}}</p>
    </div>

    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Featured Story</h2>
        <div class="bg-primary-50 p-6 rounded-lg">
            <h3 class="text-lg font-semibold text-primary-800 mb-2">{{featured_title}}</h3>
            <p class="text-gray-700 mb-4">{{featured_excerpt}}</p>
            <a href="{{featured_link}}" class="text-primary-600 font-medium">Read More →</a>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div class="border border-gray-200 p-4 rounded-lg">
            <h3 class="font-semibold text-gray-800 mb-2">{{article_1_title}}</h3>
            <p class="text-sm text-gray-600 mb-2">{{article_1_excerpt}}</p>
            <a href="{{article_1_link}}" class="text-primary-600 text-sm">Read More</a>
        </div>
        <div class="border border-gray-200 p-4 rounded-lg">
            <h3 class="font-semibold text-gray-800 mb-2">{{article_2_title}}</h3>
            <p class="text-sm text-gray-600 mb-2">{{article_2_excerpt}}</p>
            <a href="{{article_2_link}}" class="text-primary-600 text-sm">Read More</a>
        </div>
    </div>

    <div class="text-center text-sm text-gray-500 border-t pt-4">
        <p>{{company_name}} | {{company_address}}</p>
        <p class="mt-2">
            <a href="{{unsubscribe_link}}" class="text-gray-500">Unsubscribe</a> |
            <a href="{{preferences_link}}" class="text-gray-500">Update Preferences</a>
        </p>
    </div>
</div>''',
                'variables': {
                    'newsletter_title': {'type': 'text', 'required': True},
                    'issue_date': {'type': 'date', 'required': True},
                    'issue_number': {'type': 'number', 'required': True},
                    'featured_title': {'type': 'text', 'required': True},
                    'featured_excerpt': {'type': 'text', 'required': True},
                    'featured_link': {'type': 'text', 'required': True},
                    'article_1_title': {'type': 'text', 'required': True},
                    'article_1_excerpt': {'type': 'text', 'required': True},
                    'article_1_link': {'type': 'text', 'required': True},
                    'article_2_title': {'type': 'text', 'required': True},
                    'article_2_excerpt': {'type': 'text', 'required': True},
                    'article_2_link': {'type': 'text', 'required': True},
                    'company_name': {'type': 'text', 'required': True},
                    'company_address': {'type': 'text', 'required': True},
                    'unsubscribe_link': {'type': 'text', 'required': True},
                    'preferences_link': {'type': 'text', 'required': True}
                },
                'is_public': True
            },
            # DOCUMENT TEMPLATES
            {
                'name': 'Meeting Minutes Template',
                'description': 'Professional template for recording meeting minutes',
                'template_type': 'document',
                'category': document_category,
                'content': '''<div class="p-8">
    <div class="text-center border-b border-gray-200 pb-6 mb-8">
        <h1 class="text-3xl font-bold text-gray-800">MEETING MINUTES</h1>
        <h2 class="text-xl text-gray-600 mt-2">{{meeting_title}}</h2>
    </div>

    <div class="grid grid-cols-2 gap-6 mb-8">
        <div>
            <h3 class="font-semibold text-gray-700 mb-3">Meeting Details</h3>
            <div class="space-y-2 text-sm">
                <div><strong>Date:</strong> {{meeting_date}}</div>
                <div><strong>Time:</strong> {{meeting_time}}</div>
                <div><strong>Location:</strong> {{meeting_location}}</div>
                <div><strong>Chair:</strong> {{meeting_chair}}</div>
            </div>
        </div>
        <div>
            <h3 class="font-semibold text-gray-700 mb-3">Attendees</h3>
            <div class="text-sm">{{attendees_list}}</div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="font-semibold text-gray-700 mb-3">Agenda Items</h3>
        <div class="bg-gray-50 p-4 rounded-lg">
            <div class="text-sm">{{agenda_items}}</div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="font-semibold text-gray-700 mb-3">Discussion Summary</h3>
        <div class="text-sm leading-relaxed">{{discussion_summary}}</div>
    </div>

    <div class="mb-8">
        <h3 class="font-semibold text-gray-700 mb-3">Action Items</h3>
        <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
            <div class="text-sm">{{action_items}}</div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="font-semibold text-gray-700 mb-3">Next Meeting</h3>
        <div class="text-sm space-y-1">
            <div><strong>Date:</strong> {{next_meeting_date}}</div>
            <div><strong>Time:</strong> {{next_meeting_time}}</div>
            <div><strong>Location:</strong> {{next_meeting_location}}</div>
        </div>
    </div>

    <div class="text-center text-sm text-gray-500 border-t pt-4">
        <p>Minutes prepared by: <strong>{{prepared_by}}</strong></p>
        <p>Date prepared: {{preparation_date}}</p>
    </div>
</div>''',
                'variables': {
                    'meeting_title': {'type': 'text', 'required': True},
                    'meeting_date': {'type': 'date', 'required': True},
                    'meeting_time': {'type': 'text', 'required': True},
                    'meeting_location': {'type': 'text', 'required': True},
                    'meeting_chair': {'type': 'text', 'required': True},
                    'attendees_list': {'type': 'text', 'required': True},
                    'agenda_items': {'type': 'text', 'required': True},
                    'discussion_summary': {'type': 'text', 'required': True},
                    'action_items': {'type': 'text', 'required': True},
                    'next_meeting_date': {'type': 'date', 'required': False},
                    'next_meeting_time': {'type': 'text', 'required': False},
                    'next_meeting_location': {'type': 'text', 'required': False},
                    'prepared_by': {'type': 'text', 'required': True},
                    'preparation_date': {'type': 'date', 'required': True}
                },
                'is_public': True
            },
            {
                'name': 'Invoice Template',
                'description': 'Professional invoice template for billing',
                'template_type': 'document',
                'category': document_category,
                'content': '''<div class="p-8">
    <div class="flex justify-between items-start mb-8 border-b border-gray-200 pb-6">
        <div>
            <div class="w-32 h-12 bg-gray-200 flex items-center justify-center text-gray-500 border border-dashed border-gray-400 mb-3">
                <i class="fa-solid fa-building mr-2"></i>
                <span class="text-sm">Logo</span>
            </div>
            <div class="text-sm text-gray-600">
                <p><strong>{{company_name}}</strong></p>
                <p>{{company_address}}</p>
                <p>{{company_phone}}</p>
                <p>{{company_email}}</p>
            </div>
        </div>
        <div class="text-right">
            <h1 class="text-3xl font-bold text-gray-800 mb-4">INVOICE</h1>
            <div class="text-sm text-gray-600 space-y-1">
                <p><span class="font-medium">Invoice #:</span> {{invoice_number}}</p>
                <p><span class="font-medium">Date:</span> {{invoice_date}}</p>
                <p><span class="font-medium">Due Date:</span> {{due_date}}</p>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h2 class="text-sm font-semibold text-gray-700 mb-3">BILL TO:</h2>
        <div class="text-sm text-gray-600">
            <p class="font-medium">{{client_name}}</p>
            <p>{{client_address}}</p>
            <p>{{client_email}}</p>
            <p>{{client_phone}}</p>
        </div>
    </div>

    <div class="mb-8">
        <table class="w-full text-sm border-collapse">
            <thead>
                <tr class="bg-gray-100">
                    <th class="py-3 px-4 text-left font-semibold text-gray-700 border border-gray-200">Item</th>
                    <th class="py-3 px-4 text-left font-semibold text-gray-700 border border-gray-200">Description</th>
                    <th class="py-3 px-4 text-center font-semibold text-gray-700 border border-gray-200">Qty</th>
                    <th class="py-3 px-4 text-right font-semibold text-gray-700 border border-gray-200">Price</th>
                    <th class="py-3 px-4 text-right font-semibold text-gray-700 border border-gray-200">Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="py-3 px-4 border border-gray-200">{{item_1_name}}</td>
                    <td class="py-3 px-4 border border-gray-200">{{item_1_description}}</td>
                    <td class="py-3 px-4 text-center border border-gray-200">{{item_1_quantity}}</td>
                    <td class="py-3 px-4 text-right border border-gray-200">${{item_1_price}}</td>
                    <td class="py-3 px-4 text-right border border-gray-200">${{item_1_amount}}</td>
                </tr>
                <tr>
                    <td class="py-3 px-4 border border-gray-200">{{item_2_name}}</td>
                    <td class="py-3 px-4 border border-gray-200">{{item_2_description}}</td>
                    <td class="py-3 px-4 text-center border border-gray-200">{{item_2_quantity}}</td>
                    <td class="py-3 px-4 text-right border border-gray-200">${{item_2_price}}</td>
                    <td class="py-3 px-4 text-right border border-gray-200">${{item_2_amount}}</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="flex justify-end mb-8">
        <div class="w-64">
            <div class="flex justify-between py-2 border-b border-gray-200">
                <span class="font-medium text-gray-700">Subtotal:</span>
                <span>${{subtotal}}</span>
            </div>
            <div class="flex justify-between py-2 border-b border-gray-200">
                <span class="font-medium text-gray-700">Tax ({{tax_rate}}%):</span>
                <span>${{tax_amount}}</span>
            </div>
            <div class="flex justify-between py-3 font-bold text-lg text-gray-800">
                <span>Total:</span>
                <span>${{total_amount}}</span>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-sm font-semibold text-gray-700 mb-2">NOTES:</h3>
        <p class="text-sm text-gray-600">{{notes}}</p>
    </div>

    <div class="text-center text-sm text-gray-500 border-t pt-4">
        <p>Thank you for your business!</p>
        <p>{{company_name}} | {{company_website}}</p>
    </div>
</div>''',
                'variables': {
                    'company_name': {'type': 'text', 'required': True},
                    'company_address': {'type': 'text', 'required': True},
                    'company_phone': {'type': 'text', 'required': True},
                    'company_email': {'type': 'email', 'required': True},
                    'company_website': {'type': 'text', 'required': True},
                    'invoice_number': {'type': 'text', 'required': True},
                    'invoice_date': {'type': 'date', 'required': True},
                    'due_date': {'type': 'date', 'required': True},
                    'client_name': {'type': 'text', 'required': True},
                    'client_address': {'type': 'text', 'required': True},
                    'client_email': {'type': 'email', 'required': True},
                    'client_phone': {'type': 'text', 'required': True},
                    'item_1_name': {'type': 'text', 'required': True},
                    'item_1_description': {'type': 'text', 'required': True},
                    'item_1_quantity': {'type': 'number', 'required': True},
                    'item_1_price': {'type': 'number', 'required': True},
                    'item_1_amount': {'type': 'number', 'required': True},
                    'item_2_name': {'type': 'text', 'required': False},
                    'item_2_description': {'type': 'text', 'required': False},
                    'item_2_quantity': {'type': 'number', 'required': False},
                    'item_2_price': {'type': 'number', 'required': False},
                    'item_2_amount': {'type': 'number', 'required': False},
                    'subtotal': {'type': 'number', 'required': True},
                    'tax_rate': {'type': 'number', 'required': True},
                    'tax_amount': {'type': 'number', 'required': True},
                    'total_amount': {'type': 'number', 'required': True},
                    'notes': {'type': 'text', 'required': False}
                },
                'is_public': True
            },
            # REPORT TEMPLATES
            {
                'name': 'Monthly Report Template',
                'description': 'Comprehensive monthly business report template',
                'template_type': 'report',
                'category': report_category,
                'content': '''<div class="p-8">
    <div class="text-center border-b border-gray-200 pb-6 mb-8">
        <h1 class="text-3xl font-bold text-gray-800">MONTHLY REPORT</h1>
        <h2 class="text-xl text-gray-600 mt-2">{{report_month}} {{report_year}}</h2>
        <p class="text-sm text-gray-500 mt-2">Prepared by {{prepared_by}} | {{report_date}}</p>
    </div>

    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 border-l-4 border-primary-500 pl-4">Executive Summary</h2>
        <div class="bg-gray-50 p-6 rounded-lg">
            <p class="text-gray-700 leading-relaxed">{{executive_summary}}</p>
        </div>
    </div>

    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 border-l-4 border-primary-500 pl-4">Key Metrics</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-blue-600">${{total_revenue}}</div>
                <div class="text-sm text-blue-800">Total Revenue</div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-green-600">{{new_customers}}</div>
                <div class="text-sm text-green-800">New Customers</div>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-purple-600">{{retention_rate}}%</div>
                <div class="text-sm text-purple-800">Retention Rate</div>
            </div>
            <div class="bg-orange-50 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-orange-600">{{monthly_growth}}%</div>
                <div class="text-sm text-orange-800">Monthly Growth</div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Achievements</h3>
            <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
                <div class="text-sm text-gray-700">{{achievements}}</div>
            </div>
        </div>
        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Challenges</h3>
            <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-400">
                <div class="text-sm text-gray-700">{{challenges}}</div>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 border-l-4 border-primary-500 pl-4">Financial Overview</h2>
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <table class="w-full text-sm">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="py-3 px-4 text-left font-semibold text-gray-700">Category</th>
                        <th class="py-3 px-4 text-right font-semibold text-gray-700">Amount</th>
                        <th class="py-3 px-4 text-right font-semibold text-gray-700">Variance</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-t border-gray-200">
                        <td class="py-3 px-4 font-medium">Revenue</td>
                        <td class="py-3 px-4 text-right text-green-600">${{revenue}}</td>
                        <td class="py-3 px-4 text-right">{{budget_variance}}</td>
                    </tr>
                    <tr class="border-t border-gray-200">
                        <td class="py-3 px-4 font-medium">Expenses</td>
                        <td class="py-3 px-4 text-right text-red-600">${{expenses}}</td>
                        <td class="py-3 px-4 text-right">-</td>
                    </tr>
                    <tr class="border-t border-gray-200 bg-gray-50">
                        <td class="py-3 px-4 font-bold">Net Profit</td>
                        <td class="py-3 px-4 text-right font-bold text-blue-600">${{net_profit}}</td>
                        <td class="py-3 px-4 text-right">-</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Team Performance</h3>
        <div class="bg-blue-50 p-4 rounded-lg">
            <div class="text-sm text-gray-700">{{team_performance}}</div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Upcoming Initiatives</h3>
        <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
            <div class="text-sm text-gray-700">{{upcoming_initiatives}}</div>
        </div>
    </div>

    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 border-l-4 border-primary-500 pl-4">Recommendations</h2>
        <div class="bg-primary-50 p-6 rounded-lg">
            <div class="text-sm text-gray-700">{{recommendations}}</div>
        </div>
    </div>
</div>''',
                'variables': {
                    'report_month': {'type': 'text', 'required': True},
                    'report_year': {'type': 'number', 'required': True},
                    'prepared_by': {'type': 'text', 'required': True},
                    'report_date': {'type': 'date', 'required': True},
                    'executive_summary': {'type': 'text', 'required': True},
                    'total_revenue': {'type': 'text', 'required': True},
                    'new_customers': {'type': 'number', 'required': True},
                    'retention_rate': {'type': 'number', 'required': True},
                    'monthly_growth': {'type': 'number', 'required': True},
                    'achievements': {'type': 'text', 'required': True},
                    'challenges': {'type': 'text', 'required': True},
                    'revenue': {'type': 'text', 'required': True},
                    'expenses': {'type': 'text', 'required': True},
                    'net_profit': {'type': 'text', 'required': True},
                    'budget_variance': {'type': 'text', 'required': True},
                    'team_performance': {'type': 'text', 'required': True},
                    'upcoming_initiatives': {'type': 'text', 'required': True},
                    'recommendations': {'type': 'text', 'required': True}
                },
                'is_public': True
            },
            {
                'name': 'Project Status Report',
                'description': 'Weekly/monthly project status report template',
                'template_type': 'report',
                'category': report_category,
                'content': '''<div class="p-8">
    <div class="flex justify-between items-start border-b border-gray-200 pb-6 mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-800">PROJECT STATUS REPORT</h1>
            <h2 class="text-xl text-gray-600 mt-2">{{project_name}}</h2>
        </div>
        <div class="text-right text-sm text-gray-600">
            <p><strong>Report Period:</strong> {{report_period}}</p>
            <p><strong>Project Manager:</strong> {{project_manager}}</p>
            <p><strong>Date:</strong> {{report_date}}</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-2xl font-bold text-blue-600">{{completion_percentage}}%</div>
            <div class="text-sm text-blue-800">Complete</div>
        </div>
        <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-2xl font-bold text-green-600">{{budget_used}}%</div>
            <div class="text-sm text-green-800">Budget Used</div>
        </div>
        <div class="text-center p-4 bg-orange-50 rounded-lg">
            <div class="text-2xl font-bold text-orange-600">{{days_remaining}}</div>
            <div class="text-sm text-orange-800">Days Remaining</div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Project Overview</h3>
        <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-sm text-gray-700">{{project_overview}}</p>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Completed This Period</h3>
            <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
                <div class="text-sm text-gray-700">{{completed_tasks}}</div>
            </div>
        </div>
        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Upcoming Tasks</h3>
            <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                <div class="text-sm text-gray-700">{{upcoming_tasks}}</div>
            </div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Issues & Risks</h3>
        <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-400">
            <div class="text-sm text-gray-700">{{issues_risks}}</div>
        </div>
    </div>

    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Resource Allocation</h3>
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <table class="w-full text-sm">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="py-3 px-4 text-left font-semibold text-gray-700">Resource</th>
                        <th class="py-3 px-4 text-center font-semibold text-gray-700">Allocated</th>
                        <th class="py-3 px-4 text-center font-semibold text-gray-700">Utilized</th>
                        <th class="py-3 px-4 text-center font-semibold text-gray-700">Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-t border-gray-200">
                        <td class="py-3 px-4">{{resource_1_name}}</td>
                        <td class="py-3 px-4 text-center">{{resource_1_allocated}}</td>
                        <td class="py-3 px-4 text-center">{{resource_1_utilized}}</td>
                        <td class="py-3 px-4 text-center">{{resource_1_status}}</td>
                    </tr>
                    <tr class="border-t border-gray-200">
                        <td class="py-3 px-4">{{resource_2_name}}</td>
                        <td class="py-3 px-4 text-center">{{resource_2_allocated}}</td>
                        <td class="py-3 px-4 text-center">{{resource_2_utilized}}</td>
                        <td class="py-3 px-4 text-center">{{resource_2_status}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="text-center text-sm text-gray-500 border-t pt-4">
        <p>Next report due: {{next_report_date}}</p>
    </div>
</div>''',
                'variables': {
                    'project_name': {'type': 'text', 'required': True},
                    'report_period': {'type': 'text', 'required': True},
                    'project_manager': {'type': 'text', 'required': True},
                    'report_date': {'type': 'date', 'required': True},
                    'completion_percentage': {'type': 'number', 'required': True},
                    'budget_used': {'type': 'number', 'required': True},
                    'days_remaining': {'type': 'number', 'required': True},
                    'project_overview': {'type': 'text', 'required': True},
                    'completed_tasks': {'type': 'text', 'required': True},
                    'upcoming_tasks': {'type': 'text', 'required': True},
                    'issues_risks': {'type': 'text', 'required': True},
                    'resource_1_name': {'type': 'text', 'required': True},
                    'resource_1_allocated': {'type': 'text', 'required': True},
                    'resource_1_utilized': {'type': 'text', 'required': True},
                    'resource_1_status': {'type': 'text', 'required': True},
                    'resource_2_name': {'type': 'text', 'required': False},
                    'resource_2_allocated': {'type': 'text', 'required': False},
                    'resource_2_utilized': {'type': 'text', 'required': False},
                    'resource_2_status': {'type': 'text', 'required': False},
                    'next_report_date': {'type': 'date', 'required': True}
                },
                'is_public': True
            },

            # FORM TEMPLATES
            {
                'name': 'Contact Form Template',
                'description': 'Professional contact form for websites',
                'template_type': 'form',
                'category': TemplateCategory.objects.filter(name='Form Templates').first(),
                'content': '''<div class="p-8 max-w-2xl mx-auto">
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">Contact Us</h1>
        <p class="text-gray-600 mt-2">{{form_description}}</p>
    </div>

    <form class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                <input type="text" name="first_name" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                <input type="text" name="last_name" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
            </div>
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
            <input type="email" name="email" required
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
            <input type="tel" name="phone"
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
            <select name="subject" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                <option value="">Select a subject</option>
                <option value="general">General Inquiry</option>
                <option value="support">Technical Support</option>
                <option value="sales">Sales Question</option>
                <option value="partnership">Partnership</option>
            </select>
        </div>

        <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
            <textarea name="message" rows="5" required
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Please describe your inquiry..."></textarea>
        </div>

        <div class="flex items-center">
            <input type="checkbox" name="newsletter" id="newsletter"
                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
            <label for="newsletter" class="ml-2 block text-sm text-gray-700">
                Subscribe to our newsletter for updates
            </label>
        </div>

        <div class="text-center">
            <button type="submit"
                    class="bg-primary-600 text-white px-8 py-3 rounded-md font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                Send Message
            </button>
        </div>
    </form>

    <div class="text-center mt-8 text-sm text-gray-500">
        <p>We'll get back to you within {{response_time}} business hours.</p>
        <p class="mt-2">Or contact us directly at <a href="mailto:{{contact_email}}" class="text-primary-600">{{contact_email}}</a></p>
    </div>
</div>''',
                'variables': {
                    'form_description': {'type': 'text', 'required': True},
                    'response_time': {'type': 'text', 'required': True},
                    'contact_email': {'type': 'email', 'required': True}
                },
                'is_public': True
            },
            {
                'name': 'Survey Form Template',
                'description': 'Customer satisfaction survey form',
                'template_type': 'form',
                'category': TemplateCategory.objects.filter(name='Form Templates').first(),
                'content': '''<div class="p-8 max-w-3xl mx-auto">
    <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800">{{survey_title}}</h1>
        <p class="text-gray-600 mt-2">{{survey_description}}</p>
        <p class="text-sm text-gray-500 mt-2">Estimated time: {{estimated_time}} minutes</p>
    </div>

    <form class="space-y-8">
        <div class="bg-gray-50 p-6 rounded-lg">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Overall Satisfaction</h3>
            <p class="text-sm text-gray-600 mb-4">How satisfied are you with our {{service_type}}?</p>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500">Very Dissatisfied</span>
                <div class="flex space-x-2">
                    <label class="flex flex-col items-center">
                        <input type="radio" name="satisfaction" value="1" class="mb-1">
                        <span class="text-xs">1</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="satisfaction" value="2" class="mb-1">
                        <span class="text-xs">2</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="satisfaction" value="3" class="mb-1">
                        <span class="text-xs">3</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="satisfaction" value="4" class="mb-1">
                        <span class="text-xs">4</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="satisfaction" value="5" class="mb-1">
                        <span class="text-xs">5</span>
                    </label>
                </div>
                <span class="text-sm text-gray-500">Very Satisfied</span>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Service Quality</h3>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-700">Response Time</span>
                    <div class="flex space-x-2">
                        <label><input type="radio" name="response_time" value="excellent" class="mr-1">Excellent</label>
                        <label><input type="radio" name="response_time" value="good" class="mr-1">Good</label>
                        <label><input type="radio" name="response_time" value="fair" class="mr-1">Fair</label>
                        <label><input type="radio" name="response_time" value="poor" class="mr-1">Poor</label>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-700">Staff Helpfulness</span>
                    <div class="flex space-x-2">
                        <label><input type="radio" name="staff_helpfulness" value="excellent" class="mr-1">Excellent</label>
                        <label><input type="radio" name="staff_helpfulness" value="good" class="mr-1">Good</label>
                        <label><input type="radio" name="staff_helpfulness" value="fair" class="mr-1">Fair</label>
                        <label><input type="radio" name="staff_helpfulness" value="poor" class="mr-1">Poor</label>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-700">Problem Resolution</span>
                    <div class="flex space-x-2">
                        <label><input type="radio" name="problem_resolution" value="excellent" class="mr-1">Excellent</label>
                        <label><input type="radio" name="problem_resolution" value="good" class="mr-1">Good</label>
                        <label><input type="radio" name="problem_resolution" value="fair" class="mr-1">Fair</label>
                        <label><input type="radio" name="problem_resolution" value="poor" class="mr-1">Poor</label>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Additional Feedback</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">What did we do well?</label>
                    <textarea name="positive_feedback" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">How can we improve?</label>
                    <textarea name="improvement_suggestions" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"></textarea>
                </div>
            </div>
        </div>

        <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Recommendation</h3>
            <p class="text-sm text-gray-600 mb-4">How likely are you to recommend us to others?</p>
            <div class="flex justify-between items-center">
                <span class="text-sm text-gray-500">Not Likely</span>
                <div class="flex space-x-2">
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="0" class="mb-1">
                        <span class="text-xs">0</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="1" class="mb-1">
                        <span class="text-xs">1</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="2" class="mb-1">
                        <span class="text-xs">2</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="3" class="mb-1">
                        <span class="text-xs">3</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="4" class="mb-1">
                        <span class="text-xs">4</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="5" class="mb-1">
                        <span class="text-xs">5</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="6" class="mb-1">
                        <span class="text-xs">6</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="7" class="mb-1">
                        <span class="text-xs">7</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="8" class="mb-1">
                        <span class="text-xs">8</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="9" class="mb-1">
                        <span class="text-xs">9</span>
                    </label>
                    <label class="flex flex-col items-center">
                        <input type="radio" name="recommendation" value="10" class="mb-1">
                        <span class="text-xs">10</span>
                    </label>
                </div>
                <span class="text-sm text-gray-500">Very Likely</span>
            </div>
        </div>

        <div class="text-center">
            <button type="submit"
                    class="bg-primary-600 text-white px-8 py-3 rounded-md font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                Submit Survey
            </button>
        </div>
    </form>

    <div class="text-center mt-6 text-sm text-gray-500">
        <p>Thank you for taking the time to provide feedback!</p>
    </div>
</div>''',
                'variables': {
                    'survey_title': {'type': 'text', 'required': True},
                    'survey_description': {'type': 'text', 'required': True},
                    'estimated_time': {'type': 'number', 'required': True},
                    'service_type': {'type': 'text', 'required': True}
                },
                'is_public': True
            },

            # WEBPAGE TEMPLATES
            {
                'name': 'Landing Page Template',
                'description': 'Modern landing page for product launches',
                'template_type': 'webpage',
                'category': TemplateCategory.objects.filter(name='Web Templates').first(),
                'content': '''<div class="min-h-screen">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-primary-600 to-primary-800 text-white">
        <div class="max-w-7xl mx-auto px-4 py-20">
            <div class="text-center">
                <h1 class="text-5xl font-bold mb-6">{{hero_title}}</h1>
                <p class="text-xl mb-8 max-w-3xl mx-auto">{{hero_subtitle}}</p>
                <div class="space-x-4">
                    <button class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100">
                        {{cta_primary}}
                    </button>
                    <button class="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600">
                        {{cta_secondary}}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">{{features_title}}</h2>
                <p class="text-xl text-gray-600">{{features_subtitle}}</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center p-6">
                    <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-rocket text-primary-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">{{feature_1_title}}</h3>
                    <p class="text-gray-600">{{feature_1_description}}</p>
                </div>
                <div class="text-center p-6">
                    <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-shield-alt text-primary-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">{{feature_2_title}}</h3>
                    <p class="text-gray-600">{{feature_2_description}}</p>
                </div>
                <div class="text-center p-6">
                    <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fa-solid fa-chart-line text-primary-600 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">{{feature_3_title}}</h3>
                    <p class="text-gray-600">{{feature_3_description}}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Testimonials Section -->
    <div class="py-20">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">What Our Customers Say</h2>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gray-200 rounded-full mr-4"></div>
                        <div>
                            <h4 class="font-semibold text-gray-800">{{testimonial_1_name}}</h4>
                            <p class="text-sm text-gray-600">{{testimonial_1_title}}</p>
                        </div>
                    </div>
                    <p class="text-gray-700">"{{testimonial_1_content}}"</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gray-200 rounded-full mr-4"></div>
                        <div>
                            <h4 class="font-semibold text-gray-800">{{testimonial_2_name}}</h4>
                            <p class="text-sm text-gray-600">{{testimonial_2_title}}</p>
                        </div>
                    </div>
                    <p class="text-gray-700">"{{testimonial_2_content}}"</p>
                </div>
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-primary-600 text-white py-20">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">{{cta_section_title}}</h2>
            <p class="text-xl mb-8">{{cta_section_subtitle}}</p>
            <button class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100">
                {{cta_final}}
            </button>
        </div>
    </div>

    <!-- Footer -->
    <div class="bg-gray-800 text-white py-12">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center">
                <p class="text-gray-400">© {{current_year}} {{company_name}}. All rights reserved.</p>
                <div class="mt-4 space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-white">Terms of Service</a>
                    <a href="#" class="text-gray-400 hover:text-white">Contact</a>
                </div>
            </div>
        </div>
    </div>
</div>''',
                'variables': {
                    'hero_title': {'type': 'text', 'required': True},
                    'hero_subtitle': {'type': 'text', 'required': True},
                    'cta_primary': {'type': 'text', 'required': True},
                    'cta_secondary': {'type': 'text', 'required': True},
                    'features_title': {'type': 'text', 'required': True},
                    'features_subtitle': {'type': 'text', 'required': True},
                    'feature_1_title': {'type': 'text', 'required': True},
                    'feature_1_description': {'type': 'text', 'required': True},
                    'feature_2_title': {'type': 'text', 'required': True},
                    'feature_2_description': {'type': 'text', 'required': True},
                    'feature_3_title': {'type': 'text', 'required': True},
                    'feature_3_description': {'type': 'text', 'required': True},
                    'testimonial_1_name': {'type': 'text', 'required': True},
                    'testimonial_1_title': {'type': 'text', 'required': True},
                    'testimonial_1_content': {'type': 'text', 'required': True},
                    'testimonial_2_name': {'type': 'text', 'required': True},
                    'testimonial_2_title': {'type': 'text', 'required': True},
                    'testimonial_2_content': {'type': 'text', 'required': True},
                    'cta_section_title': {'type': 'text', 'required': True},
                    'cta_section_subtitle': {'type': 'text', 'required': True},
                    'cta_final': {'type': 'text', 'required': True},
                    'current_year': {'type': 'number', 'required': True},
                    'company_name': {'type': 'text', 'required': True}
                },
                'is_public': True
            }
        ]

        for template_data in templates:
            template, created = Template.objects.get_or_create(
                name=template_data['name'],
                created_by=user,
                defaults=template_data
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created template: {template.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Template already exists: {template.name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample templates')
        )
