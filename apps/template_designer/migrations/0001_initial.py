# Generated by Django 4.2.7 on 2025-06-20 17:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Template",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("email", "Email Template"),
                            ("document", "Document Template"),
                            ("report", "Report Template"),
                            ("form", "Form Template"),
                            ("webpage", "Web Page Template"),
                            ("custom", "Custom Template"),
                        ],
                        default="custom",
                        max_length=20,
                    ),
                ),
                (
                    "content",
                    models.TextField(help_text="Template content with placeholders"),
                ),
                (
                    "variables",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Template variables and their types",
                    ),
                ),
                (
                    "layout_config",
                    models.JSONField(
                        blank=True, default=dict, help_text="Layout configuration"
                    ),
                ),
                (
                    "style_config",
                    models.JSONField(
                        blank=True, default=dict, help_text="Styling configuration"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_public", models.BooleanField(default=False)),
                ("is_active", models.BooleanField(default=True)),
                ("usage_count", models.PositiveIntegerField(default=0)),
            ],
            options={
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="TemplateCategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                ("icon", models.CharField(default="fa-solid fa-file", max_length=50)),
                ("color", models.CharField(default="#3B82F6", max_length=7)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name_plural": "Template Categories",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="TemplateUsage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("used_at", models.DateTimeField(auto_now_add=True)),
                (
                    "context_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Data used when template was applied",
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usage_logs",
                        to="template_designer.template",
                    ),
                ),
                (
                    "used_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-used_at"],
            },
        ),
        migrations.AddField(
            model_name="template",
            name="category",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="template_designer.templatecategory",
            ),
        ),
        migrations.AddField(
            model_name="template",
            name="created_by",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.CreateModel(
            name="TemplateVersion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("version_number", models.CharField(max_length=20)),
                ("content", models.TextField()),
                ("variables", models.JSONField(blank=True, default=dict)),
                ("layout_config", models.JSONField(blank=True, default=dict)),
                ("style_config", models.JSONField(blank=True, default=dict)),
                ("change_summary", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="versions",
                        to="template_designer.template",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "unique_together": {("template", "version_number")},
            },
        ),
    ]
