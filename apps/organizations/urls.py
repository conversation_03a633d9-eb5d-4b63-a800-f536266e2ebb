from django.urls import path
from . import views

app_name = 'organizations'

urlpatterns = [
    # Signup wizard
    path('signup/', views.signup_wizard, name='signup_wizard'),
    path('signup/complete/<slug:slug>/', views.signup_complete, name='signup_complete'),

    # Organization management
    path('', views.organization_list, name='list'),
    path('create/', views.organization_create, name='create'),
    path('<slug:slug>/', views.organization_detail, name='detail'),
    path('<slug:slug>/edit/', views.organization_edit, name='edit'),
    path('<slug:slug>/delete/', views.organization_delete, name='delete'),

    # Organization switching
    path('<slug:slug>/switch/', views.switch_organization, name='switch'),
    
    # Member management
    path('<slug:slug>/members/', views.member_list, name='member_list'),
    path('<slug:slug>/members/invite/', views.invite_member, name='invite_member'),
    path('<slug:slug>/members/<int:user_id>/edit/', views.edit_member, name='edit_member'),
    path('<slug:slug>/members/<int:user_id>/remove/', views.remove_member, name='remove_member'),
    
    # Branch management
    path('<slug:slug>/branches/', views.branch_list, name='branch_list'),
    path('<slug:slug>/branches/create/', views.branch_create, name='branch_create'),
    path('<slug:org_slug>/branches/<slug:branch_slug>/', views.branch_detail, name='branch_detail'),
    path('<slug:org_slug>/branches/<slug:branch_slug>/edit/', views.branch_edit, name='branch_edit'),
    path('<slug:org_slug>/branches/<slug:branch_slug>/delete/', views.branch_delete, name='branch_delete'),
    
    # Invitation handling
    path('invitations/<str:token>/', views.accept_invitation, name='accept_invitation'),
    path('invitations/<str:token>/decline/', views.decline_invitation, name='decline_invitation'),
]
