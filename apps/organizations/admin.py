from django.contrib import admin
from .models import Organization, Branch, OrganizationMembership, OrganizationInvitation


class BranchInline(admin.TabularInline):
    model = Branch
    extra = 0
    fields = ['name', 'slug', 'manager', 'is_active']


class OrganizationMembershipInline(admin.TabularInline):
    model = OrganizationMembership
    extra = 0
    fields = ['user', 'role', 'branch', 'is_active', 'is_default']
    readonly_fields = ['invitation_accepted_at']


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'plan_type', 'total_users', 'is_active', 'created_at']
    list_filter = ['plan_type', 'is_active', 'created_at']
    search_fields = ['name', 'slug', 'email']
    list_editable = ['is_active']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at', 'total_users', 'total_mentions_this_month']
    prepopulated_fields = {'slug': ('name',)}
    inlines = [BranchInline, OrganizationMembershipInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'description', 'logo')
        }),
        ('Contact Information', {
            'fields': ('website', 'email', 'phone', 'address')
        }),
        ('Settings', {
            'fields': ('timezone', 'is_active')
        }),
        ('Plan & Limits', {
            'fields': ('plan_type', 'max_users', 'max_mentions_per_month')
        }),
        ('Statistics', {
            'fields': ('total_users', 'total_mentions_this_month'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Branch)
class BranchAdmin(admin.ModelAdmin):
    list_display = ['name', 'organization', 'manager', 'is_active', 'created_at']
    list_filter = ['organization', 'is_active', 'created_at']
    search_fields = ['name', 'organization__name', 'email']
    list_editable = ['is_active']
    ordering = ['organization', 'name']
    readonly_fields = ['created_at', 'updated_at']
    prepopulated_fields = {'slug': ('name',)}

    fieldsets = (
        ('Basic Information', {
            'fields': ('organization', 'name', 'slug', 'description', 'manager')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'address')
        }),
        ('Settings', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(OrganizationMembership)
class OrganizationMembershipAdmin(admin.ModelAdmin):
    list_display = ['user', 'organization', 'role', 'branch', 'is_active', 'is_default', 'created_at']
    list_filter = ['role', 'is_active', 'is_default', 'organization', 'created_at']
    search_fields = ['user__username', 'user__email', 'organization__name']
    list_editable = ['role', 'is_active']
    ordering = ['organization', 'user']
    readonly_fields = ['created_at', 'updated_at', 'invitation_accepted_at']

    fieldsets = (
        ('Membership Details', {
            'fields': ('user', 'organization', 'branch', 'role')
        }),
        ('Status', {
            'fields': ('is_active', 'is_default')
        }),
        ('Invitation Info', {
            'fields': ('invited_by', 'invitation_accepted_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(OrganizationInvitation)
class OrganizationInvitationAdmin(admin.ModelAdmin):
    list_display = ['email', 'organization', 'role', 'invited_by', 'is_accepted', 'is_expired', 'created_at']
    list_filter = ['role', 'is_accepted', 'organization', 'created_at']
    search_fields = ['email', 'organization__name', 'invited_by__username']
    ordering = ['-created_at']
    readonly_fields = ['created_at', 'updated_at', 'token', 'is_expired', 'is_valid']

    fieldsets = (
        ('Invitation Details', {
            'fields': ('organization', 'branch', 'email', 'role', 'invited_by')
        }),
        ('Status', {
            'fields': ('is_accepted', 'accepted_at', 'expires_at', 'is_expired', 'is_valid')
        }),
        ('Token', {
            'fields': ('token',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
