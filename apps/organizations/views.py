from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.contrib.auth.models import User
from django.utils.text import slugify
from django.utils import timezone
from allauth.account.utils import perform_login
from allauth.account.models import EmailAddress
from django.urls import reverse
from django.db import IntegrityError, transaction
from datetime import timedelta
import uuid

from .models import Organization, Branch, OrganizationMembership, OrganizationInvitation
from .forms import (
    SignupWizardStep1Form, SignupWizardStep2Form, SignupWizardStep3Form,
    SignupWizardStep4Form, SignupWizardStep5Form, OrganizationForm,
    BranchForm, OrganizationInvitationForm, MembershipEditForm
)
from apps.authentication.models import UserProfile
from django.http import HttpResponseRedirect


def cleanup_temp_files():
    """Clean up old temporary files"""
    from django.core.files.storage import default_storage
    import os
    from datetime import datetime, timedelta

    try:
        temp_dir = 'temp_uploads'
        if default_storage.exists(temp_dir):
            # Get files older than 1 hour
            cutoff_time = datetime.now() - timedelta(hours=1)

            # List files in temp directory
            dirs, files = default_storage.listdir(temp_dir)
            for file in files:
                file_path = os.path.join(temp_dir, file)
                try:
                    # Get file modification time
                    file_time = default_storage.get_modified_time(file_path)
                    if file_time < cutoff_time:
                        default_storage.delete(file_path)
                except:
                    pass  # Ignore errors for individual files
    except:
        pass  # Ignore errors in cleanup


def wizard_redirect(step):
    """Helper function to create wizard redirect URLs"""
    url = reverse('organizations:signup_wizard') + f'?step={step}'
    return HttpResponseRedirect(url)


def signup_wizard(request):
    """Multi-step signup wizard for new organizations"""
    step = request.GET.get('step', '1')

    # Clean up old temporary files
    cleanup_temp_files()

    # Initialize session data if not exists
    if 'signup_wizard_data' not in request.session:
        request.session['signup_wizard_data'] = {}

    wizard_data = request.session['signup_wizard_data']

    # Debug: Print session data
    print(f"Signup wizard step {step}, session data keys: {list(wizard_data.keys())}")

    if step == '1':
        return signup_wizard_step1(request, wizard_data)
    elif step == '2':
        return signup_wizard_step2(request, wizard_data)
    elif step == '3':
        return signup_wizard_step3(request, wizard_data)
    elif step == '4':
        return signup_wizard_step4(request, wizard_data)
    elif step == '5':
        return signup_wizard_step5(request, wizard_data)
    else:
        return redirect('organizations:signup_wizard')


def signup_wizard_step1(request, wizard_data):
    """Step 1: User Account Information"""
    if request.method == 'POST':
        form = SignupWizardStep1Form(request.POST)
        if form.is_valid():
            # Store step 1 data
            wizard_data['step1'] = form.cleaned_data
            request.session['signup_wizard_data'] = wizard_data
            request.session.modified = True
            return wizard_redirect(2)
    else:
        # Pre-populate form with existing data
        initial_data = wizard_data.get('step1', {})
        form = SignupWizardStep1Form(initial=initial_data)

    context = {
        'form': form,
        'step': 1,
        'step_title': 'Create Your Account',
        'step_description': 'Let\'s start by creating your user account',
        'progress_percentage': 20,
    }
    return render(request, 'organizations/signup_wizard.html', context)


def signup_wizard_step2(request, wizard_data):
    """Step 2: Organization Basic Information"""
    if 'step1' not in wizard_data:
        messages.error(request, 'Please complete step 1 first.')
        return wizard_redirect(1)

    if request.method == 'POST':
        form = SignupWizardStep2Form(request.POST)
        if form.is_valid():
            # Store step 2 data
            wizard_data['step2'] = form.cleaned_data
            request.session['signup_wizard_data'] = wizard_data
            request.session.modified = True
            return wizard_redirect(3)
    else:
        # Pre-populate form with existing data
        initial_data = wizard_data.get('step2', {})
        form = SignupWizardStep2Form(initial=initial_data)

    context = {
        'form': form,
        'step': 2,
        'step_title': 'Radio Station Information',
        'step_description': 'Tell us about your radio station',
        'progress_percentage': 40,
    }
    return render(request, 'organizations/signup_wizard.html', context)


def signup_wizard_step3(request, wizard_data):
    """Step 3: Contact Information & Location"""
    if 'step2' not in wizard_data:
        messages.error(request, 'Please complete previous steps first.')
        return wizard_redirect(1)

    if request.method == 'POST':
        form = SignupWizardStep3Form(request.POST)
        if form.is_valid():
            # Store step 3 data
            wizard_data['step3'] = form.cleaned_data
            request.session['signup_wizard_data'] = wizard_data
            request.session.modified = True
            return wizard_redirect(4)
    else:
        # Pre-populate form with existing data
        initial_data = wizard_data.get('step3', {})
        # Pre-populate organization email with user email if not set
        if not initial_data.get('organization_email') and wizard_data.get('step1', {}).get('email'):
            initial_data['organization_email'] = wizard_data['step1']['email']
        form = SignupWizardStep3Form(initial=initial_data)

    context = {
        'form': form,
        'step': 3,
        'step_title': 'Contact & Location',
        'step_description': 'Provide your station\'s contact information and location',
        'progress_percentage': 60,
    }
    return render(request, 'organizations/signup_wizard.html', context)


def signup_wizard_step4(request, wizard_data):
    """Step 4: Station Details & Configuration"""
    if 'step3' not in wizard_data:
        messages.error(request, 'Please complete previous steps first.')
        return wizard_redirect(1)

    if request.method == 'POST':
        form = SignupWizardStep4Form(request.POST, request.FILES)
        if form.is_valid():
            # Store step 4 data (excluding the logo file)
            step4_data = form.cleaned_data.copy()
            if 'logo' in step4_data:
                del step4_data['logo']  # Remove logo from cleaned_data to avoid serialization issues
            wizard_data['step4'] = step4_data

            # Handle file upload separately - save temporarily
            if 'logo' in request.FILES:
                import tempfile
                import os
                from django.core.files.storage import default_storage

                logo_file = request.FILES['logo']
                # Create a temporary file name
                temp_filename = f"temp_logo_{request.session.session_key}_{logo_file.name}"
                temp_path = f"temp_uploads/{temp_filename}"

                # Save the file temporarily
                saved_path = default_storage.save(temp_path, logo_file)
                wizard_data['logo_temp_path'] = saved_path
                wizard_data['logo_original_name'] = logo_file.name

            request.session['signup_wizard_data'] = wizard_data
            request.session.modified = True
            return wizard_redirect(5)
    else:
        # Pre-populate form with existing data
        initial_data = wizard_data.get('step4', {})
        form = SignupWizardStep4Form(initial=initial_data)

    context = {
        'form': form,
        'step': 4,
        'step_title': 'Station Configuration',
        'step_description': 'Configure your station settings and preferences',
        'progress_percentage': 80,
    }
    return render(request, 'organizations/signup_wizard.html', context)


def signup_wizard_step5(request, wizard_data):
    """Step 5: Final Setup & Confirmation"""
    # Check if we have all required data
    required_steps = ['step1', 'step2', 'step3', 'step4']
    missing_steps = [step for step in required_steps if step not in wizard_data]

    if missing_steps:
        messages.error(request, f'Please complete previous steps first. Missing: {", ".join(missing_steps)}')
        return wizard_redirect(1)

    if request.method == 'POST':
        form = SignupWizardStep5Form(request.POST)
        if form.is_valid():
            # Store step 5 data
            wizard_data['step5'] = form.cleaned_data
            request.session['signup_wizard_data'] = wizard_data
            request.session.modified = True

            # Create the organization and user
            try:
                with transaction.atomic():
                    # Validate wizard data before creation
                    if not all(key in wizard_data for key in required_steps):
                        raise ValueError("Missing required wizard data")

                    user, organization = create_organization_from_wizard_data(wizard_data)

                    # Log the user in using Django-Allauth
                    perform_login(request, user, email_verification='none')

                    # Clear wizard data and cleanup temp files
                    if 'signup_wizard_data' in request.session:
                        # Clean up any temporary logo file
                        wizard_data = request.session['signup_wizard_data']
                        if 'logo_temp_path' in wizard_data:
                            try:
                                from django.core.files.storage import default_storage
                                default_storage.delete(wizard_data['logo_temp_path'])
                            except:
                                pass
                        del request.session['signup_wizard_data']

                    messages.success(
                        request,
                        f'Welcome to RadioMention! Your organization "{organization.name}" has been created successfully.'
                    )
                    return redirect('organizations:signup_complete', slug=organization.slug)

            except (IntegrityError, ValueError) as e:
                error_msg = str(e).lower()
                if 'username' in error_msg:
                    messages.error(request, 'Username already exists. Please choose a different username and try again.')
                elif 'email' in error_msg:
                    messages.error(request, 'Email address already exists. Please use a different email address.')
                elif 'missing required wizard data' in error_msg:
                    messages.error(request, 'Session data was lost. Please complete the signup process again.')
                    # Clear corrupted session data
                    if 'signup_wizard_data' in request.session:
                        del request.session['signup_wizard_data']
                    return wizard_redirect(1)
                else:
                    messages.error(request, 'A user with this information already exists. Please check your details and try again.')
                return wizard_redirect(1)  # Go back to step 1 to fix user details

            except Exception as e:
                messages.error(request, f'Error creating organization: {str(e)}')
                # Log the full error for debugging
                import traceback
                print(f"Organization creation error: {traceback.format_exc()}")
                return wizard_redirect(5)
    else:
        # Pre-populate form with existing data
        initial_data = wizard_data.get('step5', {})
        form = SignupWizardStep5Form(initial=initial_data)

    # Prepare summary data for review
    summary_data = prepare_wizard_summary(wizard_data)

    context = {
        'form': form,
        'step': 5,
        'step_title': 'Review & Complete',
        'step_description': 'Review your information and complete the setup',
        'progress_percentage': 100,
        'summary_data': summary_data,
    }
    return render(request, 'organizations/signup_wizard.html', context)


def create_organization_from_wizard_data(wizard_data):
    """Create organization and user from wizard data using Django-Allauth"""
    # Validate that all required data is present
    required_steps = ['step1', 'step2', 'step3', 'step4', 'step5']
    for step in required_steps:
        if step not in wizard_data:
            raise ValueError(f"Missing {step} data in wizard_data")

    step1_data = wizard_data['step1']
    step2_data = wizard_data['step2']
    step3_data = wizard_data['step3']
    step4_data = wizard_data['step4']
    step5_data = wizard_data['step5']

    # Double-check for existing user before creation (race condition protection)
    username = step1_data['username'].lower().strip()
    email = step1_data['email'].lower().strip()

    if User.objects.filter(username__iexact=username).exists():
        raise ValueError(f"Username '{username}' already exists")

    if User.objects.filter(email__iexact=email).exists():
        raise ValueError(f"Email '{email}' already exists")

    # Create user
    user = User.objects.create_user(
        username=username,
        email=email,
        password=step1_data['password1'],
        first_name=step1_data['first_name'],
        last_name=step1_data['last_name']
    )

    # Create EmailAddress for Allauth (mark as verified since this is signup)
    email_address = EmailAddress.objects.create(
        user=user,
        email=step1_data['email'],
        verified=True,
        primary=True
    )

    # Create user profile
    profile, created = UserProfile.objects.get_or_create(user=user)
    profile.phone = step1_data.get('phone', '')
    profile.job_title = step1_data.get('job_title', '')
    profile.email_notifications = step4_data.get('enable_email_notifications', True)
    profile.conflict_alerts = step4_data.get('enable_conflict_alerts', True)
    profile.mention_reminders = step4_data.get('enable_deadline_reminders', True)
    profile.save()

    # Create organization (slug will be auto-generated by pre_save signal)
    organization = Organization.objects.create(
        name=step2_data['organization_name'],
        description=step2_data.get('description', ''),
        call_sign=step2_data.get('call_sign', ''),
        frequency=step2_data.get('frequency', ''),
        station_type=step2_data.get('station_type', ''),

        # Contact information
        website=step3_data.get('website', ''),
        email=step3_data['organization_email'],
        phone=step3_data['organization_phone'],
        address=step3_data['address'],
        city=step3_data['city'],
        state=step3_data['state'],
        zip_code=step3_data['zip_code'],
        country=step3_data['country'],
        timezone=step3_data['timezone'],

        # Station features
        has_multiple_shows=step4_data.get('has_multiple_shows', True),
        has_live_programming=step4_data.get('has_live_programming', True),
        has_automated_programming=step4_data.get('has_automated_programming', False),

        # Notification settings
        enable_email_notifications=step4_data.get('enable_email_notifications', True),
        enable_conflict_alerts=step4_data.get('enable_conflict_alerts', True),
        enable_deadline_reminders=step4_data.get('enable_deadline_reminders', True),

        # Plan settings
        plan_type=step4_data.get('plan_type', 'free'),
        max_users=step4_data.get('estimated_users', 5),
        max_mentions_per_month=step4_data.get('estimated_mentions_per_month', 100),
    )

    # Handle logo upload
    if 'logo_temp_path' in wizard_data:
        from django.core.files.storage import default_storage
        from django.core.files import File
        import os

        temp_path = wizard_data['logo_temp_path']
        original_name = wizard_data.get('logo_original_name', 'logo.png')

        # Open the temporary file and save it to the organization
        if default_storage.exists(temp_path):
            with default_storage.open(temp_path, 'rb') as temp_file:
                # Create a proper file name for the organization logo
                file_extension = os.path.splitext(original_name)[1]
                final_filename = f"org_{organization.id}_logo{file_extension}"

                # Save the file to the organization's logo field
                organization.logo.save(final_filename, File(temp_file), save=True)

            # Clean up the temporary file
            try:
                default_storage.delete(temp_path)
            except:
                pass  # Ignore errors when cleaning up temp files

    # Create owner membership with superuser privileges
    membership = OrganizationMembership.objects.create(
        user=user,
        organization=organization,
        role='owner',
        is_default=True,
        invitation_accepted_at=timezone.now()
    )

    # Make the first user (organization creator) a superuser with full privileges
    user.is_staff = True  # Allow access to admin interface for this organization
    user.save()

    print(f"Created organization owner: {user.username} for {organization.name} with superuser privileges")

    # Create main branch if requested
    if step5_data.get('create_main_branch', True):
        branch_name = step5_data.get('main_branch_name', 'Main Studio')

        Branch.objects.create(
            organization=organization,
            name=branch_name,
            description=f'Main branch for {organization.name}',
            address=organization.address,
            phone=organization.phone,
            email=organization.email,
            manager=user
        )

    # Create sample data if requested (default: False)
    if step5_data.get('create_sample_data', False):
        create_sample_data_for_organization(organization, user)

    return user, organization


def prepare_wizard_summary(wizard_data):
    """Prepare summary data for the final step"""
    summary = {}

    if 'step1' in wizard_data:
        summary['user'] = {
            'name': f"{wizard_data['step1']['first_name']} {wizard_data['step1']['last_name']}",
            'username': wizard_data['step1']['username'],
            'email': wizard_data['step1']['email'],
            'job_title': wizard_data['step1'].get('job_title', 'Not specified'),
        }

    if 'step2' in wizard_data:
        summary['organization'] = {
            'name': wizard_data['step2']['organization_name'],
            'call_sign': wizard_data['step2'].get('call_sign', 'Not specified'),
            'frequency': wizard_data['step2'].get('frequency', 'Not specified'),
            'station_type': wizard_data['step2'].get('station_type', 'Not specified'),
            'description': wizard_data['step2'].get('description', 'No description provided'),
        }

    if 'step3' in wizard_data:
        summary['contact'] = {
            'email': wizard_data['step3']['organization_email'],
            'phone': wizard_data['step3']['organization_phone'],
            'address': wizard_data['step3']['address'],
            'city': wizard_data['step3']['city'],
            'state': wizard_data['step3']['state'],
            'country': wizard_data['step3']['country'],
            'timezone': wizard_data['step3']['timezone'],
        }

    if 'step4' in wizard_data:
        plan_choices = {
            'free': 'Free Plan - Up to 5 users, 100 mentions/month',
            'basic': 'Basic Plan - Up to 15 users, 500 mentions/month',
            'premium': 'Premium Plan - Up to 50 users, 2000 mentions/month',
            'enterprise': 'Enterprise Plan - Unlimited users and mentions',
        }
        summary['configuration'] = {
            'plan_type': plan_choices.get(wizard_data['step4'].get('plan_type', 'free')),
            'estimated_users': wizard_data['step4'].get('estimated_users', 5),
            'estimated_mentions': wizard_data['step4'].get('estimated_mentions_per_month', 100),
            'features': {
                'multiple_shows': wizard_data['step4'].get('has_multiple_shows', True),
                'live_programming': wizard_data['step4'].get('has_live_programming', True),
                'automated_programming': wizard_data['step4'].get('has_automated_programming', False),
            }
        }

    return summary


def create_sample_data_for_organization(organization, user):
    """Create sample data to help users get started"""
    from apps.core.models import Client, Presenter
    from apps.shows.models import Show, ShowPresenter
    from apps.mentions.models import Mention

    # Create sample clients
    sample_clients = [
        {
            'name': 'Local Coffee Shop',
            'contact_person': 'John Smith',
            'email': '<EMAIL>',
            'phone': '+****************',
            'industry': 'Food & Beverage',
        },
        {
            'name': 'City Auto Dealership',
            'contact_person': 'Sarah Johnson',
            'email': '<EMAIL>',
            'phone': '+****************',
            'industry': 'Automotive',
        },
    ]

    for client_data in sample_clients:
        Client.objects.create(
            organization=organization,
            **client_data
        )

    # Create sample presenter
    Presenter.objects.create(
        organization=organization,
        first_name='Demo',
        last_name='Presenter',
        stage_name='DJ Demo',
        email='<EMAIL>',
        bio='Sample presenter for demonstration purposes',
        experience_years=5,
    )

    # Create sample show
    Show.objects.create(
        organization=organization,
        name='Morning Drive',
        description='Popular morning show',
        start_time='06:00',
        end_time='10:00',
        days_of_week=[0, 1, 2, 3, 4],  # Monday to Friday
    )


def signup_complete(request, slug):
    """Signup completion page"""
    organization = get_object_or_404(Organization, slug=slug)

    # Verify user has access to this organization
    if not request.user.is_authenticated:
        return redirect('account_login')

    membership = OrganizationMembership.objects.filter(
        user=request.user,
        organization=organization,
        is_active=True
    ).first()

    if not membership:
        messages.error(request, 'You do not have access to this organization.')
        return redirect('organizations:list')

    context = {
        'organization': organization,
        'membership': membership,
    }
    return render(request, 'organizations/signup_complete.html', context)


@login_required
def organization_list(request):
    """List user's organizations"""
    memberships = OrganizationMembership.objects.filter(
        user=request.user,
        is_active=True
    ).select_related('organization')

    context = {
        'memberships': memberships,
    }
    return render(request, 'organizations/list.html', context)


@login_required
def organization_detail(request, slug):
    """Organization detail view"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user is member
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    # Get organization statistics
    total_members = organization.organizationmembership_set.filter(is_active=True).count()
    total_branches = organization.branches.filter(is_active=True).count()

    context = {
        'organization': organization,
        'membership': membership,
        'total_members': total_members,
        'total_branches': total_branches,
    }
    return render(request, 'organizations/detail.html', context)


@login_required
def organization_create(request):
    """Create new organization"""
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            description = request.POST.get('description', '')
            website = request.POST.get('website', '')
            email = request.POST.get('email', '')
            phone = request.POST.get('phone', '')
            address = request.POST.get('address', '')

            # Create organization (slug will be auto-generated by pre_save signal)
            organization = Organization.objects.create(
                name=name,
                description=description,
                website=website,
                email=email,
                phone=phone,
                address=address
            )

            # Create owner membership for creator with superuser privileges
            membership = OrganizationMembership.objects.create(
                user=request.user,
                organization=organization,
                role='owner',
                is_default=True,
                invitation_accepted_at=timezone.now()
            )

            # Make the organization creator a superuser with full privileges
            if not request.user.is_staff:  # Only set if not already staff
                request.user.is_staff = True
                request.user.save()

            print(f"Created organization owner: {request.user.username} for {organization.name} with superuser privileges")

            messages.success(request, f'Organization "{name}" created successfully!')
            return redirect('organizations:detail', slug=organization.slug)

        except Exception as e:
            messages.error(request, f'Error creating organization: {str(e)}')

    return render(request, 'organizations/create.html')


@login_required
def organization_edit(request, slug):
    """Edit organization"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user has permission to edit
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    if not membership.has_permission('manage_settings'):
        messages.error(request, 'You do not have permission to edit this organization.')
        return redirect('organizations:detail', slug=slug)

    if request.method == 'POST':
        form = OrganizationForm(request.POST, request.FILES, instance=organization)
        if form.is_valid():
            try:
                form.save()
                messages.success(request, 'Organization updated successfully!')
                return redirect('organizations:detail', slug=organization.slug)
            except Exception as e:
                messages.error(request, f'Error updating organization: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = OrganizationForm(instance=organization)

    context = {
        'organization': organization,
        'membership': membership,
        'form': form,
    }
    return render(request, 'organizations/edit.html', context)


@login_required
def organization_delete(request, slug):
    """Delete organization"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user is owner
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        role='owner',
        is_active=True
    )

    if request.method == 'POST':
        organization_name = organization.name
        organization.delete()
        messages.success(request, f'Organization "{organization_name}" deleted successfully!')
        return redirect('organizations:list')

    context = {
        'organization': organization,
        'membership': membership,
    }
    return render(request, 'organizations/delete.html', context)


@login_required
def switch_organization(request, slug):
    """Switch current organization"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user is member
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    # Update session
    request.session['current_organization_id'] = organization.id

    messages.success(request, f'Switched to {organization.name}')
    return redirect('core:dashboard')


@login_required
def member_list(request, slug):
    """List organization members"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user has permission
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    if not membership.has_permission('manage_users'):
        messages.error(request, 'You do not have permission to view members.')
        return redirect('organizations:detail', slug=slug)

    members = OrganizationMembership.objects.filter(
        organization=organization,
        is_active=True
    ).select_related('user', 'branch')

    context = {
        'organization': organization,
        'membership': membership,
        'members': members,
    }
    return render(request, 'organizations/members.html', context)


@login_required
def invite_member(request, slug):
    """Invite new member to organization"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user has permission
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    if not membership.has_permission('manage_users'):
        messages.error(request, 'You do not have permission to invite members.')
        return redirect('organizations:detail', slug=slug)

    if request.method == 'POST':
        try:
            email = request.POST.get('email')
            role = request.POST.get('role', 'viewer')
            branch_id = request.POST.get('branch')

            # Check if user already exists and is member
            existing_user = User.objects.filter(email=email).first()
            if existing_user:
                existing_membership = OrganizationMembership.objects.filter(
                    user=existing_user,
                    organization=organization
                ).first()
                if existing_membership:
                    messages.error(request, 'User is already a member of this organization.')
                    return redirect('organizations:member_list', slug=slug)

            # Check if invitation already exists
            existing_invitation = OrganizationInvitation.objects.filter(
                organization=organization,
                email=email,
                is_accepted=False
            ).first()

            if existing_invitation and not existing_invitation.is_expired:
                messages.error(request, 'An invitation has already been sent to this email.')
                return redirect('organizations:member_list', slug=slug)

            # Create invitation
            branch = None
            if branch_id:
                branch = organization.branches.filter(id=branch_id).first()

            invitation = OrganizationInvitation.objects.create(
                organization=organization,
                branch=branch,
                email=email,
                role=role,
                invited_by=request.user,
                token=str(uuid.uuid4()),
                expires_at=timezone.now() + timedelta(days=7)
            )

            # TODO: Send invitation email

            messages.success(request, f'Invitation sent to {email}')
            return redirect('organizations:member_list', slug=slug)

        except Exception as e:
            messages.error(request, f'Error sending invitation: {str(e)}')

    branches = organization.branches.filter(is_active=True)
    context = {
        'organization': organization,
        'membership': membership,
        'branches': branches,
    }
    return render(request, 'organizations/invite.html', context)


@login_required
def edit_member(request, slug, user_id):
    """Edit member role"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user has permission
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    if not membership.has_permission('manage_users'):
        return JsonResponse({'success': False, 'error': 'Permission denied'})

    target_membership = get_object_or_404(
        OrganizationMembership,
        user_id=user_id,
        organization=organization
    )

    if request.method == 'POST':
        try:
            new_role = request.POST.get('role')
            branch_id = request.POST.get('branch')

            # Prevent changing owner role unless user is owner
            if target_membership.role == 'owner' and membership.role != 'owner':
                return JsonResponse({'success': False, 'error': 'Cannot change owner role'})

            target_membership.role = new_role
            if branch_id:
                target_membership.branch_id = branch_id
            target_membership.save()

            return JsonResponse({'success': True})

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request'})


@login_required
def remove_member(request, slug, user_id):
    """Remove member from organization"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user has permission
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    if not membership.has_permission('manage_users'):
        return JsonResponse({'success': False, 'error': 'Permission denied'})

    target_membership = get_object_or_404(
        OrganizationMembership,
        user_id=user_id,
        organization=organization
    )

    # Prevent removing owner
    if target_membership.role == 'owner':
        return JsonResponse({'success': False, 'error': 'Cannot remove owner'})

    # Prevent removing self
    if target_membership.user == request.user:
        return JsonResponse({'success': False, 'error': 'Cannot remove yourself'})

    if request.method == 'POST':
        try:
            target_membership.is_active = False
            target_membership.save()
            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request'})


# Branch views
@login_required
def branch_list(request, slug):
    """List organization branches"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user is member
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    branches = organization.branches.filter(is_active=True)

    context = {
        'organization': organization,
        'membership': membership,
        'branches': branches,
    }
    return render(request, 'organizations/branches.html', context)


@login_required
def branch_create(request, slug):
    """Create new branch"""
    organization = get_object_or_404(Organization, slug=slug)

    # Check if user has permission
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    if not membership.has_permission('manage_settings'):
        messages.error(request, 'You do not have permission to create branches.')
        return redirect('organizations:branch_list', slug=slug)

    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            description = request.POST.get('description', '')
            email = request.POST.get('email', '')
            phone = request.POST.get('phone', '')
            address = request.POST.get('address', '')
            manager_id = request.POST.get('manager')

            # Generate unique slug within organization
            slug_base = slugify(name)
            branch_slug = slug_base
            counter = 1
            while organization.branches.filter(slug=branch_slug).exists():
                branch_slug = f"{slug_base}-{counter}"
                counter += 1

            # Get manager if specified
            manager = None
            if manager_id:
                manager = User.objects.filter(id=manager_id).first()

            branch = Branch.objects.create(
                organization=organization,
                name=name,
                slug=branch_slug,
                description=description,
                email=email,
                phone=phone,
                address=address,
                manager=manager
            )

            messages.success(request, f'Branch "{name}" created successfully!')
            return redirect('organizations:branch_detail',
                          org_slug=organization.slug,
                          branch_slug=branch.slug)

        except Exception as e:
            messages.error(request, f'Error creating branch: {str(e)}')

    # Get organization members for manager selection
    members = User.objects.filter(
        organizationmembership__organization=organization,
        organizationmembership__is_active=True
    )

    context = {
        'organization': organization,
        'membership': membership,
        'members': members,
    }
    return render(request, 'organizations/branch_create.html', context)


@login_required
def branch_detail(request, org_slug, branch_slug):
    """Branch detail view"""
    organization = get_object_or_404(Organization, slug=org_slug)
    branch = get_object_or_404(Branch, organization=organization, slug=branch_slug)

    # Check if user is member
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    context = {
        'organization': organization,
        'branch': branch,
        'membership': membership,
    }
    return render(request, 'organizations/branch_detail.html', context)


@login_required
def branch_edit(request, org_slug, branch_slug):
    """Edit branch"""
    organization = get_object_or_404(Organization, slug=org_slug)
    branch = get_object_or_404(Branch, organization=organization, slug=branch_slug)

    # Check if user has permission
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    if not membership.has_permission('manage_settings'):
        messages.error(request, 'You do not have permission to edit branches.')
        return redirect('organizations:branch_detail',
                      org_slug=org_slug,
                      branch_slug=branch_slug)

    if request.method == 'POST':
        try:
            branch.name = request.POST.get('name')
            branch.description = request.POST.get('description', '')
            branch.email = request.POST.get('email', '')
            branch.phone = request.POST.get('phone', '')
            branch.address = request.POST.get('address', '')

            manager_id = request.POST.get('manager')
            if manager_id:
                branch.manager = User.objects.filter(id=manager_id).first()
            else:
                branch.manager = None

            branch.save()

            messages.success(request, 'Branch updated successfully!')
            return redirect('organizations:branch_detail',
                          org_slug=org_slug,
                          branch_slug=branch.slug)

        except Exception as e:
            messages.error(request, f'Error updating branch: {str(e)}')

    # Get organization members for manager selection
    members = User.objects.filter(
        organizationmembership__organization=organization,
        organizationmembership__is_active=True
    )

    context = {
        'organization': organization,
        'branch': branch,
        'membership': membership,
        'members': members,
    }
    return render(request, 'organizations/branch_edit.html', context)


@login_required
def branch_delete(request, org_slug, branch_slug):
    """Delete branch"""
    organization = get_object_or_404(Organization, slug=org_slug)
    branch = get_object_or_404(Branch, organization=organization, slug=branch_slug)

    # Check if user has permission
    membership = get_object_or_404(
        OrganizationMembership,
        user=request.user,
        organization=organization,
        is_active=True
    )

    if not membership.has_permission('manage_settings'):
        messages.error(request, 'You do not have permission to delete branches.')
        return redirect('organizations:branch_detail',
                      org_slug=org_slug,
                      branch_slug=branch_slug)

    if request.method == 'POST':
        branch_name = branch.name
        branch.delete()
        messages.success(request, f'Branch "{branch_name}" deleted successfully!')
        return redirect('organizations:branch_list', slug=organization.slug)

    context = {
        'organization': organization,
        'branch': branch,
        'membership': membership,
    }
    return render(request, 'organizations/branch_delete.html', context)


# Invitation views
def accept_invitation(request, token):
    """Accept organization invitation"""
    invitation = get_object_or_404(OrganizationInvitation, token=token)

    if not invitation.is_valid:
        messages.error(request, 'This invitation is no longer valid.')
        return redirect('account_login')

    if request.user.is_authenticated:
        # User is logged in, create membership
        try:
            membership, created = OrganizationMembership.objects.get_or_create(
                user=request.user,
                organization=invitation.organization,
                defaults={
                    'branch': invitation.branch,
                    'role': invitation.role,
                    'invited_by': invitation.invited_by,
                    'invitation_accepted_at': timezone.now()
                }
            )

            if not created:
                # Update existing membership
                membership.role = invitation.role
                membership.branch = invitation.branch
                membership.is_active = True
                membership.invitation_accepted_at = timezone.now()
                membership.save()

            # Mark invitation as accepted
            invitation.is_accepted = True
            invitation.accepted_at = timezone.now()
            invitation.save()

            messages.success(request, f'Welcome to {invitation.organization.name}!')
            return redirect('organizations:detail', slug=invitation.organization.slug)

        except Exception as e:
            messages.error(request, f'Error accepting invitation: {str(e)}')
            return redirect('core:dashboard')
    else:
        # User not logged in, redirect to login with next parameter
        return redirect(f'/accounts/login/?next=/organizations/invitations/{token}/')


def decline_invitation(request, token):
    """Decline organization invitation"""
    invitation = get_object_or_404(OrganizationInvitation, token=token)

    if not invitation.is_valid:
        messages.error(request, 'This invitation is no longer valid.')
        return redirect('account_login')

    if request.method == 'POST':
        invitation.delete()
        messages.info(request, 'Invitation declined.')

    return redirect('account_login')
