from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from apps.organizations.models import Organization, OrganizationMembership


class Command(BaseCommand):
    help = 'Set up default organization for existing data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            default='Default Radio Station',
            help='Name of the default organization'
        )
        parser.add_argument(
            '--slug',
            type=str,
            default='default-radio-station',
            help='Slug for the default organization'
        )

    def handle(self, *args, **options):
        org_name = options['name']
        org_slug = options['slug']
        
        # Create default organization if it doesn't exist
        organization, created = Organization.objects.get_or_create(
            slug=org_slug,
            defaults={
                'name': org_name,
                'description': 'Default organization for existing data',
                'plan_type': 'enterprise',
                'max_users': 1000,
                'max_mentions_per_month': 10000,
            }
        )
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'Created default organization: {org_name}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Organization already exists: {org_name}')
            )
        
        # Add all existing users to the organization
        users = User.objects.all()
        memberships_created = 0
        
        for user in users:
            membership, created = OrganizationMembership.objects.get_or_create(
                user=user,
                organization=organization,
                defaults={
                    'role': 'owner' if user.is_superuser else 'admin',
                    'is_default': True,
                    'invitation_accepted_at': timezone.now()
                }
            )
            
            if created:
                memberships_created += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Created {memberships_created} organization memberships'
            )
        )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Default organization setup complete. Organization ID: {organization.id}'
            )
        )
