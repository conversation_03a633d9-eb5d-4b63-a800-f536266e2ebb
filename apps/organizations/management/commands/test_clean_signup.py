from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from apps.organizations.models import Organization, OrganizationMembership
from apps.organizations.views import create_organization_from_wizard_data
from apps.core.models import Client, Presenter
from apps.shows.models import Show
from apps.mentions.models import Mention


class Command(BaseCommand):
    help = 'Test that clean signup creates no sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test data after running',
        )

    def handle(self, *args, **options):
        self.stdout.write('Testing clean signup process...')
        
        # Test data for wizard
        test_wizard_data = {
            'step1': {
                'first_name': 'Test',
                'last_name': 'User',
                'username': 'testuser_clean',
                'email': '<EMAIL>',
                'password1': 'testpass123!',
                'password2': 'testpass123!',
                'job_title': 'Manager',
            },
            'step2': {
                'organization_name': 'Test Clean Radio',
                'call_sign': 'TCLR',
                'frequency': '101.5 FM',
                'station_type': 'commercial',
                'description': 'Test radio station for clean signup',
            },
            'step3': {
                'organization_email': '<EMAIL>',
                'organization_phone': '+****************',
                'address': '123 Test Street',
                'city': 'Test City',
                'state': 'Test State',
                'zip_code': '12345',
                'country': 'US',
                'timezone': 'America/New_York',
            },
            'step4': {
                'plan_type': 'free',
                'estimated_users': 5,
                'estimated_mentions_per_month': 100,
                'has_multiple_shows': True,
                'has_live_programming': True,
                'has_automated_programming': False,
            },
            'step5': {
                'create_sample_data': False,  # This should be False by default now
                'create_main_branch': True,
                'enable_notifications': True,
                'enable_deadline_reminders': True,
            }
        }
        
        try:
            with transaction.atomic():
                # Create organization using wizard data
                user, organization = create_organization_from_wizard_data(test_wizard_data)
                
                self.stdout.write(f'Created organization: {organization.name}')
                self.stdout.write(f'Created user: {user.username}')
                
                # Check for sample data
                clients_count = Client.objects.filter(organization=organization).count()
                presenters_count = Presenter.objects.filter(organization=organization).count()
                shows_count = Show.objects.filter(organization=organization).count()
                mentions_count = Mention.objects.filter(client__organization=organization).count()
                
                self.stdout.write('\n--- Sample Data Check ---')
                self.stdout.write(f'Clients: {clients_count}')
                self.stdout.write(f'Presenters: {presenters_count}')
                self.stdout.write(f'Shows: {shows_count}')
                self.stdout.write(f'Mentions: {mentions_count}')
                
                # Verify no sample data was created
                if clients_count == 0 and presenters_count == 0 and shows_count == 0 and mentions_count == 0:
                    self.stdout.write(
                        self.style.SUCCESS('✅ SUCCESS: No sample data was created!')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR('❌ FAILED: Sample data was created unexpectedly!')
                    )
                    self.stdout.write('Expected: 0 clients, 0 presenters, 0 shows, 0 mentions')
                    self.stdout.write(f'Actual: {clients_count} clients, {presenters_count} presenters, {shows_count} shows, {mentions_count} mentions')
                
                # Check organization membership
                membership = OrganizationMembership.objects.filter(
                    user=user,
                    organization=organization
                ).first()
                
                if membership:
                    self.stdout.write(f'✅ Organization membership created: {membership.role}')
                else:
                    self.stdout.write('❌ Organization membership not created')
                
                # Test with sample data enabled
                self.stdout.write('\n--- Testing with sample data enabled ---')
                test_wizard_data['step1']['username'] = 'testuser_sample'
                test_wizard_data['step1']['email'] = '<EMAIL>'
                test_wizard_data['step2']['organization_name'] = 'Test Sample Radio'
                test_wizard_data['step5']['create_sample_data'] = True
                
                user2, organization2 = create_organization_from_wizard_data(test_wizard_data)
                
                # Check for sample data
                clients_count2 = Client.objects.filter(organization=organization2).count()
                presenters_count2 = Presenter.objects.filter(organization=organization2).count()
                shows_count2 = Show.objects.filter(organization=organization2).count()
                mentions_count2 = Mention.objects.filter(client__organization=organization2).count()
                
                self.stdout.write(f'Clients: {clients_count2}')
                self.stdout.write(f'Presenters: {presenters_count2}')
                self.stdout.write(f'Shows: {shows_count2}')
                self.stdout.write(f'Mentions: {mentions_count2}')
                
                if clients_count2 > 0 and presenters_count2 > 0 and shows_count2 > 0:
                    self.stdout.write(
                        self.style.SUCCESS('✅ SUCCESS: Sample data was created when enabled!')
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR('❌ FAILED: Sample data was not created when enabled!')
                    )
                
                # Cleanup if requested
                if options['cleanup']:
                    self.stdout.write('\n--- Cleaning up test data ---')
                    organization.delete()
                    organization2.delete()
                    user.delete()
                    user2.delete()
                    self.stdout.write('✅ Test data cleaned up')
                else:
                    self.stdout.write('\n--- Test data preserved ---')
                    self.stdout.write(f'Clean organization: {organization.slug}')
                    self.stdout.write(f'Sample organization: {organization2.slug}')
                    self.stdout.write('Use --cleanup flag to remove test data')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Test failed with error: {str(e)}')
            )
            raise
