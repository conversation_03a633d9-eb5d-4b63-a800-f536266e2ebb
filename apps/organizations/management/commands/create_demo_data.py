from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from apps.organizations.models import Organization, OrganizationMembership, Branch
from apps.core.models import Client, Presenter
from apps.shows.models import Show
from apps.mentions.models import Mention


class Command(BaseCommand):
    help = 'Create demo data for testing multi-organization features'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset existing demo data',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting demo data...')
            # Delete demo organizations (this will cascade)
            Organization.objects.filter(slug__startswith='demo-').delete()
            User.objects.filter(username__startswith='demo_').delete()

        # Create demo organizations
        org1 = Organization.objects.create(
            name='WXYZ Radio Station',
            slug='wxyz-radio',
            description='Premier radio station serving the metropolitan area',
            website='https://wxyzradio.com',
            email='<EMAIL>',
            phone='+****************',
            address='123 Radio Tower Blvd, Metro City, MC 12345',
            plan_type='premium',
            max_users=50,
            max_mentions_per_month=1000
        )

        org2 = Organization.objects.create(
            name='KQED Community Radio',
            slug='kqed-community',
            description='Community-focused radio station with local programming',
            website='https://kqedcommunity.org',
            email='<EMAIL>',
            phone='+****************',
            address='456 Community Center Dr, Hometown, HT 67890',
            plan_type='basic',
            max_users=20,
            max_mentions_per_month=500
        )

        self.stdout.write(f'Created organizations: {org1.name}, {org2.name}')

        # Create branches for WXYZ
        branch1 = Branch.objects.create(
            organization=org1,
            name='Downtown Studio',
            slug='downtown-studio',
            description='Main broadcasting studio in downtown',
            address='123 Radio Tower Blvd, Metro City, MC 12345',
            email='<EMAIL>',
            phone='+****************'
        )

        branch2 = Branch.objects.create(
            organization=org1,
            name='Satellite Office',
            slug='satellite-office',
            description='Remote broadcasting facility',
            address='789 Satellite Way, Metro City, MC 12346',
            email='<EMAIL>',
            phone='+****************'
        )

        self.stdout.write(f'Created branches for {org1.name}')

        # Create demo users
        users_data = [
            {
                'username': 'demo_admin1',
                'email': '<EMAIL>',
                'first_name': 'John',
                'last_name': 'Smith',
                'org': org1,
                'role': 'admin',
                'branch': branch1
            },
            {
                'username': 'demo_manager1',
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Johnson',
                'org': org1,
                'role': 'manager',
                'branch': branch1
            },
            {
                'username': 'demo_editor1',
                'email': '<EMAIL>',
                'first_name': 'Mike',
                'last_name': 'Davis',
                'org': org1,
                'role': 'editor',
                'branch': branch2
            },
            {
                'username': 'demo_admin2',
                'email': '<EMAIL>',
                'first_name': 'Lisa',
                'last_name': 'Wilson',
                'org': org2,
                'role': 'admin',
                'branch': None
            },
        ]

        for user_data in users_data:
            user = User.objects.create_user(
                username=user_data['username'],
                email=user_data['email'],
                password='demo123!',
                first_name=user_data['first_name'],
                last_name=user_data['last_name']
            )

            membership = OrganizationMembership.objects.create(
                user=user,
                organization=user_data['org'],
                branch=user_data['branch'],
                role=user_data['role'],
                is_default=True,
                invitation_accepted_at=timezone.now()
            )

            self.stdout.write(f'Created user: {user.username} ({user_data["role"]} at {user_data["org"].name})')

        # Create demo clients for each organization
        clients_org1 = [
            {'name': 'Metro Coffee Shop', 'contact': 'Jane Doe', 'email': '<EMAIL>'},
            {'name': 'City Auto Dealership', 'contact': 'Bob Wilson', 'email': '<EMAIL>'},
            {'name': 'Downtown Fitness', 'contact': 'Amy Chen', 'email': '<EMAIL>'},
        ]

        for client_data in clients_org1:
            Client.objects.create(
                organization=org1,
                branch=branch1,
                name=client_data['name'],
                contact_person=client_data['contact'],
                email=client_data['email'],
                phone='+****************',
                industry='Local Business'
            )

        clients_org2 = [
            {'name': 'Community Garden', 'contact': 'Green Thumb', 'email': '<EMAIL>'},
            {'name': 'Local Library', 'contact': 'Book Lover', 'email': '<EMAIL>'},
        ]

        for client_data in clients_org2:
            Client.objects.create(
                organization=org2,
                name=client_data['name'],
                contact_person=client_data['contact'],
                email=client_data['email'],
                phone='+****************',
                industry='Community Service'
            )

        self.stdout.write('Created demo clients for both organizations')

        # Create demo presenters
        presenters_org1 = [
            {'first_name': 'Alex', 'last_name': 'Rodriguez', 'stage_name': 'DJ Alex', 'email': '<EMAIL>'},
            {'first_name': 'Emma', 'last_name': 'Thompson', 'stage_name': 'Morning Emma', 'email': '<EMAIL>'},
        ]

        for presenter_data in presenters_org1:
            Presenter.objects.create(
                organization=org1,
                branch=branch1,
                first_name=presenter_data['first_name'],
                last_name=presenter_data['last_name'],
                stage_name=presenter_data['stage_name'],
                email=presenter_data['email'],
                phone='+****************',
                bio=f"Experienced radio presenter at {org1.name}",
                experience_years=5
            )

        presenters_org2 = [
            {'first_name': 'David', 'last_name': 'Brown', 'stage_name': 'Community Dave', 'email': '<EMAIL>'},
        ]

        for presenter_data in presenters_org2:
            Presenter.objects.create(
                organization=org2,
                first_name=presenter_data['first_name'],
                last_name=presenter_data['last_name'],
                stage_name=presenter_data['stage_name'],
                email=presenter_data['email'],
                phone='+****************',
                bio=f"Community radio host at {org2.name}",
                experience_years=3
            )

        self.stdout.write('Created demo presenters for both organizations')

        # Create demo shows
        Show.objects.create(
            organization=org1,
            branch=branch1,
            name='Morning Drive',
            description='Wake up with the best music and news',
            start_time='06:00:00',
            end_time='10:00:00',
            days_of_week=[0, 1, 2, 3, 4]  # Monday to Friday
        )

        Show.objects.create(
            organization=org2,
            name='Community Hour',
            description='Local news and community updates',
            start_time='18:00:00',
            end_time='19:00:00',
            days_of_week=[0, 1, 2, 3, 4, 5, 6]  # Every day
        )

        self.stdout.write('Created demo shows for both organizations')

        self.stdout.write(
            self.style.SUCCESS(
                '\nDemo data created successfully!\n\n'
                'Demo Users:\n'
                '- demo_admin1 / demo123! (WXYZ Radio - Admin)\n'
                '- demo_manager1 / demo123! (WXYZ Radio - Manager)\n'
                '- demo_editor1 / demo123! (WXYZ Radio - Editor)\n'
                '- demo_admin2 / demo123! (KQED Community - Admin)\n\n'
                'Organizations:\n'
                '- WXYZ Radio Station (Premium plan)\n'
                '- KQED Community Radio (Basic plan)\n'
            )
        )
