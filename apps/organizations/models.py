from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.exceptions import ValidationError

from apps.core.models import TimeStampedModel


class Organization(TimeStampedModel):
    """Model for radio station organizations"""
    name = models.CharField(max_length=200, unique=True)
    slug = models.SlugField(max_length=200, unique=True)
    description = models.TextField(blank=True)
    website = models.URLField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    logo = models.ImageField(upload_to='organizations/logos/', blank=True)

    # Radio station specific fields
    call_sign = models.Char<PERSON>ield(max_length=10, blank=True, help_text="Official radio station call sign")
    frequency = models.CharField(max_length=20, blank=True, help_text="Broadcasting frequency")
    station_type = models.CharField(
        max_length=20,
        choices=[
            ('commercial', 'Commercial Radio'),
            ('public', 'Public Radio'),
            ('community', 'Community Radio'),
            ('college', 'College/University Radio'),
            ('religious', 'Religious Radio'),
            ('talk', 'Talk Radio'),
            ('music', 'Music Radio'),
            ('news', 'News Radio'),
            ('sports', 'Sports Radio'),
            ('other', 'Other'),
        ],
        blank=True
    )

    # Location details
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=50, blank=True)
    zip_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=50, default='United States')

    # Organization settings
    timezone = models.CharField(max_length=50, default='US/Eastern')
    is_active = models.BooleanField(default=True)

    # Station features
    has_multiple_shows = models.BooleanField(default=True)
    has_live_programming = models.BooleanField(default=True)
    has_automated_programming = models.BooleanField(default=False)

    # Notification settings
    enable_email_notifications = models.BooleanField(default=True)
    enable_conflict_alerts = models.BooleanField(default=True)
    enable_deadline_reminders = models.BooleanField(default=True)

    # Subscription/plan info (for future use)
    plan_type = models.CharField(
        max_length=20,
        choices=[
            ('free', 'Free'),
            ('basic', 'Basic'),
            ('premium', 'Premium'),
            ('enterprise', 'Enterprise'),
        ],
        default='free'
    )
    max_users = models.PositiveIntegerField(default=5)
    max_mentions_per_month = models.PositiveIntegerField(default=100)

    class Meta:
        ordering = ['name']
        verbose_name = 'Organization'
        verbose_name_plural = 'Organizations'
        indexes = [
            models.Index(fields=['slug'], name='organization_slug_idx'),
            models.Index(fields=['is_active'], name='organization_is_active_idx'),
            models.Index(fields=['plan_type'], name='organization_plan_type_idx'),
            models.Index(fields=['created_at'], name='organization_created_at_idx'),
            models.Index(fields=['name'], name='organization_name_idx'),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('organizations:detail', kwargs={'slug': self.slug})

    @property
    def total_users(self):
        return self.organizationmembership_set.filter(is_active=True).count()

    @property
    def total_mentions_this_month(self):
        from datetime import datetime
        from apps.mentions.models import Mention
        current_month = datetime.now().replace(day=1)
        return Mention.objects.filter(
            client__organization=self,
            created_at__gte=current_month
        ).count()

    def can_add_user(self):
        return self.total_users < self.max_users

    def can_add_mention(self):
        return self.total_mentions_this_month < self.max_mentions_per_month


class Branch(TimeStampedModel):
    """Model for organization branches/departments"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, related_name='branches')
    name = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200)
    description = models.TextField(blank=True)
    address = models.TextField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    is_active = models.BooleanField(default=True)

    # Branch manager
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_branches'
    )

    class Meta:
        ordering = ['organization', 'name']
        unique_together = ['organization', 'slug']
        verbose_name = 'Branch'
        verbose_name_plural = 'Branches'

    def __str__(self):
        return f"{self.organization.name} - {self.name}"

    def get_absolute_url(self):
        return reverse('organizations:branch_detail', kwargs={
            'org_slug': self.organization.slug,
            'branch_slug': self.slug
        })


class OrganizationMembership(TimeStampedModel):
    """Through model for User-Organization relationship with roles"""
    ROLE_CHOICES = [
        ('owner', 'Owner'),
        ('admin', 'Administrator'),
        ('manager', 'Manager'),
        ('editor', 'Editor'),
        ('presenter', 'Presenter'),
        ('news_reader', 'News Reader'),
        ('viewer', 'Viewer'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, null=True, blank=True)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='viewer')
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False, help_text="Default organization for this user")

    # Invitation fields
    invited_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sent_invitations'
    )
    invitation_accepted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ['user', 'organization']
        ordering = ['organization', 'user']
        verbose_name = 'Organization Membership'
        verbose_name_plural = 'Organization Memberships'
        indexes = [
            models.Index(fields=['user'], name='membership_user_idx'),
            models.Index(fields=['organization'], name='membership_organization_idx'),
            models.Index(fields=['role'], name='membership_role_idx'),
            models.Index(fields=['is_active'], name='membership_is_active_idx'),
            models.Index(fields=['is_default'], name='membership_is_default_idx'),
            models.Index(fields=['user', 'is_active'], name='membership_user_active_idx'),
            models.Index(fields=['organization', 'is_active'], name='membership_org_active_idx'),
            models.Index(fields=['branch'], name='membership_branch_idx'),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.organization.name} ({self.role})"

    def clean(self):
        # Ensure only one default organization per user
        if self.is_default:
            existing_default = OrganizationMembership.objects.filter(
                user=self.user,
                is_default=True
            ).exclude(pk=self.pk)
            if existing_default.exists():
                raise ValidationError("User can only have one default organization")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    @property
    def role_display(self):
        return dict(self.ROLE_CHOICES)[self.role]

    def has_permission(self, permission):
        """Check if user has specific permission based on role and individual overrides"""
        # First check for individual permission overrides
        try:
            from apps.authentication.models import UserPermissionOverride
            override = UserPermissionOverride.objects.get(
                user=self.user,
                organization=self.organization,
                permission=permission
            )
            # If override exists, use its state
            return override.state == 'granted'
        except UserPermissionOverride.DoesNotExist:
            # No override found, use role-based permissions
            pass

        # Default role-based permissions
        permissions = {
            'owner': [
                'view', 'edit', 'delete', 'manage_users', 'manage_settings',
                'manage_organization', 'manage_billing', 'manage_integrations',
                'view_analytics', 'export_data', 'manage_api_keys',
                'manage_branches', 'manage_shows', 'manage_mentions',
                'approve_mentions', 'schedule_mentions', 'manage_presenters',
                'manage_clients', 'view_reports', 'manage_conflicts',
                'admin_access'  # Full admin access to organization
            ],
            'admin': [
                'view', 'edit', 'delete', 'manage_users', 'manage_settings',
                'manage_shows', 'manage_mentions', 'approve_mentions',
                'schedule_mentions', 'manage_presenters', 'manage_clients',
                'view_reports', 'manage_conflicts'
            ],
            'manager': [
                'view', 'edit', 'manage_mentions', 'approve_mentions',
                'schedule_mentions', 'manage_presenters', 'view_reports'
            ],
            'editor': ['view', 'edit', 'manage_mentions', 'schedule_mentions'],
            'presenter': [
                'mark_mentions_read', 'view_presenter_dashboard', 'view_schedule'
            ],
            'news_reader': [
                'view', 'view_news_reader_dashboard', 'manage_reading_notes',
                'update_reading_status', 'view_assigned_mentions', 'view_schedule',
                'access_live_reading_tools'
            ],
            'viewer': ['view'],
        }
        return permission in permissions.get(self.role, [])

    @property
    def is_organization_superuser(self):
        """Check if this membership grants superuser privileges for the organization"""
        return self.role == 'owner' and self.is_active

    @property
    def can_access_admin(self):
        """Check if user can access admin interface for this organization"""
        return self.role in ['owner', 'admin'] and self.is_active


class OrganizationInvitation(TimeStampedModel):
    """Model for organization invitations"""
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, null=True, blank=True)
    email = models.EmailField()
    role = models.CharField(max_length=20, choices=OrganizationMembership.ROLE_CHOICES, default='viewer')
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='organization_invitations_sent')
    token = models.CharField(max_length=100, unique=True)
    is_accepted = models.BooleanField(default=False)
    accepted_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField()

    class Meta:
        unique_together = ['organization', 'email']
        ordering = ['-created_at']
        verbose_name = 'Organization Invitation'
        verbose_name_plural = 'Organization Invitations'

    def __str__(self):
        return f"Invitation to {self.email} for {self.organization.name}"

    @property
    def is_expired(self):
        from django.utils import timezone
        return timezone.now() > self.expires_at

    @property
    def is_valid(self):
        return not self.is_accepted and not self.is_expired
