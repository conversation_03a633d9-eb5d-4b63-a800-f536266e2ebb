"""
Organization-level admin permissions and utilities.
Provides superuser-like permissions within organization context.
"""

from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.models import Permission
from .models import OrganizationMembership
from .middleware import get_current_organization


class OrganizationAdminBackend(BaseBackend):
    """
    Authentication backend that grants admin permissions based on organization membership.
    Organization owners get superuser-like permissions within their organization context.
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        # This backend doesn't handle authentication, only permissions
        return None
    
    def has_perm(self, user_obj, perm, obj=None):
        """
        Check if user has permission within organization context.
        Organization owners get full permissions for their organization's data.
        """
        if not user_obj.is_active:
            return False
        
        # Global superusers always have permission
        if user_obj.is_superuser:
            return True
        
        # Check organization-level permissions
        try:
            # Get current organization from request context
            # This would need to be passed through the request
            current_org = getattr(user_obj, '_current_organization', None)
            
            if current_org:
                membership = OrganizationMembership.objects.get(
                    user=user_obj,
                    organization=current_org,
                    is_active=True
                )
                
                # Organization owners get full admin permissions for their org
                if membership.is_organization_superuser:
                    # Check if permission is related to organization models
                    org_related_perms = [
                        'mentions.', 'core.', 'shows.', 'reports.',
                        'organizations.', 'activity_logs.'
                    ]
                    
                    for perm_prefix in org_related_perms:
                        if perm.startswith(perm_prefix):
                            return True
                
                # Check specific organization permissions
                perm_map = {
                    'mentions.add_mention': 'manage_mentions',
                    'mentions.change_mention': 'manage_mentions',
                    'mentions.delete_mention': 'delete',
                    'mentions.view_mention': 'view',
                    'mentions.approve_mention': 'approve_mentions',
                    
                    'core.add_client': 'manage_clients',
                    'core.change_client': 'manage_clients',
                    'core.delete_client': 'delete',
                    'core.view_client': 'view',
                    
                    'core.add_presenter': 'manage_presenters',
                    'core.change_presenter': 'manage_presenters',
                    'core.delete_presenter': 'delete',
                    'core.view_presenter': 'view',
                    
                    'shows.add_show': 'manage_shows',
                    'shows.change_show': 'manage_shows',
                    'shows.delete_show': 'delete',
                    'shows.view_show': 'view',
                    
                    'organizations.change_organization': 'manage_organization',
                    'organizations.view_organization': 'view',
                    
                    'reports.view_report': 'view_reports',
                    'activity_logs.view_activitylog': 'view',
                }
                
                required_permission = perm_map.get(perm)
                if required_permission:
                    return membership.has_permission(required_permission)
                    
        except OrganizationMembership.DoesNotExist:
            pass
        
        return False
    
    def has_module_perms(self, user_obj, app_label):
        """
        Check if user has permissions for an entire app module.
        Organization owners get access to organization-related modules.
        """
        if not user_obj.is_active:
            return False
        
        # Global superusers always have permission
        if user_obj.is_superuser:
            return True
        
        # Check organization-level module permissions
        try:
            current_org = getattr(user_obj, '_current_organization', None)
            
            if current_org:
                membership = OrganizationMembership.objects.get(
                    user=user_obj,
                    organization=current_org,
                    is_active=True
                )
                
                # Organization owners get access to organization-related modules
                if membership.is_organization_superuser:
                    org_modules = [
                        'mentions', 'core', 'shows', 'reports',
                        'organizations', 'activity_logs'
                    ]
                    return app_label in org_modules
                
                # Admins get access to most modules
                if membership.role == 'admin':
                    admin_modules = [
                        'mentions', 'core', 'shows', 'reports'
                    ]
                    return app_label in admin_modules
                    
        except OrganizationMembership.DoesNotExist:
            pass
        
        return False


def set_organization_context(user, organization):
    """
    Set organization context for permission checking.
    This should be called in middleware or views.
    """
    user._current_organization = organization


def get_user_organization_permissions(user, organization):
    """
    Get all permissions a user has within an organization.
    Returns a list of permission strings.
    """
    permissions = []
    
    try:
        membership = OrganizationMembership.objects.get(
            user=user,
            organization=organization,
            is_active=True
        )
        
        # Get role-based permissions
        role_permissions = {
            'owner': [
                'view', 'edit', 'delete', 'manage_users', 'manage_settings',
                'manage_organization', 'manage_billing', 'manage_integrations',
                'view_analytics', 'export_data', 'manage_api_keys',
                'manage_branches', 'manage_shows', 'manage_mentions',
                'approve_mentions', 'schedule_mentions', 'manage_presenters',
                'manage_clients', 'view_reports', 'manage_conflicts',
                'admin_access'
            ],
            'admin': [
                'view', 'edit', 'delete', 'manage_users', 'manage_settings',
                'manage_shows', 'manage_mentions', 'approve_mentions',
                'schedule_mentions', 'manage_presenters', 'manage_clients',
                'view_reports', 'manage_conflicts'
            ],
            'manager': [
                'view', 'edit', 'manage_mentions', 'approve_mentions',
                'schedule_mentions', 'manage_presenters', 'view_reports'
            ],
            'editor': ['view', 'edit', 'manage_mentions', 'schedule_mentions'],
            'news_reader': [
                'view', 'view_news_reader_dashboard', 'manage_reading_notes',
                'update_reading_status', 'view_assigned_mentions', 'view_schedule',
                'access_live_reading_tools'
            ],
            'viewer': ['view'],
        }
        
        permissions = role_permissions.get(membership.role, [])
        
    except OrganizationMembership.DoesNotExist:
        pass
    
    return permissions


def user_can_access_admin(user, organization):
    """
    Check if user can access admin interface for organization.
    """
    try:
        membership = OrganizationMembership.objects.get(
            user=user,
            organization=organization,
            is_active=True
        )
        return membership.can_access_admin
    except OrganizationMembership.DoesNotExist:
        return False


def promote_to_organization_superuser(user, organization):
    """
    Promote a user to organization superuser (owner role).
    This gives them full admin access within the organization.
    """
    try:
        membership = OrganizationMembership.objects.get(
            user=user,
            organization=organization,
            is_active=True
        )
        
        # Update role to owner
        membership.role = 'owner'
        membership.save()
        
        # Grant staff status for admin access
        if not user.is_staff:
            user.is_staff = True
            user.save()
        
        return True
        
    except OrganizationMembership.DoesNotExist:
        return False


def create_organization_superuser(user_data, organization_data):
    """
    Create a new organization with a superuser owner.
    Used during organization signup process.
    """
    from django.contrib.auth.models import User
    from .models import Organization, OrganizationMembership
    from django.utils import timezone
    
    # Create user
    user = User.objects.create_user(
        username=user_data['username'],
        email=user_data['email'],
        password=user_data['password'],
        first_name=user_data.get('first_name', ''),
        last_name=user_data.get('last_name', '')
    )
    
    # Grant staff status for admin access
    user.is_staff = True
    user.save()
    
    # Create organization
    organization = Organization.objects.create(**organization_data)
    
    # Create owner membership with superuser privileges
    membership = OrganizationMembership.objects.create(
        user=user,
        organization=organization,
        role='owner',
        is_default=True,
        invitation_accepted_at=timezone.now()
    )
    
    return user, organization, membership
