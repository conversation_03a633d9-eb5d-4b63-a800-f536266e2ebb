"""
Django signals for the organizations app.
Handles automatic data processing using pre_save signals.
"""

from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from django.utils.text import slugify
from django.utils import timezone
from datetime import timed<PERSON>ta
import uuid

from .models import Organization, Branch, OrganizationMembership, OrganizationInvitation


@receiver(pre_save, sender=Organization)
def organization_pre_save(sender, instance, **kwargs):
    """
    Handle Organization pre-save processing:
    - Auto-generate unique slug
    - Normalize data (call sign, email)
    - Set plan limits based on plan type
    """
    # Auto-generate slug if not provided
    if not instance.slug:
        base_slug = slugify(instance.name)
        slug = base_slug
        counter = 1
        
        # Ensure unique slug
        while Organization.objects.filter(slug=slug).exclude(pk=instance.pk).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1
        
        instance.slug = slug
    
    # Normalize data
    if instance.call_sign:
        instance.call_sign = instance.call_sign.upper().strip()
    
    if instance.email:
        instance.email = instance.email.lower().strip()
    
    # Set plan limits based on plan type
    plan_limits = {
        'free': {'max_users': 5, 'max_mentions_per_month': 100},
        'basic': {'max_users': 15, 'max_mentions_per_month': 500},
        'premium': {'max_users': 50, 'max_mentions_per_month': 2000},
        'enterprise': {'max_users': 999, 'max_mentions_per_month': 99999},
    }
    
    if instance.plan_type in plan_limits:
        limits = plan_limits[instance.plan_type]
        instance.max_users = limits['max_users']
        instance.max_mentions_per_month = limits['max_mentions_per_month']


@receiver(pre_save, sender=Branch)
def branch_pre_save(sender, instance, **kwargs):
    """
    Handle Branch pre-save processing:
    - Auto-generate unique slug within organization
    - Normalize email
    """
    # Auto-generate slug if not provided
    if not instance.slug:
        base_slug = slugify(instance.name)
        slug = base_slug
        counter = 1
        
        # Ensure unique slug within organization
        while Branch.objects.filter(
            organization=instance.organization,
            slug=slug
        ).exclude(pk=instance.pk).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1
        
        instance.slug = slug
    
    # Normalize email
    if instance.email:
        instance.email = instance.email.lower().strip()


@receiver(pre_save, sender=OrganizationInvitation)
def organization_invitation_pre_save(sender, instance, **kwargs):
    """
    Handle OrganizationInvitation pre-save processing:
    - Auto-generate unique token
    - Set expiration date (7 days from now)
    - Normalize email
    """
    # Auto-generate token if not provided
    if not instance.token:
        instance.token = str(uuid.uuid4())
    
    # Set expiration date if not provided (7 days from now)
    if not instance.expires_at:
        instance.expires_at = timezone.now() + timedelta(days=7)
    
    # Normalize email
    if instance.email:
        instance.email = instance.email.lower().strip()


@receiver(post_save, sender=Organization)
def organization_post_save(sender, instance, created, **kwargs):
    """
    Handle Organization post-save processing:
    - Log organization creation
    - Send welcome notifications (if needed)
    """
    if created:
        # Log organization creation
        print(f"New organization created: {instance.name} ({instance.slug})")
        
        # You could add additional post-creation logic here:
        # - Send welcome emails
        # - Create default settings
        # - Set up initial data
        # - Trigger analytics events


@receiver(post_save, sender=OrganizationMembership)
def organization_membership_post_save(sender, instance, created, **kwargs):
    """
    Handle OrganizationMembership post-save processing:
    - Ensure only one default organization per user
    - Log membership changes
    """
    if created:
        # Log membership creation
        print(f"New membership: {instance.user.username} joined {instance.organization.name} as {instance.role}")
        
        # If this is the user's first organization, make it default
        user_memberships = OrganizationMembership.objects.filter(
            user=instance.user,
            is_active=True
        )
        
        if user_memberships.count() == 1:
            instance.is_default = True
            instance.save(update_fields=['is_default'])
    
    # Ensure only one default organization per user
    if instance.is_default:
        OrganizationMembership.objects.filter(
            user=instance.user,
            is_default=True
        ).exclude(pk=instance.pk).update(is_default=False)


@receiver(pre_save, sender=OrganizationMembership)
def organization_membership_pre_save(sender, instance, **kwargs):
    """
    Handle OrganizationMembership pre-save processing:
    - Set invitation_accepted_at for new memberships
    """
    if not instance.pk and not instance.invitation_accepted_at:
        # This is a new membership, set acceptance time
        instance.invitation_accepted_at = timezone.now()


# Additional utility functions for signal processing

def normalize_phone_number(phone):
    """Normalize phone number format"""
    if not phone:
        return phone
    
    # Remove all non-digit characters except +
    import re
    normalized = re.sub(r'[^\d+]', '', phone)
    
    # Add + if it's missing and looks like an international number
    if normalized.startswith('1') and len(normalized) == 11:
        normalized = '+' + normalized
    elif not normalized.startswith('+') and len(normalized) >= 10:
        normalized = '+1' + normalized
    
    return normalized


def generate_unique_slug(model_class, base_slug, exclude_pk=None, organization=None):
    """
    Generate a unique slug for a model.
    
    Args:
        model_class: The model class to check against
        base_slug: The base slug to start with
        exclude_pk: Primary key to exclude from uniqueness check
        organization: Organization to scope the uniqueness check to
    
    Returns:
        str: A unique slug
    """
    slug = base_slug
    counter = 1
    
    while True:
        # Build the query
        query = model_class.objects.filter(slug=slug)
        
        if exclude_pk:
            query = query.exclude(pk=exclude_pk)
        
        if organization and hasattr(model_class, 'organization'):
            query = query.filter(organization=organization)
        
        if not query.exists():
            break
        
        slug = f"{base_slug}-{counter}"
        counter += 1
    
    return slug


def validate_plan_limits(organization):
    """
    Validate that organization is within plan limits.
    
    Args:
        organization: Organization instance
    
    Returns:
        dict: Validation results with warnings/errors
    """
    results = {
        'valid': True,
        'warnings': [],
        'errors': []
    }
    
    # Check user limit
    current_users = organization.total_users
    if current_users > organization.max_users:
        results['valid'] = False
        results['errors'].append(
            f"Organization has {current_users} users but plan allows only {organization.max_users}"
        )
    elif current_users >= organization.max_users * 0.8:  # 80% warning threshold
        results['warnings'].append(
            f"Organization is approaching user limit ({current_users}/{organization.max_users})"
        )
    
    # Check mention limit
    current_mentions = organization.total_mentions_this_month
    if current_mentions > organization.max_mentions_per_month:
        results['valid'] = False
        results['errors'].append(
            f"Organization has {current_mentions} mentions this month but plan allows only {organization.max_mentions_per_month}"
        )
    elif current_mentions >= organization.max_mentions_per_month * 0.8:  # 80% warning threshold
        results['warnings'].append(
            f"Organization is approaching mention limit ({current_mentions}/{organization.max_mentions_per_month})"
        )
    
    return results
