# Generated by Django 4.2.7 on 2025-06-11 07:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200, unique=True)),
                ("slug", models.SlugField(max_length=200, unique=True)),
                ("description", models.TextField(blank=True)),
                ("website", models.URLField(blank=True)),
                ("phone", models.CharField(blank=True, max_length=20)),
                ("email", models.EmailField(blank=True, max_length=254)),
                ("address", models.TextField(blank=True)),
                (
                    "logo",
                    models.ImageField(blank=True, upload_to="organizations/logos/"),
                ),
                (
                    "call_sign",
                    models.CharField(
                        blank=True,
                        help_text="Official radio station call sign",
                        max_length=10,
                    ),
                ),
                (
                    "frequency",
                    models.CharField(
                        blank=True, help_text="Broadcasting frequency", max_length=20
                    ),
                ),
                (
                    "station_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("commercial", "Commercial Radio"),
                            ("public", "Public Radio"),
                            ("community", "Community Radio"),
                            ("college", "College/University Radio"),
                            ("religious", "Religious Radio"),
                            ("talk", "Talk Radio"),
                            ("music", "Music Radio"),
                            ("news", "News Radio"),
                            ("sports", "Sports Radio"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("city", models.CharField(blank=True, max_length=100)),
                ("state", models.CharField(blank=True, max_length=50)),
                ("zip_code", models.CharField(blank=True, max_length=20)),
                ("country", models.CharField(default="United States", max_length=50)),
                ("timezone", models.CharField(default="US/Eastern", max_length=50)),
                ("is_active", models.BooleanField(default=True)),
                ("has_multiple_shows", models.BooleanField(default=True)),
                ("has_live_programming", models.BooleanField(default=True)),
                ("has_automated_programming", models.BooleanField(default=False)),
                ("enable_email_notifications", models.BooleanField(default=True)),
                ("enable_conflict_alerts", models.BooleanField(default=True)),
                ("enable_deadline_reminders", models.BooleanField(default=True)),
                (
                    "plan_type",
                    models.CharField(
                        choices=[
                            ("free", "Free"),
                            ("basic", "Basic"),
                            ("premium", "Premium"),
                            ("enterprise", "Enterprise"),
                        ],
                        default="free",
                        max_length=20,
                    ),
                ),
                ("max_users", models.PositiveIntegerField(default=5)),
                ("max_mentions_per_month", models.PositiveIntegerField(default=100)),
            ],
            options={
                "verbose_name": "Organization",
                "verbose_name_plural": "Organizations",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Branch",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                ("slug", models.SlugField(max_length=200)),
                ("description", models.TextField(blank=True)),
                ("address", models.TextField(blank=True)),
                ("phone", models.CharField(blank=True, max_length=20)),
                ("email", models.EmailField(blank=True, max_length=254)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "manager",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="managed_branches",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="branches",
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Branch",
                "verbose_name_plural": "Branches",
                "ordering": ["organization", "name"],
                "unique_together": {("organization", "slug")},
            },
        ),
        migrations.CreateModel(
            name="OrganizationMembership",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("owner", "Owner"),
                            ("admin", "Administrator"),
                            ("manager", "Manager"),
                            ("editor", "Editor"),
                            ("presenter", "Presenter"),
                            ("viewer", "Viewer"),
                        ],
                        default="viewer",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_default",
                    models.BooleanField(
                        default=False, help_text="Default organization for this user"
                    ),
                ),
                ("invitation_accepted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "branch",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organizations.branch",
                    ),
                ),
                (
                    "invited_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="sent_invitations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organizations.organization",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Organization Membership",
                "verbose_name_plural": "Organization Memberships",
                "ordering": ["organization", "user"],
                "unique_together": {("user", "organization")},
            },
        ),
        migrations.CreateModel(
            name="OrganizationInvitation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("email", models.EmailField(max_length=254)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("owner", "Owner"),
                            ("admin", "Administrator"),
                            ("manager", "Manager"),
                            ("editor", "Editor"),
                            ("presenter", "Presenter"),
                            ("viewer", "Viewer"),
                        ],
                        default="viewer",
                        max_length=20,
                    ),
                ),
                ("token", models.CharField(max_length=100, unique=True)),
                ("is_accepted", models.BooleanField(default=False)),
                ("accepted_at", models.DateTimeField(blank=True, null=True)),
                ("expires_at", models.DateTimeField()),
                (
                    "branch",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organizations.branch",
                    ),
                ),
                (
                    "invited_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="organization_invitations_sent",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Organization Invitation",
                "verbose_name_plural": "Organization Invitations",
                "ordering": ["-created_at"],
                "unique_together": {("organization", "email")},
            },
        ),
    ]
