# Generated by Django 4.2.7 on 2025-06-22 00:13

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("organizations", "0001_initial"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="organization",
            index=models.Index(fields=["slug"], name="organization_slug_idx"),
        ),
        migrations.AddIndex(
            model_name="organization",
            index=models.Index(fields=["is_active"], name="organization_is_active_idx"),
        ),
        migrations.AddIndex(
            model_name="organization",
            index=models.Index(fields=["plan_type"], name="organization_plan_type_idx"),
        ),
        migrations.AddIndex(
            model_name="organization",
            index=models.Index(
                fields=["created_at"], name="organization_created_at_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="organization",
            index=models.Index(fields=["name"], name="organization_name_idx"),
        ),
        migrations.AddIndex(
            model_name="organizationmembership",
            index=models.Index(fields=["user"], name="membership_user_idx"),
        ),
        migrations.AddIndex(
            model_name="organizationmembership",
            index=models.Index(
                fields=["organization"], name="membership_organization_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="organizationmembership",
            index=models.Index(fields=["role"], name="membership_role_idx"),
        ),
        migrations.AddIndex(
            model_name="organizationmembership",
            index=models.Index(fields=["is_active"], name="membership_is_active_idx"),
        ),
        migrations.AddIndex(
            model_name="organizationmembership",
            index=models.Index(fields=["is_default"], name="membership_is_default_idx"),
        ),
        migrations.AddIndex(
            model_name="organizationmembership",
            index=models.Index(
                fields=["user", "is_active"], name="membership_user_active_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="organizationmembership",
            index=models.Index(
                fields=["organization", "is_active"], name="membership_org_active_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="organizationmembership",
            index=models.Index(fields=["branch"], name="membership_branch_idx"),
        ),
    ]
