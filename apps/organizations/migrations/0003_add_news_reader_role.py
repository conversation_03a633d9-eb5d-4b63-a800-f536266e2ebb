# Generated by Django 4.2.7 on 2025-06-26 10:20

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("organizations", "0002_add_production_indexes"),
    ]

    operations = [
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name="organizationinvitation",
            name="role",
            field=models.Char<PERSON>ield(
                choices=[
                    ("owner", "Owner"),
                    ("admin", "Administrator"),
                    ("manager", "Manager"),
                    ("editor", "Editor"),
                    ("presenter", "Presenter"),
                    ("news_reader", "News Reader"),
                    ("viewer", "Viewer"),
                ],
                default="viewer",
                max_length=20,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="organizationmembership",
            name="role",
            field=models.Char<PERSON><PERSON>(
                choices=[
                    ("owner", "Owner"),
                    ("admin", "Administrator"),
                    ("manager", "Manager"),
                    ("editor", "Editor"),
                    ("presenter", "Presenter"),
                    ("news_reader", "News Reader"),
                    ("viewer", "Viewer"),
                ],
                default="viewer",
                max_length=20,
            ),
        ),
    ]
