from django.shortcuts import redirect
from django.urls import reverse
from django.contrib import messages
from .models import OrganizationMembership


class OrganizationMiddleware:
    """
    Middleware to ensure users have access to organization data
    and redirect to organization setup if needed.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # Skip middleware for certain paths
        skip_paths = [
            '/admin/',
            '/accounts/',
            '/organizations/',
            '/static/',
            '/media/',
        ]
        
        if any(request.path.startswith(path) for path in skip_paths):
            return self.get_response(request)
        
        # Only apply to authenticated users
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Check if user has any organization memberships
            memberships = OrganizationMembership.objects.filter(
                user=request.user,
                is_active=True
            ).select_related('organization')
            
            if not memberships.exists():
                # User has no organizations, redirect to create one
                if request.path != reverse('organizations:create'):
                    messages.info(
                        request, 
                        'Please create or join an organization to continue.'
                    )
                    return redirect('organizations:create')
            else:
                # Ensure current organization is set
                current_org_id = request.session.get('current_organization_id')
                if not current_org_id or not memberships.filter(organization_id=current_org_id).exists():
                    # Set default organization
                    default_membership = memberships.filter(is_default=True).first()
                    if not default_membership:
                        default_membership = memberships.first()
                    
                    request.session['current_organization_id'] = default_membership.organization.id

                # Set organization context for permission checking
                current_org_id = request.session.get('current_organization_id')
                if current_org_id:
                    try:
                        from .models import Organization
                        current_org = Organization.objects.get(id=current_org_id)
                        # Set organization context on user for permission backend
                        request.user._current_organization = current_org
                    except Organization.DoesNotExist:
                        pass
        
        response = self.get_response(request)
        return response


class OrganizationDataFilterMixin:
    """
    Mixin to filter querysets by current organization
    """
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Only filter if user is authenticated and has current organization
        if (hasattr(self.request, 'user') and 
            self.request.user.is_authenticated and 
            hasattr(self.request, 'session')):
            
            current_org_id = self.request.session.get('current_organization_id')
            if current_org_id and hasattr(queryset.model, 'organization'):
                queryset = queryset.filter(organization_id=current_org_id)
        
        return queryset
    
    def form_valid(self, form):
        """Automatically set organization on form save"""
        # Check if form has instance attribute (ModelForm) before accessing it
        if hasattr(form, 'instance') and hasattr(form.instance, 'organization') and not form.instance.organization_id:
            current_org_id = self.request.session.get('current_organization_id')
            if current_org_id:
                form.instance.organization_id = current_org_id

        return super().form_valid(form)

    def delete(self, request, *args, **kwargs):
        """Handle delete operations - DeleteView doesn't use form_valid"""
        # For DeleteView, we don't need to set organization since we're deleting
        # Just ensure the object belongs to the current organization (handled by get_queryset)
        return super().delete(request, *args, **kwargs)


def get_current_organization(request):
    """
    Helper function to get current organization from request
    """
    if not hasattr(request, 'user') or not request.user.is_authenticated:
        return None
    
    current_org_id = request.session.get('current_organization_id')
    if not current_org_id:
        return None
    
    try:
        from .models import Organization
        return Organization.objects.get(id=current_org_id)
    except Organization.DoesNotExist:
        return None


def get_current_membership(request):
    """
    Helper function to get current organization membership from request
    """
    if not hasattr(request, 'user') or not request.user.is_authenticated:
        return None
    
    current_org_id = request.session.get('current_organization_id')
    if not current_org_id:
        return None
    
    try:
        return OrganizationMembership.objects.get(
            user=request.user,
            organization_id=current_org_id,
            is_active=True
        )
    except OrganizationMembership.DoesNotExist:
        return None


def user_has_permission(request, permission):
    """
    Helper function to check if user has specific permission in current organization
    """
    membership = get_current_membership(request)
    if not membership:
        return False

    return membership.has_permission(permission)


def user_has_any_permission(request, permissions):
    """
    Helper function to check if user has any of the specified permissions
    """
    membership = get_current_membership(request)
    if not membership:
        return False

    return any(membership.has_permission(perm) for perm in permissions)


def get_user_permissions(request):
    """
    Get all permissions for the current user in current organization
    """
    membership = get_current_membership(request)
    if not membership:
        return []

    # Get role-based permissions
    role_permissions = {
        'owner': [
            'view', 'edit', 'delete', 'manage_users', 'manage_settings',
            'manage_organization', 'manage_billing', 'manage_integrations',
            'view_analytics', 'export_data', 'manage_api_keys',
            'manage_branches', 'manage_shows', 'manage_mentions',
            'approve_mentions', 'schedule_mentions', 'manage_presenters',
            'manage_clients', 'view_reports', 'manage_conflicts',
            'admin_access', 'view_user_details', 'invite_users', 'manage_user_roles'
        ],
        'admin': [
            'view', 'edit', 'delete', 'manage_users', 'manage_settings',
            'manage_shows', 'manage_mentions', 'approve_mentions',
            'schedule_mentions', 'manage_presenters', 'manage_clients',
            'view_reports', 'manage_conflicts', 'view_user_details',
            'invite_users', 'manage_user_roles', 'manage_api_keys'
        ],
        'manager': [
            'view', 'edit', 'manage_mentions', 'approve_mentions',
            'schedule_mentions', 'manage_presenters', 'view_reports',
            'manage_shows', 'manage_clients'
        ],
        'editor': [
            'view', 'edit', 'manage_mentions', 'schedule_mentions',
            'manage_shows', 'manage_clients'
        ],
        'presenter': [
            'mark_mentions_read', 'view_presenter_dashboard', 'view_schedule'
        ],
        'viewer': ['view'],
    }

    return role_permissions.get(membership.role, [])
