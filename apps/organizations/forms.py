from django import forms
from django.core.exceptions import ValidationError
from django.utils.text import slugify
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from django.core.validators import RegexValidator
import re

from .models import Organization, Branch, OrganizationMembership, OrganizationInvitation


class SignupWizardStep1Form(forms.Form):
    """Step 1: User Account Information"""
    
    first_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'Enter your first name'
        })
    )
    
    last_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'Enter your last name'
        })
    )
    
    username = forms.Char<PERSON><PERSON>(
        max_length=150,
        help_text='Username must be 3-150 characters long and contain only letters, numbers, and @/./+/-/_ characters.',
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'Choose a username'
        })
    )
    
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': '<EMAIL>'
        })
    )
    
    password1 = forms.CharField(
        label='Password',
        widget=forms.PasswordInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'Create a strong password'
        }),
        help_text='Password must be at least 8 characters long and contain a mix of letters, numbers, and special characters.'
    )
    
    password2 = forms.CharField(
        label='Confirm Password',
        widget=forms.PasswordInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'Confirm your password'
        })
    )
    
    job_title = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'e.g., Station Manager, Program Director'
        })
    )
    
    phone = forms.CharField(
        max_length=20,
        required=False,
        validators=[RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")],
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': '+****************'
        })
    )
    
    def clean_username(self):
        username = self.cleaned_data['username']
        if len(username) < 3:
            raise ValidationError('Username must be at least 3 characters long.')

        # Check for existing username (case-insensitive)
        if User.objects.filter(username__iexact=username).exists():
            # Suggest alternative usernames
            suggestions = []
            for i in range(1, 4):
                suggestion = f"{username}{i}"
                if not User.objects.filter(username__iexact=suggestion).exists():
                    suggestions.append(suggestion)

            suggestion_text = ""
            if suggestions:
                suggestion_text = f" Try: {', '.join(suggestions[:2])}"

            raise ValidationError(f'Username "{username}" is already taken.{suggestion_text}')

        # Additional username validation
        import re
        if not re.match(r'^[a-zA-Z0-9@.+_-]+$', username):
            raise ValidationError('Username can only contain letters, numbers, and @/./+/-/_ characters.')

        return username.lower()  # Store username in lowercase for consistency
    
    def clean_email(self):
        email = self.cleaned_data['email']
        email = email.lower().strip()  # Normalize email

        # Check for existing email (case-insensitive)
        if User.objects.filter(email__iexact=email).exists():
            raise ValidationError('A user with this email already exists. Please use a different email address.')

        return email
    
    def clean_password1(self):
        password1 = self.cleaned_data['password1']
        if len(password1) < 8:
            raise ValidationError('Password must be at least 8 characters long.')
        
        # Check for complexity
        if not re.search(r'[A-Za-z]', password1):
            raise ValidationError('Password must contain at least one letter.')
        if not re.search(r'\d', password1):
            raise ValidationError('Password must contain at least one number.')
        
        return password1
    
    def clean(self):
        cleaned_data = super().clean()
        password1 = cleaned_data.get('password1')
        password2 = cleaned_data.get('password2')
        
        if password1 and password2 and password1 != password2:
            raise ValidationError('Passwords do not match.')
        
        return cleaned_data


class SignupWizardStep2Form(forms.Form):
    """Step 2: Organization Basic Information"""
    
    organization_name = forms.CharField(
        max_length=200,
        label='Radio Station Name',
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'e.g., WXYZ Radio Station, Community FM 101.5'
        })
    )
    
    call_sign = forms.CharField(
        max_length=10,
        required=False,
        label='Call Sign',
        help_text='Official radio station call sign (e.g., WXYZ, KQED)',
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'WXYZ'
        })
    )
    
    frequency = forms.CharField(
        max_length=20,
        required=False,
        label='Frequency',
        help_text='Broadcasting frequency (e.g., 101.5 FM, 1010 AM)',
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': '101.5 FM'
        })
    )
    
    station_type = forms.ChoiceField(
        choices=[
            ('', 'Select Station Type'),
            ('commercial', 'Commercial Radio'),
            ('public', 'Public Radio'),
            ('community', 'Community Radio'),
            ('college', 'College/University Radio'),
            ('religious', 'Religious Radio'),
            ('talk', 'Talk Radio'),
            ('music', 'Music Radio'),
            ('news', 'News Radio'),
            ('sports', 'Sports Radio'),
            ('other', 'Other'),
        ],
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors'
        })
    )
    
    description = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'Brief description of your radio station, target audience, and programming...',
            'rows': 4
        })
    )
    
    def clean_organization_name(self):
        name = self.cleaned_data['organization_name']
        if Organization.objects.filter(name=name).exists():
            raise ValidationError('An organization with this name already exists.')
        return name


class SignupWizardStep3Form(forms.Form):
    """Step 3: Contact Information & Location"""
    
    website = forms.URLField(
        required=False,
        widget=forms.URLInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'https://www.yourstation.com'
        })
    )
    
    organization_email = forms.EmailField(
        label='Station Email',
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': '<EMAIL>'
        })
    )
    
    organization_phone = forms.CharField(
        max_length=20,
        label='Station Phone',
        validators=[RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")],
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': '+****************'
        })
    )
    
    address = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'Full station address including street, city, state, and ZIP code...',
            'rows': 3
        })
    )
    
    city = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'City'
        })
    )
    
    state = forms.CharField(
        max_length=50,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'State/Province'
        })
    )
    
    zip_code = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'ZIP/Postal Code'
        })
    )
    
    country = forms.CharField(
        max_length=50,
        initial='United States',
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'Country'
        })
    )
    
    timezone = forms.ChoiceField(
        choices=[
            ('Africa/Nairobi', 'East Africa Time (UTC+3)'),
            ('US/Eastern', 'Eastern Time (ET)'),
            ('US/Central', 'Central Time (CT)'),
            ('US/Mountain', 'Mountain Time (MT)'),
            ('US/Pacific', 'Pacific Time (PT)'),
            ('US/Alaska', 'Alaska Time (AKT)'),
            ('US/Hawaii', 'Hawaii Time (HT)'),
            ('UTC', 'UTC'),
        ],
        initial='Africa/Nairobi',
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors'
        })
    )


class SignupWizardStep4Form(forms.Form):
    """Step 4: Station Details & Configuration"""

    logo = forms.ImageField(
        required=False,
        help_text='Upload your station logo (PNG, JPG, or GIF format, max 5MB)',
        widget=forms.FileInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'accept': 'image/*'
        })
    )

    def clean_logo(self):
        logo = self.cleaned_data.get('logo')
        if logo:
            # Check file size (5MB limit)
            if logo.size > 5 * 1024 * 1024:
                raise ValidationError('Logo file size must be less than 5MB.')

            # Check file type
            if not logo.content_type.startswith('image/'):
                raise ValidationError('Please upload a valid image file.')

        return logo

    plan_type = forms.ChoiceField(
        choices=[
            ('free', 'Free Plan - Up to 5 users, 100 mentions/month'),
            ('basic', 'Basic Plan - Up to 15 users, 500 mentions/month'),
            ('premium', 'Premium Plan - Up to 50 users, 2000 mentions/month'),
            ('enterprise', 'Enterprise Plan - Unlimited users and mentions'),
        ],
        initial='free',
        widget=forms.RadioSelect(attrs={
            'class': 'plan-radio'
        })
    )

    estimated_users = forms.IntegerField(
        min_value=1,
        max_value=1000,
        initial=5,
        help_text='Estimated number of users who will access the system',
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': '5'
        })
    )

    estimated_mentions_per_month = forms.IntegerField(
        min_value=1,
        max_value=10000,
        initial=100,
        help_text='Estimated number of mentions per month',
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': '100'
        })
    )

    # Station Features
    has_multiple_shows = forms.BooleanField(
        required=False,
        initial=True,
        label='Multiple Shows',
        help_text='Does your station have multiple shows with different presenters?'
    )

    has_live_programming = forms.BooleanField(
        required=False,
        initial=True,
        label='Live Programming',
        help_text='Does your station have live programming that requires real-time mention scheduling?'
    )

    has_automated_programming = forms.BooleanField(
        required=False,
        initial=False,
        label='Automated Programming',
        help_text='Does your station use automated programming systems?'
    )

    # Notification Preferences
    enable_email_notifications = forms.BooleanField(
        required=False,
        initial=True,
        label='Email Notifications',
        help_text='Receive email notifications for important events'
    )

    enable_conflict_alerts = forms.BooleanField(
        required=False,
        initial=True,
        label='Conflict Alerts',
        help_text='Get notified when scheduling conflicts are detected'
    )

    enable_deadline_reminders = forms.BooleanField(
        required=False,
        initial=True,
        label='Deadline Reminders',
        help_text='Receive reminders for upcoming mention deadlines'
    )


class SignupWizardStep5Form(forms.Form):
    """Step 5: Initial Setup & Preferences"""

    # Sample Data
    create_sample_data = forms.BooleanField(
        required=False,
        initial=False,
        label='Create Sample Data',
        help_text='Create sample clients, shows, and mentions to help you get started (not recommended for production use)'
    )

    # Initial Branch Setup
    create_main_branch = forms.BooleanField(
        required=False,
        initial=True,
        label='Create Main Branch',
        help_text='Create a main branch for your organization (recommended for multi-location stations)'
    )

    main_branch_name = forms.CharField(
        max_length=200,
        required=False,
        initial='Main Studio',
        widget=forms.TextInput(attrs={
            'class': 'w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors',
            'placeholder': 'Main Studio'
        })
    )

    # Terms and Conditions
    agree_to_terms = forms.BooleanField(
        required=True,
        label='I agree to the Terms of Service and Privacy Policy',
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
        })
    )

    subscribe_to_updates = forms.BooleanField(
        required=False,
        initial=True,
        label='Subscribe to product updates and announcements',
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        create_main_branch = cleaned_data.get('create_main_branch')
        main_branch_name = cleaned_data.get('main_branch_name')

        if create_main_branch and not main_branch_name:
            raise ValidationError('Main branch name is required when creating a main branch.')

        return cleaned_data


class OrganizationForm(forms.ModelForm):
    """Form for creating and editing organizations"""

    class Meta:
        model = Organization
        fields = ['name', 'description', 'website', 'email', 'phone', 'address', 'logo']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter organization name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Brief description of your organization...',
                'rows': 3
            }),
            'website': forms.URLInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'https://www.example.com'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': '<EMAIL>'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': '+****************'
            }),
            'address': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Full address...',
                'rows': 3
            }),
            'logo': forms.FileInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'accept': 'image/*'
            }),
        }

    def clean_name(self):
        name = self.cleaned_data['name']
        # Exclude current instance when editing
        queryset = Organization.objects.filter(name=name)
        if self.instance and self.instance.pk:
            queryset = queryset.exclude(pk=self.instance.pk)

        if queryset.exists():
            raise ValidationError('An organization with this name already exists.')
        return name


class BranchForm(forms.ModelForm):
    """Form for creating and editing branches"""

    class Meta:
        model = Branch
        fields = ['name', 'description', 'address', 'phone', 'email', 'manager']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter branch name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Brief description of this branch...',
                'rows': 3
            }),
            'address': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Branch address...',
                'rows': 3
            }),
            'phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': '+****************'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': '<EMAIL>'
            }),
            'manager': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
        }


class OrganizationInvitationForm(forms.ModelForm):
    """Form for inviting users to organizations"""

    class Meta:
        model = OrganizationInvitation
        fields = ['email', 'role', 'branch']
        widgets = {
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': '<EMAIL>'
            }),
            'role': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'branch': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        if organization:
            self.fields['branch'].queryset = Branch.objects.filter(
                organization=organization,
                is_active=True
            )
        else:
            self.fields['branch'].queryset = Branch.objects.none()


class MembershipEditForm(forms.ModelForm):
    """Form for editing organization membership"""

    class Meta:
        model = OrganizationMembership
        fields = ['role', 'branch', 'is_active']
        widgets = {
            'role': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'branch': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            }),
        }

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        if organization:
            self.fields['branch'].queryset = Branch.objects.filter(
                organization=organization,
                is_active=True
            )
        else:
            self.fields['branch'].queryset = Branch.objects.none()
