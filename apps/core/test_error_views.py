"""
Test views for error pages - only for development/testing.
These views should NOT be used in production.
"""

from django.http import Http404, HttpResponseServerError, HttpResponseForbidden, HttpResponseBadRequest
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.conf import settings


def test_404_view(request):
    """Test view to trigger a 404 error."""
    if not settings.DEBUG:
        raise Http404("Test views are only available in DEBUG mode")
    raise Http404("This is a test 404 error")


def test_500_view(request):
    """Test view to trigger a 500 error."""
    if not settings.DEBUG:
        raise Http404("Test views are only available in DEBUG mode")
    raise Exception("This is a test 500 error")


def test_403_view(request):
    """Test view to trigger a 403 error."""
    if not settings.DEBUG:
        raise Http404("Test views are only available in DEBUG mode")
    from django.core.exceptions import PermissionDenied
    raise PermissionDenied("This is a test 403 error")


def test_400_view(request):
    """Test view to trigger a 400 error."""
    if not settings.DEBUG:
        raise Http404("Test views are only available in DEBUG mode")
    from django.core.exceptions import BadRequest
    raise BadRequest("This is a test 400 error")


@csrf_exempt
def test_csrf_view(request):
    """Test view to trigger a CSRF error."""
    if not settings.DEBUG:
        raise Http404("Test views are only available in DEBUG mode")
    
    if request.method == 'GET':
        # Show a form without CSRF token to test CSRF failure
        return render(request, 'test_csrf_form.html')
    else:
        # This should trigger CSRF failure if accessed without proper token
        return render(request, 'test_csrf_success.html')


def test_maintenance_view(request):
    """Test view to show maintenance page."""
    if not settings.DEBUG:
        raise Http404("Test views are only available in DEBUG mode")
    return render(request, 'maintenance.html')


def error_test_index(request):
    """Index page for testing error pages."""
    if not settings.DEBUG:
        raise Http404("Test views are only available in DEBUG mode")
    
    context = {
        'test_urls': [
            {'name': '404 Error', 'url': '/test-errors/404/', 'description': 'Page not found error'},
            {'name': '500 Error', 'url': '/test-errors/500/', 'description': 'Internal server error'},
            {'name': '403 Error', 'url': '/test-errors/403/', 'description': 'Permission denied error'},
            {'name': '400 Error', 'url': '/test-errors/400/', 'description': 'Bad request error'},
            {'name': 'CSRF Error', 'url': '/test-errors/csrf/', 'description': 'CSRF verification failed'},
            {'name': 'Maintenance', 'url': '/test-errors/maintenance/', 'description': 'Maintenance page'},
        ]
    }
    
    return render(request, 'test_error_index.html', context)
