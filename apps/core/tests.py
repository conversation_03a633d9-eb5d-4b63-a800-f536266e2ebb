from django.test import TestCase, Client as TestClient
from django.contrib.auth.models import User
from django.urls import reverse
from apps.organizations.models import Organization, OrganizationMembership
from apps.core.models import Client, Presenter, Industry
from apps.core.forms import ClientForm


class ClientIndustryTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.organization = Organization.objects.create(
            name='Test Radio Station',
            slug='test-radio'
        )
        self.membership = OrganizationMembership.objects.create(
            user=self.user,
            organization=self.organization,
            role='admin'
        )

        # Create test industries for the organization
        self.industries = [
            Industry.objects.create(
                organization=self.organization,
                code='technology',
                name='Technology',
                description='Technology industry',
                is_active=True
            ),
            Industry.objects.create(
                organization=self.organization,
                code='healthcare',
                name='Healthcare',
                description='Healthcare industry',
                is_active=True
            ),
            Industry.objects.create(
                organization=self.organization,
                code='retail',
                name='Retail',
                description='Retail industry',
                is_active=True
            ),
        ]
        self.client_obj = Client.objects.create(
            name='Test Client',
            contact_person='<PERSON>',
            email='<EMAIL>',
            phone='**********',
            industry='technology',
            organization=self.organization
        )
        self.test_client = TestClient()

    def test_client_industry_choices(self):
        """Test that industry choices are properly loaded from Industry model"""
        # Test that industry choices exist for the organization
        choices = Client.get_industry_choices(self.organization)
        self.assertTrue(len(choices) > 1)  # Should have at least the empty choice plus industries

        # Test that default industries are available
        industry_values = [choice[0] for choice in choices]
        self.assertIn('technology', industry_values)
        self.assertIn('healthcare', industry_values)
        self.assertIn('retail', industry_values)

    def test_client_form_industry_widget(self):
        """Test that ClientForm uses Select widget for industry"""
        form = ClientForm()
        # Check that industry field uses Select widget
        self.assertEqual(form.fields['industry'].widget.__class__.__name__, 'Select')

    def test_client_list_industry_filter(self):
        """Test industry filtering in client list view"""
        self.test_client.login(username='testuser', password='testpass123')

        # Set current organization in session
        session = self.test_client.session
        session['current_organization_id'] = self.organization.id
        session.save()

        # Test filtering by industry
        response = self.test_client.get(reverse('core:client_list'), {'industry': 'technology'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Client')

        # Test filtering by different industry (should not show our client)
        response = self.test_client.get(reverse('core:client_list'), {'industry': 'healthcare'})
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'Test Client')

    def test_client_creation_with_industry(self):
        """Test creating a client with industry selection"""
        self.test_client.login(username='testuser', password='testpass123')

        # Set current organization in session
        session = self.test_client.session
        session['current_organization_id'] = self.organization.id
        session.save()

        client_data = {
            'name': 'New Test Client',
            'contact_person': 'Jane Smith',
            'email': '<EMAIL>',
            'phone': '**********',
            'industry': 'healthcare',
            'is_active': True
        }

        response = self.test_client.post(reverse('core:client_create'), client_data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful creation

        # Verify client was created with correct industry
        new_client = Client.objects.get(name='New Test Client')
        self.assertEqual(new_client.industry, 'healthcare')


class MentionReorderTestCase(TestCase):
    def setUp(self):
        """Set up test data for mention reordering"""
        self.user = User.objects.create_user(
            username='presenter',
            email='<EMAIL>',
            password='testpass123'
        )
        self.organization = Organization.objects.create(
            name='Test Radio Station',
            slug='test-radio'
        )
        self.membership = OrganizationMembership.objects.create(
            user=self.user,
            organization=self.organization,
            role='presenter'
        )
        # Create presenter profile
        from apps.core.models import Presenter
        self.presenter = Presenter.objects.create(
            user=self.user,
            organization=self.organization,
            first_name='Test',
            last_name='Presenter',
            email='<EMAIL>'
        )
        self.test_client = TestClient()

    def test_reorder_mentions_endpoint_exists(self):
        """Test that the reorder mentions endpoint exists"""
        self.test_client.login(username='presenter', password='testpass123')

        # Set current organization in session
        session = self.test_client.session
        session['current_organization_id'] = self.organization.id
        session.save()

        # Test POST request to reorder endpoint
        response = self.test_client.post(
            reverse('core:reorder_mentions'),
            data='{"mention_order": []}',
            content_type='application/json'
        )
        # Should return 400 for empty mention order, but endpoint should exist
        self.assertEqual(response.status_code, 400)

    def test_reorder_mentions_requires_login(self):
        """Test that reorder mentions requires authentication"""
        response = self.test_client.post(reverse('core:reorder_mentions'))
        # Should redirect to login
        self.assertEqual(response.status_code, 302)


class PresenterProfileCreationTestCase(TestCase):
    def setUp(self):
        """Set up test data for presenter profile creation"""
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='testpass123'
        )
        self.organization = Organization.objects.create(
            name='Test Radio Station',
            slug='test-radio'
        )
        self.admin_membership = OrganizationMembership.objects.create(
            user=self.admin_user,
            organization=self.organization,
            role='admin'
        )
        self.test_client = TestClient()

    def test_presenter_profile_created_on_user_creation(self):
        """Test that presenter profile is created when user is created with presenter role"""
        self.test_client.login(username='admin', password='testpass123')

        # Set current organization in session
        session = self.test_client.session
        session['current_organization_id'] = self.organization.id
        session.save()

        # Create user with presenter role
        user_data = {
            'username': 'newpresenter',
            'email': '<EMAIL>',
            'password': 'testpass123',
            'first_name': 'New',
            'last_name': 'Presenter',
            'role': 'presenter'
        }

        response = self.test_client.post('/users/create/', user_data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful creation

        # Verify user was created
        new_user = User.objects.get(username='newpresenter')
        self.assertEqual(new_user.email, '<EMAIL>')

        # Verify organization membership was created with presenter role
        membership = OrganizationMembership.objects.get(user=new_user, organization=self.organization)
        self.assertEqual(membership.role, 'presenter')

        # Verify presenter profile was created automatically
        presenter = Presenter.objects.get(user=new_user, organization=self.organization)
        self.assertEqual(presenter.first_name, 'New')
        self.assertEqual(presenter.last_name, 'Presenter')
        self.assertEqual(presenter.email, '<EMAIL>')
        self.assertTrue(presenter.is_active)

    def test_presenter_profile_created_on_role_change(self):
        """Test that presenter profile is created when existing user role is changed to presenter"""
        # Create a regular user first
        regular_user = User.objects.create_user(
            username='regularuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Regular',
            last_name='User'
        )
        regular_membership = OrganizationMembership.objects.create(
            user=regular_user,
            organization=self.organization,
            role='viewer'
        )

        # Verify no presenter profile exists initially
        self.assertFalse(Presenter.objects.filter(user=regular_user, organization=self.organization).exists())

        self.test_client.login(username='admin', password='testpass123')

        # Set current organization in session
        session = self.test_client.session
        session['current_organization_id'] = self.organization.id
        session.save()

        # Change user role to presenter
        edit_data = {
            'first_name': 'Regular',
            'last_name': 'User',
            'email': '<EMAIL>',
            'organization_role': 'presenter',
            'phone': '',
            'bio': '',
            'job_title': '',
            'department': '',
            'timezone': 'UTC',
            'language': 'en',
            'theme': 'light'
        }

        response = self.test_client.post(f'/users/{regular_user.id}/edit/', edit_data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful update

        # Verify role was changed
        regular_membership.refresh_from_db()
        self.assertEqual(regular_membership.role, 'presenter')

        # Verify presenter profile was created
        presenter = Presenter.objects.get(user=regular_user, organization=self.organization)
        self.assertEqual(presenter.first_name, 'Regular')
        self.assertEqual(presenter.last_name, 'User')
        self.assertEqual(presenter.email, '<EMAIL>')
        self.assertTrue(presenter.is_active)

    def test_presenter_profile_deactivated_on_role_change_away_from_presenter(self):
        """Test that presenter profile is deactivated when user role is changed from presenter"""
        # Create a presenter user
        presenter_user = User.objects.create_user(
            username='presenteruser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Presenter',
            last_name='User'
        )
        presenter_membership = OrganizationMembership.objects.create(
            user=presenter_user,
            organization=self.organization,
            role='presenter'
        )

        # Create presenter profile
        presenter = Presenter.objects.create(
            user=presenter_user,
            organization=self.organization,
            first_name='Presenter',
            last_name='User',
            email='<EMAIL>',
            is_active=True
        )

        self.test_client.login(username='admin', password='testpass123')

        # Set current organization in session
        session = self.test_client.session
        session['current_organization_id'] = self.organization.id
        session.save()

        # Change user role from presenter to viewer
        edit_data = {
            'first_name': 'Presenter',
            'last_name': 'User',
            'email': '<EMAIL>',
            'organization_role': 'viewer',
            'phone': '',
            'bio': '',
            'job_title': '',
            'department': '',
            'timezone': 'UTC',
            'language': 'en',
            'theme': 'light'
        }

        response = self.test_client.post(f'/users/{presenter_user.id}/edit/', edit_data)
        self.assertEqual(response.status_code, 302)  # Redirect after successful update

        # Verify role was changed
        presenter_membership.refresh_from_db()
        self.assertEqual(presenter_membership.role, 'viewer')

        # Verify presenter profile was deactivated
        presenter.refresh_from_db()
        self.assertFalse(presenter.is_active)
