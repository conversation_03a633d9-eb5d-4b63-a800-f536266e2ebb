import requests
from django.core.cache import cache
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class WeatherService:
    """Service for fetching weather data from OpenWeatherMap API"""
    
    BASE_URL = "http://api.openweathermap.org/data/2.5/weather"
    CACHE_TIMEOUT = 600  # 10 minutes
    
    def __init__(self, api_key, units='imperial'):
        self.api_key = api_key
        self.units = units
    
    def get_weather(self, location):
        """
        Get current weather for a location
        
        Args:
            location (str): City name, state/country code (e.g., "London, UK")
            
        Returns:
            dict: Weather data or None if error
        """
        if not self.api_key or not location:
            return None
            
        # Create cache key (sanitize for memcached compatibility)
        import hashlib
        location_hash = hashlib.md5(location.encode()).hexdigest()[:8]
        cache_key = f"weather_{location_hash}_{self.units}_{self.api_key[:8]}"
        
        # Try to get from cache first
        cached_data = cache.get(cache_key)
        if cached_data:
            logger.info(f"Weather data for {location} retrieved from cache")
            return cached_data
        
        try:
            # Make API request
            params = {
                'q': location,
                'appid': self.api_key,
                'units': self.units
            }
            
            response = requests.get(self.BASE_URL, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                weather_data = self._parse_weather_data(data)
                
                # Cache the result
                cache.set(cache_key, weather_data, self.CACHE_TIMEOUT)
                logger.info(f"Weather data for {location} fetched and cached")
                
                return weather_data
            else:
                logger.error(f"Weather API error {response.status_code} for {location}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error(f"Weather API timeout for {location}")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Weather API request error for {location}: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected weather API error for {location}: {str(e)}")
            return None
    
    def _parse_weather_data(self, data):
        """Parse OpenWeatherMap API response into simplified format"""
        try:
            return {
                'location': data['name'],
                'country': data['sys']['country'],
                'temperature': round(data['main']['temp']),
                'feels_like': round(data['main']['feels_like']),
                'humidity': data['main']['humidity'],
                'pressure': data['main']['pressure'],
                'description': data['weather'][0]['description'].title(),
                'icon': data['weather'][0]['icon'],
                'wind_speed': data.get('wind', {}).get('speed', 0),
                'wind_direction': data.get('wind', {}).get('deg', 0),
                'visibility': data.get('visibility', 0) / 1000,  # Convert to km
                'units': self.units,
                'timestamp': data['dt']
            }
        except (KeyError, IndexError, TypeError) as e:
            logger.error(f"Error parsing weather data: {str(e)}")
            return None
    
    def get_weather_areas(self, country, state=None):
        """
        Get suggested weather areas based on organization location
        
        Args:
            country (str): Country code or name
            state (str): State/province code or name (optional)
            
        Returns:
            list: List of suggested location strings
        """
        # Common major cities by country
        city_suggestions = {
            'US': [
                'New York, NY, US',
                'Los Angeles, CA, US', 
                'Chicago, IL, US',
                'Houston, TX, US',
                'Phoenix, AZ, US',
                'Philadelphia, PA, US',
                'San Antonio, TX, US',
                'San Diego, CA, US',
                'Dallas, TX, US',
                'San Jose, CA, US'
            ],
            'CA': [
                'Toronto, ON, CA',
                'Montreal, QC, CA',
                'Vancouver, BC, CA',
                'Calgary, AB, CA',
                'Edmonton, AB, CA',
                'Ottawa, ON, CA',
                'Winnipeg, MB, CA',
                'Quebec City, QC, CA',
                'Hamilton, ON, CA',
                'Kitchener, ON, CA'
            ],
            'GB': [
                'London, GB',
                'Birmingham, GB',
                'Manchester, GB',
                'Glasgow, GB',
                'Liverpool, GB',
                'Leeds, GB',
                'Sheffield, GB',
                'Edinburgh, GB',
                'Bristol, GB',
                'Cardiff, GB'
            ],
            'AU': [
                'Sydney, NSW, AU',
                'Melbourne, VIC, AU',
                'Brisbane, QLD, AU',
                'Perth, WA, AU',
                'Adelaide, SA, AU',
                'Gold Coast, QLD, AU',
                'Newcastle, NSW, AU',
                'Canberra, ACT, AU',
                'Sunshine Coast, QLD, AU',
                'Wollongong, NSW, AU'
            ]
        }
        
        # Get suggestions for the country
        suggestions = city_suggestions.get(country.upper(), [])
        
        # If no specific suggestions, create generic ones
        if not suggestions:
            if state:
                suggestions = [f"Capital City, {state}, {country}"]
            else:
                suggestions = [f"Capital City, {country}"]
        
        return suggestions
    
    @staticmethod
    def get_weather_icon_url(icon_code):
        """Get full URL for weather icon"""
        return f"https://openweathermap.org/img/w/{icon_code}.png"
    
    @staticmethod
    def get_temperature_unit_symbol(units):
        """Get temperature unit symbol"""
        unit_symbols = {
            'imperial': '°F',
            'metric': '°C',
            'kelvin': 'K'
        }
        return unit_symbols.get(units, '°F')
    
    @staticmethod
    def get_wind_direction(degrees):
        """Convert wind direction degrees to compass direction"""
        directions = [
            'N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
            'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'
        ]
        index = round(degrees / 22.5) % 16
        return directions[index]


def get_organization_weather(organization):
    """
    Get weather data for an organization based on their API settings
    
    Args:
        organization: Organization instance
        
    Returns:
        dict: Weather data or None if not configured/error
    """
    try:
        api_settings = organization.api_settings
        
        if not api_settings.has_weather_api:
            return None
            
        weather_service = WeatherService(
            api_key=api_settings.openweather_api_key,
            units=api_settings.weather_units
        )
        
        return weather_service.get_weather(api_settings.weather_location)
        
    except Exception as e:
        logger.error(f"Error getting organization weather: {str(e)}")
        return None
