"""
Caching utilities for production optimization
"""
import hashlib
from functools import wraps
from django.core.cache import cache
from django.conf import settings
from django.utils.encoding import force_str
from django.db.models.signals import post_save, post_delete
import logging

logger = logging.getLogger(__name__)


def make_cache_key(*args, **kwargs):
    """
    Generate a cache key from arguments
    """
    key_parts = []
    
    # Add positional arguments
    for arg in args:
        key_parts.append(str(arg))
    
    # Add keyword arguments
    for k, v in sorted(kwargs.items()):
        key_parts.append(f"{k}:{v}")
    
    # Create hash of the key parts
    key_string = ":".join(key_parts)
    key_hash = hashlib.md5(force_str(key_string).encode()).hexdigest()
    
    return f"cache:{key_hash}"


def cache_result(timeout=300, key_prefix="", version=None):
    """
    Decorator to cache function results
    
    Args:
        timeout: Cache timeout in seconds (default: 5 minutes)
        key_prefix: Prefix for cache key
        version: Cache version for invalidation
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = make_cache_key(
                key_prefix,
                func.__name__,
                *args,
                **kwargs
            )
            
            # Try to get from cache
            result = cache.get(cache_key, version=version)
            if result is not None:
                logger.debug(f"Cache hit for {func.__name__}: {cache_key}")
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout=timeout, version=version)
            logger.debug(f"Cache set for {func.__name__}: {cache_key}")
            
            return result
        return wrapper
    return decorator


def invalidate_cache_pattern(pattern):
    """
    Invalidate cache keys matching a pattern
    """
    try:
        # This requires django-redis
        from django_redis import get_redis_connection
        redis_conn = get_redis_connection("default")
        
        # Find keys matching pattern
        keys = redis_conn.keys(f"*{pattern}*")
        if keys:
            redis_conn.delete(*keys)
            logger.info(f"Invalidated {len(keys)} cache keys matching pattern: {pattern}")
    except ImportError:
        logger.warning("django-redis not available, cannot invalidate cache patterns")
    except Exception as e:
        logger.error(f"Error invalidating cache pattern {pattern}: {e}")


class CacheManager:
    """
    Centralized cache management
    """
    
    # Cache timeouts (in seconds)
    TIMEOUTS = {
        'short': 60,           # 1 minute
        'medium': 300,         # 5 minutes
        'long': 1800,          # 30 minutes
        'very_long': 3600,     # 1 hour
        'daily': 86400,        # 24 hours
    }
    
    @classmethod
    def get_timeout(cls, duration='medium'):
        """Get timeout value by name"""
        return cls.TIMEOUTS.get(duration, cls.TIMEOUTS['medium'])
    
    @classmethod
    def cache_organization_data(cls, org_id, data, timeout='long'):
        """Cache organization-specific data"""
        key = f"org:{org_id}:data"
        cache.set(key, data, timeout=cls.get_timeout(timeout))
        return key
    
    @classmethod
    def get_organization_data(cls, org_id):
        """Get cached organization data"""
        key = f"org:{org_id}:data"
        return cache.get(key)
    
    @classmethod
    def invalidate_organization_cache(cls, org_id):
        """Invalidate all cache for an organization"""
        invalidate_cache_pattern(f"org:{org_id}")
    
    @classmethod
    def cache_user_permissions(cls, user_id, org_id, permissions, timeout='medium'):
        """Cache user permissions for an organization"""
        key = f"user:{user_id}:org:{org_id}:permissions"
        cache.set(key, permissions, timeout=cls.get_timeout(timeout))
        return key
    
    @classmethod
    def get_user_permissions(cls, user_id, org_id):
        """Get cached user permissions"""
        key = f"user:{user_id}:org:{org_id}:permissions"
        return cache.get(key)
    
    @classmethod
    def cache_mention_stats(cls, org_id, stats, timeout='short'):
        """Cache mention statistics"""
        key = f"org:{org_id}:mention_stats"
        cache.set(key, stats, timeout=cls.get_timeout(timeout))
        return key
    
    @classmethod
    def get_mention_stats(cls, org_id):
        """Get cached mention statistics"""
        key = f"org:{org_id}:mention_stats"
        return cache.get(key)
    
    @classmethod
    def cache_dashboard_data(cls, user_id, org_id, data, timeout='short'):
        """Cache dashboard data for a user"""
        key = f"user:{user_id}:org:{org_id}:dashboard"
        cache.set(key, data, timeout=cls.get_timeout(timeout))
        return key
    
    @classmethod
    def get_dashboard_data(cls, user_id, org_id):
        """Get cached dashboard data"""
        key = f"user:{user_id}:org:{org_id}:dashboard"
        return cache.get(key)


# Signal handlers for cache invalidation
def invalidate_organization_cache_on_change(sender, instance, **kwargs):
    """Invalidate organization cache when organization changes"""
    if hasattr(instance, 'organization'):
        CacheManager.invalidate_organization_cache(instance.organization.id)
    elif hasattr(instance, 'id'):
        CacheManager.invalidate_organization_cache(instance.id)


def invalidate_user_cache_on_change(sender, instance, **kwargs):
    """Invalidate user cache when user-related data changes"""
    if hasattr(instance, 'user'):
        invalidate_cache_pattern(f"user:{instance.user.id}")


# Template cache tags
from django import template
from django.template.loader import render_to_string
from django.utils.safestring import mark_safe

register = template.Library()


@register.simple_tag(takes_context=True)
def cache_include(context, template_name, cache_key=None, timeout=300, **kwargs):
    """
    Template tag to cache included templates
    """
    if cache_key is None:
        cache_key = f"template:{template_name}:{hash(str(kwargs))}"
    
    # Try to get from cache
    cached_content = cache.get(cache_key)
    if cached_content is not None:
        return mark_safe(cached_content)
    
    # Render template and cache
    content = render_to_string(template_name, {**context.flatten(), **kwargs})
    cache.set(cache_key, content, timeout=timeout)
    
    return mark_safe(content)


# Query optimization decorators
def select_related_cache(related_fields, timeout=300):
    """
    Decorator to cache QuerySet with select_related
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = make_cache_key(
                func.__name__,
                str(related_fields),
                *args,
                **kwargs
            )
            
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute query with select_related
            queryset = func(*args, **kwargs)
            if hasattr(queryset, 'select_related'):
                queryset = queryset.select_related(*related_fields)
            
            # Convert to list to cache
            result = list(queryset)
            cache.set(cache_key, result, timeout=timeout)
            
            return result
        return wrapper
    return decorator


def prefetch_related_cache(prefetch_fields, timeout=300):
    """
    Decorator to cache QuerySet with prefetch_related
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = make_cache_key(
                func.__name__,
                str(prefetch_fields),
                *args,
                **kwargs
            )
            
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute query with prefetch_related
            queryset = func(*args, **kwargs)
            if hasattr(queryset, 'prefetch_related'):
                queryset = queryset.prefetch_related(*prefetch_fields)
            
            # Convert to list to cache
            result = list(queryset)
            cache.set(cache_key, result, timeout=timeout)
            
            return result
        return wrapper
    return decorator
