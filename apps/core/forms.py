from django import forms
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.core.validators import MinLengthValidator
from .models import Client, Presenter, Industry, PresenterPersonalNote, ShowNote
from apps.organizations.models import Organization, OrganizationMembership
import json


class IndustryForm(forms.ModelForm):
    """Form for creating and editing industries"""

    class Meta:
        model = Industry
        fields = ['name', 'code', 'description', 'color', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter industry name (e.g., Healthcare)'
            }),
            'code': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter industry code (e.g., healthcare)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Optional description of this industry...',
                'rows': 3
            }),
            'color': forms.TextInput(attrs={
                'type': 'color',
                'class': 'w-16 h-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'title': 'Choose industry color'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

    def clean_code(self):
        code = self.cleaned_data.get('code')
        if code:
            # Convert to lowercase and replace spaces with underscores
            code = code.lower().replace(' ', '_').replace('-', '_')
            # Check for uniqueness within organization
            if self.organization:
                existing = Industry.objects.filter(
                    organization=self.organization,
                    code=code
                ).exclude(pk=self.instance.pk if self.instance else None)
                if existing.exists():
                    raise ValidationError('An industry with this code already exists in your organization.')
        return code


class ClientForm(forms.ModelForm):
    """Form for creating and editing clients"""

    # Override industry field as ChoiceField to ensure proper rendering
    industry = forms.ChoiceField(
        choices=[],  # Will be populated in __init__
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        })
    )

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        # Update industry choices to include custom industries
        if self.organization:
            self.fields['industry'].choices = Client.get_industry_choices(self.organization)
        else:
            self.fields['industry'].choices = [('', 'Select Industry')]

    class Meta:
        model = Client
        fields = ['name', 'contact_person', 'email', 'phone', 'address', 'industry', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter client name'
            }),
            'contact_person': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter contact person name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': '<EMAIL>'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': '+****************'
            }),
            'address': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter full address...',
                'rows': 3
            }),

            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            }),
        }


class PresenterPersonalNoteForm(forms.ModelForm):
    """Form for creating and editing presenter personal notes"""

    # Custom field for tags input
    tags_input = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            'placeholder': 'Enter tags separated by commas (e.g., important, meeting, reminder)',
        }),
        help_text="Enter tags separated by commas"
    )

    class Meta:
        model = PresenterPersonalNote
        fields = [
            'title', 'content', 'category', 'priority', 'status',
            'due_date', 'is_pinned', 'is_private'
        ]

        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                'placeholder': 'Enter note title...',
            }),
            'content': forms.Textarea(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                'rows': 6,
                'placeholder': 'Write your note content here...',
            }),
            'category': forms.Select(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            }),
            'priority': forms.Select(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            }),
            'status': forms.Select(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            }),
            'due_date': forms.DateTimeInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                'type': 'datetime-local',
            }),
            'is_pinned': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            }),
            'is_private': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            }),
        }

        labels = {
            'title': 'Note Title',
            'content': 'Note Content',
            'category': 'Category',
            'priority': 'Priority Level',
            'status': 'Status',
            'due_date': 'Due Date (Optional)',
            'is_pinned': 'Pin to top',
            'is_private': 'Keep private',
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Add validation
        self.fields['title'].validators.append(MinLengthValidator(3))
        self.fields['content'].validators.append(MinLengthValidator(10))

        # Pre-populate tags field if editing
        if self.instance and self.instance.pk and self.instance.tags:
            self.fields['tags_input'].initial = ', '.join(self.instance.tags)

    def clean_tags_input(self):
        """Process tags input into a list"""
        tags_input = self.cleaned_data.get('tags_input', '')
        if tags_input:
            # Split by comma, strip whitespace, and filter empty strings
            tags = [tag.strip() for tag in tags_input.split(',') if tag.strip()]
            # Limit to 10 tags and 30 characters each
            tags = tags[:10]
            tags = [tag[:30] for tag in tags]
            return tags
        return []

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Set user and presenter if not already set
        if self.user and not instance.user_id:
            instance.user = self.user
            # Get presenter profile for the user
            try:
                presenter = Presenter.objects.get(user=self.user)
                instance.presenter = presenter
            except Presenter.DoesNotExist:
                raise forms.ValidationError("User does not have a presenter profile.")

        # Process tags
        tags = self.cleaned_data.get('tags_input', [])
        if isinstance(tags, list):
            instance.tags = tags

        if commit:
            instance.save()
        return instance


class PresenterPersonalNoteFilterForm(forms.Form):
    """Form for filtering presenter personal notes"""

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            'placeholder': 'Search notes...',
        })
    )

    category = forms.ChoiceField(
        required=False,
        choices=[('', 'All Categories')] + PresenterPersonalNote.CATEGORY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
        })
    )

    priority = forms.ChoiceField(
        required=False,
        choices=[('', 'All Priorities')] + PresenterPersonalNote.PRIORITY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
        })
    )

    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Statuses')] + PresenterPersonalNote.STATUS_CHOICES,
        widget=forms.Select(attrs={
            'class': 'block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
        })
    )


class QuickNoteForm(forms.Form):
    """Quick form for creating simple notes"""

    title = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            'placeholder': 'Quick note title...',
        })
    )

    content = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            'rows': 3,
            'placeholder': 'Quick note content...',
        })
    )

    priority = forms.ChoiceField(
        choices=PresenterPersonalNote.PRIORITY_CHOICES,
        initial=2,
        widget=forms.Select(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
        })
    )

    def save(self, user):
        """Create a new note from the form data"""
        try:
            presenter = Presenter.objects.get(user=user)
        except Presenter.DoesNotExist:
            raise forms.ValidationError("User does not have a presenter profile.")

        note = PresenterPersonalNote.objects.create(
            user=user,
            presenter=presenter,
            title=self.cleaned_data['title'],
            content=self.cleaned_data['content'],
            priority=self.cleaned_data['priority'],
            category='general',
            status='active'
        )
        return note


class ShowNoteForm(forms.ModelForm):
    """Form for creating and editing show-specific notes"""

    # Custom field for tags input
    tags_input = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            'placeholder': 'Enter tags separated by commas (e.g., important, guest, music)',
        }),
        help_text="Enter tags separated by commas"
    )

    class Meta:
        model = ShowNote
        fields = [
            'title', 'content', 'category', 'priority', 'status',
            'is_pinned'
        ]

        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                'placeholder': 'Enter note title...',
            }),
            'content': forms.Textarea(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
                'rows': 6,
                'placeholder': 'Write your show note content here...',
            }),
            'category': forms.Select(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            }),
            'priority': forms.Select(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            }),
            'status': forms.Select(attrs={
                'class': 'mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            }),
            'is_pinned': forms.CheckboxInput(attrs={
                'class': 'rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500',
            }),
        }

        labels = {
            'title': 'Note Title',
            'content': 'Note Content',
            'category': 'Category',
            'priority': 'Priority Level',
            'status': 'Status',
            'is_pinned': 'Pin to top',
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.show = kwargs.pop('show', None)
        self.show_date = kwargs.pop('show_date', None)
        super().__init__(*args, **kwargs)

        # Add validation
        self.fields['title'].validators.append(MinLengthValidator(3))
        self.fields['content'].validators.append(MinLengthValidator(10))

        # Pre-populate tags field if editing
        if self.instance and self.instance.pk and self.instance.tags:
            self.fields['tags_input'].initial = ', '.join(self.instance.tags)

    def clean_tags_input(self):
        """Process tags input into a list"""
        tags_input = self.cleaned_data.get('tags_input', '')
        if tags_input:
            # Split by comma, strip whitespace, and filter empty strings
            tags = [tag.strip() for tag in tags_input.split(',') if tag.strip()]
            # Limit to 10 tags and 30 characters each
            tags = tags[:10]
            tags = [tag[:30] for tag in tags]
            return tags
        return []

    def clean_title(self):
        """Validate title uniqueness for the same show/date combination"""
        title = self.cleaned_data.get('title')
        if title and self.user and self.show and self.show_date:
            # Check for duplicate titles for the same user, show, and date
            existing = ShowNote.objects.filter(
                user=self.user,
                show=self.show,
                show_date=self.show_date,
                title=title
            ).exclude(pk=self.instance.pk if self.instance else None)

            if existing.exists():
                raise ValidationError('You already have a note with this title for this show on this date.')
        return title

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Set user, presenter, show, and show_date if not already set
        if self.user and not instance.user_id:
            instance.user = self.user
            # Get presenter profile for the user
            try:
                presenter = Presenter.objects.get(user=self.user)
                instance.presenter = presenter
            except Presenter.DoesNotExist:
                raise forms.ValidationError("User does not have a presenter profile.")

        if self.show and not instance.show_id:
            instance.show = self.show

        if self.show_date and not instance.show_date:
            instance.show_date = self.show_date

        # Process tags
        tags = self.cleaned_data.get('tags_input', [])
        if isinstance(tags, list):
            instance.tags = tags

        if commit:
            instance.save()
        return instance