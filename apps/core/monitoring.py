"""
Performance monitoring and health check utilities
"""
import time
import psutil
import logging
from django.core.cache import cache
from django.db import connection
from django.conf import settings
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.cache import never_cache
from django.utils.decorators import method_decorator
from django.views import View
import json

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """
    Performance monitoring utilities
    """
    
    @staticmethod
    def get_system_metrics():
        """
        Get system performance metrics
        """
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available = memory.available
            memory_total = memory.total
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_free = disk.free
            disk_total = disk.total
            
            # Network I/O
            network = psutil.net_io_counters()
            
            return {
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count,
                },
                'memory': {
                    'percent': memory_percent,
                    'available': memory_available,
                    'total': memory_total,
                    'used': memory_total - memory_available,
                },
                'disk': {
                    'percent': disk_percent,
                    'free': disk_free,
                    'total': disk_total,
                    'used': disk_total - disk_free,
                },
                'network': {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv,
                },
                'timestamp': time.time(),
            }
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            return None
    
    @staticmethod
    def get_database_metrics():
        """
        Get database performance metrics
        """
        try:
            with connection.cursor() as cursor:
                # Get database size
                cursor.execute("""
                    SELECT pg_size_pretty(pg_database_size(current_database())) as size,
                           pg_database_size(current_database()) as size_bytes
                """)
                db_size = cursor.fetchone()
                
                # Get connection count
                cursor.execute("""
                    SELECT count(*) as connections
                    FROM pg_stat_activity
                    WHERE state = 'active'
                """)
                active_connections = cursor.fetchone()[0]
                
                # Get slow queries (if pg_stat_statements is available)
                try:
                    cursor.execute("""
                        SELECT query, calls, total_time, mean_time
                        FROM pg_stat_statements
                        ORDER BY mean_time DESC
                        LIMIT 5
                    """)
                    slow_queries = cursor.fetchall()
                except:
                    slow_queries = []
                
                return {
                    'size': db_size[0] if db_size else 'Unknown',
                    'size_bytes': db_size[1] if db_size else 0,
                    'active_connections': active_connections,
                    'slow_queries': slow_queries,
                    'timestamp': time.time(),
                }
        except Exception as e:
            logger.error(f"Error getting database metrics: {e}")
            return None
    
    @staticmethod
    def get_cache_metrics():
        """
        Get cache performance metrics
        """
        try:
            # Try to get Redis info if using django-redis
            try:
                from django_redis import get_redis_connection
                redis_conn = get_redis_connection("default")
                redis_info = redis_conn.info()
                
                return {
                    'type': 'redis',
                    'used_memory': redis_info.get('used_memory', 0),
                    'used_memory_human': redis_info.get('used_memory_human', '0B'),
                    'connected_clients': redis_info.get('connected_clients', 0),
                    'total_commands_processed': redis_info.get('total_commands_processed', 0),
                    'keyspace_hits': redis_info.get('keyspace_hits', 0),
                    'keyspace_misses': redis_info.get('keyspace_misses', 0),
                    'timestamp': time.time(),
                }
            except ImportError:
                # Fallback for other cache backends
                return {
                    'type': 'other',
                    'status': 'available',
                    'timestamp': time.time(),
                }
        except Exception as e:
            logger.error(f"Error getting cache metrics: {e}")
            return None
    
    @staticmethod
    def get_application_metrics():
        """
        Get application-specific metrics
        """
        try:
            from apps.mentions.models import Mention, MentionReading
            from apps.organizations.models import Organization
            from apps.core.models import Client
            from django.contrib.auth.models import User
            
            # Get counts
            total_organizations = Organization.objects.count()
            total_users = User.objects.count()
            total_clients = Client.objects.count()
            total_mentions = Mention.objects.count()
            pending_mentions = Mention.objects.filter(status='pending').count()
            scheduled_mentions = Mention.objects.filter(status='scheduled').count()
            completed_readings = MentionReading.objects.filter(actual_read_time__isnull=False).count()
            
            return {
                'organizations': total_organizations,
                'users': total_users,
                'clients': total_clients,
                'mentions': {
                    'total': total_mentions,
                    'pending': pending_mentions,
                    'scheduled': scheduled_mentions,
                    'completed_readings': completed_readings,
                },
                'timestamp': time.time(),
            }
        except Exception as e:
            logger.error(f"Error getting application metrics: {e}")
            return None


class HealthCheckView(View):
    """
    Health check endpoint for monitoring
    """
    
    @method_decorator(never_cache)
    def get(self, request):
        """
        Return health status
        """
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'checks': {}
        }
        
        # Database check
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                health_status['checks']['database'] = 'healthy'
        except Exception as e:
            health_status['checks']['database'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'unhealthy'
        
        # Cache check
        try:
            cache.set('health_check', 'ok', timeout=60)
            if cache.get('health_check') == 'ok':
                health_status['checks']['cache'] = 'healthy'
            else:
                health_status['checks']['cache'] = 'unhealthy: cache not working'
                health_status['status'] = 'unhealthy'
        except Exception as e:
            health_status['checks']['cache'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'unhealthy'
        
        # Disk space check
        try:
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            if disk_percent > 90:
                health_status['checks']['disk'] = f'warning: {disk_percent}% used'
                if health_status['status'] == 'healthy':
                    health_status['status'] = 'warning'
            else:
                health_status['checks']['disk'] = 'healthy'
        except Exception as e:
            health_status['checks']['disk'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'unhealthy'
        
        # Memory check
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            if memory_percent > 90:
                health_status['checks']['memory'] = f'warning: {memory_percent}% used'
                if health_status['status'] == 'healthy':
                    health_status['status'] = 'warning'
            else:
                health_status['checks']['memory'] = 'healthy'
        except Exception as e:
            health_status['checks']['memory'] = f'unhealthy: {str(e)}'
            health_status['status'] = 'unhealthy'
        
        # Return appropriate status code
        status_code = 200
        if health_status['status'] == 'unhealthy':
            status_code = 503
        elif health_status['status'] == 'warning':
            status_code = 200  # Still return 200 for warnings
        
        return JsonResponse(health_status, status=status_code)


@require_http_methods(["GET"])
@never_cache
def metrics_view(request):
    """
    Metrics endpoint for monitoring systems
    """
    metrics = {
        'timestamp': time.time(),
        'system': PerformanceMonitor.get_system_metrics(),
        'database': PerformanceMonitor.get_database_metrics(),
        'cache': PerformanceMonitor.get_cache_metrics(),
        'application': PerformanceMonitor.get_application_metrics(),
    }
    
    return JsonResponse(metrics)


class PerformanceMiddleware:
    """
    Middleware to track request performance
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Start timing
        start_time = time.time()
        
        # Process request
        response = self.get_response(request)
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Add performance headers
        response['X-Response-Time'] = f"{duration:.3f}s"
        
        # Log slow requests
        slow_threshold = getattr(settings, 'SLOW_REQUEST_THRESHOLD', 2.0)  # 2 seconds
        if duration > slow_threshold:
            # Safely access user ID if available
            user_id = 'anonymous'
            if hasattr(request, 'user') and hasattr(request.user, 'id'):
                user_id = request.user.id
            
            logger.warning(
                f"Slow request: {request.method} {request.path} "
                f"took {duration:.3f}s (user: {user_id})"
            )
        
        # Store metrics in cache for monitoring - only if user attribute exists and is authenticated
        if hasattr(request, 'user') and hasattr(request.user, 'is_authenticated') and request.user.is_authenticated:
            cache_key = f"perf_metrics:{request.user.id}"
            metrics = cache.get(cache_key, [])
            metrics.append({
                'path': request.path,
                'method': request.method,
                'duration': duration,
                'timestamp': time.time(),
            })
            # Keep only last 100 requests
            metrics = metrics[-100:]
            cache.set(cache_key, metrics, timeout=3600)  # 1 hour
        
        return response
