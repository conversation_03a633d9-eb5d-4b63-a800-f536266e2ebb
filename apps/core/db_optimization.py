"""
Database optimization utilities for production
"""
import logging
from django.db import connection, connections
from django.core.management.color import no_style
from django.db.models import QuerySet
from django.conf import settings
from functools import wraps
import time

logger = logging.getLogger(__name__)


class DatabaseOptimizer:
    """
    Database optimization utilities
    """
    
    @staticmethod
    def analyze_queries():
        """
        Analyze database queries for optimization opportunities
        """
        if not settings.DEBUG:
            logger.warning("Query analysis requires DEBUG=True")
            return None
        
        queries = connection.queries
        if not queries:
            return {"message": "No queries recorded"}
        
        # Analyze query patterns
        slow_queries = []
        duplicate_queries = {}
        total_time = 0
        
        for query in queries:
            time_taken = float(query['time'])
            total_time += time_taken
            
            # Identify slow queries (>100ms)
            if time_taken > 0.1:
                slow_queries.append({
                    'sql': query['sql'][:200] + '...' if len(query['sql']) > 200 else query['sql'],
                    'time': time_taken
                })
            
            # Track duplicate queries
            sql_hash = hash(query['sql'])
            if sql_hash in duplicate_queries:
                duplicate_queries[sql_hash]['count'] += 1
                duplicate_queries[sql_hash]['total_time'] += time_taken
            else:
                duplicate_queries[sql_hash] = {
                    'sql': query['sql'][:200] + '...' if len(query['sql']) > 200 else query['sql'],
                    'count': 1,
                    'total_time': time_taken
                }
        
        # Find frequently duplicated queries
        frequent_duplicates = [
            q for q in duplicate_queries.values() 
            if q['count'] > 1
        ]
        frequent_duplicates.sort(key=lambda x: x['count'], reverse=True)
        
        return {
            'total_queries': len(queries),
            'total_time': round(total_time, 3),
            'average_time': round(total_time / len(queries), 3),
            'slow_queries': slow_queries[:10],  # Top 10 slow queries
            'duplicate_queries': frequent_duplicates[:10],  # Top 10 duplicated queries
        }
    
    @staticmethod
    def get_connection_info():
        """
        Get database connection information
        """
        db_info = {}
        
        for alias in connections:
            conn = connections[alias]
            db_info[alias] = {
                'vendor': conn.vendor,
                'settings': {
                    'ENGINE': conn.settings_dict.get('ENGINE'),
                    'NAME': conn.settings_dict.get('NAME'),
                    'HOST': conn.settings_dict.get('HOST'),
                    'PORT': conn.settings_dict.get('PORT'),
                    'CONN_MAX_AGE': conn.settings_dict.get('CONN_MAX_AGE'),
                }
            }
            
            # Get connection status
            try:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    db_info[alias]['status'] = 'connected'
            except Exception as e:
                db_info[alias]['status'] = f'error: {e}'
        
        return db_info
    
    @staticmethod
    def optimize_queryset(queryset):
        """
        Apply common optimizations to a QuerySet
        """
        if not isinstance(queryset, QuerySet):
            return queryset
        
        # Apply select_related for foreign keys
        model = queryset.model
        foreign_keys = [
            field.name for field in model._meta.fields
            if field.many_to_one and not field.null
        ]
        
        if foreign_keys:
            queryset = queryset.select_related(*foreign_keys)
        
        return queryset
    
    @staticmethod
    def get_table_sizes():
        """
        Get database table sizes (PostgreSQL specific)
        """
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        schemaname,
                        tablename,
                        attname,
                        n_distinct,
                        correlation
                    FROM pg_stats
                    WHERE schemaname = 'public'
                    ORDER BY tablename, attname;
                """)
                
                stats = cursor.fetchall()
                
                cursor.execute("""
                    SELECT 
                        tablename,
                        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
                """)
                
                sizes = cursor.fetchall()
                
                return {
                    'table_sizes': [
                        {
                            'table': row[0],
                            'size': row[1],
                            'size_bytes': row[2]
                        }
                        for row in sizes
                    ],
                    'column_stats': [
                        {
                            'schema': row[0],
                            'table': row[1],
                            'column': row[2],
                            'distinct_values': row[3],
                            'correlation': row[4]
                        }
                        for row in stats
                    ]
                }
        except Exception as e:
            logger.error(f"Error getting table sizes: {e}")
            return None


def query_debugger(func):
    """
    Decorator to debug database queries for a function
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not settings.DEBUG:
            return func(*args, **kwargs)
        
        # Reset queries
        connection.queries_log.clear()
        
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        # Analyze queries
        queries = connection.queries
        query_count = len(queries)
        total_query_time = sum(float(q['time']) for q in queries)
        
        logger.debug(
            f"Function {func.__name__} executed in {end_time - start_time:.3f}s "
            f"with {query_count} queries taking {total_query_time:.3f}s total"
        )
        
        # Log slow queries
        for query in queries:
            if float(query['time']) > 0.1:  # 100ms threshold
                logger.warning(
                    f"Slow query ({query['time']}s): {query['sql'][:200]}..."
                )
        
        return result
    return wrapper


class QueryOptimizer:
    """
    Query optimization utilities
    """
    
    @staticmethod
    def bulk_create_optimized(model_class, objects, batch_size=1000):
        """
        Optimized bulk create with batching
        """
        if not objects:
            return []
        
        created_objects = []
        for i in range(0, len(objects), batch_size):
            batch = objects[i:i + batch_size]
            created_batch = model_class.objects.bulk_create(
                batch,
                batch_size=batch_size,
                ignore_conflicts=False
            )
            created_objects.extend(created_batch)
        
        return created_objects
    
    @staticmethod
    def bulk_update_optimized(objects, fields, batch_size=1000):
        """
        Optimized bulk update with batching
        """
        if not objects:
            return
        
        model_class = objects[0].__class__
        
        for i in range(0, len(objects), batch_size):
            batch = objects[i:i + batch_size]
            model_class.objects.bulk_update(
                batch,
                fields,
                batch_size=batch_size
            )
    
    @staticmethod
    def prefetch_related_optimized(queryset, *lookups):
        """
        Optimized prefetch_related with chunking for large datasets
        """
        if not lookups:
            return queryset
        
        # For large querysets, use iterator to avoid memory issues
        if queryset.count() > 10000:
            return queryset.prefetch_related(*lookups).iterator(chunk_size=2000)
        else:
            return queryset.prefetch_related(*lookups)


class ConnectionPoolManager:
    """
    Database connection pool management
    """
    
    @staticmethod
    def get_connection_stats():
        """
        Get connection pool statistics
        """
        stats = {}
        
        for alias in connections:
            conn = connections[alias]
            stats[alias] = {
                'queries_executed': len(getattr(conn, 'queries', [])),
                'connection_created': hasattr(conn, 'connection') and conn.connection is not None,
            }
            
            # Try to get PostgreSQL specific stats
            try:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT 
                            count(*) as total_connections,
                            count(*) FILTER (WHERE state = 'active') as active_connections,
                            count(*) FILTER (WHERE state = 'idle') as idle_connections
                        FROM pg_stat_activity
                        WHERE datname = current_database()
                    """)
                    
                    row = cursor.fetchone()
                    if row:
                        stats[alias].update({
                            'total_connections': row[0],
                            'active_connections': row[1],
                            'idle_connections': row[2],
                        })
            except Exception:
                pass  # Not PostgreSQL or insufficient permissions
        
        return stats
    
    @staticmethod
    def close_old_connections():
        """
        Close old database connections
        """
        for conn in connections.all():
            conn.close_if_unusable_or_obsolete()


# Context manager for query optimization
class OptimizedQuery:
    """
    Context manager for optimized database queries
    """
    
    def __init__(self, description=""):
        self.description = description
        self.start_time = None
        self.query_count_start = 0
    
    def __enter__(self):
        self.start_time = time.time()
        if settings.DEBUG:
            self.query_count_start = len(connection.queries)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = time.time()
        duration = end_time - self.start_time
        
        if settings.DEBUG:
            query_count = len(connection.queries) - self.query_count_start
            logger.debug(
                f"OptimizedQuery '{self.description}': "
                f"{duration:.3f}s, {query_count} queries"
            )
            
            # Log if performance is poor
            if duration > 1.0 or query_count > 10:
                logger.warning(
                    f"Performance concern in '{self.description}': "
                    f"{duration:.3f}s, {query_count} queries"
                )
