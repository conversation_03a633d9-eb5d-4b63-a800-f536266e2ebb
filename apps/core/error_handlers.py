"""
Custom error handlers for RadioMention application.
Provides enhanced error pages with logging and user-friendly messages.
"""

import logging
from django.shortcuts import render
from django.http import HttpResponseNotFound, HttpResponseServerError, HttpResponseForbidden, HttpResponseBadRequest
from django.template import TemplateDoesNotExist
from django.views.decorators.csrf import requires_csrf_token
from django.views.decorators.cache import never_cache
from django.conf import settings

logger = logging.getLogger(__name__)


@never_cache
def custom_404_view(request, exception=None):
    """
    Custom 404 error handler.
    Logs the error and renders a user-friendly 404 page.
    """
    # Log the 404 error with context
    logger.warning(
        f"404 Error: {request.path} not found",
        extra={
            'request': request,
            'user': request.user if hasattr(request, 'user') else None,
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
    )
    
    context = {
        'request_path': request.path,
        'request_method': request.method,
        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
    }
    
    try:
        return render(request, '404.html', context, status=404)
    except TemplateDoesNotExist:
        # Fallback if custom template doesn't exist
        return HttpResponseNotFound('<h1>Page Not Found</h1>')


@never_cache
@requires_csrf_token
def custom_500_view(request):
    """
    Custom 500 error handler.
    Logs the error and renders a user-friendly 500 page.
    """
    # Log the 500 error with context
    logger.error(
        f"500 Error: Internal server error on {request.path}",
        extra={
            'request': request,
            'user': request.user if hasattr(request, 'user') else None,
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        },
        exc_info=True
    )
    
    context = {
        'request_path': request.path,
        'request_method': request.method,
        'debug': settings.DEBUG,
    }
    
    try:
        return render(request, '500.html', context, status=500)
    except TemplateDoesNotExist:
        # Fallback if custom template doesn't exist
        return HttpResponseServerError('<h1>Internal Server Error</h1>')


@never_cache
def custom_403_view(request, exception=None):
    """
    Custom 403 error handler.
    Logs the error and renders a user-friendly 403 page.
    """
    # Log the 403 error with context
    logger.warning(
        f"403 Error: Access forbidden to {request.path}",
        extra={
            'request': request,
            'user': request.user if hasattr(request, 'user') else None,
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
    )
    
    context = {
        'request_path': request.path,
        'request_method': request.method,
        'user': request.user if hasattr(request, 'user') else None,
    }
    
    try:
        return render(request, '403.html', context, status=403)
    except TemplateDoesNotExist:
        # Fallback if custom template doesn't exist
        return HttpResponseForbidden('<h1>Access Forbidden</h1>')


@never_cache
def custom_400_view(request, exception=None):
    """
    Custom 400 error handler.
    Logs the error and renders a user-friendly 400 page.
    """
    # Log the 400 error with context
    logger.warning(
        f"400 Error: Bad request to {request.path}",
        extra={
            'request': request,
            'user': request.user if hasattr(request, 'user') else None,
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
    )
    
    context = {
        'request_path': request.path,
        'request_method': request.method,
        'user_agent': request.META.get('HTTP_USER_AGENT', ''),
    }
    
    try:
        return render(request, '400.html', context, status=400)
    except TemplateDoesNotExist:
        # Fallback if custom template doesn't exist
        return HttpResponseBadRequest('<h1>Bad Request</h1>')


@never_cache
@requires_csrf_token
def custom_csrf_failure_view(request, reason=""):
    """
    Custom CSRF failure handler.
    Logs the CSRF failure and renders a user-friendly CSRF failure page.
    """
    # Log the CSRF failure with context
    logger.warning(
        f"CSRF Error: CSRF verification failed on {request.path}",
        extra={
            'request': request,
            'user': request.user if hasattr(request, 'user') else None,
            'path': request.path,
            'method': request.method,
            'reason': reason,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
    )
    
    context = {
        'request_path': request.path,
        'request_method': request.method,
        'reason': reason,
        'user': request.user if hasattr(request, 'user') else None,
    }
    
    try:
        return render(request, 'csrf_failure.html', context, status=403)
    except TemplateDoesNotExist:
        # Fallback if custom template doesn't exist
        return HttpResponseForbidden('<h1>CSRF Verification Failed</h1>')


def health_check_view(request):
    """
    Simple health check endpoint for monitoring.
    Returns basic system status information.
    """
    from django.http import JsonResponse
    from django.db import connection
    from django.core.cache import cache
    import time
    
    start_time = time.time()
    
    # Check database connectivity
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        db_status = "healthy"
    except Exception as e:
        db_status = f"error: {str(e)}"
        logger.error(f"Database health check failed: {e}")
    
    # Check cache connectivity
    try:
        cache.set('health_check', 'test', 10)
        cache_result = cache.get('health_check')
        cache_status = "healthy" if cache_result == 'test' else "error"
    except Exception as e:
        cache_status = f"error: {str(e)}"
        logger.error(f"Cache health check failed: {e}")
    
    response_time = round((time.time() - start_time) * 1000, 2)
    
    health_data = {
        'status': 'healthy' if db_status == 'healthy' and cache_status == 'healthy' else 'degraded',
        'timestamp': time.time(),
        'response_time_ms': response_time,
        'checks': {
            'database': db_status,
            'cache': cache_status,
        },
        'version': '1.0.0',
        'environment': 'production' if not settings.DEBUG else 'development',
    }
    
    status_code = 200 if health_data['status'] == 'healthy' else 503
    return JsonResponse(health_data, status=status_code)
