"""
Core notification system for the radio mentions application.
Handles email notifications, in-app notifications, and notification preferences.
"""

from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone
from apps.organizations.models import Organization, OrganizationMembership
from apps.settings.models import UserPreferences
from apps.activity_logs.models import ActivityLog
import logging

logger = logging.getLogger(__name__)


class NotificationManager:
    """Central notification manager for all notification types"""
    
    NOTIFICATION_TYPES = {
        'show_started': 'Show Started',
        'show_ended': 'Show Ended',
        'show_missing_presenter': 'Show Missing Presenter',
        'mention_approved': 'Mention Approved',
        'mention_rejected': 'Mention Rejected',
        'mention_overdue': 'Mention Overdue',
        'approval_reminder': 'Approval Reminder',
        'deadline_reminder': 'Deadline Reminder',
        'conflict_detected': 'Conflict Detected',
        'system_alert': 'System Alert',
        'daily_summary': 'Daily Summary',
        'performance_report': 'Performance Report',
    }
    
    def __init__(self):
        self.logger = logger
    
    def send_notification(self, notification_type, recipients, context, organization=None):
        """
        Send notification to specified recipients
        
        Args:
            notification_type: Type of notification from NOTIFICATION_TYPES
            recipients: List of User objects or user IDs
            context: Dictionary with notification context data
            organization: Organization object for filtering preferences
        """
        if notification_type not in self.NOTIFICATION_TYPES:
            self.logger.error(f"Unknown notification type: {notification_type}")
            return False
        
        # Ensure recipients is a list of User objects
        if not isinstance(recipients, list):
            recipients = [recipients]
        
        user_recipients = []
        for recipient in recipients:
            if isinstance(recipient, User):
                user_recipients.append(recipient)
            elif isinstance(recipient, int):
                try:
                    user_recipients.append(User.objects.get(id=recipient))
                except User.DoesNotExist:
                    self.logger.warning(f"User with ID {recipient} not found")
                    continue
        
        success_count = 0
        for user in user_recipients:
            if self._should_send_notification(user, notification_type, organization):
                if self._send_email_notification(user, notification_type, context):
                    success_count += 1
                    self._log_notification(user, notification_type, context, organization)
        
        return success_count > 0
    
    def _should_send_notification(self, user, notification_type, organization):
        """Check if user should receive this notification type"""
        try:
            # Get user preferences
            preferences = UserPreferences.objects.get(user=user, organization=organization)
            
            # Check if email notifications are enabled
            if not preferences.email_notifications:
                return False
            
            # Check specific notification type preferences
            notification_mapping = {
                'show_started': True,  # Always send show notifications to admins
                'show_ended': True,
                'show_missing_presenter': True,
                'mention_approved': preferences.notify_mention_approved,
                'mention_rejected': preferences.notify_mention_rejected,
                'mention_overdue': preferences.notify_deadlines,
                'approval_reminder': True,  # Always send to managers/admins
                'deadline_reminder': preferences.notify_deadlines,
                'conflict_detected': preferences.notify_conflicts,
                'system_alert': True,  # Always send system alerts
                'daily_summary': True,
                'performance_report': True,
            }
            
            return notification_mapping.get(notification_type, True)
            
        except UserPreferences.DoesNotExist:
            # Default to sending notifications if no preferences set
            return True
    
    def _send_email_notification(self, user, notification_type, context):
        """Send email notification to user"""
        try:
            # Prepare email context
            email_context = {
                'user': user,
                'notification_type': self.NOTIFICATION_TYPES[notification_type],
                **context
            }
            
            # Get email template
            template_name = f'notifications/email/{notification_type}.html'
            subject_template = f'notifications/email/{notification_type}_subject.txt'
            
            try:
                subject = render_to_string(subject_template, email_context).strip()
                message = render_to_string(template_name, email_context)
            except:
                # Fallback to generic template
                subject = f"RadioMention: {self.NOTIFICATION_TYPES[notification_type]}"
                message = render_to_string('notifications/email/generic.html', email_context)
            
            # Send email
            send_mail(
                subject=subject,
                message='',  # Plain text version
                html_message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=False,
            )
            
            self.logger.info(f"Email notification sent to {user.email}: {notification_type}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send email to {user.email}: {str(e)}")
            return False
    
    def _log_notification(self, user, notification_type, context, organization):
        """Log notification in activity logs"""
        try:
            # Create a safe context for logging (remove non-serializable objects)
            safe_context = {}
            for key, value in context.items():
                if hasattr(value, '_meta'):  # Django model instance
                    safe_context[key] = str(value)
                elif isinstance(value, (str, int, float, bool, list, dict)):
                    safe_context[key] = value
                else:
                    safe_context[key] = str(value)

            ActivityLog.log_activity(
                user=None,  # System generated
                organization=organization,
                action='notification_sent',
                description=f"Notification sent to {user.username}: {self.NOTIFICATION_TYPES[notification_type]}",
                level='info',
                metadata={
                    'notification_type': notification_type,
                    'recipient': user.username,
                    'context': safe_context
                }
            )
        except Exception as e:
            self.logger.error(f"Failed to log notification: {str(e)}")


def get_role_recipients(organization, roles, exclude_users=None):
    """
    Get users with specific roles in an organization
    
    Args:
        organization: Organization object
        roles: List of role names ('owner', 'admin', 'manager', etc.)
        exclude_users: List of users to exclude
    
    Returns:
        List of User objects
    """
    if not isinstance(roles, list):
        roles = [roles]
    
    memberships = OrganizationMembership.objects.filter(
        organization=organization,
        role__in=roles,
        is_active=True
    ).select_related('user')
    
    users = [membership.user for membership in memberships]
    
    if exclude_users:
        if not isinstance(exclude_users, list):
            exclude_users = [exclude_users]
        users = [user for user in users if user not in exclude_users]
    
    return users


def send_role_notification(organization, roles, notification_type, context, exclude_users=None):
    """
    Send notification to users with specific roles
    
    Args:
        organization: Organization object
        roles: List of role names or single role name
        notification_type: Type of notification
        context: Notification context data
        exclude_users: Users to exclude from notification
    
    Returns:
        Boolean indicating success
    """
    recipients = get_role_recipients(organization, roles, exclude_users)
    
    if not recipients:
        logger.warning(f"No recipients found for roles {roles} in organization {organization.name}")
        return False
    
    notification_manager = NotificationManager()
    return notification_manager.send_notification(
        notification_type=notification_type,
        recipients=recipients,
        context=context,
        organization=organization
    )


# Convenience functions for common notification scenarios
def notify_admins(organization, notification_type, context, exclude_users=None):
    """Send notification to organization owners and admins"""
    return send_role_notification(
        organization=organization,
        roles=['owner', 'admin'],
        notification_type=notification_type,
        context=context,
        exclude_users=exclude_users
    )


def notify_managers(organization, notification_type, context, exclude_users=None):
    """Send notification to managers (and above)"""
    return send_role_notification(
        organization=organization,
        roles=['owner', 'admin', 'manager'],
        notification_type=notification_type,
        context=context,
        exclude_users=exclude_users
    )


def notify_presenters(organization, notification_type, context, exclude_users=None):
    """Send notification to presenters"""
    return send_role_notification(
        organization=organization,
        roles=['presenter'],
        notification_type=notification_type,
        context=context,
        exclude_users=exclude_users
    )


class RoleSpecificNotifications:
    """Role-specific notification handlers"""

    def __init__(self):
        self.notification_manager = NotificationManager()

    def notify_owners(self, organization, event_type, context):
        """Send notifications specifically for organization owners"""
        owner_notifications = {
            'billing_alert': self._handle_billing_alert,
            'system_critical': self._handle_system_critical,
            'organization_stats': self._handle_organization_stats,
            'security_alert': self._handle_security_alert,
            'user_management': self._handle_user_management,
        }

        if event_type in owner_notifications:
            return owner_notifications[event_type](organization, context)

        # Fallback to admin notifications
        return notify_admins(organization, event_type, context)

    def notify_admins(self, organization, event_type, context):
        """Send notifications specifically for admins"""
        admin_notifications = {
            'show_monitoring': self._handle_show_monitoring,
            'system_health': self._handle_system_health,
            'user_activity': self._handle_user_activity,
            'performance_alerts': self._handle_performance_alerts,
            'backup_status': self._handle_backup_status,
        }

        if event_type in admin_notifications:
            return admin_notifications[event_type](organization, context)

        return notify_admins(organization, event_type, context)

    def notify_managers(self, organization, event_type, context):
        """Send notifications specifically for managers"""
        manager_notifications = {
            'approval_workflow': self._handle_approval_workflow,
            'team_performance': self._handle_team_performance,
            'content_review': self._handle_content_review,
            'scheduling_issues': self._handle_scheduling_issues,
            'presenter_management': self._handle_presenter_management,
        }

        if event_type in manager_notifications:
            return manager_notifications[event_type](organization, context)

        return notify_managers(organization, event_type, context)

    def notify_editors(self, organization, event_type, context):
        """Send notifications specifically for editors"""
        editor_notifications = {
            'content_feedback': self._handle_content_feedback,
            'approval_status': self._handle_approval_status,
            'deadline_alerts': self._handle_deadline_alerts,
            'client_updates': self._handle_client_updates,
        }

        if event_type in editor_notifications:
            return editor_notifications[event_type](organization, context)

        return send_role_notification(organization, ['editor'], event_type, context)

    def notify_presenters(self, organization, event_type, context):
        """Send notifications specifically for presenters"""
        presenter_notifications = {
            'show_reminders': self._handle_show_reminders,
            'mention_updates': self._handle_mention_updates,
            'schedule_changes': self._handle_schedule_changes,
            'performance_feedback': self._handle_performance_feedback,
        }

        if event_type in presenter_notifications:
            return presenter_notifications[event_type](organization, context)

        return notify_presenters(organization, event_type, context)

    # Owner-specific handlers
    def _handle_billing_alert(self, organization, context):
        """Handle billing-related alerts for owners"""
        return send_role_notification(
            organization=organization,
            roles=['owner'],
            notification_type='billing_alert',
            context=context
        )

    def _handle_system_critical(self, organization, context):
        """Handle critical system alerts for owners"""
        return send_role_notification(
            organization=organization,
            roles=['owner'],
            notification_type='system_critical',
            context=context
        )

    def _handle_organization_stats(self, organization, context):
        """Handle organization statistics for owners"""
        return send_role_notification(
            organization=organization,
            roles=['owner'],
            notification_type='organization_stats',
            context=context
        )

    def _handle_security_alert(self, organization, context):
        """Handle security alerts for owners"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin'],
            notification_type='security_alert',
            context=context
        )

    def _handle_user_management(self, organization, context):
        """Handle user management notifications for owners"""
        return send_role_notification(
            organization=organization,
            roles=['owner'],
            notification_type='user_management',
            context=context
        )

    # Admin-specific handlers
    def _handle_show_monitoring(self, organization, context):
        """Handle show monitoring alerts for admins"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin'],
            notification_type='show_monitoring',
            context=context
        )

    def _handle_system_health(self, organization, context):
        """Handle system health notifications for admins"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin'],
            notification_type='system_health',
            context=context
        )

    def _handle_user_activity(self, organization, context):
        """Handle user activity notifications for admins"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin'],
            notification_type='user_activity',
            context=context
        )

    def _handle_performance_alerts(self, organization, context):
        """Handle performance alerts for admins"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin'],
            notification_type='performance_alerts',
            context=context
        )

    def _handle_backup_status(self, organization, context):
        """Handle backup status notifications for admins"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin'],
            notification_type='backup_status',
            context=context
        )

    # Manager-specific handlers
    def _handle_approval_workflow(self, organization, context):
        """Handle approval workflow notifications for managers"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin', 'manager'],
            notification_type='approval_workflow',
            context=context
        )

    def _handle_team_performance(self, organization, context):
        """Handle team performance notifications for managers"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin', 'manager'],
            notification_type='team_performance',
            context=context
        )

    def _handle_content_review(self, organization, context):
        """Handle content review notifications for managers"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin', 'manager'],
            notification_type='content_review',
            context=context
        )

    def _handle_scheduling_issues(self, organization, context):
        """Handle scheduling issue notifications for managers"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin', 'manager'],
            notification_type='scheduling_issues',
            context=context
        )

    def _handle_presenter_management(self, organization, context):
        """Handle presenter management notifications for managers"""
        return send_role_notification(
            organization=organization,
            roles=['owner', 'admin', 'manager'],
            notification_type='presenter_management',
            context=context
        )

    # Editor-specific handlers
    def _handle_content_feedback(self, organization, context):
        """Handle content feedback notifications for editors"""
        return send_role_notification(
            organization=organization,
            roles=['editor'],
            notification_type='content_feedback',
            context=context
        )

    def _handle_approval_status(self, organization, context):
        """Handle approval status notifications for editors"""
        return send_role_notification(
            organization=organization,
            roles=['editor'],
            notification_type='approval_status',
            context=context
        )

    def _handle_deadline_alerts(self, organization, context):
        """Handle deadline alert notifications for editors"""
        return send_role_notification(
            organization=organization,
            roles=['editor'],
            notification_type='deadline_alerts',
            context=context
        )

    def _handle_client_updates(self, organization, context):
        """Handle client update notifications for editors"""
        return send_role_notification(
            organization=organization,
            roles=['editor'],
            notification_type='client_updates',
            context=context
        )

    # Presenter-specific handlers
    def _handle_show_reminders(self, organization, context):
        """Handle show reminder notifications for presenters"""
        return send_role_notification(
            organization=organization,
            roles=['presenter'],
            notification_type='show_reminders',
            context=context
        )

    def _handle_mention_updates(self, organization, context):
        """Handle mention update notifications for presenters"""
        return send_role_notification(
            organization=organization,
            roles=['presenter'],
            notification_type='mention_updates',
            context=context
        )

    def _handle_schedule_changes(self, organization, context):
        """Handle schedule change notifications for presenters"""
        return send_role_notification(
            organization=organization,
            roles=['presenter'],
            notification_type='schedule_changes',
            context=context
        )

    def _handle_performance_feedback(self, organization, context):
        """Handle performance feedback notifications for presenters"""
        return send_role_notification(
            organization=organization,
            roles=['presenter'],
            notification_type='performance_feedback',
            context=context
        )


# Global instance for easy access
role_notifications = RoleSpecificNotifications()
