"""
Context processors for the core app.
These functions add variables to all template contexts.
"""

def sidebar_context(request):
    """
    Add sidebar-related context variables to all templates.
    """
    context = {}

    # Check if user attribute exists and is authenticated
    if hasattr(request, 'user') and request.user.is_authenticated:
        from apps.mentions.models import Mention
        from apps.organizations.middleware import get_current_organization

        # Get current organization
        current_org = get_current_organization(request)

        # Get pending approvals count for sidebar notification (filtered by organization)
        if current_org:
            pending_approvals_count = Mention.objects.filter(
                status='pending',
                client__organization=current_org
            ).count()
        else:
            # Fallback for users without organization context
            pending_approvals_count = Mention.objects.filter(status='pending').count()

        # Only show count if there are pending approvals
        if pending_approvals_count > 0:
            context['pending_approvals_count'] = pending_approvals_count

    return context


def organization_context(request):
    """Add organization context to all templates"""
    context = {}

    if hasattr(request, 'user') and request.user.is_authenticated:
        from apps.organizations.models import OrganizationMembership

        # Get user's organizations
        memberships = OrganizationMembership.objects.filter(
            user=request.user,
            is_active=True
        ).select_related('organization', 'branch')

        # Get current organization from session or default
        current_org_id = request.session.get('current_organization_id')
        current_membership = None

        if current_org_id:
            current_membership = memberships.filter(
                organization_id=current_org_id
            ).first()

        # If no current org or invalid, use default or first available
        if not current_membership:
            current_membership = memberships.filter(is_default=True).first()
            if not current_membership:
                current_membership = memberships.first()

        # Update session with current organization
        if current_membership:
            request.session['current_organization_id'] = current_membership.organization.id
            context['current_organization'] = current_membership.organization
            context['current_membership'] = current_membership
            context['current_branch'] = current_membership.branch

        context['user_organizations'] = [m.organization for m in memberships]
        context['user_memberships'] = memberships

    return context


def permissions_context(request):
    """Add user permissions context to all templates"""
    context = {}

    if hasattr(request, 'user') and request.user.is_authenticated:
        from apps.organizations.middleware import (
            get_current_membership,
            get_user_permissions,
            user_has_permission
        )

        # Get current membership and permissions
        current_membership = get_current_membership(request)
        if current_membership:
            user_permissions = get_user_permissions(request)
            context['user_permissions'] = user_permissions

            # Add permission checking function to templates
            context['has_permission'] = lambda perm: user_has_permission(request, perm)

            # Add common permission flags for easy template access
            context['can_manage_users'] = user_has_permission(request, 'manage_users')
            context['can_manage_mentions'] = user_has_permission(request, 'manage_mentions')
            context['can_manage_shows'] = user_has_permission(request, 'manage_shows')
            context['can_manage_clients'] = user_has_permission(request, 'manage_clients')
            context['can_manage_presenters'] = user_has_permission(request, 'manage_presenters')
            context['can_view_reports'] = user_has_permission(request, 'view_reports')
            context['can_approve_mentions'] = user_has_permission(request, 'approve_mentions')
            context['can_view_presenter_dashboard'] = user_has_permission(request, 'view_presenter_dashboard')
            context['can_manage_settings'] = user_has_permission(request, 'manage_settings')
            context['can_view_analytics'] = user_has_permission(request, 'view_analytics')

    return context
