"""
Management command to set up default notification preferences for all users.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from apps.organizations.models import OrganizationMembership
from apps.settings.models import UserPreferences


class Command(BaseCommand):
    help = 'Set up default notification preferences for all users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update existing preferences',
        )

    def handle(self, *args, **options):
        force_update = options.get('force')

        self.stdout.write(
            self.style.SUCCESS('Setting up user notification preferences...')
        )

        # Get all active organization memberships
        memberships = OrganizationMembership.objects.filter(is_active=True)
        
        created_count = 0
        updated_count = 0
        skipped_count = 0

        for membership in memberships:
            user = membership.user
            organization = membership.organization
            role = membership.role

            # Check if preferences already exist
            preferences, created = UserPreferences.objects.get_or_create(
                user=user,
                organization=organization,
                defaults=self._get_default_preferences(role)
            )

            if created:
                created_count += 1
                self.stdout.write(f'  ✓ Created preferences for {user.username} in {organization.name}')
            elif force_update:
                # Update existing preferences
                for key, value in self._get_default_preferences(role).items():
                    setattr(preferences, key, value)
                preferences.save()
                updated_count += 1
                self.stdout.write(f'  ↻ Updated preferences for {user.username} in {organization.name}')
            else:
                skipped_count += 1

        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS(f'✓ Setup completed:')
        )
        self.stdout.write(f'  - Created: {created_count} preferences')
        self.stdout.write(f'  - Updated: {updated_count} preferences')
        self.stdout.write(f'  - Skipped: {skipped_count} preferences')

        if skipped_count > 0 and not force_update:
            self.stdout.write(
                self.style.WARNING(f'Use --force to update existing preferences')
            )

    def _get_default_preferences(self, role):
        """Get default notification preferences based on user role"""

        # Base preferences for all users (using actual model fields)
        base_preferences = {
            'email_notifications': True,
            'browser_notifications': True,
            'mobile_notifications': True,
            'notify_mention_assigned': True,
            'notify_mention_approved': True,
            'notify_mention_rejected': True,
            'notify_conflicts': True,
            'notify_deadlines': True,
            'notify_schedule_changes': True,
            'default_dashboard_view': 'calendar',
            'calendar_default_view': 'week',
            'items_per_page': 20,
            'show_completed_mentions': False,
            'calendar_start_hour': 6,
            'calendar_end_hour': 24,
            'theme': 'light',
            'sidebar_collapsed': False,
        }

        # Role-specific preferences
        role_preferences = {
            'owner': {
                **base_preferences,
                # Owners get all notifications
                'notify_mention_assigned': True,
                'notify_conflicts': True,
                'notify_deadlines': True,
                'show_completed_mentions': True,  # Owners might want to see completed items
            },
            'admin': {
                **base_preferences,
                # Admins get most notifications
                'notify_mention_assigned': True,
                'notify_conflicts': True,
                'notify_deadlines': True,
                'show_completed_mentions': True,
            },
            'manager': {
                **base_preferences,
                # Managers get workflow-related notifications
                'notify_mention_assigned': True,
                'notify_conflicts': True,
                'notify_deadlines': True,
                'show_completed_mentions': False,
            },
            'editor': {
                **base_preferences,
                # Editors get content-related notifications
                'notify_mention_assigned': True,
                'notify_mention_approved': True,
                'notify_mention_rejected': True,
                'notify_conflicts': True,
                'notify_deadlines': True,
            },
            'presenter': {
                **base_preferences,
                # Presenters get fewer notifications, focused on their work
                'notify_mention_assigned': True,
                'notify_schedule_changes': True,
                'notify_conflicts': False,  # Less relevant for presenters
                'notify_deadlines': False,  # Less relevant for presenters
                'default_dashboard_view': 'list',  # Might prefer list view
                'show_completed_mentions': False,
            },
            'viewer': {
                **base_preferences,
                # Viewers get minimal notifications
                'notify_mention_assigned': False,
                'notify_conflicts': False,
                'notify_deadlines': False,
                'notify_schedule_changes': False,
                'browser_notifications': False,
                'mobile_notifications': False,
            }
        }

        return role_preferences.get(role, base_preferences)
