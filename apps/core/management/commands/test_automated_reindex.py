"""
Management command to test automated database reindexing
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.activity_logs.tasks import automated_maintenance_reindex
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Test automated database reindexing task'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force execution even outside maintenance window',
        )
        parser.add_argument(
            '--async',
            action='store_true',
            help='Run task asynchronously (requires celery worker)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would happen without executing',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Testing automated database reindexing...')
        )
        
        current_time = timezone.now()
        current_hour = current_time.hour
        
        # Show current status
        self.stdout.write(f"Current time: {current_time}")
        self.stdout.write(f"Current hour: {current_hour}")
        self.stdout.write(f"Maintenance window: 2-6 AM")
        
        if 2 <= current_hour <= 6:
            self.stdout.write(
                self.style.SUCCESS("✅ Currently in maintenance window")
            )
        else:
            self.stdout.write(
                self.style.WARNING("⚠️  Currently outside maintenance window")
            )
            if not options['force']:
                self.stdout.write(
                    self.style.ERROR("Use --force to run outside maintenance window")
                )
        
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING("DRY RUN: Would execute automated_maintenance_reindex task")
            )
            return
        
        if not options['force'] and not (2 <= current_hour <= 6):
            self.stdout.write(
                self.style.ERROR("Aborting: Outside maintenance window and --force not specified")
            )
            return
        
        try:
            if options['async']:
                # Run asynchronously
                self.stdout.write("Running task asynchronously...")
                result = automated_maintenance_reindex.delay()
                self.stdout.write(f"Task ID: {result.id}")
                self.stdout.write("Check task status with: celery -A radio_mentions_project inspect active")
            else:
                # Run synchronously
                self.stdout.write("Running task synchronously...")
                result = automated_maintenance_reindex()
                self.stdout.write(
                    self.style.SUCCESS(f"Task completed: {result}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Task failed: {str(e)}")
            )
            logger.error(f"Automated reindex test failed: {str(e)}")
            raise
        
        self.stdout.write(
            self.style.SUCCESS('Automated reindex test completed')
        )
