"""
Management command for system health monitoring and alerting
"""
import time
import logging
from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from django.db import connection
import psutil

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Monitor system health and send alerts'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--check-only',
            action='store_true',
            help='Run a one-time health check instead of continuous monitoring'
        )
        parser.add_argument(
            '--alert-email',
            type=str,
            help='Email address for alerts'
        )
        parser.add_argument(
            '--cpu-threshold',
            type=float,
            default=80.0,
            help='CPU usage threshold for alerts (default: 80%%)'
        )
        parser.add_argument(
            '--memory-threshold',
            type=float,
            default=85.0,
            help='Memory usage threshold for alerts (default: 85%%)'
        )
        parser.add_argument(
            '--disk-threshold',
            type=float,
            default=90.0,
            help='Disk usage threshold for alerts (default: 90%%)'
        )

    def handle(self, *args, **options):
        if options['check_only']:
            self._run_health_check()
            return
            
        alert_email = options['alert_email']
        cpu_threshold = options['cpu_threshold']
        memory_threshold = options['memory_threshold']
        disk_threshold = options['disk_threshold']
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Starting health monitoring '
                f'(CPU: {cpu_threshold}%, Memory: {memory_threshold}%, Disk: {disk_threshold}%)'
            )
        )
        
        # Run health check
        health_status = self._get_health_status()
        
        # Check for alerts
        alerts = self._check_alerts(
            health_status,
            cpu_threshold,
            memory_threshold,
            disk_threshold
        )
        
        # Display status
        self._display_health_status(health_status)
        
        # Send alerts if needed
        if alerts and alert_email:
            self._send_alerts(alerts, alert_email)
            self.stdout.write(
                self.style.WARNING(f'Alerts sent to {alert_email}')
            )

    def _run_health_check(self):
        """Run a one-time health check"""
        self.stdout.write(self.style.SUCCESS('Starting system health check...'))
        
        # Check database connectivity
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                self.stdout.write(self.style.SUCCESS('✓ Database connection: OK'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Database connection: FAILED - {e}'))
        
        # Check cache connectivity
        try:
            cache.set('health_check', 'test', timeout=60)
            if cache.get('health_check') == 'test':
                self.stdout.write(self.style.SUCCESS('✓ Cache connection: OK'))
            else:
                self.stdout.write(self.style.ERROR('✗ Cache connection: FAILED'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Cache connection: FAILED - {e}'))
        
        # Check system resources
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent < 80:
                self.stdout.write(self.style.SUCCESS(f'✓ CPU usage: {cpu_percent}%'))
            else:
                self.stdout.write(self.style.WARNING(f'⚠ CPU usage: {cpu_percent}% (HIGH)'))
            
            # Memory usage
            memory = psutil.virtual_memory()
            if memory.percent < 85:
                self.stdout.write(self.style.SUCCESS(f'✓ Memory usage: {memory.percent}%'))
            else:
                self.stdout.write(self.style.WARNING(f'⚠ Memory usage: {memory.percent}% (HIGH)'))
            
            # Disk usage
            disk = psutil.disk_usage('/')
            if disk.percent < 90:
                self.stdout.write(self.style.SUCCESS(f'✓ Disk usage: {disk.percent}%'))
            else:
                self.stdout.write(self.style.WARNING(f'⚠ Disk usage: {disk.percent}% (HIGH)'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ System resources check: FAILED - {e}'))
        
        self.stdout.write(self.style.SUCCESS('System health check completed.'))

    def _get_health_status(self):
        """Get comprehensive health status"""
        health_status = {
            'timestamp': time.time(),
            'database': self._check_database(),
            'cache': self._check_cache(),
            'system': self._check_system_resources(),
            'overall': 'healthy'
        }
        
        # Determine overall status
        if any(status == 'unhealthy' for status in [
            health_status['database']['status'],
            health_status['cache']['status'],
            health_status['system']['status']
        ]):
            health_status['overall'] = 'unhealthy'
        elif any(status == 'warning' for status in [
            health_status['database']['status'],
            health_status['cache']['status'],
            health_status['system']['status']
        ]):
            health_status['overall'] = 'warning'
        
        return health_status

    def _check_database(self):
        """Check database health"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return {'status': 'healthy', 'message': 'Database connection OK'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Database error: {e}'}

    def _check_cache(self):
        """Check cache health"""
        try:
            cache.set('health_check', 'test', timeout=60)
            if cache.get('health_check') == 'test':
                return {'status': 'healthy', 'message': 'Cache working properly'}
            else:
                return {'status': 'unhealthy', 'message': 'Cache not responding'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Cache error: {e}'}

    def _check_system_resources(self):
        """Check system resource usage"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            status = 'healthy'
            messages = []
            
            if cpu_percent > 90:
                status = 'unhealthy'
                messages.append(f'Critical CPU usage: {cpu_percent}%')
            elif cpu_percent > 80:
                status = 'warning'
                messages.append(f'High CPU usage: {cpu_percent}%')
            
            if memory.percent > 95:
                status = 'unhealthy'
                messages.append(f'Critical memory usage: {memory.percent}%')
            elif memory.percent > 85:
                if status == 'healthy':
                    status = 'warning'
                messages.append(f'High memory usage: {memory.percent}%')
            
            if disk.percent > 95:
                status = 'unhealthy'
                messages.append(f'Critical disk usage: {disk.percent}%')
            elif disk.percent > 90:
                if status == 'healthy':
                    status = 'warning'
                messages.append(f'High disk usage: {disk.percent}%')
            
            if not messages:
                messages.append('System resources within normal limits')
            
            return {
                'status': status,
                'message': '; '.join(messages),
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': disk.percent
            }
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'System check error: {e}'}

    def _check_alerts(self, health_status, cpu_threshold, memory_threshold, disk_threshold):
        """Check if any alerts should be triggered"""
        alerts = []
        
        system = health_status.get('system', {})
        
        if system.get('cpu_percent', 0) > cpu_threshold:
            alerts.append({
                'type': 'cpu',
                'message': f'High CPU usage: {system["cpu_percent"]:.1f}% (threshold: {cpu_threshold}%)',
                'severity': 'critical' if system['cpu_percent'] > 90 else 'warning'
            })
        
        if system.get('memory_percent', 0) > memory_threshold:
            alerts.append({
                'type': 'memory',
                'message': f'High memory usage: {system["memory_percent"]:.1f}% (threshold: {memory_threshold}%)',
                'severity': 'critical' if system['memory_percent'] > 95 else 'warning'
            })
        
        if system.get('disk_percent', 0) > disk_threshold:
            alerts.append({
                'type': 'disk',
                'message': f'High disk usage: {system["disk_percent"]:.1f}% (threshold: {disk_threshold}%)',
                'severity': 'critical' if system['disk_percent'] > 95 else 'warning'
            })
        
        if health_status['database']['status'] == 'unhealthy':
            alerts.append({
                'type': 'database',
                'message': health_status['database']['message'],
                'severity': 'critical'
            })
        
        if health_status['cache']['status'] == 'unhealthy':
            alerts.append({
                'type': 'cache',
                'message': health_status['cache']['message'],
                'severity': 'warning'
            })
        
        return alerts

    def _display_health_status(self, health_status):
        """Display health status"""
        overall = health_status['overall']
        
        if overall == 'healthy':
            style = self.style.SUCCESS
            icon = '✓'
        elif overall == 'warning':
            style = self.style.WARNING
            icon = '⚠'
        else:
            style = self.style.ERROR
            icon = '✗'
        
        self.stdout.write(style(f'{icon} Overall System Health: {overall.upper()}'))
        self.stdout.write(f'Database: {health_status["database"]["message"]}')
        self.stdout.write(f'Cache: {health_status["cache"]["message"]}')
        self.stdout.write(f'System: {health_status["system"]["message"]}')

    def _send_alerts(self, alerts, alert_email):
        """Send alert emails"""
        if not alerts:
            return
        
        critical_alerts = [a for a in alerts if a['severity'] == 'critical']
        warning_alerts = [a for a in alerts if a['severity'] == 'warning']
        
        subject = '[RadioMention] System Health Alert'
        if critical_alerts:
            subject += ' - CRITICAL'
        
        message_parts = ['System Health Alert\n', '=' * 50, '\n']
        
        if critical_alerts:
            message_parts.extend([
                'CRITICAL ALERTS:\n',
                '\n'.join(f'• {alert["message"]}' for alert in critical_alerts),
                '\n\n'
            ])
        
        if warning_alerts:
            message_parts.extend([
                'WARNING ALERTS:\n',
                '\n'.join(f'• {alert["message"]}' for alert in warning_alerts),
                '\n\n'
            ])
        
        message_parts.extend([
            f'Timestamp: {timezone.now()}',
            '\nPlease check the system immediately.',
            '\nThis is an automated alert from the RadioMention monitoring system.'
        ])
        
        message = ''.join(message_parts)
        
        try:
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[alert_email],
                fail_silently=False,
            )
        except Exception as e:
            logger.error(f'Error sending alert email: {e}')
            self.stdout.write(self.style.ERROR(f'Failed to send alert: {e}'))
