"""
Management command to sync presenter profiles for existing users with presenter role
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from apps.organizations.models import OrganizationMembership
from apps.core.models import Presenter


class Command(BaseCommand):
    help = 'Create presenter profiles for existing users with presenter role who don\'t have one'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be created without actually creating anything',
        )
        parser.add_argument(
            '--organization',
            type=str,
            help='Only process users from specific organization (by slug)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        organization_slug = options.get('organization')

        self.stdout.write(
            self.style.SUCCESS('Starting presenter profile sync...')
        )

        # Get all users with presenter role
        presenter_memberships = OrganizationMembership.objects.filter(
            role='presenter',
            is_active=True
        ).select_related('user', 'organization')

        if organization_slug:
            presenter_memberships = presenter_memberships.filter(
                organization__slug=organization_slug
            )

        created_count = 0
        updated_count = 0
        skipped_count = 0

        for membership in presenter_memberships:
            user = membership.user
            organization = membership.organization

            try:
                # Check if presenter profile already exists
                presenter = Presenter.objects.get(
                    user=user,
                    organization=organization
                )
                
                # If presenter exists but is inactive, reactivate it
                if not presenter.is_active:
                    if not dry_run:
                        presenter.is_active = True
                        presenter.save()
                        updated_count += 1
                        self.stdout.write(
                            f'  ✓ Reactivated presenter profile for {user.username} in {organization.name}'
                        )
                    else:
                        self.stdout.write(
                            f'  [DRY RUN] Would reactivate presenter profile for {user.username} in {organization.name}'
                        )
                else:
                    skipped_count += 1
                    self.stdout.write(
                        f'  - Presenter profile already exists for {user.username} in {organization.name}'
                    )

            except Presenter.DoesNotExist:
                # Create new presenter profile
                if not dry_run:
                    presenter = Presenter.objects.create(
                        user=user,
                        organization=organization,
                        first_name=user.first_name or user.username,
                        last_name=user.last_name or '',
                        email=user.email,
                        is_active=True
                    )
                    created_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'  ✓ Created presenter profile for {user.username} in {organization.name}'
                        )
                    )
                else:
                    self.stdout.write(
                        f'  [DRY RUN] Would create presenter profile for {user.username} in {organization.name}'
                    )

        # Summary
        self.stdout.write('\n' + '='*50)
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN SUMMARY:'))
            self.stdout.write(f'Would create: {created_count} presenter profiles')
            self.stdout.write(f'Would reactivate: {updated_count} presenter profiles')
        else:
            self.stdout.write(self.style.SUCCESS('SYNC COMPLETE:'))
            self.stdout.write(f'Created: {created_count} presenter profiles')
            self.stdout.write(f'Reactivated: {updated_count} presenter profiles')
        
        self.stdout.write(f'Skipped (already exists): {skipped_count} presenter profiles')
        self.stdout.write(f'Total processed: {created_count + updated_count + skipped_count} memberships')

        if dry_run:
            self.stdout.write('\nRun without --dry-run to actually create the profiles.')
