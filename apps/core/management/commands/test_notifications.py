"""
Management command to test the notification system.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.organizations.models import Organization
from apps.core.notifications import NotificationManager, notify_admins, notify_managers
from apps.core.models import Presenter
from apps.shows.models import Show
from apps.mentions.models import Mention
from datetime import datetime, timedelta


class Command(BaseCommand):
    help = 'Test the notification system with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--organization',
            type=str,
            help='Organization slug to test with',
        )
        parser.add_argument(
            '--notification-type',
            type=str,
            help='Specific notification type to test',
            choices=[
                'show_missing_presenter',
                'approval_reminder',
                'daily_summary',
                'show_reminder',
                'system_alert',
                'all'
            ],
            default='all'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending',
        )

    def handle(self, *args, **options):
        organization_slug = options.get('organization')
        notification_type = options.get('notification_type')
        dry_run = options.get('dry_run')

        if organization_slug:
            try:
                organization = Organization.objects.get(slug=organization_slug)
            except Organization.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Organization "{organization_slug}" not found')
                )
                return
        else:
            organization = Organization.objects.filter(is_active=True).first()
            if not organization:
                self.stdout.write(
                    self.style.ERROR('No active organizations found')
                )
                return

        self.stdout.write(
            self.style.SUCCESS(f'Testing notifications for organization: {organization.name}')
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No notifications will be sent')
            )

        notification_manager = NotificationManager()

        if notification_type == 'all':
            self._test_all_notifications(organization, notification_manager, dry_run)
        else:
            self._test_specific_notification(organization, notification_manager, notification_type, dry_run)

        self.stdout.write(
            self.style.SUCCESS('Notification testing completed')
        )

    def _test_all_notifications(self, organization, notification_manager, dry_run):
        """Test all notification types"""
        test_methods = [
            ('show_missing_presenter', self._test_show_missing_presenter),
            ('approval_reminder', self._test_approval_reminder),
            ('daily_summary', self._test_daily_summary),
            ('show_reminder', self._test_show_reminder),
            ('system_alert', self._test_system_alert),
        ]

        for notification_type, test_method in test_methods:
            self.stdout.write(f'\nTesting {notification_type}...')
            try:
                test_method(organization, notification_manager, dry_run)
                self.stdout.write(
                    self.style.SUCCESS(f'✓ {notification_type} test completed')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ {notification_type} test failed: {str(e)}')
                )

    def _test_specific_notification(self, organization, notification_manager, notification_type, dry_run):
        """Test a specific notification type"""
        test_methods = {
            'show_missing_presenter': self._test_show_missing_presenter,
            'approval_reminder': self._test_approval_reminder,
            'daily_summary': self._test_daily_summary,
            'show_reminder': self._test_show_reminder,
            'system_alert': self._test_system_alert,
        }

        if notification_type in test_methods:
            test_methods[notification_type](organization, notification_manager, dry_run)
        else:
            self.stdout.write(
                self.style.ERROR(f'Unknown notification type: {notification_type}')
            )

    def _test_show_missing_presenter(self, organization, notification_manager, dry_run):
        """Test show missing presenter notification"""
        # Get or create a test show
        show = Show.objects.filter(organization=organization).first()
        if not show:
            self.stdout.write(
                self.style.WARNING('No shows found for testing')
            )
            return

        context = {
            'show': show,
            'issue': 'No active session (TEST)',
            'time': timezone.now()
        }

        if not dry_run:
            notify_admins(
                organization=organization,
                notification_type='show_missing_presenter',
                context=context
            )

        self.stdout.write(f'  - Show: {show.name}')
        self.stdout.write(f'  - Issue: {context["issue"]}')

    def _test_approval_reminder(self, organization, notification_manager, dry_run):
        """Test approval reminder notification"""
        # Get pending mentions or create test data
        pending_mentions = Mention.objects.filter(
            client__organization=organization,
            status='pending'
        )[:3]

        if not pending_mentions.exists():
            self.stdout.write(
                self.style.WARNING('No pending mentions found for testing')
            )
            return

        context = {
            'mentions': pending_mentions,
            'count': pending_mentions.count(),
            'urgency': 'normal',
            'threshold_hours': 2,
            'organization': organization
        }

        if not dry_run:
            notify_managers(
                organization=organization,
                notification_type='approval_reminder',
                context=context
            )

        self.stdout.write(f'  - Pending mentions: {context["count"]}')
        self.stdout.write(f'  - Urgency: {context["urgency"]}')

    def _test_daily_summary(self, organization, notification_manager, dry_run):
        """Test daily summary notification"""
        yesterday = timezone.now().date() - timedelta(days=1)
        
        # Generate test statistics
        stats = {
            'shows_scheduled': 5,
            'mentions_completed': 23,
            'mentions_missed': 2,
            'active_presenters': 3,
        }

        context = {
            'date': yesterday,
            'stats': stats,
            'organization': organization
        }

        if not dry_run:
            notify_managers(
                organization=organization,
                notification_type='daily_summary',
                context=context
            )

        self.stdout.write(f'  - Date: {yesterday}')
        self.stdout.write(f'  - Shows scheduled: {stats["shows_scheduled"]}')
        self.stdout.write(f'  - Mentions completed: {stats["mentions_completed"]}')

    def _test_show_reminder(self, organization, notification_manager, dry_run):
        """Test show reminder notification"""
        # Get a presenter and show
        presenter = Presenter.objects.filter(organization=organization).first()
        show = Show.objects.filter(organization=organization).first()

        if not presenter or not show:
            self.stdout.write(
                self.style.WARNING('No presenter or show found for testing')
            )
            return

        context = {
            'show': show,
            'presenter': presenter,
            'start_time': show.start_time,
            'reminder_time': timezone.now() + timedelta(minutes=30)
        }

        if not dry_run:
            notification_manager.send_notification(
                notification_type='show_reminder',
                recipients=[presenter.user],
                context=context,
                organization=organization
            )

        self.stdout.write(f'  - Presenter: {presenter.display_name}')
        self.stdout.write(f'  - Show: {show.name}')
        self.stdout.write(f'  - Start time: {show.start_time}')

    def _test_system_alert(self, organization, notification_manager, dry_run):
        """Test system alert notification"""
        context = {
            'issues': [
                'Test system alert - Database connection slow',
                'Test system alert - High memory usage detected'
            ],
            'check_time': timezone.now(),
            'severity': 'warning'
        }

        if not dry_run:
            notify_admins(
                organization=organization,
                notification_type='system_alert',
                context=context
            )

        self.stdout.write(f'  - Issues: {len(context["issues"])}')
        self.stdout.write(f'  - Severity: {context["severity"]}')
        for issue in context['issues']:
            self.stdout.write(f'    • {issue}')
