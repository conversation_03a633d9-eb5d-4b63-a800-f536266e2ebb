"""
Management command to test background tasks.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.organizations.models import Organization
from apps.core.tasks import (
    monitor_show_status, check_show_sessions, track_presenter_activity,
    system_health_check, send_daily_summaries
)
from apps.mentions.tasks import (
    check_pending_approvals, send_deadline_reminders, check_overdue_mentions,
    detect_scheduling_conflicts
)
from apps.reports.tasks import (
    generate_daily_reports, generate_weekly_analytics
)
from apps.activity_logs.tasks import (
    cleanup_old_logs, monitor_database_health
)


class Command(BaseCommand):
    help = 'Test background tasks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--task',
            type=str,
            help='Specific task to test',
            choices=[
                'monitor_show_status',
                'check_show_sessions',
                'track_presenter_activity',
                'check_pending_approvals',
                'send_deadline_reminders',
                'check_overdue_mentions',
                'detect_scheduling_conflicts',
                'system_health_check',
                'generate_daily_reports',
                'cleanup_old_logs',
                'monitor_database_health',
                'all'
            ],
            default='all'
        )
        parser.add_argument(
            '--async',
            action='store_true',
            help='Run tasks asynchronously (requires celery worker)',
        )

    def handle(self, *args, **options):
        task_name = options.get('task')
        run_async = options.get('async')

        self.stdout.write(
            self.style.SUCCESS('Testing background tasks...')
        )

        if run_async:
            self.stdout.write(
                self.style.WARNING('Running tasks asynchronously - check celery worker logs for results')
            )
        else:
            self.stdout.write(
                self.style.WARNING('Running tasks synchronously - this may take a while')
            )

        if task_name == 'all':
            self._test_all_tasks(run_async)
        else:
            self._test_specific_task(task_name, run_async)

        self.stdout.write(
            self.style.SUCCESS('Task testing completed')
        )

    def _test_all_tasks(self, run_async):
        """Test all background tasks"""
        tasks = [
            ('monitor_show_status', monitor_show_status),
            ('check_show_sessions', check_show_sessions),
            ('track_presenter_activity', track_presenter_activity),
            ('check_pending_approvals', check_pending_approvals),
            ('send_deadline_reminders', send_deadline_reminders),
            ('check_overdue_mentions', check_overdue_mentions),
            ('detect_scheduling_conflicts', detect_scheduling_conflicts),
            ('system_health_check', system_health_check),
            ('generate_daily_reports', generate_daily_reports),
            ('cleanup_old_logs', cleanup_old_logs),
            ('monitor_database_health', monitor_database_health),
        ]

        for task_name, task_func in tasks:
            self.stdout.write(f'\nTesting {task_name}...')
            try:
                result = self._run_task(task_func, run_async)
                self.stdout.write(
                    self.style.SUCCESS(f'✓ {task_name}: {result}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ {task_name} failed: {str(e)}')
                )

    def _test_specific_task(self, task_name, run_async):
        """Test a specific task"""
        task_map = {
            'monitor_show_status': monitor_show_status,
            'check_show_sessions': check_show_sessions,
            'track_presenter_activity': track_presenter_activity,
            'check_pending_approvals': check_pending_approvals,
            'send_deadline_reminders': send_deadline_reminders,
            'check_overdue_mentions': check_overdue_mentions,
            'detect_scheduling_conflicts': detect_scheduling_conflicts,
            'system_health_check': system_health_check,
            'generate_daily_reports': generate_daily_reports,
            'cleanup_old_logs': cleanup_old_logs,
            'monitor_database_health': monitor_database_health,
        }

        if task_name in task_map:
            task_func = task_map[task_name]
            self.stdout.write(f'Testing {task_name}...')
            try:
                result = self._run_task(task_func, run_async)
                self.stdout.write(
                    self.style.SUCCESS(f'✓ {task_name}: {result}')
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'✗ {task_name} failed: {str(e)}')
                )
        else:
            self.stdout.write(
                self.style.ERROR(f'Unknown task: {task_name}')
            )

    def _run_task(self, task_func, run_async):
        """Run a task either synchronously or asynchronously"""
        if run_async:
            # Run asynchronously using celery
            result = task_func.delay()
            return f"Task queued with ID: {result.id}"
        else:
            # Run synchronously
            result = task_func()
            return result
