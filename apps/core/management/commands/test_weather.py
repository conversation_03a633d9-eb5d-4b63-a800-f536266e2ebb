from django.core.management.base import BaseCommand
from apps.core.services.weather_service import WeatherService


class Command(BaseCommand):
    help = 'Test weather service functionality'

    def add_arguments(self, parser):
        parser.add_argument('--api-key', type=str, help='OpenWeatherMap API key')
        parser.add_argument('--location', type=str, default='New York, NY, US', help='Location to test')
        parser.add_argument('--units', type=str, default='imperial', choices=['imperial', 'metric', 'kelvin'], help='Temperature units')

    def handle(self, *args, **options):
        api_key = options.get('api_key')
        location = options.get('location')
        units = options.get('units')

        if not api_key:
            self.stdout.write(
                self.style.ERROR('Please provide an API key with --api-key')
            )
            return

        self.stdout.write(f'Testing weather service...')
        self.stdout.write(f'Location: {location}')
        self.stdout.write(f'Units: {units}')
        self.stdout.write(f'API Key: {api_key[:8]}...')

        weather_service = WeatherService(api_key=api_key, units=units)
        
        try:
            weather_data = weather_service.get_weather(location)
            
            if weather_data:
                self.stdout.write(
                    self.style.SUCCESS('Weather data retrieved successfully!')
                )
                self.stdout.write(f'Location: {weather_data["location"]}, {weather_data["country"]}')
                self.stdout.write(f'Temperature: {weather_data["temperature"]}°{weather_service.get_temperature_unit_symbol(units)}')
                self.stdout.write(f'Description: {weather_data["description"]}')
                self.stdout.write(f'Feels like: {weather_data["feels_like"]}°{weather_service.get_temperature_unit_symbol(units)}')
                self.stdout.write(f'Humidity: {weather_data["humidity"]}%')
                self.stdout.write(f'Wind: {weather_data["wind_speed"]} {units == "imperial" and "mph" or "m/s"}')
                self.stdout.write(f'Icon: {weather_data["icon"]}')
            else:
                self.stdout.write(
                    self.style.ERROR('Failed to retrieve weather data')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error: {str(e)}')
            )

        # Test location suggestions
        self.stdout.write('\nTesting location suggestions...')
        suggestions = weather_service.get_weather_areas('US', 'NY')
        self.stdout.write(f'US suggestions: {suggestions[:3]}')
        
        suggestions = weather_service.get_weather_areas('GB')
        self.stdout.write(f'UK suggestions: {suggestions[:3]}')
