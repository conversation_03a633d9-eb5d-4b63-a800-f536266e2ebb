"""
Management command to test email configuration and delivery.
"""

from django.core.management.base import BaseCommand
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth.models import User
from apps.organizations.models import Organization
from apps.core.notifications import NotificationManager


class Command(BaseCommand):
    help = 'Test email configuration and delivery'

    def add_arguments(self, parser):
        parser.add_argument(
            '--to',
            type=str,
            help='Email address to send test email to',
        )
        parser.add_argument(
            '--test-notifications',
            action='store_true',
            help='Test notification system emails',
        )

    def handle(self, *args, **options):
        to_email = options.get('to')
        test_notifications = options.get('test_notifications')

        self.stdout.write(
            self.style.SUCCESS('Testing email configuration...')
        )

        # Display current email configuration
        self._display_email_config()

        # Test basic email sending
        if to_email:
            self._test_basic_email(to_email)
        else:
            self.stdout.write(
                self.style.WARNING('No --to email provided, skipping basic email test')
            )

        # Test notification system
        if test_notifications:
            self._test_notification_emails()

        self.stdout.write(
            self.style.SUCCESS('Email testing completed')
        )

    def _display_email_config(self):
        """Display current email configuration"""
        self.stdout.write('\nCurrent Email Configuration:')
        self.stdout.write(f'  EMAIL_BACKEND: {settings.EMAIL_BACKEND}')
        
        if hasattr(settings, 'EMAIL_HOST'):
            self.stdout.write(f'  EMAIL_HOST: {settings.EMAIL_HOST}')
        if hasattr(settings, 'EMAIL_PORT'):
            self.stdout.write(f'  EMAIL_PORT: {settings.EMAIL_PORT}')
        if hasattr(settings, 'EMAIL_USE_TLS'):
            self.stdout.write(f'  EMAIL_USE_TLS: {settings.EMAIL_USE_TLS}')
        if hasattr(settings, 'DEFAULT_FROM_EMAIL'):
            self.stdout.write(f'  DEFAULT_FROM_EMAIL: {settings.DEFAULT_FROM_EMAIL}')

        # Check if using console backend
        if 'console' in settings.EMAIL_BACKEND:
            self.stdout.write(
                self.style.WARNING('  ⚠️  Using console backend - emails will be printed to console')
            )
        elif 'smtp' in settings.EMAIL_BACKEND:
            self.stdout.write(
                self.style.SUCCESS('  ✓ Using SMTP backend')
            )

    def _test_basic_email(self, to_email):
        """Test basic email sending"""
        self.stdout.write(f'\nTesting basic email to {to_email}...')
        
        try:
            send_mail(
                subject='RadioMention Email Test',
                message='This is a test email from RadioMention system.',
                from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
                recipient_list=[to_email],
                fail_silently=False,
            )
            
            self.stdout.write(
                self.style.SUCCESS('  ✓ Basic email sent successfully')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'  ✗ Basic email failed: {str(e)}')
            )

    def _test_notification_emails(self):
        """Test notification system emails"""
        self.stdout.write('\nTesting notification system...')
        
        # Get a test user and organization
        user = User.objects.filter(is_active=True).first()
        organization = Organization.objects.filter(is_active=True).first()
        
        if not user or not organization:
            self.stdout.write(
                self.style.WARNING('  ⚠️  No active user or organization found for testing')
            )
            return

        self.stdout.write(f'  Using test user: {user.username}')
        self.stdout.write(f'  Using test organization: {organization.name}')

        # Test notification manager
        notification_manager = NotificationManager()
        
        test_notifications = [
            {
                'type': 'system_alert',
                'context': {
                    'issues': ['Test system alert'],
                    'check_time': 'now',
                    'severity': 'info'
                }
            },
            {
                'type': 'daily_summary',
                'context': {
                    'date': 'today',
                    'stats': {
                        'shows_scheduled': 5,
                        'mentions_completed': 10,
                        'mentions_missed': 1,
                        'active_presenters': 3
                    },
                    'organization': organization
                }
            }
        ]

        for notification in test_notifications:
            try:
                self.stdout.write(f'  Testing {notification["type"]} notification...')
                
                success = notification_manager.send_notification(
                    notification_type=notification['type'],
                    recipients=[user],
                    context=notification['context'],
                    organization=organization
                )
                
                if success:
                    self.stdout.write(
                        self.style.SUCCESS(f'    ✓ {notification["type"]} notification sent')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'    ⚠️  {notification["type"]} notification not sent (user preferences)')
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'    ✗ {notification["type"]} notification failed: {str(e)}')
                )

    def _get_email_recommendations(self):
        """Get recommendations for email configuration"""
        recommendations = []
        
        if 'console' in settings.EMAIL_BACKEND:
            recommendations.append(
                "For production, configure SMTP settings in settings_production.py"
            )
            
        if not hasattr(settings, 'DEFAULT_FROM_EMAIL'):
            recommendations.append(
                "Set DEFAULT_FROM_EMAIL in settings"
            )
            
        if hasattr(settings, 'EMAIL_HOST_USER') and not settings.EMAIL_HOST_USER:
            recommendations.append(
                "Set EMAIL_HOST_USER for SMTP authentication"
            )
            
        return recommendations
