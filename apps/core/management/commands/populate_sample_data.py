from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from datetime import datetime, time, timedelta
import random

from apps.core.models import Client, Presenter
from apps.shows.models import Show, ShowPresenter
from apps.mentions.models import Mention, MentionReading
from apps.organizations.models import Organization, Branch


class Command(BaseCommand):
    help = 'Populate the database with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')

        # Get or create a default organization for sample data
        organization, org_created = Organization.objects.get_or_create(
            slug='sample-radio-station',
            defaults={
                'name': 'Sample Radio Station',
                'description': 'Default organization for sample data',
                'plan_type': 'enterprise',
                'max_users': 1000,
                'max_mentions_per_month': 10000,
            }
        )

        if org_created:
            self.stdout.write(f'Created default organization: {organization.name}')
        else:
            self.stdout.write(f'Using existing organization: {organization.name}')

        # Create clients
        clients_data = [
            {'name': 'Spotify Premium', 'contact_person': '<PERSON>', 'email': '<EMAIL>', 'phone': '+****************', 'industry': 'Technology'},
            {'name': 'Local Gym', 'contact_person': '<PERSON>', 'email': '<EMAIL>', 'phone': '+****************', 'industry': 'Health & Fitness'},
            {'name': 'Burger King', 'contact_person': 'Lisa Martinez', 'email': '<EMAIL>', 'phone': '+****************', 'industry': 'Food & Beverage'},
            {'name': 'Netflix', 'contact_person': 'David Wilson', 'email': '<EMAIL>', 'phone': '+****************', 'industry': 'Entertainment'},
            {'name': 'Toyota', 'contact_person': 'Emma Davis', 'email': '<EMAIL>', 'phone': '+****************', 'industry': 'Automotive'},
        ]

        for client_data in clients_data:
            client, created = Client.objects.get_or_create(
                name=client_data['name'],
                organization=organization,
                defaults={**client_data, 'organization': organization}
            )
            if created:
                self.stdout.write(f'Created client: {client.name}')
        
        # Create presenters
        presenters_data = [
            {'first_name': 'Mike', 'last_name': 'Johnson', 'stage_name': 'DJ Mike', 'email': '<EMAIL>', 'phone': '+****************', 'experience_years': 8},
            {'first_name': 'Sarah', 'last_name': 'Parker', 'stage_name': 'Sarah P', 'email': '<EMAIL>', 'phone': '+****************', 'experience_years': 5},
            {'first_name': 'David', 'last_name': 'Wilson', 'stage_name': 'Dave Wave', 'email': '<EMAIL>', 'phone': '+****************', 'experience_years': 12},
            {'first_name': 'Emma', 'last_name': 'Thompson', 'stage_name': 'Emma T', 'email': '<EMAIL>', 'phone': '+****************', 'experience_years': 3},
            {'first_name': 'James', 'last_name': 'Miller', 'stage_name': 'Jimmy M', 'email': '<EMAIL>', 'phone': '+****************', 'experience_years': 7},
        ]
        
        for presenter_data in presenters_data:
            presenter, created = Presenter.objects.get_or_create(
                email=presenter_data['email'],
                organization=organization,
                defaults={**presenter_data, 'organization': organization}
            )
            if created:
                self.stdout.write(f'Created presenter: {presenter.display_name}')
        
        # Create shows
        shows_data = [
            {'name': 'Morning Drive', 'description': 'Wake up with the best music and news', 'start_time': time(6, 0), 'end_time': time(10, 0), 'days_of_week': [0, 1, 2, 3, 4]},
            {'name': 'Mid-Morning Show', 'description': 'Keep the energy going', 'start_time': time(10, 0), 'end_time': time(12, 0), 'days_of_week': [0, 1, 2, 3, 4]},
            {'name': 'Lunch Hour', 'description': 'Perfect music for your lunch break', 'start_time': time(12, 0), 'end_time': time(14, 0), 'days_of_week': [0, 1, 2, 3, 4]},
            {'name': 'Afternoon Drive', 'description': 'Drive home with great tunes', 'start_time': time(15, 0), 'end_time': time(18, 0), 'days_of_week': [0, 1, 2, 3, 4]},
            {'name': 'Evening Show', 'description': 'Relax and unwind', 'start_time': time(18, 0), 'end_time': time(22, 0), 'days_of_week': [0, 1, 2, 3, 4]},
            {'name': 'Weekend Special', 'description': 'Weekend vibes', 'start_time': time(10, 0), 'end_time': time(16, 0), 'days_of_week': [5, 6]},
        ]
        
        for show_data in shows_data:
            show, created = Show.objects.get_or_create(
                name=show_data['name'],
                organization=organization,
                defaults={**show_data, 'organization': organization}
            )
            if created:
                self.stdout.write(f'Created show: {show.name}')
        
        # Assign presenters to shows
        shows = Show.objects.all()
        presenters = Presenter.objects.all()
        
        for show in shows:
            # Assign 1-2 presenters per show
            assigned_presenters = random.sample(list(presenters), random.randint(1, 2))
            for i, presenter in enumerate(assigned_presenters):
                ShowPresenter.objects.get_or_create(
                    show=show,
                    presenter=presenter,
                    defaults={
                        'role': 'Host' if i == 0 else 'Co-Host',
                        'is_primary': i == 0
                    }
                )
        
        # Create mentions
        clients = Client.objects.all()
        user = User.objects.first()
        
        mention_titles = [
            'Summer Sale Promotion',
            'New Product Launch',
            'Special Event Announcement',
            'Holiday Hours Notice',
            'Customer Appreciation',
            'Grand Opening',
            'Limited Time Offer',
            'Community Event',
            'Service Update',
            'Brand Partnership'
        ]
        
        for i in range(20):
            client = random.choice(clients)
            title = f"{client.name} - {random.choice(mention_titles)}"
            content = f"This is a sample mention for {client.name}. {random.choice(['Check out our latest offers!', 'Visit us today!', 'Call now for more information!', 'Limited time only!'])}"
            
            mention = Mention.objects.create(
                client=client,
                title=title,
                content=content,
                status=random.choice(['pending', 'scheduled', 'read']),
                priority=random.randint(1, 4),
                duration_seconds=random.choice([15, 30, 45, 60]),
                created_by=user
            )
            
            # Create some readings for scheduled/read mentions
            if mention.status in ['scheduled', 'read']:
                show = random.choice(shows)
                if show.showpresenter_set.exists():
                    presenter = random.choice(show.showpresenter_set.all()).presenter

                    # Find a valid date when the show airs
                    base_date = datetime.now().date()
                    for days_offset in range(-7, 8):  # Try dates from -7 to +7 days
                        scheduled_date = base_date + timedelta(days=days_offset)
                        weekday = scheduled_date.weekday()

                        # Check if show airs on this weekday
                        if weekday in show.days_of_week:
                            # Generate a time within the show's time range
                            show_start_minutes = show.start_time.hour * 60 + show.start_time.minute
                            show_end_minutes = show.end_time.hour * 60 + show.end_time.minute

                            # Handle shows that cross midnight
                            if show_end_minutes < show_start_minutes:
                                show_end_minutes += 24 * 60

                            # Pick a random time within the show
                            random_minutes = random.randint(show_start_minutes, show_end_minutes - 1)
                            random_minutes = random_minutes % (24 * 60)  # Handle overflow

                            scheduled_time = time(random_minutes // 60, random_minutes % 60)

                            try:
                                reading = MentionReading.objects.create(
                                    mention=mention,
                                    show=show,
                                    scheduled_date=scheduled_date,
                                    scheduled_time=scheduled_time
                                )

                                # Mark some as completed
                                if mention.status == 'read' or random.choice([True, False]):
                                    reading.actual_read_time = datetime.combine(scheduled_date, scheduled_time)
                                    reading.duration_seconds = mention.duration_seconds + random.randint(-5, 10)
                                    reading.presenter = presenter  # Assign presenter when marked as read
                                    reading.save()

                                break  # Successfully created, exit the loop
                            except Exception as e:
                                # If creation fails, try next date
                                continue
        
        self.stdout.write(self.style.SUCCESS('Successfully created sample data!'))
        self.stdout.write(f'Created {Client.objects.count()} clients')
        self.stdout.write(f'Created {Presenter.objects.count()} presenters')
        self.stdout.write(f'Created {Show.objects.count()} shows')
        self.stdout.write(f'Created {Mention.objects.count()} mentions')
        self.stdout.write(f'Created {MentionReading.objects.count()} mention readings')
