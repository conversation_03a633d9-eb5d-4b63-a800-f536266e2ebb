"""
Management command to validate the entire task and notification system.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings
from apps.organizations.models import Organization, OrganizationMembership
from apps.core.models import Presenter
from apps.shows.models import Show
from apps.mentions.models import Mention
from apps.settings.models import UserPreferences
import redis
import logging


class Command(BaseCommand):
    help = 'Validate the task and notification system configuration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix issues found during validation',
        )

    def handle(self, *args, **options):
        fix_issues = options.get('fix')

        self.stdout.write(
            self.style.SUCCESS('Validating task and notification system...')
        )

        validation_results = {
            'celery_config': self._validate_celery_config(),
            'redis_connection': self._validate_redis_connection(),
            'email_config': self._validate_email_config(),
            'organizations': self._validate_organizations(),
            'user_preferences': self._validate_user_preferences(),
            'notification_templates': self._validate_notification_templates(),
            'task_schedules': self._validate_task_schedules(),
        }

        # Display results
        total_issues = 0
        for category, results in validation_results.items():
            self.stdout.write(f'\n{category.upper().replace("_", " ")}:')
            
            if results['status'] == 'ok':
                self.stdout.write(
                    self.style.SUCCESS(f'  ✓ {results["message"]}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'  ✗ {results["message"]}')
                )
                total_issues += 1
                
                if 'issues' in results:
                    for issue in results['issues']:
                        self.stdout.write(f'    • {issue}')
                
                if fix_issues and 'fix_function' in results:
                    self.stdout.write('    Attempting to fix...')
                    try:
                        results['fix_function']()
                        self.stdout.write(
                            self.style.SUCCESS('    ✓ Fixed')
                        )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'    ✗ Fix failed: {str(e)}')
                        )

        # Summary
        self.stdout.write('\n' + '='*50)
        if total_issues == 0:
            self.stdout.write(
                self.style.SUCCESS('✓ All validations passed! System is ready.')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'✗ Found {total_issues} issues that need attention.')
            )
            if not fix_issues:
                self.stdout.write(
                    self.style.WARNING('Run with --fix to attempt automatic fixes.')
                )

    def _validate_celery_config(self):
        """Validate Celery configuration"""
        try:
            # Check if celery is properly configured
            if not hasattr(settings, 'CELERY_BROKER_URL'):
                return {
                    'status': 'error',
                    'message': 'CELERY_BROKER_URL not configured'
                }
            
            if not hasattr(settings, 'CELERY_RESULT_BACKEND'):
                return {
                    'status': 'error',
                    'message': 'CELERY_RESULT_BACKEND not configured'
                }
            
            # Check if django_celery_beat is installed
            if 'django_celery_beat' not in settings.INSTALLED_APPS:
                return {
                    'status': 'error',
                    'message': 'django_celery_beat not in INSTALLED_APPS'
                }
            
            return {
                'status': 'ok',
                'message': 'Celery configuration is valid'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Celery configuration error: {str(e)}'
            }

    def _validate_redis_connection(self):
        """Validate Redis connection"""
        try:
            # Try to connect to Redis
            r = redis.Redis.from_url(settings.CELERY_BROKER_URL)
            r.ping()
            
            return {
                'status': 'ok',
                'message': 'Redis connection successful'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Redis connection failed: {str(e)}'
            }

    def _validate_email_config(self):
        """Validate email configuration"""
        try:
            # Check email backend
            if not hasattr(settings, 'EMAIL_BACKEND'):
                return {
                    'status': 'error',
                    'message': 'EMAIL_BACKEND not configured'
                }
            
            # Check if using console backend (development)
            if 'console' in settings.EMAIL_BACKEND:
                return {
                    'status': 'warning',
                    'message': 'Using console email backend (development only)'
                }
            
            # Check required email settings for production
            required_settings = ['EMAIL_HOST', 'DEFAULT_FROM_EMAIL']
            missing_settings = [s for s in required_settings if not hasattr(settings, s)]
            
            if missing_settings:
                return {
                    'status': 'error',
                    'message': f'Missing email settings: {", ".join(missing_settings)}'
                }
            
            return {
                'status': 'ok',
                'message': 'Email configuration is valid'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Email configuration error: {str(e)}'
            }

    def _validate_organizations(self):
        """Validate organization setup"""
        try:
            organizations = Organization.objects.filter(is_active=True)
            
            if not organizations.exists():
                return {
                    'status': 'warning',
                    'message': 'No active organizations found'
                }
            
            issues = []
            for org in organizations:
                # Check if organization has admins
                admins = OrganizationMembership.objects.filter(
                    organization=org,
                    role__in=['owner', 'admin'],
                    is_active=True
                )
                
                if not admins.exists():
                    issues.append(f'Organization "{org.name}" has no active admins')
                
                # Check if organization has shows
                shows = Show.objects.filter(organization=org, is_active=True)
                if not shows.exists():
                    issues.append(f'Organization "{org.name}" has no active shows')
            
            if issues:
                return {
                    'status': 'warning',
                    'message': f'Found {len(issues)} organization issues',
                    'issues': issues
                }
            
            return {
                'status': 'ok',
                'message': f'All {organizations.count()} organizations are properly configured'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Organization validation error: {str(e)}'
            }

    def _validate_user_preferences(self):
        """Validate user preferences for notifications"""
        try:
            # Check if users have notification preferences
            from apps.settings.models import UserPreferences

            # Get all active users
            active_users = OrganizationMembership.objects.filter(
                is_active=True
            ).values_list('user_id', flat=True).distinct()

            # Count users without preferences
            users_with_prefs = UserPreferences.objects.filter(
                user_id__in=active_users
            ).values_list('user_id', flat=True).distinct()

            users_without_prefs = len(set(active_users) - set(users_with_prefs))
            
            if users_without_prefs > 0:
                return {
                    'status': 'warning',
                    'message': f'{users_without_prefs} users missing notification preferences',
                    'fix_function': self._fix_user_preferences
                }
            
            return {
                'status': 'ok',
                'message': 'All users have notification preferences configured'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'User preferences validation error: {str(e)}'
            }

    def _validate_notification_templates(self):
        """Validate notification templates exist"""
        try:
            from django.template.loader import get_template
            
            required_templates = [
                'notifications/email/generic.html',
                'notifications/email/show_missing_presenter.html',
                'notifications/email/approval_reminder.html',
                'notifications/email/daily_summary.html',
                'notifications/email/show_reminder.html',
            ]
            
            missing_templates = []
            for template in required_templates:
                try:
                    get_template(template)
                except:
                    missing_templates.append(template)
            
            if missing_templates:
                return {
                    'status': 'error',
                    'message': f'Missing notification templates: {len(missing_templates)}',
                    'issues': missing_templates
                }
            
            return {
                'status': 'ok',
                'message': 'All notification templates found'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Template validation error: {str(e)}'
            }

    def _validate_task_schedules(self):
        """Validate task schedules are reasonable"""
        try:
            from radio_mentions_project.celery import app
            
            if not hasattr(app.conf, 'beat_schedule'):
                return {
                    'status': 'error',
                    'message': 'No beat schedule configured'
                }
            
            schedule = app.conf.beat_schedule
            
            if not schedule:
                return {
                    'status': 'error',
                    'message': 'Beat schedule is empty'
                }
            
            # Check for reasonable intervals
            issues = []
            for task_name, config in schedule.items():
                interval = config.get('schedule', 0)
                
                # Check for very frequent tasks (less than 30 seconds)
                if interval < 30 and task_name != 'process-notification-queue':
                    issues.append(f'Task "{task_name}" has very frequent schedule: {interval}s')
                
                # Check for very infrequent tasks (more than 1 month)
                if interval > 2592000:  # 30 days
                    issues.append(f'Task "{task_name}" has very infrequent schedule: {interval}s')
            
            if issues:
                return {
                    'status': 'warning',
                    'message': f'Found {len(issues)} schedule issues',
                    'issues': issues
                }
            
            return {
                'status': 'ok',
                'message': f'All {len(schedule)} task schedules are reasonable'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Task schedule validation error: {str(e)}'
            }

    def _fix_user_preferences(self):
        """Create missing user preferences"""
        from django.contrib.auth.models import User
        
        # Get all users without preferences
        users_without_prefs = User.objects.filter(
            organizationmembership__is_active=True,
            userpreferences__isnull=True
        ).distinct()
        
        for user in users_without_prefs:
            # Get user's organizations
            memberships = OrganizationMembership.objects.filter(
                user=user,
                is_active=True
            )
            
            for membership in memberships:
                UserPreferences.objects.get_or_create(
                    user=user,
                    organization=membership.organization,
                    defaults={
                        'email_notifications': True,
                        'notify_mention_approved': True,
                        'notify_mention_rejected': True,
                        'notify_conflicts': True,
                        'notify_deadlines': True,
                        'notify_schedule_changes': True,
                    }
                )
