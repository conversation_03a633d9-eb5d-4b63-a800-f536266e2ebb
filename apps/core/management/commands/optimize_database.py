"""
Management command for database optimization
"""
from django.core.management.base import BaseCommand
from django.db import connection
from apps.core.db_optimization import DatabaseOptimizer, ConnectionPoolManager
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Optimize database performance and analyze queries'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--analyze-queries',
            action='store_true',
            help='Analyze recent queries for optimization opportunities'
        )
        parser.add_argument(
            '--connection-info',
            action='store_true',
            help='Display database connection information'
        )
        parser.add_argument(
            '--table-sizes',
            action='store_true',
            help='Show database table sizes and statistics'
        )
        parser.add_argument(
            '--connection-stats',
            action='store_true',
            help='Show connection pool statistics'
        )
        parser.add_argument(
            '--vacuum',
            action='store_true',
            help='Run VACUUM ANALYZE on PostgreSQL (requires superuser)'
        )
        parser.add_argument(
            '--reindex',
            action='store_true',
            help='Reindex database tables (requires superuser)'
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Run all optimization checks'
        )

    def handle(self, *args, **options):
        if options['all']:
            options.update({
                'analyze_queries': True,
                'connection_info': True,
                'table_sizes': True,
                'connection_stats': True,
            })
        
        self.stdout.write(
            self.style.SUCCESS('Starting database optimization analysis...')
        )
        
        if options['analyze_queries']:
            self._analyze_queries()
        
        if options['connection_info']:
            self._show_connection_info()
        
        if options['table_sizes']:
            self._show_table_sizes()
        
        if options['connection_stats']:
            self._show_connection_stats()
        
        if options['vacuum']:
            self._run_vacuum()
        
        if options['reindex']:
            self._run_reindex()
        
        self.stdout.write(
            self.style.SUCCESS('Database optimization analysis completed.')
        )

    def _analyze_queries(self):
        """Analyze database queries"""
        self.stdout.write('\n📊 Query Analysis')
        self.stdout.write('=' * 50)
        
        analysis = DatabaseOptimizer.analyze_queries()
        
        if not analysis:
            self.stdout.write(
                self.style.WARNING('No query analysis available (requires DEBUG=True)')
            )
            return
        
        if 'message' in analysis:
            self.stdout.write(analysis['message'])
            return
        
        # Display summary
        self.stdout.write(f"Total queries: {analysis['total_queries']}")
        self.stdout.write(f"Total time: {analysis['total_time']}s")
        self.stdout.write(f"Average time: {analysis['average_time']}s")
        
        # Show slow queries
        if analysis['slow_queries']:
            self.stdout.write('\n🐌 Slow Queries (>100ms):')
            for i, query in enumerate(analysis['slow_queries'], 1):
                self.stdout.write(f"{i}. {query['time']}s: {query['sql']}")
        
        # Show duplicate queries
        if analysis['duplicate_queries']:
            self.stdout.write('\n🔄 Duplicate Queries:')
            for i, query in enumerate(analysis['duplicate_queries'], 1):
                self.stdout.write(
                    f"{i}. {query['count']} times ({query['total_time']:.3f}s total): {query['sql']}"
                )
        
        # Recommendations
        self.stdout.write('\n💡 Recommendations:')
        if analysis['slow_queries']:
            self.stdout.write('  • Consider adding indexes for slow queries')
            self.stdout.write('  • Review query logic and use select_related/prefetch_related')
        
        if analysis['duplicate_queries']:
            self.stdout.write('  • Cache results of frequently repeated queries')
            self.stdout.write('  • Use select_related to reduce N+1 query problems')
        
        if analysis['average_time'] > 0.05:
            self.stdout.write('  • Average query time is high - consider query optimization')

    def _show_connection_info(self):
        """Show database connection information"""
        self.stdout.write('\n🔌 Database Connection Information')
        self.stdout.write('=' * 50)
        
        conn_info = DatabaseOptimizer.get_connection_info()
        
        for alias, info in conn_info.items():
            self.stdout.write(f"\nDatabase: {alias}")
            self.stdout.write(f"  Vendor: {info['vendor']}")
            self.stdout.write(f"  Engine: {info['settings']['ENGINE']}")
            self.stdout.write(f"  Name: {info['settings']['NAME']}")
            self.stdout.write(f"  Host: {info['settings']['HOST']}")
            self.stdout.write(f"  Port: {info['settings']['PORT']}")
            self.stdout.write(f"  Connection Max Age: {info['settings']['CONN_MAX_AGE']}")
            
            if info['status'] == 'connected':
                self.stdout.write(self.style.SUCCESS(f"  Status: {info['status']}"))
            else:
                self.stdout.write(self.style.ERROR(f"  Status: {info['status']}"))

    def _show_table_sizes(self):
        """Show database table sizes"""
        self.stdout.write('\n📏 Database Table Sizes')
        self.stdout.write('=' * 50)
        
        table_info = DatabaseOptimizer.get_table_sizes()
        
        if not table_info:
            self.stdout.write(
                self.style.WARNING('Table size information not available (PostgreSQL only)')
            )
            return
        
        # Show table sizes
        self.stdout.write('\nTable Sizes:')
        for table in table_info['table_sizes'][:10]:  # Top 10 largest tables
            self.stdout.write(f"  {table['table']}: {table['size']}")
        
        # Show total database size
        total_size = sum(table['size_bytes'] for table in table_info['table_sizes'])
        total_size_mb = total_size / (1024 * 1024)
        self.stdout.write(f"\nTotal database size: {total_size_mb:.2f} MB")

    def _show_connection_stats(self):
        """Show connection pool statistics"""
        self.stdout.write('\n📈 Connection Pool Statistics')
        self.stdout.write('=' * 50)
        
        stats = ConnectionPoolManager.get_connection_stats()
        
        for alias, stat in stats.items():
            self.stdout.write(f"\nDatabase: {alias}")
            self.stdout.write(f"  Queries executed: {stat['queries_executed']}")
            self.stdout.write(f"  Connection created: {stat['connection_created']}")
            
            if 'total_connections' in stat:
                self.stdout.write(f"  Total connections: {stat['total_connections']}")
                self.stdout.write(f"  Active connections: {stat['active_connections']}")
                self.stdout.write(f"  Idle connections: {stat['idle_connections']}")

    def _run_vacuum(self):
        """Run VACUUM ANALYZE on PostgreSQL"""
        self.stdout.write('\n🧹 Running VACUUM ANALYZE')
        self.stdout.write('=' * 50)
        
        try:
            with connection.cursor() as cursor:
                # Check if we're using PostgreSQL
                if connection.vendor != 'postgresql':
                    self.stdout.write(
                        self.style.WARNING('VACUUM is only available for PostgreSQL')
                    )
                    return
                
                self.stdout.write('Running VACUUM ANALYZE...')
                cursor.execute('VACUUM ANALYZE;')
                self.stdout.write(self.style.SUCCESS('VACUUM ANALYZE completed'))
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'VACUUM ANALYZE failed: {e}')
            )
            self.stdout.write(
                'Note: VACUUM ANALYZE may require superuser privileges'
            )

    def _run_reindex(self):
        """Reindex database tables"""
        self.stdout.write('\n🔄 Reindexing Database')
        self.stdout.write('=' * 50)
        
        try:
            with connection.cursor() as cursor:
                if connection.vendor != 'postgresql':
                    self.stdout.write(
                        self.style.WARNING('REINDEX is only available for PostgreSQL')
                    )
                    return
                
                # Get all tables
                cursor.execute("""
                    SELECT tablename FROM pg_tables 
                    WHERE schemaname = 'public'
                """)
                tables = cursor.fetchall()
                
                for table in tables:
                    table_name = table[0]
                    try:
                        self.stdout.write(f'Reindexing table: {table_name}')
                        cursor.execute(f'REINDEX TABLE {table_name};')
                    except Exception as e:
                        self.stdout.write(
                            self.style.WARNING(f'Failed to reindex {table_name}: {e}')
                        )
                
                self.stdout.write(self.style.SUCCESS('Database reindexing completed'))
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Reindexing failed: {e}')
            )
            self.stdout.write(
                'Note: REINDEX may require superuser privileges'
            )
