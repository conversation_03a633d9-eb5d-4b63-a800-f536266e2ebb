"""
Management command to test Sentry error monitoring - DISABLED
"""
from django.core.management.base import BaseCommand
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Test Sentry error monitoring integration - DISABLED'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--test-error',
            action='store_true',
            help='Generate a test error to verify Sentry integration'
        )
        parser.add_argument(
            '--test-warning',
            action='store_true',
            help='Generate a test warning to verify logging'
        )
        parser.add_argument(
            '--test-info',
            action='store_true',
            help='Generate a test info message'
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.WARNING('Sentry integration is DISABLED')
        )
        self.stdout.write(
            self.style.SUCCESS('Sentry SDK has been disabled in production settings')
        )

        # Disabled functionality
        # if options['test_error']:
        #     self._test_error()
        # elif options['test_warning']:
        #     self._test_warning()
        # elif options['test_info']:
        #     self._test_info()
        # else:
        #     self._test_all()

    def _test_error(self):
        """Test error reporting to Sentry"""
        self.stdout.write("Generating test error for Sentry...")
        try:
            # This will generate a ZeroDivisionError
            result = 1 / 0
        except Exception as e:
            logger.error(f"Test error for Sentry monitoring: {e}")
            self.stdout.write(
                self.style.ERROR(f'Test error generated: {e}')
            )
            self.stdout.write(
                self.style.SUCCESS('Error should appear in Sentry dashboard')
            )

    def _test_warning(self):
        """Test warning logging"""
        self.stdout.write("Generating test warning...")
        logger.warning("Test warning message for monitoring system")
        self.stdout.write(
            self.style.WARNING('Test warning generated')
        )

    def _test_info(self):
        """Test info logging"""
        self.stdout.write("Generating test info message...")
        logger.info("Test info message for monitoring system")
        self.stdout.write(
            self.style.SUCCESS('Test info message generated')
        )

    def _test_all(self):
        """Test all logging levels"""
        self.stdout.write("Testing all logging levels...")
        
        # Test info
        logger.info("Sentry integration test - INFO level")
        self.stdout.write("✓ Info message logged")
        
        # Test warning
        logger.warning("Sentry integration test - WARNING level")
        self.stdout.write("✓ Warning message logged")
        
        # Test error (without exception)
        logger.error("Sentry integration test - ERROR level")
        self.stdout.write("✓ Error message logged")
        
        self.stdout.write(
            self.style.SUCCESS('All test messages generated successfully')
        )
        self.stdout.write(
            self.style.SUCCESS('Check your Sentry dashboard for error reports')
        )
