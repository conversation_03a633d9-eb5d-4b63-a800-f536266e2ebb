"""
Management command to monitor system performance and health.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import Count, Avg
from datetime import datetime, timedelta
from apps.organizations.models import Organization
from apps.activity_logs.models import ActivityLog
from apps.mentions.models import MentionReading
from apps.shows.models import ShowSession
from apps.core.models import Presenter
import json
import time


class Command(BaseCommand):
    help = 'Monitor system performance and health in real-time'

    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=30,
            help='Monitoring interval in seconds (default: 30)',
        )
        parser.add_argument(
            '--duration',
            type=int,
            default=300,
            help='Total monitoring duration in seconds (default: 300)',
        )
        parser.add_argument(
            '--output-file',
            type=str,
            help='Output file for monitoring data (JSON format)',
        )

    def handle(self, *args, **options):
        interval = options.get('interval')
        duration = options.get('duration')
        output_file = options.get('output_file')

        self.stdout.write(
            self.style.SUCCESS(f'Starting system monitoring...')
        )
        self.stdout.write(f'  Interval: {interval} seconds')
        self.stdout.write(f'  Duration: {duration} seconds')
        if output_file:
            self.stdout.write(f'  Output file: {output_file}')

        monitoring_data = []
        start_time = time.time()
        end_time = start_time + duration

        try:
            while time.time() < end_time:
                # Collect performance metrics
                metrics = self._collect_metrics()
                
                # Display metrics
                self._display_metrics(metrics)
                
                # Store metrics
                monitoring_data.append(metrics)
                
                # Wait for next interval
                time.sleep(interval)

        except KeyboardInterrupt:
            self.stdout.write(
                self.style.WARNING('\nMonitoring interrupted by user')
            )

        # Save data if output file specified
        if output_file and monitoring_data:
            self._save_monitoring_data(monitoring_data, output_file)

        # Display summary
        self._display_summary(monitoring_data)

        self.stdout.write(
            self.style.SUCCESS('Monitoring completed')
        )

    def _collect_metrics(self):
        """Collect current system metrics"""
        current_time = timezone.now()
        
        # Basic system metrics
        metrics = {
            'timestamp': current_time.isoformat(),
            'organizations': {
                'total': Organization.objects.filter(is_active=True).count(),
            },
            'shows': {
                'active_sessions': ShowSession.objects.filter(
                    date=current_time.date(),
                    actual_end_time__isnull=True
                ).count(),
                'total_today': ShowSession.objects.filter(
                    date=current_time.date()
                ).count(),
            },
            'mentions': {
                'pending_today': MentionReading.objects.filter(
                    scheduled_date=current_time.date(),
                    actual_read_time__isnull=True
                ).count(),
                'completed_today': MentionReading.objects.filter(
                    scheduled_date=current_time.date(),
                    actual_read_time__isnull=False
                ).count(),
            },
            'presenters': {
                'active': Presenter.objects.filter(is_active=True).count(),
                'online_now': self._get_active_presenters_count(),
            },
            'activity': {
                'logs_last_hour': ActivityLog.objects.filter(
                    created_at__gte=current_time - timedelta(hours=1)
                ).count(),
                'errors_last_hour': ActivityLog.objects.filter(
                    created_at__gte=current_time - timedelta(hours=1),
                    level__in=['error', 'critical']
                ).count(),
            }
        }

        # Calculate performance indicators
        metrics['performance'] = self._calculate_performance_indicators(metrics)

        return metrics

    def _get_active_presenters_count(self):
        """Get count of presenters who are currently active"""
        current_time = timezone.now()
        
        # Count presenters with recent activity (last 30 minutes)
        recent_activity = ActivityLog.objects.filter(
            created_at__gte=current_time - timedelta(minutes=30),
            action__in=['mention_read', 'show_started', 'show_ended']
        ).values('user').distinct().count()
        
        return recent_activity

    def _calculate_performance_indicators(self, metrics):
        """Calculate performance indicators"""
        performance = {}
        
        # Completion rate
        total_mentions = metrics['mentions']['pending_today'] + metrics['mentions']['completed_today']
        if total_mentions > 0:
            completion_rate = (metrics['mentions']['completed_today'] / total_mentions) * 100
            performance['completion_rate'] = round(completion_rate, 2)
        else:
            performance['completion_rate'] = 0

        # System health score (0-100)
        health_score = 100
        
        # Deduct points for errors
        if metrics['activity']['errors_last_hour'] > 0:
            health_score -= min(metrics['activity']['errors_last_hour'] * 10, 50)
        
        # Deduct points for low completion rate
        if performance['completion_rate'] < 80:
            health_score -= (80 - performance['completion_rate'])
        
        # Deduct points for inactive system
        if metrics['activity']['logs_last_hour'] == 0:
            health_score -= 20
        
        performance['health_score'] = max(health_score, 0)
        
        return performance

    def _display_metrics(self, metrics):
        """Display current metrics"""
        timestamp = datetime.fromisoformat(metrics['timestamp'].replace('Z', '+00:00'))
        
        self.stdout.write(f'\n📊 System Metrics - {timestamp.strftime("%H:%M:%S")}')
        self.stdout.write('=' * 50)
        
        # Organizations
        self.stdout.write(f'🏢 Organizations: {metrics["organizations"]["total"]} active')
        
        # Shows
        active_sessions = metrics['shows']['active_sessions']
        total_today = metrics['shows']['total_today']
        self.stdout.write(f'📻 Shows: {active_sessions} active, {total_today} total today')
        
        # Mentions
        pending = metrics['mentions']['pending_today']
        completed = metrics['mentions']['completed_today']
        self.stdout.write(f'📝 Mentions: {completed} completed, {pending} pending today')
        
        # Presenters
        active_presenters = metrics['presenters']['active']
        online_now = metrics['presenters']['online_now']
        self.stdout.write(f'👥 Presenters: {online_now} online, {active_presenters} total active')
        
        # Activity
        logs_hour = metrics['activity']['logs_last_hour']
        errors_hour = metrics['activity']['errors_last_hour']
        self.stdout.write(f'📋 Activity: {logs_hour} logs, {errors_hour} errors (last hour)')
        
        # Performance
        completion_rate = metrics['performance']['completion_rate']
        health_score = metrics['performance']['health_score']
        
        # Color-code health score
        if health_score >= 90:
            health_style = self.style.SUCCESS
        elif health_score >= 70:
            health_style = self.style.WARNING
        else:
            health_style = self.style.ERROR
        
        self.stdout.write(f'⚡ Performance: {completion_rate}% completion rate')
        self.stdout.write(health_style(f'💚 Health Score: {health_score}/100'))

    def _save_monitoring_data(self, data, filename):
        """Save monitoring data to file"""
        try:
            with open(filename, 'w') as f:
                json.dump(data, f, indent=2)
            self.stdout.write(
                self.style.SUCCESS(f'Monitoring data saved to {filename}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to save monitoring data: {str(e)}')
            )

    def _display_summary(self, data):
        """Display monitoring summary"""
        if not data:
            return

        self.stdout.write('\n📈 Monitoring Summary')
        self.stdout.write('=' * 50)
        
        # Calculate averages
        total_points = len(data)
        avg_completion_rate = sum(d['performance']['completion_rate'] for d in data) / total_points
        avg_health_score = sum(d['performance']['health_score'] for d in data) / total_points
        
        # Find min/max health scores
        health_scores = [d['performance']['health_score'] for d in data]
        min_health = min(health_scores)
        max_health = max(health_scores)
        
        # Count errors
        total_errors = sum(d['activity']['errors_last_hour'] for d in data)
        
        self.stdout.write(f'📊 Data Points: {total_points}')
        self.stdout.write(f'📈 Average Completion Rate: {avg_completion_rate:.1f}%')
        self.stdout.write(f'💚 Average Health Score: {avg_health_score:.1f}/100')
        self.stdout.write(f'📉 Health Score Range: {min_health} - {max_health}')
        self.stdout.write(f'🚨 Total Errors Detected: {total_errors}')
        
        # Recommendations
        self.stdout.write('\n💡 Recommendations:')
        if avg_health_score < 80:
            self.stdout.write('  • System health is below optimal - investigate error logs')
        if avg_completion_rate < 90:
            self.stdout.write('  • Mention completion rate could be improved')
        if total_errors > 0:
            self.stdout.write('  • Review and address system errors')
        if avg_health_score >= 95 and avg_completion_rate >= 95:
            self.stdout.write('  • System is performing excellently! 🎉')
