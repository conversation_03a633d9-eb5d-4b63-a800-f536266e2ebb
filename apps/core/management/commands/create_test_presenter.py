from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from apps.core.models import Presenter
from apps.organizations.models import Organization, OrganizationMembership
from django.utils import timezone


class Command(BaseCommand):
    help = 'Create a test presenter user account'

    def handle(self, *args, **options):
        self.stdout.write('Creating test presenter user...')
        
        # Get the sample organization
        try:
            org = Organization.objects.get(slug='sample-radio-station')
        except Organization.DoesNotExist:
            self.stdout.write(self.style.ERROR('Sample organization not found. Run populate_sample_data first.'))
            return

        # Get a presenter
        presenter = Presenter.objects.first()
        if not presenter:
            self.stdout.write(self.style.ERROR('No presenters found. Run populate_sample_data first.'))
            return

        self.stdout.write(f'Found presenter: {presenter.first_name} {presenter.last_name}')

        # Create a user account for this presenter
        user, created = User.objects.get_or_create(
            username=f'presenter_{presenter.id}',
            defaults={
                'email': presenter.email,
                'first_name': presenter.first_name,
                'last_name': presenter.last_name,
            }
        )

        if created:
            user.set_password('testpass123')
            user.save()
            self.stdout.write(f'Created user: {user.username}')
        else:
            self.stdout.write(f'User already exists: {user.username}')

        # Create organization membership
        membership, created = OrganizationMembership.objects.get_or_create(
            user=user,
            organization=org,
            defaults={
                'role': 'presenter',
                'is_default': True,
                'invitation_accepted_at': timezone.now()
            }
        )

        # Link the user to the presenter
        presenter.user = user
        presenter.save()

        self.stdout.write(f'Presenter {presenter.first_name} {presenter.last_name} is now linked to user {user.username}')
        self.stdout.write(f'Shows assigned to this presenter:')
        for show_presenter in presenter.showpresenter_set.all():
            self.stdout.write(f'  - {show_presenter.show.name} ({show_presenter.role})')

        self.stdout.write(self.style.SUCCESS(f'Test presenter user created successfully!'))
        self.stdout.write(f'Login with: username={user.username}, password=testpass123')
