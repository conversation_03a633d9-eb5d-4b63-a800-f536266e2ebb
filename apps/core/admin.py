from django.contrib import admin
from .models import Client, Presenter, PresenterAvailability, PresenterSkill, PresenterNote


class PresenterAvailabilityInline(admin.TabularInline):
    model = PresenterAvailability
    extra = 0
    fields = ['day_of_week', 'start_time', 'end_time', 'is_available', 'notes']


class PresenterSkillInline(admin.TabularInline):
    model = PresenterSkill
    extra = 0
    fields = ['name', 'category', 'proficiency_level', 'certification']


class PresenterNoteInline(admin.TabularInline):
    model = PresenterNote
    extra = 0
    fields = ['title', 'note_type', 'content', 'is_private']
    readonly_fields = ['author']


@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ['name', 'contact_person', 'email', 'phone', 'industry', 'is_active', 'created_at']
    list_filter = ['is_active', 'industry', 'created_at']
    search_fields = ['name', 'contact_person', 'email', 'phone']
    list_editable = ['is_active']
    ordering = ['name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'contact_person', 'industry', 'is_active')
        }),
        ('Contact Details', {
            'fields': ('email', 'phone', 'address')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Presenter)
class PresenterAdmin(admin.ModelAdmin):
    list_display = ['display_name', 'full_name', 'organization', 'email', 'contract_type', 'is_active', 'is_available']
    list_filter = ['organization', 'contract_type', 'is_active', 'is_available', 'hire_date']
    search_fields = ['first_name', 'last_name', 'stage_name', 'email', 'specialties']
    list_editable = ['is_active', 'is_available']
    ordering = ['stage_name', 'first_name', 'last_name']
    readonly_fields = ['created_at', 'updated_at', 'active_shows_count', 'total_mentions_count']

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'organization', 'first_name', 'last_name', 'stage_name', 'email')
        }),
        ('Contact Information', {
            'fields': ('phone', 'emergency_contact', 'emergency_phone')
        }),
        ('Professional Information', {
            'fields': ('bio', 'specialties', 'profile_picture', 'experience_years', 'hire_date', 'contract_type')
        }),
        ('Status', {
            'fields': ('is_active', 'is_available')
        }),
        ('Social Media', {
            'fields': ('twitter_handle', 'instagram_handle', 'facebook_page'),
            'classes': ('collapse',)
        }),
        ('Statistics', {
            'fields': ('active_shows_count', 'total_mentions_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [PresenterAvailabilityInline, PresenterSkillInline, PresenterNoteInline]

    def save_formset(self, request, form, formset, change):
        instances = formset.save(commit=False)
        for instance in instances:
            if isinstance(instance, PresenterNote) and not instance.author_id:
                instance.author = request.user
            instance.save()
        formset.save_m2m()


@admin.register(PresenterAvailability)
class PresenterAvailabilityAdmin(admin.ModelAdmin):
    list_display = ['presenter', 'get_day_of_week_display', 'start_time', 'end_time', 'is_available']
    list_filter = ['day_of_week', 'is_available', 'presenter__organization']
    search_fields = ['presenter__first_name', 'presenter__last_name', 'presenter__stage_name']


@admin.register(PresenterSkill)
class PresenterSkillAdmin(admin.ModelAdmin):
    list_display = ['presenter', 'name', 'category', 'proficiency_level', 'certification']
    list_filter = ['category', 'proficiency_level', 'presenter__organization']
    search_fields = ['presenter__first_name', 'presenter__last_name', 'name', 'certification']


@admin.register(PresenterNote)
class PresenterNoteAdmin(admin.ModelAdmin):
    list_display = ['presenter', 'title', 'note_type', 'author', 'is_private', 'created_at']
    list_filter = ['note_type', 'is_private', 'created_at', 'presenter__organization']
    search_fields = ['presenter__first_name', 'presenter__last_name', 'title', 'content']
    readonly_fields = ['created_at', 'updated_at']

    def save_model(self, request, obj, form, change):
        if not obj.author_id:
            obj.author = request.user
        super().save_model(request, obj, form, change)
