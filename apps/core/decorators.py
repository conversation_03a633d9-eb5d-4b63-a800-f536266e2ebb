"""
Permission decorators for view-level access control
"""
from functools import wraps
from django.http import HttpResponseForbidden
from django.shortcuts import redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from apps.organizations.middleware import get_current_membership, user_has_permission


def require_permission(permission):
    """
    Decorator to require specific permission for view access
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            # Check if user has the required permission
            if not user_has_permission(request, permission):
                # Get current membership for better error message
                membership = get_current_membership(request)
                if membership:
                    messages.error(
                        request, 
                        f'Access denied. You need "{permission}" permission to access this page. '
                        f'Your current role is "{membership.get_role_display()}".'
                    )
                else:
                    messages.error(request, 'Access denied. No organization membership found.')
                
                return redirect('core:dashboard')
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def require_any_permission(permissions):
    """
    Decorator to require any of the specified permissions for view access
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            # Check if user has any of the required permissions
            has_permission = any(user_has_permission(request, perm) for perm in permissions)
            
            if not has_permission:
                membership = get_current_membership(request)
                if membership:
                    messages.error(
                        request, 
                        f'Access denied. You need one of these permissions: {", ".join(permissions)}. '
                        f'Your current role is "{membership.get_role_display()}".'
                    )
                else:
                    messages.error(request, 'Access denied. No organization membership found.')
                
                return redirect('core:dashboard')
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def require_role(role):
    """
    Decorator to require specific organization role for view access
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            membership = get_current_membership(request)
            
            if not membership or membership.role != role:
                if membership:
                    messages.error(
                        request, 
                        f'Access denied. This page requires "{role}" role. '
                        f'Your current role is "{membership.get_role_display()}".'
                    )
                else:
                    messages.error(request, 'Access denied. No organization membership found.')
                
                return redirect('core:dashboard')
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def require_any_role(roles):
    """
    Decorator to require any of the specified organization roles for view access
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            membership = get_current_membership(request)
            
            if not membership or membership.role not in roles:
                if membership:
                    messages.error(
                        request, 
                        f'Access denied. This page requires one of these roles: {", ".join(roles)}. '
                        f'Your current role is "{membership.get_role_display()}".'
                    )
                else:
                    messages.error(request, 'Access denied. No organization membership found.')
                
                return redirect('core:dashboard')
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def organization_required(view_func):
    """
    Decorator to ensure user has organization context
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        from apps.organizations.middleware import get_current_organization
        
        current_org = get_current_organization(request)
        if not current_org:
            messages.error(request, 'Please select an organization to continue.')
            return redirect('organizations:list')
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def presenter_only(view_func):
    """
    Decorator specifically for presenter-only views
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        membership = get_current_membership(request)
        
        if not membership or membership.role != 'presenter':
            if membership:
                messages.error(
                    request, 
                    f'Access denied. This page is only available to presenters. '
                    f'Your current role is "{membership.get_role_display()}".'
                )
            else:
                messages.error(request, 'Access denied. No organization membership found.')
            
            return redirect('core:dashboard')
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def admin_or_manager_required(view_func):
    """
    Decorator for admin or manager level access
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        membership = get_current_membership(request)
        
        if not membership or membership.role not in ['owner', 'admin', 'manager']:
            if membership:
                messages.error(
                    request, 
                    f'Access denied. This page requires admin or manager role. '
                    f'Your current role is "{membership.get_role_display()}".'
                )
            else:
                messages.error(request, 'Access denied. No organization membership found.')
            
            return redirect('core:dashboard')
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view


def owner_or_admin_required(view_func):
    """
    Decorator for owner or admin level access
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        membership = get_current_membership(request)
        
        if not membership or membership.role not in ['owner', 'admin']:
            if membership:
                messages.error(
                    request, 
                    f'Access denied. This page requires owner or admin role. '
                    f'Your current role is "{membership.get_role_display()}".'
                )
            else:
                messages.error(request, 'Access denied. No organization membership found.')
            
            return redirect('core:dashboard')
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view
