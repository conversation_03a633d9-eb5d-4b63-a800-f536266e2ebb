"""
Template tags for permission checking
"""
from django import template
from apps.organizations.middleware import get_current_membership, user_has_permission

register = template.Library()


@register.simple_tag(takes_context=True)
def has_permission(context, permission):
    """
    Template tag to check if user has specific permission
    Usage: {% has_permission 'manage_users' as can_manage %}
    """
    request = context['request']
    return user_has_permission(request, permission)


@register.simple_tag(takes_context=True)
def has_any_permission(context, *permissions):
    """
    Template tag to check if user has any of the specified permissions
    Usage: {% has_any_permission 'manage_users' 'view_users' as can_access %}
    """
    request = context['request']
    return any(user_has_permission(request, perm) for perm in permissions)


@register.simple_tag(takes_context=True)
def has_role(context, role):
    """
    Template tag to check if user has specific role
    Usage: {% has_role 'admin' as is_admin %}
    """
    request = context['request']
    membership = get_current_membership(request)
    return membership and membership.role == role


@register.simple_tag(takes_context=True)
def has_any_role(context, *roles):
    """
    Template tag to check if user has any of the specified roles
    Usage: {% has_any_role 'admin' 'manager' as is_admin_or_manager %}
    """
    request = context['request']
    membership = get_current_membership(request)
    return membership and membership.role in roles


@register.filter
def can_access_section(user_permissions, section):
    """
    Filter to check if user can access a specific section
    Usage: {{ user_permissions|can_access_section:'mentions' }}
    """
    section_permissions = {
        'mentions': ['view', 'manage_mentions'],
        'shows': ['view', 'manage_shows'],
        'clients': ['view', 'manage_clients'],
        'presenters': ['view', 'manage_presenters'],
        'users': ['manage_users'],
        'reports': ['view_reports'],
        'settings': ['manage_settings'],
        'analytics': ['view_analytics'],
    }
    
    required_perms = section_permissions.get(section, [])
    return any(perm in user_permissions for perm in required_perms)


@register.inclusion_tag('includes/permission_check.html', takes_context=True)
def permission_required(context, permission):
    """
    Inclusion tag for permission-based content rendering
    Usage: {% permission_required 'manage_users' %}...content...{% endpermission_required %}
    """
    request = context['request']
    has_perm = user_has_permission(request, permission)
    return {'has_permission': has_perm}


@register.inclusion_tag('includes/role_check.html', takes_context=True)
def role_required(context, role):
    """
    Inclusion tag for role-based content rendering
    Usage: {% role_required 'admin' %}...content...{% endrole_required %}
    """
    request = context['request']
    membership = get_current_membership(request)
    has_role = membership and membership.role == role
    return {'has_role': has_role}
