"""
Cache middleware for production optimization
"""
import time
import hashlib
import base64
from django.core.cache import cache
from django.http import HttpResponse
from django.utils.cache import get_cache_key
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class SmartCacheMiddleware(MiddlewareMixin):
    """
    Smart caching middleware that caches responses based on various criteria
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.cache_timeout = getattr(settings, 'CACHE_MIDDLEWARE_SECONDS', 300)
        self.cache_key_prefix = getattr(settings, 'CACHE_MIDDLEWARE_KEY_PREFIX', 'middleware')
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Check if we have a cached response for this request
        """
        # Skip caching for certain conditions
        if not self._should_cache_request(request):
            return None
        
        # Generate cache key
        cache_key = self._get_cache_key(request)
        
        # Try to get cached response
        try:
            cached_response = cache.get(cache_key)
            if cached_response is not None:
                logger.debug(f"Cache hit for {request.path}: {cache_key}")
                # Decode content from base64 if it's a string
                content = cached_response['content']
                if isinstance(content, str):
                    content = base64.b64decode(content.encode('utf-8'))

                # Add cache headers
                response = HttpResponse(
                    content,
                    content_type=cached_response['content_type'],
                    status=cached_response['status_code']
                )
                response['X-Cache'] = 'HIT'
                response['X-Cache-Key'] = cache_key
                return response
        except Exception as e:
            # Log cache error but continue with normal request processing
            logger.warning(f"Failed to retrieve cached response for {request.path}: {e}")
            # Continue with normal request processing
        
        return None
    
    def process_response(self, request, response):
        """
        Cache the response if appropriate
        """
        # Skip caching for certain conditions
        if not self._should_cache_response(request, response):
            return response
        
        # Generate cache key
        cache_key = self._get_cache_key(request)

        # Cache the response - encode bytes content as base64 string for JSON serialization
        try:
            content = response.content
            if isinstance(content, bytes):
                content = base64.b64encode(content).decode('utf-8')

            cached_data = {
                'content': content,
                'content_type': response.get('Content-Type', 'text/html'),
                'status_code': response.status_code,
                'timestamp': time.time()
            }

            cache.set(cache_key, cached_data, timeout=self.cache_timeout)
        except Exception as e:
            # Log cache error but don't break the response
            logger.warning(f"Failed to cache response for {request.path}: {e}")
            # Continue without caching
        logger.debug(f"Cache set for {request.path}: {cache_key}")
        
        # Add cache headers
        response['X-Cache'] = 'MISS'
        response['X-Cache-Key'] = cache_key
        
        return response
    
    def _should_cache_request(self, request):
        """
        Determine if we should try to serve a cached response
        """
        # Don't cache if user is authenticated (unless specifically allowed)
        # Check if user attribute exists (authentication middleware may not have run yet)
        if hasattr(request, 'user') and request.user.is_authenticated and not getattr(settings, 'CACHE_AUTHENTICATED_REQUESTS', False):
            return False
        
        # Don't cache POST, PUT, DELETE requests
        if request.method not in ['GET', 'HEAD']:
            return False
        
        # Don't cache if there are messages
        if hasattr(request, '_messages') and len(request._messages):
            return False
        
        # Don't cache admin pages
        if request.path.startswith('/admin/'):
            return False
        
        # Don't cache API endpoints (unless specifically configured)
        if request.path.startswith('/api/') and not getattr(settings, 'CACHE_API_RESPONSES', False):
            return False
        
        return True
    
    def _should_cache_response(self, request, response):
        """
        Determine if we should cache this response
        """
        # Only cache successful responses
        if response.status_code != 200:
            return False
        
        # Don't cache if response has Set-Cookie header
        if response.has_header('Set-Cookie'):
            return False
        
        # Don't cache if response is too large
        max_size = getattr(settings, 'CACHE_MAX_RESPONSE_SIZE', 1024 * 1024)  # 1MB default
        if len(response.content) > max_size:
            return False
        
        return self._should_cache_request(request)
    
    def _get_cache_key(self, request):
        """
        Generate a cache key for the request
        """
        # Include path, query string, and user info
        key_parts = [
            self.cache_key_prefix,
            request.path,
            request.GET.urlencode(),
        ]
        
        # Include user ID if authenticated and caching authenticated requests
        if hasattr(request, 'user') and request.user.is_authenticated and getattr(settings, 'CACHE_AUTHENTICATED_REQUESTS', False):
            key_parts.append(f"user:{request.user.id}")
        
        # Include organization if available
        if hasattr(request, 'organization') and request.organization:
            key_parts.append(f"org:{request.organization.id}")
        
        # Create hash of key parts
        key_string = ":".join(str(part) for part in key_parts)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        
        return f"page:{key_hash}"


class DatabaseQueryCacheMiddleware(MiddlewareMixin):
    """
    Middleware to cache database queries for the duration of a request
    """
    
    def process_request(self, request):
        """
        Initialize query cache for this request
        """
        request._query_cache = {}
        return None
    
    def process_response(self, request, response):
        """
        Clear query cache after request
        """
        if hasattr(request, '_query_cache'):
            del request._query_cache
        return response


class ETagMiddleware(MiddlewareMixin):
    """
    Middleware to add ETag headers for better caching
    """
    
    def process_response(self, request, response):
        """
        Add ETag header based on response content
        """
        # Only add ETag for GET requests
        if request.method != 'GET':
            return response
        
        # Only add ETag for successful responses
        if response.status_code != 200:
            return response
        
        # Don't add ETag if already present
        if response.has_header('ETag'):
            return response
        
        # Generate ETag from content hash
        content_hash = hashlib.md5(response.content).hexdigest()
        etag = f'"{content_hash}"'
        
        response['ETag'] = etag
        
        # Check if client has matching ETag
        if_none_match = request.META.get('HTTP_IF_NONE_MATCH')
        if if_none_match == etag:
            # Return 304 Not Modified
            response = HttpResponse(status=304)
            response['ETag'] = etag
        
        return response


class CompressionMiddleware(MiddlewareMixin):
    """
    Middleware to add compression headers
    """
    
    def process_response(self, request, response):
        """
        Add compression-related headers
        """
        # Add Vary header for Accept-Encoding
        if not response.has_header('Vary'):
            response['Vary'] = 'Accept-Encoding'
        elif 'Accept-Encoding' not in response['Vary']:
            response['Vary'] += ', Accept-Encoding'
        
        # Add Cache-Control headers for static content
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            response['Cache-Control'] = 'public, max-age=31536000'  # 1 year
        elif response.status_code == 200:
            response['Cache-Control'] = 'public, max-age=300'  # 5 minutes
        
        return response
