"""
Security middleware for production hardening
"""
import re
import time
import logging
from django.http import HttpResponseForbidden, HttpResponse
from django.core.cache import cache
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from django.contrib.auth import logout
from django.shortcuts import redirect

logger = logging.getLogger(__name__)


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Add security headers to all responses
    """
    
    def process_response(self, request, response):
        """
        Add security headers
        """
        # X-Content-Type-Options
        response['X-Content-Type-Options'] = 'nosniff'
        
        # X-Frame-Options (if not already set)
        if not response.has_header('X-Frame-Options'):
            response['X-Frame-Options'] = 'DENY'
        
        # X-XSS-Protection
        response['X-XSS-Protection'] = '1; mode=block'
        
        # Referrer Policy
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Permissions Policy (formerly Feature Policy)
        response['Permissions-Policy'] = (
            'geolocation=(), '
            'microphone=(), '
            'camera=(), '
            'payment=(), '
            'usb=(), '
            'magnetometer=(), '
            'gyroscope=(), '
            'accelerometer=()'
        )
        
        # Content Security Policy (basic)
        if not response.has_header('Content-Security-Policy'):
            csp = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com; "
                "font-src 'self' https://cdn.jsdelivr.net https://fonts.gstatic.com; "
                "img-src 'self' data: https:; "
                "connect-src 'self'; "
                "frame-ancestors 'none'; "
                "base-uri 'self'; "
                "form-action 'self';"
            )
            response['Content-Security-Policy'] = csp
        
        # HSTS (only over HTTPS)
        if request.is_secure():
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'
        
        return response
    
class RateLimitMiddleware(MiddlewareMixin):
    """
    Global rate limiting middleware based on client IP and request type.
    """

    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limits = {
            'default': (1000, 60),   # 1000 requests per minute
            'login': (100, 300),     # 100 login attempts per 5 minutes
            'api': (5000, 3600),     # 5000 API requests per hour
            'upload': (100, 60),     # 100 uploads per minute
        }
        self.trusted_ips = ['127.0.0.1', '::1']  # Add staging IPs if needed
        super().__init__(get_response)

    def process_request(self, request):
        ip = self._get_client_ip(request)

        # Skip rate limiting for trusted IPs or staff/superusers
        if ip in self.trusted_ips:
            return None
        if hasattr(request, 'user') and request.user.is_authenticated:
            if request.user.is_staff or request.user.is_superuser:
                return None

        limit_type = self._get_limit_type(request)

        if self._is_rate_limited(ip, limit_type):
            logger.warning(f"⚠️ Rate limit exceeded for IP {ip} on {request.path} [{limit_type}]")
            return HttpResponseForbidden("Rate limit exceeded. Please try again later.")

        return None

    def _get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def _get_limit_type(self, request):
        path = request.path
        if path.startswith('/accounts/login/'):
            return 'login'
        elif path.startswith('/api/'):
            return 'api'
        elif request.method == 'POST' and 'upload' in path:
            return 'upload'
        else:
            return 'default'

    def _is_rate_limited(self, ip, limit_type):
        max_requests, period = self.rate_limits.get(limit_type, self.rate_limits['default'])
        cache_key = f"rate_limit:{limit_type}:{ip}"
        current_requests = cache.get(cache_key, 0)

        logger.debug(f"[RateLimit] IP={ip} Type={limit_type} Count={current_requests}/{max_requests}")

        if current_requests >= max_requests:
            return True

        cache.set(cache_key, current_requests + 1, timeout=period)
        return False


# class RateLimitMiddleware(MiddlewareMixin):
#     """
#     Rate limiting middleware to prevent abuse
#     """
    
#     def __init__(self, get_response):
#         self.get_response = get_response
#         # Rate limits: (requests, period_in_seconds)
#         self.rate_limits = {
#             'default': (100, 60),  # 100 requests per minute
#             'login': (5, 300),     # 5 login attempts per 5 minutes
#             'api': (1000, 3600),   # 1000 API requests per hour
#             'upload': (10, 60),    # 10 uploads per minute
#         }
#         super().__init__(get_response)
    
#     def process_request(self, request):
#         """
#         Check rate limits before processing request
#         """
#         # Get client IP
#         ip = self._get_client_ip(request)
        
#         # Determine rate limit type
#         limit_type = self._get_limit_type(request)
        
#         # Check rate limit
#         if self._is_rate_limited(ip, limit_type):
#             logger.warning(f"Rate limit exceeded for IP {ip} on {request.path}")
#             return HttpResponseForbidden("Rate limit exceeded. Please try again later.")
        
#         return None
    
#     def _get_client_ip(self, request):
#         """
#         Get the client's IP address
#         """
#         x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
#         if x_forwarded_for:
#             ip = x_forwarded_for.split(',')[0].strip()
#         else:
#             ip = request.META.get('REMOTE_ADDR')
#         return ip
    
#     def _get_limit_type(self, request):
#         """
#         Determine which rate limit to apply
#         """
#         if request.path.startswith('/accounts/login/'):
#             return 'login'
#         elif request.path.startswith('/api/'):
#             return 'api'
#         elif request.method == 'POST' and 'upload' in request.path:
#             return 'upload'
#         else:
#             return 'default'
    
#     def _is_rate_limited(self, ip, limit_type):
#         """
#         Check if IP is rate limited for the given type
#         """
#         max_requests, period = self.rate_limits.get(limit_type, self.rate_limits['default'])
        
#         cache_key = f"rate_limit:{limit_type}:{ip}"
#         current_requests = cache.get(cache_key, 0)
        
#         if current_requests >= max_requests:
#             return True
        
#         # Increment counter
#         cache.set(cache_key, current_requests + 1, timeout=period)
#         return False


class SuspiciousActivityMiddleware(MiddlewareMixin):
    """
    Detect and block suspicious activity
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        # Suspicious patterns
        self.suspicious_patterns = [
            r'\.\./',  # Directory traversal
            r'<script',  # XSS attempts
            r'union\s+select',  # SQL injection
            r'drop\s+table',  # SQL injection
            r'exec\s*\(',  # Code execution
            r'eval\s*\(',  # Code execution
            r'system\s*\(',  # System commands
        ]
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.suspicious_patterns]
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Check for suspicious activity
        """
        # Check URL path
        if self._is_suspicious(request.path):
            self._log_suspicious_activity(request, 'Suspicious URL path')
            return HttpResponseForbidden("Forbidden")
        
        # Check query parameters
        for key, value in request.GET.items():
            if self._is_suspicious(value):
                self._log_suspicious_activity(request, f'Suspicious query parameter: {key}')
                return HttpResponseForbidden("Forbidden")
        
        # Check POST data
        if request.method == 'POST':
            for key, value in request.POST.items():
                if isinstance(value, str) and self._is_suspicious(value):
                    self._log_suspicious_activity(request, f'Suspicious POST data: {key}')
                    return HttpResponseForbidden("Forbidden")
        
        return None
    
    def _is_suspicious(self, text):
        """
        Check if text contains suspicious patterns
        """
        for pattern in self.compiled_patterns:
            if pattern.search(text):
                return True
        return False
    
    def _log_suspicious_activity(self, request, reason):
        """
        Log suspicious activity
        """
        ip = request.META.get('REMOTE_ADDR', 'unknown')
        user_agent = request.META.get('HTTP_USER_AGENT', 'unknown')
        
        logger.warning(
            f"Suspicious activity detected: {reason} | "
            f"IP: {ip} | "
            f"Path: {request.path} | "
            f"User-Agent: {user_agent}"
        )


class SessionSecurityMiddleware(MiddlewareMixin):
    """
    Enhanced session security
    """
    
    def process_request(self, request):
        """
        Check session security
        """
        # Check if user attribute exists and is authenticated
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return None
        
        # Check session age
        session_start = request.session.get('session_start')
        if not session_start:
            request.session['session_start'] = time.time()
        else:
            # Check if session is too old
            max_age = getattr(settings, 'SESSION_MAX_AGE', 24 * 60 * 60)  # 24 hours
            if time.time() - session_start > max_age:
                logout(request)
                return redirect('account_login')
        
        # Check IP consistency
        session_ip = request.session.get('session_ip')
        current_ip = request.META.get('REMOTE_ADDR')
        
        if not session_ip:
            request.session['session_ip'] = current_ip
        elif session_ip != current_ip and getattr(settings, 'SESSION_IP_CHECK', True):
            # IP changed - potential session hijacking
            logger.warning(f"Session IP changed for user {request.user.id}: {session_ip} -> {current_ip}")
            logout(request)
            return redirect('account_login')
        
        # Update last activity
        request.session['last_activity'] = time.time()
        
        return None


class BruteForceProtectionMiddleware(MiddlewareMixin):
    """
    Protection against brute force attacks
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.max_attempts = 5
        self.lockout_duration = 300  # 5 minutes
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Check for brute force attempts
        """
        if request.path.startswith('/accounts/login/') and request.method == 'POST':
            ip = request.META.get('REMOTE_ADDR')
            
            # Check if IP is locked out
            lockout_key = f"lockout:{ip}"
            if cache.get(lockout_key):
                logger.warning(f"Blocked login attempt from locked out IP: {ip}")
                return HttpResponseForbidden("Too many failed login attempts. Please try again later.")
            
            # Check failed attempts
            attempts_key = f"login_attempts:{ip}"
            attempts = cache.get(attempts_key, 0)
            
            if attempts >= self.max_attempts:
                # Lock out the IP
                cache.set(lockout_key, True, timeout=self.lockout_duration)
                cache.delete(attempts_key)
                logger.warning(f"IP {ip} locked out after {attempts} failed login attempts")
                return HttpResponseForbidden("Too many failed login attempts. Please try again later.")
        
        return None
    
    def process_response(self, request, response):
        """
        Track failed login attempts
        """
        if (request.path.startswith('/accounts/login/') and 
            request.method == 'POST' and 
            response.status_code == 200 and 
            'form' in response.content.decode('utf-8', errors='ignore')):
            
            # Login failed (form is still present)
            ip = request.META.get('REMOTE_ADDR')
            attempts_key = f"login_attempts:{ip}"
            attempts = cache.get(attempts_key, 0) + 1
            cache.set(attempts_key, attempts, timeout=self.lockout_duration)
            
            logger.info(f"Failed login attempt #{attempts} from IP: {ip}")
        
        elif (request.path.startswith('/accounts/login/') and 
              request.method == 'POST' and 
              response.status_code == 302):
            
            # Login successful - clear attempts
            ip = request.META.get('REMOTE_ADDR')
            attempts_key = f"login_attempts:{ip}"
            cache.delete(attempts_key)
        
        return response
