from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse


class TimeStampedModel(models.Model):
    """Abstract base class with created_at and updated_at fields"""
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True


class Industry(TimeStampedModel):
    """Model for client industries"""
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='industries'
    )
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=50, help_text="Unique code for this industry (e.g., 'automotive', 'healthcare')")
    description = models.TextField(blank=True, help_text="Optional description of this industry")
    color = models.CharField(
        max_length=7,
        default='#3b82f6',
        help_text="Hex color code for this industry (e.g., #3b82f6)"
    )
    is_active = models.<PERSON><PERSON>an<PERSON>ield(default=True)

    class Meta:
        ordering = ['organization', 'name']
        unique_together = ['organization', 'code']
        verbose_name = 'Industry'
        verbose_name_plural = 'Industries'

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('core:industry_detail', kwargs={'pk': self.pk})


class Client(TimeStampedModel):
    """Model for radio station clients"""

    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='clients'
    )
    branch = models.ForeignKey(
        'organizations.Branch',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='clients'
    )
    name = models.CharField(max_length=200)
    contact_person = models.CharField(max_length=100)
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    address = models.TextField(blank=True)
    # Industry field - references Industry model via code
    industry = models.CharField(max_length=50, blank=True, help_text="Industry code from the Industries endpoint")
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['organization', 'name']
        unique_together = ['organization', 'name']
        indexes = [
            models.Index(fields=['organization'], name='client_organization_idx'),
            models.Index(fields=['is_active'], name='client_is_active_idx'),
            models.Index(fields=['industry'], name='client_industry_idx'),
            models.Index(fields=['email'], name='client_email_idx'),
            models.Index(fields=['organization', 'is_active'], name='client_org_active_idx'),
            models.Index(fields=['name'], name='client_name_idx'),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('core:client_detail', kwargs={'pk': self.pk})

    @property
    def total_mentions(self):
        return self.mention_set.count()

    @property
    def active_mentions(self):
        return self.mention_set.filter(status__in=['pending', 'scheduled']).count()

    @classmethod
    def get_industry_choices(cls, organization=None):
        """Get industry choices from the Industries endpoint (/industries/)"""
        choices = [('', 'Select Industry')]

        if organization:
            # Get all industries from the Industry model (managed via /industries/ endpoint)
            industries = Industry.objects.filter(
                organization=organization,
                is_active=True
            ).order_by('name')

            for industry in industries:
                choices.append((industry.code, industry.name))

        return choices

    @property
    def industry_name(self):
        """Get industry name from the Industries endpoint (/industries/)"""
        if self.industry and self.organization:
            # Look up industry from the Industry model
            industry = Industry.objects.filter(
                organization=self.organization,
                code=self.industry,
                is_active=True
            ).first()
            if industry:
                return industry.name
        return "Not specified"


class Presenter(TimeStampedModel):
    """Model for radio presenters"""
    from django.contrib.auth.models import User
    from django.core.validators import RegexValidator

    # Link to user account (optional for existing presenters)
    user = models.OneToOneField(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='presenter_profile'
    )

    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='presenters'
    )
    branch = models.ForeignKey(
        'organizations.Branch',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='presenters'
    )

    # Basic Information
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    stage_name = models.CharField(max_length=100, blank=True, help_text="Professional name used on air")
    email = models.EmailField()

    # Contact Information
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+*********'. Up to 15 digits allowed.")
    phone = models.CharField(validators=[phone_regex], max_length=17, blank=True)
    emergency_contact = models.CharField(max_length=100, blank=True)
    emergency_phone = models.CharField(validators=[phone_regex], max_length=17, blank=True)

    # Professional Information
    bio = models.TextField(blank=True, help_text="Presenter biography")
    specialties = models.CharField(max_length=200, blank=True, help_text="Areas of expertise (e.g., Music, News, Sports)")
    profile_picture = models.ImageField(upload_to='presenters/', blank=True)
    experience_years = models.PositiveIntegerField(default=0)

    # Professional Details
    hire_date = models.DateField(null=True, blank=True)
    contract_type = models.CharField(max_length=20, choices=[
        ('full_time', 'Full Time'),
        ('part_time', 'Part Time'),
        ('freelance', 'Freelance'),
        ('volunteer', 'Volunteer'),
    ], default='full_time')

    # Status and Availability
    is_active = models.BooleanField(default=True)
    is_available = models.BooleanField(default=True, help_text="Currently available for shows")

    # Social Media
    twitter_handle = models.CharField(max_length=50, blank=True)
    instagram_handle = models.CharField(max_length=50, blank=True)
    facebook_page = models.URLField(blank=True)

    class Meta:
        ordering = ['stage_name', 'first_name', 'last_name']
        unique_together = ['organization', 'email']
        indexes = [
            models.Index(fields=['organization'], name='presenter_organization_idx'),
            models.Index(fields=['is_active'], name='presenter_is_active_idx'),
            models.Index(fields=['is_available'], name='presenter_is_available_idx'),
            models.Index(fields=['user'], name='presenter_user_idx'),
            models.Index(fields=['email'], name='presenter_email_idx'),
            models.Index(fields=['organization', 'is_active'], name='presenter_org_active_idx'),
            models.Index(fields=['branch'], name='presenter_branch_idx'),
            models.Index(fields=['hire_date'], name='presenter_hire_date_idx'),
        ]

    def __str__(self):
        return f"{self.stage_name or self.full_name} ({self.organization.name})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def display_name(self):
        return self.stage_name if self.stage_name else self.full_name

    def get_absolute_url(self):
        return reverse('core:presenter_detail', kwargs={'pk': self.pk})

    @property
    def total_shows(self):
        return self.showpresenter_set.filter(is_active=True).count()

    @property
    def active_shows_count(self):
        return self.showpresenter_set.filter(is_active=True).count()

    @property
    def total_mentions_read(self):
        return self.mentionreading_set.filter(actual_read_time__isnull=False).count()

    @property
    def total_mentions_count(self):
        from apps.mentions.models import MentionReading
        return MentionReading.objects.filter(presenter=self).values('mention').distinct().count()


class PresenterAvailability(TimeStampedModel):
    """Model for presenter availability schedules"""

    DAYS_OF_WEEK = [
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    ]

    presenter = models.ForeignKey(Presenter, on_delete=models.CASCADE, related_name='availability')
    day_of_week = models.IntegerField(choices=DAYS_OF_WEEK)
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_available = models.BooleanField(default=True)
    notes = models.TextField(blank=True, help_text="Additional notes about availability")

    class Meta:
        ordering = ['day_of_week', 'start_time']
        unique_together = ['presenter', 'day_of_week', 'start_time']

    def __str__(self):
        return f"{self.presenter.display_name} - {self.get_day_of_week_display()} {self.start_time}-{self.end_time}"


class PresenterSkill(TimeStampedModel):
    """Model for presenter skills and certifications"""

    SKILL_CATEGORIES = [
        ('technical', 'Technical'),
        ('broadcasting', 'Broadcasting'),
        ('music', 'Music'),
        ('journalism', 'Journalism'),
        ('language', 'Language'),
        ('other', 'Other'),
    ]

    presenter = models.ForeignKey(Presenter, on_delete=models.CASCADE, related_name='skills')
    name = models.CharField(max_length=100)
    category = models.CharField(max_length=20, choices=SKILL_CATEGORIES, default='other')
    proficiency_level = models.CharField(max_length=20, choices=[
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    ], default='intermediate')

    certification = models.CharField(max_length=200, blank=True, help_text="Certification or qualification details")
    date_acquired = models.DateField(null=True, blank=True)

    class Meta:
        ordering = ['category', 'name']
        unique_together = ['presenter', 'name']

    def __str__(self):
        return f"{self.presenter.display_name} - {self.name} ({self.proficiency_level})"


class PresenterNote(TimeStampedModel):
    """Model for internal notes about presenters"""
    from django.contrib.auth.models import User

    presenter = models.ForeignKey(Presenter, on_delete=models.CASCADE, related_name='notes')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='presenter_notes')
    title = models.CharField(max_length=200)
    content = models.TextField()

    # Note types
    NOTE_TYPES = [
        ('general', 'General'),
        ('performance', 'Performance Review'),
        ('feedback', 'Feedback'),
        ('training', 'Training'),
        ('disciplinary', 'Disciplinary'),
        ('achievement', 'Achievement'),
    ]
    note_type = models.CharField(max_length=20, choices=NOTE_TYPES, default='general')

    is_private = models.BooleanField(default=False, help_text="Only visible to management")

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.presenter.display_name} - {self.title}"


class PresenterPersonalNote(TimeStampedModel):
    """Model for personal notes created by presenters for themselves"""
    from django.contrib.auth.models import User

    # Relationships
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='personal_notes')
    presenter = models.ForeignKey(Presenter, on_delete=models.CASCADE, related_name='personal_notes')

    # Note content
    title = models.CharField(max_length=200, help_text="Title for your note")
    content = models.TextField(help_text="Note content and details")

    # Categories for organization
    CATEGORY_CHOICES = [
        ('general', 'General'),
        ('show_prep', 'Show Preparation'),
        ('reminders', 'Reminders'),
        ('ideas', 'Ideas'),
        ('contacts', 'Contacts'),
        ('research', 'Research'),
        ('personal', 'Personal'),
        ('technical', 'Technical'),
    ]
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='general')

    # Priority levels
    PRIORITY_CHOICES = [
        (1, 'Low'),
        (2, 'Normal'),
        (3, 'High'),
        (4, 'Urgent'),
    ]
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2)

    # Status tracking
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('archived', 'Archived'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')

    # Optional due date for reminders
    due_date = models.DateTimeField(null=True, blank=True, help_text="Optional due date for this note")

    # Flags
    is_pinned = models.BooleanField(default=False, help_text="Pin this note to the top")
    is_private = models.BooleanField(default=True, help_text="Keep this note private (only visible to you)")

    # Tags for better organization (JSON field for flexibility)
    tags = models.JSONField(default=list, blank=True, help_text="Tags for organizing notes")

    class Meta:
        ordering = ['-is_pinned', '-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['presenter', 'category']),
            models.Index(fields=['created_at']),
            models.Index(fields=['due_date']),
        ]

    def __str__(self):
        return f"{self.title} - {self.user.username}"

    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('core:personal_note_detail', kwargs={'pk': self.pk})

    @property
    def is_overdue(self):
        """Check if note is overdue"""
        if self.due_date:
            from django.utils import timezone
            return timezone.now() > self.due_date
        return False

    @property
    def priority_display(self):
        """Get priority display with color class"""
        priority_map = {
            1: {'label': 'Low', 'class': 'text-gray-600'},
            2: {'label': 'Normal', 'class': 'text-blue-600'},
            3: {'label': 'High', 'class': 'text-orange-600'},
            4: {'label': 'Urgent', 'class': 'text-red-600'},
        }
        return priority_map.get(self.priority, {'label': 'Normal', 'class': 'text-blue-600'})


class ShowNote(TimeStampedModel):
    """Model for show-specific notes created by presenters for individual shows"""
    from django.contrib.auth.models import User

    # Relationships
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='show_notes')
    presenter = models.ForeignKey(Presenter, on_delete=models.CASCADE, related_name='show_notes')
    show = models.ForeignKey('shows.Show', on_delete=models.CASCADE, related_name='presenter_notes')

    # Show-specific context
    show_date = models.DateField(help_text="Specific date this note applies to")

    # Note content
    title = models.CharField(max_length=200, help_text="Title for your show note")
    content = models.TextField(help_text="Note content and details for this specific show")

    # Categories specific to show preparation
    CATEGORY_CHOICES = [
        ('preparation', 'Show Preparation'),
        ('reminders', 'Reminders'),
        ('guests', 'Guest Information'),
        ('music', 'Music Notes'),
        ('segments', 'Segment Notes'),
        ('technical', 'Technical Notes'),
        ('follow_up', 'Follow-up Items'),
        ('general', 'General'),
    ]
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='preparation')

    # Priority levels
    PRIORITY_CHOICES = [
        (1, 'Low'),
        (2, 'Normal'),
        (3, 'High'),
        (4, 'Urgent'),
    ]
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2)

    # Status tracking
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('archived', 'Archived'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')

    # Flags
    is_pinned = models.BooleanField(default=False, help_text="Pin this note for this show")

    # Tags for better organization (JSON field for flexibility)
    tags = models.JSONField(default=list, blank=True, help_text="Tags for organizing show notes")

    class Meta:
        ordering = ['-is_pinned', '-created_at']
        unique_together = ['user', 'show', 'show_date', 'title']  # Prevent duplicate titles for same show/date
        indexes = [
            models.Index(fields=['user', 'show', 'show_date']),
            models.Index(fields=['presenter', 'show_date']),
            models.Index(fields=['show', 'show_date']),
            models.Index(fields=['created_at']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.title} - {self.show.name} ({self.show_date})"

    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('core:show_note_detail', kwargs={'pk': self.pk})

    @property
    def is_for_today(self):
        """Check if this note is for today's show"""
        from django.utils import timezone
        return self.show_date == timezone.now().date()

    @property
    def is_for_future(self):
        """Check if this note is for a future show"""
        from django.utils import timezone
        return self.show_date > timezone.now().date()

    @property
    def days_until_show(self):
        """Get number of days until the show"""
        from django.utils import timezone
        delta = self.show_date - timezone.now().date()
        return delta.days

    @property
    def priority_display(self):
        """Get priority display with color class"""
        priority_map = {
            1: {'label': 'Low', 'class': 'text-gray-600'},
            2: {'label': 'Normal', 'class': 'text-blue-600'},
            3: {'label': 'High', 'class': 'text-orange-600'},
            4: {'label': 'Urgent', 'class': 'text-red-600'},
        }
        return priority_map.get(self.priority, {'label': 'Normal', 'class': 'text-blue-600'})
