# Generated by Django 4.2.7 on 2025-06-11 07:08

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("organizations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Presenter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("first_name", models.Char<PERSON>ield(max_length=50)),
                ("last_name", models.Char<PERSON>ield(max_length=50)),
                (
                    "stage_name",
                    models.CharField(
                        blank=True,
                        help_text="Professional name used on air",
                        max_length=100,
                    ),
                ),
                ("email", models.<PERSON><PERSON><PERSON>ield(max_length=254)),
                (
                    "phone",
                    models.Char<PERSON>ield(
                        blank=True,
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                ("emergency_contact", models.CharField(blank=True, max_length=100)),
                (
                    "emergency_phone",
                    models.CharField(
                        blank=True,
                        max_length=17,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.",
                                regex="^\\+?1?\\d{9,15}$",
                            )
                        ],
                    ),
                ),
                ("bio", models.TextField(blank=True, help_text="Presenter biography")),
                (
                    "specialties",
                    models.CharField(
                        blank=True,
                        help_text="Areas of expertise (e.g., Music, News, Sports)",
                        max_length=200,
                    ),
                ),
                (
                    "profile_picture",
                    models.ImageField(blank=True, upload_to="presenters/"),
                ),
                ("experience_years", models.PositiveIntegerField(default=0)),
                ("hire_date", models.DateField(blank=True, null=True)),
                (
                    "contract_type",
                    models.CharField(
                        choices=[
                            ("full_time", "Full Time"),
                            ("part_time", "Part Time"),
                            ("freelance", "Freelance"),
                            ("volunteer", "Volunteer"),
                        ],
                        default="full_time",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_available",
                    models.BooleanField(
                        default=True, help_text="Currently available for shows"
                    ),
                ),
                ("twitter_handle", models.CharField(blank=True, max_length=50)),
                ("instagram_handle", models.CharField(blank=True, max_length=50)),
                ("facebook_page", models.URLField(blank=True)),
                (
                    "branch",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="presenters",
                        to="organizations.branch",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="presenters",
                        to="organizations.organization",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="presenter_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["stage_name", "first_name", "last_name"],
                "unique_together": {("organization", "email")},
            },
        ),
        migrations.CreateModel(
            name="PresenterNote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("title", models.CharField(max_length=200)),
                ("content", models.TextField()),
                (
                    "note_type",
                    models.CharField(
                        choices=[
                            ("general", "General"),
                            ("performance", "Performance Review"),
                            ("feedback", "Feedback"),
                            ("training", "Training"),
                            ("disciplinary", "Disciplinary"),
                            ("achievement", "Achievement"),
                        ],
                        default="general",
                        max_length=20,
                    ),
                ),
                (
                    "is_private",
                    models.BooleanField(
                        default=False, help_text="Only visible to management"
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="presenter_notes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "presenter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notes",
                        to="core.presenter",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PresenterSkill",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=100)),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("technical", "Technical"),
                            ("broadcasting", "Broadcasting"),
                            ("music", "Music"),
                            ("journalism", "Journalism"),
                            ("language", "Language"),
                            ("other", "Other"),
                        ],
                        default="other",
                        max_length=20,
                    ),
                ),
                (
                    "proficiency_level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                            ("expert", "Expert"),
                        ],
                        default="intermediate",
                        max_length=20,
                    ),
                ),
                (
                    "certification",
                    models.CharField(
                        blank=True,
                        help_text="Certification or qualification details",
                        max_length=200,
                    ),
                ),
                ("date_acquired", models.DateField(blank=True, null=True)),
                (
                    "presenter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="skills",
                        to="core.presenter",
                    ),
                ),
            ],
            options={
                "ordering": ["category", "name"],
                "unique_together": {("presenter", "name")},
            },
        ),
        migrations.CreateModel(
            name="PresenterAvailability",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "day_of_week",
                    models.IntegerField(
                        choices=[
                            (0, "Monday"),
                            (1, "Tuesday"),
                            (2, "Wednesday"),
                            (3, "Thursday"),
                            (4, "Friday"),
                            (5, "Saturday"),
                            (6, "Sunday"),
                        ]
                    ),
                ),
                ("start_time", models.TimeField()),
                ("end_time", models.TimeField()),
                ("is_available", models.BooleanField(default=True)),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Additional notes about availability"
                    ),
                ),
                (
                    "presenter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="availability",
                        to="core.presenter",
                    ),
                ),
            ],
            options={
                "ordering": ["day_of_week", "start_time"],
                "unique_together": {("presenter", "day_of_week", "start_time")},
            },
        ),
        migrations.CreateModel(
            name="Client",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=200)),
                ("contact_person", models.CharField(max_length=100)),
                ("email", models.EmailField(max_length=254)),
                ("phone", models.CharField(max_length=20)),
                ("address", models.TextField(blank=True)),
                ("industry", models.CharField(blank=True, max_length=100)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "branch",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="clients",
                        to="organizations.branch",
                    ),
                ),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="clients",
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "ordering": ["organization", "name"],
                "unique_together": {("organization", "name")},
            },
        ),
    ]
