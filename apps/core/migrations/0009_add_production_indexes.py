# Generated by Django 4.2.7 on 2025-06-22 00:13

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0008_alter_client_industry"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="client",
            index=models.Index(fields=["organization"], name="client_organization_idx"),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(fields=["is_active"], name="client_is_active_idx"),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(fields=["industry"], name="client_industry_idx"),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(fields=["email"], name="client_email_idx"),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(
                fields=["organization", "is_active"], name="client_org_active_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(fields=["name"], name="client_name_idx"),
        ),
        migrations.AddIndex(
            model_name="presenter",
            index=models.Index(
                fields=["organization"], name="presenter_organization_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="presenter",
            index=models.Index(fields=["is_active"], name="presenter_is_active_idx"),
        ),
        migrations.AddIndex(
            model_name="presenter",
            index=models.Index(
                fields=["is_available"], name="presenter_is_available_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="presenter",
            index=models.Index(fields=["user"], name="presenter_user_idx"),
        ),
        migrations.AddIndex(
            model_name="presenter",
            index=models.Index(fields=["email"], name="presenter_email_idx"),
        ),
        migrations.AddIndex(
            model_name="presenter",
            index=models.Index(
                fields=["organization", "is_active"], name="presenter_org_active_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="presenter",
            index=models.Index(fields=["branch"], name="presenter_branch_idx"),
        ),
        migrations.AddIndex(
            model_name="presenter",
            index=models.Index(fields=["hire_date"], name="presenter_hire_date_idx"),
        ),
    ]
