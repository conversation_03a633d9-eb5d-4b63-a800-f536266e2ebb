# Generated by Django 4.2.7 on 2025-06-18 17:47

from django.db import migrations


def populate_industries_and_migrate_data(apps, schema_editor):
    """Create default industries for all organizations and migrate existing client data"""
    Industry = apps.get_model('core', 'Industry')
    Organization = apps.get_model('organizations', 'Organization')
    Client = apps.get_model('core', 'Client')

    # Default industries from the original INDUSTRY_CHOICES
    default_industries = [
        ('automotive', 'Automotive'),
        ('banking_finance', 'Banking & Finance'),
        ('construction', 'Construction'),
        ('education', 'Education'),
        ('entertainment', 'Entertainment'),
        ('fashion_retail', 'Fashion & Retail'),
        ('food_beverage', 'Food & Beverage'),
        ('government', 'Government'),
        ('healthcare', 'Healthcare'),
        ('hospitality_tourism', 'Hospitality & Tourism'),
        ('insurance', 'Insurance'),
        ('legal_services', 'Legal Services'),
        ('manufacturing', 'Manufacturing'),
        ('media_advertising', 'Media & Advertising'),
        ('non_profit', 'Non-Profit'),
        ('real_estate', 'Real Estate'),
        ('retail', 'Retail'),
        ('sports_recreation', 'Sports & Recreation'),
        ('technology', 'Technology'),
        ('telecommunications', 'Telecommunications'),
        ('transportation', 'Transportation'),
        ('utilities', 'Utilities'),
        ('other', 'Other'),
    ]

    # Create industries for each organization and migrate client data
    for organization in Organization.objects.all():
        industry_mapping = {}

        # Create industries
        for code, name in default_industries:
            industry, created = Industry.objects.get_or_create(
                organization=organization,
                code=code,
                defaults={
                    'name': name,
                    'is_active': True
                }
            )
            industry_mapping[code] = industry

        # Migrate existing client industry data
        for client in Client.objects.filter(organization=organization):
            if client.industry and client.industry in industry_mapping:
                # Set the new_industry field based on the old industry value
                client.new_industry = industry_mapping[client.industry]
                client.save()


def reverse_populate_industries_and_migrate_data(apps, schema_editor):
    """Reverse the population and migration"""
    Industry = apps.get_model('core', 'Industry')
    Client = apps.get_model('core', 'Client')

    # Clear new_industry field for all clients
    Client.objects.update(new_industry=None)

    # Delete all industries
    Industry.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0003_add_industry_model_and_new_field"),
        ("organizations", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(populate_industries_and_migrate_data, reverse_populate_industries_and_migrate_data),
    ]
