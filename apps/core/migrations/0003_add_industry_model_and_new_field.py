# Generated by Django 4.2.7 on 2025-06-18 17:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("organizations", "0001_initial"),
        ("core", "0002_update_client_industry_choices"),
    ]

    operations = [
        migrations.CreateModel(
            name="Industry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=100)),
                (
                    "code",
                    models.CharField(
                        help_text="Unique code for this industry (e.g., 'automotive', 'healthcare')",
                        max_length=50,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True, help_text="Optional description of this industry"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "organization",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="industries",
                        to="organizations.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Industry",
                "verbose_name_plural": "Industries",
                "ordering": ["organization", "name"],
                "unique_together": {("organization", "code")},
            },
        ),
        migrations.AddField(
            model_name="client",
            name="new_industry",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="clients",
                to="core.industry",
            ),
        ),
    ]
