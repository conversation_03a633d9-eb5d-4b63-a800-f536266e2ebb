# Generated by Django 4.2.7 on 2025-07-09 22:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("core", "0010_add_color_to_industry"),
    ]

    operations = [
        migrations.CreateModel(
            name="PresenterPersonalNote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "title",
                    models.Char<PERSON>ield(help_text="Title for your note", max_length=200),
                ),
                ("content", models.TextField(help_text="Note content and details")),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("general", "General"),
                            ("show_prep", "Show Preparation"),
                            ("reminders", "Reminders"),
                            ("ideas", "Ideas"),
                            ("contacts", "Contacts"),
                            ("research", "Research"),
                            ("personal", "Personal"),
                            ("technical", "Technical"),
                        ],
                        default="general",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.IntegerField(
                        choices=[(1, "Low"), (2, "Normal"), (3, "High"), (4, "Urgent")],
                        default=2,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("archived", "Archived"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "due_date",
                    models.DateTimeField(
                        blank=True,
                        help_text="Optional due date for this note",
                        null=True,
                    ),
                ),
                (
                    "is_pinned",
                    models.BooleanField(
                        default=False, help_text="Pin this note to the top"
                    ),
                ),
                (
                    "is_private",
                    models.BooleanField(
                        default=True,
                        help_text="Keep this note private (only visible to you)",
                    ),
                ),
                (
                    "tags",
                    models.JSONField(
                        blank=True, default=list, help_text="Tags for organizing notes"
                    ),
                ),
                (
                    "presenter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="personal_notes",
                        to="core.presenter",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="personal_notes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-is_pinned", "-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "status"], name="core_presen_user_id_e487d4_idx"
                    ),
                    models.Index(
                        fields=["presenter", "category"],
                        name="core_presen_present_a11a8f_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="core_presen_created_07d2da_idx"
                    ),
                    models.Index(
                        fields=["due_date"], name="core_presen_due_dat_c1a599_idx"
                    ),
                ],
            },
        ),
    ]
