# Generated by Django 4.2.7 on 2025-06-18 17:59

from django.db import migrations, models


def copy_industry_to_legacy_and_clear(apps, schema_editor):
    """Copy old industry CharField data to legacy_industry and clear industry field"""
    Client = apps.get_model('core', 'Client')

    # Copy industry data to legacy_industry and clear industry
    for client in Client.objects.all():
        if client.industry:  # If industry has a value (CharField)
            client.legacy_industry = client.industry
            client.industry = ''  # Clear the field
            client.save()


def reverse_copy_industry_to_legacy(apps, schema_editor):
    """Reverse the operation"""
    Client = apps.get_model('core', 'Client')

    # Copy legacy_industry back to industry
    for client in Client.objects.all():
        if client.legacy_industry:
            client.industry = client.legacy_industry
            client.legacy_industry = None
            client.save()


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0004_populate_industries_and_migrate_data"),
    ]

    operations = [
        # Add legacy_industry field
        migrations.AddField(
            model_name="client",
            name="legacy_industry",
            field=models.CharField(
                blank=True,
                choices=[
                    ("", "Select Industry"),
                    ("automotive", "Automotive"),
                    ("banking_finance", "Banking & Finance"),
                    ("construction", "Construction"),
                    ("education", "Education"),
                    ("entertainment", "Entertainment"),
                    ("fashion_retail", "Fashion & Retail"),
                    ("food_beverage", "Food & Beverage"),
                    ("government", "Government"),
                    ("healthcare", "Healthcare"),
                    ("hospitality_tourism", "Hospitality & Tourism"),
                    ("insurance", "Insurance"),
                    ("legal_services", "Legal Services"),
                    ("manufacturing", "Manufacturing"),
                    ("media_advertising", "Media & Advertising"),
                    ("non_profit", "Non-Profit"),
                    ("real_estate", "Real Estate"),
                    ("retail", "Retail"),
                    ("sports_recreation", "Sports & Recreation"),
                    ("technology", "Technology"),
                    ("telecommunications", "Telecommunications"),
                    ("transportation", "Transportation"),
                    ("utilities", "Utilities"),
                    ("other", "Other"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        # Copy data and clear industry field
        migrations.RunPython(copy_industry_to_legacy_and_clear, reverse_copy_industry_to_legacy),
    ]
