# Generated by Django 4.2.7 on 2025-06-19 15:10

from django.db import migrations


def populate_default_industries(apps, schema_editor):
    """Populate Industry model with default industries for all organizations"""
    Industry = apps.get_model('core', 'Industry')
    Organization = apps.get_model('organizations', 'Organization')

    # Default industries (previously hardcoded in Client model)
    DEFAULT_INDUSTRIES = [
        ('automotive', 'Automotive'),
        ('banking_finance', 'Banking & Finance'),
        ('construction', 'Construction'),
        ('education', 'Education'),
        ('entertainment', 'Entertainment'),
        ('fashion_retail', 'Fashion & Retail'),
        ('food_beverage', 'Food & Beverage'),
        ('government', 'Government'),
        ('healthcare', 'Healthcare'),
        ('hospitality_tourism', 'Hospitality & Tourism'),
        ('insurance', 'Insurance'),
        ('legal_services', 'Legal Services'),
        ('manufacturing', 'Manufacturing'),
        ('media_advertising', 'Media & Advertising'),
        ('non_profit', 'Non-Profit'),
        ('real_estate', 'Real Estate'),
        ('retail', 'Retail'),
        ('sports_recreation', 'Sports & Recreation'),
        ('technology', 'Technology'),
        ('telecommunications', 'Telecommunications'),
        ('transportation', 'Transportation'),
        ('utilities', 'Utilities'),
        ('other', 'Other'),
    ]

    # Create default industries for each organization
    for organization in Organization.objects.all():
        for code, name in DEFAULT_INDUSTRIES:
            # Only create if it doesn't already exist
            Industry.objects.get_or_create(
                organization=organization,
                code=code,
                defaults={
                    'name': name,
                    'description': f'Default {name} industry category',
                    'is_active': True
                }
            )


def reverse_populate_default_industries(apps, schema_editor):
    """Remove default industries (reverse migration)"""
    Industry = apps.get_model('core', 'Industry')

    # Default industry codes to remove
    DEFAULT_CODES = [
        'automotive', 'banking_finance', 'construction', 'education', 'entertainment',
        'fashion_retail', 'food_beverage', 'government', 'healthcare', 'hospitality_tourism',
        'insurance', 'legal_services', 'manufacturing', 'media_advertising', 'non_profit',
        'real_estate', 'retail', 'sports_recreation', 'technology', 'telecommunications',
        'transportation', 'utilities', 'other'
    ]

    # Remove default industries
    Industry.objects.filter(code__in=DEFAULT_CODES).delete()


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0006_remove_new_industry_field"),
        ("organizations", "0001_initial"),  # Ensure Organization model exists
    ]

    operations = [
        migrations.RunPython(
            populate_default_industries,
            reverse_populate_default_industries
        ),
    ]
