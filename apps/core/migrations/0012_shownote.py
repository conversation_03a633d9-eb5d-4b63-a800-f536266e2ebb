# Generated by Django 4.2.7 on 2025-07-09 23:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("shows", "0008_delete_presentershownote"),
        ("core", "0011_presenterpersonalnote"),
    ]

    operations = [
        migrations.CreateModel(
            name="ShowNote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "show_date",
                    models.DateField(help_text="Specific date this note applies to"),
                ),
                (
                    "title",
                    models.CharField(
                        help_text="Title for your show note", max_length=200
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        help_text="Note content and details for this specific show"
                    ),
                ),
                (
                    "category",
                    models.Char<PERSON><PERSON>(
                        choices=[
                            ("preparation", "Show Preparation"),
                            ("reminders", "Reminders"),
                            ("guests", "Guest Information"),
                            ("music", "Music Notes"),
                            ("segments", "Segment Notes"),
                            ("technical", "Technical Notes"),
                            ("follow_up", "Follow-up Items"),
                            ("general", "General"),
                        ],
                        default="preparation",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.IntegerField(
                        choices=[(1, "Low"), (2, "Normal"), (3, "High"), (4, "Urgent")],
                        default=2,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("archived", "Archived"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "is_pinned",
                    models.BooleanField(
                        default=False, help_text="Pin this note for this show"
                    ),
                ),
                (
                    "tags",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Tags for organizing show notes",
                    ),
                ),
                (
                    "presenter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="show_notes",
                        to="core.presenter",
                    ),
                ),
                (
                    "show",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="presenter_notes",
                        to="shows.show",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="show_notes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-is_pinned", "-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "show", "show_date"],
                        name="core_showno_user_id_8cf02d_idx",
                    ),
                    models.Index(
                        fields=["presenter", "show_date"],
                        name="core_showno_present_818f4e_idx",
                    ),
                    models.Index(
                        fields=["show", "show_date"],
                        name="core_showno_show_id_54840a_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="core_showno_created_80c3fb_idx"
                    ),
                    models.Index(
                        fields=["status"], name="core_showno_status_2f3401_idx"
                    ),
                ],
                "unique_together": {("user", "show", "show_date", "title")},
            },
        ),
    ]
