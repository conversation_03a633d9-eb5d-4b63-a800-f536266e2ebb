"""
Core background tasks for the radio mentions application.
Handles show monitoring, system health checks, and general maintenance tasks.
"""

from celery import shared_task
from django.utils import timezone
from django.db.models import Q, Count
from datetime import datetime, timedelta
from apps.organizations.models import Organization
from apps.shows.models import Show, ShowSession
from apps.core.models import Presenter
from apps.mentions.models import MentionReading
from apps.core.notifications import notify_admins, notify_managers, NotificationManager
from apps.activity_logs.models import ActivityLog
import logging

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def monitor_show_status(self):
    """
    Monitor show status and detect shows that should be on air
    Runs every minute to check for:
    - Shows that should be live but have no active sessions
    - Shows with missing presenters
    - Shows that ended early
    """
    try:
        current_time = timezone.now()
        current_date = current_time.date()
        current_time_only = current_time.time()
        
        organizations = Organization.objects.filter(is_active=True)
        
        for organization in organizations:
            # Get shows that should be live right now
            todays_shows = Show.objects.filter(
                organization=organization,
                is_active=True,
                days_of_week__contains=[current_date.weekday()]
            )
            
            for show in todays_shows:
                # Check if show should be live
                if show.start_time <= current_time_only <= show.end_time:
                    # Check if there's an active session
                    active_session = ShowSession.objects.filter(
                        show=show,
                        date=current_date,
                        actual_end_time__isnull=True
                    ).first()
                    
                    if not active_session:
                        # Show should be live but no active session
                        _handle_missing_show_session(show, organization)
                    else:
                        # Check if presenter is assigned
                        if not show.showpresenter_set.filter(is_active=True).exists():
                            _handle_missing_presenter(show, organization, active_session)
        
        logger.info("Show status monitoring completed successfully")
        return "Show monitoring completed"
        
    except Exception as exc:
        logger.error(f"Show monitoring failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=60)


@shared_task(bind=True, max_retries=3)
def check_show_sessions(self):
    """
    Check for show sessions that need attention:
    - Sessions that should have ended but are still active
    - Sessions that started late
    """
    try:
        current_time = timezone.now()
        current_date = current_time.date()
        current_time_only = current_time.time()
        
        # Find sessions that should have ended
        overdue_sessions = ShowSession.objects.filter(
            date=current_date,
            actual_end_time__isnull=True,
            show__end_time__lt=current_time_only
        ).select_related('show', 'presenter', 'show__organization')
        
        for session in overdue_sessions:
            # Auto-end sessions that are 30 minutes overdue
            end_time = datetime.combine(current_date, session.show.end_time)
            if current_time > end_time + timedelta(minutes=30):
                session.actual_end_time = current_time
                session.end_reason = 'auto_ended'
                session.notes = 'Automatically ended - session overdue'
                session.save()
                
                # Notify admins
                notify_admins(
                    organization=session.show.organization,
                    notification_type='show_ended',
                    context={
                        'show': session.show,
                        'presenter': session.presenter,
                        'end_reason': 'Auto-ended (overdue)',
                        'session': session
                    }
                )
        
        logger.info("Show session check completed successfully")
        return f"Checked {overdue_sessions.count()} overdue sessions"
        
    except Exception as exc:
        logger.error(f"Show session check failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=300)


@shared_task(bind=True, max_retries=3)
def system_health_check(self):
    """
    Perform system health checks and alert admins of issues
    """
    try:
        health_issues = []
        current_time = timezone.now()
        
        # Check for organizations with no recent activity
        week_ago = current_time - timedelta(days=7)
        inactive_orgs = Organization.objects.filter(
            is_active=True,
            activity_logs__created_at__lt=week_ago
        ).distinct()
        
        if inactive_orgs.exists():
            health_issues.append(f"{inactive_orgs.count()} organizations with no recent activity")
        
        # Check for shows with no presenters
        shows_without_presenters = Show.objects.filter(
            is_active=True,
            showpresenter__isnull=True
        ).count()
        
        if shows_without_presenters > 0:
            health_issues.append(f"{shows_without_presenters} shows have no assigned presenters")
        
        # Check for old pending mentions (over 48 hours)
        old_pending = MentionReading.objects.filter(
            mention__status='pending',
            created_at__lt=current_time - timedelta(hours=48)
        ).count()
        
        if old_pending > 0:
            health_issues.append(f"{old_pending} mentions pending approval for over 48 hours")
        
        # If there are health issues, notify system admins
        if health_issues:
            # Get all organization owners for system-wide alerts
            for organization in Organization.objects.filter(is_active=True):
                notify_admins(
                    organization=organization,
                    notification_type='system_alert',
                    context={
                        'issues': health_issues,
                        'check_time': current_time,
                        'severity': 'warning'
                    }
                )
        
        logger.info(f"System health check completed. Found {len(health_issues)} issues")
        return f"Health check completed. Issues: {len(health_issues)}"
        
    except Exception as exc:
        logger.error(f"System health check failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=1800)


@shared_task(bind=True, max_retries=3)
def send_daily_summaries(self):
    """
    Send daily summary reports to organization admins and managers
    """
    try:
        current_date = timezone.now().date()
        yesterday = current_date - timedelta(days=1)
        
        organizations = Organization.objects.filter(is_active=True)
        
        for organization in organizations:
            # Gather daily statistics
            stats = _gather_daily_stats(organization, yesterday)
            
            # Send to admins and managers
            notify_managers(
                organization=organization,
                notification_type='daily_summary',
                context={
                    'date': yesterday,
                    'stats': stats,
                    'organization': organization
                }
            )
        
        logger.info("Daily summaries sent successfully")
        return f"Daily summaries sent to {organizations.count()} organizations"
        
    except Exception as exc:
        logger.error(f"Daily summary task failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=3600)


@shared_task(bind=True, max_retries=3)
def process_notification_queue(self):
    """
    Process any queued notifications that need to be sent
    This is a placeholder for future notification queuing system
    """
    try:
        # For now, this is a placeholder
        # In the future, we could implement a notification queue
        # for batching and rate limiting notifications

        logger.debug("Notification queue processing completed")
        return "Notification queue processed"

    except Exception as exc:
        logger.error(f"Notification queue processing failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=30)


@shared_task(bind=True, max_retries=3)
def track_presenter_activity(self):
    """
    Track presenter activity and detect inactive presenters during shows
    """
    try:
        current_time = timezone.now()
        current_date = current_time.date()
        current_time_only = current_time.time()

        # Find active show sessions
        active_sessions = ShowSession.objects.filter(
            date=current_date,
            actual_end_time__isnull=True
        ).select_related('show', 'presenter', 'show__organization')

        for session in active_sessions:
            # Check if presenter has been active (marked mentions as read recently)
            recent_activity = MentionReading.objects.filter(
                presenter=session.presenter,
                scheduled_date=current_date,
                actual_read_time__gte=current_time - timedelta(minutes=30)
            ).exists()

            # If no recent activity and show has been running for more than 15 minutes
            session_duration = current_time - session.actual_start_time
            if (not recent_activity and
                session_duration > timedelta(minutes=15) and
                session.show.start_time <= current_time_only <= session.show.end_time):

                # Check if there are pending mentions for this show
                pending_mentions = MentionReading.objects.filter(
                    show=session.show,
                    scheduled_date=current_date,
                    actual_read_time__isnull=True,
                    scheduled_time__lte=current_time_only
                ).count()

                if pending_mentions > 0:
                    _handle_inactive_presenter(session, pending_mentions)

        logger.info("Presenter activity tracking completed")
        return f"Tracked {active_sessions.count()} active sessions"

    except Exception as exc:
        logger.error(f"Presenter activity tracking failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=300)


@shared_task(bind=True, max_retries=3)
def detect_show_conflicts(self):
    """
    Detect potential show conflicts and scheduling issues
    """
    try:
        current_time = timezone.now()
        current_date = current_time.date()

        organizations = Organization.objects.filter(is_active=True)
        conflicts_found = 0

        for organization in organizations:
            # Check for presenter double-booking
            todays_shows = Show.objects.filter(
                organization=organization,
                is_active=True,
                days_of_week__contains=[current_date.weekday()]
            ).prefetch_related('showpresenter_set__presenter')

            # Group shows by time slots and check for presenter conflicts
            time_slots = {}
            for show in todays_shows:
                time_key = f"{show.start_time}-{show.end_time}"
                if time_key not in time_slots:
                    time_slots[time_key] = []
                time_slots[time_key].append(show)

            # Check each time slot for conflicts
            for time_slot, shows in time_slots.items():
                if len(shows) > 1:
                    # Multiple shows at same time - check for presenter conflicts
                    presenters_in_slot = set()
                    for show in shows:
                        show_presenters = set(sp.presenter for sp in show.showpresenter_set.filter(is_active=True))
                        if presenters_in_slot.intersection(show_presenters):
                            # Presenter conflict detected
                            _handle_presenter_conflict(shows, organization)
                            conflicts_found += 1
                        presenters_in_slot.update(show_presenters)

        logger.info(f"Show conflict detection completed. Found {conflicts_found} conflicts")
        return f"Detected {conflicts_found} show conflicts"

    except Exception as exc:
        logger.error(f"Show conflict detection failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=600)


@shared_task(bind=True, max_retries=3)
def send_show_reminders(self):
    """
    Send reminders to presenters about upcoming shows
    """
    try:
        current_time = timezone.now()
        reminder_time = current_time + timedelta(minutes=30)  # 30 minutes ahead
        current_date = current_time.date()
        reminder_time_only = reminder_time.time()

        # Find shows starting in 30 minutes
        upcoming_shows = Show.objects.filter(
            is_active=True,
            days_of_week__contains=[current_date.weekday()],
            start_time__lte=reminder_time_only,
            start_time__gt=current_time.time()
        ).prefetch_related('showpresenter_set__presenter')

        reminders_sent = 0
        for show in upcoming_shows:
            # Check if reminder already sent today
            reminder_sent_today = ActivityLog.objects.filter(
                organization=show.organization,
                action='show_reminder_sent',
                content_type__model='show',
                object_id=show.id,
                created_at__date=current_date
            ).exists()

            if not reminder_sent_today:
                # Send reminder to assigned presenters
                presenters = [sp.presenter for sp in show.showpresenter_set.filter(is_active=True)]

                if presenters:
                    notification_manager = NotificationManager()
                    for presenter in presenters:
                        notification_manager.send_notification(
                            notification_type='show_reminder',
                            recipients=[presenter.user],
                            context={
                                'show': show,
                                'presenter': presenter,
                                'start_time': show.start_time,
                                'reminder_time': reminder_time
                            },
                            organization=show.organization
                        )

                    # Log that reminder was sent
                    ActivityLog.log_activity(
                        user=None,
                        organization=show.organization,
                        action='show_reminder_sent',
                        description=f"Show reminder sent for '{show.name}' to {len(presenters)} presenters",
                        content_object=show
                    )

                    reminders_sent += 1

        logger.info(f"Show reminders sent: {reminders_sent}")
        return f"Sent {reminders_sent} show reminders"

    except Exception as exc:
        logger.error(f"Show reminder task failed: {str(exc)}")
        raise self.retry(exc=exc, countdown=300)


# Helper functions
def _handle_missing_show_session(show, organization):
    """Handle case where show should be live but has no active session"""
    try:
        # Log the issue
        ActivityLog.log_activity(
            user=None,
            organization=organization,
            action='show_monitoring',
            description=f"Show '{show.name}' should be live but has no active session",
            level='warning',
            content_object=show
        )
        
        # Notify admins
        notify_admins(
            organization=organization,
            notification_type='show_missing_presenter',
            context={
                'show': show,
                'issue': 'No active session',
                'time': timezone.now()
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to handle missing show session: {str(e)}")


def _handle_missing_presenter(show, organization, session):
    """Handle case where show is live but has no presenter"""
    try:
        # Log the issue
        ActivityLog.log_activity(
            user=None,
            organization=organization,
            action='show_monitoring',
            description=f"Show '{show.name}' is live but has no assigned presenter",
            level='warning',
            content_object=show
        )
        
        # Notify admins
        notify_admins(
            organization=organization,
            notification_type='show_missing_presenter',
            context={
                'show': show,
                'session': session,
                'issue': 'No assigned presenter',
                'time': timezone.now()
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to handle missing presenter: {str(e)}")


def _gather_daily_stats(organization, date):
    """Gather daily statistics for an organization"""
    try:
        stats = {
            'shows_scheduled': Show.objects.filter(
                organization=organization,
                is_active=True,
                days_of_week__contains=[date.weekday()]
            ).count(),
            'mentions_completed': MentionReading.objects.filter(
                mention__client__organization=organization,
                scheduled_date=date,
                actual_read_time__isnull=False
            ).count(),
            'mentions_missed': MentionReading.objects.filter(
                mention__client__organization=organization,
                scheduled_date=date,
                actual_read_time__isnull=True,
                scheduled_time__lt=timezone.now().time()
            ).count(),
            'active_presenters': Presenter.objects.filter(
                organization=organization,
                is_active=True,
                showpresenter__show__days_of_week__contains=[date.weekday()]
            ).distinct().count(),
        }

        return stats

    except Exception as e:
        logger.error(f"Failed to gather daily stats: {str(e)}")
        return {}


def _handle_inactive_presenter(session, pending_mentions):
    """Handle case where presenter appears inactive during show"""
    try:
        # Log the issue
        ActivityLog.log_activity(
            user=None,
            organization=session.show.organization,
            action='presenter_inactive',
            description=f"Presenter {session.presenter.display_name} appears inactive during show '{session.show.name}' with {pending_mentions} pending mentions",
            level='warning',
            content_object=session
        )

        # Notify admins about inactive presenter
        notify_admins(
            organization=session.show.organization,
            notification_type='presenter_inactive',
            context={
                'show': session.show,
                'presenter': session.presenter,
                'session': session,
                'pending_mentions': pending_mentions,
                'time': timezone.now()
            }
        )

    except Exception as e:
        logger.error(f"Failed to handle inactive presenter: {str(e)}")


def _handle_presenter_conflict(shows, organization):
    """Handle presenter double-booking conflict"""
    try:
        show_names = [show.name for show in shows]

        # Log the conflict
        ActivityLog.log_activity(
            user=None,
            organization=organization,
            action='presenter_conflict',
            description=f"Presenter conflict detected between shows: {', '.join(show_names)}",
            level='error'
        )

        # Notify managers about the conflict
        notify_managers(
            organization=organization,
            notification_type='conflict_detected',
            context={
                'conflict_type': 'Presenter Double-booking',
                'shows': shows,
                'show_names': show_names,
                'time': timezone.now()
            }
        )

    except Exception as e:
        logger.error(f"Failed to handle presenter conflict: {str(e)}")
