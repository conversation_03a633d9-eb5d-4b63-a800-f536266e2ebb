from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, time, timedelta
from apps.organizations.models import Organization, OrganizationMembership
from apps.core.models import Client, Presenter
from apps.shows.models import Show
from apps.mentions.models import RecurringMention, RecurringMentionShow, Mention
from apps.mentions.services import RecurringMentionService


class Command(BaseCommand):
    help = 'Test bulk approve functionality for recurring mentions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test data after running',
        )

    def handle(self, *args, **options):
        self.stdout.write('Testing bulk approve functionality...')
        
        # Get or create test organization
        org, _ = Organization.objects.get_or_create(
            slug='test-bulk-approve',
            defaults={
                'name': 'Test Bulk Approve Org',
                'plan_type': 'free',
            }
        )
        
        # Get or create test user
        user, _ = User.objects.get_or_create(
            username='test_bulk_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User',
            }
        )
        
        # Create membership
        membership, _ = OrganizationMembership.objects.get_or_create(
            user=user,
            organization=org,
            defaults={
                'role': 'admin',
                'is_default': True,
                'invitation_accepted_at': timezone.now()
            }
        )
        
        # Create test clients
        client1, _ = Client.objects.get_or_create(
            name='Test Client 1',
            organization=org,
            defaults={
                'contact_person': 'Test Contact 1',
                'email': '<EMAIL>',
            }
        )
        
        client2, _ = Client.objects.get_or_create(
            name='Test Client 2',
            organization=org,
            defaults={
                'contact_person': 'Test Contact 2',
                'email': '<EMAIL>',
            }
        )
        
        # Create test show
        show, _ = Show.objects.get_or_create(
            name='Test Bulk Show',
            organization=org,
            defaults={
                'description': 'Test show for bulk approve',
                'start_time': time(9, 0),
                'end_time': time(12, 0),
                'days_of_week': [0, 1, 2, 3, 4],  # Monday to Friday
            }
        )
        
        # Create test presenter
        presenter, _ = Presenter.objects.get_or_create(
            first_name='Test',
            last_name='Presenter',
            organization=org,
            defaults={
                'email': '<EMAIL>',
                'stage_name': 'Test DJ',
            }
        )
        
        try:
            # Create multiple recurring mentions
            recurring_mentions = []
            
            # Recurring mention 1 (weekdays only to match show schedule)
            recurring1 = RecurringMention.objects.create(
                title='Daily Morning Sponsor',
                content='This morning is brought to you by Test Client 1',
                client=client1,
                frequency='weekly',
                interval=1,
                weekdays=[0, 1, 2, 3, 4],  # Monday to Friday only
                start_date=date.today(),
                end_date=date.today() + timedelta(days=10),
                created_by=user
            )
            
            RecurringMentionShow.objects.create(
                recurring_mention=recurring1,
                show=show,
                presenter=presenter,
                scheduled_time=time(10, 0)
            )
            
            recurring_mentions.append(recurring1)
            
            # Recurring mention 2
            recurring2 = RecurringMention.objects.create(
                title='Weekly Special Offer',
                content='Special weekly offer from Test Client 2',
                client=client2,
                frequency='weekly',
                interval=1,
                weekdays=[0, 2, 4],  # Monday, Wednesday, Friday
                start_date=date.today(),
                end_date=date.today() + timedelta(days=14),
                created_by=user
            )
            
            RecurringMentionShow.objects.create(
                recurring_mention=recurring2,
                show=show,
                presenter=presenter,
                scheduled_time=time(11, 0)
            )
            
            recurring_mentions.append(recurring2)
            
            # Generate mentions from recurring patterns
            total_generated = 0
            for recurring in recurring_mentions:
                generated = RecurringMentionService.generate_upcoming_mentions(recurring, days_ahead=7)
                total_generated += len(generated)
                self.stdout.write(f'Generated {len(generated)} mentions from "{recurring.title}"')
            
            self.stdout.write(f'Total generated mentions: {total_generated}')
            
            # Check pending mentions count
            pending_count = Mention.objects.filter(
                client__organization=org,
                status='pending',
                recurring_mention__isnull=False
            ).count()
            
            self.stdout.write(f'Pending mentions from recurring patterns: {pending_count}')
            
            # Show some examples
            pending_mentions = Mention.objects.filter(
                client__organization=org,
                status='pending',
                recurring_mention__isnull=False
            )[:5]
            
            self.stdout.write('\n--- Sample Pending Mentions ---')
            for mention in pending_mentions:
                self.stdout.write(f'- {mention.title} (from {mention.recurring_mention.title})')
            
            if options['cleanup']:
                self.stdout.write('\n--- Cleaning up test data ---')
                org.delete()  # This will cascade delete everything
                user.delete()
                self.stdout.write('✅ Test data cleaned up')
            else:
                self.stdout.write(f'\n--- Test data preserved ---')
                self.stdout.write(f'Organization: {org.slug}')
                self.stdout.write(f'Pending mentions: {pending_count}')
                self.stdout.write(f'Visit: http://127.0.0.1:8001/mentions/recurring/')
                self.stdout.write('Use --cleanup flag to remove test data')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Test failed with error: {str(e)}')
            )
            raise
