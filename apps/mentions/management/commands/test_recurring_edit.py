from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, time, timedelta
from apps.organizations.models import Organization, OrganizationMembership
from apps.core.models import Client, Presenter
from apps.shows.models import Show
from apps.mentions.models import RecurringMention, RecurringMentionShow, Mention
from apps.mentions.services import RecurringMentionService


class Command(BaseCommand):
    help = 'Test recurring mention edit functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test data after running',
        )

    def handle(self, *args, **options):
        self.stdout.write('Testing recurring mention edit functionality...')
        
        # Get or create test organization
        org, _ = Organization.objects.get_or_create(
            slug='test-recurring-edit',
            defaults={
                'name': 'Test Recurring Edit Org',
                'plan_type': 'free',
            }
        )
        
        # Get or create test user
        user, _ = User.objects.get_or_create(
            username='test_recurring_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User',
            }
        )
        
        # Create membership
        membership, _ = OrganizationMembership.objects.get_or_create(
            user=user,
            organization=org,
            defaults={
                'role': 'admin',
                'is_default': True,
                'invitation_accepted_at': timezone.now()
            }
        )
        
        # Create test client
        client, _ = Client.objects.get_or_create(
            name='Test Recurring Client',
            organization=org,
            defaults={
                'contact_person': 'Test Contact',
                'email': '<EMAIL>',
            }
        )
        
        # Create test show
        show, _ = Show.objects.get_or_create(
            name='Test Recurring Show',
            organization=org,
            defaults={
                'description': 'Test show for recurring mentions',
                'start_time': time(9, 0),
                'end_time': time(12, 0),
                'days_of_week': [0, 1, 2, 3, 4],  # Monday to Friday
            }
        )
        
        # Create test presenter
        presenter, _ = Presenter.objects.get_or_create(
            first_name='Test',
            last_name='Presenter',
            organization=org,
            defaults={
                'email': '<EMAIL>',
                'stage_name': 'Test DJ',
            }
        )
        
        try:
            # Create recurring mention (weekly on weekdays only to match show schedule)
            recurring_mention = RecurringMention.objects.create(
                title='Test Recurring Mention',
                content='Original content for testing',
                client=client,
                frequency='weekly',
                interval=1,
                weekdays=[0, 1, 2, 3, 4],  # Monday to Friday only
                start_date=date.today(),
                end_date=date.today() + timedelta(days=14),
                created_by=user
            )
            
            # Create show assignment
            RecurringMentionShow.objects.create(
                recurring_mention=recurring_mention,
                show=show,
                presenter=presenter,
                scheduled_time=time(10, 0)
            )
            
            self.stdout.write(f'Created recurring mention: {recurring_mention.title}')
            
            # Generate initial mentions
            initial_mentions = RecurringMentionService.generate_upcoming_mentions(recurring_mention)
            initial_count = len(initial_mentions)
            self.stdout.write(f'Generated {initial_count} initial mentions')
            
            # Check that mentions are linked to recurring mention
            linked_mentions = Mention.objects.filter(recurring_mention=recurring_mention)
            self.stdout.write(f'Found {linked_mentions.count()} mentions linked to recurring mention')
            
            # Test 1: Edit content only (should update existing mentions, not create new ones)
            self.stdout.write('\n--- Test 1: Content-only edit ---')
            update_data = {
                'title': 'Updated Test Recurring Mention',
                'content': 'Updated content for testing',
                'priority': 3,
                'duration_seconds': 45,
            }
            
            RecurringMentionService.update_recurring_pattern(recurring_mention, **update_data)
            
            # Check mentions after content update
            after_content_update = Mention.objects.filter(recurring_mention=recurring_mention)
            self.stdout.write(f'Mentions after content update: {after_content_update.count()}')
            
            if after_content_update.count() == initial_count:
                self.stdout.write(self.style.SUCCESS('✅ Content update: No new mentions created'))
                
                # Check if content was updated
                updated_mention = after_content_update.first()
                if (updated_mention.title == 'Updated Test Recurring Mention' and 
                    updated_mention.content == 'Updated content for testing'):
                    self.stdout.write(self.style.SUCCESS('✅ Content update: Existing mentions updated'))
                else:
                    self.stdout.write(self.style.ERROR('❌ Content update: Existing mentions not updated'))
            else:
                self.stdout.write(self.style.ERROR('❌ Content update: New mentions were created unexpectedly'))
            
            # Test 2: Edit pattern (should regenerate mentions)
            self.stdout.write('\n--- Test 2: Pattern edit ---')
            pattern_update_data = {
                'frequency': 'weekly',
                'weekdays': [0, 2, 4],  # Monday, Wednesday, Friday
                'interval': 1,
            }
            
            RecurringMentionService.update_recurring_pattern(recurring_mention, **pattern_update_data)
            
            # Check mentions after pattern update
            after_pattern_update = Mention.objects.filter(recurring_mention=recurring_mention)
            self.stdout.write(f'Mentions after pattern update: {after_pattern_update.count()}')
            
            if after_pattern_update.count() != initial_count:
                self.stdout.write(self.style.SUCCESS('✅ Pattern update: Mentions regenerated'))
            else:
                self.stdout.write(self.style.WARNING('⚠️ Pattern update: Mention count unchanged (may be expected)'))
            
            # Final verification
            final_mentions = Mention.objects.filter(recurring_mention=recurring_mention)
            self.stdout.write(f'\n--- Final State ---')
            self.stdout.write(f'Total mentions linked to recurring mention: {final_mentions.count()}')
            
            for mention in final_mentions[:3]:  # Show first 3
                self.stdout.write(f'  - {mention.title}: {mention.content[:50]}...')
            
            if options['cleanup']:
                self.stdout.write('\n--- Cleaning up test data ---')
                recurring_mention.delete()  # This will cascade delete related mentions
                show.delete()
                client.delete()
                presenter.delete()
                org.delete()
                user.delete()
                self.stdout.write('✅ Test data cleaned up')
            else:
                self.stdout.write(f'\n--- Test data preserved ---')
                self.stdout.write(f'Recurring mention ID: {recurring_mention.pk}')
                self.stdout.write('Use --cleanup flag to remove test data')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Test failed with error: {str(e)}')
            )
            raise
