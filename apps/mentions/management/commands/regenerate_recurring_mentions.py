from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import date, timedelta
from apps.mentions.models import RecurringMention, Mention
from apps.mentions.services import RecurringMentionService


class Command(BaseCommand):
    help = 'Regenerate mentions for recurring patterns that may have incomplete schedules'

    def add_arguments(self, parser):
        parser.add_argument(
            '--recurring-id',
            type=int,
            help='Regenerate mentions for a specific recurring mention ID',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force regeneration even if mentions already exist',
        )

    def handle(self, *args, **options):
        self.stdout.write('Checking recurring mentions for incomplete schedules...')
        
        # Get recurring mentions to check
        if options['recurring_id']:
            recurring_mentions = RecurringMention.objects.filter(
                id=options['recurring_id'],
                is_active=True
            )
            if not recurring_mentions.exists():
                self.stdout.write(
                    self.style.ERROR(f'Recurring mention with ID {options["recurring_id"]} not found or inactive')
                )
                return
        else:
            # Get all active recurring mentions
            recurring_mentions = RecurringMention.objects.filter(
                is_active=True,
                start_date__lte=date.today()
            )

        total_processed = 0
        total_generated = 0
        
        for recurring_mention in recurring_mentions:
            self.stdout.write(f'\nChecking: {recurring_mention.title}')
            self.stdout.write(f'  Period: {recurring_mention.start_date} to {recurring_mention.end_date or "ongoing"}')
            self.stdout.write(f'  Frequency: {recurring_mention.get_frequency_display()}')
            
            # Calculate expected date range
            end_date = recurring_mention.end_date
            if not end_date:
                # If no end date, check for next 90 days
                end_date = date.today() + timedelta(days=90)
            
            # Count existing mentions
            existing_mentions = Mention.objects.filter(
                recurring_mention=recurring_mention
            ).count()
            
            # Calculate expected mentions (rough estimate)
            date_range = (end_date - recurring_mention.start_date).days
            if recurring_mention.frequency == 'daily':
                expected_mentions = date_range // recurring_mention.interval
            elif recurring_mention.frequency == 'weekly':
                expected_mentions = (date_range // 7) * len(recurring_mention.weekdays or [1])
            elif recurring_mention.frequency == 'monthly':
                expected_mentions = date_range // 30
            else:
                expected_mentions = date_range  # Custom frequency
            
            # Account for multiple shows
            show_count = recurring_mention.recurringmentionshow_set.count()
            expected_mentions *= show_count
            
            self.stdout.write(f'  Existing mentions: {existing_mentions}')
            self.stdout.write(f'  Expected mentions (approx): {expected_mentions}')
            
            # Check if regeneration is needed
            needs_regeneration = False
            if options['force']:
                needs_regeneration = True
                reason = "forced regeneration"
            elif existing_mentions == 0:
                needs_regeneration = True
                reason = "no mentions found"
            elif existing_mentions < expected_mentions * 0.5:  # Less than 50% of expected
                needs_regeneration = True
                reason = f"only {existing_mentions} of ~{expected_mentions} expected mentions"
            elif recurring_mention.end_date and existing_mentions < expected_mentions * 0.8:  # Less than 80% for finite patterns
                needs_regeneration = True
                reason = f"incomplete schedule: {existing_mentions} of ~{expected_mentions} expected"
            
            if needs_regeneration:
                self.stdout.write(f'  🔄 Regeneration needed: {reason}')
                
                if not options['dry_run']:
                    try:
                        # Delete existing future mentions if forcing
                        if options['force']:
                            future_mentions = Mention.objects.filter(
                                recurring_mention=recurring_mention,
                                mentionreading__scheduled_date__gte=date.today(),
                                mentionreading__actual_read_time__isnull=True
                            )
                            deleted_count = future_mentions.count()
                            future_mentions.delete()
                            self.stdout.write(f'  🗑️  Deleted {deleted_count} existing future mentions')
                        
                        # Generate new mentions
                        generated_mentions = RecurringMentionService.generate_upcoming_mentions(recurring_mention)
                        generated_count = len(generated_mentions)
                        total_generated += generated_count
                        
                        self.stdout.write(
                            self.style.SUCCESS(f'  ✅ Generated {generated_count} new mentions')
                        )
                        
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'  ❌ Error generating mentions: {str(e)}')
                        )
                else:
                    self.stdout.write('  📋 Would regenerate mentions (dry run)')
            else:
                self.stdout.write('  ✅ Schedule appears complete')
            
            total_processed += 1
        
        # Summary
        self.stdout.write(f'\n--- Summary ---')
        self.stdout.write(f'Processed: {total_processed} recurring mentions')
        if not options['dry_run']:
            self.stdout.write(f'Generated: {total_generated} new mentions')
        else:
            self.stdout.write('Dry run completed - no changes made')
        
        if total_generated > 0 or options['dry_run']:
            self.stdout.write(
                self.style.SUCCESS('✅ Recurring mention regeneration completed!')
            )
        else:
            self.stdout.write('ℹ️  No regeneration needed')
