from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, timedelta
from apps.core.models import Client
from apps.organizations.models import Organization
from apps.mentions.models import RecurringMention


class Command(BaseCommand):
    help = 'Populate recurring mention history with test data'

    def handle(self, *args, **options):
        # Get or create test organization and client
        org, _ = Organization.objects.get_or_create(
            name='Test Radio Station',
            defaults={'slug': 'test-radio'}
        )
        
        client, _ = Client.objects.get_or_create(
            name='Test Client',
            organization=org,
            defaults={
                'email': '<EMAIL>',
                'phone': '************',
                'industry': 'technology'
            }
        )
        
        user = User.objects.first()
        
        # Create test recurring mentions with different statuses
        test_data = [
            {
                'title': 'Morning Drive Sponsorship',
                'campaign_name': 'Q1 Morning Drive Campaign',
                'status': 'finished',
                'start_date': date.today() - timedelta(days=90),
                'end_date': date.today() - timedelta(days=30),
                'total_required': 60,
                'frequency': 'weekly',
                'content': 'Your morning commute is sponsored by Test Client - driving innovation forward!'
            },
            {
                'title': 'Weekend Special Events',
                'campaign_name': 'Weekend Promotions',
                'status': 'canceled',
                'start_date': date.today() - timedelta(days=60),
                'end_date': date.today() + timedelta(days=30),
                'total_required': 24,
                'frequency': 'weekly',
                'content': 'This weekend at Test Client - special events and promotions!'
            },
            {
                'title': 'Hourly Brand Mentions',
                'campaign_name': 'Brand Awareness Campaign',
                'status': 'ended',
                'start_date': date.today() - timedelta(days=120),
                'end_date': date.today() - timedelta(days=1),
                'total_required': 240,
                'frequency': 'daily',
                'content': 'Test Client - your trusted technology partner.'
            },
            {
                'title': 'Holiday Special Campaign',
                'campaign_name': 'Holiday 2024 Campaign',
                'status': 'replaced',
                'start_date': date.today() - timedelta(days=45),
                'end_date': date.today() + timedelta(days=15),
                'total_required': 30,
                'frequency': 'daily',
                'content': 'Holiday specials at Test Client - limited time offers!'
            },
            {
                'title': 'Split Test Campaign A',
                'campaign_name': 'A/B Test Campaign',
                'status': 'split',
                'start_date': date.today() - timedelta(days=30),
                'end_date': date.today() + timedelta(days=30),
                'total_required': 60,
                'frequency': 'daily',
                'content': 'Version A: Test Client offers the best technology solutions.'
            },
            {
                'title': 'Current Active Campaign',
                'campaign_name': 'Current Promotions',
                'status': 'active',
                'start_date': date.today() - timedelta(days=7),
                'end_date': date.today() + timedelta(days=60),
                'total_required': 90,
                'frequency': 'daily',
                'content': 'Test Client - currently running promotions and special offers!'
            }
        ]
        
        created_count = 0
        for data in test_data:
            recurring_mention, created = RecurringMention.objects.get_or_create(
                title=data['title'],
                client=client,
                defaults={
                    'campaign_name': data['campaign_name'],
                    'status': data['status'],
                    'start_date': data['start_date'],
                    'end_date': data['end_date'],
                    'total_required': data['total_required'],
                    'frequency': data['frequency'],
                    'content': data['content'],
                    'priority': 2,
                    'duration_seconds': 30,
                    'created_by': user,
                    'status_changed_at': timezone.now(),
                    'status_changed_by': user,
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created: {recurring_mention.title} ({recurring_mention.status})')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Already exists: {recurring_mention.title}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} recurring mention records')
        )
