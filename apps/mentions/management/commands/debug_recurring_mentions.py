from django.core.management.base import BaseCommand
from django.db import transaction
from apps.mentions.models import RecurringMention
from apps.shows.models import Show
from apps.organizations.models import Organization


class Command(BaseCommand):
    help = 'Debug recurring mentions generation issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--organization-id',
            type=int,
            help='Organization ID to debug',
        )
        parser.add_argument(
            '--recurring-mention-id',
            type=int,
            help='Specific recurring mention ID to debug',
        )
        parser.add_argument(
            '--check-shows',
            action='store_true',
            help='Check show configurations',
        )

    def handle(self, *args, **options):
        organization_id = options.get('organization_id')
        recurring_mention_id = options.get('recurring_mention_id')
        check_shows = options.get('check_shows')

        if organization_id:
            try:
                organization = Organization.objects.get(id=organization_id)
                self.stdout.write(f"Debugging organization: {organization.name}")
            except Organization.DoesNotExist:
                self.stdout.write(self.style.ERROR(f"Organization with ID {organization_id} not found"))
                return
        else:
            organization = None

        if check_shows:
            self.check_show_configurations(organization)

        if recurring_mention_id:
            self.debug_specific_recurring_mention(recurring_mention_id)
        elif organization:
            self.debug_organization_recurring_mentions(organization)
        else:
            self.debug_all_recurring_mentions()

    def check_show_configurations(self, organization=None):
        """Check show configurations for potential issues"""
        self.stdout.write(self.style.SUCCESS("\n=== SHOW CONFIGURATION CHECK ==="))
        
        if organization:
            shows = Show.objects.filter(organization=organization, is_active=True)
        else:
            shows = Show.objects.filter(is_active=True)

        for show in shows:
            self.stdout.write(f"\nShow: {show.name} (ID: {show.id})")
            self.stdout.write(f"  Organization: {show.organization.name}")
            self.stdout.write(f"  Time: {show.start_time} - {show.end_time}")
            self.stdout.write(f"  Days of week: {show.days_of_week}")
            
            # Check for potential issues
            issues = []
            if not show.days_of_week:
                issues.append("No days_of_week configured")
            elif not isinstance(show.days_of_week, list):
                issues.append(f"days_of_week is not a list: {type(show.days_of_week)}")
            elif len(show.days_of_week) == 0:
                issues.append("days_of_week is empty list")
            else:
                # Check for valid weekday numbers
                invalid_days = [day for day in show.days_of_week if not isinstance(day, int) or day < 0 or day > 6]
                if invalid_days:
                    issues.append(f"Invalid weekday numbers: {invalid_days}")

            if issues:
                self.stdout.write(self.style.WARNING(f"  Issues: {', '.join(issues)}"))
            else:
                self.stdout.write(self.style.SUCCESS("  Configuration looks good"))

    def debug_specific_recurring_mention(self, recurring_mention_id):
        """Debug a specific recurring mention"""
        try:
            recurring_mention = RecurringMention.objects.get(id=recurring_mention_id)
        except RecurringMention.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"RecurringMention with ID {recurring_mention_id} not found"))
            return

        self.stdout.write(self.style.SUCCESS(f"\n=== DEBUGGING RECURRING MENTION {recurring_mention_id} ==="))
        self.stdout.write(f"Title: {recurring_mention.title}")
        self.stdout.write(f"Client: {recurring_mention.client.name}")
        self.stdout.write(f"Frequency: {recurring_mention.frequency}")
        self.stdout.write(f"Weekdays: {recurring_mention.weekdays}")
        self.stdout.write(f"Start Date: {recurring_mention.start_date}")
        self.stdout.write(f"End Date: {recurring_mention.end_date}")
        self.stdout.write(f"Is Active: {recurring_mention.is_active}")

        # Check show assignments
        show_assignments = recurring_mention.recurringmentionshow_set.all()
        self.stdout.write(f"\nShow Assignments ({len(show_assignments)}):")
        for assignment in show_assignments:
            self.stdout.write(f"  - {assignment.show.name} at {assignment.scheduled_time}")
            self.stdout.write(f"    Show days: {assignment.show.days_of_week}")
            self.stdout.write(f"    Show active: {assignment.show.is_active}")

        # Try to generate mentions and see what happens
        self.stdout.write(f"\n=== TESTING MENTION GENERATION ===")
        try:
            generated_mentions = recurring_mention.generate_mentions()
            self.stdout.write(f"Generated {len(generated_mentions)} mentions")
            
            if len(generated_mentions) == 0:
                self.stdout.write(self.style.WARNING("No mentions were generated!"))
                
                # Check potential issues
                if not recurring_mention.is_active:
                    self.stdout.write("  - Recurring mention is not active")
                if not show_assignments:
                    self.stdout.write("  - No show assignments configured")
                if not recurring_mention.weekdays and recurring_mention.frequency == 'weekly':
                    self.stdout.write("  - No weekdays configured for weekly frequency")
                    
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error generating mentions: {str(e)}"))

    def debug_organization_recurring_mentions(self, organization):
        """Debug all recurring mentions for an organization"""
        self.stdout.write(self.style.SUCCESS(f"\n=== DEBUGGING ORGANIZATION: {organization.name} ==="))
        
        recurring_mentions = RecurringMention.objects.filter(
            client__organization=organization
        ).select_related('client')

        self.stdout.write(f"Found {len(recurring_mentions)} recurring mentions")
        
        for rm in recurring_mentions:
            self.stdout.write(f"\n{rm.id}: {rm.title} ({rm.client.name})")
            self.stdout.write(f"  Frequency: {rm.frequency}, Active: {rm.is_active}")
            
            show_count = rm.recurringmentionshow_set.count()
            self.stdout.write(f"  Show assignments: {show_count}")
            
            if show_count == 0:
                self.stdout.write(self.style.WARNING("    No show assignments!"))

    def debug_all_recurring_mentions(self):
        """Debug all recurring mentions in the system"""
        self.stdout.write(self.style.SUCCESS("\n=== DEBUGGING ALL RECURRING MENTIONS ==="))
        
        recurring_mentions = RecurringMention.objects.all().select_related('client', 'client__organization')
        
        by_org = {}
        for rm in recurring_mentions:
            org_name = rm.client.organization.name
            if org_name not in by_org:
                by_org[org_name] = []
            by_org[org_name].append(rm)

        for org_name, rms in by_org.items():
            self.stdout.write(f"\n{org_name}: {len(rms)} recurring mentions")
            for rm in rms:
                show_count = rm.recurringmentionshow_set.count()
                status = "✓" if rm.is_active and show_count > 0 else "✗"
                self.stdout.write(f"  {status} {rm.id}: {rm.title} ({show_count} shows)")
