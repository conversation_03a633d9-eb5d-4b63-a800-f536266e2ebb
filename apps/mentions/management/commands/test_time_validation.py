from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import date, time, timedelta
from apps.organizations.models import Organization, OrganizationMembership
from apps.core.models import Client, Presenter
from apps.shows.models import Show
from apps.mentions.models import RecurringMention, RecurringMentionShow


class Command(BaseCommand):
    help = 'Test time validation for recurring mentions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test data after running',
        )

    def handle(self, *args, **options):
        self.stdout.write('Testing time validation for recurring mentions...')
        
        # Get or create test organization
        org, _ = Organization.objects.get_or_create(
            slug='test-time-validation',
            defaults={
                'name': 'Test Time Validation Org',
                'plan_type': 'free',
            }
        )
        
        # Get or create test user
        user, _ = User.objects.get_or_create(
            username='test_time_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User',
            }
        )
        
        # Create membership
        membership, _ = OrganizationMembership.objects.get_or_create(
            user=user,
            organization=org,
            defaults={
                'role': 'admin',
                'is_default': True,
                'invitation_accepted_at': timezone.now()
            }
        )
        
        # Create test client
        client, _ = Client.objects.get_or_create(
            name='Test Time Client',
            organization=org,
            defaults={
                'contact_person': 'Test Contact',
                'email': '<EMAIL>',
            }
        )
        
        # Create test show with specific time range (9 AM to 12 PM)
        show, _ = Show.objects.get_or_create(
            name='Test Time Show',
            organization=org,
            defaults={
                'description': 'Test show for time validation',
                'start_time': time(9, 0),  # 9:00 AM
                'end_time': time(12, 0),   # 12:00 PM
                'days_of_week': [0, 1, 2, 3, 4],  # Monday to Friday
            }
        )
        
        self.stdout.write(f'Created show: {show.name} ({show.get_time_frame_display()})')
        
        try:
            # Test 1: Valid time within show range
            self.stdout.write('\n--- Test 1: Valid time (10:30 AM) ---')
            recurring_mention1 = RecurringMention.objects.create(
                title='Test Valid Time',
                content='Test content for valid time',
                client=client,
                frequency='daily',
                interval=1,
                start_date=date.today(),
                end_date=date.today() + timedelta(days=7),
                created_by=user
            )
            
            try:
                assignment1 = RecurringMentionShow.objects.create(
                    recurring_mention=recurring_mention1,
                    show=show,
                    scheduled_time=time(10, 30)  # 10:30 AM - should be valid
                )
                self.stdout.write(self.style.SUCCESS('✅ Valid time accepted: 10:30 AM'))
            except ValidationError as e:
                self.stdout.write(self.style.ERROR(f'❌ Valid time rejected: {e}'))
            
            # Test 2: Invalid time before show starts
            self.stdout.write('\n--- Test 2: Invalid time before show (8:00 AM) ---')
            recurring_mention2 = RecurringMention.objects.create(
                title='Test Early Time',
                content='Test content for early time',
                client=client,
                frequency='daily',
                interval=1,
                start_date=date.today(),
                end_date=date.today() + timedelta(days=7),
                created_by=user
            )
            
            try:
                assignment2 = RecurringMentionShow.objects.create(
                    recurring_mention=recurring_mention2,
                    show=show,
                    scheduled_time=time(8, 0)  # 8:00 AM - should be invalid
                )
                self.stdout.write(self.style.ERROR('❌ Invalid early time was accepted (should be rejected)'))
            except ValidationError as e:
                self.stdout.write(self.style.SUCCESS(f'✅ Invalid early time rejected: {e}'))
            
            # Test 3: Invalid time after show ends
            self.stdout.write('\n--- Test 3: Invalid time after show (1:00 PM) ---')
            recurring_mention3 = RecurringMention.objects.create(
                title='Test Late Time',
                content='Test content for late time',
                client=client,
                frequency='daily',
                interval=1,
                start_date=date.today(),
                end_date=date.today() + timedelta(days=7),
                created_by=user
            )
            
            try:
                assignment3 = RecurringMentionShow.objects.create(
                    recurring_mention=recurring_mention3,
                    show=show,
                    scheduled_time=time(13, 0)  # 1:00 PM - should be invalid
                )
                self.stdout.write(self.style.ERROR('❌ Invalid late time was accepted (should be rejected)'))
            except ValidationError as e:
                self.stdout.write(self.style.SUCCESS(f'✅ Invalid late time rejected: {e}'))
            
            # Test 4: Edge case - exactly at start time
            self.stdout.write('\n--- Test 4: Edge case - exactly at start time (9:00 AM) ---')
            recurring_mention4 = RecurringMention.objects.create(
                title='Test Start Time',
                content='Test content for start time',
                client=client,
                frequency='daily',
                interval=1,
                start_date=date.today(),
                end_date=date.today() + timedelta(days=7),
                created_by=user
            )
            
            try:
                assignment4 = RecurringMentionShow.objects.create(
                    recurring_mention=recurring_mention4,
                    show=show,
                    scheduled_time=time(9, 0)  # 9:00 AM - should be valid
                )
                self.stdout.write(self.style.SUCCESS('✅ Start time accepted: 9:00 AM'))
            except ValidationError as e:
                self.stdout.write(self.style.ERROR(f'❌ Start time rejected: {e}'))
            
            # Test 5: Edge case - exactly at end time
            self.stdout.write('\n--- Test 5: Edge case - exactly at end time (12:00 PM) ---')
            recurring_mention5 = RecurringMention.objects.create(
                title='Test End Time',
                content='Test content for end time',
                client=client,
                frequency='daily',
                interval=1,
                start_date=date.today(),
                end_date=date.today() + timedelta(days=7),
                created_by=user
            )
            
            try:
                assignment5 = RecurringMentionShow.objects.create(
                    recurring_mention=recurring_mention5,
                    show=show,
                    scheduled_time=time(12, 0)  # 12:00 PM - should be valid
                )
                self.stdout.write(self.style.SUCCESS('✅ End time accepted: 12:00 PM'))
            except ValidationError as e:
                self.stdout.write(self.style.ERROR(f'❌ End time rejected: {e}'))
            
            # Test show time validation method directly
            self.stdout.write('\n--- Testing show.is_time_within_show() method ---')
            test_times = [
                (time(8, 0), False, '8:00 AM (before show)'),
                (time(9, 0), True, '9:00 AM (start time)'),
                (time(10, 30), True, '10:30 AM (during show)'),
                (time(12, 0), True, '12:00 PM (end time)'),
                (time(13, 0), False, '1:00 PM (after show)'),
            ]
            
            for test_time, expected, description in test_times:
                result = show.is_time_within_show(test_time)
                if result == expected:
                    self.stdout.write(self.style.SUCCESS(f'✅ {description}: {result} (expected {expected})'))
                else:
                    self.stdout.write(self.style.ERROR(f'❌ {description}: {result} (expected {expected})'))
            
            if options['cleanup']:
                self.stdout.write('\n--- Cleaning up test data ---')
                org.delete()  # This will cascade delete everything
                user.delete()
                self.stdout.write('✅ Test data cleaned up')
            else:
                self.stdout.write(f'\n--- Test data preserved ---')
                self.stdout.write(f'Organization: {org.slug}')
                self.stdout.write(f'Show: {show.name} ({show.get_time_frame_display()})')
                self.stdout.write('Use --cleanup flag to remove test data')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Test failed with error: {str(e)}')
            )
            raise
