from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, time
from apps.core.models import Client
from apps.mentions.models import Mention, MentionReading
from apps.shows.models import Show
from apps.organizations.middleware import get_current_organization


class Command(BaseCommand):
    help = 'Create sample mentions for telecommunications and government clients'

    def add_arguments(self, parser):
        parser.add_argument(
            '--org-id',
            type=int,
            help='Organization ID to create mentions for',
            required=True
        )

    def handle(self, *args, **options):
        org_id = options['org_id']
        
        try:
            # Get telecommunications and government clients
            telecom_client = Client.objects.filter(
                organization_id=org_id,
                industry='telecommunications'
            ).first()
            
            gov_client = Client.objects.filter(
                organization_id=org_id,
                industry='government'
            ).first()
            
            if not telecom_client:
                self.stdout.write(
                    self.style.ERROR('No telecommunications client found. Please create one first.')
                )
                return
                
            if not gov_client:
                self.stdout.write(
                    self.style.ERROR('No government client found. Please create one first.')
                )
                return
            
            # Get today's show (or create a default one)
            today = timezone.now().date()
            show = Show.objects.filter(
                organization_id=org_id,
                start_time__lte=time(10, 0),
                end_time__gte=time(9, 0)
            ).first()
            
            if not show:
                self.stdout.write(
                    self.style.WARNING('No suitable show found. Creating mentions without show assignment.')
                )
            
            # Sample mentions data
            mentions_data = [
                {
                    'client': telecom_client,
                    'title': '5G Network Launch',
                    'content': 'Experience lightning-fast 5G speeds now available in Kampala! Get connected to the future with our new 5G network. Visit our stores today for exclusive launch offers and be among the first to experience next-generation connectivity.',
                    'time': time(9, 0),
                    'duration': 30
                },
                {
                    'client': gov_client,
                    'title': 'Public Health Campaign',
                    'content': 'The Ministry of Health reminds all citizens to maintain proper hygiene practices. Wash your hands regularly, wear masks in crowded places, and get vaccinated. Your health is our priority. Together we can build a healthier Uganda.',
                    'time': time(9, 15),
                    'duration': 45
                },
                {
                    'client': telecom_client,
                    'title': 'Data Bundle Promotion',
                    'content': 'Double your data this weekend! Get 2GB for the price of 1GB. Perfect for streaming, browsing, and staying connected with family and friends. Dial *123# to activate now. Limited time offer!',
                    'time': time(9, 30),
                    'duration': 25
                },
                {
                    'client': gov_client,
                    'title': 'Road Safety Awareness',
                    'content': 'Drive safely, arrive alive. The Uganda Police Traffic Department urges all motorists to observe speed limits, wear seatbelts, and avoid using phones while driving. Road safety is everyone\'s responsibility.',
                    'time': time(9, 45),
                    'duration': 35
                },
                {
                    'client': telecom_client,
                    'title': 'Mobile Money Security',
                    'content': 'Protect your mobile money! Never share your PIN with anyone. Always verify recipient details before sending money. Our customer care will never ask for your PIN. Stay safe, stay smart.',
                    'time': time(10, 0),
                    'duration': 30
                },
                {
                    'client': gov_client,
                    'title': 'Tax Compliance Reminder',
                    'content': 'Uganda Revenue Authority reminds all taxpayers that the deadline for filing annual returns is approaching. File your returns online at ura.go.ug or visit our nearest office. Compliance is your civic duty.',
                    'time': time(9, 55),
                    'duration': 40
                }
            ]
            
            created_count = 0
            
            for mention_data in mentions_data:
                # Create the mention
                mention = Mention.objects.create(
                    client=mention_data['client'],
                    title=mention_data['title'],
                    content=mention_data['content'],
                    duration_seconds=mention_data['duration'],
                    priority=2,  # Normal priority
                    status='scheduled'  # Approved status
                )
                
                # Create the reading schedule for today
                reading = MentionReading.objects.create(
                    mention=mention,
                    scheduled_date=today,
                    scheduled_time=mention_data['time'],
                    show=show
                )
                
                created_count += 1
                self.stdout.write(
                    f"Created mention: {mention.title} for {mention.client.name} at {mention_data['time']}"
                )
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created {created_count} sample mentions!')
            )
            self.stdout.write(
                self.style.SUCCESS('Visit /live-show/ to see the alternating alignment in action!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating sample mentions: {str(e)}')
            )
