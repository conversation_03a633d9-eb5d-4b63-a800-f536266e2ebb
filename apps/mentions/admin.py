from django.contrib import admin
from .models import Mention, MentionReading


class MentionReadingInline(admin.TabularInline):
    model = MentionReading
    extra = 0
    fields = ['show', 'presenter', 'scheduled_date', 'scheduled_time', 'actual_read_time']
    readonly_fields = ['actual_read_time']


@admin.register(Mention)
class MentionAdmin(admin.ModelAdmin):
    list_display = ['title', 'client', 'status', 'priority', 'created_by', 'created_at']
    list_filter = ['status', 'priority', 'created_at', 'client']
    search_fields = ['title', 'content', 'client__name']
    list_editable = ['status', 'priority']
    ordering = ['-priority', '-created_at']
    readonly_fields = ['created_at', 'updated_at', 'approved_at']
    inlines = [MentionReadingInline]

    fieldsets = (
        ('Mention Details', {
            'fields': ('client', 'title', 'content', 'duration_seconds')
        }),
        ('Status & Priority', {
            'fields': ('status', 'priority', 'notes')
        }),
        ('Approval', {
            'fields': ('created_by', 'approved_by', 'approved_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(MentionReading)
class MentionReadingAdmin(admin.ModelAdmin):
    list_display = ['mention', 'show', 'presenter', 'scheduled_date', 'scheduled_time', 'is_completed', 'created_at']
    list_filter = ['scheduled_date', 'show', 'presenter', 'created_at']
    search_fields = ['mention__title', 'show__name', 'presenter__first_name', 'presenter__last_name']
    ordering = ['scheduled_date', 'scheduled_time']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Reading Details', {
            'fields': ('mention', 'show', 'presenter')
        }),
        ('Schedule', {
            'fields': ('scheduled_date', 'scheduled_time')
        }),
        ('Completion', {
            'fields': ('actual_read_time', 'duration_seconds', 'read_by', 'notes')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
