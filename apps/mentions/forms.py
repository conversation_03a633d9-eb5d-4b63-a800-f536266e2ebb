from django import forms
from django.core.exceptions import ValidationError
from datetime import datetime, timedelta, time
from django.utils import timezone

from .models import Mention, MentionReading, RecurringMention, RecurringMentionShow
from apps.core.models import Client, Presenter
from apps.shows.models import Show
from apps.organizations.middleware import get_current_organization


class MentionForm(forms.ModelForm):
    """Form for creating and editing mentions"""

    class Meta:
        model = Mention
        fields = ['client', 'title', 'content', 'priority', 'duration_seconds', 'notes']
        widgets = {
            'client': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'title': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter mention title'
            }),
            'content': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'rows': 6,
                'placeholder': 'Enter the mention content that will be read on air...'
            }),
            'priority': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'duration_seconds': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'min': '5',
                'max': '300'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'rows': 3,
                'placeholder': 'Additional notes or instructions...'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        # Filter clients by organization
        if self.organization:
            self.fields['client'].queryset = Client.objects.filter(
                organization=self.organization,
                is_active=True
            )

        # Add help text
        self.fields['duration_seconds'].help_text = "Expected duration in seconds (5-300)"
        self.fields['content'].help_text = "This is the exact text that will be read on air"


class MentionWithScheduleForm(forms.ModelForm):
    """Enhanced form for creating mentions with immediate scheduling"""

    # Scheduling fields
    schedule_immediately = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
        }),
        help_text="Schedule this mention to specific shows and times"
    )

    class Meta:
        model = Mention
        fields = ['client', 'title', 'content', 'priority', 'duration_seconds', 'notes']
        widgets = {
            'client': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'title': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter mention title'
            }),
            'content': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'rows': 6,
                'placeholder': 'Enter the mention content that will be read on air...'
            }),
            'priority': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'duration_seconds': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'min': '5',
                'max': '300'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'rows': 3,
                'placeholder': 'Additional notes or instructions...'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        # Filter clients by organization
        if self.organization:
            self.fields['client'].queryset = Client.objects.filter(
                organization=self.organization,
                is_active=True
            )

        # Add help text
        self.fields['duration_seconds'].help_text = "Expected duration in seconds (5-300)"
        self.fields['content'].help_text = "This is the exact text that will be read on air"

    def clean_content(self):
        content = self.cleaned_data.get('content')
        if content:
            # Basic content validation
            if len(content.strip()) < 10:
                raise ValidationError("Mention content must be at least 10 characters long.")
            if len(content) > 2000:
                raise ValidationError("Mention content cannot exceed 2000 characters.")
        return content

    def clean_duration_seconds(self):
        duration = self.cleaned_data.get('duration_seconds')
        if duration:
            if duration < 5:
                raise ValidationError("Duration must be at least 5 seconds.")
            if duration > 300:
                raise ValidationError("Duration cannot exceed 300 seconds (5 minutes).")
        return duration


class MentionReadingForm(forms.ModelForm):
    """Form for scheduling mention readings"""
    
    class Meta:
        model = MentionReading
        fields = ['show', 'presenter', 'scheduled_date', 'scheduled_time', 'notes']
        widgets = {
            'show': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'presenter': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'scheduled_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'scheduled_time': forms.TimeInput(attrs={
                'type': 'time',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'rows': 3,
                'placeholder': 'Additional scheduling notes...'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        
        # Filter by organization
        if self.organization:
            self.fields['show'].queryset = Show.objects.filter(
                organization=self.organization,
                is_active=True
            )
            self.fields['presenter'].queryset = Presenter.objects.filter(
                organization=self.organization,
                is_active=True
            )

    def clean(self):
        cleaned_data = super().clean()
        scheduled_date = cleaned_data.get('scheduled_date')
        scheduled_time = cleaned_data.get('scheduled_time')
        show = cleaned_data.get('show')
        presenter = cleaned_data.get('presenter')

        if scheduled_date and scheduled_time:
            # Check if date is in the past
            scheduled_datetime = datetime.combine(scheduled_date, scheduled_time)
            if scheduled_datetime < timezone.now():
                raise ValidationError("Cannot schedule mentions in the past.")

        # Check for conflicts if we have all required fields
        if all([scheduled_date, scheduled_time, show, presenter]):
            # Check for existing readings at the same time
            existing = MentionReading.objects.filter(
                show=show,
                scheduled_date=scheduled_date,
                scheduled_time=scheduled_time
            )
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise ValidationError(
                    f"There is already a mention scheduled for {show.name} "
                    f"at {scheduled_time} on {scheduled_date}."
                )

        return cleaned_data


class RecurringMentionForm(forms.ModelForm):
    """
    DEPRECATED: Form for creating recurring mentions

    This form is deprecated in favor of the new 4-step wizard system.
    Use the recurring_wizard_step* views instead.

    This form is kept for editing existing recurring mentions only.
    """
    
    class Meta:
        model = RecurringMention
        fields = [
            'client', 'title', 'content', 'priority', 'duration_seconds',
            'frequency', 'interval', 'weekdays', 'start_date', 'end_date',
            'max_occurrences'
        ]
        widgets = {
            'client': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'title': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter recurring mention title'
            }),
            'content': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'rows': 6,
                'placeholder': 'Enter the mention content...'
            }),
            'priority': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'duration_seconds': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'min': '5',
                'max': '300'
            }),
            'frequency': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'interval': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'min': '1',
                'max': '52'
            }),
            'start_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'end_date': forms.DateInput(attrs={
                'type': 'date',
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
            }),
            'max_occurrences': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500',
                'min': '1'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)
        
        # Filter clients by organization
        if self.organization:
            self.fields['client'].queryset = Client.objects.filter(
                organization=self.organization,
                is_active=True
            )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        frequency = cleaned_data.get('frequency')
        weekdays = cleaned_data.get('weekdays')

        # Validate date range
        if start_date and end_date:
            if end_date <= start_date:
                raise ValidationError("End date must be after start date.")

        # Validate weekdays for weekly frequency
        if frequency == 'weekly' and not weekdays:
            raise ValidationError("Please select at least one weekday for weekly recurrence.")

        return cleaned_data


class MentionFilterForm(forms.Form):
    """Form for filtering mentions"""

    STATUS_CHOICES = [('', 'All Statuses')] + Mention.STATUS_CHOICES
    PRIORITY_CHOICES = [('', 'All Priorities')] + Mention.PRIORITY_CHOICES

    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )
    priority = forms.ChoiceField(
        choices=PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )
    client = forms.ModelChoiceField(
        queryset=Client.objects.none(),
        required=False,
        empty_label="All Clients",
        widget=forms.Select(attrs={
            'class': 'w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500',
            'placeholder': 'Search mentions...'
        })
    )

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        if organization:
            self.fields['client'].queryset = Client.objects.filter(
                organization=organization,
                is_active=True
            )


class RecurringMentionFilterForm(forms.Form):
    """Form for filtering recurring mentions"""

    STATUS_CHOICES = [('', 'All Status')] + RecurringMention.STATUS_CHOICES
    FREQUENCY_CHOICES = [('', 'All Frequencies')] + RecurringMention.FREQUENCY_CHOICES
    PRIORITY_CHOICES = [('', 'All Priorities')] + Mention.PRIORITY_CHOICES

    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    frequency = forms.ChoiceField(
        choices=FREQUENCY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    priority = forms.ChoiceField(
        choices=PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    client = forms.ModelChoiceField(
        queryset=Client.objects.none(),
        required=False,
        empty_label="All Clients",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Search by title, content, or campaign name...'
        })
    )
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
        })
    )

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        if organization:
            self.fields['client'].queryset = Client.objects.filter(
                organization=organization,
                is_active=True
            ).order_by('name')


class ClientOrderScheduleFilterForm(forms.Form):
    """Form for filtering client order schedule report"""

    STATUS_CHOICES = [
        ('', 'All Status'),
        ('completed', 'Completed'),
        ('pending', 'Pending'),
    ]

    client_id = forms.ModelChoiceField(
        queryset=Client.objects.none(),
        required=False,
        empty_label="All Clients",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        })
    )
    mention_title = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            'placeholder': 'Search by mention title...'
        })
    )
    show = forms.ModelChoiceField(
        queryset=None,
        required=False,
        empty_label="All Shows",
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        })
    )
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        })
    )
    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        })
    )
    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        })
    )

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        if organization:
            from apps.core.models import Client
            from apps.shows.models import Show

            self.fields['client_id'].queryset = Client.objects.filter(
                organization=organization,
                is_active=True
            ).order_by('name')

            self.fields['show'].queryset = Show.objects.filter(
                organization=organization,
                is_active=True
            ).order_by('name')


class BulkActionForm(forms.Form):
    """Form for bulk actions on mentions"""

    ACTION_CHOICES = [
        ('', 'Select Action'),
        ('approve', 'Approve Selected'),
        ('reject', 'Reject Selected'),
        ('delete', 'Delete Selected'),
        ('change_priority', 'Change Priority'),
        ('change_status', 'Change Status'),
    ]

    action = forms.ChoiceField(
        choices=ACTION_CHOICES,
        required=True,
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )

    # Optional fields for specific actions
    new_priority = forms.ChoiceField(
        choices=Mention.PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )

    new_status = forms.ChoiceField(
        choices=Mention.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )

    selected_mentions = forms.CharField(
        widget=forms.HiddenInput(),
        required=True
    )

    def clean(self):
        cleaned_data = super().clean()
        action = cleaned_data.get('action')

        if action == 'change_priority' and not cleaned_data.get('new_priority'):
            raise ValidationError("Please select a new priority.")

        if action == 'change_status' and not cleaned_data.get('new_status'):
            raise ValidationError("Please select a new status.")

        return cleaned_data


class QuickScheduleForm(forms.Form):
    """Form for quick scheduling of mentions"""

    mention = forms.ModelChoiceField(
        queryset=Mention.objects.none(),
        widget=forms.HiddenInput()
    )
    show = forms.ModelChoiceField(
        queryset=Show.objects.none(),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )
    presenter = forms.ModelChoiceField(
        queryset=Presenter.objects.none(),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )
    scheduled_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )
    scheduled_time = forms.TimeField(
        widget=forms.TimeInput(attrs={
            'type': 'time',
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500'
        })
    )

    def __init__(self, *args, **kwargs):
        organization = kwargs.pop('organization', None)
        super().__init__(*args, **kwargs)

        if organization:
            self.fields['mention'].queryset = Mention.objects.filter(
                client__organization=organization,
                status='pending'
            )
            self.fields['show'].queryset = Show.objects.filter(
                organization=organization,
                is_active=True
            )
            self.fields['presenter'].queryset = Presenter.objects.filter(
                organization=organization,
                is_active=True
            )
