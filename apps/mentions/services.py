from datetime import datetime, timedelta
from django.db.models import Q
from django.utils import timezone
from .models import MentionReading, Mention
from apps.activity_logs.models import ConflictLog, ActivityLog


class ConflictDetectionService:
    """Service for detecting and resolving scheduling conflicts"""
    
    @staticmethod
    def detect_conflicts(mention_reading=None, organization=None):
        """Detect scheduling conflicts for a specific reading or organization"""
        conflicts = []
        
        if mention_reading:
            conflicts.extend(ConflictDetectionService._check_reading_conflicts(mention_reading))
        elif organization:
            # Check all active readings in the organization - optimized with select_related
            readings = MentionReading.objects.filter(
                mention__client__organization=organization,
                actual_read_time__isnull=True  # Only check unread mentions
            ).select_related('mention', 'show', 'presenter')[:100]  # Limit to prevent performance issues

            for reading in readings:
                conflicts.extend(ConflictDetectionService._check_reading_conflicts(reading))
        
        return conflicts
    
    @staticmethod
    def _check_reading_conflicts(reading):
        """Check conflicts for a specific reading"""
        conflicts = []

        # Time overlap conflicts
        time_conflicts = ConflictDetectionService._check_time_overlap(reading)
        conflicts.extend(time_conflicts)

        # Show capacity conflicts
        capacity_conflicts = ConflictDetectionService._check_show_capacity(reading)
        conflicts.extend(capacity_conflicts)

        # Show presenter availability conflicts (new)
        presenter_availability_conflicts = ConflictDetectionService._check_show_presenter_availability(reading)
        conflicts.extend(presenter_availability_conflicts)

        return conflicts
    
    @staticmethod
    def _check_time_overlap(reading):
        """Check for time overlap conflicts"""
        conflicts = []

        # Check if overlapping mentions are allowed in organization settings
        try:
            organization = reading.mention.client.organization
            settings = organization.settings
            if settings.allow_overlapping_mentions:
                # Skip time overlap checks if overlapping mentions are allowed
                return conflicts
        except:
            # If we can't get settings, proceed with conflict detection as default
            pass

        start_time = reading.scheduled_datetime
        end_time = start_time + timedelta(seconds=reading.mention.duration_seconds)

        # Find overlapping readings on the same show
        overlapping_readings = MentionReading.objects.filter(
            show=reading.show,
            scheduled_date=reading.scheduled_date,
            actual_read_time__isnull=True
        ).exclude(pk=reading.pk)

        for other_reading in overlapping_readings:
            other_start = other_reading.scheduled_datetime
            other_end = other_start + timedelta(seconds=other_reading.mention.duration_seconds)

            # Check for overlap
            if start_time < other_end and end_time > other_start:
                conflict = {
                    'type': 'time_overlap',
                    'reading1': reading,
                    'reading2': other_reading,
                    'description': f"Time overlap between '{reading.mention.title}' and '{other_reading.mention.title}'",
                    'severity': ConflictDetectionService._calculate_overlap_severity(start_time, end_time, other_start, other_end)
                }
                conflicts.append(conflict)

        return conflicts
    
    @staticmethod
    def _check_show_presenter_availability(reading):
        """Check if the show has available presenters"""
        conflicts = []

        # Check if the show has any active presenters assigned
        active_presenters = reading.show.showpresenter_set.filter(is_active=True).count()

        if active_presenters == 0:
            conflict = {
                'type': 'no_presenter_available',
                'reading1': reading,
                'reading2': None,
                'description': f"Show '{reading.show.name}' has no active presenters assigned",
                'severity': 'high'
            }
            conflicts.append(conflict)

        # Check if the show is scheduled on a day it doesn't air
        if not reading.show.is_day_valid_for_show(reading.scheduled_date):
            weekday_name = reading.scheduled_date.strftime('%A')
            conflict = {
                'type': 'invalid_show_day',
                'reading1': reading,
                'reading2': None,
                'description': f"Show '{reading.show.name}' does not air on {weekday_name}s",
                'severity': 'high'
            }
            conflicts.append(conflict)

        return conflicts
    
    @staticmethod
    def _check_show_capacity(reading):
        """Check if show capacity is exceeded"""
        conflicts = []
        
        # Get organization settings for max mentions per hour
        try:
            settings = reading.show.organization.settings
            max_per_hour = settings.max_mentions_per_hour
        except:
            max_per_hour = 6  # Default
        
        # Count mentions in the same hour
        hour_start = reading.scheduled_datetime.replace(minute=0, second=0, microsecond=0)
        hour_end = hour_start + timedelta(hours=1)
        
        mentions_in_hour = MentionReading.objects.filter(
            show=reading.show,
            scheduled_date=reading.scheduled_date,
            scheduled_time__gte=hour_start.time(),
            scheduled_time__lt=hour_end.time(),
            actual_read_time__isnull=True
        ).count()
        
        if mentions_in_hour > max_per_hour:
            conflict = {
                'type': 'show_capacity_exceeded',
                'reading1': reading,
                'reading2': None,
                'description': f"Show capacity exceeded: {mentions_in_hour} mentions in hour (max: {max_per_hour})",
                'severity': 'medium'
            }
            conflicts.append(conflict)
        
        return conflicts
    
    @staticmethod
    def _calculate_overlap_severity(start1, end1, start2, end2):
        """Calculate severity of time overlap"""
        overlap_start = max(start1, start2)
        overlap_end = min(end1, end2)
        overlap_duration = (overlap_end - overlap_start).total_seconds()
        
        total_duration = min((end1 - start1).total_seconds(), (end2 - start2).total_seconds())
        overlap_percentage = (overlap_duration / total_duration) * 100
        
        if overlap_percentage > 75:
            return 'high'
        elif overlap_percentage > 25:
            return 'medium'
        else:
            return 'low'
    
    @staticmethod
    def log_conflict(conflict, organization, user=None):
        """Log a detected conflict"""
        conflict_log = ConflictLog.objects.create(
            organization=organization,
            conflict_type=conflict['type'],
            mention1=conflict['reading1'].mention,
            mention2=conflict['reading2'].mention if conflict['reading2'] else conflict['reading1'].mention,
            description=conflict['description'],
            severity=conflict['severity']
        )
        
        # Log activity
        ActivityLog.log_activity(
            user=user,
            organization=organization,
            action='conflict_detected',
            description=conflict['description'],
            content_object=conflict_log,
            level='warning'
        )
        
        return conflict_log
    
    @staticmethod
    def resolve_conflict(conflict_log, resolution_strategy, user=None):
        """Resolve a conflict using the specified strategy"""
        if resolution_strategy == 'priority':
            return ConflictDetectionService._resolve_by_priority(conflict_log, user)
        elif resolution_strategy == 'first_come':
            return ConflictDetectionService._resolve_by_first_come(conflict_log, user)
        else:
            return False
    
    @staticmethod
    def _resolve_by_priority(conflict_log, user):
        """Resolve conflict by priority"""
        mention1 = conflict_log.mention1
        mention2 = conflict_log.mention2
        
        if mention1.priority > mention2.priority:
            # Keep mention1, reschedule mention2
            ConflictDetectionService._reschedule_mention(mention2)
            winner = mention1
        elif mention2.priority > mention1.priority:
            # Keep mention2, reschedule mention1
            ConflictDetectionService._reschedule_mention(mention1)
            winner = mention2
        else:
            # Same priority, use first come first serve
            return ConflictDetectionService._resolve_by_first_come(conflict_log, user)
        
        # Mark conflict as resolved
        conflict_log.status = 'resolved'
        conflict_log.resolved_by = user
        conflict_log.resolved_at = datetime.now()
        conflict_log.resolution_notes = f"Resolved by priority. {winner.title} kept, other mention rescheduled."
        conflict_log.save()
        
        return True
    
    @staticmethod
    def _resolve_by_first_come(conflict_log, user):
        """Resolve conflict by creation time"""
        mention1 = conflict_log.mention1
        mention2 = conflict_log.mention2
        
        if mention1.created_at < mention2.created_at:
            ConflictDetectionService._reschedule_mention(mention2)
            winner = mention1
        else:
            ConflictDetectionService._reschedule_mention(mention1)
            winner = mention2
        
        # Mark conflict as resolved
        conflict_log.status = 'resolved'
        conflict_log.resolved_by = user
        conflict_log.resolved_at = datetime.now()
        conflict_log.resolution_notes = f"Resolved by first come first serve. {winner.title} kept, other mention rescheduled."
        conflict_log.save()
        
        return True
    
    @staticmethod
    def _reschedule_mention(mention):
        """Reschedule a mention to avoid conflicts"""
        try:
            # Find the mention's current reading
            reading = mention.mentionreading_set.filter(actual_read_time__isnull=True).first()
            if not reading:
                print(f"No active reading found for mention: {mention.title}")
                return False

            print(f"Attempting to reschedule mention: {mention.title} from {reading.scheduled_time}")

            # Store original details
            original_time = reading.scheduled_time
            original_date = reading.scheduled_date

            # Try 15-minute intervals for the next 6 hours (more granular and longer range)
            for minutes in range(15, 360, 15):
                try:
                    new_datetime = datetime.combine(original_date, original_time) + timedelta(minutes=minutes)
                    new_time = new_datetime.time()
                    new_date = new_datetime.date()

                    # Skip if we've moved to the next day (for now)
                    if new_date != original_date:
                        continue

                    # Create a temporary reading to check for conflicts
                    temp_reading = MentionReading(
                        mention=mention,
                        show=reading.show,
                        presenter=reading.presenter,
                        scheduled_date=new_date,
                        scheduled_time=new_time
                    )

                    # Check if this time slot is available using the model method
                    if not temp_reading.has_conflicts():
                        # Update the existing reading
                        reading.scheduled_time = new_time
                        reading.scheduled_date = new_date
                        reading.save()

                        print(f"Successfully rescheduled to {new_time} on {new_date}")

                        # Add a note to the mention
                        note = f"Automatically rescheduled from {original_time} to {new_time} due to conflict resolution."
                        if mention.notes:
                            mention.notes = f"{mention.notes}\n\n{note}"
                        else:
                            mention.notes = note
                        mention.save()

                        return True

                except (ValueError, OverflowError) as e:
                    print(f"Error calculating new time: {e}")
                    continue

            print(f"Could not find alternative slot for mention: {mention.title}")

            # If no slot found, mark as needs manual rescheduling
            mention.status = 'pending'
            note = "Automatic rescheduling failed - manual intervention required."
            if mention.notes:
                mention.notes = f"{mention.notes}\n\n{note}"
            else:
                mention.notes = note
            mention.save()

            # Delete the conflicting reading
            reading.delete()

            return False

        except Exception as e:
            print(f"Error in _reschedule_mention: {e}")
            return False


class RecurringMentionService:
    """Service for managing recurring mentions"""
    
    @staticmethod
    def generate_upcoming_mentions(recurring_mention, days_ahead=None):
        """Generate mentions for the recurring pattern period"""
        from datetime import date

        # If no days_ahead specified, use the recurring mention's end date or a reasonable default
        if days_ahead is None:
            if recurring_mention.end_date:
                # Generate until the end date of the recurring mention
                end_date = recurring_mention.end_date
            else:
                # If no end date, generate for 90 days (3 months) by default
                end_date = date.today() + timedelta(days=90)
        else:
            # Use the specified days_ahead
            end_date = date.today() + timedelta(days=days_ahead)

        return recurring_mention.generate_mentions(end_date)
    
    @staticmethod
    def update_recurring_pattern(recurring_mention, **kwargs):
        """Update a recurring mention pattern and regenerate future mentions only if needed"""
        from datetime import date

        # Store original values to check if regeneration is needed
        original_values = {
            'title': recurring_mention.title,
            'content': recurring_mention.content,
            'frequency': recurring_mention.frequency,
            'interval': recurring_mention.interval,
            'weekdays': recurring_mention.weekdays,
            'start_date': recurring_mention.start_date,
            'end_date': recurring_mention.end_date,
            'max_occurrences': recurring_mention.max_occurrences,
            'priority': recurring_mention.priority,
            'duration_seconds': recurring_mention.duration_seconds,
        }

        # Check if any pattern-affecting fields are changing
        pattern_changed = False
        content_changed = False

        for key, new_value in kwargs.items():
            old_value = getattr(recurring_mention, key)
            if old_value != new_value:
                if key in ['frequency', 'interval', 'weekdays', 'start_date', 'end_date', 'max_occurrences']:
                    pattern_changed = True
                elif key in ['title', 'content', 'priority', 'duration_seconds']:
                    content_changed = True

        # Update the recurring mention
        for key, value in kwargs.items():
            setattr(recurring_mention, key, value)
        recurring_mention.save()

        # If only content changed, update existing mentions without regenerating
        if content_changed and not pattern_changed:
            RecurringMentionService._update_existing_mentions_content(recurring_mention, original_values)
            return []

        # If pattern changed, regenerate future mentions
        if pattern_changed:
            RecurringMentionService._regenerate_future_mentions(recurring_mention)
            return RecurringMentionService.generate_upcoming_mentions(recurring_mention)

        return []

    @staticmethod
    def _update_existing_mentions_content(recurring_mention, original_values):
        """Update content of existing unread mentions without regenerating"""
        from datetime import date

        # Find existing mentions that belong to this recurring pattern
        # Use the recurring_mention field for precise identification
        existing_mentions = Mention.objects.filter(
            recurring_mention=recurring_mention,
            status__in=['pending', 'scheduled']
        ).exclude(
            mentionreading__actual_read_time__isnull=False
        )

        # Update content fields for unread mentions
        updated_count = 0
        for mention in existing_mentions:
            mention.title = recurring_mention.title
            mention.content = recurring_mention.content
            mention.priority = recurring_mention.priority
            mention.duration_seconds = recurring_mention.duration_seconds
            mention.save()
            updated_count += 1

        print(f"Updated {updated_count} existing mentions with new content")

    @staticmethod
    def _regenerate_future_mentions(recurring_mention):
        """Delete future unread mentions and prepare for regeneration"""
        from datetime import date

        # Delete future unread mentions more precisely using the recurring_mention field
        future_mentions = Mention.objects.filter(
            recurring_mention=recurring_mention,
            status__in=['pending', 'scheduled']
        )

        deleted_count = 0
        for mention in future_mentions:
            # Only delete if not yet read and scheduled for future dates
            readings = mention.mentionreading_set.all()
            has_completed_reading = readings.filter(actual_read_time__isnull=False).exists()
            has_future_readings = readings.filter(scheduled_date__gte=date.today()).exists()

            if not has_completed_reading and has_future_readings:
                mention.delete()
                deleted_count += 1

        print(f"Deleted {deleted_count} future mentions for regeneration")


class SchedulePatternService:
    """Service for copying and managing recurring mention schedule patterns"""

    @staticmethod
    def copy_schedule_pattern(source_recurring_mention, target_recurring_mention):
        """
        Copy the complete schedule pattern from source to target recurring mention.
        This includes frequency, interval, weekdays, and all show assignments.
        """
        from .models import RecurringMentionShow

        # Copy basic schedule settings
        target_recurring_mention.frequency = source_recurring_mention.frequency
        target_recurring_mention.interval = source_recurring_mention.interval
        target_recurring_mention.weekdays = source_recurring_mention.weekdays.copy()
        target_recurring_mention.monthdays = source_recurring_mention.monthdays.copy()
        target_recurring_mention.save()

        # Copy show assignments
        source_show_assignments = source_recurring_mention.recurringmentionshow_set.all()

        for assignment in source_show_assignments:
            RecurringMentionShow.objects.create(
                recurring_mention=target_recurring_mention,
                show=assignment.show,
                presenter=assignment.presenter,
                scheduled_time=assignment.scheduled_time,
                scheduled_days=assignment.scheduled_days.copy(),
                is_active=assignment.is_active
            )

    @staticmethod
    def get_schedule_pattern_summary(recurring_mention):
        """
        Get a human-readable summary of the schedule pattern for display purposes.
        """
        from datetime import time

        # Basic pattern info
        frequency_display = recurring_mention.get_frequency_display()
        interval_text = f"every {recurring_mention.interval}" if recurring_mention.interval > 1 else "every"

        # Weekdays for weekly patterns
        weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        weekdays_text = ""
        if recurring_mention.frequency == 'weekly' and recurring_mention.weekdays:
            selected_days = [weekday_names[day] for day in recurring_mention.weekdays]
            weekdays_text = f" on {', '.join(selected_days)}"

        # Show assignments
        show_assignments = recurring_mention.recurringmentionshow_set.all()
        shows_text = []
        for assignment in show_assignments:
            time_str = assignment.scheduled_time.strftime('%H:%M')
            shows_text.append(f"{assignment.show.name} at {time_str}")

        return {
            'frequency_text': f"{interval_text} {frequency_display.lower()}{weekdays_text}",
            'shows_text': shows_text,
            'total_shows': len(shows_text)
        }

    @staticmethod
    def detect_schedule_conflicts(new_recurring_mention, existing_recurring_mentions=None):
        """
        Detect potential conflicts when adding a new recurring mention to existing schedules.
        Returns a list of conflicts with details about overlapping mentions.
        """
        from .models import RecurringMention, RecurringMentionShow
        from datetime import date, timedelta

        conflicts = []

        # Get existing recurring mentions if not provided
        if existing_recurring_mentions is None:
            existing_recurring_mentions = RecurringMention.objects.filter(
                client__organization=new_recurring_mention.client.organization,
                is_active=True
            ).exclude(id=new_recurring_mention.id if new_recurring_mention.id else None)

        # Get new mention's show assignments
        new_assignments = new_recurring_mention.recurringmentionshow_set.all()

        for new_assignment in new_assignments:
            # Check against existing recurring mentions
            for existing_mention in existing_recurring_mentions:
                existing_assignments = existing_mention.recurringmentionshow_set.filter(
                    show=new_assignment.show,
                    scheduled_time=new_assignment.scheduled_time,
                    is_active=True
                )

                for existing_assignment in existing_assignments:
                    # Check if weekdays overlap
                    new_days = set(new_assignment.scheduled_days)
                    existing_days = set(existing_assignment.scheduled_days)

                    if new_days.intersection(existing_days):
                        # Check if date ranges overlap
                        new_start = new_recurring_mention.start_date
                        new_end = new_recurring_mention.end_date or date(2099, 12, 31)
                        existing_start = existing_mention.start_date
                        existing_end = existing_mention.end_date or date(2099, 12, 31)

                        if (new_start <= existing_end and new_end >= existing_start):
                            overlapping_days = new_days.intersection(existing_days)
                            weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
                            day_names = [weekday_names[day] for day in overlapping_days]

                            conflicts.append({
                                'type': 'time_overlap',
                                'severity': 'medium',
                                'existing_mention': existing_mention,
                                'new_assignment': new_assignment,
                                'existing_assignment': existing_assignment,
                                'show': new_assignment.show,
                                'time': new_assignment.scheduled_time,
                                'overlapping_days': day_names,
                                'message': f"Overlaps with '{existing_mention.title}' on {', '.join(day_names)} at {new_assignment.scheduled_time.strftime('%H:%M')} in {new_assignment.show.name}"
                            })

        return conflicts

    @staticmethod
    def validate_schedule_pattern(recurring_mention):
        """
        Validate that a recurring mention's schedule pattern is valid and consistent.
        """
        errors = []

        # Check basic requirements
        if not recurring_mention.frequency:
            errors.append("Frequency is required")

        if recurring_mention.interval < 1:
            errors.append("Interval must be at least 1")

        # Check weekdays for weekly frequency
        if recurring_mention.frequency == 'weekly':
            if not recurring_mention.weekdays:
                errors.append("At least one weekday must be selected for weekly frequency")
            elif not all(0 <= day <= 6 for day in recurring_mention.weekdays):
                errors.append("Invalid weekday values (must be 0-6)")

        # Check monthdays for monthly frequency
        if recurring_mention.frequency == 'monthly':
            if not recurring_mention.monthdays:
                errors.append("At least one month day must be selected for monthly frequency")
            elif not all(1 <= day <= 31 for day in recurring_mention.monthdays):
                errors.append("Invalid month day values (must be 1-31)")

        # Check date range
        if recurring_mention.end_date and recurring_mention.start_date:
            if recurring_mention.end_date <= recurring_mention.start_date:
                errors.append("End date must be after start date")

        # Check show assignments
        show_assignments = recurring_mention.recurringmentionshow_set.all()
        if not show_assignments.exists():
            errors.append("At least one show assignment is required")

        return errors


class ScheduleSplittingService:
    """Service for splitting recurring mention schedules between two mentions"""

    @staticmethod
    def split_schedule(original_mention, split_method, mention_a_data, mention_b_data, user=None):
        """
        Split a recurring mention schedule between two new mentions.

        Args:
            original_mention: The RecurringMention to split
            split_method: 'days', 'times', 'shows', or 'weeks'
            mention_a_data: Dict with title, content, duration_seconds for first mention
            mention_b_data: Dict with title, content, duration_seconds for second mention
            user: User performing the split

        Returns:
            Dict with success status, created mentions, and any warnings
        """
        from datetime import date
        from .models import RecurringMention, MentionAuditLog

        try:
            # Validate split method
            if split_method not in ['days', 'times', 'shows', 'weeks']:
                return {
                    'success': False,
                    'error': f'Invalid split method: {split_method}'
                }

            # Validate that split is possible
            validation_result = ScheduleSplittingService._validate_split_feasibility(
                original_mention, split_method
            )
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error']
                }

            # Create audit log for original mention termination
            original_data = ScheduleSplittingService._capture_mention_data(original_mention)

            MentionAuditLog.log_change(
                change_type='reschedule',
                description=f'Terminated recurring mention "{original_mention.title}" for schedule split',
                original_data=original_data,
                new_data={'termination_date': date.today().isoformat(), 'reason': 'split_into_two_mentions'},
                recurring_mention=original_mention,
                changed_by=user,
                metadata={
                    'split_action': True,
                    'split_method': split_method,
                    'termination_reason': 'Split into two mentions',
                }
            )

            # Terminate original mention and clean up future mentions
            original_mention.end_date = date.today()
            original_mention.is_active = False
            original_mention.save()

            # Clean up future mentions from the terminated pattern to avoid calendar conflicts
            from .models import Mention
            future_mention_ids = Mention.objects.filter(
                recurring_mention=original_mention,
                mentionreading__scheduled_date__gte=date.today(),
                status__in=['pending', 'scheduled']
            ).values_list('id', flat=True)

            if future_mention_ids:
                deleted_count = Mention.objects.filter(id__in=future_mention_ids).delete()[0]
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f'Cleaned up {deleted_count} future mentions from terminated recurring pattern {original_mention.id}')

            # Create the two split mentions
            mention_a = ScheduleSplittingService._create_split_mention(
                original_mention, mention_a_data, 'A', user
            )
            mention_b = ScheduleSplittingService._create_split_mention(
                original_mention, mention_b_data, 'B', user
            )

            # Apply the split logic based on method
            split_result = ScheduleSplittingService._apply_split_logic(
                original_mention, mention_a, mention_b, split_method
            )

            if not split_result['success']:
                return split_result

            # Create audit logs for new mentions
            for mention, label in [(mention_a, 'A'), (mention_b, 'B')]:
                MentionAuditLog.log_change(
                    change_type='created',
                    description=f'Created split mention {label} "{mention.title}" from schedule split',
                    original_data={},
                    new_data=ScheduleSplittingService._capture_mention_data(mention),
                    recurring_mention=mention,
                    changed_by=user,
                    metadata={
                        'split_action': True,
                        'split_method': split_method,
                        'split_label': label,
                        'original_mention_id': original_mention.id,
                        'original_mention_title': original_mention.title,
                    }
                )

            # Generate mentions for both new recurring patterns
            generated_counts = {}
            for mention, label in [(mention_a, 'A'), (mention_b, 'B')]:
                try:
                    generated_mentions = RecurringMentionService.generate_upcoming_mentions(mention)
                    generated_counts[label] = len(generated_mentions)
                except Exception as e:
                    # Log the error but don't fail the split
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f'Error generating mentions for split mention {label}: {str(e)}')
                    generated_counts[label] = 0

            return {
                'success': True,
                'mention_a': mention_a,
                'mention_b': mention_b,
                'split_method': split_method,
                'warnings': split_result.get('warnings', []),
                'generated_counts': generated_counts
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'Error during schedule split: {str(e)}'
            }

    @staticmethod
    def _validate_split_feasibility(original_mention, split_method):
        """Validate that the split method is feasible for the given mention"""

        if split_method == 'days':
            # Need at least 2 weekdays
            if len(original_mention.weekdays) < 2:
                return {
                    'valid': False,
                    'error': 'Cannot split by days: mention runs on fewer than 2 weekdays'
                }

        elif split_method == 'times':
            # Need multiple time slots
            show_assignments = original_mention.recurringmentionshow_set.all()
            if show_assignments.count() < 2:
                return {
                    'valid': False,
                    'error': 'Cannot split by times: mention has fewer than 2 time slots'
                }

        elif split_method == 'shows':
            # Need multiple shows
            shows = set(assignment.show for assignment in original_mention.recurringmentionshow_set.all())
            if len(shows) < 2:
                return {
                    'valid': False,
                    'error': 'Cannot split by shows: mention runs on fewer than 2 different shows'
                }

        elif split_method == 'weeks':
            # Always feasible for weekly patterns
            if original_mention.frequency != 'weekly':
                return {
                    'valid': False,
                    'error': 'Cannot split by weeks: mention is not a weekly pattern'
                }

        return {'valid': True}

    @staticmethod
    def _capture_mention_data(mention):
        """Capture complete mention data for audit purposes"""
        return {
            'title': mention.title,
            'content': mention.content,
            'client_id': mention.client.id,
            'client_name': mention.client.name,
            'frequency': mention.frequency,
            'interval': mention.interval,
            'weekdays': mention.weekdays,
            'monthdays': mention.monthdays,
            'start_date': mention.start_date.isoformat(),
            'end_date': mention.end_date.isoformat() if mention.end_date else None,
            'duration_seconds': mention.duration_seconds,
            'priority': mention.priority,
            'is_active': mention.is_active,
        }

    @staticmethod
    def _create_split_mention(original_mention, mention_data, label, user):
        """Create a new mention based on the original with new data"""
        from datetime import date
        from .models import RecurringMention

        return RecurringMention.objects.create(
            title=mention_data['title'],
            content=mention_data['content'],
            client=original_mention.client,
            frequency=original_mention.frequency,
            interval=original_mention.interval,
            weekdays=original_mention.weekdays.copy(),
            monthdays=original_mention.monthdays.copy(),
            start_date=date.today(),  # Start from today
            end_date=original_mention.end_date,  # Use original end date
            duration_seconds=mention_data.get('duration_seconds', original_mention.duration_seconds),
            priority=original_mention.priority,
            max_occurrences=original_mention.max_occurrences,
            is_active=True,
            created_by=user
        )

    @staticmethod
    def _apply_split_logic(original_mention, mention_a, mention_b, split_method):
        """Apply the specific split logic based on the chosen method"""

        if split_method == 'days':
            return ScheduleSplittingService._split_by_days(original_mention, mention_a, mention_b)
        elif split_method == 'times':
            return ScheduleSplittingService._split_by_times(original_mention, mention_a, mention_b)
        elif split_method == 'shows':
            return ScheduleSplittingService._split_by_shows(original_mention, mention_a, mention_b)
        elif split_method == 'weeks':
            return ScheduleSplittingService._split_by_weeks(original_mention, mention_a, mention_b)
        else:
            return {'success': False, 'error': f'Unknown split method: {split_method}'}

    @staticmethod
    def _split_by_days(original_mention, mention_a, mention_b):
        """Split schedule by weekdays: A gets odd days, B gets even days"""
        from .models import RecurringMentionShow

        try:
            # Divide weekdays
            weekdays = sorted(original_mention.weekdays)
            days_a = weekdays[::2]  # Every other day starting from first (0, 2, 4...)
            days_b = weekdays[1::2]  # Every other day starting from second (1, 3, 5...)

            # Update weekdays for each mention
            mention_a.weekdays = days_a
            mention_a.save()

            mention_b.weekdays = days_b
            mention_b.save()

            # Copy show assignments for both mentions with appropriate days
            original_assignments = original_mention.recurringmentionshow_set.all()

            for assignment in original_assignments:
                # Create assignment for mention A with its days
                RecurringMentionShow.objects.create(
                    recurring_mention=mention_a,
                    show=assignment.show,
                    presenter=assignment.presenter,
                    scheduled_time=assignment.scheduled_time,
                    scheduled_days=days_a,
                    is_active=assignment.is_active
                )

                # Create assignment for mention B with its days
                RecurringMentionShow.objects.create(
                    recurring_mention=mention_b,
                    show=assignment.show,
                    presenter=assignment.presenter,
                    scheduled_time=assignment.scheduled_time,
                    scheduled_days=days_b,
                    is_active=assignment.is_active
                )

            weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            days_a_names = [weekday_names[day] for day in days_a]
            days_b_names = [weekday_names[day] for day in days_b]

            return {
                'success': True,
                'warnings': [],
                'split_details': {
                    'mention_a_days': days_a_names,
                    'mention_b_days': days_b_names
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'Error splitting by days: {str(e)}'}

    @staticmethod
    def _split_by_times(original_mention, mention_a, mention_b):
        """Split schedule by time slots: A gets morning, B gets afternoon"""
        from .models import RecurringMentionShow
        from datetime import time

        try:
            # Define morning/afternoon split (12:00 PM as cutoff)
            noon = time(12, 0)

            original_assignments = original_mention.recurringmentionshow_set.all()
            morning_assignments = []
            afternoon_assignments = []

            for assignment in original_assignments:
                if assignment.scheduled_time < noon:
                    morning_assignments.append(assignment)
                else:
                    afternoon_assignments.append(assignment)

            # Ensure both mentions get at least one time slot
            if not morning_assignments or not afternoon_assignments:
                # If uneven split, divide assignments alternately
                assignments_list = list(original_assignments)
                morning_assignments = assignments_list[::2]  # Every other assignment
                afternoon_assignments = assignments_list[1::2]

            # Create show assignments for mention A (morning/first half)
            for assignment in morning_assignments:
                RecurringMentionShow.objects.create(
                    recurring_mention=mention_a,
                    show=assignment.show,
                    presenter=assignment.presenter,
                    scheduled_time=assignment.scheduled_time,
                    scheduled_days=assignment.scheduled_days.copy(),
                    is_active=assignment.is_active
                )

            # Create show assignments for mention B (afternoon/second half)
            for assignment in afternoon_assignments:
                RecurringMentionShow.objects.create(
                    recurring_mention=mention_b,
                    show=assignment.show,
                    presenter=assignment.presenter,
                    scheduled_time=assignment.scheduled_time,
                    scheduled_days=assignment.scheduled_days.copy(),
                    is_active=assignment.is_active
                )

            return {
                'success': True,
                'warnings': [],
                'split_details': {
                    'mention_a_slots': len(morning_assignments),
                    'mention_b_slots': len(afternoon_assignments)
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'Error splitting by times: {str(e)}'}

    @staticmethod
    def _split_by_shows(original_mention, mention_a, mention_b):
        """Split schedule by shows: A gets first half of shows, B gets second half"""
        from .models import RecurringMentionShow

        try:
            original_assignments = original_mention.recurringmentionshow_set.all()

            # Group assignments by show
            shows_assignments = {}
            for assignment in original_assignments:
                show_id = assignment.show.id
                if show_id not in shows_assignments:
                    shows_assignments[show_id] = []
                shows_assignments[show_id].append(assignment)

            # Split shows between mentions
            shows_list = list(shows_assignments.keys())
            mid_point = len(shows_list) // 2

            shows_a = shows_list[:mid_point] if mid_point > 0 else shows_list[:1]
            shows_b = shows_list[mid_point:] if mid_point > 0 else shows_list[1:]

            # Ensure both mentions get at least one show
            if not shows_b and len(shows_list) > 1:
                shows_b = [shows_list[-1]]
                shows_a = shows_list[:-1]

            # Create assignments for mention A
            for show_id in shows_a:
                for assignment in shows_assignments[show_id]:
                    RecurringMentionShow.objects.create(
                        recurring_mention=mention_a,
                        show=assignment.show,
                        presenter=assignment.presenter,
                        scheduled_time=assignment.scheduled_time,
                        scheduled_days=assignment.scheduled_days.copy(),
                        is_active=assignment.is_active
                    )

            # Create assignments for mention B
            for show_id in shows_b:
                for assignment in shows_assignments[show_id]:
                    RecurringMentionShow.objects.create(
                        recurring_mention=mention_b,
                        show=assignment.show,
                        presenter=assignment.presenter,
                        scheduled_time=assignment.scheduled_time,
                        scheduled_days=assignment.scheduled_days.copy(),
                        is_active=assignment.is_active
                    )

            return {
                'success': True,
                'warnings': [],
                'split_details': {
                    'mention_a_shows': len(shows_a),
                    'mention_b_shows': len(shows_b)
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'Error splitting by shows: {str(e)}'}

    @staticmethod
    def _split_by_weeks(original_mention, mention_a, mention_b):
        """Split schedule by weeks: A gets odd weeks, B gets even weeks"""
        from .models import RecurringMentionShow

        try:
            # For weekly patterns, we modify the interval to create alternating weeks
            # Mention A: starts this week, runs every 2 weeks
            # Mention B: starts next week, runs every 2 weeks

            mention_a.interval = 2  # Every 2 weeks
            mention_a.save()

            mention_b.interval = 2  # Every 2 weeks
            # Mention B starts one week later
            from datetime import date, timedelta
            mention_b.start_date = date.today() + timedelta(weeks=1)
            mention_b.save()

            # Copy all show assignments to both mentions
            original_assignments = original_mention.recurringmentionshow_set.all()

            for assignment in original_assignments:
                # Create assignment for mention A
                RecurringMentionShow.objects.create(
                    recurring_mention=mention_a,
                    show=assignment.show,
                    presenter=assignment.presenter,
                    scheduled_time=assignment.scheduled_time,
                    scheduled_days=assignment.scheduled_days.copy(),
                    is_active=assignment.is_active
                )

                # Create assignment for mention B
                RecurringMentionShow.objects.create(
                    recurring_mention=mention_b,
                    show=assignment.show,
                    presenter=assignment.presenter,
                    scheduled_time=assignment.scheduled_time,
                    scheduled_days=assignment.scheduled_days.copy(),
                    is_active=assignment.is_active
                )

            return {
                'success': True,
                'warnings': [],
                'split_details': {
                    'mention_a_weeks': 'Odd weeks (starting this week)',
                    'mention_b_weeks': 'Even weeks (starting next week)'
                }
            }

        except Exception as e:
            return {'success': False, 'error': f'Error splitting by weeks: {str(e)}'}

    @staticmethod
    def get_split_preview(original_mention, split_method):
        """Generate a preview of how the schedule will be split"""

        if split_method == 'days':
            weekdays = sorted(original_mention.weekdays)
            days_a = weekdays[::2]
            days_b = weekdays[1::2]

            weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            days_a_names = [weekday_names[day] for day in days_a]
            days_b_names = [weekday_names[day] for day in days_b]

            return {
                'method': 'Split by Days',
                'mention_a': f"Runs on: {', '.join(days_a_names)}",
                'mention_b': f"Runs on: {', '.join(days_b_names)}",
                'description': 'Schedule divided by weekdays with alternating pattern'
            }

        elif split_method == 'times':
            assignments = original_mention.recurringmentionshow_set.all()
            from datetime import time
            noon = time(12, 0)

            morning_count = sum(1 for a in assignments if a.scheduled_time < noon)
            afternoon_count = len(assignments) - morning_count

            return {
                'method': 'Split by Time Slots',
                'mention_a': f"Morning slots: {morning_count} time slots",
                'mention_b': f"Afternoon slots: {afternoon_count} time slots",
                'description': 'Schedule divided by time of day (before/after 12:00 PM)'
            }

        elif split_method == 'shows':
            assignments = original_mention.recurringmentionshow_set.all()
            shows = set(assignment.show.name for assignment in assignments)
            shows_list = list(shows)
            mid_point = len(shows_list) // 2

            shows_a = shows_list[:mid_point] if mid_point > 0 else shows_list[:1]
            shows_b = shows_list[mid_point:] if mid_point > 0 else shows_list[1:]

            return {
                'method': 'Split by Shows',
                'mention_a': f"Shows: {', '.join(shows_a)}",
                'mention_b': f"Shows: {', '.join(shows_b)}",
                'description': 'Schedule divided between different radio shows'
            }

        elif split_method == 'weeks':
            return {
                'method': 'Split by Weeks',
                'mention_a': 'Runs on odd weeks (Week 1, 3, 5...)',
                'mention_b': 'Runs on even weeks (Week 2, 4, 6...)',
                'description': 'Schedule alternates between mentions every week'
            }

        return {
            'method': 'Unknown',
            'mention_a': 'Preview not available',
            'mention_b': 'Preview not available',
            'description': 'Invalid split method selected'
        }
