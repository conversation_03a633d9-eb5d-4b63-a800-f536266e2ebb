from django.urls import path
from . import views

app_name = 'mentions'

urlpatterns = [
    # Calendar
    path('calendar/', views.calendar_view, name='calendar'),
    path('calendar-interface/', views.calendar_interface, name='calendar_interface'),

    # Approval Workflow
    path('approval-workflow/', views.approval_workflow, name='approval_workflow'),
    path('<int:pk>/approve/', views.approve_mention, name='approve_mention'),
    path('<int:pk>/reject/', views.reject_mention, name='reject_mention'),

    # Mention URLs
    path('', views.mention_list, name='mention_list'),
    path('<int:pk>/', views.mention_detail, name='mention_detail'),
    path('create/', views.mention_create, name='mention_create'),
    path('<int:pk>/edit/', views.mention_edit, name='mention_edit'),
    path('<int:pk>/delete/', views.mention_delete, name='mention_delete'),
    path('<int:pk>/cancel/', views.cancel_mention, name='cancel_mention'),

    # Approval URLs
    path('approvals/', views.approval_list, name='approval_list'),

    # Reading URLs
    path('readings/<int:pk>/', views.reading_detail, name='reading_detail'),
    path('readings/<int:pk>/delete/', views.reading_delete, name='reading_delete'),
    path('readings/<int:pk>/mark-read/', views.mark_reading_complete, name='mark_reading_complete'),
    path('readings/<int:pk>/auto-resolve/', views.auto_resolve_conflict, name='auto_resolve_conflict'),
    path('schedule/', views.schedule_mention, name='schedule_mention'),

    # Enhanced Features
    path('conflicts/', views.conflict_detection, name='conflict_detection'),
    path('bulk-schedule/', views.bulk_schedule, name='bulk_schedule'),
    path('analytics/', views.mention_analytics, name='analytics'),

    # Recurring Mentions
    path('recurring/', views.recurring_mentions, name='recurring_mentions'),
    path('recurring/history/', views.recurring_history, name='recurring_history'),
    path('recurring/create/', views.recurring_mention_create, name='recurring_mention_create'),

    # New Simplified Recurring Wizard - Separate Pages
    path('recurring/wizard/', views.recurring_wizard, name='recurring_wizard'),
    path('recurring/wizard/basic-info/', views.recurring_wizard_step1, name='recurring_wizard_step1'),
    path('recurring/wizard/schedule/', views.recurring_wizard_step2, name='recurring_wizard_step2'),
    path('recurring/wizard/preview/', views.recurring_wizard_step3, name='recurring_wizard_step3'),
    path('recurring/wizard/clear/', views.recurring_wizard_clear, name='recurring_wizard_clear'),
    path('recurring/wizard/save/', views.recurring_wizard_save, name='recurring_wizard_save'),
    path('recurring/wizard/debug/', views.wizard_session_debug, name='wizard_session_debug'),

    # Legacy URL support (redirect to new structure)
    path('recurring/wizard/step/<int:step>/', views.recurring_wizard_step_legacy, name='recurring_wizard_step'),

    path('recurring/<int:pk>/edit/', views.recurring_mention_edit, name='recurring_mention_edit'),
    path('recurring/<int:pk>/delete/', views.recurring_mention_delete, name='recurring_mention_delete'),
    path('recurring/<int:pk>/toggle/', views.toggle_recurring_mention, name='toggle_recurring_mention'),
    path('recurring/<int:pk>/update-status/', views.update_recurring_status, name='update_recurring_status'),
    path('recurring/<int:pk>/generate/', views.generate_recurring_mentions, name='generate_recurring_mentions'),
    path('recurring/<int:pk>/approve-pending/', views.approve_recurring_pending, name='approve_recurring_pending'),
    path('recurring/<int:pk>/reschedule/', views.reschedule_recurring_mention, name='reschedule_recurring_mention'),
    path('recurring/<int:pk>/replace/', views.replace_recurring_mention, name='replace_recurring_mention'),
    path('recurring/<int:pk>/add-mention/', views.add_additional_mention, name='add_additional_mention'),
    path('recurring/<int:pk>/split-schedule/', views.split_recurring_schedule, name='split_recurring_schedule'),

    # Quick Single Mention Creation
    path('quick-create/', views.quick_create_mention, name='quick_create_mention'),

    # API endpoints
    path('api/check-conflicts/', views.check_time_slot_conflicts, name='check_time_slot_conflicts'),
    path('api/time-slots/', views.get_available_time_slots, name='get_available_time_slots'),

    # Quick Actions (for dashboard)
    path('<int:pk>/approve/', views.quick_approve_mention, name='quick_approve'),
    path('<int:pk>/reject/', views.quick_reject_mention, name='quick_reject'),

    # Bulk Actions for Recurring Mentions
    path('recurring/bulk-approve/', views.bulk_approve_recurring_mentions, name='bulk_approve_recurring'),
    path('recurring/pending-count/', views.get_pending_mentions_count, name='get_pending_count'),
]
