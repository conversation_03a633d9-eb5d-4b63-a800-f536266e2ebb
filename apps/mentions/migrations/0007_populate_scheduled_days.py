# Generated by Django 4.2.7 on 2025-06-17 20:53

from django.db import migrations


def populate_scheduled_days(apps, schema_editor):
    """Populate scheduled_days field for existing RecurringMentionShow records"""
    RecurringMentionShow = apps.get_model('mentions', 'RecurringMentionShow')

    for rms in RecurringMentionShow.objects.all():
        # For existing records, use the parent RecurringMention's weekdays
        if rms.recurring_mention.weekdays:
            rms.scheduled_days = rms.recurring_mention.weekdays
        else:
            # If no weekdays specified, default to all weekdays (0-6)
            rms.scheduled_days = [0, 1, 2, 3, 4, 5, 6]
        rms.save()


def reverse_populate_scheduled_days(apps, schema_editor):
    """Reverse migration - clear scheduled_days field"""
    RecurringMentionShow = apps.get_model('mentions', 'RecurringMentionShow')

    for rms in RecurringMentionShow.objects.all():
        rms.scheduled_days = []
        rms.save()


class Migration(migrations.Migration):
    dependencies = [
        ("mentions", "0006_add_scheduled_days_to_recurring_mention_show"),
    ]

    operations = [
        migrations.RunPython(populate_scheduled_days, reverse_populate_scheduled_days),
    ]
