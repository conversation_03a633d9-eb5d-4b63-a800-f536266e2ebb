# Generated by Django 4.2.7 on 2025-06-13 14:20

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("mentions", "0002_make_presenter_nullable"),
    ]

    operations = [
        migrations.AddField(
            model_name="recurringmention",
            name="campaign_name",
            field=models.CharField(
                blank=True, help_text="Campaign name for tracking", max_length=200
            ),
        ),
        migrations.AddField(
            model_name="recurringmention",
            name="daily_frequency",
            field=models.PositiveIntegerField(
                default=1, help_text="Target mentions per day"
            ),
        ),
        migrations.AddField(
            model_name="recurringmention",
            name="monthdays",
            field=models.JSONField(
                default=list,
                help_text="List of month days for monthly recurrence (1-31)",
            ),
        ),
        migrations.AddField(
            model_name="recurringmention",
            name="total_aired",
            field=models.PositiveIntegerField(
                default=0, help_text="Total mentions actually aired"
            ),
        ),
        migrations.AddField(
            model_name="recurringmention",
            name="total_required",
            field=models.PositiveIntegerField(
                blank=True, help_text="Total mentions required for campaign", null=True
            ),
        ),
        migrations.AlterField(
            model_name="recurringmention",
            name="weekdays",
            field=models.JSONField(
                default=list,
                help_text="List of weekday numbers for weekly recurrence (0=Monday, 6=Sunday)",
            ),
        ),
    ]
