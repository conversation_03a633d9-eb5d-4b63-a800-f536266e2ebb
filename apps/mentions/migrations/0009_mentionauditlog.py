# Generated by Django 4.2.7 on 2025-06-24 00:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("mentions", "0008_add_production_indexes"),
    ]

    operations = [
        migrations.CreateModel(
            name="MentionAuditLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "change_type",
                    models.CharField(
                        choices=[
                            ("reschedule", "Rescheduled"),
                            ("content_update", "Content Updated"),
                            ("status_change", "Status Changed"),
                            ("schedule_change", "Schedule Changed"),
                            ("created", "Created"),
                            ("deleted", "Deleted"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Human-readable description of the change"
                    ),
                ),
                (
                    "original_data",
                    models.J<PERSON><PERSON><PERSON>(
                        default=dict, help_text="Original mention data before change"
                    ),
                ),
                (
                    "new_data",
                    models.J<PERSON>NField(
                        default=dict, help_text="New mention data after change"
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional context about the change",
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                (
                    "changed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "mention",
                    models.ForeignKey(
                        blank=True,
                        help_text="Regular mention that was changed",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mentions.mention",
                    ),
                ),
                (
                    "recurring_mention",
                    models.ForeignKey(
                        blank=True,
                        help_text="Recurring mention that was changed",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mentions.recurringmention",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(fields=["mention"], name="audit_mention_idx"),
                    models.Index(
                        fields=["recurring_mention"], name="audit_recurring_mention_idx"
                    ),
                    models.Index(fields=["change_type"], name="audit_change_type_idx"),
                    models.Index(fields=["changed_by"], name="audit_changed_by_idx"),
                    models.Index(fields=["created_at"], name="audit_created_at_idx"),
                    models.Index(
                        fields=["mention", "change_type"],
                        name="audit_mention_change_idx",
                    ),
                    models.Index(
                        fields=["recurring_mention", "change_type"],
                        name="audit_recurring_change_idx",
                    ),
                ],
            },
        ),
    ]
