# Generated by Django 4.2.7 on 2025-06-11 07:08

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("core", "0001_initial"),
        ("shows", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="RecurringMention",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("title", models.CharField(max_length=200)),
                ("content", models.TextField()),
                (
                    "priority",
                    models.IntegerField(
                        choices=[(1, "Low"), (2, "Normal"), (3, "High"), (4, "Urgent")],
                        default=2,
                    ),
                ),
                ("duration_seconds", models.PositiveIntegerField(default=30)),
                (
                    "frequency",
                    models.CharField(
                        choices=[
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                            ("custom", "Custom"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "interval",
                    models.PositiveIntegerField(
                        default=1, help_text="Every X days/weeks/months"
                    ),
                ),
                (
                    "weekdays",
                    models.JSONField(
                        default=list,
                        help_text="List of weekday numbers for weekly recurrence",
                    ),
                ),
                ("start_date", models.DateField()),
                ("end_date", models.DateField(blank=True, null=True)),
                ("max_occurrences", models.PositiveIntegerField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.client"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="RecurringMentionShow",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("scheduled_time", models.TimeField()),
                ("is_active", models.BooleanField(default=True)),
                (
                    "presenter",
                    models.ForeignKey(
                        blank=True,
                        help_text="Presenter will be assigned when mention is read during show",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.presenter",
                    ),
                ),
                (
                    "recurring_mention",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mentions.recurringmention",
                    ),
                ),
                (
                    "show",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="shows.show"
                    ),
                ),
            ],
            options={
                "unique_together": {("recurring_mention", "show", "scheduled_time")},
            },
        ),
        migrations.AddField(
            model_name="recurringmention",
            name="shows",
            field=models.ManyToManyField(
                through="mentions.RecurringMentionShow", to="shows.show"
            ),
        ),
        migrations.CreateModel(
            name="Mention",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("title", models.CharField(max_length=200)),
                ("content", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Approval"),
                            ("scheduled", "Approved"),
                            ("read", "Completed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.IntegerField(
                        choices=[(1, "Low"), (2, "Normal"), (3, "High"), (4, "Urgent")],
                        default=2,
                    ),
                ),
                (
                    "duration_seconds",
                    models.PositiveIntegerField(
                        default=30, help_text="Expected duration in seconds"
                    ),
                ),
                ("notes", models.TextField(blank=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_mentions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.client"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "recurring_mention",
                    models.ForeignKey(
                        blank=True,
                        help_text="Source recurring mention if this was auto-generated",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="mentions.recurringmention",
                    ),
                ),
            ],
            options={
                "ordering": ["-priority", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MentionReading",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("scheduled_date", models.DateField()),
                ("scheduled_time", models.TimeField()),
                ("actual_read_time", models.DateTimeField(blank=True, null=True)),
                ("duration_seconds", models.IntegerField(blank=True, null=True)),
                ("notes", models.TextField(blank=True)),
                (
                    "read_by",
                    models.CharField(
                        blank=True,
                        help_text="Actual presenter name if different",
                        max_length=100,
                    ),
                ),
                (
                    "mention",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="mentions.mention",
                    ),
                ),
                (
                    "presenter",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="core.presenter"
                    ),
                ),
                (
                    "show",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="shows.show"
                    ),
                ),
            ],
            options={
                "ordering": ["scheduled_date", "scheduled_time"],
                "unique_together": {
                    ("mention", "show", "scheduled_date", "scheduled_time")
                },
            },
        ),
    ]
