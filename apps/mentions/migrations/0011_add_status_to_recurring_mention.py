# Generated by Django 4.2.7 on 2025-07-05 20:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("mentions", "0010_add_conflict_check_indexes"),
    ]

    operations = [
        migrations.AddField(
            model_name="recurringmention",
            name="status",
            field=models.CharField(
                choices=[
                    ("active", "Active"),
                    ("ended", "Ended"),
                    ("finished", "Finished"),
                    ("canceled", "Canceled"),
                    ("replaced", "Replaced"),
                    ("split", "Split"),
                ],
                default="active",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="recurringmention",
            name="status_changed_at",
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="recurringmention",
            name="status_changed_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="status_changed_recurring_mentions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
