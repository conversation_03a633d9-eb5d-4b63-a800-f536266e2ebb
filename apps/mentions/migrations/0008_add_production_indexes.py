# Generated by Django 4.2.7 on 2025-06-22 00:13

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("mentions", "0007_populate_scheduled_days"),
    ]

    operations = [
        migrations.AddIndex(
            model_name="mention",
            index=models.Index(fields=["status"], name="mention_status_idx"),
        ),
        migrations.AddIndex(
            model_name="mention",
            index=models.Index(fields=["client"], name="mention_client_idx"),
        ),
        migrations.AddIndex(
            model_name="mention",
            index=models.Index(fields=["priority"], name="mention_priority_idx"),
        ),
        migrations.AddIndex(
            model_name="mention",
            index=models.Index(fields=["created_at"], name="mention_created_at_idx"),
        ),
        migrations.AddIndex(
            model_name="mention",
            index=models.Index(
                fields=["status", "priority"], name="mention_status_priority_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mention",
            index=models.Index(
                fields=["client", "status"], name="mention_client_status_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mention",
            index=models.Index(
                fields=["recurring_mention"], name="mention_recurring_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mention",
            index=models.Index(fields=["created_by"], name="mention_created_by_idx"),
        ),
        migrations.AddIndex(
            model_name="mention",
            index=models.Index(fields=["approved_by"], name="mention_approved_by_idx"),
        ),
        migrations.AddIndex(
            model_name="mentionreading",
            index=models.Index(
                fields=["scheduled_date"], name="reading_scheduled_date_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mentionreading",
            index=models.Index(
                fields=["scheduled_time"], name="reading_scheduled_time_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mentionreading",
            index=models.Index(fields=["mention"], name="reading_mention_idx"),
        ),
        migrations.AddIndex(
            model_name="mentionreading",
            index=models.Index(fields=["show"], name="reading_show_idx"),
        ),
        migrations.AddIndex(
            model_name="mentionreading",
            index=models.Index(fields=["presenter"], name="reading_presenter_idx"),
        ),
        migrations.AddIndex(
            model_name="mentionreading",
            index=models.Index(
                fields=["actual_read_time"], name="reading_actual_read_time_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mentionreading",
            index=models.Index(
                fields=["scheduled_date", "scheduled_time"], name="reading_schedule_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mentionreading",
            index=models.Index(
                fields=["show", "scheduled_date"], name="reading_show_date_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mentionreading",
            index=models.Index(
                fields=["mention", "actual_read_time"],
                name="reading_mention_completed_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="recurringmention",
            index=models.Index(fields=["client"], name="recurring_client_idx"),
        ),
        migrations.AddIndex(
            model_name="recurringmention",
            index=models.Index(fields=["is_active"], name="recurring_is_active_idx"),
        ),
        migrations.AddIndex(
            model_name="recurringmention",
            index=models.Index(fields=["start_date"], name="recurring_start_date_idx"),
        ),
        migrations.AddIndex(
            model_name="recurringmention",
            index=models.Index(fields=["end_date"], name="recurring_end_date_idx"),
        ),
        migrations.AddIndex(
            model_name="recurringmention",
            index=models.Index(fields=["frequency"], name="recurring_frequency_idx"),
        ),
        migrations.AddIndex(
            model_name="recurringmention",
            index=models.Index(fields=["created_by"], name="recurring_created_by_idx"),
        ),
        migrations.AddIndex(
            model_name="recurringmention",
            index=models.Index(
                fields=["client", "is_active"], name="recurring_client_active_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="recurringmention",
            index=models.Index(
                fields=["start_date", "end_date"], name="recurring_date_range_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="recurringmentionshow",
            index=models.Index(
                fields=["recurring_mention"], name="recurring_show_mention_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="recurringmentionshow",
            index=models.Index(fields=["show"], name="recurring_show_show_idx"),
        ),
        migrations.AddIndex(
            model_name="recurringmentionshow",
            index=models.Index(
                fields=["presenter"], name="recurring_show_presenter_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="recurringmentionshow",
            index=models.Index(
                fields=["scheduled_time"], name="recurring_show_time_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="recurringmentionshow",
            index=models.Index(fields=["is_active"], name="recurring_show_active_idx"),
        ),
        migrations.AddIndex(
            model_name="recurringmentionshow",
            index=models.Index(
                fields=["show", "is_active"], name="recurring_show_show_active_idx"
            ),
        ),
    ]
