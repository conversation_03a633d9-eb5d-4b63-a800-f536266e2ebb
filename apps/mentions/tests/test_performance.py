"""
Performance tests for mentions conflict checking optimization
"""
import time
from django.test import TestCase, TransactionTestCase
from django.contrib.auth.models import User
from django.core.cache import cache
from datetime import datetime, date, time as dt_time, timedelta
from apps.organizations.models import Organization
from apps.core.models import Client
from apps.shows.models import Show
from apps.mentions.models import Mention, MentionReading, RecurringMention, RecurringMentionShow
from apps.mentions.views import _get_time_slot_conflicts, _check_recurring_conflicts


class ConflictCheckingPerformanceTest(TransactionTestCase):
    """Test performance improvements in conflict checking"""
    
    def setUp(self):
        """Set up test data"""
        # Clear cache before each test
        cache.clear()
        
        # Create test organization
        self.organization = Organization.objects.create(
            name="Test Radio Station",
            slug="test-radio"
        )
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test client
        self.client_obj = Client.objects.create(
            name="Test Client",
            organization=self.organization
        )
        
        # Create test show
        self.show = Show.objects.create(
            name="Morning Show",
            organization=self.organization,
            start_time=dt_time(6, 0),
            end_time=dt_time(10, 0),
            is_active=True
        )
        
        # Create test mentions and readings for conflict checking
        self.create_test_data()
    
    def create_test_data(self):
        """Create test mentions and readings for performance testing"""
        base_date = date.today()
        
        # Create multiple mentions with readings spread across 30 days
        for i in range(50):  # Create 50 mentions
            mention = Mention.objects.create(
                title=f"Test Mention {i}",
                content=f"Test content {i}",
                client=self.client_obj,
                status='scheduled',
                priority=2,
                duration_seconds=30,
                created_by=self.user
            )
            
            # Create readings for different dates and times
            for j in range(3):  # 3 readings per mention
                reading_date = base_date + timedelta(days=(i + j) % 30)
                reading_time = dt_time(8, (i * 5 + j * 10) % 60, 0)  # Spread times
                
                MentionReading.objects.create(
                    mention=mention,
                    show=self.show,
                    scheduled_date=reading_date,
                    scheduled_time=reading_time
                )
        
        # Create recurring mentions for recurring conflict testing
        for i in range(10):
            recurring = RecurringMention.objects.create(
                title=f"Recurring Mention {i}",
                content=f"Recurring content {i}",
                client=self.client_obj,
                frequency='weekly',
                weekdays=[1, 3, 5],  # Monday, Wednesday, Friday
                start_date=base_date,
                end_date=base_date + timedelta(days=90),
                is_active=True,
                created_by=self.user
            )
            
            RecurringMentionShow.objects.create(
                recurring_mention=recurring,
                show=self.show,
                scheduled_time=dt_time(8, i * 5, 0),  # Different times
                scheduled_days=[1, 3, 5]
            )
    
    def test_time_slot_conflicts_performance(self):
        """Test performance of time slot conflict checking"""
        test_time = dt_time(8, 30, 0)
        weekday = 1  # Monday
        
        # Measure time without cache (first call)
        start_time = time.time()
        conflicts1 = _get_time_slot_conflicts(
            self.show, test_time, weekday, self.organization
        )
        first_call_time = time.time() - start_time
        
        # Measure time with cache (second call)
        start_time = time.time()
        conflicts2 = _get_time_slot_conflicts(
            self.show, test_time, weekday, self.organization
        )
        cached_call_time = time.time() - start_time
        
        # Verify results are the same
        self.assertEqual(conflicts1, conflicts2)
        
        # Cached call should be significantly faster
        self.assertLess(cached_call_time, first_call_time * 0.1)  # At least 10x faster
        
        print(f"First call time: {first_call_time:.4f}s")
        print(f"Cached call time: {cached_call_time:.4f}s")
        print(f"Performance improvement: {first_call_time / cached_call_time:.1f}x")
    
    def test_recurring_conflicts_performance(self):
        """Test performance of recurring conflict checking"""
        test_time = dt_time(8, 15, 0)
        weekday = 1  # Monday
        
        # Measure time without cache (first call)
        start_time = time.time()
        conflicts1 = _check_recurring_conflicts(
            self.show, test_time, weekday, self.organization
        )
        first_call_time = time.time() - start_time
        
        # Measure time with cache (second call)
        start_time = time.time()
        conflicts2 = _check_recurring_conflicts(
            self.show, test_time, weekday, self.organization
        )
        cached_call_time = time.time() - start_time
        
        # Verify results are the same
        self.assertEqual(conflicts1, conflicts2)
        
        # Cached call should be significantly faster
        self.assertLess(cached_call_time, first_call_time * 0.1)  # At least 10x faster
        
        print(f"Recurring conflicts - First call time: {first_call_time:.4f}s")
        print(f"Recurring conflicts - Cached call time: {cached_call_time:.4f}s")
        print(f"Recurring conflicts - Performance improvement: {first_call_time / cached_call_time:.1f}x")
    
    def test_database_query_optimization(self):
        """Test that the optimized query reduces database calls"""
        from django.test.utils import override_settings
        from django.db import connection
        
        test_time = dt_time(8, 45, 0)
        weekday = 2  # Tuesday
        
        # Clear cache to ensure fresh queries
        cache.clear()
        
        # Reset query count
        connection.queries_log.clear()
        
        with self.assertNumQueries(3):  # Should be 3 queries or less (organization settings, recurring conflicts, time slot conflicts)
            conflicts = _get_time_slot_conflicts(
                self.show, test_time, weekday, self.organization
            )
        
        # Verify we get some results (conflicts or no conflicts)
        self.assertIsInstance(conflicts, dict)
        self.assertIn('conflicts', conflicts)
        self.assertIn('warnings', conflicts)
    
    def test_conflict_checking_accuracy(self):
        """Test that optimized conflict checking still works correctly"""
        # Create a specific conflict scenario
        conflict_date = date.today() + timedelta(days=7)
        conflict_time = dt_time(9, 0, 0)
        
        # Create a mention reading that should conflict
        mention = Mention.objects.create(
            title="Conflict Test Mention",
            content="This should create a conflict",
            client=self.client_obj,
            status='scheduled',
            priority=2,
            duration_seconds=30,
            created_by=self.user
        )
        
        MentionReading.objects.create(
            mention=mention,
            show=self.show,
            scheduled_date=conflict_date,
            scheduled_time=conflict_time
        )
        
        # Clear cache to ensure fresh query
        cache.clear()
        
        # Check for conflicts on the same weekday
        weekday = conflict_date.weekday()
        conflicts = _get_time_slot_conflicts(
            self.show, conflict_time, weekday, self.organization
        )
        
        # Should detect the conflict
        self.assertGreater(len(conflicts['conflicts']), 0)
        
        # Check that the conflict message contains our test mention
        conflict_messages = [c['message'] for c in conflicts['conflicts']]
        self.assertTrue(any('Conflict Test Mention' in msg for msg in conflict_messages))
