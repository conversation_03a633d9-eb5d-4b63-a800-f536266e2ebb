from django.urls import path
from . import views

app_name = 'news_reader'

urlpatterns = [
    # Dashboard
    path('dashboard/', views.news_reader_dashboard, name='dashboard'),
    
    # Reading Notes Management
    path('notes/', views.note_list, name='note_list'),
    path('notes/create/', views.note_create, name='note_create'),
    path('notes/<int:pk>/', views.note_detail, name='note_detail'),
    path('notes/<int:pk>/edit/', views.note_edit, name='note_edit'),
    path('notes/<int:pk>/delete/', views.note_delete, name='note_delete'),
    
    # Quick note actions
    path('notes/<int:pk>/mark-prepared/', views.mark_note_prepared, name='mark_note_prepared'),
    path('notes/<int:pk>/start-preparation/', views.start_note_preparation, name='start_note_preparation'),
    path('notes/<int:pk>/complete-reading/', views.complete_note_reading, name='complete_note_reading'),
    path('notes/<int:pk>/toggle-favorite/', views.toggle_note_favorite, name='toggle_note_favorite'),
    
    # Mention assignment for notes
    path('mentions/<int:mention_id>/create-note/', views.create_note_for_mention, name='create_note_for_mention'),
    path('readings/<int:reading_id>/create-note/', views.create_note_for_reading, name='create_note_for_reading'),
    
    # Reading Sessions
    path('sessions/', views.session_list, name='session_list'),
    path('sessions/<int:pk>/', views.session_detail, name='session_detail'),
    path('sessions/start/', views.start_session, name='start_session'),
    path('sessions/<int:pk>/end/', views.end_session, name='end_session'),
    
    # Assignments
    path('assignments/', views.assignment_list, name='assignment_list'),
    path('assignments/<int:pk>/', views.assignment_detail, name='assignment_detail'),
    path('assignments/<int:pk>/complete/', views.complete_assignment, name='complete_assignment'),
    
    # Live Reading Support
    path('live/', views.live_reading_interface, name='live_reading_interface'),
    path('live/current-mentions/', views.get_current_mentions, name='get_current_mentions'),
    path('live/upcoming-mentions/', views.get_upcoming_mentions, name='get_upcoming_mentions'),
    path('live/mark-reading-complete/<int:reading_id>/', views.mark_live_reading_complete, name='mark_live_reading_complete'),
    
    # API endpoints
    path('api/notes/search/', views.search_notes, name='search_notes'),
    path('api/mentions/assigned/', views.get_assigned_mentions, name='get_assigned_mentions'),
    path('api/dashboard/stats/', views.get_dashboard_stats, name='get_dashboard_stats'),
    
    # Article Management
    path('articles/', views.article_list, name='article_list'),
    path('articles/create/', views.article_create, name='article_create'),
    path('articles/<int:pk>/', views.article_detail, name='article_detail'),
    path('articles/<int:pk>/edit/', views.article_edit, name='article_edit'),
    path('articles/<int:pk>/delete/', views.article_delete, name='article_delete'),
    path('articles/<int:pk>/editor/', views.article_editor, name='article_editor'),

    # Article Actions
    path('articles/<int:pk>/auto-save/', views.article_auto_save, name='article_auto_save'),
    path('articles/<int:pk>/assign/', views.article_assign, name='article_assign'),

    # Article Export Options
    path('articles/<int:pk>/export/pdf/', views.article_export_pdf, name='article_export_pdf'),
    path('articles/<int:pk>/teleprompter/', views.article_teleprompter, name='article_teleprompter'),
    path('articles/<int:pk>/mobile/', views.article_mobile_view, name='article_mobile_view'),

    # Live Reading Interface
    path('live-reading/', views.live_reading_interface, name='live_reading_interface'),
    path('live-reading/article/<int:pk>/', views.live_reading_article, name='live_reading_article'),
    path('live-reading/article/<int:pk>/mark-read/', views.mark_article_read, name='mark_article_read'),

    # Reports and exports
    path('reports/preparation-schedule/', views.preparation_schedule_report, name='preparation_schedule_report'),
    path('reports/performance/', views.performance_report, name='performance_report'),
    path('export/notes/', views.export_notes, name='export_notes'),
]
