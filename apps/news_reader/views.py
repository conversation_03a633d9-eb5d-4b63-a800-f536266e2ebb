from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from datetime import datetime, timedelta
import json

from apps.core.decorators import require_permission, require_role
from apps.organizations.middleware import get_current_organization, get_current_membership
from .models import ReadingNote, ReadingSession, NewsReaderAssignment, Article, ArticleRevision, ArticleAssignment
from .forms import ReadingNoteForm, ReadingNoteCreateForm, ReadingSessionForm, QuickNoteForm
from apps.mentions.models import Mention, MentionReading
from apps.shows.models import Show


@login_required
@require_role('news_reader')
def news_reader_dashboard(request):
    """Main dashboard for news readers"""
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization to access the news reader dashboard.')
        return redirect('organizations:list')

    today = timezone.now().date()

    # Get user's reading notes and assignments
    user_notes = ReadingNote.objects.filter(user=request.user).select_related('mention', 'mention_reading')
    user_assignments = NewsReaderAssignment.objects.filter(
        user=request.user,
        is_active=True
    ).select_related('mention_reading__mention', 'mention_reading__show')

    # Get user's articles and article assignments
    user_articles = Article.objects.filter(author=request.user)
    user_article_assignments = ArticleAssignment.objects.filter(
        user=request.user,
        is_active=True
    ).select_related('article')

    # Dashboard statistics
    stats = {
        'total_notes': user_notes.count(),
        'prepared_today': user_notes.filter(
            preparation_status='prepared',
            preparation_completed_at__date=today
        ).count(),
        'in_progress': user_notes.filter(preparation_status='in_progress').count(),
        'overdue': user_notes.filter(
            preparation_status__in=['not_prepared', 'in_progress']
        ).count(),  # This will be refined with proper overdue logic
        'active_assignments': user_assignments.count(),
        'completed_today': user_notes.filter(
            preparation_status='completed',
            updated_at__date=today
        ).count(),
        # Article statistics
        'total_articles': user_articles.count(),
        'articles_draft': user_articles.filter(status='draft').count(),
        'articles_published': user_articles.filter(status='published').count(),
        'article_assignments': user_article_assignments.count(),
        'articles_due_today': user_article_assignments.filter(
            due_date__date=today,
            is_completed=False
        ).count(),
    }

    # Today's schedule - upcoming mentions assigned to this user
    todays_schedule = user_assignments.filter(
        mention_reading__scheduled_date=today
    ).order_by('mention_reading__scheduled_time')[:10]

    # Recent activity
    recent_notes = user_notes.order_by('-updated_at')[:5]

    # Upcoming deadlines
    upcoming_deadlines = user_assignments.filter(
        due_date__gte=timezone.now(),
        is_completed=False
    ).order_by('due_date')[:5]

    # Recent articles
    recent_articles = user_articles.order_by('-created_at')[:5]

    # Upcoming article deadlines
    upcoming_article_deadlines = user_article_assignments.filter(
        due_date__gte=timezone.now(),
        is_completed=False
    ).order_by('due_date')[:5]

    context = {
        'stats': stats,
        'todays_schedule': todays_schedule,
        'recent_notes': recent_notes,
        'upcoming_deadlines': upcoming_deadlines,
        'recent_articles': recent_articles,
        'upcoming_article_deadlines': upcoming_article_deadlines,
        'current_organization': current_org,
    }

    return render(request, 'news_reader/dashboard.html', context)


@login_required
@require_role('news_reader')
def note_list(request):
    """List all reading notes for the current user"""
    notes = ReadingNote.objects.filter(user=request.user).select_related('mention', 'mention_reading')

    # Filtering
    status_filter = request.GET.get('status')
    if status_filter:
        notes = notes.filter(preparation_status=status_filter)

    priority_filter = request.GET.get('priority')
    if priority_filter:
        notes = notes.filter(priority=priority_filter)

    search_query = request.GET.get('search')
    if search_query:
        notes = notes.filter(
            Q(title__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(mention__title__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(notes, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_choices': ReadingNote.STATUS_CHOICES,
        'priority_choices': ReadingNote.PRIORITY_CHOICES,
        'current_filters': {
            'status': status_filter,
            'priority': priority_filter,
            'search': search_query,
        }
    }

    return render(request, 'news_reader/note_list.html', context)


@login_required
@require_role('news_reader')
def note_detail(request, pk):
    """Detail view for a reading note"""
    note = get_object_or_404(ReadingNote, pk=pk, user=request.user)

    context = {
        'note': note,
    }

    return render(request, 'news_reader/note_detail.html', context)


@login_required
@require_role('news_reader')
def note_create(request):
    """Create a new reading note"""
    if request.method == 'POST':
        form = ReadingNoteCreateForm(request.POST, user=request.user)
        if form.is_valid():
            note = form.save(commit=False)
            note.user = request.user
            note.save()
            messages.success(request, f'Note created successfully for "{note.mention.title}"!')
            return redirect('news_reader:note_detail', pk=note.pk)
    else:
        form = ReadingNoteCreateForm(user=request.user)

    context = {
        'form': form,
    }

    return render(request, 'news_reader/note_create.html', context)


@login_required
@require_role('news_reader')
def note_edit(request, pk):
    """Edit a reading note"""
    note = get_object_or_404(ReadingNote, pk=pk, user=request.user)

    if request.method == 'POST':
        form = ReadingNoteForm(request.POST, instance=note, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, 'Note updated successfully!')
            return redirect('news_reader:note_detail', pk=note.pk)
    else:
        form = ReadingNoteForm(instance=note, user=request.user)

    context = {
        'note': note,
        'form': form,
    }

    return render(request, 'news_reader/note_edit.html', context)


@login_required
@require_role('news_reader')
def note_delete(request, pk):
    """Delete a reading note"""
    note = get_object_or_404(ReadingNote, pk=pk, user=request.user)

    if request.method == 'POST':
        note.delete()
        messages.success(request, 'Note deleted successfully!')
        return redirect('news_reader:note_list')

    context = {
        'note': note,
    }

    return render(request, 'news_reader/note_delete.html', context)


# Quick Actions for Notes
@login_required
@require_role('news_reader')
def mark_note_prepared(request, pk):
    """Mark a note as prepared"""
    note = get_object_or_404(ReadingNote, pk=pk, user=request.user)
    note.mark_as_prepared()
    messages.success(request, f'Note for "{note.mention.title}" marked as prepared!')
    return redirect('news_reader:dashboard')


@login_required
@require_role('news_reader')
def start_note_preparation(request, pk):
    """Start preparation for a note"""
    note = get_object_or_404(ReadingNote, pk=pk, user=request.user)
    note.start_preparation()
    messages.success(request, f'Started preparation for "{note.mention.title}"!')
    return redirect('news_reader:note_detail', pk=note.pk)


@login_required
@require_role('news_reader')
def complete_note_reading(request, pk):
    """Mark a note reading as completed"""
    note = get_object_or_404(ReadingNote, pk=pk, user=request.user)
    note.complete_reading()
    messages.success(request, f'Reading completed for "{note.mention.title}"!')
    return redirect('news_reader:dashboard')


@login_required
@require_role('news_reader')
def toggle_note_favorite(request, pk):
    """Toggle favorite status of a note"""
    note = get_object_or_404(ReadingNote, pk=pk, user=request.user)
    note.is_favorite = not note.is_favorite
    note.save()
    status = 'added to' if note.is_favorite else 'removed from'
    messages.success(request, f'Note {status} favorites!')
    return redirect('news_reader:note_detail', pk=note.pk)


# Note creation for specific mentions/readings
@login_required
@require_role('news_reader')
def create_note_for_mention(request, mention_id):
    """Create a note for a specific mention"""
    mention = get_object_or_404(Mention, pk=mention_id)

    # Check if note already exists
    existing_note = ReadingNote.objects.filter(user=request.user, mention=mention).first()
    if existing_note:
        messages.info(request, 'Note already exists for this mention.')
        return redirect('news_reader:note_detail', pk=existing_note.pk)

    if request.method == 'POST':
        form = QuickNoteForm(request.POST)
        if form.is_valid():
            note = ReadingNote.objects.create(
                user=request.user,
                mention=mention,
                title=f"Note for {mention.title}",
                content=form.cleaned_data['content'],
                priority=form.cleaned_data['priority'],
                preparation_status='not_prepared'
            )
            messages.success(request, 'Note created successfully!')
            return redirect('news_reader:note_detail', pk=note.pk)
    else:
        form = QuickNoteForm()

    context = {
        'mention': mention,
        'form': form,
    }

    return render(request, 'news_reader/create_note_for_mention.html', context)


@login_required
@require_role('news_reader')
def create_note_for_reading(request, reading_id):
    """Create a note for a specific mention reading"""
    reading = get_object_or_404(MentionReading, pk=reading_id)

    # Check if note already exists
    existing_note = ReadingNote.objects.filter(
        user=request.user,
        mention=reading.mention,
        mention_reading=reading
    ).first()
    if existing_note:
        messages.info(request, 'Note already exists for this reading.')
        return redirect('news_reader:note_detail', pk=existing_note.pk)

    if request.method == 'POST':
        form = QuickNoteForm(request.POST)
        if form.is_valid():
            note = ReadingNote.objects.create(
                user=request.user,
                mention=reading.mention,
                mention_reading=reading,
                title=f"Note for {reading.mention.title} - {reading.scheduled_date}",
                content=form.cleaned_data['content'],
                priority=form.cleaned_data['priority'],
                preparation_status='not_prepared'
            )
            messages.success(request, 'Note created successfully!')
            return redirect('news_reader:note_detail', pk=note.pk)
    else:
        form = QuickNoteForm()

    context = {
        'reading': reading,
        'form': form,
    }

    return render(request, 'news_reader/create_note_for_reading.html', context)


# Session Management
@login_required
@require_role('news_reader')
def session_list(request):
    """List reading sessions for the current user"""
    sessions = ReadingSession.objects.filter(user=request.user).order_by('-start_time')

    # Pagination
    paginator = Paginator(sessions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
    }

    return render(request, 'news_reader/session_list.html', context)


@login_required
@require_role('news_reader')
def session_detail(request, pk):
    """Detail view for a reading session"""
    session = get_object_or_404(ReadingSession, pk=pk, user=request.user)

    context = {
        'session': session,
    }

    return render(request, 'news_reader/session_detail.html', context)


@login_required
@require_role('news_reader')
def start_session(request):
    """Start a new reading session"""
    if request.method == 'POST':
        session_type = request.POST.get('session_type', 'preparation')
        session = ReadingSession.objects.create(
            user=request.user,
            session_type=session_type
        )
        messages.success(request, f'{session.get_session_type_display()} session started!')
        return redirect('news_reader:session_detail', pk=session.pk)

    return render(request, 'news_reader/start_session.html')


@login_required
@require_role('news_reader')
def end_session(request, pk):
    """End a reading session"""
    session = get_object_or_404(ReadingSession, pk=pk, user=request.user)

    if request.method == 'POST':
        session.end_session()
        messages.success(request, 'Session ended successfully!')
        return redirect('news_reader:session_detail', pk=session.pk)

    context = {
        'session': session,
    }

    return render(request, 'news_reader/end_session.html', context)


# Assignment Management
@login_required
@require_role('news_reader')
def assignment_list(request):
    """List assignments for the current user"""
    assignments = NewsReaderAssignment.objects.filter(
        user=request.user,
        is_active=True
    ).select_related('mention_reading__mention', 'mention_reading__show').order_by('due_date')

    # Filtering
    status_filter = request.GET.get('status')
    if status_filter == 'completed':
        assignments = assignments.filter(is_completed=True)
    elif status_filter == 'pending':
        assignments = assignments.filter(is_completed=False)
    elif status_filter == 'overdue':
        assignments = assignments.filter(is_completed=False, due_date__lt=timezone.now())

    # Pagination
    paginator = Paginator(assignments, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'current_filters': {
            'status': status_filter,
        }
    }

    return render(request, 'news_reader/assignment_list.html', context)


@login_required
@require_role('news_reader')
def assignment_detail(request, pk):
    """Detail view for an assignment"""
    assignment = get_object_or_404(NewsReaderAssignment, pk=pk, user=request.user)

    # Get or create reading note for this assignment
    reading_note, created = ReadingNote.objects.get_or_create(
        user=request.user,
        mention=assignment.mention_reading.mention,
        mention_reading=assignment.mention_reading,
        defaults={
            'title': f"Note for {assignment.mention_reading.mention.title}",
            'preparation_status': 'not_prepared'
        }
    )

    context = {
        'assignment': assignment,
        'reading_note': reading_note,
        'note_created': created,
    }

    return render(request, 'news_reader/assignment_detail.html', context)


@login_required
@require_role('news_reader')
def complete_assignment(request, pk):
    """Mark an assignment as completed"""
    assignment = get_object_or_404(NewsReaderAssignment, pk=pk, user=request.user)

    if request.method == 'POST':
        assignment.mark_completed()
        messages.success(request, 'Assignment marked as completed!')
        return redirect('news_reader:assignment_list')

    context = {
        'assignment': assignment,
    }

    return render(request, 'news_reader/complete_assignment.html', context)


# Live Reading Support
@login_required
@require_role('news_reader')
def live_reading_interface(request):
    """Live reading interface for news readers"""
    current_time = timezone.now()
    today = current_time.date()

    # Get current and upcoming mentions for today
    current_mentions = MentionReading.objects.filter(
        scheduled_date=today,
        mention__status='scheduled',
        scheduled_time__lte=current_time.time(),
        actual_read_time__isnull=True
    ).select_related('mention', 'show').order_by('scheduled_time')[:5]

    upcoming_mentions = MentionReading.objects.filter(
        scheduled_date=today,
        mention__status='scheduled',
        scheduled_time__gt=current_time.time()
    ).select_related('mention', 'show').order_by('scheduled_time')[:10]

    # Get user's notes for these mentions
    mention_ids = list(current_mentions.values_list('mention_id', flat=True)) + \
                 list(upcoming_mentions.values_list('mention_id', flat=True))

    user_notes = ReadingNote.objects.filter(
        user=request.user,
        mention_id__in=mention_ids
    ).select_related('mention')

    # Create a dictionary for quick note lookup
    notes_dict = {note.mention_id: note for note in user_notes}

    # Add notes to mentions for easier template access
    for mention in current_mentions:
        mention.user_note = notes_dict.get(mention.mention_id)

    for mention in upcoming_mentions:
        mention.user_note = notes_dict.get(mention.mention_id)

    context = {
        'current_mentions': current_mentions,
        'upcoming_mentions': upcoming_mentions,
        'notes_dict': notes_dict,
        'current_time': current_time,
    }

    return render(request, 'news_reader/live_reading_interface.html', context)


@login_required
@require_role('news_reader')
def get_current_mentions(request):
    """API endpoint to get current mentions"""
    from django.http import JsonResponse

    current_time = timezone.now()
    today = current_time.date()

    mentions = MentionReading.objects.filter(
        scheduled_date=today,
        mention__status='scheduled',
        scheduled_time__lte=current_time.time(),
        actual_read_time__isnull=True
    ).select_related('mention', 'show').order_by('scheduled_time')[:5]

    data = []
    for mention in mentions:
        data.append({
            'id': mention.id,
            'title': mention.mention.title,
            'content': mention.mention.content,
            'show': mention.show.name,
            'scheduled_time': mention.scheduled_time.strftime('%H:%M'),
            'duration': mention.mention.duration_seconds,
        })

    return JsonResponse({'mentions': data})


@login_required
@require_role('news_reader')
def get_upcoming_mentions(request):
    """API endpoint to get upcoming mentions"""
    from django.http import JsonResponse

    current_time = timezone.now()
    today = current_time.date()

    mentions = MentionReading.objects.filter(
        scheduled_date=today,
        mention__status='scheduled',
        scheduled_time__gt=current_time.time()
    ).select_related('mention', 'show').order_by('scheduled_time')[:10]

    data = []
    for mention in mentions:
        data.append({
            'id': mention.id,
            'title': mention.mention.title,
            'content': mention.mention.content,
            'show': mention.show.name,
            'scheduled_time': mention.scheduled_time.strftime('%H:%M'),
            'duration': mention.mention.duration_seconds,
        })

    return JsonResponse({'mentions': data})


@login_required
@require_role('news_reader')
def mark_live_reading_complete(request, reading_id):
    """Mark a reading as completed during live session"""
    reading = get_object_or_404(MentionReading, pk=reading_id)

    if request.method == 'POST':
        reading.actual_read_time = timezone.now()
        reading.read_by = request.user.get_full_name() or request.user.username
        reading.save()

        # Update associated reading note if exists
        try:
            note = ReadingNote.objects.get(
                user=request.user,
                mention=reading.mention,
                mention_reading=reading
            )
            note.complete_reading()
        except ReadingNote.DoesNotExist:
            pass

        messages.success(request, f'Reading completed for "{reading.mention.title}"!')
        return redirect('news_reader:live_reading_interface')

    context = {
        'reading': reading,
    }

    return render(request, 'news_reader/mark_live_reading_complete.html', context)


# API Endpoints
@login_required
@require_role('news_reader')
def search_notes(request):
    """API endpoint to search notes"""
    query = request.GET.get('q', '')
    if not query:
        return JsonResponse({'notes': []})

    notes = ReadingNote.objects.filter(
        user=request.user
    ).filter(
        Q(title__icontains=query) |
        Q(content__icontains=query) |
        Q(mention__title__icontains=query)
    ).select_related('mention')[:10]

    data = []
    for note in notes:
        data.append({
            'id': note.id,
            'title': note.title or f"Note for {note.mention.title}",
            'mention_title': note.mention.title,
            'status': note.get_preparation_status_display(),
            'url': note.get_absolute_url(),
        })

    return JsonResponse({'notes': data})


@login_required
@require_role('news_reader')
def get_assigned_mentions(request):
    """API endpoint to get assigned mentions"""
    assignments = NewsReaderAssignment.objects.filter(
        user=request.user,
        is_active=True,
        is_completed=False
    ).select_related('mention_reading__mention', 'mention_reading__show')[:20]

    data = []
    for assignment in assignments:
        data.append({
            'id': assignment.id,
            'mention_title': assignment.mention_reading.mention.title,
            'show_name': assignment.mention_reading.show.name,
            'scheduled_date': assignment.mention_reading.scheduled_date.isoformat(),
            'scheduled_time': assignment.mention_reading.scheduled_time.strftime('%H:%M'),
            'due_date': assignment.due_date.isoformat(),
            'is_overdue': assignment.is_overdue,
        })

    return JsonResponse({'assignments': data})


@login_required
@require_role('news_reader')
def get_dashboard_stats(request):
    """API endpoint to get dashboard statistics"""
    today = timezone.now().date()
    user_notes = ReadingNote.objects.filter(user=request.user)
    user_assignments = NewsReaderAssignment.objects.filter(user=request.user, is_active=True)

    stats = {
        'total_notes': user_notes.count(),
        'prepared_today': user_notes.filter(
            preparation_status='prepared',
            preparation_completed_at__date=today
        ).count(),
        'in_progress': user_notes.filter(preparation_status='in_progress').count(),
        'active_assignments': user_assignments.count(),
        'completed_today': user_notes.filter(
            preparation_status='completed',
            updated_at__date=today
        ).count(),
        'overdue_assignments': user_assignments.filter(
            is_completed=False,
            due_date__lt=timezone.now()
        ).count(),
    }

    return JsonResponse({'stats': stats})


# Reports and Exports
@login_required
@require_role('news_reader')
def preparation_schedule_report(request):
    """Generate preparation schedule report"""
    from apps.reports.pdf_generator import create_direct_pdf, create_pdf_response

    # Get report format preference
    format_type = request.GET.get('format', 'html')

    # Get user assignments
    user_assignments = NewsReaderAssignment.objects.filter(
        user=request.user,
        is_active=True
    ).select_related('mention_reading__mention', 'mention_reading__show').order_by('due_date')

    if format_type == 'pdf':
        # Prepare data for PDF generation
        pdf_data = {
            'assignments': [],
            'user': request.user,
            'report_date': timezone.now().date(),
            'total_assignments': user_assignments.count(),
            'completed_assignments': user_assignments.filter(is_completed=True).count(),
            'overdue_assignments': user_assignments.filter(
                is_completed=False,
                due_date__lt=timezone.now()
            ).count(),
        }

        # Convert assignments to PDF-friendly format
        for assignment in user_assignments:
            pdf_data['assignments'].append({
                'mention_title': assignment.mention_reading.mention.title,
                'client_name': assignment.mention_reading.mention.client.name,
                'show_name': assignment.mention_reading.show.name,
                'scheduled_date': assignment.mention_reading.scheduled_date,
                'scheduled_time': assignment.mention_reading.scheduled_time,
                'due_date': assignment.due_date,
                'status': 'Completed' if assignment.is_completed else ('Overdue' if assignment.is_overdue else 'Pending'),
                'priority': assignment.mention_reading.mention.get_priority_display(),
                'duration': assignment.mention_reading.mention.duration_seconds,
                'assignment_notes': assignment.assignment_notes,
            })

        # PDF options
        pdf_options = {
            'orientation': 'portrait',
            'font_size': 10,
            'include_logo': True,
            'include_summary': True,
        }

        # Generate PDF
        pdf = create_direct_pdf('news-reader-schedule', pdf_data,
                              f"Reading Schedule - {request.user.get_full_name() or request.user.username}",
                              pdf_options)

        # Return PDF response
        filename = f"reading_schedule_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        return create_pdf_response(pdf, filename)

    # HTML view
    context = {
        'assignments': user_assignments,
        'report_date': timezone.now().date(),
    }

    return render(request, 'news_reader/preparation_schedule_report.html', context)


@login_required
@require_role('news_reader')
def performance_report(request):
    """Generate performance report"""
    from apps.reports.pdf_generator import create_direct_pdf, create_pdf_response

    # Get report format preference
    format_type = request.GET.get('format', 'html')

    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if not start_date:
        start_date = (timezone.now() - timedelta(days=30)).date()
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get performance data
    sessions = ReadingSession.objects.filter(
        user=request.user,
        start_time__date__range=[start_date, end_date]
    )

    notes = ReadingNote.objects.filter(
        user=request.user,
        created_at__date__range=[start_date, end_date]
    )

    performance_data = {
        'total_sessions': sessions.count(),
        'total_prep_time': sum([s.duration.total_seconds() / 3600 for s in sessions if s.end_time]),  # in hours
        'total_notes': notes.count(),
        'completed_notes': notes.filter(preparation_status='completed').count(),
        'average_prep_time': notes.exclude(actual_prep_time__isnull=True).aggregate(
            avg_time=Avg('actual_prep_time')
        )['avg_time'] or 0,
    }

    if format_type == 'pdf':
        # Prepare data for PDF generation
        pdf_data = {
            'user': request.user,
            'start_date': start_date,
            'end_date': end_date,
            'performance_data': performance_data,
            'sessions': sessions.order_by('-start_time')[:10],
            'recent_notes': notes.order_by('-created_at')[:10],
        }

        # PDF options
        pdf_options = {
            'orientation': 'portrait',
            'font_size': 10,
            'include_logo': True,
            'include_summary': True,
        }

        # Generate PDF
        pdf = create_direct_pdf('news-reader-performance', pdf_data,
                              f"Performance Report - {request.user.get_full_name() or request.user.username}",
                              pdf_options)

        # Return PDF response
        filename = f"performance_report_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        return create_pdf_response(pdf, filename)

    # HTML view
    context = {
        'performance_data': performance_data,
        'start_date': start_date,
        'end_date': end_date,
        'sessions': sessions.order_by('-start_time')[:10],
        'recent_notes': notes.order_by('-created_at')[:10],
    }

    return render(request, 'news_reader/performance_report.html', context)


@login_required
@require_role('news_reader')
def export_notes(request):
    """Export notes to CSV"""
    import csv
    from django.http import HttpResponse

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="reading_notes.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Title', 'Mention Title', 'Status', 'Priority', 'Created Date',
        'Preparation Started', 'Preparation Completed', 'Content'
    ])

    notes = ReadingNote.objects.filter(user=request.user).select_related('mention')
    for note in notes:
        writer.writerow([
            note.title or f"Note for {note.mention.title}",
            note.mention.title,
            note.get_preparation_status_display(),
            note.get_priority_display(),
            note.created_at.strftime('%Y-%m-%d %H:%M'),
            note.preparation_started_at.strftime('%Y-%m-%d %H:%M') if note.preparation_started_at else '',
            note.preparation_completed_at.strftime('%Y-%m-%d %H:%M') if note.preparation_completed_at else '',
            note.content[:100] + '...' if len(note.content) > 100 else note.content,
        ])

    return response


# Article Management Views

@login_required
@require_role('news_reader')
def article_list(request):
    """List all articles with filtering and search"""
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization to access articles.')
        return redirect('organizations:list')

    # Base queryset - show all articles for now, can be filtered by organization later
    articles = Article.objects.select_related('author', 'last_edited_by')

    # Filtering
    status_filter = request.GET.get('status')
    if status_filter:
        articles = articles.filter(status=status_filter)

    category_filter = request.GET.get('category')
    if category_filter:
        articles = articles.filter(category=category_filter)

    priority_filter = request.GET.get('priority')
    if priority_filter:
        articles = articles.filter(priority=priority_filter)

    author_filter = request.GET.get('author')
    if author_filter:
        articles = articles.filter(author_id=author_filter)

    # Search
    search_query = request.GET.get('search')
    if search_query:
        articles = articles.filter(
            Q(title__icontains=search_query) |
            Q(content__icontains=search_query) |
            Q(byline__icontains=search_query) |
            Q(slug__icontains=search_query)
        )

    # Sorting
    sort_by = request.GET.get('sort', '-created_at')
    if sort_by in ['title', '-title', 'created_at', '-created_at', 'scheduled_date', '-scheduled_date', 'priority', '-priority']:
        articles = articles.order_by(sort_by)

    # Pagination
    paginator = Paginator(articles, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter choices for the template
    from django.contrib.auth.models import User
    authors = User.objects.filter(authored_articles__isnull=False).distinct().order_by('first_name', 'last_name')

    context = {
        'page_obj': page_obj,
        'status_choices': Article.STATUS_CHOICES,
        'category_choices': Article.CATEGORY_CHOICES,
        'priority_choices': Article.PRIORITY_CHOICES,
        'authors': authors,
        'current_filters': {
            'status': status_filter,
            'category': category_filter,
            'priority': priority_filter,
            'author': author_filter,
            'search': search_query,
            'sort': sort_by,
        },
        'current_organization': current_org,
    }

    return render(request, 'news_reader/article_list.html', context)


@login_required
@require_role('news_reader')
def article_detail(request, pk):
    """Detail view for an article"""
    article = get_object_or_404(Article, pk=pk)

    # Get article revisions
    revisions = article.revisions.select_related('revised_by')[:10]

    # Get article assignments
    assignments = article.assignments.filter(is_active=True).select_related('user', 'assigned_by')

    # Check if current user has an assignment for this article
    user_assignment = assignments.filter(user=request.user).first()

    context = {
        'article': article,
        'revisions': revisions,
        'assignments': assignments,
        'user_assignment': user_assignment,
        'can_edit': request.user == article.author or request.user.has_perm('news_reader.change_article'),
    }

    return render(request, 'news_reader/article_detail.html', context)


@login_required
@require_role('news_reader')
def article_create(request):
    """Create a new article"""
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization to create articles.')
        return redirect('organizations:list')

    if request.method == 'POST':
        # Handle form submission
        title = request.POST.get('title', '').strip()
        content = request.POST.get('content', '').strip()
        category = request.POST.get('category', 'local')
        priority = request.POST.get('priority', 'normal')
        byline = request.POST.get('byline', '').strip()

        # Validate required fields
        if not title:
            messages.error(request, 'Article title is required.')
            return render(request, 'news_reader/article_create.html', {
                'form_data': request.POST,
                'category_choices': Article.CATEGORY_CHOICES,
                'priority_choices': Article.PRIORITY_CHOICES,
                'voice_tone_choices': Article.VOICE_TONE_CHOICES,
            })

        try:
            # Create the article
            article = Article.objects.create(
                title=title,
                content=content,
                author=request.user,
                byline=byline or f"By {request.user.get_full_name() or request.user.username}",
                category=category,
                priority=priority,
                voice_tone=request.POST.get('voice_tone', 'neutral'),
                reading_speed=int(request.POST.get('reading_speed', 3)),
                target_length_minutes=int(request.POST.get('target_length_minutes', 2)),
                target_length_seconds=int(request.POST.get('target_length_seconds', 0)),
                is_breaking=request.POST.get('is_breaking') == 'on',
                is_urgent=request.POST.get('is_urgent') == 'on',
            )

            # Handle air times
            air_times_str = request.POST.get('air_times', '')
            if air_times_str:
                air_times = [time.strip() for time in air_times_str.split(',') if time.strip()]
                article.air_times = air_times
                article.save()

            # Create initial revision
            ArticleRevision.objects.create(
                article=article,
                revision_number=1,
                title=article.title,
                content=article.content,
                status=article.status,
                word_count=article.word_count,
                revised_by=request.user,
                revision_notes="Initial article creation"
            )

            messages.success(request, f'Article "{article.title}" created successfully.')
            return redirect('news_reader:article_detail', pk=article.pk)

        except Exception as e:
            messages.error(request, f'Error creating article: {str(e)}')

    context = {
        'category_choices': Article.CATEGORY_CHOICES,
        'priority_choices': Article.PRIORITY_CHOICES,
        'voice_tone_choices': Article.VOICE_TONE_CHOICES,
        'current_organization': current_org,
    }

    return render(request, 'news_reader/article_create.html', context)


@login_required
@require_role('news_reader')
def article_edit(request, pk):
    """Edit an existing article"""
    article = get_object_or_404(Article, pk=pk)

    # Check permissions
    if request.user != article.author and not request.user.has_perm('news_reader.change_article'):
        messages.error(request, 'You do not have permission to edit this article.')
        return redirect('news_reader:article_detail', pk=article.pk)

    if request.method == 'POST':
        # Store original values for revision tracking
        original_title = article.title
        original_content = article.content
        original_status = article.status

        # Handle form submission
        title = request.POST.get('title', '').strip()
        content = request.POST.get('content', '').strip()

        # Validate required fields
        if not title:
            messages.error(request, 'Article title is required.')
            return render(request, 'news_reader/article_edit.html', {
                'article': article,
                'form_data': request.POST,
                'category_choices': Article.CATEGORY_CHOICES,
                'priority_choices': Article.PRIORITY_CHOICES,
                'voice_tone_choices': Article.VOICE_TONE_CHOICES,
                'status_choices': Article.STATUS_CHOICES,
            })

        try:
            # Update the article
            article.title = title
            article.content = content
            article.byline = request.POST.get('byline', article.byline)
            article.category = request.POST.get('category', article.category)
            article.priority = request.POST.get('priority', article.priority)
            article.status = request.POST.get('status', article.status)
            article.voice_tone = request.POST.get('voice_tone', article.voice_tone)
            article.reading_speed = int(request.POST.get('reading_speed', article.reading_speed))
            article.target_length_minutes = int(request.POST.get('target_length_minutes', article.target_length_minutes))
            article.target_length_seconds = int(request.POST.get('target_length_seconds', article.target_length_seconds))
            article.is_breaking = request.POST.get('is_breaking') == 'on'
            article.is_urgent = request.POST.get('is_urgent') == 'on'
            article.last_edited_by = request.user

            # Handle air times
            air_times_str = request.POST.get('air_times', '')
            if air_times_str:
                air_times = [time.strip() for time in air_times_str.split(',') if time.strip()]
                article.air_times = air_times
            else:
                article.air_times = []

            # Handle scheduled date
            scheduled_date_str = request.POST.get('scheduled_date')
            if scheduled_date_str:
                from datetime import datetime
                article.scheduled_date = datetime.strptime(scheduled_date_str, '%Y-%m-%d').date()

            # Handle pronunciation guide
            pronunciation_data = request.POST.get('pronunciation_guide', '{}')
            try:
                article.pronunciation_guide = json.loads(pronunciation_data)
            except json.JSONDecodeError:
                pass  # Keep existing pronunciation guide if JSON is invalid

            article.save()

            # Create revision if significant changes were made
            if (original_title != article.title or
                original_content != article.content or
                original_status != article.status):

                article.create_new_version()
                ArticleRevision.objects.create(
                    article=article,
                    revision_number=article.version,
                    title=article.title,
                    content=article.content,
                    status=article.status,
                    word_count=article.word_count,
                    revised_by=request.user,
                    revision_notes=request.POST.get('revision_notes', 'Article updated')
                )

            messages.success(request, f'Article "{article.title}" updated successfully.')
            return redirect('news_reader:article_detail', pk=article.pk)

        except Exception as e:
            messages.error(request, f'Error updating article: {str(e)}')

    # Convert air_times list to comma-separated string for the form
    air_times_str = ', '.join(article.air_times) if article.air_times else ''

    context = {
        'article': article,
        'air_times_str': air_times_str,
        'category_choices': Article.CATEGORY_CHOICES,
        'priority_choices': Article.PRIORITY_CHOICES,
        'voice_tone_choices': Article.VOICE_TONE_CHOICES,
        'status_choices': Article.STATUS_CHOICES,
        'pronunciation_guide_json': json.dumps(article.pronunciation_guide),
    }

    return render(request, 'news_reader/article_edit.html', context)


@login_required
@require_role('news_reader')
def article_delete(request, pk):
    """Delete an article"""
    article = get_object_or_404(Article, pk=pk)

    # Check permissions
    if request.user != article.author and not request.user.has_perm('news_reader.delete_article'):
        messages.error(request, 'You do not have permission to delete this article.')
        return redirect('news_reader:article_detail', pk=article.pk)

    if request.method == 'POST':
        article_title = article.title
        article.delete()
        messages.success(request, f'Article "{article_title}" deleted successfully.')
        return redirect('news_reader:article_list')

    context = {
        'article': article,
    }

    return render(request, 'news_reader/article_delete.html', context)


@login_required
@require_role('news_reader')
def article_editor(request, pk):
    """Rich article editor interface (like make_article)"""
    article = get_object_or_404(Article, pk=pk)

    # Check permissions
    if request.user != article.author and not request.user.has_perm('news_reader.change_article'):
        messages.error(request, 'You do not have permission to edit this article.')
        return redirect('news_reader:article_detail', pk=article.pk)

    context = {
        'article': article,
        'pronunciation_guide_json': json.dumps(article.pronunciation_guide),
        'air_times_json': json.dumps(article.air_times),
        'audio_assets_json': json.dumps(article.audio_assets),
        'time_markers_json': json.dumps(article.time_markers),
        'emphasis_words_json': json.dumps(article.emphasis_words),
    }

    return render(request, 'news_reader/article_editor.html', context)


@login_required
@require_role('news_reader')
@require_http_methods(["POST"])
def article_auto_save(request, pk):
    """Auto-save article content via AJAX"""
    article = get_object_or_404(Article, pk=pk)

    # Check permissions
    if request.user != article.author and not request.user.has_perm('news_reader.change_article'):
        return JsonResponse({'success': False, 'error': 'Permission denied'}, status=403)

    try:
        data = json.loads(request.body)

        # Update article fields
        if 'title' in data:
            article.title = data['title']
        if 'content' in data:
            article.content = data['content']
        if 'pronunciation_guide' in data:
            article.pronunciation_guide = data['pronunciation_guide']
        if 'air_times' in data:
            article.air_times = data['air_times']
        if 'time_markers' in data:
            article.time_markers = data['time_markers']
        if 'emphasis_words' in data:
            article.emphasis_words = data['emphasis_words']

        article.last_edited_by = request.user
        article.save()

        return JsonResponse({
            'success': True,
            'word_count': article.word_count,
            'estimated_reading_time': article.estimated_reading_time,
            'reading_pace_status': article.reading_pace_status,
            'last_saved': timezone.now().strftime('%H:%M:%S')
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


@login_required
@require_role('news_reader')
def article_export_pdf(request, pk):
    """Export article as PDF"""
    article = get_object_or_404(Article, pk=pk)

    try:
        # Import PDF generation utilities
        from apps.reports.pdf_generator import EnhancedPDF, create_pdf_response

        # Create enhanced PDF
        pdf = EnhancedPDF(orientation='P', unit='mm', format='A4')
        pdf.add_page()

        # Set header configuration
        pdf.header_config = {
            'title': 'News Article',
            'enabled': True
        }

        # Article title
        pdf.set_font('Arial', 'B', 18)
        pdf.set_text_color(*pdf.primary_color)
        pdf.cell(0, 15, article.title, 0, 1, 'C')
        pdf.set_text_color(0, 0, 0)
        pdf.ln(5)

        # Article metadata
        pdf.set_font('Arial', '', 12)
        pdf.cell(0, 8, f"By: {article.byline}", 0, 1)
        pdf.cell(0, 8, f"Category: {article.get_category_display()}", 0, 1)
        pdf.cell(0, 8, f"Created: {article.created_at.strftime('%B %d, %Y')}", 0, 1)
        pdf.cell(0, 8, f"Status: {article.get_status_display()}", 0, 1)
        pdf.ln(5)

        # Article statistics
        pdf.add_section_header('Article Statistics', level=2)
        current_y = pdf.get_y()

        stats = {
            'Word Count': article.word_count,
            'Target Length': f"{article.target_length_minutes}:{article.target_length_seconds:02d}",
            'Priority': article.get_priority_display(),
            'Voice Tone': article.get_voice_tone_display()
        }

        for i, (key, value) in enumerate(stats.items()):
            x = 10 + i * 47.5
            pdf.add_metric_card(key, str(value), x, current_y, 45, 20)

        pdf.set_y(current_y + 30)

        # Article content
        pdf.add_section_header('Content', level=2)
        pdf.set_font('Arial', '', 11)

        # Split content into paragraphs and add them
        paragraphs = article.content.split('\n\n')
        for paragraph in paragraphs:
            if paragraph.strip():
                # Clean up the paragraph text
                clean_text = paragraph.strip().replace('\n', ' ')
                pdf.multi_cell(0, 6, clean_text)
                pdf.ln(3)

        # Air times if exists
        if article.air_times:
            pdf.add_section_header('Scheduled Air Times', level=2)
            pdf.set_font('Arial', '', 11)
            air_times_text = ', '.join(article.air_times)
            pdf.cell(0, 8, air_times_text, 0, 1)
            pdf.ln(5)

        # Pronunciation guide if exists
        if article.pronunciation_guide:
            pdf.add_section_header('Pronunciation Guide', level=2)
            pdf.set_font('Arial', '', 11)

            for word, pronunciation in article.pronunciation_guide.items():
                pdf.set_font('Arial', 'B', 11)
                pdf.cell(50, 6, word + ':', 0, 0)
                pdf.set_font('Arial', '', 11)
                pdf.cell(0, 6, pronunciation, 0, 1)

        # Create filename and return response
        filename = f"{article.slug}_{timezone.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        return create_pdf_response(pdf, filename)

    except Exception as e:
        messages.error(request, f'Error generating PDF: {str(e)}')
        return redirect('news_reader:article_detail', pk=article.pk)


@login_required
@require_role('news_reader')
def article_teleprompter(request, pk):
    """Teleprompter view for article"""
    article = get_object_or_404(Article, pk=pk)

    context = {
        'article': article,
    }

    return render(request, 'news_reader/article_teleprompter.html', context)


@login_required
@require_role('news_reader')
def article_mobile_view(request, pk):
    """Mobile-optimized view for article"""
    article = get_object_or_404(Article, pk=pk)

    context = {
        'article': article,
    }

    return render(request, 'news_reader/article_mobile.html', context)


# Article Assignment Views

@login_required
@require_role('news_reader')
def article_assign(request, pk):
    """Assign article to news readers"""
    article = get_object_or_404(Article, pk=pk)

    # Check permissions - only authors or managers can assign
    if (request.user != article.author and
        not request.user.has_perm('news_reader.add_articleassignment')):
        messages.error(request, 'You do not have permission to assign this article.')
        return redirect('news_reader:article_detail', pk=article.pk)

    if request.method == 'POST':
        user_ids = request.POST.getlist('users')
        due_date_str = request.POST.get('due_date')
        assignment_notes = request.POST.get('assignment_notes', '')

        if not user_ids or not due_date_str:
            messages.error(request, 'Please select users and set a due date.')
        else:
            try:
                from datetime import datetime
                due_date = datetime.strptime(due_date_str, '%Y-%m-%dT%H:%M')
                due_date = timezone.make_aware(due_date)

                created_count = 0
                for user_id in user_ids:
                    user = get_object_or_404(User, pk=user_id)
                    assignment, created = ArticleAssignment.objects.get_or_create(
                        article=article,
                        user=user,
                        defaults={
                            'assigned_by': request.user,
                            'due_date': due_date,
                            'assignment_notes': assignment_notes,
                        }
                    )
                    if created:
                        created_count += 1

                messages.success(request, f'Article assigned to {created_count} news readers.')
                return redirect('news_reader:article_detail', pk=article.pk)

            except Exception as e:
                messages.error(request, f'Error creating assignments: {str(e)}')

    # Get available news readers
    from apps.organizations.models import OrganizationMembership
    news_readers = User.objects.filter(
        organization_memberships__role='news_reader',
        organization_memberships__is_active=True
    ).distinct().order_by('first_name', 'last_name')

    # Get existing assignments
    existing_assignments = article.assignments.filter(is_active=True).values_list('user_id', flat=True)

    context = {
        'article': article,
        'news_readers': news_readers,
        'existing_assignments': existing_assignments,
    }

    return render(request, 'news_reader/article_assign.html', context)


@login_required
@require_role('news_reader')
def live_reading_interface(request):
    """Live reading interface for news readers"""
    current_org = get_current_organization(request)
    if not current_org:
        messages.error(request, 'Please select an organization to access live reading.')
        return redirect('organizations:list')

    # Get articles scheduled for today or ready for reading
    today = timezone.now().date()

    # Get articles that are approved/scheduled and assigned to current user
    user_assignments = ArticleAssignment.objects.filter(
        user=request.user,
        is_active=True,
        article__status__in=['approved', 'scheduled', 'published']
    ).select_related('article').order_by('due_date')

    # Also get articles scheduled for today (even if not assigned)
    scheduled_articles = Article.objects.filter(
        scheduled_date=today,
        status__in=['approved', 'scheduled']
    ).exclude(
        assignments__user=request.user,
        assignments__is_active=True
    )

    # Combine and organize articles
    reading_queue = []

    # Add assigned articles
    for assignment in user_assignments:
        reading_queue.append({
            'article': assignment.article,
            'assignment': assignment,
            'type': 'assigned',
            'due_time': assignment.due_date,
            'priority': assignment.article.priority
        })

    # Add scheduled articles
    for article in scheduled_articles:
        reading_queue.append({
            'article': article,
            'assignment': None,
            'type': 'scheduled',
            'due_time': None,
            'priority': article.priority
        })

    # Sort by priority and due time
    priority_order = {'breaking': 0, 'urgent': 1, 'high': 2, 'normal': 3, 'low': 4}
    reading_queue.sort(key=lambda x: (
        priority_order.get(x['priority'], 5),
        x['due_time'] or timezone.now()
    ))

    context = {
        'reading_queue': reading_queue,
        'current_organization': current_org,
        'total_articles': len(reading_queue),
    }

    return render(request, 'news_reader/live_reading.html', context)


@login_required
@require_role('news_reader')
def live_reading_article(request, pk):
    """Live reading interface for a specific article"""
    article = get_object_or_404(Article, pk=pk)

    # Check if user has access to this article
    user_assignment = ArticleAssignment.objects.filter(
        article=article,
        user=request.user,
        is_active=True
    ).first()

    # Allow access if user is assigned or if article is published/scheduled
    if not user_assignment and article.status not in ['published', 'scheduled', 'approved']:
        messages.error(request, 'You do not have access to read this article.')
        return redirect('news_reader:live_reading_interface')

    # Mark assignment as in progress if exists
    if user_assignment and not user_assignment.is_completed:
        user_assignment.preparation_status = 'in_progress'
        user_assignment.save()

    context = {
        'article': article,
        'assignment': user_assignment,
        'pronunciation_guide_json': json.dumps(article.pronunciation_guide),
        'air_times_json': json.dumps(article.air_times),
    }

    return render(request, 'news_reader/live_reading_article.html', context)


@login_required
@require_role('news_reader')
@require_http_methods(["POST"])
def mark_article_read(request, pk):
    """Mark an article as read/completed"""
    article = get_object_or_404(Article, pk=pk)

    # Find user's assignment for this article
    user_assignment = ArticleAssignment.objects.filter(
        article=article,
        user=request.user,
        is_active=True
    ).first()

    if user_assignment:
        user_assignment.mark_completed()

        # Create a reading session record
        ReadingSession.objects.create(
            user=request.user,
            session_type='live_reading',
            notes=f"Read article: {article.title}",
            duration_minutes=request.POST.get('duration_minutes', 0)
        )

        return JsonResponse({'success': True, 'message': 'Article marked as read'})

    return JsonResponse({'success': False, 'error': 'No assignment found'}, status=404)
