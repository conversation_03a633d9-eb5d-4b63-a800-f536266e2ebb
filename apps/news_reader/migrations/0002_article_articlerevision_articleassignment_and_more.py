# Generated by Django 4.2.7 on 2025-06-26 22:17

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("news_reader", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Article",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "title",
                    models.Char<PERSON>ield(help_text="Article headline", max_length=300),
                ),
                (
                    "slug",
                    models.SlugField(
                        help_text="URL-friendly identifier", max_length=100, unique=True
                    ),
                ),
                (
                    "content",
                    models.TextField(help_text="Main article content with formatting"),
                ),
                (
                    "byline",
                    models.Char<PERSON><PERSON>(
                        blank=True,
                        help_text="Author byline (e.g., '<PERSON>')",
                        max_length=200,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("breaking_news", "Breaking News"),
                            ("weather", "Weather"),
                            ("politics", "Politics"),
                            ("business", "Business"),
                            ("health", "Health"),
                            ("sports", "Sports"),
                            ("entertainment", "Entertainment"),
                            ("technology", "Technology"),
                            ("local", "Local News"),
                            ("international", "International"),
                        ],
                        default="local",
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("in_review", "In Review"),
                            ("approved", "Approved"),
                            ("scheduled", "Scheduled"),
                            ("published", "Published"),
                            ("archived", "Archived"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("normal", "Normal"),
                            ("high", "High"),
                            ("urgent", "Urgent"),
                            ("breaking", "Breaking"),
                        ],
                        default="normal",
                        max_length=10,
                    ),
                ),
                (
                    "target_length_minutes",
                    models.PositiveIntegerField(
                        default=2,
                        help_text="Target reading length in minutes",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(30),
                        ],
                    ),
                ),
                (
                    "target_length_seconds",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Additional seconds for target length",
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(59),
                        ],
                    ),
                ),
                (
                    "estimated_reading_time",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Estimated reading time in seconds (auto-calculated)",
                        null=True,
                    ),
                ),
                (
                    "word_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Word count (auto-calculated)"
                    ),
                ),
                (
                    "voice_tone",
                    models.CharField(
                        choices=[
                            ("neutral", "Neutral"),
                            ("formal", "Formal"),
                            ("urgent", "Urgent"),
                            ("conversational", "Conversational"),
                        ],
                        default="neutral",
                        max_length=20,
                    ),
                ),
                (
                    "reading_speed",
                    models.IntegerField(
                        default=3,
                        help_text="Reading speed scale (1=slow, 5=fast)",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "air_times",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of scheduled air times (e.g., ['07:30', '09:30', '12:30'])",
                    ),
                ),
                (
                    "scheduled_date",
                    models.DateField(
                        blank=True, help_text="Date when article should air", null=True
                    ),
                ),
                (
                    "pronunciation_guide",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Dictionary of word pronunciations (e.g., {'Newsom': 'NEW-sum'})",
                    ),
                ),
                (
                    "time_markers",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of time markers in the content for pacing",
                    ),
                ),
                (
                    "emphasis_words",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of words or phrases to emphasize during reading",
                    ),
                ),
                (
                    "audio_assets",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="List of associated audio clips and their metadata",
                    ),
                ),
                ("published_at", models.DateTimeField(blank=True, null=True)),
                (
                    "is_breaking",
                    models.BooleanField(
                        default=False, help_text="Mark as breaking news"
                    ),
                ),
                (
                    "is_urgent",
                    models.BooleanField(default=False, help_text="Mark as urgent"),
                ),
                (
                    "auto_save_enabled",
                    models.BooleanField(
                        default=True, help_text="Enable auto-save for this article"
                    ),
                ),
                (
                    "version",
                    models.PositiveIntegerField(
                        default=1, help_text="Article version number"
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="authored_articles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "last_edited_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="last_edited_articles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ArticleRevision",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("revision_number", models.PositiveIntegerField()),
                ("title", models.CharField(max_length=300)),
                ("content", models.TextField()),
                ("status", models.CharField(max_length=20)),
                ("word_count", models.PositiveIntegerField(default=0)),
                (
                    "revision_notes",
                    models.TextField(
                        blank=True,
                        help_text="Notes about what changed in this revision",
                    ),
                ),
                (
                    "changes_summary",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Summary of changes made in this revision",
                    ),
                ),
                (
                    "article",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="revisions",
                        to="news_reader.article",
                    ),
                ),
                (
                    "revised_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="article_revisions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-revision_number"],
                "indexes": [
                    models.Index(
                        fields=["article", "revision_number"],
                        name="news_reader_article_0d8455_idx",
                    ),
                    models.Index(
                        fields=["revised_by", "created_at"],
                        name="news_reader_revised_bb8ac8_idx",
                    ),
                ],
                "unique_together": {("article", "revision_number")},
            },
        ),
        migrations.CreateModel(
            name="ArticleAssignment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("assigned_at", models.DateTimeField(auto_now_add=True)),
                (
                    "due_date",
                    models.DateTimeField(
                        help_text="When the article should be prepared by"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_completed", models.BooleanField(default=False)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "preparation_status",
                    models.CharField(
                        choices=[
                            ("not_prepared", "Not Prepared"),
                            ("prepared", "Prepared"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                        ],
                        default="not_prepared",
                        max_length=20,
                    ),
                ),
                (
                    "preparation_notes",
                    models.TextField(
                        blank=True, help_text="Notes about article preparation"
                    ),
                ),
                (
                    "assignment_notes",
                    models.TextField(
                        blank=True, help_text="Special instructions for this assignment"
                    ),
                ),
                (
                    "article",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assignments",
                        to="news_reader.article",
                    ),
                ),
                (
                    "assigned_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_articles",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="article_assignments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["due_date"],
                "indexes": [
                    models.Index(
                        fields=["user", "is_active", "due_date"],
                        name="news_reader_user_id_894f14_idx",
                    ),
                    models.Index(
                        fields=["article", "is_active"],
                        name="news_reader_article_e4c48c_idx",
                    ),
                    models.Index(
                        fields=["preparation_status"],
                        name="news_reader_prepara_5c187c_idx",
                    ),
                ],
                "unique_together": {("article", "user")},
            },
        ),
        migrations.AddIndex(
            model_name="article",
            index=models.Index(
                fields=["author", "status"], name="news_reader_author__a048b0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="article",
            index=models.Index(
                fields=["category", "status"], name="news_reader_categor_9e01f7_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="article",
            index=models.Index(
                fields=["scheduled_date", "status"],
                name="news_reader_schedul_ed1f30_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="article",
            index=models.Index(
                fields=["priority", "created_at"], name="news_reader_priorit_ef6138_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="article",
            index=models.Index(fields=["slug"], name="news_reader_slug_03e53a_idx"),
        ),
    ]
