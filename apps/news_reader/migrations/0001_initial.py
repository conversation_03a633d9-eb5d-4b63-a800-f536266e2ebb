# Generated by Django 4.2.7 on 2025-06-26 10:23

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("mentions", "0009_mentionauditlog"),
    ]

    operations = [
        migrations.CreateModel(
            name="ReadingSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("session_id", models.UUIDField(default=uuid.uuid4, unique=True)),
                ("start_time", models.DateTimeField(auto_now_add=True)),
                ("end_time", models.DateTimeField(blank=True, null=True)),
                (
                    "session_type",
                    models.CharField(
                        choices=[
                            ("preparation", "Preparation Session"),
                            ("live_reading", "Live Reading Session"),
                            ("review", "Review Session"),
                        ],
                        default="preparation",
                        max_length=20,
                    ),
                ),
                ("mentions_prepared", models.PositiveIntegerField(default=0)),
                ("mentions_read", models.PositiveIntegerField(default=0)),
                (
                    "total_reading_time",
                    models.PositiveIntegerField(
                        default=0, help_text="Total time in seconds"
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Session notes and observations"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reading_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-start_time"],
                "indexes": [
                    models.Index(
                        fields=["user", "start_time"],
                        name="news_reader_user_id_319d47_idx",
                    ),
                    models.Index(
                        fields=["session_type", "start_time"],
                        name="news_reader_session_33015c_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="ReadingNote",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "title",
                    models.CharField(
                        blank=True,
                        help_text="Optional title for the note",
                        max_length=200,
                    ),
                ),
                (
                    "content",
                    models.TextField(
                        blank=True, help_text="Preparation notes and reminders"
                    ),
                ),
                (
                    "pronunciation_guide",
                    models.TextField(
                        blank=True,
                        help_text="Pronunciation guides for difficult words or names",
                    ),
                ),
                (
                    "personal_reminders",
                    models.TextField(
                        blank=True, help_text="Personal reminders and cues for reading"
                    ),
                ),
                (
                    "preparation_status",
                    models.CharField(
                        choices=[
                            ("not_prepared", "Not Prepared"),
                            ("prepared", "Prepared"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                        ],
                        default="not_prepared",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.IntegerField(
                        choices=[(1, "Low"), (2, "Normal"), (3, "High"), (4, "Urgent")],
                        default=2,
                    ),
                ),
                (
                    "estimated_prep_time",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Estimated preparation time in minutes",
                        null=True,
                    ),
                ),
                (
                    "actual_prep_time",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Actual preparation time in minutes",
                        null=True,
                    ),
                ),
                ("preparation_started_at", models.DateTimeField(blank=True, null=True)),
                (
                    "preparation_completed_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("last_reviewed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "is_favorite",
                    models.BooleanField(
                        default=False, help_text="Mark as favorite for quick access"
                    ),
                ),
                (
                    "needs_review",
                    models.BooleanField(
                        default=False, help_text="Flag for items needing review"
                    ),
                ),
                (
                    "mention",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reading_notes",
                        to="mentions.mention",
                    ),
                ),
                (
                    "mention_reading",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reading_notes",
                        to="mentions.mentionreading",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reading_notes",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "preparation_status"],
                        name="news_reader_user_id_1902a2_idx",
                    ),
                    models.Index(
                        fields=["mention", "preparation_status"],
                        name="news_reader_mention_56865a_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="news_reader_created_9c5d24_idx"
                    ),
                ],
                "unique_together": {("user", "mention")},
            },
        ),
        migrations.CreateModel(
            name="NewsReaderAssignment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("assigned_at", models.DateTimeField(auto_now_add=True)),
                (
                    "due_date",
                    models.DateTimeField(
                        help_text="When the reading should be prepared by"
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_completed", models.BooleanField(default=False)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "assignment_notes",
                    models.TextField(
                        blank=True, help_text="Special instructions for this assignment"
                    ),
                ),
                (
                    "assigned_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_readings",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "mention_reading",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="news_assignments",
                        to="mentions.mentionreading",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="news_assignments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["due_date"],
                "indexes": [
                    models.Index(
                        fields=["user", "is_active", "due_date"],
                        name="news_reader_user_id_80b7f9_idx",
                    ),
                    models.Index(
                        fields=["mention_reading", "is_active"],
                        name="news_reader_mention_ae5071_idx",
                    ),
                ],
                "unique_together": {("user", "mention_reading")},
            },
        ),
    ]
