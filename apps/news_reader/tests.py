from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from datetime import datetime, timedelta, time

from apps.organizations.models import Organization, OrganizationMembership
from apps.mentions.models import Mention, MentionReading
from apps.shows.models import Show
from apps.core.models import Client as CoreClient
from .models import ReadingNote, ReadingSession, NewsReaderAssignment


class NewsReaderModelTests(TestCase):
    """Test News Reader models"""

    def setUp(self):
        # Create test user
        self.user = User.objects.create_user(
            username='newsreader',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create organization and membership
        self.organization = Organization.objects.create(
            name='Test Radio Station',
            slug='test-radio'
        )

        self.membership = OrganizationMembership.objects.create(
            user=self.user,
            organization=self.organization,
            role='news_reader',
            is_active=True
        )

        # Create test client
        self.client_obj = CoreClient.objects.create(
            name='Test Client',
            organization=self.organization
        )

        # Create test show
        self.show = Show.objects.create(
            name='Morning News',
            organization=self.organization,
            start_time=time(6, 0),
            end_time=time(9, 0)
        )

        # Create test mention
        self.mention = Mention.objects.create(
            title='Test News Mention',
            content='This is a test news mention for reading.',
            client=self.client_obj,
            duration_seconds=30,
            priority=2,
            status='scheduled'
        )

        # Create mention reading
        self.mention_reading = MentionReading.objects.create(
            mention=self.mention,
            show=self.show,
            scheduled_date=timezone.now().date(),
            scheduled_time=time(7, 30)  # Within show hours (6:00-9:00)
        )

    def test_reading_note_creation(self):
        """Test creating a reading note"""
        note = ReadingNote.objects.create(
            user=self.user,
            mention=self.mention,
            title='Test Note',
            content='Test preparation notes',
            preparation_status='not_prepared',
            priority=2
        )

        self.assertEqual(note.user, self.user)
        self.assertEqual(note.mention, self.mention)
        self.assertEqual(note.preparation_status, 'not_prepared')
        self.assertFalse(note.is_overdue)  # Should not be overdue without mention_reading

    def test_reading_note_status_transitions(self):
        """Test reading note status transitions"""
        note = ReadingNote.objects.create(
            user=self.user,
            mention=self.mention,
            content='Test notes'
        )

        # Test starting preparation
        note.start_preparation()
        self.assertEqual(note.preparation_status, 'in_progress')
        self.assertIsNotNone(note.preparation_started_at)

        # Test marking as prepared
        note.mark_as_prepared()
        self.assertEqual(note.preparation_status, 'prepared')
        self.assertIsNotNone(note.preparation_completed_at)

        # Test completing reading
        note.complete_reading()
        self.assertEqual(note.preparation_status, 'completed')

    def test_reading_session_creation(self):
        """Test creating a reading session"""
        session = ReadingSession.objects.create(
            user=self.user,
            session_type='preparation'
        )

        self.assertEqual(session.user, self.user)
        self.assertEqual(session.session_type, 'preparation')
        self.assertTrue(session.is_active)
        self.assertIsNone(session.end_time)

        # Test ending session
        session.end_session()
        self.assertFalse(session.is_active)
        self.assertIsNotNone(session.end_time)

    def test_news_reader_assignment_creation(self):
        """Test creating a news reader assignment"""
        due_date = timezone.now() + timedelta(hours=2)

        assignment = NewsReaderAssignment.objects.create(
            user=self.user,
            mention_reading=self.mention_reading,
            due_date=due_date,
            assignment_notes='Please prepare this carefully'
        )

        self.assertEqual(assignment.user, self.user)
        self.assertEqual(assignment.mention_reading, self.mention_reading)
        self.assertFalse(assignment.is_overdue)
        self.assertFalse(assignment.is_completed)

        # Test marking as completed
        assignment.mark_completed()
        self.assertTrue(assignment.is_completed)
        self.assertIsNotNone(assignment.completed_at)
