from django import forms
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from .models import ReadingNote, ReadingSession, NewsReaderAssignment, Article, ArticleAssignment
from apps.mentions.models import Mention, MentionReading
import json


class ReadingNoteForm(forms.ModelForm):
    """Form for creating and editing reading notes"""
    
    class Meta:
        model = ReadingNote
        fields = [
            'title', 'content', 'pronunciation_guide', 'personal_reminders',
            'preparation_status', 'priority', 'estimated_prep_time',
            'is_favorite', 'needs_review'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter note title (optional)'
            }),
            'content': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'rows': 6,
                'placeholder': 'Enter your preparation notes and reminders...'
            }),
            'pronunciation_guide': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'rows': 3,
                'placeholder': 'Enter pronunciation guides for difficult words or names...'
            }),
            'personal_reminders': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'rows': 3,
                'placeholder': 'Enter personal reminders and cues for reading...'
            }),
            'preparation_status': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'priority': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'estimated_prep_time': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Minutes',
                'min': '1',
                'max': '120'
            }),
            'is_favorite': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            }),
            'needs_review': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            }),
        }
        labels = {
            'title': 'Note Title',
            'content': 'Preparation Notes',
            'pronunciation_guide': 'Pronunciation Guide',
            'personal_reminders': 'Personal Reminders',
            'preparation_status': 'Preparation Status',
            'priority': 'Priority Level',
            'estimated_prep_time': 'Estimated Prep Time (minutes)',
            'is_favorite': 'Mark as Favorite',
            'needs_review': 'Needs Review',
        }
        help_texts = {
            'title': 'Optional title for this note',
            'content': 'Your main preparation notes and key points',
            'pronunciation_guide': 'How to pronounce difficult words, names, or terms',
            'personal_reminders': 'Personal cues and reminders for reading',
            'estimated_prep_time': 'How long you expect to spend preparing this',
            'is_favorite': 'Mark this note as a favorite for quick access',
            'needs_review': 'Flag this note as needing review',
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Set initial values based on mention if creating new note
        if not self.instance.pk and hasattr(self.instance, 'mention') and self.instance.mention:
            self.fields['title'].initial = f"Note for {self.instance.mention.title}"


class ReadingNoteCreateForm(ReadingNoteForm):
    """Form for creating a new reading note with mention selection"""
    
    mention = forms.ModelChoiceField(
        queryset=Mention.objects.none(),
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        help_text='Select the mention this note is for'
    )
    
    mention_reading = forms.ModelChoiceField(
        queryset=MentionReading.objects.none(),
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        help_text='Optional: Select specific reading schedule'
    )

    class Meta(ReadingNoteForm.Meta):
        fields = ['mention', 'mention_reading'] + ReadingNoteForm.Meta.fields

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        if self.user:
            # Get mentions that are scheduled and don't already have notes by this user
            existing_note_mentions = ReadingNote.objects.filter(user=self.user).values_list('mention_id', flat=True)
            self.fields['mention'].queryset = Mention.objects.filter(
                status='scheduled'
            ).exclude(id__in=existing_note_mentions).order_by('-created_at')
            
            # Initially empty, will be populated via JavaScript based on mention selection
            self.fields['mention_reading'].queryset = MentionReading.objects.none()

    def clean(self):
        cleaned_data = super().clean()
        mention = cleaned_data.get('mention')
        user = self.user
        
        # Check if user already has a note for this mention
        if mention and user:
            existing_note = ReadingNote.objects.filter(user=user, mention=mention).first()
            if existing_note:
                raise forms.ValidationError(f"You already have a note for '{mention.title}'.")
        
        return cleaned_data


class ReadingSessionForm(forms.ModelForm):
    """Form for creating and managing reading sessions"""
    
    class Meta:
        model = ReadingSession
        fields = ['session_type', 'notes']
        widgets = {
            'session_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'rows': 4,
                'placeholder': 'Enter session notes and observations...'
            }),
        }
        labels = {
            'session_type': 'Session Type',
            'notes': 'Session Notes',
        }
        help_texts = {
            'session_type': 'What type of session is this?',
            'notes': 'Notes and observations about this session',
        }


class NewsReaderAssignmentForm(forms.ModelForm):
    """Form for creating and managing news reader assignments"""
    
    class Meta:
        model = NewsReaderAssignment
        fields = ['user', 'mention_reading', 'due_date', 'assignment_notes']
        widgets = {
            'user': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'mention_reading': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'due_date': forms.DateTimeInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'type': 'datetime-local'
            }),
            'assignment_notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'rows': 3,
                'placeholder': 'Special instructions for this assignment...'
            }),
        }
        labels = {
            'user': 'News Reader',
            'mention_reading': 'Mention Reading',
            'due_date': 'Due Date & Time',
            'assignment_notes': 'Assignment Notes',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Filter users to only show news readers
        from apps.organizations.models import OrganizationMembership
        news_reader_user_ids = OrganizationMembership.objects.filter(
            role='news_reader',
            is_active=True
        ).values_list('user_id', flat=True)
        
        self.fields['user'].queryset = User.objects.filter(
            id__in=news_reader_user_ids
        ).order_by('first_name', 'last_name', 'username')
        
        # Filter mention readings to only show scheduled ones
        self.fields['mention_reading'].queryset = MentionReading.objects.filter(
            mention__status='scheduled',
            actual_read_time__isnull=True
        ).select_related('mention', 'show').order_by('scheduled_date', 'scheduled_time')


class QuickNoteForm(forms.Form):
    """Quick form for creating simple notes"""
    
    content = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            'rows': 3,
            'placeholder': 'Quick note...'
        }),
        label='Note Content',
        help_text='Enter a quick preparation note'
    )
    
    priority = forms.ChoiceField(
        choices=ReadingNote.PRIORITY_CHOICES,
        initial=2,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        label='Priority'
    )


class NoteSearchForm(forms.Form):
    """Form for searching notes"""
    
    query = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            'placeholder': 'Search notes...'
        }),
        label='Search Query'
    )
    
    status = forms.ChoiceField(
        choices=[('', 'All Statuses')] + ReadingNote.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        label='Status'
    )
    
    priority = forms.ChoiceField(
        choices=[('', 'All Priorities')] + ReadingNote.PRIORITY_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        label='Priority'
    )


class ArticleForm(forms.ModelForm):
    """Form for creating and editing articles"""

    # Custom fields for better UX
    air_times_text = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            'placeholder': 'e.g., 07:30, 09:30, 12:30'
        }),
        help_text='Enter air times separated by commas',
        label='Air Times'
    )

    pronunciation_guide_text = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            'rows': 4,
            'placeholder': 'Word: pronunciation (one per line)\ne.g., Newsom: NEW-sum'
        }),
        help_text='Enter pronunciation guides, one per line',
        label='Pronunciation Guide'
    )

    class Meta:
        model = Article
        fields = [
            'title', 'content', 'byline', 'category', 'priority', 'status',
            'target_length_minutes', 'target_length_seconds', 'voice_tone', 'reading_speed',
            'scheduled_date', 'is_breaking', 'is_urgent'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter article title'
            }),
            'content': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'rows': 15,
                'placeholder': 'Enter article content...'
            }),
            'byline': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'By Author Name'
            }),
            'category': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'priority': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'status': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'target_length_minutes': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'min': '1',
                'max': '30'
            }),
            'target_length_seconds': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'min': '0',
                'max': '59'
            }),
            'voice_tone': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'reading_speed': forms.NumberInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'min': '1',
                'max': '5'
            }),
            'scheduled_date': forms.DateInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'type': 'date'
            }),
            'is_breaking': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            }),
            'is_urgent': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate custom fields if editing existing article
        if self.instance and self.instance.pk:
            # Convert air_times list to comma-separated string
            if self.instance.air_times:
                self.fields['air_times_text'].initial = ', '.join(self.instance.air_times)

            # Convert pronunciation_guide dict to text format
            if self.instance.pronunciation_guide:
                pronunciation_lines = []
                for word, pronunciation in self.instance.pronunciation_guide.items():
                    pronunciation_lines.append(f"{word}: {pronunciation}")
                self.fields['pronunciation_guide_text'].initial = '\n'.join(pronunciation_lines)

    def clean_air_times_text(self):
        """Clean and validate air times"""
        air_times_text = self.cleaned_data.get('air_times_text', '')
        if not air_times_text:
            return []

        air_times = []
        for time_str in air_times_text.split(','):
            time_str = time_str.strip()
            if time_str:
                # Validate time format (HH:MM)
                try:
                    from datetime import datetime
                    datetime.strptime(time_str, '%H:%M')
                    air_times.append(time_str)
                except ValueError:
                    raise forms.ValidationError(f'Invalid time format: {time_str}. Use HH:MM format.')

        return air_times

    def clean_pronunciation_guide_text(self):
        """Clean and validate pronunciation guide"""
        pronunciation_text = self.cleaned_data.get('pronunciation_guide_text', '')
        if not pronunciation_text:
            return {}

        pronunciation_guide = {}
        for line in pronunciation_text.split('\n'):
            line = line.strip()
            if line and ':' in line:
                word, pronunciation = line.split(':', 1)
                pronunciation_guide[word.strip()] = pronunciation.strip()

        return pronunciation_guide

    def save(self, commit=True):
        """Override save to handle custom fields"""
        article = super().save(commit=False)

        # Set air_times from cleaned custom field
        article.air_times = self.cleaned_data.get('air_times_text', [])

        # Set pronunciation_guide from cleaned custom field
        article.pronunciation_guide = self.cleaned_data.get('pronunciation_guide_text', {})

        if commit:
            article.save()

        return article


class ArticleQuickCreateForm(forms.ModelForm):
    """Simplified form for quick article creation"""

    class Meta:
        model = Article
        fields = ['title', 'content', 'category', 'priority']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'Enter article title'
            }),
            'content': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'rows': 10,
                'placeholder': 'Enter article content...'
            }),
            'category': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
            'priority': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            }),
        }


class ArticleAssignmentForm(forms.ModelForm):
    """Form for assigning articles to news readers"""

    users = forms.ModelMultipleChoiceField(
        queryset=User.objects.none(),
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded'
        }),
        label='Assign to News Readers'
    )

    class Meta:
        model = ArticleAssignment
        fields = ['due_date', 'assignment_notes']
        widgets = {
            'due_date': forms.DateTimeInput(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'type': 'datetime-local'
            }),
            'assignment_notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'rows': 3,
                'placeholder': 'Special instructions for this assignment...'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Filter users to only show news readers
        from apps.organizations.models import OrganizationMembership
        news_reader_user_ids = OrganizationMembership.objects.filter(
            role='news_reader',
            is_active=True
        ).values_list('user_id', flat=True)

        self.fields['users'].queryset = User.objects.filter(
            id__in=news_reader_user_ids
        ).order_by('first_name', 'last_name', 'username')


class ArticleFilterForm(forms.Form):
    """Form for filtering articles in the list view"""

    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            'placeholder': 'Search articles...'
        }),
        label='Search'
    )

    status = forms.ChoiceField(
        required=False,
        choices=[('', 'All Statuses')] + Article.STATUS_CHOICES,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        label='Status'
    )

    category = forms.ChoiceField(
        required=False,
        choices=[('', 'All Categories')] + Article.CATEGORY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        label='Category'
    )

    priority = forms.ChoiceField(
        required=False,
        choices=[('', 'All Priorities')] + Article.PRIORITY_CHOICES,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        label='Priority'
    )

    author = forms.ModelChoiceField(
        required=False,
        queryset=User.objects.none(),
        empty_label='All Authors',
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        label='Author'
    )

    sort = forms.ChoiceField(
        required=False,
        choices=[
            ('-created_at', 'Newest First'),
            ('created_at', 'Oldest First'),
            ('title', 'Title A-Z'),
            ('-title', 'Title Z-A'),
            ('-scheduled_date', 'Scheduled Date (Latest)'),
            ('scheduled_date', 'Scheduled Date (Earliest)'),
            ('-priority', 'Priority (High to Low)'),
            ('priority', 'Priority (Low to High)'),
        ],
        initial='-created_at',
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
        }),
        label='Sort By'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Populate author choices with users who have authored articles
        self.fields['author'].queryset = User.objects.filter(
            authored_articles__isnull=False
        ).distinct().order_by('first_name', 'last_name')
