from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils import timezone
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from apps.core.models import TimeStampedModel
from apps.mentions.models import Mention, MentionReading
import uuid
import json


class ReadingNote(TimeStampedModel):
    """Model for news reader preparation notes"""
    STATUS_CHOICES = [
        ('not_prepared', 'Not Prepared'),
        ('prepared', 'Prepared'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
    ]

    PRIORITY_CHOICES = [
        (1, 'Low'),
        (2, 'Normal'),
        (3, 'High'),
        (4, 'Urgent'),
    ]

    # Relationships
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reading_notes')
    mention = models.ForeignKey(Mention, on_delete=models.CASCADE, related_name='reading_notes')
    mention_reading = models.ForeignKey(MentionReading, on_delete=models.CASCADE,
                                      related_name='reading_notes', null=True, blank=True)

    # Note content
    title = models.CharField(max_length=200, blank=True, help_text="Optional title for the note")
    content = models.TextField(blank=True, help_text="Preparation notes and reminders")
    pronunciation_guide = models.TextField(blank=True,
                                         help_text="Pronunciation guides for difficult words or names")
    personal_reminders = models.TextField(blank=True,
                                        help_text="Personal reminders and cues for reading")

    # Status and metadata
    preparation_status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_prepared')
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2)
    estimated_prep_time = models.PositiveIntegerField(null=True, blank=True,
                                                    help_text="Estimated preparation time in minutes")
    actual_prep_time = models.PositiveIntegerField(null=True, blank=True,
                                                 help_text="Actual preparation time in minutes")

    # Timestamps
    preparation_started_at = models.DateTimeField(null=True, blank=True)
    preparation_completed_at = models.DateTimeField(null=True, blank=True)
    last_reviewed_at = models.DateTimeField(null=True, blank=True)

    # Flags
    is_favorite = models.BooleanField(default=False, help_text="Mark as favorite for quick access")
    needs_review = models.BooleanField(default=False, help_text="Flag for items needing review")

    class Meta:
        unique_together = ['user', 'mention']
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'preparation_status']),
            models.Index(fields=['mention', 'preparation_status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Note by {self.user.username} for {self.mention.title}"

    def get_absolute_url(self):
        return reverse('news_reader:note_detail', kwargs={'pk': self.pk})

    def mark_as_prepared(self):
        """Mark the note as prepared"""
        self.preparation_status = 'prepared'
        self.preparation_completed_at = timezone.now()
        self.save()

    def start_preparation(self):
        """Mark preparation as started"""
        if self.preparation_status == 'not_prepared':
            self.preparation_status = 'in_progress'
            self.preparation_started_at = timezone.now()
            self.save()

    def complete_reading(self):
        """Mark the reading as completed"""
        self.preparation_status = 'completed'
        self.save()

    @property
    def preparation_duration(self):
        """Calculate preparation duration if both timestamps exist"""
        if self.preparation_started_at and self.preparation_completed_at:
            delta = self.preparation_completed_at - self.preparation_started_at
            return delta.total_seconds() / 60  # Return minutes
        return None

    @property
    def is_overdue(self):
        """Check if preparation is overdue based on mention reading schedule"""
        if self.mention_reading and self.preparation_status in ['not_prepared', 'in_progress']:
            scheduled_datetime = timezone.datetime.combine(
                self.mention_reading.scheduled_date,
                self.mention_reading.scheduled_time
            )
            scheduled_datetime = timezone.make_aware(scheduled_datetime)
            # Consider overdue if less than 30 minutes before scheduled time
            return timezone.now() > (scheduled_datetime - timezone.timedelta(minutes=30))
        return False


class ReadingSession(TimeStampedModel):
    """Model to track news reader sessions and performance"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reading_sessions')
    session_id = models.UUIDField(default=uuid.uuid4, unique=True)

    # Session details
    start_time = models.DateTimeField(auto_now_add=True)
    end_time = models.DateTimeField(null=True, blank=True)
    session_type = models.CharField(max_length=20, choices=[
        ('preparation', 'Preparation Session'),
        ('live_reading', 'Live Reading Session'),
        ('review', 'Review Session'),
    ], default='preparation')

    # Performance metrics
    mentions_prepared = models.PositiveIntegerField(default=0)
    mentions_read = models.PositiveIntegerField(default=0)
    total_reading_time = models.PositiveIntegerField(default=0, help_text="Total time in seconds")

    # Session notes
    notes = models.TextField(blank=True, help_text="Session notes and observations")

    class Meta:
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['user', 'start_time']),
            models.Index(fields=['session_type', 'start_time']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.get_session_type_display()} ({self.start_time.date()})"

    def end_session(self):
        """End the current session"""
        if not self.end_time:
            self.end_time = timezone.now()
            self.save()

    @property
    def duration(self):
        """Calculate session duration"""
        end = self.end_time or timezone.now()
        return end - self.start_time

    @property
    def is_active(self):
        """Check if session is currently active"""
        return self.end_time is None


class NewsReaderAssignment(TimeStampedModel):
    """Model to assign specific mentions to news readers"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='news_assignments')
    mention_reading = models.ForeignKey(MentionReading, on_delete=models.CASCADE,
                                      related_name='news_assignments')

    # Assignment details
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='assigned_readings')
    assigned_at = models.DateTimeField(auto_now_add=True)
    due_date = models.DateTimeField(help_text="When the reading should be prepared by")

    # Status
    is_active = models.BooleanField(default=True)
    is_completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Notes
    assignment_notes = models.TextField(blank=True, help_text="Special instructions for this assignment")

    class Meta:
        unique_together = ['user', 'mention_reading']
        ordering = ['due_date']
        indexes = [
            models.Index(fields=['user', 'is_active', 'due_date']),
            models.Index(fields=['mention_reading', 'is_active']),
        ]

    def __str__(self):
        return f"{self.user.username} assigned to {self.mention_reading}"

    def mark_completed(self):
        """Mark assignment as completed"""
        self.is_completed = True
        self.completed_at = timezone.now()
        self.save()

    @property
    def is_overdue(self):
        """Check if assignment is overdue"""
        return not self.is_completed and timezone.now() > self.due_date


class Article(TimeStampedModel):
    """Model for news articles that can be created and edited by news readers"""

    CATEGORY_CHOICES = [
        ('breaking_news', 'Breaking News'),
        ('weather', 'Weather'),
        ('politics', 'Politics'),
        ('business', 'Business'),
        ('health', 'Health'),
        ('sports', 'Sports'),
        ('entertainment', 'Entertainment'),
        ('technology', 'Technology'),
        ('local', 'Local News'),
        ('international', 'International'),
    ]

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('in_review', 'In Review'),
        ('approved', 'Approved'),
        ('scheduled', 'Scheduled'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
        ('breaking', 'Breaking'),
    ]

    VOICE_TONE_CHOICES = [
        ('neutral', 'Neutral'),
        ('formal', 'Formal'),
        ('urgent', 'Urgent'),
        ('conversational', 'Conversational'),
    ]

    # Basic article information
    title = models.CharField(max_length=300, help_text="Article headline")
    slug = models.SlugField(max_length=100, unique=True, help_text="URL-friendly identifier")
    content = models.TextField(help_text="Main article content with formatting")

    # Author and organization
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='authored_articles')
    byline = models.CharField(max_length=200, blank=True, help_text="Author byline (e.g., 'By John Smith')")

    # Metadata
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='local')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='normal')

    # Reading and timing information
    target_length_minutes = models.PositiveIntegerField(
        default=2,
        validators=[MinValueValidator(1), MaxValueValidator(30)],
        help_text="Target reading length in minutes"
    )
    target_length_seconds = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(59)],
        help_text="Additional seconds for target length"
    )
    estimated_reading_time = models.PositiveIntegerField(
        null=True, blank=True,
        help_text="Estimated reading time in seconds (auto-calculated)"
    )
    word_count = models.PositiveIntegerField(
        default=0,
        help_text="Word count (auto-calculated)"
    )

    # Voice and reading settings
    voice_tone = models.CharField(max_length=20, choices=VOICE_TONE_CHOICES, default='neutral')
    reading_speed = models.IntegerField(
        default=3,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Reading speed scale (1=slow, 5=fast)"
    )

    # Scheduling and air times
    air_times = models.JSONField(
        default=list,
        blank=True,
        help_text="List of scheduled air times (e.g., ['07:30', '09:30', '12:30'])"
    )
    scheduled_date = models.DateField(null=True, blank=True, help_text="Date when article should air")

    # Content formatting and guides
    pronunciation_guide = models.JSONField(
        default=dict,
        blank=True,
        help_text="Dictionary of word pronunciations (e.g., {'Newsom': 'NEW-sum'})"
    )
    time_markers = models.JSONField(
        default=list,
        blank=True,
        help_text="List of time markers in the content for pacing"
    )
    emphasis_words = models.JSONField(
        default=list,
        blank=True,
        help_text="List of words or phrases to emphasize during reading"
    )

    # Audio assets
    audio_assets = models.JSONField(
        default=list,
        blank=True,
        help_text="List of associated audio clips and their metadata"
    )

    # Publishing and workflow
    published_at = models.DateTimeField(null=True, blank=True)
    last_edited_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='last_edited_articles'
    )

    # Flags
    is_breaking = models.BooleanField(default=False, help_text="Mark as breaking news")
    is_urgent = models.BooleanField(default=False, help_text="Mark as urgent")
    auto_save_enabled = models.BooleanField(default=True, help_text="Enable auto-save for this article")

    # Version control
    version = models.PositiveIntegerField(default=1, help_text="Article version number")

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['author', 'status']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['scheduled_date', 'status']),
            models.Index(fields=['priority', 'created_at']),
            models.Index(fields=['slug']),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_status_display()})"

    def get_absolute_url(self):
        return reverse('news_reader:article_detail', kwargs={'pk': self.pk})

    def get_edit_url(self):
        return reverse('news_reader:article_edit', kwargs={'pk': self.pk})

    @property
    def target_length_total_seconds(self):
        """Get total target length in seconds"""
        return (self.target_length_minutes * 60) + self.target_length_seconds

    @property
    def is_overdue(self):
        """Check if article is overdue for publication"""
        if self.scheduled_date and self.status in ['draft', 'in_review']:
            scheduled_datetime = timezone.datetime.combine(
                self.scheduled_date,
                timezone.datetime.min.time()
            )
            scheduled_datetime = timezone.make_aware(scheduled_datetime)
            return timezone.now().date() > self.scheduled_date
        return False

    @property
    def reading_pace_status(self):
        """Compare estimated vs target reading time"""
        if not self.estimated_reading_time:
            return 'unknown'

        target_seconds = self.target_length_total_seconds
        difference = abs(self.estimated_reading_time - target_seconds)

        if difference <= 10:  # Within 10 seconds
            return 'on_target'
        elif self.estimated_reading_time > target_seconds:
            return 'too_long'
        else:
            return 'too_short'

    def calculate_reading_time(self, words_per_minute=150):
        """Calculate estimated reading time based on word count"""
        if self.word_count > 0:
            minutes = self.word_count / words_per_minute
            self.estimated_reading_time = int(minutes * 60)
            return self.estimated_reading_time
        return 0

    def update_word_count(self):
        """Update word count based on content"""
        if self.content:
            # Simple word count (could be enhanced with better text processing)
            import re
            words = re.findall(r'\b\w+\b', self.content)
            self.word_count = len(words)
            return self.word_count
        return 0

    def add_pronunciation(self, word, pronunciation):
        """Add a pronunciation guide entry"""
        if not isinstance(self.pronunciation_guide, dict):
            self.pronunciation_guide = {}
        self.pronunciation_guide[word] = pronunciation

    def add_air_time(self, time_str):
        """Add an air time to the schedule"""
        if not isinstance(self.air_times, list):
            self.air_times = []
        if time_str not in self.air_times:
            self.air_times.append(time_str)

    def remove_air_time(self, time_str):
        """Remove an air time from the schedule"""
        if isinstance(self.air_times, list) and time_str in self.air_times:
            self.air_times.remove(time_str)

    def publish(self):
        """Publish the article"""
        self.status = 'published'
        self.published_at = timezone.now()
        self.save()

    def archive(self):
        """Archive the article"""
        self.status = 'archived'
        self.save()

    def create_new_version(self):
        """Create a new version of the article"""
        self.version += 1
        self.save()

    def save(self, *args, **kwargs):
        """Override save to auto-calculate word count and reading time"""
        # Update word count and reading time before saving
        self.update_word_count()
        self.calculate_reading_time()

        # Auto-generate slug if not provided
        if not self.slug and self.title:
            from django.utils.text import slugify
            base_slug = slugify(self.title)[:90]  # Leave room for uniqueness suffix
            slug = base_slug
            counter = 1
            while Article.objects.filter(slug=slug).exclude(pk=self.pk).exists():
                slug = f"{base_slug}-{counter}"
                counter += 1
            self.slug = slug

        super().save(*args, **kwargs)


class ArticleRevision(TimeStampedModel):
    """Model to track article revision history"""
    article = models.ForeignKey(Article, on_delete=models.CASCADE, related_name='revisions')
    revision_number = models.PositiveIntegerField()

    # Snapshot of article data at this revision
    title = models.CharField(max_length=300)
    content = models.TextField()
    status = models.CharField(max_length=20)
    word_count = models.PositiveIntegerField(default=0)

    # Revision metadata
    revised_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='article_revisions')
    revision_notes = models.TextField(blank=True, help_text="Notes about what changed in this revision")

    # Change tracking
    changes_summary = models.JSONField(
        default=dict,
        blank=True,
        help_text="Summary of changes made in this revision"
    )

    class Meta:
        unique_together = ['article', 'revision_number']
        ordering = ['-revision_number']
        indexes = [
            models.Index(fields=['article', 'revision_number']),
            models.Index(fields=['revised_by', 'created_at']),
        ]

    def __str__(self):
        return f"{self.article.title} - Revision {self.revision_number}"


class ArticleAssignment(TimeStampedModel):
    """Model to assign articles to news readers for preparation and reading"""
    article = models.ForeignKey(Article, on_delete=models.CASCADE, related_name='assignments')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='article_assignments')

    # Assignment details
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_articles'
    )
    assigned_at = models.DateTimeField(auto_now_add=True)
    due_date = models.DateTimeField(help_text="When the article should be prepared by")

    # Status
    is_active = models.BooleanField(default=True)
    is_completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Reading preparation
    preparation_status = models.CharField(
        max_length=20,
        choices=ReadingNote.STATUS_CHOICES,
        default='not_prepared'
    )
    preparation_notes = models.TextField(blank=True, help_text="Notes about article preparation")

    # Assignment notes
    assignment_notes = models.TextField(blank=True, help_text="Special instructions for this assignment")

    class Meta:
        unique_together = ['article', 'user']
        ordering = ['due_date']
        indexes = [
            models.Index(fields=['user', 'is_active', 'due_date']),
            models.Index(fields=['article', 'is_active']),
            models.Index(fields=['preparation_status']),
        ]

    def __str__(self):
        return f"{self.user.username} assigned to {self.article.title}"

    def mark_completed(self):
        """Mark assignment as completed"""
        self.is_completed = True
        self.completed_at = timezone.now()
        self.preparation_status = 'completed'
        self.save()

    @property
    def is_overdue(self):
        """Check if assignment is overdue"""
        return not self.is_completed and timezone.now() > self.due_date
