<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ article.title }} - Mobile View</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }
        
        .highlight-word {
            background-color: rgba(59, 130, 246, 0.2);
            padding: 1px 3px;
            border-radius: 3px;
        }
        
        .time-marker {
            background-color: rgba(34, 197, 94, 0.2);
            color: #059669;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        .reading-controls {
            position: sticky;
            bottom: 0;
            background: white;
            border-top: 1px solid #e5e7eb;
            box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .pronunciation-item {
            background: #f3f4f6;
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <button onclick="window.history.back()" class="p-2 -ml-2 text-gray-600">
                    <i class="fa-solid fa-arrow-left text-lg"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-900 truncate mx-3">Mobile View</h1>
                <button onclick="shareArticle()" class="p-2 -mr-2 text-gray-600">
                    <i class="fa-solid fa-share text-lg"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Article Content -->
    <main class="pb-20">
        <!-- Article Header -->
        <div class="bg-white px-4 py-6 border-b border-gray-200">
            <div class="mb-4">
                {% if article.is_breaking or article.is_urgent %}
                    <div class="flex items-center space-x-2 mb-3">
                        {% if article.is_breaking %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i class="fa-solid fa-exclamation-triangle mr-1"></i>
                                BREAKING
                            </span>
                        {% endif %}
                        {% if article.is_urgent %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                <i class="fa-solid fa-clock mr-1"></i>
                                URGENT
                            </span>
                        {% endif %}
                    </div>
                {% endif %}
                
                <h1 class="text-2xl font-bold text-gray-900 leading-tight">{{ article.title }}</h1>
            </div>
            
            <div class="flex items-center text-sm text-gray-600 mb-4">
                <span>{{ article.byline }}</span>
                <span class="mx-2">•</span>
                <span>{{ article.created_at|date:"M d, Y" }}</span>
            </div>
            
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">
                        <i class="fa-solid fa-font mr-1"></i>
                        {{ article.word_count }} words
                    </span>
                    <span class="text-gray-600">
                        <i class="fa-solid fa-clock mr-1"></i>
                        {{ article.target_length_minutes }}:{{ article.target_length_seconds|stringformat:"02d" }}
                    </span>
                </div>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                    {% if article.status == 'draft' %}bg-gray-100 text-gray-800
                    {% elif article.status == 'in_review' %}bg-yellow-100 text-yellow-800
                    {% elif article.status == 'approved' %}bg-green-100 text-green-800
                    {% elif article.status == 'published' %}bg-blue-100 text-blue-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ article.get_status_display }}
                </span>
            </div>
        </div>

        <!-- Article Body -->
        <div class="bg-white px-4 py-6">
            <div class="prose prose-lg max-w-none text-gray-900" id="article-content">
                {{ article.content|linebreaks }}
            </div>
        </div>

        <!-- Air Times -->
        {% if article.air_times %}
            <div class="bg-white px-4 py-6 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-3">
                    <i class="fa-solid fa-broadcast-tower mr-2 text-blue-600"></i>
                    Air Times
                </h3>
                <div class="flex flex-wrap gap-2">
                    {% for time in article.air_times %}
                        <span class="inline-flex items-center px-3 py-2 rounded-lg bg-blue-100 text-blue-800 font-medium">
                            <i class="fa-solid fa-clock mr-2"></i>
                            {{ time }}
                        </span>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Pronunciation Guide -->
        {% if article.pronunciation_guide %}
            <div class="bg-white px-4 py-6 border-t border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fa-solid fa-volume-high mr-2 text-green-600"></i>
                    Pronunciation Guide
                </h3>
                <div class="space-y-3">
                    {% for word, pronunciation in article.pronunciation_guide.items %}
                        <div class="pronunciation-item p-3 rounded-lg">
                            <div class="flex items-center justify-between">
                                <span class="font-semibold text-gray-900">{{ word }}</span>
                                <button onclick="speakWord('{{ word }}')" class="p-1 text-blue-600 hover:text-blue-800">
                                    <i class="fa-solid fa-volume-high"></i>
                                </button>
                            </div>
                            <div class="text-gray-700 mt-1">{{ pronunciation }}</div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}

        <!-- Article Metadata -->
        <div class="bg-white px-4 py-6 border-t border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fa-solid fa-info-circle mr-2 text-gray-600"></i>
                Article Details
            </h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600">Category</span>
                    <span class="font-medium text-gray-900">{{ article.get_category_display }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Priority</span>
                    <span class="font-medium text-gray-900">{{ article.get_priority_display }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Voice Tone</span>
                    <span class="font-medium text-gray-900">{{ article.get_voice_tone_display }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Reading Speed</span>
                    <span class="font-medium text-gray-900">{{ article.reading_speed }}/5</span>
                </div>
                {% if article.scheduled_date %}
                    <div class="flex justify-between">
                        <span class="text-gray-600">Scheduled</span>
                        <span class="font-medium text-gray-900">{{ article.scheduled_date|date:"F d, Y" }}</span>
                    </div>
                {% endif %}
            </div>
        </div>
    </main>

    <!-- Reading Controls -->
    <div class="reading-controls">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                    <button onclick="toggleReading()" class="flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full" id="reading-btn">
                        <i class="fa-solid fa-play"></i>
                    </button>
                    <div class="text-sm">
                        <div class="font-medium text-gray-900">Reading Progress</div>
                        <div class="text-gray-600" id="reading-time">0:00 / {{ article.target_length_minutes }}:{{ article.target_length_seconds|stringformat:"02d" }}</div>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="adjustFontSize(-1)" class="p-2 text-gray-600 bg-gray-100 rounded-lg">
                        <i class="fa-solid fa-minus"></i>
                    </button>
                    <span class="text-sm text-gray-600" id="font-size">100%</span>
                    <button onclick="adjustFontSize(1)" class="p-2 text-gray-600 bg-gray-100 rounded-lg">
                        <i class="fa-solid fa-plus"></i>
                    </button>
                </div>
            </div>
            
            <!-- Reading Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%" id="progress-bar"></div>
            </div>
        </div>
    </div>

    <script>
        let isReading = false;
        let readingStartTime = null;
        let currentFontSize = 100;
        
        function toggleReading() {
            const btn = document.getElementById('reading-btn');
            const timeDisplay = document.getElementById('reading-time');
            
            if (isReading) {
                btn.innerHTML = '<i class="fa-solid fa-play"></i>';
                isReading = false;
                clearInterval(readingTimer);
            } else {
                btn.innerHTML = '<i class="fa-solid fa-pause"></i>';
                isReading = true;
                readingStartTime = Date.now();
                startReadingTimer();
            }
        }
        
        let readingTimer;
        function startReadingTimer() {
            readingTimer = setInterval(() => {
                const elapsed = Math.floor((Date.now() - readingStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                const targetMinutes = {{ article.target_length_minutes }};
                const targetSeconds = {{ article.target_length_seconds }};
                
                document.getElementById('reading-time').textContent = 
                    `${minutes}:${seconds.toString().padStart(2, '0')} / ${targetMinutes}:${targetSeconds.toString().padStart(2, '0')}`;
                
                // Update progress bar
                const totalTarget = (targetMinutes * 60) + targetSeconds;
                const progress = Math.min((elapsed / totalTarget) * 100, 100);
                document.getElementById('progress-bar').style.width = progress + '%';
            }, 1000);
        }
        
        function adjustFontSize(delta) {
            currentFontSize += delta * 10;
            currentFontSize = Math.max(70, Math.min(150, currentFontSize));
            
            document.getElementById('article-content').style.fontSize = currentFontSize + '%';
            document.getElementById('font-size').textContent = currentFontSize + '%';
        }
        
        function speakWord(word) {
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(word);
                utterance.rate = 0.8;
                speechSynthesis.speak(utterance);
            }
        }
        
        function shareArticle() {
            if (navigator.share) {
                navigator.share({
                    title: '{{ article.title }}',
                    text: '{{ article.title }} - {{ article.byline }}',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Link copied to clipboard!');
                });
            }
        }
        
        // Prevent zoom on double tap
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
