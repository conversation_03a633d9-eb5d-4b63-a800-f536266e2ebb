{% extends 'base.html' %}
{% load static %}

{% block title %}
  Session Details - {{ session.get_session_type_display }}
{% endblock %}

{% block content %}
  <div class="max-w-6xl mx-auto bg-neutral-50 min-h-screen">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 flex items-center">
            {% if session.session_type == 'preparation' %}
              <i class="fa-solid fa-book-open text-blue-600 mr-3"></i>
            {% elif session.session_type == 'live_reading' %}
              <i class="fa-solid fa-broadcast-tower text-red-600 mr-3"></i>
            {% elif session.session_type == 'review' %}
              <i class="fa-solid fa-search text-green-600 mr-3"></i>
            {% else %}
              <i class="fa-solid fa-clock text-gray-600 mr-3"></i>
            {% endif %}
            {{ session.get_session_type_display }} Session
          </h1>
          <p class="text-gray-600">
            {% if session.is_active %}
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                <i class="fa-solid fa-circle text-green-500 mr-1"></i>
                Active
              </span>
            {% else %}
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                <i class="fa-solid fa-circle text-gray-500 mr-1"></i>
                Completed
              </span>
            {% endif %}Started {{ session.start_time|date:'M d, Y \a\t H:i' }}
            {% if session.end_time %}
              • Ended {{ session.end_time|date:'M d, Y \a\t H:i' }}
            {% endif %}
          </p>
        </div>
        <div class="flex items-center space-x-3">
          {% if session.is_active %}
            <form method="post" action="{% url 'news_reader:end_session' session.id %}" class="inline">
              {% csrf_token %}
              <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                <i class="fa-solid fa-stop mr-2"></i>
                End Session
              </button>
            </form>
          {% endif %}
          <a href="{% url 'news_reader:session_list' %}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">Back to Sessions</a>
        </div>
      </div>
    </div>

    <!-- Session Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- Session Info -->
      <div class="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Session Information</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 class="text-sm font-medium text-gray-500 mb-2">Duration</h4>
              <p class="text-lg font-semibold text-gray-900">
                {% if session.duration %}
                  {{ session.duration }}
                {% elif session.is_active %}
                  <span id="live-duration" class="text-green-600">
                    <i class="fa-solid fa-clock mr-1"></i>
                    Calculating...
                  </span>
                {% else %}
                  Unknown
                {% endif %}
              </p>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-500 mb-2">Status</h4>
              <p class="text-lg font-semibold">
                {% if session.is_active %}
                  <span class="text-green-600">Active</span>
                {% else %}
                  <span class="text-gray-600">Completed</span>
                {% endif %}
              </p>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-500 mb-2">Start Time</h4>
              <p class="text-lg font-semibold text-gray-900">{{ session.start_time|date:'H:i' }}</p>
              <p class="text-sm text-gray-500">{{ session.start_time|date:'M d, Y' }}</p>
            </div>
            {% if session.end_time %}
              <div>
                <h4 class="text-sm font-medium text-gray-500 mb-2">End Time</h4>
                <p class="text-lg font-semibold text-gray-900">{{ session.end_time|date:'H:i' }}</p>
                <p class="text-sm text-gray-500">{{ session.end_time|date:'M d, Y' }}</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Quick Stats</h3>
        </div>
        <div class="p-6 space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">Session Type</span>
            <span class="text-sm font-medium text-gray-900">{{ session.get_session_type_display }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">User</span>
            <span class="text-sm font-medium text-gray-900">{{ session.user.get_full_name|default:session.user.username }}</span>
          </div>
          {% if session.notes %}
            <div class="pt-2 border-t border-gray-200">
              <span class="text-sm text-gray-600">Notes</span>
              <p class="text-sm text-gray-900 mt-1">{{ session.notes|truncatechars:100 }}</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Session Notes -->
    {% if session.notes %}
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Session Notes</h3>
        </div>
        <div class="p-6">
          <div class="prose max-w-none">
            <p class="text-gray-900 whitespace-pre-wrap">{{ session.notes }}</p>
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Session Activities -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Session Activities</h3>
        <p class="text-sm text-gray-500 mt-1">Activities and notes created during this session</p>
      </div>
      <div class="p-6">
        {% if session_notes %}
          <div class="space-y-4">
            {% for note in session_notes %}
              <div class="border border-gray-200 rounded-lg p-4">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <h4 class="font-medium text-gray-900"><a href="{% url 'news_reader:note_detail' note.id %}" class="text-primary-600 hover:text-primary-700">{{ note.title|default:note.mention.title }}</a></h4>
                    <p class="text-sm text-gray-600 mt-1">{{ note.mention.client.name }} • {{ note.mention.duration_seconds }}s</p>
                    {% if note.content %}
                      <p class="text-sm text-gray-700 mt-2">{{ note.content|truncatechars:150 }}</p>
                    {% endif %}
                  </div>
                  <div class="ml-4 text-right">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if note.preparation_status == 'completed' %}
                        bg-green-100 text-green-800

                      {% elif note.preparation_status == 'prepared' %}
                        bg-blue-100 text-blue-800

                      {% elif note.preparation_status == 'in_progress' %}
                        bg-yellow-100 text-yellow-800

                      {% else %}
                        bg-gray-100 text-gray-800
                      {% endif %}">
                      {{ note.get_preparation_status_display }}
                    </span>
                    <p class="text-xs text-gray-500 mt-1">{{ note.updated_at|date:'H:i' }}</p>
                  </div>
                </div>
              </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-8">
            <i class="fa-solid fa-sticky-note text-gray-400 text-3xl mb-3"></i>
            <h4 class="text-lg font-medium text-gray-900 mb-2">No activities yet</h4>
            <p class="text-gray-500 mb-4">No notes or activities were created during this session.</p>
            {% if session.is_active %}
              <a href="{% url 'news_reader:note_list' %}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">Create Note</a>
            {% endif %}
          </div>
        {% endif %}
      </div>
    </div>

    <!-- Session Actions -->
    {% if session.is_active %}
      <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">Session Actions</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{% url 'news_reader:live_reading_interface' %}" class="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
              <i class="fa-solid fa-broadcast-tower text-red-600 text-xl mr-3"></i>
              <div>
                <h4 class="font-medium text-red-900">Go Live</h4>
                <p class="text-sm text-red-700">Switch to live reading</p>
              </div>
            </a>

            <a href="{% url 'news_reader:note_list' %}" class="flex items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
              <i class="fa-solid fa-sticky-note text-blue-600 text-xl mr-3"></i>
              <div>
                <h4 class="font-medium text-blue-900">Add Notes</h4>
                <p class="text-sm text-blue-700">Create preparation notes</p>
              </div>
            </a>

            <a href="{% url 'news_reader:assignment_list' %}" class="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
              <i class="fa-solid fa-tasks text-green-600 text-xl mr-3"></i>
              <div>
                <h4 class="font-medium text-green-900">Assignments</h4>
                <p class="text-sm text-green-700">View reading tasks</p>
              </div>
            </a>
          </div>
        </div>
      </div>
    {% endif %}
  </div>

  <script>
// Update live duration for active sessions
{% if session.is_active %}
function updateLiveDuration() {
    const startTime = new Date('{{ session.start_time|date:"c" }}');
    const now = new Date();
    const diff = now - startTime;
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    let duration = '';
    if (hours > 0) {
        duration = `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
        duration = `${minutes}m ${seconds}s`;
    } else {
        duration = `${seconds}s`;
    }
    
    const element = document.getElementById('live-duration');
    if (element) {
        element.innerHTML = `<i class="fa-solid fa-clock mr-1"></i>${duration}`;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    updateLiveDuration();
    setInterval(updateLiveDuration, 1000); // Update every second
});
{% endif %}
</script>
{% endblock %}
