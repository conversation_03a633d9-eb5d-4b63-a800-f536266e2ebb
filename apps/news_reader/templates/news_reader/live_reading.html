{% extends 'base.html' %}
{% load static %}

{% block title %}Live Reading Interface{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Live Reading Interface</h1>
                <p class="text-gray-600">Manage your reading queue and go live with articles.</p>
            </div>
            <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-500">{{ current_organization.name }}</span>
                <div class="flex items-center space-x-2">
                    <a href="{% url 'news_reader:dashboard' %}" 
                       class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                        <i class="fa-solid fa-arrow-left mr-2"></i>
                        Dashboard
                    </a>
                    {% if reading_queue %}
                        <button onclick="startLiveSession()" 
                               class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                            <i class="fa-solid fa-microphone mr-2"></i>
                            Go Live
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Live Status Bar -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-3 h-3 bg-gray-400 rounded-full mr-3" id="live-indicator"></div>
                <span class="text-lg font-semibold text-gray-700" id="live-status">Ready to Broadcast</span>
            </div>
            <div class="flex items-center space-x-6">
                <div class="text-right">
                    <div class="text-sm text-gray-500">Session Time</div>
                    <div class="text-xl font-mono font-bold text-gray-900" id="session-timer">00:00</div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Current Time</div>
                    <div class="text-xl font-mono font-bold text-gray-900" id="current-time"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reading Queue -->
    {% if reading_queue %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fa-solid fa-list-ol mr-2 text-blue-600"></i>
                    Reading Queue ({{ total_articles }} articles)
                </h3>
                <div class="flex items-center space-x-2">
                    <button onclick="sortByPriority()" class="text-sm text-blue-600 hover:text-blue-700">
                        Sort by Priority
                    </button>
                    <span class="text-gray-300">|</span>
                    <button onclick="sortByTime()" class="text-sm text-blue-600 hover:text-blue-700">
                        Sort by Time
                    </button>
                </div>
            </div>
            
            <div class="divide-y divide-gray-200" id="reading-queue">
                {% for item in reading_queue %}
                    <div class="p-6 hover:bg-gray-50 transition-colors reading-queue-item" data-article-id="{{ item.article.pk }}">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <h4 class="text-lg font-medium text-gray-900 mr-3">{{ item.article.title }}</h4>
                                    
                                    <!-- Priority Badge -->
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mr-2
                                        {% if item.priority == 'breaking' %}bg-red-200 text-red-900
                                        {% elif item.priority == 'urgent' %}bg-orange-100 text-orange-800
                                        {% elif item.priority == 'high' %}bg-yellow-100 text-yellow-800
                                        {% elif item.priority == 'normal' %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ item.article.get_priority_display }}
                                    </span>
                                    
                                    <!-- Category Badge -->
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ item.article.get_category_display }}
                                    </span>
                                    
                                    {% if item.article.is_breaking %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 ml-2">
                                            BREAKING
                                        </span>
                                    {% endif %}
                                    
                                    {% if item.article.is_urgent %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800 ml-2">
                                            URGENT
                                        </span>
                                    {% endif %}
                                </div>
                                
                                <div class="text-sm text-gray-600 mb-3">
                                    <span class="mr-4">{{ item.article.byline }}</span>
                                    <span class="mr-4">{{ item.article.word_count }} words</span>
                                    <span class="mr-4">{{ item.article.target_length_minutes }}:{{ item.article.target_length_seconds|stringformat:"02d" }} target</span>
                                    {% if item.due_time %}
                                        <span class="text-orange-600">Due: {{ item.due_time|date:"H:i" }}</span>
                                    {% endif %}
                                </div>
                                
                                {% if item.article.air_times %}
                                    <div class="flex items-center mb-3">
                                        <i class="fa-solid fa-broadcast-tower text-blue-500 mr-2"></i>
                                        <span class="text-sm text-gray-600">
                                            Air times: {% for time in item.article.air_times %}{{ time }}{% if not forloop.last %}, {% endif %}{% endfor %}
                                        </span>
                                    </div>
                                {% endif %}
                                
                                <!-- Article Preview -->
                                <div class="text-sm text-gray-700 line-clamp-2">
                                    {{ item.article.content|truncatewords:30 }}
                                </div>
                            </div>
                            
                            <div class="ml-6 flex flex-col items-end space-y-2">
                                <!-- Action Buttons -->
                                <div class="flex items-center space-x-2">
                                    <a href="{% url 'news_reader:article_detail' item.article.pk %}" 
                                       class="text-gray-600 hover:text-gray-800 p-2" title="View Details">
                                        <i class="fa-solid fa-eye"></i>
                                    </a>
                                    <a href="{% url 'news_reader:article_editor' item.article.pk %}" 
                                       class="text-blue-600 hover:text-blue-800 p-2" title="Quick Edit">
                                        <i class="fa-solid fa-edit"></i>
                                    </a>
                                    <button onclick="previewArticle({{ item.article.pk }})" 
                                            class="text-green-600 hover:text-green-800 p-2" title="Preview">
                                        <i class="fa-solid fa-search"></i>
                                    </button>
                                </div>
                                
                                <!-- Live Reading Button -->
                                <button onclick="startLiveReading({{ item.article.pk }})" 
                                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                                    <i class="fa-solid fa-microphone mr-2"></i>
                                    Read Live
                                </button>
                                
                                <!-- Assignment Status -->
                                {% if item.assignment %}
                                    <div class="text-xs text-gray-500">
                                        {% if item.assignment.is_completed %}
                                            <span class="text-green-600">✓ Completed</span>
                                        {% elif item.assignment.is_overdue %}
                                            <span class="text-red-600">⚠ Overdue</span>
                                        {% else %}
                                            <span class="text-yellow-600">⏳ Pending</span>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 text-center py-12">
            <i class="fa-solid fa-microphone-slash text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Articles in Reading Queue</h3>
            <p class="text-gray-500 mb-4">You don't have any articles assigned or scheduled for reading.</p>
            <div class="flex items-center justify-center space-x-3">
                <a href="{% url 'news_reader:article_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fa-solid fa-newspaper mr-2"></i>
                    Browse Articles
                </a>
                <a href="{% url 'news_reader:article_create' %}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fa-solid fa-plus mr-2"></i>
                    Create Article
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- Preview Modal -->
<div id="preview-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Article Preview</h3>
                <button onclick="closePreview()" class="text-gray-400 hover:text-gray-600">
                    <i class="fa-solid fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto max-h-[70vh]" id="preview-content">
                <!-- Preview content will be loaded here -->
            </div>
            <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
                <button onclick="closePreview()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium">
                    Close
                </button>
                <button onclick="startLiveReadingFromPreview()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fa-solid fa-microphone mr-2"></i>
                    Read Live
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let sessionStartTime = null;
let sessionTimer = null;
let currentPreviewArticleId = null;

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateClock();
    setInterval(updateClock, 1000);
});

function updateClock() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', { 
        hour12: true,
        hour: 'numeric',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('current-time').textContent = timeString;
}

function startLiveSession() {
    const indicator = document.getElementById('live-indicator');
    const status = document.getElementById('live-status');
    
    indicator.className = 'w-3 h-3 bg-red-500 rounded-full mr-3 animate-pulse';
    status.textContent = 'LIVE ON AIR';
    status.className = 'text-lg font-semibold text-red-600';
    
    sessionStartTime = Date.now();
    sessionTimer = setInterval(updateSessionTimer, 1000);
}

function updateSessionTimer() {
    if (!sessionStartTime) return;
    
    const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    
    document.getElementById('session-timer').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

function startLiveReading(articleId) {
    window.open(`/news-reader/live-reading/article/${articleId}/`, '_blank');
}

function previewArticle(articleId) {
    currentPreviewArticleId = articleId;
    
    // Load article content via AJAX (simplified for now)
    fetch(`/news-reader/articles/${articleId}/`)
        .then(response => response.text())
        .then(html => {
            // Extract article content (this is simplified - in production you'd have a proper API)
            document.getElementById('preview-content').innerHTML = `
                <div class="prose max-w-none">
                    <p class="text-gray-600">Loading article preview...</p>
                    <p class="text-sm text-gray-500">Click "Read Live" to open the full teleprompter interface.</p>
                </div>
            `;
            document.getElementById('preview-modal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error loading preview:', error);
        });
}

function closePreview() {
    document.getElementById('preview-modal').classList.add('hidden');
    currentPreviewArticleId = null;
}

function startLiveReadingFromPreview() {
    if (currentPreviewArticleId) {
        startLiveReading(currentPreviewArticleId);
        closePreview();
    }
}

function sortByPriority() {
    // Implement sorting logic
    console.log('Sorting by priority');
}

function sortByTime() {
    // Implement sorting logic
    console.log('Sorting by time');
}
</script>
{% endblock %}
