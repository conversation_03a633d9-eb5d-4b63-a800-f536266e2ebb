{% extends 'base.html' %}
{% load static %}

{% block title %}Create Article{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{% url 'news_reader:dashboard' %}" class="hover:text-primary-600">Dashboard</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <a href="{% url 'news_reader:article_list' %}" class="hover:text-primary-600">Articles</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <span class="text-gray-900">Create Article</span>
        </nav>
        <h1 class="text-2xl font-bold text-gray-900">Create New Article</h1>
        <p class="text-gray-600">Create a new news article for reading.</p>
    </div>

    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
                    <input type="text" name="title" id="title" required
                           value="{{ form_data.title|default:'' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                           placeholder="Enter article title">
                </div>
                
                <div>
                    <label for="byline" class="block text-sm font-medium text-gray-700 mb-1">Byline</label>
                    <input type="text" name="byline" id="byline"
                           value="{{ form_data.byline|default:'' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                           placeholder="By Author Name">
                </div>
                
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select name="category" id="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        {% for value, label in category_choices %}
                            <option value="{{ value }}" {% if form_data.category == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                    <select name="priority" id="priority" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        {% for value, label in priority_choices %}
                            <option value="{{ value }}" {% if form_data.priority == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="flex items-center space-x-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_breaking" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" {% if form_data.is_breaking %}checked{% endif %}>
                        <span class="ml-2 text-sm text-gray-700">Breaking News</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" name="is_urgent" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" {% if form_data.is_urgent %}checked{% endif %}>
                        <span class="ml-2 text-sm text-gray-700">Urgent</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Content</h3>
            
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Article Content *</label>
                <textarea name="content" id="content" rows="15" required
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          placeholder="Enter article content...">{{ form_data.content|default:'' }}</textarea>
                <div class="mt-2 flex items-center justify-between text-sm text-gray-500">
                    <span>Word count: <span id="word-count">0</span></span>
                    <span>Estimated reading time: <span id="reading-time">0:00</span></span>
                </div>
            </div>
        </div>

        <!-- Reading Settings -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Reading Settings</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Target Length</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" name="target_length_minutes" min="1" max="30" 
                               value="{{ form_data.target_length_minutes|default:'2' }}"
                               class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <span class="text-sm text-gray-500">min</span>
                        <input type="number" name="target_length_seconds" min="0" max="59" 
                               value="{{ form_data.target_length_seconds|default:'0' }}"
                               class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        <span class="text-sm text-gray-500">sec</span>
                    </div>
                </div>
                
                <div>
                    <label for="voice_tone" class="block text-sm font-medium text-gray-700 mb-1">Voice Tone</label>
                    <select name="voice_tone" id="voice_tone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                        {% for value, label in voice_tone_choices %}
                            <option value="{{ value }}" {% if form_data.voice_tone == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="reading_speed" class="block text-sm font-medium text-gray-700 mb-1">Reading Speed</label>
                    <div class="flex items-center space-x-2">
                        <span class="text-xs text-gray-600">Slow</span>
                        <input type="range" name="reading_speed" id="reading_speed" min="1" max="5" 
                               value="{{ form_data.reading_speed|default:'3' }}"
                               class="flex-1">
                        <span class="text-xs text-gray-600">Fast</span>
                    </div>
                    <div class="text-center mt-1">
                        <span class="text-sm text-gray-600" id="speed-value">3</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scheduling -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Scheduling</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="air_times" class="block text-sm font-medium text-gray-700 mb-1">Air Times</label>
                    <input type="text" name="air_times" id="air_times"
                           value="{{ form_data.air_times|default:'' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                           placeholder="e.g., 07:30, 09:30, 12:30">
                    <p class="mt-1 text-sm text-gray-500">Enter air times separated by commas</p>
                </div>
                
                <div>
                    <label for="scheduled_date" class="block text-sm font-medium text-gray-700 mb-1">Scheduled Date</label>
                    <input type="date" name="scheduled_date" id="scheduled_date"
                           value="{{ form_data.scheduled_date|default:'' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between">
            <a href="{% url 'news_reader:article_list' %}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-2 rounded-lg text-sm font-medium">
                Cancel
            </a>
            <div class="flex items-center space-x-3">
                <button type="submit" name="status" value="draft" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg text-sm font-medium">
                    Save as Draft
                </button>
                <button type="submit" name="status" value="in_review" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg text-sm font-medium">
                    Save & Submit for Review
                </button>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const contentTextarea = document.getElementById('content');
    const wordCountSpan = document.getElementById('word-count');
    const readingTimeSpan = document.getElementById('reading-time');
    const readingSpeedSlider = document.getElementById('reading_speed');
    const speedValueSpan = document.getElementById('speed-value');
    
    // Update word count and reading time
    function updateStats() {
        const content = contentTextarea.value;
        const words = content.trim().split(/\s+/).filter(word => word.length > 0);
        const wordCount = words.length;
        
        // Calculate reading time (average 150 words per minute)
        const minutes = Math.floor(wordCount / 150);
        const seconds = Math.floor((wordCount % 150) / 150 * 60);
        
        wordCountSpan.textContent = wordCount;
        readingTimeSpan.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    
    // Update reading speed display
    function updateSpeedValue() {
        speedValueSpan.textContent = readingSpeedSlider.value;
    }
    
    // Event listeners
    contentTextarea.addEventListener('input', updateStats);
    readingSpeedSlider.addEventListener('input', updateSpeedValue);
    
    // Initial updates
    updateStats();
    updateSpeedValue();
});
</script>
{% endblock %}
