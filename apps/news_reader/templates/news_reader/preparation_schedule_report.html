{% extends 'base.html' %}
{% load static %}

{% block title %}Preparation Schedule Report{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Preparation Schedule Report</h1>
                <p class="text-gray-600">Your reading assignments and preparation schedule.</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'news_reader:preparation_schedule_report' %}?format=pdf" 
                   class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center" 
                   target="_blank">
                    <i class="fa-solid fa-file-pdf mr-2"></i>
                    Download PDF
                </a>
                <a href="{% url 'news_reader:dashboard' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Report Info -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <span class="text-sm font-medium text-gray-500">Report Date:</span>
                <p class="text-gray-900">{{ report_date|date:"M d, Y" }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Total Assignments:</span>
                <p class="text-gray-900">{{ assignments.count }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Status:</span>
                <p class="text-gray-900">
                    {% with completed=assignments|length %}
                        {% with overdue=0 %}
                            {{ completed }} Active
                        {% endwith %}
                    {% endwith %}
                </p>
            </div>
        </div>
    </div>

    <!-- Assignments Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Reading Assignments</h3>
        </div>
        
        {% if assignments %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mention</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scheduled</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for assignment in assignments %}
                            <tr class="hover:bg-gray-50 {% if assignment.is_overdue %}bg-red-50{% endif %}">
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ assignment.mention_reading.mention.title }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ assignment.mention_reading.mention.duration_seconds }}s
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ assignment.mention_reading.mention.client.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ assignment.mention_reading.show.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        {{ assignment.mention_reading.scheduled_date|date:"M d, Y" }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ assignment.mention_reading.scheduled_time|time:"H:i" }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm {% if assignment.is_overdue %}text-red-600 font-medium{% else %}text-gray-900{% endif %}">
                                        {{ assignment.due_date|date:"M d, Y" }}
                                    </div>
                                    <div class="text-sm {% if assignment.is_overdue %}text-red-500{% else %}text-gray-500{% endif %}">
                                        {{ assignment.due_date|time:"H:i" }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if assignment.is_completed %}bg-green-100 text-green-800
                                        {% elif assignment.is_overdue %}bg-red-100 text-red-800
                                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                        {% if assignment.is_completed %}
                                            Completed
                                        {% elif assignment.is_overdue %}
                                            Overdue
                                        {% else %}
                                            Pending
                                        {% endif %}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if assignment.mention_reading.mention.priority == 4 %}bg-red-100 text-red-800
                                        {% elif assignment.mention_reading.mention.priority == 3 %}bg-orange-100 text-orange-800
                                        {% elif assignment.mention_reading.mention.priority == 2 %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ assignment.mention_reading.mention.get_priority_display }}
                                    </span>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-12">
                <i class="fa-solid fa-calendar-check text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No assignments found</h3>
                <p class="text-gray-500">You don't have any active reading assignments.</p>
            </div>
        {% endif %}
    </div>

    <!-- Assignment Details -->
    {% if assignments %}
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Assignment Details</h3>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    {% for assignment in assignments|slice:":10" %}
                        <div class="border-l-4 {% if assignment.is_overdue %}border-red-500 bg-red-50{% elif assignment.is_completed %}border-green-500 bg-green-50{% else %}border-blue-500 bg-blue-50{% endif %} p-4 rounded-r-lg">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-2">{{ assignment.mention_reading.mention.title }}</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                        <div>
                                            <span class="font-medium text-gray-600">Client:</span>
                                            <p class="text-gray-900">{{ assignment.mention_reading.mention.client.name }}</p>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-600">Show:</span>
                                            <p class="text-gray-900">{{ assignment.mention_reading.show.name }}</p>
                                        </div>
                                        <div>
                                            <span class="font-medium text-gray-600">Duration:</span>
                                            <p class="text-gray-900">{{ assignment.mention_reading.mention.duration_seconds }} seconds</p>
                                        </div>
                                    </div>
                                    
                                    {% if assignment.assignment_notes %}
                                        <div class="mt-3">
                                            <span class="font-medium text-gray-600">Instructions:</span>
                                            <p class="text-gray-900 mt-1">{{ assignment.assignment_notes }}</p>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="mt-3 bg-white p-3 rounded border">
                                        <span class="font-medium text-gray-600">Content:</span>
                                        <p class="text-gray-900 mt-1">{{ assignment.mention_reading.mention.content }}</p>
                                    </div>
                                </div>
                                <div class="ml-4 text-right">
                                    <div class="text-sm text-gray-500">
                                        Scheduled: {{ assignment.mention_reading.scheduled_date|date:"M d" }} at {{ assignment.mention_reading.scheduled_time|time:"H:i" }}
                                    </div>
                                    <div class="text-sm {% if assignment.is_overdue %}text-red-600{% else %}text-gray-500{% endif %}">
                                        Due: {{ assignment.due_date|date:"M d" }} at {{ assignment.due_date|time:"H:i" }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                    
                    {% if assignments.count > 10 %}
                        <div class="text-center py-4">
                            <p class="text-gray-500">Showing first 10 assignments. <a href="{% url 'news_reader:assignment_list' %}" class="text-primary-600 hover:text-primary-700">View all assignments</a></p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}
</div>

<script>
// Print functionality
function printReport() {
    window.print();
}

// Add print button if needed
document.addEventListener('DOMContentLoaded', function() {
    // You can add additional print styling or functionality here
});
</script>

<style media="print">
    /* Print-specific styles */
    .no-print {
        display: none !important;
    }
    
    body {
        font-size: 12px;
    }
    
    .bg-red-50, .bg-green-50, .bg-blue-50 {
        background-color: #f9f9f9 !important;
    }
</style>
{% endblock %}
