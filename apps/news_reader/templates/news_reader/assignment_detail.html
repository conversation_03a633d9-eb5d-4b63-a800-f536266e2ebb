{% extends 'base.html' %}
{% load static %}

{% block title %}Assignment: {{ assignment.mention_reading.mention.title }}{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Reading Assignment</h1>
                <p class="text-gray-600">{{ assignment.mention_reading.mention.title }}</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'news_reader:assignment_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Assignments
                </a>
                {% if not assignment.is_completed %}
                    <a href="{% url 'news_reader:complete_assignment' assignment.pk %}" 
                       class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                        Mark Complete
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Status Alert -->
    {% if assignment.is_overdue %}
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fa-solid fa-exclamation-triangle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Assignment Overdue</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>This assignment was due on {{ assignment.due_date|date:"M d, Y" }} at {{ assignment.due_date|time:"H:i" }}.</p>
                    </div>
                </div>
            </div>
        </div>
    {% elif assignment.is_completed %}
        <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fa-solid fa-check-circle text-green-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">Assignment Completed</h3>
                    <div class="mt-2 text-sm text-green-700">
                        <p>Completed on {{ assignment.completed_at|date:"M d, Y" }} at {{ assignment.completed_at|time:"H:i" }}.</p>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Assignment Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Mention Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fa-solid fa-microphone mr-2 text-blue-600"></i>
                    Mention Details
                </h3>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Title:</span>
                            <p class="text-gray-900">{{ assignment.mention_reading.mention.title }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Client:</span>
                            <p class="text-gray-900">{{ assignment.mention_reading.mention.client.name }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Duration:</span>
                            <p class="text-gray-900">{{ assignment.mention_reading.mention.duration_seconds }} seconds</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Priority:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if assignment.mention_reading.mention.priority == 4 %}bg-red-100 text-red-800
                                {% elif assignment.mention_reading.mention.priority == 3 %}bg-orange-100 text-orange-800
                                {% elif assignment.mention_reading.mention.priority == 2 %}bg-blue-100 text-blue-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ assignment.mention_reading.mention.get_priority_display }}
                            </span>
                        </div>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Content:</span>
                        <p class="text-gray-900 bg-gray-50 p-3 rounded-md mt-1">{{ assignment.mention_reading.mention.content }}</p>
                    </div>
                </div>
            </div>

            <!-- Reading Schedule -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fa-solid fa-calendar-day mr-2 text-green-600"></i>
                    Reading Schedule
                </h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Show:</span>
                        <p class="text-gray-900">{{ assignment.mention_reading.show.name }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Date & Time:</span>
                        <p class="text-gray-900">
                            {{ assignment.mention_reading.scheduled_date|date:"M d, Y" }} at 
                            {{ assignment.mention_reading.scheduled_time|time:"H:i" }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Assignment Instructions -->
            {% if assignment.assignment_notes %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fa-solid fa-clipboard-list mr-2 text-purple-600"></i>
                        Assignment Instructions
                    </h3>
                    <div class="prose max-w-none">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ assignment.assignment_notes }}</p>
                    </div>
                </div>
            {% endif %}

            <!-- Reading Note -->
            {% if reading_note %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fa-solid fa-sticky-note mr-2 text-yellow-600"></i>
                            Preparation Note
                        </h3>
                        <a href="{% url 'news_reader:note_detail' reading_note.pk %}" 
                           class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                            View Full Note
                        </a>
                    </div>
                    
                    <!-- Note Status -->
                    <div class="mb-4">
                        <span class="text-sm font-medium text-gray-500">Status:</span>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {% if reading_note.preparation_status == 'completed' %}bg-green-100 text-green-800
                            {% elif reading_note.preparation_status == 'prepared' %}bg-blue-100 text-blue-800
                            {% elif reading_note.preparation_status == 'in_progress' %}bg-yellow-100 text-yellow-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ reading_note.get_preparation_status_display }}
                        </span>
                    </div>

                    <!-- Note Content Preview -->
                    {% if reading_note.content %}
                        <div class="mb-4">
                            <span class="text-sm font-medium text-gray-500">Notes:</span>
                            <p class="text-gray-900 mt-1">
                                {% if reading_note.content|length > 200 %}
                                    {{ reading_note.content|truncatechars:200 }}
                                    <a href="{% url 'news_reader:note_detail' reading_note.pk %}" class="text-primary-600 hover:text-primary-700">Read more</a>
                                {% else %}
                                    {{ reading_note.content }}
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}

                    <!-- Quick Actions -->
                    <div class="flex items-center space-x-2">
                        {% if reading_note.preparation_status == 'not_prepared' %}
                            <a href="{% url 'news_reader:start_note_preparation' reading_note.pk %}" 
                               class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm">
                                Start Preparation
                            </a>
                        {% elif reading_note.preparation_status == 'in_progress' %}
                            <a href="{% url 'news_reader:mark_note_prepared' reading_note.pk %}" 
                               class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                Mark as Prepared
                            </a>
                        {% endif %}
                        <a href="{% url 'news_reader:note_edit' reading_note.pk %}" 
                           class="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-sm">
                            Edit Note
                        </a>
                    </div>
                </div>
            {% else %}
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">No Preparation Note</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>You haven't created a preparation note for this assignment yet.</p>
                            </div>
                            <div class="mt-4">
                                <a href="{% url 'news_reader:create_note_for_reading' assignment.mention_reading.pk %}" 
                                   class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                    Create Preparation Note
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Assignment Status -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fa-solid fa-tasks mr-2 text-indigo-600"></i>
                    Assignment Status
                </h3>
                <div class="space-y-4">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Status:</span>
                        <div class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if assignment.is_completed %}bg-green-100 text-green-800
                                {% elif assignment.is_overdue %}bg-red-100 text-red-800
                                {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                {% if assignment.is_completed %}
                                    Completed
                                {% elif assignment.is_overdue %}
                                    Overdue
                                {% else %}
                                    Pending
                                {% endif %}
                            </span>
                        </div>
                    </div>

                    <div>
                        <span class="text-sm font-medium text-gray-500">Due Date:</span>
                        <p class="text-gray-900 {% if assignment.is_overdue %}text-red-600 font-medium{% endif %}">
                            {{ assignment.due_date|date:"M d, Y" }} at {{ assignment.due_date|time:"H:i" }}
                        </p>
                    </div>

                    {% if assignment.assigned_by %}
                        <div>
                            <span class="text-sm font-medium text-gray-500">Assigned By:</span>
                            <p class="text-gray-900">{{ assignment.assigned_by.get_full_name|default:assignment.assigned_by.username }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Progress Indicator -->
            {% if reading_note %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fa-solid fa-chart-line mr-2 text-green-600"></i>
                        Preparation Progress
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">Progress</span>
                            <span class="font-medium text-gray-900">
                                {% if reading_note.preparation_status == 'completed' %}100%
                                {% elif reading_note.preparation_status == 'prepared' %}75%
                                {% elif reading_note.preparation_status == 'in_progress' %}50%
                                {% else %}25%{% endif %}
                            </span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full 
                                {% if reading_note.preparation_status == 'completed' %}bg-green-600 w-full
                                {% elif reading_note.preparation_status == 'prepared' %}bg-blue-600 w-3/4
                                {% elif reading_note.preparation_status == 'in_progress' %}bg-yellow-600 w-1/2
                                {% else %}bg-gray-400 w-1/4{% endif %}">
                            </div>
                        </div>
                        <div class="text-xs text-gray-500">
                            {{ reading_note.get_preparation_status_display }}
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Metadata -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fa-solid fa-info-circle mr-2 text-gray-600"></i>
                    Information
                </h3>
                <div class="space-y-3 text-sm">
                    <div>
                        <span class="font-medium text-gray-500">Assigned:</span>
                        <p class="text-gray-900">{{ assignment.assigned_at|date:"M d, Y H:i" }}</p>
                    </div>
                    {% if assignment.completed_at %}
                        <div>
                            <span class="font-medium text-gray-500">Completed:</span>
                            <p class="text-gray-900">{{ assignment.completed_at|date:"M d, Y H:i" }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
