{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Article - {{ article.title }}{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{% url 'news_reader:dashboard' %}" class="hover:text-primary-600">Dashboard</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <a href="{% url 'news_reader:article_list' %}" class="hover:text-primary-600">Articles</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <a href="{{ article.get_absolute_url }}" class="hover:text-primary-600">{{ article.title|truncatechars:30 }}</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <span class="text-gray-900">Delete</span>
        </nav>
        <h1 class="text-2xl font-bold text-gray-900">Delete Article</h1>
    </div>

    <!-- Warning Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fa-solid fa-triangle-exclamation text-red-500 text-2xl"></i>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                    Are you sure you want to delete this article?
                </h3>
                <p class="text-gray-600 mb-4">
                    This action cannot be undone. The article and all its associated data will be permanently removed.
                </p>
                
                <!-- Article Preview -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h4 class="font-medium text-gray-900 mb-2">{{ article.title }}</h4>
                    <div class="text-sm text-gray-600 space-y-1">
                        <div><strong>Author:</strong> {{ article.author.get_full_name|default:article.author.username }}</div>
                        <div><strong>Category:</strong> {{ article.get_category_display }}</div>
                        <div><strong>Status:</strong> {{ article.get_status_display }}</div>
                        <div><strong>Created:</strong> {{ article.created_at|date:"F d, Y H:i" }}</div>
                        <div><strong>Word Count:</strong> {{ article.word_count }} words</div>
                        {% if article.assignments.exists %}
                            <div class="text-orange-600">
                                <strong>Warning:</strong> This article has {{ article.assignments.count }} active assignment(s)
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Consequences -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <h5 class="font-medium text-red-900 mb-2">What will be deleted:</h5>
                    <ul class="text-sm text-red-800 space-y-1">
                        <li>• The article content and metadata</li>
                        <li>• All revision history ({{ article.revisions.count }} revision{{ article.revisions.count|pluralize }})</li>
                        {% if article.assignments.exists %}
                            <li>• All assignments to news readers ({{ article.assignments.count }} assignment{{ article.assignments.count|pluralize }})</li>
                        {% endif %}
                        <li>• Any pronunciation guides and formatting</li>
                        <li>• Export history and settings</li>
                    </ul>
                </div>

                <!-- Confirmation Form -->
                <form method="post" class="space-y-4">
                    {% csrf_token %}
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="confirm-delete" name="confirm_delete" required
                               class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                        <label for="confirm-delete" class="ml-2 text-sm text-gray-700">
                            I understand that this action cannot be undone
                        </label>
                    </div>
                    
                    <div class="flex items-center justify-between pt-4">
                        <a href="{{ article.get_absolute_url }}" 
                           class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-2 rounded-lg text-sm font-medium">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                id="delete-button" disabled>
                            <i class="fa-solid fa-trash mr-2"></i>
                            Delete Article
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirm-delete');
    const deleteButton = document.getElementById('delete-button');
    
    confirmCheckbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
    });
});
</script>
{% endblock %}
