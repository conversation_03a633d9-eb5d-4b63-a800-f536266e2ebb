<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ article.title }} - Teleprompter</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        body {
            background: #000;
            color: #fff;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        
        .teleprompter-content {
            font-size: 2.5rem;
            line-height: 1.6;
            text-align: center;
            padding: 2rem;
            animation: scroll-up linear infinite;
        }
        
        .highlight-word {
            background-color: rgba(255, 255, 0, 0.3);
            padding: 2px 4px;
            border-radius: 4px;
        }
        
        .time-marker {
            color: #10b981;
            font-size: 0.8em;
            background-color: rgba(16, 185, 129, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            margin: 0 4px;
        }
        
        .controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 15px 25px;
            border-radius: 50px;
            display: flex;
            align-items: center;
            gap: 15px;
            z-index: 1000;
        }
        
        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .control-btn.active {
            background: #3b82f6;
        }
        
        @keyframes scroll-up {
            from {
                transform: translateY(100vh);
            }
            to {
                transform: translateY(-100%);
            }
        }
        
        .paused {
            animation-play-state: paused !important;
        }
        
        .speed-1 { animation-duration: 120s; }
        .speed-2 { animation-duration: 90s; }
        .speed-3 { animation-duration: 60s; }
        .speed-4 { animation-duration: 45s; }
        .speed-5 { animation-duration: 30s; }
    </style>
</head>

<body>
    <!-- Teleprompter Content -->
    <div class="teleprompter-content speed-3" id="teleprompter-content">
        <div class="mb-8">
            <h1 class="text-4xl font-bold mb-4">{{ article.title }}</h1>
            <p class="text-xl text-gray-300 mb-2">{{ article.byline }}</p>
            <p class="text-lg text-gray-400">{{ article.created_at|date:"F d, Y" }}</p>
        </div>
        
        <div class="text-content">
            {{ article.content|linebreaks }}
        </div>
        
        {% if article.pronunciation_guide %}
            <div class="mt-12 text-xl">
                <h3 class="text-2xl font-bold mb-4 text-yellow-400">Pronunciation Guide</h3>
                {% for word, pronunciation in article.pronunciation_guide.items %}
                    <div class="mb-2">
                        <span class="font-bold">{{ word }}:</span> {{ pronunciation }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        
        <div class="mt-12 text-xl text-gray-400">
            <p>Target Length: {{ article.target_length_minutes }}:{{ article.target_length_seconds|stringformat:"02d" }}</p>
            <p>Word Count: {{ article.word_count }} words</p>
            {% if article.air_times %}
                <p>Air Times: {% for time in article.air_times %}{{ time }}{% if not forloop.last %}, {% endif %}{% endfor %}</p>
            {% endif %}
        </div>
    </div>

    <!-- Controls -->
    <div class="controls">
        <button class="control-btn" onclick="togglePlay()" id="play-btn">
            <i class="fa-solid fa-play"></i>
        </button>
        
        <button class="control-btn" onclick="resetScroll()">
            <i class="fa-solid fa-rotate-left"></i>
        </button>
        
        <div class="flex items-center gap-2 text-sm">
            <span>Speed:</span>
            <button class="control-btn" onclick="setSpeed(1)">1</button>
            <button class="control-btn active" onclick="setSpeed(2)">2</button>
            <button class="control-btn" onclick="setSpeed(3)">3</button>
            <button class="control-btn" onclick="setSpeed(4)">4</button>
            <button class="control-btn" onclick="setSpeed(5)">5</button>
        </div>
        
        <button class="control-btn" onclick="toggleFullscreen()">
            <i class="fa-solid fa-expand"></i>
        </button>
        
        <button class="control-btn" onclick="closePrompt()">
            <i class="fa-solid fa-times"></i>
        </button>
    </div>

    <script>
        let isPlaying = false;
        let currentSpeed = 3;
        
        function togglePlay() {
            const content = document.getElementById('teleprompter-content');
            const playBtn = document.getElementById('play-btn');
            
            if (isPlaying) {
                content.classList.add('paused');
                playBtn.innerHTML = '<i class="fa-solid fa-play"></i>';
                isPlaying = false;
            } else {
                content.classList.remove('paused');
                playBtn.innerHTML = '<i class="fa-solid fa-pause"></i>';
                isPlaying = true;
            }
        }
        
        function setSpeed(speed) {
            const content = document.getElementById('teleprompter-content');
            
            // Remove all speed classes
            content.classList.remove('speed-1', 'speed-2', 'speed-3', 'speed-4', 'speed-5');
            
            // Add new speed class
            content.classList.add(`speed-${speed}`);
            
            // Update active button
            document.querySelectorAll('.controls .control-btn').forEach(btn => {
                if (btn.textContent.trim() === speed.toString()) {
                    btn.classList.add('active');
                } else if (btn.textContent.trim().match(/^\d$/)) {
                    btn.classList.remove('active');
                }
            });
            
            currentSpeed = speed;
        }
        
        function resetScroll() {
            const content = document.getElementById('teleprompter-content');
            content.style.animation = 'none';
            content.offsetHeight; // Trigger reflow
            content.style.animation = null;
            
            // Reset to paused state
            content.classList.add('paused');
            document.getElementById('play-btn').innerHTML = '<i class="fa-solid fa-play"></i>';
            isPlaying = false;
        }
        
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        function closePrompt() {
            window.close();
            // Fallback if window.close() doesn't work
            window.history.back();
        }
        
        // Keyboard controls
        document.addEventListener('keydown', function(e) {
            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    togglePlay();
                    break;
                case 'KeyR':
                    resetScroll();
                    break;
                case 'KeyF':
                    toggleFullscreen();
                    break;
                case 'Escape':
                    closePrompt();
                    break;
                case 'Digit1':
                case 'Digit2':
                case 'Digit3':
                case 'Digit4':
                case 'Digit5':
                    setSpeed(parseInt(e.code.slice(-1)));
                    break;
            }
        });
        
        // Auto-start after 2 seconds
        setTimeout(() => {
            togglePlay();
        }, 2000);
        
        // Hide controls after 5 seconds of inactivity
        let controlsTimeout;
        const controls = document.querySelector('.controls');
        
        function showControls() {
            controls.style.opacity = '1';
            clearTimeout(controlsTimeout);
            controlsTimeout = setTimeout(() => {
                if (isPlaying) {
                    controls.style.opacity = '0.3';
                }
            }, 5000);
        }
        
        document.addEventListener('mousemove', showControls);
        document.addEventListener('keydown', showControls);
        
        // Initial show
        showControls();
    </script>
</body>
</html>
