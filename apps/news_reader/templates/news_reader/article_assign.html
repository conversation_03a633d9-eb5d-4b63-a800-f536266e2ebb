{% extends 'base.html' %}
{% load static %}

{% block title %}Assign Article - {{ article.title }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{% url 'news_reader:dashboard' %}" class="hover:text-primary-600">Dashboard</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <a href="{% url 'news_reader:article_list' %}" class="hover:text-primary-600">Articles</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <a href="{{ article.get_absolute_url }}" class="hover:text-primary-600">{{ article.title|truncatechars:30 }}</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <span class="text-gray-900">Assign</span>
        </nav>
        <h1 class="text-2xl font-bold text-gray-900">Assign Article to News Readers</h1>
        <p class="text-gray-600">Select news readers and set preparation deadlines for this article.</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Assignment Form -->
        <div class="lg:col-span-2">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Article Preview -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Article to Assign</h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 mb-2">{{ article.title }}</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div><strong>Author:</strong> {{ article.author.get_full_name|default:article.author.username }}</div>
                            <div><strong>Category:</strong> {{ article.get_category_display }}</div>
                            <div><strong>Status:</strong> {{ article.get_status_display }}</div>
                            <div><strong>Word Count:</strong> {{ article.word_count }} words</div>
                            <div><strong>Target Length:</strong> {{ article.target_length_minutes }}:{{ article.target_length_seconds|stringformat:"02d" }}</div>
                            {% if article.air_times %}
                                <div><strong>Air Times:</strong> {% for time in article.air_times %}{{ time }}{% if not forloop.last %}, {% endif %}{% endfor %}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- News Reader Selection -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Select News Readers</h3>
                    
                    {% if news_readers %}
                        <div class="space-y-3">
                            {% for reader in news_readers %}
                                <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                    <input type="checkbox" name="users" value="{{ reader.id }}" 
                                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                                           {% if reader.id in existing_assignments %}disabled checked{% endif %}>
                                    <div class="ml-3 flex-1">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    {{ reader.get_full_name|default:reader.username }}
                                                </div>
                                                <div class="text-sm text-gray-500">{{ reader.email }}</div>
                                            </div>
                                            {% if reader.id in existing_assignments %}
                                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Already Assigned
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </label>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-gray-500">No news readers available for assignment.</p>
                    {% endif %}
                </div>

                <!-- Assignment Details -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Assignment Details</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label for="due_date" class="block text-sm font-medium text-gray-700 mb-1">Due Date & Time *</label>
                            <input type="datetime-local" name="due_date" id="due_date" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            <p class="mt-1 text-sm text-gray-500">When should the article be prepared by?</p>
                        </div>
                        
                        <div>
                            <label for="assignment_notes" class="block text-sm font-medium text-gray-700 mb-1">Assignment Notes</label>
                            <textarea name="assignment_notes" id="assignment_notes" rows="4"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                      placeholder="Special instructions or notes for this assignment..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center justify-between">
                    <a href="{{ article.get_absolute_url }}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-2 rounded-lg text-sm font-medium">
                        Cancel
                    </a>
                    <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg text-sm font-medium">
                        <i class="fa-solid fa-user-plus mr-2"></i>
                        Assign Article
                    </button>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Current Assignments -->
            {% if existing_assignments %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Assignments</h3>
                    <div class="space-y-3">
                        {% for assignment in article.assignments.all %}
                            {% if assignment.is_active %}
                                <div class="p-3 bg-gray-50 rounded-lg">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ assignment.user.get_full_name|default:assignment.user.username }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        Due: {{ assignment.due_date|date:"M d, Y H:i" }}
                                    </div>
                                    <div class="mt-2">
                                        {% if assignment.is_completed %}
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Completed
                                            </span>
                                        {% elif assignment.is_overdue %}
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Overdue
                                            </span>
                                        {% else %}
                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                Pending
                                            </span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Assignment Tips -->
            <div class="bg-blue-50 rounded-lg p-6">
                <h3 class="text-lg font-medium text-blue-900 mb-3">
                    <i class="fa-solid fa-lightbulb mr-2"></i>
                    Assignment Tips
                </h3>
                <ul class="text-sm text-blue-800 space-y-2">
                    <li>• Set due dates at least 30 minutes before air time</li>
                    <li>• Consider the article complexity when setting deadlines</li>
                    <li>• Include pronunciation guides for difficult words</li>
                    <li>• Add special instructions in the notes field</li>
                    <li>• News readers will receive notifications about assignments</li>
                </ul>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="{% url 'news_reader:article_editor' article.pk %}" 
                       class="flex items-center text-sm text-primary-600 hover:text-primary-700">
                        <i class="fa-solid fa-edit mr-2"></i>
                        Edit Article
                    </a>
                    <a href="{% url 'news_reader:article_export_pdf' article.pk %}" 
                       class="flex items-center text-sm text-primary-600 hover:text-primary-700">
                        <i class="fa-solid fa-file-pdf mr-2"></i>
                        Export PDF
                    </a>
                    <a href="{% url 'news_reader:article_teleprompter' article.pk %}" 
                       class="flex items-center text-sm text-primary-600 hover:text-primary-700" target="_blank">
                        <i class="fa-solid fa-tv mr-2"></i>
                        Teleprompter View
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set default due date to 2 hours from now
    const dueDateInput = document.getElementById('due_date');
    const now = new Date();
    now.setHours(now.getHours() + 2);
    
    // Format for datetime-local input
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    
    dueDateInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
});
</script>
{% endblock %}
