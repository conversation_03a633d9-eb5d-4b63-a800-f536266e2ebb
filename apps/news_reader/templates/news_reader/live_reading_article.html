<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ article.title }} - Live Reading</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <style>
        ::-webkit-scrollbar { display: none; }
        
        .teleprompter-text {
            line-height: 1.8;
            letter-spacing: 0.5px;
            word-spacing: 2px;
        }
        
        .current-line {
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0.1) 100%);
            border-left: 4px solid #3b82f6;
            padding-left: 16px;
            margin-left: -20px;
        }
        
        .scroll-container {
            scroll-behavior: smooth;
        }
        
        .pronunciation {
            font-size: 0.75em;
            color: #9ca3af;
            font-style: italic;
        }
        
        .control-button {
            transition: all 0.1s ease;
        }
        
        .control-button:active {
            transform: scale(0.95);
        }
        
        .live-indicator {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .emergency-stop {
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        }
        
        html, body {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        broadcast: {
                            bg: "#000000",
                            text: "#ffffff",
                            control: "#1f2937",
                            live: "#ef4444",
                            breaking: "#f59e0b",
                            local: "#3b82f6",
                            traffic: "#f97316",
                            weather: "#10b981",
                            sports: "#8b5cf6"
                        }
                    },
                    fontFamily: {
                        broadcast: ["Roboto", "Arial", "sans-serif"],
                        sans: ["Inter", "sans-serif"]
                    }
                }
            }
        };
    </script>
</head>

<body class="font-broadcast bg-broadcast-bg text-broadcast-text h-screen flex flex-col overflow-hidden">
    <!-- Status Bar -->
    <div id="status-bar" class="flex items-center justify-between px-6 py-2 bg-broadcast-control border-b border-gray-700">
        <div class="flex items-center">
            <div class="flex items-center mr-8">
                <div class="w-3 h-3 bg-broadcast-live rounded-full live-indicator mr-2"></div>
                <span class="text-lg font-semibold">NewsRadio Live</span>
            </div>
            <span class="text-gray-300">{{ article.get_category_display }}</span>
        </div>
        <div class="flex items-center space-x-6">
            <div class="text-right">
                <div class="text-sm text-gray-400">Article Time</div>
                <div class="text-xl font-mono font-bold" id="article-timer">0:00</div>
            </div>
            <div class="text-right">
                <div class="text-sm text-gray-400">Target</div>
                <div class="text-xl font-mono font-bold">{{ article.target_length_minutes }}:{{ article.target_length_seconds|stringformat:"02d" }}</div>
            </div>
            <div class="text-right">
                <div class="text-sm text-gray-400">Current Time</div>
                <div class="text-xl font-mono font-bold" id="current-time"></div>
            </div>
            <button class="p-2 text-gray-400 hover:text-white" onclick="closeReading()">
                <i class="fa-solid fa-times text-lg"></i>
            </button>
        </div>
    </div>

    <!-- Main Teleprompter Area -->
    <div id="teleprompter-main" class="flex-1 relative overflow-hidden">
        <!-- Reading Content -->
        <div id="scroll-container" class="h-full overflow-y-auto scroll-container px-16 py-12">
            <div class="max-w-4xl mx-auto">
                <!-- Article Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-6">
                        {% if article.is_breaking %}
                            <span class="px-4 py-2 bg-broadcast-breaking text-black font-bold text-lg rounded-md mr-4">BREAKING NEWS</span>
                        {% elif article.is_urgent %}
                            <span class="px-4 py-2 bg-orange-500 text-white font-bold text-lg rounded-md mr-4">URGENT</span>
                        {% elif article.category == 'weather' %}
                            <span class="px-4 py-2 bg-broadcast-weather text-white font-bold text-lg rounded-md mr-4">WEATHER</span>
                        {% elif article.category == 'sports' %}
                            <span class="px-4 py-2 bg-broadcast-sports text-white font-bold text-lg rounded-md mr-4">SPORTS</span>
                        {% elif article.category == 'traffic' %}
                            <span class="px-4 py-2 bg-broadcast-traffic text-white font-bold text-lg rounded-md mr-4">TRAFFIC</span>
                        {% else %}
                            <span class="px-4 py-2 bg-broadcast-local text-white font-bold text-lg rounded-md mr-4">{{ article.get_category_display|upper }}</span>
                        {% endif %}
                        <span class="text-2xl text-gray-300">{{ article.byline }}</span>
                    </div>
                    
                    <h1 class="text-5xl font-bold mb-8 teleprompter-text">{{ article.title }}</h1>
                    
                    {% if article.air_times %}
                        <div class="text-xl text-gray-400 mb-6">
                            Air Times: {% for time in article.air_times %}{{ time }}{% if not forloop.last %}, {% endif %}{% endfor %}
                        </div>
                    {% endif %}
                </div>

                <!-- Article Content -->
                <div class="teleprompter-text text-3xl leading-relaxed space-y-6" id="article-content">
                    {% for paragraph in article.content|linebreaks %}
                        {% if forloop.first %}
                            <p class="current-line">{{ paragraph|safe }}</p>
                        {% else %}
                            <p>{{ paragraph|safe }}</p>
                        {% endif %}
                    {% endfor %}
                </div>

                <!-- Pronunciation Guide -->
                {% if article.pronunciation_guide %}
                    <div class="mt-12 p-6 bg-gray-800 rounded-lg">
                        <h3 class="text-2xl font-bold mb-4 text-yellow-400">Pronunciation Guide</h3>
                        <div class="grid grid-cols-2 gap-4">
                            {% for word, pronunciation in article.pronunciation_guide.items %}
                                <div class="text-lg">
                                    <span class="font-bold text-white">{{ word }}:</span>
                                    <span class="text-gray-300 ml-2">{{ pronunciation }}</span>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                <!-- End of Article -->
                <div class="mt-12 text-center">
                    <div class="text-2xl text-gray-400 mb-4">End of Article</div>
                    <div class="text-lg text-gray-500">
                        Word Count: {{ article.word_count }} | Target: {{ article.target_length_minutes }}:{{ article.target_length_seconds|stringformat:"02d" }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Reading Position Indicator -->
        <div id="position-indicator" class="absolute left-0 top-1/2 w-1 h-16 bg-broadcast-live opacity-80 transform -translate-y-1/2"></div>
    </div>

    <!-- Control Panel -->
    <div id="control-panel" class="bg-broadcast-control border-t border-gray-700 px-6 py-4">
        <div class="flex items-center justify-between">
            <!-- Left Controls -->
            <div class="flex items-center space-x-4">
                <!-- Auto-Scroll Control -->
                <button id="scroll-toggle" class="control-button flex items-center justify-center w-16 h-16 bg-blue-600 hover:bg-blue-700 rounded-full text-white text-2xl">
                    <i class="fa-solid fa-play" id="scroll-icon"></i>
                </button>
                
                <!-- Speed Controls -->
                <div class="flex items-center space-x-2">
                    <button id="speed-down" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-minus"></i>
                    </button>
                    <div class="px-3 py-2 bg-gray-700 rounded">
                        <span class="text-sm font-mono" id="speed-display">1.0x</span>
                    </div>
                    <button id="speed-up" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-plus"></i>
                    </button>
                </div>
                
                <!-- Manual Scroll -->
                <div class="flex items-center space-x-2">
                    <button id="scroll-up" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-chevron-up"></i>
                    </button>
                    <button id="scroll-down" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-chevron-down"></i>
                    </button>
                </div>
            </div>

            <!-- Center Status -->
            <div class="flex items-center space-x-8">
                <!-- Mic Status -->
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-broadcast-live rounded-full live-indicator mr-3"></div>
                    <span class="text-xl font-bold text-broadcast-live">ON AIR</span>
                </div>

                <!-- Reading Progress -->
                <div class="text-center">
                    <div class="text-sm text-gray-400">Reading Progress</div>
                    <div class="text-2xl font-mono font-bold" id="reading-progress">0%</div>
                </div>
            </div>

            <!-- Right Controls -->
            <div class="flex items-center space-x-4">
                <!-- Font Size -->
                <div class="flex items-center space-x-2">
                    <button id="font-down" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-text-height"></i>
                    </button>
                    <div class="px-3 py-2 bg-gray-700 rounded">
                        <span class="text-sm" id="font-size">32px</span>
                    </div>
                    <button id="font-up" class="control-button flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded text-white">
                        <i class="fa-solid fa-text-height"></i>
                    </button>
                </div>

                <!-- Mark as Read -->
                <button id="mark-read" class="control-button flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-white">
                    <i class="fa-solid fa-check mr-2"></i>
                    Mark Read
                </button>

                <!-- Emergency Stop -->
                <button id="emergency-stop" class="control-button emergency-stop flex items-center justify-center w-16 h-16 bg-red-600 hover:bg-red-700 rounded-full text-white text-2xl">
                    <i class="fa-solid fa-stop"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        let isScrolling = false;
        let scrollSpeed = 1;
        let fontSize = 32;
        let readingStartTime = Date.now();
        let readingTimer = null;
        let scrollInterval = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateClock();
            setInterval(updateClock, 1000);
            startReadingTimer();
            setupKeyboardShortcuts();
        });

        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', { 
                hour12: true,
                hour: 'numeric',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }

        function startReadingTimer() {
            readingTimer = setInterval(() => {
                const elapsed = Math.floor((Date.now() - readingStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                
                document.getElementById('article-timer').textContent = 
                    `${minutes}:${seconds.toString().padStart(2, '0')}`;
                
                // Update progress
                const targetSeconds = {{ article.target_length_minutes }} * 60 + {{ article.target_length_seconds }};
                const progress = Math.min((elapsed / targetSeconds) * 100, 100);
                document.getElementById('reading-progress').textContent = Math.round(progress) + '%';
            }, 1000);
        }

        // Auto-scroll functionality
        document.getElementById('scroll-toggle').addEventListener('click', function() {
            isScrolling = !isScrolling;
            const icon = document.getElementById('scroll-icon');
            
            if (isScrolling) {
                icon.className = 'fa-solid fa-pause';
                startAutoScroll();
            } else {
                icon.className = 'fa-solid fa-play';
                stopAutoScroll();
            }
        });

        function startAutoScroll() {
            const container = document.getElementById('scroll-container');
            scrollInterval = setInterval(() => {
                if (!isScrolling) {
                    clearInterval(scrollInterval);
                    return;
                }
                container.scrollTop += scrollSpeed;
            }, 50);
        }

        function stopAutoScroll() {
            if (scrollInterval) {
                clearInterval(scrollInterval);
            }
        }

        // Speed controls
        document.getElementById('speed-up').addEventListener('click', function() {
            scrollSpeed = Math.min(scrollSpeed + 0.2, 3);
            document.getElementById('speed-display').textContent = scrollSpeed.toFixed(1) + 'x';
        });

        document.getElementById('speed-down').addEventListener('click', function() {
            scrollSpeed = Math.max(scrollSpeed - 0.2, 0.2);
            document.getElementById('speed-display').textContent = scrollSpeed.toFixed(1) + 'x';
        });

        // Manual scroll
        document.getElementById('scroll-up').addEventListener('click', function() {
            document.getElementById('scroll-container').scrollTop -= 100;
        });

        document.getElementById('scroll-down').addEventListener('click', function() {
            document.getElementById('scroll-container').scrollTop += 100;
        });

        // Font size controls
        document.getElementById('font-up').addEventListener('click', function() {
            fontSize = Math.min(fontSize + 2, 48);
            updateFontSize();
        });

        document.getElementById('font-down').addEventListener('click', function() {
            fontSize = Math.max(fontSize - 2, 20);
            updateFontSize();
        });

        function updateFontSize() {
            document.getElementById('article-content').style.fontSize = fontSize + 'px';
            document.getElementById('font-size').textContent = fontSize + 'px';
        }

        // Mark as read
        document.getElementById('mark-read').addEventListener('click', function() {
            const duration = Math.floor((Date.now() - readingStartTime) / 60000); // minutes
            
            fetch(`/news-reader/live-reading/article/{{ article.pk }}/mark-read/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: `duration_minutes=${duration}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Article marked as read!');
                    this.style.background = '#10b981';
                    this.innerHTML = '<i class="fa-solid fa-check mr-2"></i>Completed';
                }
            })
            .catch(error => console.error('Error:', error));
        });

        // Emergency stop
        document.getElementById('emergency-stop').addEventListener('click', function() {
            stopAutoScroll();
            if (confirm('Emergency stop activated. Close reading session?')) {
                closeReading();
            }
        });

        function closeReading() {
            if (confirm('Are you sure you want to close the reading session?')) {
                window.close();
            }
        }

        // Keyboard shortcuts
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                switch(e.code) {
                    case 'Space':
                        e.preventDefault();
                        document.getElementById('scroll-toggle').click();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        document.getElementById('scroll-up').click();
                        break;
                    case 'ArrowDown':
                        e.preventDefault();
                        document.getElementById('scroll-down').click();
                        break;
                    case 'Escape':
                        closeReading();
                        break;
                    case 'Equal':
                        if (e.ctrlKey || e.metaKey) {
                            e.preventDefault();
                            document.getElementById('font-up').click();
                        }
                        break;
                    case 'Minus':
                        if (e.ctrlKey || e.metaKey) {
                            e.preventDefault();
                            document.getElementById('font-down').click();
                        }
                        break;
                }
            });
        }
    </script>
</body>
</html>
