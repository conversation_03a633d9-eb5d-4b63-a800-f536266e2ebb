{% extends 'base.html' %}
{% load static %}

{% block title %}My Reading Notes{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Reading Notes</h1>
                <p class="text-gray-600">Manage your preparation notes and reading status.</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'news_reader:note_create' %}" 
                   class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                    <i class="fa-solid fa-plus mr-2"></i>
                    New Note
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <form method="get" class="flex flex-wrap items-center gap-4">
            <div class="flex items-center space-x-2">
                <label for="status" class="text-sm font-medium text-gray-700">Status:</label>
                <select name="status" id="status" class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                    <option value="">All Statuses</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if current_filters.status == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="flex items-center space-x-2">
                <label for="priority" class="text-sm font-medium text-gray-700">Priority:</label>
                <select name="priority" id="priority" class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                    <option value="">All Priorities</option>
                    {% for value, label in priority_choices %}
                        <option value="{{ value }}" {% if current_filters.priority == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="flex items-center space-x-2">
                <label for="search" class="text-sm font-medium text-gray-700">Search:</label>
                <input type="text" name="search" id="search" value="{{ current_filters.search }}" 
                       placeholder="Search notes..." class="border border-gray-300 rounded-md px-3 py-1 text-sm">
            </div>
            
            <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-1 rounded-md text-sm">
                Filter
            </button>
            
            <a href="{% url 'news_reader:note_list' %}" class="text-gray-600 hover:text-gray-800 text-sm">
                Clear Filters
            </a>
        </form>
    </div>

    <!-- Notes List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        {% if page_obj %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Note</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mention</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Updated</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for note in page_obj %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        {% if note.is_favorite %}
                                            <i class="fa-solid fa-star text-yellow-500 mr-2"></i>
                                        {% endif %}
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ note.title|default:"Untitled Note" }}
                                            </div>
                                            {% if note.content %}
                                                <div class="text-sm text-gray-500 truncate max-w-xs">
                                                    {{ note.content|truncatechars:50 }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ note.mention.title }}</div>
                                    <div class="text-sm text-gray-500">{{ note.mention.client.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if note.preparation_status == 'completed' %}bg-green-100 text-green-800
                                        {% elif note.preparation_status == 'prepared' %}bg-blue-100 text-blue-800
                                        {% elif note.preparation_status == 'in_progress' %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ note.get_preparation_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if note.priority == 4 %}bg-red-100 text-red-800
                                        {% elif note.priority == 3 %}bg-orange-100 text-orange-800
                                        {% elif note.priority == 2 %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ note.get_priority_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ note.updated_at|timesince }} ago
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <a href="{% url 'news_reader:note_detail' note.pk %}" 
                                           class="text-primary-600 hover:text-primary-900">View</a>
                                        <a href="{% url 'news_reader:note_edit' note.pk %}" 
                                           class="text-indigo-600 hover:text-indigo-900">Edit</a>
                                        {% if note.preparation_status == 'not_prepared' %}
                                            <a href="{% url 'news_reader:start_note_preparation' note.pk %}" 
                                               class="text-yellow-600 hover:text-yellow-900">Start</a>
                                        {% elif note.preparation_status == 'in_progress' %}
                                            <a href="{% url 'news_reader:mark_note_prepared' note.pk %}" 
                                               class="text-green-600 hover:text-green-900">Complete</a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" 
                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to 
                                <span class="font-medium">{{ page_obj.end_index }}</span> of 
                                <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fa-solid fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">
                                            {{ num }}
                                        </span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?page={{ num }}" 
                                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ num }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fa-solid fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <i class="fa-solid fa-sticky-note text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No notes found</h3>
                <p class="text-gray-500 mb-4">Get started by creating your first reading note.</p>
                <a href="{% url 'news_reader:note_create' %}" 
                   class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Create Note
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
