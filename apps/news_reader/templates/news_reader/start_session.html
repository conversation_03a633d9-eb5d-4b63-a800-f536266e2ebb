{% extends 'base.html' %}
{% load static %}

{% block title %}Start Reading Session{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Start Reading Session</h1>
                <p class="text-gray-600">Begin a new preparation or reading session to track your progress.</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'news_reader:session_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Sessions
                </a>
            </div>
        </div>
    </div>

    <!-- Session Start Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fa-solid fa-play mr-2 text-green-600"></i>
                New Session
            </h3>
        </div>
        
        <form method="post" class="p-6 space-y-6">
            {% csrf_token %}
            
            <!-- Session Type -->
            <div>
                <label for="session_type" class="block text-sm font-medium text-gray-700 mb-2">
                    Session Type
                </label>
                <select name="session_type" id="session_type" required 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="preparation">Preparation Session</option>
                    <option value="live_reading">Live Reading Session</option>
                    <option value="review">Review Session</option>
                </select>
                <p class="mt-1 text-sm text-gray-500">Choose the type of session you're starting</p>
            </div>

            <!-- Session Description -->
            <div id="session-descriptions" class="space-y-4">
                <div id="preparation-desc" class="session-desc bg-blue-50 border border-blue-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa-solid fa-book-open text-blue-600"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-blue-800">Preparation Session</h4>
                            <p class="text-sm text-blue-700 mt-1">
                                Use this for preparing reading notes, reviewing content, and getting ready for upcoming mentions. 
                                Perfect for studying pronunciation guides and organizing your thoughts.
                            </p>
                        </div>
                    </div>
                </div>

                <div id="live_reading-desc" class="session-desc bg-red-50 border border-red-200 rounded-md p-4 hidden">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa-solid fa-broadcast-tower text-red-600"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-red-800">Live Reading Session</h4>
                            <p class="text-sm text-red-700 mt-1">
                                Start this when you're actively reading mentions during a live broadcast. 
                                This will track your reading performance and timing.
                            </p>
                        </div>
                    </div>
                </div>

                <div id="review-desc" class="session-desc bg-green-50 border border-green-200 rounded-md p-4 hidden">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa-solid fa-search text-green-600"></i>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-sm font-medium text-green-800">Review Session</h4>
                            <p class="text-sm text-green-700 mt-1">
                                Use this for reviewing completed readings, analyzing performance, 
                                and updating notes based on your reading experience.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Initial Notes (Optional) -->
            <div>
                <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                    Session Notes (Optional)
                </label>
                <textarea name="notes" id="notes" rows="3" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                          placeholder="Add any initial notes or goals for this session..."></textarea>
                <p class="mt-1 text-sm text-gray-500">You can add more notes during or after the session</p>
            </div>

            <!-- Current Status Info -->
            <div class="bg-gray-50 rounded-md p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Session Information</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-600">Start Time:</span>
                        <p class="text-gray-900" id="current-time">{{ "now"|date:"M d, Y H:i" }}</p>
                    </div>
                    <div>
                        <span class="font-medium text-gray-600">User:</span>
                        <p class="text-gray-900">{{ user.get_full_name|default:user.username }}</p>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'news_reader:session_list' %}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg text-sm font-medium flex items-center">
                    <i class="fa-solid fa-play mr-2"></i>
                    Start Session
                </button>
            </div>
        </form>
    </div>

    <!-- Quick Actions -->
    <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{% url 'news_reader:live_reading_interface' %}" 
               class="flex items-center p-4 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 transition-colors">
                <i class="fa-solid fa-broadcast-tower text-red-600 text-xl mr-3"></i>
                <div>
                    <h4 class="font-medium text-red-900">Go Live</h4>
                    <p class="text-sm text-red-700">Jump to live reading interface</p>
                </div>
            </a>
            
            <a href="{% url 'news_reader:note_list' %}" 
               class="flex items-center p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors">
                <i class="fa-solid fa-sticky-note text-blue-600 text-xl mr-3"></i>
                <div>
                    <h4 class="font-medium text-blue-900">View Notes</h4>
                    <p class="text-sm text-blue-700">Review your preparation notes</p>
                </div>
            </a>
            
            <a href="{% url 'news_reader:assignment_list' %}" 
               class="flex items-center p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                <i class="fa-solid fa-tasks text-green-600 text-xl mr-3"></i>
                <div>
                    <h4 class="font-medium text-green-900">Assignments</h4>
                    <p class="text-sm text-green-700">Check your reading assignments</p>
                </div>
            </a>
        </div>
    </div>
</div>

<script>
// Update current time display
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
    document.getElementById('current-time').textContent = timeString;
}

// Show/hide session descriptions based on selection
function updateSessionDescription() {
    const sessionType = document.getElementById('session_type').value;
    const descriptions = document.querySelectorAll('.session-desc');
    
    descriptions.forEach(desc => {
        desc.classList.add('hidden');
    });
    
    const activeDesc = document.getElementById(sessionType + '-desc');
    if (activeDesc) {
        activeDesc.classList.remove('hidden');
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000); // Update every minute
    
    // Set up session type change handler
    document.getElementById('session_type').addEventListener('change', updateSessionDescription);
    
    // Initialize description display
    updateSessionDescription();
});
</script>
{% endblock %}
