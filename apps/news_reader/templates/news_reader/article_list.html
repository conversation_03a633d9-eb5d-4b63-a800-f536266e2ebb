{% extends 'base.html' %}
{% load static %}

{% block title %}Articles{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Articles</h1>
                <p class="text-gray-600">Manage and create news articles for reading.</p>
            </div>
            <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-500">{{ current_organization.name }}</span>
                <div class="flex items-center space-x-2">
                    <a href="{% url 'news_reader:dashboard' %}" 
                       class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                        <i class="fa-solid fa-arrow-left mr-2"></i>
                        Dashboard
                    </a>
                    <a href="{% url 'news_reader:article_create' %}" 
                       class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                        <i class="fa-solid fa-plus mr-2"></i>
                        New Article
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="{{ current_filters.search }}" 
                       placeholder="Search articles..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="">All Statuses</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if current_filters.status == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="">All Categories</option>
                    {% for value, label in category_choices %}
                        <option value="{{ value }}" {% if current_filters.category == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                <select name="priority" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="">All Priorities</option>
                    {% for value, label in priority_choices %}
                        <option value="{{ value }}" {% if current_filters.priority == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Author</label>
                <select name="author" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="">All Authors</option>
                    {% for author in authors %}
                        <option value="{{ author.id }}" {% if current_filters.author == author.id|stringformat:"s" %}selected{% endif %}>
                            {{ author.get_full_name|default:author.username }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    <i class="fa-solid fa-filter mr-2"></i>
                    Filter
                </button>
            </div>
        </form>
    </div>

    <!-- Articles List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        {% if page_obj %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Author</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Length</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for article in page_obj %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <a href="{{ article.get_absolute_url }}" class="hover:text-primary-600">
                                                    {{ article.title }}
                                                </a>
                                            </div>
                                            <div class="text-sm text-gray-500">{{ article.slug }}</div>
                                            {% if article.is_breaking %}
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 mt-1">
                                                    BREAKING
                                                </span>
                                            {% endif %}
                                            {% if article.is_urgent %}
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800 mt-1">
                                                    URGENT
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ article.author.get_full_name|default:article.author.username }}</div>
                                    <div class="text-sm text-gray-500">{{ article.byline }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ article.get_category_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium 
                                        {% if article.status == 'draft' %}bg-gray-100 text-gray-800
                                        {% elif article.status == 'in_review' %}bg-yellow-100 text-yellow-800
                                        {% elif article.status == 'approved' %}bg-green-100 text-green-800
                                        {% elif article.status == 'scheduled' %}bg-blue-100 text-blue-800
                                        {% elif article.status == 'published' %}bg-purple-100 text-purple-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ article.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium 
                                        {% if article.priority == 'low' %}bg-gray-100 text-gray-800
                                        {% elif article.priority == 'normal' %}bg-blue-100 text-blue-800
                                        {% elif article.priority == 'high' %}bg-orange-100 text-orange-800
                                        {% elif article.priority == 'urgent' %}bg-red-100 text-red-800
                                        {% elif article.priority == 'breaking' %}bg-red-200 text-red-900
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ article.get_priority_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ article.target_length_minutes }}:{{ article.target_length_seconds|stringformat:"02d" }}
                                    <div class="text-xs text-gray-500">{{ article.word_count }} words</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ article.created_at|date:"M d, Y" }}
                                    <div class="text-xs">{{ article.created_at|time:"H:i" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ article.get_absolute_url }}" class="text-primary-600 hover:text-primary-900">
                                            <i class="fa-solid fa-eye"></i>
                                        </a>
                                        <a href="{% url 'news_reader:article_editor' article.pk %}" class="text-blue-600 hover:text-blue-900">
                                            <i class="fa-solid fa-edit"></i>
                                        </a>
                                        {% if user == article.author or perms.news_reader.delete_article %}
                                            <a href="{% url 'news_reader:article_delete' article.pk %}" class="text-red-600 hover:text-red-900">
                                                <i class="fa-solid fa-trash"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fa-solid fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">
                                            {{ num }}
                                        </span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ num }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fa-solid fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <i class="fa-solid fa-newspaper text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No articles found</h3>
                <p class="text-gray-500 mb-4">Get started by creating your first article.</p>
                <a href="{% url 'news_reader:article_create' %}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    <i class="fa-solid fa-plus mr-2"></i>
                    Create Article
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
