{% extends 'base.html' %}
{% load static %}
{% load time_filters %}

{% block title %}
  News Reader Dashboard
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">News Reader Dashboard</h1>
          <p class="text-gray-600">Welcome back! Here's your reading preparation overview.</p>
        </div>
        <div class="flex items-center space-x-3">
          <span class="text-sm text-gray-500">{{ current_organization.name }}</span>
          <div class="flex items-center space-x-2">
            <a href="{% url 'news_reader:live_reading_interface' %}" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
              <i class="fa-solid fa-broadcast-tower mr-2"></i>
              Live Reading
            </a>
            <a href="{% url 'news_reader:article_list' %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
              <i class="fa-solid fa-newspaper mr-2"></i>
              Articles
            </a>
            <a href="{% url 'news_reader:note_create' %}" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
              <i class="fa-solid fa-plus mr-2"></i>
              New Note
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-2 lg:grid-cols-8 gap-4 mb-8">
      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-sticky-note text-blue-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.total_notes }}</p>
            <p class="text-xs text-gray-500">Total Notes</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-check-circle text-green-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.prepared_today }}</p>
            <p class="text-xs text-gray-500">Prepared Today</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-clock text-yellow-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.in_progress }}</p>
            <p class="text-xs text-gray-500">In Progress</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-exclamation-triangle text-red-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.overdue }}</p>
            <p class="text-xs text-gray-500">Overdue</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-tasks text-purple-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.active_assignments }}</p>
            <p class="text-xs text-gray-500">Assignments</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-microphone text-indigo-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.completed_today }}</p>
            <p class="text-xs text-gray-500">Read Today</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-newspaper text-orange-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.total_articles }}</p>
            <p class="text-xs text-gray-500">Articles</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center mr-3">
            <i class="fa-solid fa-file-pen text-teal-600"></i>
          </div>
          <div>
            <p class="text-lg font-bold text-gray-900">{{ stats.articles_draft }}</p>
            <p class="text-xs text-gray-500">Drafts</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <!-- Today's Schedule -->
      <div class="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fa-solid fa-calendar-day mr-2 text-blue-600"></i>
            Today's Schedule
          </h3>
          <div class="flex items-center space-x-2">
            <span class="text-xs text-gray-500" id="current-time"></span>
            <a href="{% url 'news_reader:assignment_list' %}" class="text-primary-600 text-sm hover:text-primary-700">View All</a>
          </div>
        </div>
        <div class="p-6">
          {% if todays_schedule %}
            <div class="space-y-4">
              {% for assignment in todays_schedule %}
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div class="flex items-center space-x-4">
                    <div class="text-center">
                      <div class="text-xs text-gray-500">{{ assignment.mention_reading.scheduled_date|format_date_user:user }}</div>
                      <div class="text-lg font-bold text-gray-900">{{ assignment.mention_reading.scheduled_time|time:'H:i' }}</div>
                    </div>
                    <div class="flex-1">
                      <h4 class="font-medium text-gray-900">{{ assignment.mention_reading.mention.title }}</h4>
                      <p class="text-sm text-gray-600">{{ assignment.mention_reading.show.name }}</p>
                      <div class="flex items-center space-x-2 mt-1">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                                {% if assignment.is_overdue %}
                            
                            
                            
                            bg-red-100 text-red-800




                          {% elif assignment.is_completed %}
                            
                            
                            
                            bg-green-100 text-green-800




                          {% else %}
                            
                            
                            
                            bg-yellow-100 text-yellow-800



                          {% endif %}">
                          {% if assignment.is_overdue %}
                            Overdue
                          {% elif assignment.is_completed %}
                            Completed
                          {% else %}
                            Pending
                          {% endif %}
                        </span>
                        <span class="text-xs text-gray-500">{{ assignment.mention_reading.mention.duration_seconds }}s</span>
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <a href="{% url 'news_reader:assignment_detail' assignment.pk %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">View</a>
                    {% if not assignment.is_completed %}
                      <a href="{% url 'news_reader:create_note_for_reading' assignment.mention_reading.pk %}" class="bg-primary-600 hover:bg-primary-700 text-white px-3 py-1 rounded text-xs">Prepare</a>
                    {% endif %}
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-8">
              <i class="fa-solid fa-calendar-check text-gray-400 text-3xl mb-4"></i>
              <p class="text-gray-500">No scheduled readings for today</p>
              <a href="{% url 'news_reader:assignment_list' %}" class="text-primary-600 hover:text-primary-700 text-sm">View all assignments</a>
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fa-solid fa-bolt mr-2 text-yellow-600"></i>
            Quick Actions
          </h3>
          <div class="space-y-3">
            <a href="{% url 'news_reader:start_session' %}" class="block w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium text-center">Start Preparation Session</a>
            <a href="{% url 'news_reader:live_reading_interface' %}" class="block w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium text-center">Go Live</a>
            <a href="{% url 'news_reader:note_list' %}" class="block w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium text-center">View All Notes</a>
          </div>
        </div>

        <!-- Recent Notes -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fa-solid fa-history mr-2 text-green-600"></i>
            Recent Notes
          </h3>
          {% if recent_notes %}
            <div class="space-y-3">
              {% for note in recent_notes %}
                <div class="p-3 bg-gray-50 rounded-lg">
                  <div class="flex items-center justify-between">
                    <h4 class="text-sm font-medium text-gray-900 truncate">{{ note.title|default:note.mention.title }}</h4>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if note.preparation_status == 'completed' %}
                        
                        
                        
                        bg-green-100 text-green-800




                      {% elif note.preparation_status == 'prepared' %}
                        
                        
                        
                        bg-blue-100 text-blue-800




                      {% elif note.preparation_status == 'in_progress' %}
                        
                        
                        
                        bg-yellow-100 text-yellow-800




                      {% else %}
                        
                        
                        
                        bg-gray-100 text-gray-800



                      {% endif %}">
                      {{ note.get_preparation_status_display }}
                    </span>
                  </div>
                  <p class="text-xs text-gray-600 mt-1">{{ note.updated_at|timesince }} ago</p>
                  <div class="flex items-center justify-between mt-2">
                    <a href="{% url 'news_reader:note_detail' note.pk %}" class="text-primary-600 hover:text-primary-700 text-xs">View</a>
                    {% if note.is_favorite %}
                      <i class="fa-solid fa-star text-yellow-500 text-xs"></i>
                    {% endif %}
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <p class="text-gray-500 text-sm">No notes yet</p>
          {% endif %}
        </div>

        <!-- Upcoming Deadlines -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fa-solid fa-clock mr-2 text-red-600"></i>
            Upcoming Deadlines
          </h3>
          {% if upcoming_deadlines %}
            <div class="space-y-3">
              {% for assignment in upcoming_deadlines %}
                <div class="p-3 bg-gray-50 rounded-lg">
                  <h4 class="text-sm font-medium text-gray-900 truncate">{{ assignment.mention_reading.mention.title }}</h4>
                  <p class="text-xs text-gray-600">{{ assignment.mention_reading.show.name }}</p>
                  <div class="flex items-center justify-between mt-2">
                    <span class="text-xs {% if assignment.is_overdue %}
                        
                        
                        
                        text-red-600



                      {% else %}
                        
                        
                        
                        text-gray-500



                      {% endif %}">
                      Due: {{ assignment.due_date|date:'M d, H:i' }}
                    </span>
                    <a href="{% url 'news_reader:assignment_detail' assignment.pk %}" class="text-primary-600 hover:text-primary-700 text-xs">View</a>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <p class="text-gray-500 text-sm">No upcoming deadlines</p>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Recent Articles Section -->
    {% if recent_articles %}
      <div class="mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
              <i class="fa-solid fa-newspaper mr-2 text-orange-600"></i>
              Recent Articles
            </h3>
            <a href="{% url 'news_reader:article_list' %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">View All</a>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {% for article in recent_articles %}
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div class="flex items-start justify-between mb-2">
                    <h4 class="text-sm font-medium text-gray-900 truncate flex-1"><a href="{{ article.get_absolute_url }}" class="hover:text-primary-600">{{ article.title }}</a></h4>
                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ml-2
                      {% if article.status == 'draft' %}
                        bg-gray-100 text-gray-800

                      {% elif article.status == 'in_review' %}
                        bg-yellow-100 text-yellow-800

                      {% elif article.status == 'approved' %}
                        bg-green-100 text-green-800

                      {% elif article.status == 'published' %}
                        bg-blue-100 text-blue-800

                      {% else %}
                        bg-gray-100 text-gray-800
                      {% endif %}">
                      {{ article.get_status_display }}
                    </span>
                  </div>
                  <p class="text-xs text-gray-600 mb-2">{{ article.get_category_display }}</p>
                  <div class="flex items-center justify-between text-xs text-gray-500">
                    <span>{{ article.word_count }} words</span>
                    <span>{{ article.created_at|date:'M d' }}</span>
                  </div>
                  {% if article.is_breaking or article.is_urgent %}
                    <div class="mt-2 flex items-center space-x-1">
                      {% if article.is_breaking %}
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">BREAKING</span>
                      {% endif %}
                      {% if article.is_urgent %}
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">URGENT</span>
                      {% endif %}
                    </div>
                  {% endif %}
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    {% endif %}
  </div>

  <script>
    // Update current time
    function updateCurrentTime() {
      const now = new Date()
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      })
      document.getElementById('current-time').textContent = timeString
    }
    
    // Update time every second
    setInterval(updateCurrentTime, 1000)
    updateCurrentTime()
    
    // Auto-refresh dashboard data every 30 seconds
    setInterval(function () {
      // This could be enhanced to fetch updated stats via AJAX
      location.reload()
    }, 30000)
  </script>
{% endblock %}
