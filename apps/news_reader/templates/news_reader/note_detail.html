{% extends 'base.html' %}
{% load static %}

{% block title %}{{ note.title|default:note.mention.title }} - Reading Note{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">
                    {{ note.title|default:note.mention.title }}
                </h1>
                <p class="text-gray-600">Reading preparation note</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'news_reader:note_list' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Notes
                </a>
                <a href="{% url 'news_reader:note_edit' note.pk %}" 
                   class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Edit Note
                </a>
            </div>
        </div>
    </div>

    <!-- Note Details -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Mention Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fa-solid fa-microphone mr-2 text-blue-600"></i>
                    Mention Details
                </h3>
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Title:</span>
                        <p class="text-gray-900">{{ note.mention.title }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Client:</span>
                        <p class="text-gray-900">{{ note.mention.client.name }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-500">Content:</span>
                        <p class="text-gray-900 bg-gray-50 p-3 rounded-md">{{ note.mention.content }}</p>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Duration:</span>
                            <p class="text-gray-900">{{ note.mention.duration_seconds }} seconds</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Priority:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if note.mention.priority == 4 %}bg-red-100 text-red-800
                                {% elif note.mention.priority == 3 %}bg-orange-100 text-orange-800
                                {% elif note.mention.priority == 2 %}bg-blue-100 text-blue-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ note.mention.get_priority_display }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reading Schedule -->
            {% if note.mention_reading %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fa-solid fa-calendar-day mr-2 text-green-600"></i>
                        Reading Schedule
                    </h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="text-sm font-medium text-gray-500">Show:</span>
                            <p class="text-gray-900">{{ note.mention_reading.show.name }}</p>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-500">Date & Time:</span>
                            <p class="text-gray-900">
                                {{ note.mention_reading.scheduled_date|date:"M d, Y" }} at 
                                {{ note.mention_reading.scheduled_time|time:"H:i" }}
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Note Content -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fa-solid fa-sticky-note mr-2 text-yellow-600"></i>
                    Preparation Notes
                </h3>
                {% if note.content %}
                    <div class="prose max-w-none">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ note.content }}</p>
                    </div>
                {% else %}
                    <p class="text-gray-500 italic">No preparation notes added yet.</p>
                {% endif %}
            </div>

            <!-- Pronunciation Guide -->
            {% if note.pronunciation_guide %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fa-solid fa-volume-high mr-2 text-purple-600"></i>
                        Pronunciation Guide
                    </h3>
                    <div class="prose max-w-none">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ note.pronunciation_guide }}</p>
                    </div>
                </div>
            {% endif %}

            <!-- Personal Reminders -->
            {% if note.personal_reminders %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fa-solid fa-lightbulb mr-2 text-orange-600"></i>
                        Personal Reminders
                    </h3>
                    <div class="prose max-w-none">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ note.personal_reminders }}</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status & Actions -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fa-solid fa-tasks mr-2 text-indigo-600"></i>
                    Status & Actions
                </h3>
                <div class="space-y-4">
                    <div>
                        <span class="text-sm font-medium text-gray-500">Status:</span>
                        <div class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if note.preparation_status == 'completed' %}bg-green-100 text-green-800
                                {% elif note.preparation_status == 'prepared' %}bg-blue-100 text-blue-800
                                {% elif note.preparation_status == 'in_progress' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ note.get_preparation_status_display }}
                            </span>
                        </div>
                    </div>

                    <div>
                        <span class="text-sm font-medium text-gray-500">Priority:</span>
                        <div class="mt-1">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if note.priority == 4 %}bg-red-100 text-red-800
                                {% elif note.priority == 3 %}bg-orange-100 text-orange-800
                                {% elif note.priority == 2 %}bg-blue-100 text-blue-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ note.get_priority_display }}
                            </span>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="pt-4 border-t border-gray-200">
                        <div class="space-y-2">
                            {% if note.preparation_status == 'not_prepared' %}
                                <a href="{% url 'news_reader:start_note_preparation' note.pk %}" 
                                   class="block w-full bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-2 rounded-md text-sm font-medium text-center">
                                    Start Preparation
                                </a>
                            {% elif note.preparation_status == 'in_progress' %}
                                <a href="{% url 'news_reader:mark_note_prepared' note.pk %}" 
                                   class="block w-full bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium text-center">
                                    Mark as Prepared
                                </a>
                            {% elif note.preparation_status == 'prepared' %}
                                <a href="{% url 'news_reader:complete_note_reading' note.pk %}" 
                                   class="block w-full bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium text-center">
                                    Mark as Read
                                </a>
                            {% endif %}

                            <a href="{% url 'news_reader:toggle_note_favorite' note.pk %}" 
                               class="block w-full {% if note.is_favorite %}bg-yellow-600 hover:bg-yellow-700{% else %}bg-gray-600 hover:bg-gray-700{% endif %} text-white px-3 py-2 rounded-md text-sm font-medium text-center">
                                {% if note.is_favorite %}
                                    <i class="fa-solid fa-star mr-1"></i>
                                    Remove from Favorites
                                {% else %}
                                    <i class="fa-regular fa-star mr-1"></i>
                                    Add to Favorites
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Metadata -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fa-solid fa-info-circle mr-2 text-gray-600"></i>
                    Information
                </h3>
                <div class="space-y-3 text-sm">
                    <div>
                        <span class="font-medium text-gray-500">Created:</span>
                        <p class="text-gray-900">{{ note.created_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div>
                        <span class="font-medium text-gray-500">Last Updated:</span>
                        <p class="text-gray-900">{{ note.updated_at|date:"M d, Y H:i" }}</p>
                    </div>
                    {% if note.estimated_prep_time %}
                        <div>
                            <span class="font-medium text-gray-500">Estimated Prep Time:</span>
                            <p class="text-gray-900">{{ note.estimated_prep_time }} minutes</p>
                        </div>
                    {% endif %}
                    {% if note.preparation_started_at %}
                        <div>
                            <span class="font-medium text-gray-500">Preparation Started:</span>
                            <p class="text-gray-900">{{ note.preparation_started_at|date:"M d, Y H:i" }}</p>
                        </div>
                    {% endif %}
                    {% if note.preparation_completed_at %}
                        <div>
                            <span class="font-medium text-gray-500">Preparation Completed:</span>
                            <p class="text-gray-900">{{ note.preparation_completed_at|date:"M d, Y H:i" }}</p>
                        </div>
                    {% endif %}
                    {% if note.preparation_duration %}
                        <div>
                            <span class="font-medium text-gray-500">Preparation Duration:</span>
                            <p class="text-gray-900">{{ note.preparation_duration|floatformat:1 }} minutes</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Flags -->
            {% if note.is_favorite or note.needs_review %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fa-solid fa-flag mr-2 text-red-600"></i>
                        Flags
                    </h3>
                    <div class="space-y-2">
                        {% if note.is_favorite %}
                            <div class="flex items-center">
                                <i class="fa-solid fa-star text-yellow-500 mr-2"></i>
                                <span class="text-sm text-gray-900">Favorite</span>
                            </div>
                        {% endif %}
                        {% if note.needs_review %}
                            <div class="flex items-center">
                                <i class="fa-solid fa-exclamation-triangle text-orange-500 mr-2"></i>
                                <span class="text-sm text-gray-900">Needs Review</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
