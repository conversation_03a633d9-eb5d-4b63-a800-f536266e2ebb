{% extends 'base.html' %}
{% load static %}

{% block title %}End Reading Session{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
            <a href="{% url 'news_reader:dashboard' %}" class="hover:text-primary-600">Dashboard</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <a href="{% url 'news_reader:session_list' %}" class="hover:text-primary-600">Sessions</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <a href="{% url 'news_reader:session_detail' session.pk %}" class="hover:text-primary-600">Session {{ session.pk }}</a>
            <i class="fa-solid fa-chevron-right text-xs"></i>
            <span class="text-gray-900">End Session</span>
        </nav>
        <h1 class="text-2xl font-bold text-gray-900">End Reading Session</h1>
        <p class="text-gray-600">Confirm ending your current reading session.</p>
    </div>

    <!-- Session Summary Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fa-solid fa-clock text-blue-500 text-2xl"></i>
            </div>
            <div class="ml-4 flex-1">
                <h3 class="text-lg font-medium text-gray-900 mb-2">
                    Session Summary
                </h3>
                
                <!-- Session Details -->
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Session Type</dt>
                            <dd class="text-sm text-gray-900 mt-1">{{ session.get_session_type_display }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Started</dt>
                            <dd class="text-sm text-gray-900 mt-1">{{ session.start_time|date:"M d, Y H:i" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Duration</dt>
                            <dd class="text-sm text-gray-900 mt-1">
                                {% if session.duration_minutes %}
                                    {{ session.duration_minutes }} minutes
                                {% else %}
                                    In progress...
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="text-sm text-gray-900 mt-1">
                                {% if session.end_time %}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Completed
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        Active
                                    </span>
                                {% endif %}
                            </dd>
                        </div>
                    </div>
                </div>

                {% if session.notes %}
                    <div class="mb-4">
                        <dt class="text-sm font-medium text-gray-500 mb-1">Session Notes</dt>
                        <dd class="text-sm text-gray-900 bg-gray-50 rounded p-3">{{ session.notes }}</dd>
                    </div>
                {% endif %}

                <!-- Warning if session is already ended -->
                {% if session.end_time %}
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-yellow-800">Session Already Ended</h4>
                                <p class="text-sm text-yellow-700 mt-1">
                                    This session was already ended on {{ session.end_time|date:"M d, Y H:i" }}.
                                </p>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <!-- Confirmation for active session -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fa-solid fa-info-circle text-blue-400"></i>
                            </div>
                            <div class="ml-3">
                                <h4 class="text-sm font-medium text-blue-800">End Active Session</h4>
                                <p class="text-sm text-blue-700 mt-1">
                                    This will mark the session as completed and calculate the final duration.
                                </p>
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Action Form -->
                {% if not session.end_time %}
                    <form method="post" class="space-y-4">
                        {% csrf_token %}
                        
                        <div class="flex items-center justify-between pt-4">
                            <a href="{% url 'news_reader:session_detail' session.pk %}" 
                               class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-2 rounded-lg text-sm font-medium">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg text-sm font-medium">
                                <i class="fa-solid fa-stop mr-2"></i>
                                End Session
                            </button>
                        </div>
                    </form>
                {% else %}
                    <!-- Navigation for completed session -->
                    <div class="flex items-center justify-between pt-4">
                        <a href="{% url 'news_reader:session_list' %}" 
                           class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-2 rounded-lg text-sm font-medium">
                            <i class="fa-solid fa-arrow-left mr-2"></i>
                            Back to Sessions
                        </a>
                        <a href="{% url 'news_reader:session_detail' session.pk %}" 
                           class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg text-sm font-medium">
                            <i class="fa-solid fa-eye mr-2"></i>
                            View Details
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Session Statistics (if available) -->
    {% if session.end_time %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
                <i class="fa-solid fa-chart-bar mr-2 text-green-600"></i>
                Session Statistics
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-gray-900">{{ session.duration_minutes|default:"0" }}</div>
                    <div class="text-sm text-gray-600">Minutes</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-gray-900">{{ session.get_session_type_display }}</div>
                    <div class="text-sm text-gray-600">Session Type</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold text-gray-900">
                        {{ session.start_time|date:"H:i" }} - {{ session.end_time|date:"H:i" }}
                    </div>
                    <div class="text-sm text-gray-600">Time Range</div>
                </div>
            </div>
        </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on the end session button if session is active
    {% if not session.end_time %}
        const endButton = document.querySelector('button[type="submit"]');
        if (endButton) {
            endButton.focus();
        }
    {% endif %}
});
</script>
{% endblock %}
