{% extends 'base.html' %}
{% load static %}

{% block title %}Edit {{ note.title|default:note.mention.title }} - Reading Note{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Edit Reading Note</h1>
                <p class="text-gray-600">Update your preparation note for "{{ note.mention.title }}"</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'news_reader:note_detail' note.pk %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Cancel
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <form method="post" class="p-6 space-y-6">
            {% csrf_token %}
            
            <!-- Note Title -->
            <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.title.label }}
                </label>
                {{ form.title }}
                {% if form.title.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.title.help_text }}</p>
                {% endif %}
                {% if form.title.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.title.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Main Content -->
            <div>
                <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.content.label }}
                </label>
                {{ form.content }}
                {% if form.content.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.content.help_text }}</p>
                {% endif %}
                {% if form.content.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.content.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Pronunciation Guide -->
            <div>
                <label for="{{ form.pronunciation_guide.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.pronunciation_guide.label }}
                </label>
                {{ form.pronunciation_guide }}
                {% if form.pronunciation_guide.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.pronunciation_guide.help_text }}</p>
                {% endif %}
                {% if form.pronunciation_guide.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.pronunciation_guide.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Personal Reminders -->
            <div>
                <label for="{{ form.personal_reminders.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.personal_reminders.label }}
                </label>
                {{ form.personal_reminders }}
                {% if form.personal_reminders.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.personal_reminders.help_text }}</p>
                {% endif %}
                {% if form.personal_reminders.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.personal_reminders.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Status and Priority -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="{{ form.preparation_status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.preparation_status.label }}
                    </label>
                    {{ form.preparation_status }}
                    {% if form.preparation_status.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.preparation_status.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.priority.label }}
                    </label>
                    {{ form.priority }}
                    {% if form.priority.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.priority.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label for="{{ form.estimated_prep_time.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ form.estimated_prep_time.label }}
                    </label>
                    {{ form.estimated_prep_time }}
                    {% if form.estimated_prep_time.help_text %}
                        <p class="mt-1 text-sm text-gray-500">{{ form.estimated_prep_time.help_text }}</p>
                    {% endif %}
                    {% if form.estimated_prep_time.errors %}
                        <div class="mt-1 text-sm text-red-600">
                            {% for error in form.estimated_prep_time.errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Flags -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="flex items-center">
                    {{ form.is_favorite }}
                    <label for="{{ form.is_favorite.id_for_label }}" class="ml-2 text-sm text-gray-700">
                        {{ form.is_favorite.label }}
                    </label>
                    {% if form.is_favorite.help_text %}
                        <p class="ml-2 text-sm text-gray-500">{{ form.is_favorite.help_text }}</p>
                    {% endif %}
                </div>

                <div class="flex items-center">
                    {{ form.needs_review }}
                    <label for="{{ form.needs_review.id_for_label }}" class="ml-2 text-sm text-gray-700">
                        {{ form.needs_review.label }}
                    </label>
                    {% if form.needs_review.help_text %}
                        <p class="ml-2 text-sm text-gray-500">{{ form.needs_review.help_text }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa-solid fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                            <div class="mt-2 text-sm text-red-700">
                                {% for error in form.non_field_errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'news_reader:note_detail' note.pk %}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Update Note
                </button>
            </div>
        </form>
    </div>

    <!-- Mention Information (Read-only) -->
    <div class="mt-6 bg-gray-50 rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i class="fa-solid fa-microphone mr-2 text-blue-600"></i>
            Mention Information
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <span class="text-sm font-medium text-gray-500">Title:</span>
                <p class="text-gray-900">{{ note.mention.title }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-gray-500">Client:</span>
                <p class="text-gray-900">{{ note.mention.client.name }}</p>
            </div>
            <div class="md:col-span-2">
                <span class="text-sm font-medium text-gray-500">Content:</span>
                <p class="text-gray-900 bg-white p-3 rounded-md border">{{ note.mention.content }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
