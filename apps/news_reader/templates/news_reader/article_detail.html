{% extends 'base.html' %}
{% load static %}

{% block title %}{{ article.title }}{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                    <a href="{% url 'news_reader:dashboard' %}" class="hover:text-primary-600">Dashboard</a>
                    <i class="fa-solid fa-chevron-right text-xs"></i>
                    <a href="{% url 'news_reader:article_list' %}" class="hover:text-primary-600">Articles</a>
                    <i class="fa-solid fa-chevron-right text-xs"></i>
                    <span class="text-gray-900">{{ article.title|truncatechars:50 }}</span>
                </nav>
                <h1 class="text-2xl font-bold text-gray-900">{{ article.title }}</h1>
                <p class="text-gray-600">{{ article.byline }}</p>
            </div>
            <div class="flex items-center space-x-2">
                {% if can_edit %}
                    <a href="{% url 'news_reader:article_editor' article.pk %}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                        <i class="fa-solid fa-edit mr-2"></i>
                        Edit Article
                    </a>
                {% endif %}
                <div class="relative inline-block text-left">
                    <button type="button" class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium flex items-center" id="export-menu-button">
                        <i class="fa-solid fa-download mr-2"></i>
                        Export
                        <i class="fa-solid fa-chevron-down ml-2"></i>
                    </button>
                    <div class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden" id="export-menu">
                        <div class="py-1">
                            <a href="{% url 'news_reader:article_export_pdf' article.pk %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fa-solid fa-file-pdf mr-3 text-red-500"></i>
                                PDF Format
                            </a>
                            <a href="{% url 'news_reader:article_teleprompter' article.pk %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fa-solid fa-tv mr-3 text-blue-500"></i>
                                Teleprompter View
                            </a>
                            <a href="{% url 'news_reader:article_mobile_view' article.pk %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fa-solid fa-mobile-screen mr-3 text-green-500"></i>
                                Mobile View
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Article Content -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <div class="mb-4">
                    <div class="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                        <span>{{ article.created_at|date:"F d, Y" }}</span>
                        <span>•</span>
                        <span>{{ article.get_category_display }}</span>
                        <span>•</span>
                        <span>{{ article.word_count }} words</span>
                        <span>•</span>
                        <span>{{ article.target_length_minutes }}:{{ article.target_length_seconds|stringformat:"02d" }} target</span>
                    </div>
                    
                    <div class="flex items-center space-x-2 mb-4">
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium 
                            {% if article.status == 'draft' %}bg-gray-100 text-gray-800
                            {% elif article.status == 'in_review' %}bg-yellow-100 text-yellow-800
                            {% elif article.status == 'approved' %}bg-green-100 text-green-800
                            {% elif article.status == 'scheduled' %}bg-blue-100 text-blue-800
                            {% elif article.status == 'published' %}bg-purple-100 text-purple-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ article.get_status_display }}
                        </span>
                        
                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium 
                            {% if article.priority == 'low' %}bg-gray-100 text-gray-800
                            {% elif article.priority == 'normal' %}bg-blue-100 text-blue-800
                            {% elif article.priority == 'high' %}bg-orange-100 text-orange-800
                            {% elif article.priority == 'urgent' %}bg-red-100 text-red-800
                            {% elif article.priority == 'breaking' %}bg-red-200 text-red-900
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ article.get_priority_display }}
                        </span>
                        
                        {% if article.is_breaking %}
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                BREAKING
                            </span>
                        {% endif %}
                        
                        {% if article.is_urgent %}
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                                URGENT
                            </span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="prose max-w-none">
                    {{ article.content|linebreaks }}
                </div>
            </div>

            <!-- Revisions -->
            {% if revisions %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Revisions</h3>
                    <div class="space-y-3">
                        {% for revision in revisions %}
                            <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">
                                        Version {{ revision.revision_number }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ revision.revised_by.get_full_name|default:revision.revised_by.username }} • 
                                        {{ revision.created_at|date:"M d, Y H:i" }}
                                    </div>
                                    {% if revision.revision_notes %}
                                        <div class="text-sm text-gray-600 mt-1">{{ revision.revision_notes }}</div>
                                    {% endif %}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ revision.word_count }} words
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Article Metadata -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Article Details</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Slug</dt>
                        <dd class="text-sm text-gray-900 font-mono">{{ article.slug }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Author</dt>
                        <dd class="text-sm text-gray-900">{{ article.author.get_full_name|default:article.author.username }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Voice Tone</dt>
                        <dd class="text-sm text-gray-900">{{ article.get_voice_tone_display }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Reading Speed</dt>
                        <dd class="text-sm text-gray-900">{{ article.reading_speed }}/5</dd>
                    </div>
                    {% if article.scheduled_date %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Scheduled Date</dt>
                            <dd class="text-sm text-gray-900">{{ article.scheduled_date|date:"F d, Y" }}</dd>
                        </div>
                    {% endif %}
                    {% if article.last_edited_by %}
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Last Edited By</dt>
                            <dd class="text-sm text-gray-900">{{ article.last_edited_by.get_full_name|default:article.last_edited_by.username }}</dd>
                        </div>
                    {% endif %}
                </dl>
            </div>

            <!-- Air Times -->
            {% if article.air_times %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Air Times</h3>
                    <div class="flex flex-wrap gap-2">
                        {% for time in article.air_times %}
                            <span class="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded">{{ time }}</span>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Pronunciation Guide -->
            {% if article.pronunciation_guide %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Pronunciation Guide</h3>
                    <div class="space-y-2">
                        {% for word, pronunciation in article.pronunciation_guide.items %}
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-900">{{ word }}</span>
                                <span class="text-sm text-gray-600">{{ pronunciation }}</span>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}

            <!-- Assignments -->
            {% if assignments %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Assignments</h3>
                    <div class="space-y-3">
                        {% for assignment in assignments %}
                            <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ assignment.user.get_full_name|default:assignment.user.username }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        Due: {{ assignment.due_date|date:"M d, Y H:i" }}
                                    </div>
                                </div>
                                <div>
                                    {% if assignment.is_completed %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Completed
                                        </span>
                                    {% elif assignment.is_overdue %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Overdue
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            Pending
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    {% if can_edit %}
                        <div class="mt-4">
                            <a href="{% url 'news_reader:article_assign' article.pk %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                                <i class="fa-solid fa-plus mr-1"></i>
                                Assign to News Readers
                            </a>
                        </div>
                    {% endif %}
                </div>
            {% elif can_edit %}
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Assignments</h3>
                    <p class="text-sm text-gray-500 mb-4">No assignments yet.</p>
                    <a href="{% url 'news_reader:article_assign' article.pk %}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                        <i class="fa-solid fa-plus mr-1"></i>
                        Assign to News Readers
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Export menu toggle
    const exportButton = document.getElementById('export-menu-button');
    const exportMenu = document.getElementById('export-menu');
    
    if (exportButton && exportMenu) {
        exportButton.addEventListener('click', function() {
            exportMenu.classList.toggle('hidden');
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!exportButton.contains(event.target) && !exportMenu.contains(event.target)) {
                exportMenu.classList.add('hidden');
            }
        });
    }
});
</script>
{% endblock %}
