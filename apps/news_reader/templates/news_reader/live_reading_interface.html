{% extends 'base.html' %}
{% load static %}

{% block title %}
  Live Reading Interface
{% endblock %}

{% block extra_css %}
  <style>
    /* Live interface specific styles */
    .live-interface {
      background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
      color: white;
    }
    
    .mention-card {
      transition: all 0.3s ease;
      border-left: 4px solid transparent;
    }
    
    .mention-card.current {
      border-left-color: #ef4444;
      background: rgba(239, 68, 68, 0.1);
    }
    
    .mention-card.upcoming {
      border-left-color: #3b82f6;
      background: rgba(59, 130, 246, 0.1);
    }
    
    .time-display {
      font-family: 'Courier New', monospace;
      font-size: 2rem;
      font-weight: bold;
    }
    
    .countdown {
      font-family: 'Courier New', monospace;
      font-weight: bold;
    }
    
    @media (max-width: 768px) {
      .time-display {
        font-size: 1.5rem;
      }
    
      .mention-card {
        margin-bottom: 1rem;
      }
    }
  </style>
{% endblock %}

{% block content %}
  <div class="min-h-screen bg-gray-900">
    <!-- Live Header -->
    <div class="live-interface p-4 shadow-lg">
      <div class="max-w-7xl mx-auto">
        <div class="flex flex-col md:flex-row items-center justify-between">
          <div class="flex items-center space-x-4 mb-4 md:mb-0">
            <div class="flex items-center">
              <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse mr-2"></div>
              <h1 class="text-xl md:text-2xl font-bold">LIVE READING</h1>
            </div>
            <div class="time-display" id="current-time">{{ current_time|time:'H:i:s' }}</div>
          </div>

          <div class="flex items-center space-x-4">
            <!-- Weather Widget -->
            <div class="bg-white bg-opacity-20 rounded-lg p-3 text-center" id="weather-widget">
              <div class="text-sm opacity-75">Weather</div>
              <div class="font-medium" id="weather-temp">--°C</div>
              <div class="text-xs opacity-75" id="weather-desc">Loading...</div>
            </div>

            <a href="{% url 'news_reader:dashboard' %}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg text-sm font-medium">Exit Live</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto p-4">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Current Mentions -->
        <div class="bg-white rounded-lg shadow-lg">
          <div class="bg-red-600 text-white px-6 py-4 rounded-t-lg">
            <h2 class="text-lg font-semibold flex items-center">
              <i class="fa-solid fa-microphone mr-2"></i>
              Current Mentions
              <span class="ml-auto text-sm bg-red-700 px-2 py-1 rounded">NOW</span>
            </h2>
          </div>
          <div class="p-6">
            <div id="current-mentions" class="space-y-4">
              {% for mention in current_mentions %}
                <div class="mention-card current bg-white border border-gray-200 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-gray-900">{{ mention.mention.title }}</h3>
                        <span class="text-sm text-gray-500">{{ mention.scheduled_time|time:'H:i' }}</span>
                      </div>
                      <p class="text-sm text-gray-600 mb-2">{{ mention.show.name }}</p>
                      <div class="bg-gray-50 p-3 rounded-md mb-3">
                        <p class="text-gray-900">{{ mention.mention.content }}</p>
                      </div>

                      <!-- Reading Note Preview -->
                      {% if mention.user_note %}
                        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-3">
                          <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-yellow-800">Preparation Notes</span>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                              {% if mention.user_note.preparation_status == 'completed' %}
                                
                                bg-green-100 text-green-800


                              {% elif mention.user_note.preparation_status == 'prepared' %}
                                
                                bg-blue-100 text-blue-800


                              {% elif mention.user_note.preparation_status == 'in_progress' %}
                                
                                bg-yellow-100 text-yellow-800


                              {% else %}
                                
                                bg-gray-100 text-gray-800

                              {% endif %}">
                              {{ mention.user_note.get_preparation_status_display }}
                            </span>
                          </div>
                          {% if mention.user_note.content %}
                            <p class="text-sm text-yellow-900">{{ mention.user_note.content|truncatechars:100 }}</p>
                          {% endif %}
                          {% if mention.user_note.pronunciation_guide %}
                            <div class="mt-2">
                              <span class="text-xs font-medium text-yellow-700">Pronunciation:</span>
                              <p class="text-xs text-yellow-800">{{ mention.user_note.pronunciation_guide|truncatechars:80 }}</p>
                            </div>
                          {% endif %}
                        </div>
                      {% endif %}

                      <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>Duration: {{ mention.mention.duration_seconds }}s</span>
                        <span>Client: {{ mention.mention.client.name }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="mt-4 flex items-center justify-end space-x-2">
                    <button onclick="markAsRead({{ mention.id }})" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">Mark as Read</button>
                  </div>
                </div>
              {% empty %}
                <div class="text-center py-8">
                  <i class="fa-solid fa-clock text-gray-400 text-3xl mb-4"></i>
                  <p class="text-gray-500">No current mentions</p>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>

        <!-- Upcoming Mentions -->
        <div class="bg-white rounded-lg shadow-lg">
          <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg">
            <h2 class="text-lg font-semibold flex items-center">
              <i class="fa-solid fa-clock mr-2"></i>
              Upcoming Mentions
              <span class="ml-auto text-sm bg-blue-700 px-2 py-1 rounded">NEXT</span>
            </h2>
          </div>
          <div class="p-6">
            <div id="upcoming-mentions" class="space-y-4">
              {% for mention in upcoming_mentions %}
                <div class="mention-card upcoming bg-white border border-gray-200 rounded-lg p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-2">
                        <h3 class="font-semibold text-gray-900">{{ mention.mention.title }}</h3>
                        <div class="text-right">
                          <div class="text-sm text-gray-500">{{ mention.scheduled_time|time:'H:i' }}</div>
                          <div class="countdown text-xs text-blue-600" data-time="{{ mention.scheduled_time|time:'H:i' }}">
                            <!-- Countdown will be populated by JavaScript -->
                          </div>
                        </div>
                      </div>
                      <p class="text-sm text-gray-600 mb-2">{{ mention.show.name }}</p>
                      <div class="bg-gray-50 p-3 rounded-md mb-3">
                        <p class="text-gray-900">{{ mention.mention.content|truncatechars:150 }}</p>
                      </div>

                      <!-- Reading Note Preview -->
                      {% if mention.user_note %}
                        <div class="bg-blue-50 border border-blue-200 rounded-md p-2 mb-3">
                          <div class="flex items-center justify-between">
                            <span class="text-xs font-medium text-blue-800">Note Available</span>
                            <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium
                              {% if mention.user_note.preparation_status == 'completed' %}
                                bg-green-100 text-green-800

                              {% elif mention.user_note.preparation_status == 'prepared' %}
                                bg-blue-100 text-blue-800

                              {% elif mention.user_note.preparation_status == 'in_progress' %}
                                bg-yellow-100 text-yellow-800

                              {% else %}
                                bg-gray-100 text-gray-800
                              {% endif %}">
                              {{ mention.user_note.get_preparation_status_display }}
                            </span>
                          </div>
                        </div>
                      {% else %}
                        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-2 mb-3">
                          <span class="text-xs text-yellow-800">No preparation note</span>
                        </div>
                      {% endif %}

                      <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>Duration: {{ mention.mention.duration_seconds }}s</span>
                        <span>Client: {{ mention.mention.client.name }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              {% empty %}
                <div class="text-center py-8">
                  <i class="fa-solid fa-calendar-check text-gray-400 text-3xl mb-4"></i>
                  <p class="text-gray-500">No upcoming mentions</p>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Update current time every second
    function updateCurrentTime() {
      const now = new Date()
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      document.getElementById('current-time').textContent = timeString
    }
    
    // Update countdown timers
    function updateCountdowns() {
      const now = new Date()
      const currentTime = now.getHours() * 60 + now.getMinutes()
    
      document.querySelectorAll('.countdown').forEach(function (element) {
        const timeStr = element.getAttribute('data-time')
        const [hours, minutes] = timeStr.split(':').map(Number)
        const targetTime = hours * 60 + minutes
    
        let diff = targetTime - currentTime
        if (diff < 0) diff += 24 * 60 // Next day
    
        const diffHours = Math.floor(diff / 60)
        const diffMinutes = diff % 60
    
        if (diffHours > 0) {
          element.textContent = `in ${diffHours}h ${diffMinutes}m`
        } else {
          element.textContent = `in ${diffMinutes}m`
        }
      })
    }
    
    // Mark mention as read
    function markAsRead(readingId) {
      if (confirm('Mark this mention as read?')) {
        fetch(`/news-reader/live/mark-reading-complete/${readingId}/`, {
          method: 'POST',
          headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
          }
        })
          .then((response) => {
            if (response.ok) {
              location.reload()
            } else {
              alert('Error marking mention as read')
            }
          })
          .catch((error) => {
            console.error('Error:', error)
            alert('Error marking mention as read')
          })
      }
    }
    
    // Load weather data
    function loadWeather() {
      fetch('/live-show/weather/')
        .then((response) => response.json())
        .then((data) => {
          if (data.temperature !== undefined) {
            document.getElementById('weather-temp').textContent = `${Math.round(data.temperature)}°C`
            document.getElementById('weather-desc').textContent = data.description || ''
          }
        })
        .catch((error) => {
          console.error('Weather error:', error)
          document.getElementById('weather-desc').textContent = 'Unavailable'
        })
    }
    
    // Refresh mentions data
    function refreshMentions() {
      // Refresh current mentions
      fetch('/news-reader/live/current-mentions/')
        .then((response) => response.json())
        .then((data) => {
          // Update current mentions (simplified - in production would update DOM)
          console.log('Current mentions updated:', data.mentions.length)
        })
        .catch((error) => console.error('Error refreshing current mentions:', error))
    
      // Refresh upcoming mentions
      fetch('/news-reader/live/upcoming-mentions/')
        .then((response) => response.json())
        .then((data) => {
          // Update upcoming mentions (simplified - in production would update DOM)
          console.log('Upcoming mentions updated:', data.mentions.length)
        })
        .catch((error) => console.error('Error refreshing upcoming mentions:', error))
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function () {
      // Update time immediately and then every second
      updateCurrentTime()
      setInterval(updateCurrentTime, 1000)
    
      // Update countdowns every minute
      updateCountdowns()
      setInterval(updateCountdowns, 60000)
    
      // Load weather
      loadWeather()
      setInterval(loadWeather, 300000) // Every 5 minutes
    
      // Refresh mentions every 30 seconds
      setInterval(refreshMentions, 30000)
    
      // Add CSRF token for AJAX requests
      const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')
      if (!csrfToken) {
        const token = document.createElement('input')
        token.type = 'hidden'
        token.name = 'csrfmiddlewaretoken'
        token.value = '{{ csrf_token }}'
        document.body.appendChild(token)
      }
    })
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function (e) {
      // Escape key to exit live mode
      if (e.key === 'Escape') {
        if (confirm('Exit live reading mode?')) {
          window.location.href = '{% url "news_reader:dashboard" %}'
        }
      }
    
      // F5 to refresh
      if (e.key === 'F5') {
        e.preventDefault()
        location.reload()
      }
    })
    
    // Prevent accidental page navigation
    window.addEventListener('beforeunload', function (e) {
      e.preventDefault()
      e.returnValue = ''
    })
  </script>
{% endblock %}
