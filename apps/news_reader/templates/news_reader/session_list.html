{% extends 'base.html' %}
{% load static %}

{% block title %}Reading Sessions{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Reading Sessions</h1>
                <p class="text-gray-600">Track your preparation and reading sessions.</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'news_reader:start_session' %}" 
                   class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center">
                    <i class="fa-solid fa-play mr-2"></i>
                    Start Session
                </a>
            </div>
        </div>
    </div>

    <!-- Sessions List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        {% if page_obj %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Session</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for session in page_obj %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ session.start_time|date:"M d, Y" }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            Started at {{ session.start_time|time:"H:i" }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if session.session_type == 'live_reading' %}bg-red-100 text-red-800
                                        {% elif session.session_type == 'preparation' %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ session.get_session_type_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {% if session.end_time %}
                                        {{ session.duration.total_seconds|floatformat:0 }}s
                                    {% else %}
                                        <span class="text-green-600 font-medium">Active</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <div>Prepared: {{ session.mentions_prepared }}</div>
                                        <div>Read: {{ session.mentions_read }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if session.is_active %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <div class="w-2 h-2 bg-green-600 rounded-full mr-1 animate-pulse"></div>
                                            Active
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Completed
                                        </span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <a href="{% url 'news_reader:session_detail' session.pk %}" 
                                           class="text-primary-600 hover:text-primary-900">View</a>
                                        {% if session.is_active %}
                                            <a href="{% url 'news_reader:end_session' session.pk %}" 
                                               class="text-red-600 hover:text-red-900">End</a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" 
                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to 
                                <span class="font-medium">{{ page_obj.end_index }}</span> of 
                                <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fa-solid fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">
                                            {{ num }}
                                        </span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?page={{ num }}" 
                                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ num }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fa-solid fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <i class="fa-solid fa-clock text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No sessions found</h3>
                <p class="text-gray-500 mb-4">Start your first reading session to track your performance.</p>
                <a href="{% url 'news_reader:start_session' %}" 
                   class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Start Session
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
