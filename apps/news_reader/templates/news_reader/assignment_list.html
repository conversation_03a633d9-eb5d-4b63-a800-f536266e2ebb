{% extends 'base.html' %}
{% load static %}

{% block title %}My Assignments{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Assignments</h1>
                <p class="text-gray-600">Manage your reading assignments and deadlines.</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'news_reader:dashboard' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
        <form method="get" class="flex flex-wrap items-center gap-4">
            <div class="flex items-center space-x-2">
                <label for="status" class="text-sm font-medium text-gray-700">Status:</label>
                <select name="status" id="status" class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                    <option value="">All Assignments</option>
                    <option value="pending" {% if current_filters.status == 'pending' %}selected{% endif %}>Pending</option>
                    <option value="completed" {% if current_filters.status == 'completed' %}selected{% endif %}>Completed</option>
                    <option value="overdue" {% if current_filters.status == 'overdue' %}selected{% endif %}>Overdue</option>
                </select>
            </div>
            
            <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-1 rounded-md text-sm">
                Filter
            </button>
            
            <a href="{% url 'news_reader:assignment_list' %}" class="text-gray-600 hover:text-gray-800 text-sm">
                Clear Filters
            </a>
        </form>
    </div>

    <!-- Assignments List -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        {% if page_obj %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mention</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Show</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scheduled</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for assignment in page_obj %}
                            <tr class="hover:bg-gray-50 {% if assignment.is_overdue %}bg-red-50{% endif %}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ assignment.mention_reading.mention.title }}
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ assignment.mention_reading.mention.client.name }}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ assignment.mention_reading.show.name }}</div>
                                    <div class="text-sm text-gray-500">{{ assignment.mention_reading.mention.duration_seconds }}s</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        {{ assignment.mention_reading.scheduled_date|date:"M d, Y" }}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        {{ assignment.mention_reading.scheduled_time|time:"H:i" }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm {% if assignment.is_overdue %}text-red-600 font-medium{% else %}text-gray-900{% endif %}">
                                        {{ assignment.due_date|date:"M d, Y" }}
                                    </div>
                                    <div class="text-sm {% if assignment.is_overdue %}text-red-500{% else %}text-gray-500{% endif %}">
                                        {{ assignment.due_date|time:"H:i" }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if assignment.is_completed %}bg-green-100 text-green-800
                                        {% elif assignment.is_overdue %}bg-red-100 text-red-800
                                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                        {% if assignment.is_completed %}
                                            Completed
                                        {% elif assignment.is_overdue %}
                                            Overdue
                                        {% else %}
                                            Pending
                                        {% endif %}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% with note=assignment.mention_reading.reading_notes.first %}
                                        {% if note %}
                                            <div class="flex items-center">
                                                <div class="flex-1">
                                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                                        <div class="h-2 rounded-full 
                                                            {% if note.preparation_status == 'completed' %}bg-green-600 w-full
                                                            {% elif note.preparation_status == 'prepared' %}bg-blue-600 w-3/4
                                                            {% elif note.preparation_status == 'in_progress' %}bg-yellow-600 w-1/2
                                                            {% else %}bg-gray-400 w-1/4{% endif %}">
                                                        </div>
                                                    </div>
                                                </div>
                                                <span class="ml-2 text-xs text-gray-500">
                                                    {{ note.get_preparation_status_display }}
                                                </span>
                                            </div>
                                        {% else %}
                                            <span class="text-xs text-gray-400">No note</span>
                                        {% endif %}
                                    {% endwith %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <a href="{% url 'news_reader:assignment_detail' assignment.pk %}" 
                                           class="text-primary-600 hover:text-primary-900">View</a>
                                        {% if not assignment.is_completed %}
                                            <a href="{% url 'news_reader:create_note_for_reading' assignment.mention_reading.pk %}" 
                                               class="text-green-600 hover:text-green-900">Prepare</a>
                                            <a href="{% url 'news_reader:complete_assignment' assignment.pk %}" 
                                               class="text-blue-600 hover:text-blue-900">Complete</a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" 
                               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to 
                                <span class="font-medium">{{ page_obj.end_index }}</span> of 
                                <span class="font-medium">{{ page_obj.paginator.count }}</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fa-solid fa-chevron-left"></i>
                                    </a>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-primary-50 text-sm font-medium text-primary-600">
                                            {{ num }}
                                        </span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?page={{ num }}" 
                                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                            {{ num }}
                                        </a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}" 
                                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <i class="fa-solid fa-chevron-right"></i>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <i class="fa-solid fa-tasks text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No assignments found</h3>
                <p class="text-gray-500 mb-4">You don't have any reading assignments yet.</p>
                <a href="{% url 'news_reader:dashboard' %}" 
                   class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Go to Dashboard
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
// Auto-refresh assignments every 60 seconds
setInterval(function() {
    // Only refresh if user is still on the page
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 60000);
</script>
{% endblock %}
