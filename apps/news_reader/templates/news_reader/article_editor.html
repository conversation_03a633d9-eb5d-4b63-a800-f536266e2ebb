{% extends 'base.html' %}
{% load static %}

{% block title %}{{ article.title }} - Article Editor{% endblock %}

{% block extra_css %}
<style>
    ::-webkit-scrollbar { display: none; }

    .word-count-chart {
        height: 40px;
        width: 100%;
    }

    .highlight-word {
        background-color: rgba(59, 130, 246, 0.1);
        border-radius: 2px;
    }

    .time-marker {
        position: relative;
        background-color: rgba(34, 197, 94, 0.1);
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 0.8em;
        color: #059669;
    }

    .time-marker:after {
        content: "";
        position: absolute;
        height: 12px;
        width: 1px;
        background-color: #d1d5db;
        bottom: -16px;
        left: 50%;
    }

    html, body {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    /* Full-screen editor styles */
    .editor-fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        background: #fafafa;
    }
</style>
{% endblock %}

{% block content %}
<div class="h-screen flex flex-col" id="editor-container">
    <!-- Header -->
    <header class="bg-white border-b border-neutral-200 shadow-sm">
        <div class="flex items-center justify-between px-4 py-2">
            <div class="flex items-center">
                <a href="{% url 'news_reader:article_detail' article.pk %}" class="p-2 text-neutral-600 hover:text-primary-600 mr-2">
                    <i class="fa-solid fa-arrow-left"></i>
                </a>
                <div class="flex items-center">
                    <span class="font-semibold text-neutral-800 mr-1">NewsRadio</span>
                    <span class="text-neutral-400">|</span>
                    <span class="ml-1 text-neutral-600">{{ article.title|truncatechars:30 }}</span>
                </div>
            </div>
            <div class="flex items-center">
                <div class="hidden md:flex items-center mr-4">
                    <span class="text-xs text-neutral-500 mr-2">Last saved: <span id="last-saved">Never</span></span>
                    <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full" id="save-status">Auto-saving</span>
                </div>
                <button class="p-2 text-neutral-600 hover:text-primary-600 mr-2" onclick="previewArticle()">
                    <i class="fa-solid fa-eye"></i>
                </button>
                <button class="p-2 text-neutral-600 hover:text-primary-600 mr-2" onclick="shareArticle()">
                    <i class="fa-solid fa-share-nodes"></i>
                </button>
                <button class="hidden md:flex items-center px-4 py-1.5 bg-primary-600 hover:bg-primary-700 text-white rounded text-sm font-medium" onclick="goLive()">
                    <i class="fa-solid fa-microphone mr-2"></i>
                    On Air
                </button>
                <button class="md:hidden p-2 text-neutral-600 hover:text-primary-600" onclick="goLive()">
                    <i class="fa-solid fa-microphone"></i>
                </button>
            </div>
        </div>
        <div class="flex items-center px-4 py-1 bg-white border-t border-neutral-100 text-sm">
            <button class="flex items-center text-neutral-600 hover:text-primary-600 mr-4" onclick="saveArticle()">
                <i class="fa-solid fa-floppy-disk mr-1.5"></i>
                Save
            </button>
            <button class="flex items-center text-neutral-600 hover:text-primary-600 mr-4" onclick="toggleEditMode()">
                <i class="fa-solid fa-pen-to-square mr-1.5"></i>
                Edit
            </button>
            <button class="flex items-center text-neutral-600 hover:text-primary-600 mr-4" onclick="showFormatting()">
                <i class="fa-solid fa-font mr-1.5"></i>
                Format
            </button>
            <button class="flex items-center text-neutral-600 hover:text-primary-600 mr-4" onclick="showTiming()">
                <i class="fa-solid fa-clock mr-1.5"></i>
                Timing
            </button>
            <button class="flex items-center text-neutral-600 hover:text-primary-600" onclick="showSettings()">
                <i class="fa-solid fa-gear mr-1.5"></i>
                Settings
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex flex-1 overflow-hidden">
        <!-- Editor Area -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Toolbar -->
            <div class="flex items-center justify-between px-4 py-2 bg-white border-b border-neutral-200">
                <div class="flex items-center">
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1" onclick="formatText('bold')">
                        <i class="fa-solid fa-bold"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1" onclick="formatText('italic')">
                        <i class="fa-solid fa-italic"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1" onclick="formatText('underline')">
                        <i class="fa-solid fa-underline"></i>
                    </button>
                    <span class="mx-2 text-neutral-300">|</span>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1" onclick="formatText('insertUnorderedList')">
                        <i class="fa-solid fa-list"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1" onclick="formatText('insertOrderedList')">
                        <i class="fa-solid fa-list-ol"></i>
                    </button>
                    <span class="mx-2 text-neutral-300">|</span>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1" onclick="insertLink()">
                        <i class="fa-solid fa-link"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1" onclick="formatText('formatBlock', 'blockquote')">
                        <i class="fa-solid fa-quote-left"></i>
                    </button>
                    <span class="mx-2 text-neutral-300">|</span>
                    <button class="flex items-center px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200" onclick="addNote()">
                        <i class="fa-solid fa-comment-dots mr-1"></i>
                        Add Note
                    </button>
                </div>
                <div class="flex items-center">
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600 mr-1" onclick="undo()">
                        <i class="fa-solid fa-undo"></i>
                    </button>
                    <button class="p-1.5 text-neutral-600 hover:text-primary-600" onclick="redo()">
                        <i class="fa-solid fa-redo"></i>
                    </button>
                </div>
            </div>

            <!-- Article Editor -->
            <div class="flex-1 overflow-y-auto bg-white p-8 mx-auto max-w-3xl">
                <div class="mb-6">
                    <h1 contenteditable="true" class="text-3xl font-bold mb-2 outline-none" id="article-title">{{ article.title }}</h1>
                    <div class="flex items-center text-sm text-neutral-500 mb-4">
                        <span class="mr-3">{{ article.byline }}</span>
                        <span>{{ article.created_at|date:"F d, Y" }}</span>
                    </div>
                    <div class="flex items-center text-xs bg-neutral-100 rounded-md p-2 mb-4">
                        {% if article.is_breaking %}
                            <span class="px-2 py-0.5 bg-red-100 text-red-800 rounded mr-2">BREAKING</span>
                        {% endif %}
                        {% if article.is_urgent %}
                            <span class="px-2 py-0.5 bg-orange-100 text-orange-800 rounded mr-2">URGENT</span>
                        {% endif %}
                        <span class="text-neutral-600">
                            {% if article.air_times %}
                                Air at {% for time in article.air_times %}{{ time }}{% if not forloop.last %}, {% endif %}{% endfor %}
                            {% else %}
                                No air times scheduled
                            {% endif %}
                        </span>
                    </div>
                </div>

                <div contenteditable="true" class="outline-none text-lg leading-relaxed" id="article-content">
                    {{ article.content|linebreaks }}
                </div>
            </div>

            <!-- Timing and Word Count Bar -->
            <div class="bg-white border-t border-neutral-200 py-2 px-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex items-center mr-6">
                            <i class="fa-solid fa-stopwatch text-neutral-500 mr-2"></i>
                            <span class="font-mono text-lg font-medium" id="current-time">{{ article.estimated_reading_time|default:"0"|floatformat:"0" }}s</span>
                            <span class="text-xs text-neutral-500 ml-1">/ {{ article.target_length_total_seconds }}s target</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fa-solid fa-font text-neutral-500 mr-2"></i>
                            <span class="font-mono text-lg font-medium" id="word-count">{{ article.word_count }}</span>
                            <span class="text-xs text-neutral-500 ml-1">words</span>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <button class="flex items-center justify-center px-3 py-1.5 bg-neutral-100 hover:bg-neutral-200 text-neutral-700 rounded-l-md text-sm" onclick="testRead()">
                            <i class="fa-solid fa-play mr-1.5"></i>
                            Test Read
                        </button>
                        <button class="flex items-center justify-center px-3 py-1.5 bg-neutral-100 hover:bg-neutral-200 text-neutral-700 border-l border-neutral-200 rounded-r-md text-sm" onclick="recordAudio()">
                            <i class="fa-solid fa-microphone-lines"></i>
                        </button>
                    </div>
                </div>
                <div class="mt-2 word-count-chart" id="reading-pace-chart"></div>
            </div>
        </div>

        <!-- Right Sidebar -->
        <div class="w-80 bg-white border-l border-neutral-200 overflow-y-auto">
            <!-- Story Metadata -->
            <div class="border-b border-neutral-200">
                <div class="p-4">
                    <h3 class="font-medium text-neutral-800 mb-3">Story Metadata</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs text-neutral-500 mb-1">Slug</label>
                            <input type="text" value="{{ article.slug }}" id="article-slug" class="w-full px-3 py-1.5 border border-neutral-300 rounded text-sm">
                        </div>
                        <div>
                            <label class="block text-xs text-neutral-500 mb-1">Category</label>
                            <select class="w-full px-3 py-1.5 border border-neutral-300 rounded text-sm bg-white" id="article-category">
                                <option value="breaking_news"{% if article.category == 'breaking_news' %} selected{% endif %}>Breaking News</option>
                                <option value="weather"{% if article.category == 'weather' %} selected{% endif %}>Weather</option>
                                <option value="politics"{% if article.category == 'politics' %} selected{% endif %}>Politics</option>
                                <option value="business"{% if article.category == 'business' %} selected{% endif %}>Business</option>
                                <option value="health"{% if article.category == 'health' %} selected{% endif %}>Health</option>
                                <option value="sports"{% if article.category == 'sports' %} selected{% endif %}>Sports</option>
                                <option value="entertainment"{% if article.category == 'entertainment' %} selected{% endif %}>Entertainment</option>
                                <option value="technology"{% if article.category == 'technology' %} selected{% endif %}>Technology</option>
                                <option value="local"{% if article.category == 'local' %} selected{% endif %}>Local News</option>
                                <option value="international"{% if article.category == 'international' %} selected{% endif %}>International</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-xs text-neutral-500 mb-1">Target Length</label>
                            <div class="flex">
                                <input type="text" value="{{ article.target_length_minutes }}:{{ article.target_length_seconds|stringformat:'02d' }}" id="target-length" class="w-1/2 px-3 py-1.5 border border-neutral-300 rounded-l text-sm">
                                <select class="w-1/2 px-3 py-1.5 border border-neutral-300 border-l-0 rounded-r text-sm bg-white">
                                    <option>minutes</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label class="block text-xs text-neutral-500 mb-1">Air Times</label>
                            <div class="flex flex-wrap gap-2" id="air-times-container">
                                {% for time in article.air_times %}
                                    <span class="inline-block px-2 py-1 bg-neutral-100 text-neutral-700 text-xs rounded">{{ time }}</span>
                                {% endfor %}
                                <button class="inline-block px-2 py-1 border border-dashed border-neutral-300 text-neutral-500 text-xs rounded hover:bg-neutral-50" onclick="addAirTime()">
                                    <i class="fa-solid fa-plus text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Formatting Tools -->
            <div class="border-b border-neutral-200">
                <div class="p-4">
                    <h3 class="font-medium text-neutral-800 mb-3">Formatting Tools</h3>
                    <div class="space-y-3">
                        <div class="flex flex-wrap gap-2">
                            <button class="px-3 py-1.5 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200" onclick="addEmphasis()">
                                Emphasis
                            </button>
                            <button class="px-3 py-1.5 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200" onclick="addPause()">
                                Pause
                            </button>
                            <button class="px-3 py-1.5 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200" onclick="addBreath()">
                                Breath
                            </button>
                            <button class="px-3 py-1.5 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200" onclick="addTimeMarker()">
                                Time Marker
                            </button>
                        </div>
                        <div class="mt-3">
                            <label class="block text-xs text-neutral-500 mb-1">Reading Speed</label>
                            <div class="flex items-center">
                                <span class="text-xs text-neutral-600 mr-2">Slow</span>
                                <input type="range" min="1" max="5" value="{{ article.reading_speed }}" class="flex-1" id="reading-speed">
                                <span class="text-xs text-neutral-600 ml-2">Fast</span>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label class="block text-xs text-neutral-500 mb-1">Voice Tone</label>
                            <select class="w-full px-3 py-1.5 border border-neutral-300 rounded text-sm bg-white" id="voice-tone">
                                <option value="neutral"{% if article.voice_tone == 'neutral' %} selected{% endif %}>Neutral</option>
                                <option value="formal"{% if article.voice_tone == 'formal' %} selected{% endif %}>Formal</option>
                                <option value="urgent"{% if article.voice_tone == 'urgent' %} selected{% endif %}>Urgent</option>
                                <option value="conversational"{% if article.voice_tone == 'conversational' %} selected{% endif %}>Conversational</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pronunciation Guide -->
            <div class="border-b border-neutral-200">
                <div class="p-4">
                    <h3 class="font-medium text-neutral-800 mb-3">Pronunciation Guide</h3>
                    <div id="pronunciation-list">
                        {% for word, pronunciation in article.pronunciation_guide.items %}
                            <div class="mb-3 pronunciation-item">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium">{{ word }}</span>
                                    <button class="text-xs text-primary-600 hover:text-primary-700" onclick="editPronunciation(this)">Edit</button>
                                </div>
                                <div class="text-sm text-neutral-600">{{ pronunciation }}</div>
                            </div>
                        {% endfor %}
                    </div>
                    <button class="flex items-center text-sm text-primary-600 hover:text-primary-700" onclick="addPronunciation()">
                        <i class="fa-solid fa-plus mr-1.5"></i>
                        Add pronunciation
                    </button>
                </div>
            </div>

            <!-- Export Options -->
            <div>
                <div class="p-4">
                    <h3 class="font-medium text-neutral-800 mb-3">Export Options</h3>
                    <div class="space-y-3">
                        <button class="flex items-center justify-between w-full px-3 py-2 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200" onclick="exportPDF()">
                            <span class="flex items-center">
                                <i class="fa-solid fa-file-pdf mr-2 text-red-500"></i>
                                PDF Format
                            </span>
                            <i class="fa-solid fa-arrow-up-right-from-square text-xs"></i>
                        </button>
                        <button class="flex items-center justify-between w-full px-3 py-2 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200" onclick="teleprompterView()">
                            <span class="flex items-center">
                                <i class="fa-solid fa-tv mr-2 text-blue-500"></i>
                                Teleprompter View
                            </span>
                            <i class="fa-solid fa-arrow-up-right-from-square text-xs"></i>
                        </button>
                        <button class="flex items-center justify-between w-full px-3 py-2 bg-neutral-100 text-neutral-700 text-sm rounded hover:bg-neutral-200" onclick="mobileView()">
                            <span class="flex items-center">
                                <i class="fa-solid fa-mobile-screen mr-2 text-green-500"></i>
                                Mobile View
                            </span>
                            <i class="fa-solid fa-arrow-up-right-from-square text-xs"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Hidden form for auto-save -->
<form id="auto-save-form" style="display: none;">
    {% csrf_token %}
    <input type="hidden" name="title" id="form-title">
    <input type="hidden" name="content" id="form-content">
    <input type="hidden" name="pronunciation_guide" id="form-pronunciation">
    <input type="hidden" name="air_times" id="form-air-times">
</form>
{% endblock %}

{% block extra_js %}
<script>
        // Article data
        const articleId = {{ article.pk }};
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        
        // Auto-save functionality
        let autoSaveInterval;
        let hasUnsavedChanges = false;
        
        function startAutoSave() {
            autoSaveInterval = setInterval(autoSave, 30000); // Auto-save every 30 seconds
        }
        
        function autoSave() {
            if (!hasUnsavedChanges) return;
            
            const title = document.getElementById('article-title').textContent;
            const content = document.getElementById('article-content').innerHTML;
            
            fetch(`/news-reader/articles/${articleId}/auto-save/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    title: title,
                    content: content
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('last-saved').textContent = data.last_saved;
                    document.getElementById('word-count').textContent = data.word_count;
                    hasUnsavedChanges = false;
                }
            })
            .catch(error => console.error('Auto-save error:', error));
        }
        
        // Track changes
        document.getElementById('article-title').addEventListener('input', () => hasUnsavedChanges = true);
        document.getElementById('article-content').addEventListener('input', () => hasUnsavedChanges = true);
        
        // Formatting functions
        function formatText(command, value = null) {
            document.execCommand(command, false, value);
            hasUnsavedChanges = true;
        }
        
        function addEmphasis() {
            const selection = window.getSelection();
            if (selection.toString().length > 0) {
                const range = selection.getRangeAt(0);
                const span = document.createElement('span');
                span.className = 'highlight-word';
                range.surroundContents(span);
                hasUnsavedChanges = true;
            }
        }
        
        function addTimeMarker() {
            const selection = window.getSelection();
            if (selection.rangeCount > 0) {
                const range = selection.getRangeAt(0);
                const timeMarker = document.createElement('span');
                timeMarker.className = 'time-marker';
                timeMarker.setAttribute('data-time', '00:00');
                timeMarker.textContent = '[0s]';
                range.collapse(false);
                range.insertNode(timeMarker);
                hasUnsavedChanges = true;
            }
        }
        
        // Export functions
        function exportPDF() {
            window.open(`/news-reader/articles/${articleId}/export/pdf/`, '_blank');
        }
        
        function teleprompterView() {
            window.open(`/news-reader/articles/${articleId}/teleprompter/`, '_blank');
        }
        
        function mobileView() {
            window.open(`/news-reader/articles/${articleId}/mobile/`, '_blank');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            startAutoSave();
        });
        
        // Prevent data loss
        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
{% endblock %}
