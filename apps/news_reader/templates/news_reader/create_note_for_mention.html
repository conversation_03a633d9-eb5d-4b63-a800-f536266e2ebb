{% extends 'base.html' %}
{% load static %}

{% block title %}Create Note for {{ mention.title }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Create Note</h1>
                <p class="text-gray-600">Create a preparation note for "{{ mention.title }}"</p>
            </div>
            <div class="flex items-center space-x-3">
                <a href="{% url 'news_reader:dashboard' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Mention Information -->
    <div class="bg-blue-50 rounded-lg border border-blue-200 p-6 mb-6">
        <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
            <i class="fa-solid fa-microphone mr-2"></i>
            Mention Details
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <span class="text-sm font-medium text-blue-700">Title:</span>
                <p class="text-blue-900">{{ mention.title }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-blue-700">Client:</span>
                <p class="text-blue-900">{{ mention.client.name }}</p>
            </div>
            <div>
                <span class="text-sm font-medium text-blue-700">Duration:</span>
                <p class="text-blue-900">{{ mention.duration_seconds }} seconds</p>
            </div>
            <div>
                <span class="text-sm font-medium text-blue-700">Priority:</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                    {% if mention.priority == 4 %}bg-red-100 text-red-800
                    {% elif mention.priority == 3 %}bg-orange-100 text-orange-800
                    {% elif mention.priority == 2 %}bg-blue-100 text-blue-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ mention.get_priority_display }}
                </span>
            </div>
            <div class="md:col-span-2">
                <span class="text-sm font-medium text-blue-700">Content:</span>
                <p class="text-blue-900 bg-white p-3 rounded-md border border-blue-200 mt-1">{{ mention.content }}</p>
            </div>
        </div>
    </div>

    <!-- Quick Note Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <form method="post" class="p-6 space-y-6">
            {% csrf_token %}
            
            <!-- Note Content -->
            <div>
                <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.content.label }}
                </label>
                {{ form.content }}
                {% if form.content.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ form.content.help_text }}</p>
                {% endif %}
                {% if form.content.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.content.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Priority -->
            <div>
                <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    {{ form.priority.label }}
                </label>
                {{ form.priority }}
                {% if form.priority.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {% for error in form.priority.errors %}
                            <p>{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <!-- Form Errors -->
            {% if form.non_field_errors %}
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa-solid fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Please correct the following errors:</h3>
                            <div class="mt-2 text-sm text-red-700">
                                {% for error in form.non_field_errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{% url 'news_reader:dashboard' %}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium">
                    Create Note
                </button>
            </div>
        </form>
    </div>

    <!-- Help Text -->
    <div class="mt-6 bg-gray-50 rounded-lg border border-gray-200 p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fa-solid fa-info-circle text-gray-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-800">Quick Note</h3>
                <div class="mt-2 text-sm text-gray-600">
                    <p>This creates a basic preparation note. You can add more detailed information like pronunciation guides and personal reminders by editing the note after creation.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
