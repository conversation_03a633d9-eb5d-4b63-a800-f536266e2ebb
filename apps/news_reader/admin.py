from django.contrib import admin
from .models import ReadingNote, ReadingSession, NewsReaderAssignment


@admin.register(ReadingNote)
class ReadingNoteAdmin(admin.ModelAdmin):
    list_display = ['user', 'mention', 'preparation_status', 'priority', 'created_at', 'is_favorite']
    list_filter = ['preparation_status', 'priority', 'is_favorite', 'needs_review', 'created_at']
    search_fields = ['user__username', 'mention__title', 'content', 'title']
    readonly_fields = ['created_at', 'updated_at', 'preparation_duration']
    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'mention', 'mention_reading', 'title')
        }),
        ('Note Content', {
            'fields': ('content', 'pronunciation_guide', 'personal_reminders')
        }),
        ('Status & Priority', {
            'fields': ('preparation_status', 'priority', 'is_favorite', 'needs_review')
        }),
        ('Time Tracking', {
            'fields': ('estimated_prep_time', 'actual_prep_time', 'preparation_started_at',
                      'preparation_completed_at', 'last_reviewed_at')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'mention', 'mention_reading')


@admin.register(ReadingSession)
class ReadingSessionAdmin(admin.ModelAdmin):
    list_display = ['user', 'session_type', 'start_time', 'end_time', 'mentions_prepared', 'mentions_read', 'is_active']
    list_filter = ['session_type', 'start_time', 'end_time']
    search_fields = ['user__username', 'notes']
    readonly_fields = ['session_id', 'duration', 'is_active']
    fieldsets = (
        ('Session Information', {
            'fields': ('user', 'session_id', 'session_type', 'start_time', 'end_time')
        }),
        ('Performance Metrics', {
            'fields': ('mentions_prepared', 'mentions_read', 'total_reading_time')
        }),
        ('Notes', {
            'fields': ('notes',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(NewsReaderAssignment)
class NewsReaderAssignmentAdmin(admin.ModelAdmin):
    list_display = ['user', 'mention_reading', 'assigned_by', 'due_date', 'is_active', 'is_completed', 'is_overdue']
    list_filter = ['is_active', 'is_completed', 'assigned_at', 'due_date']
    search_fields = ['user__username', 'mention_reading__mention__title', 'assignment_notes']
    readonly_fields = ['assigned_at', 'completed_at', 'is_overdue']
    fieldsets = (
        ('Assignment Details', {
            'fields': ('user', 'mention_reading', 'assigned_by', 'due_date')
        }),
        ('Status', {
            'fields': ('is_active', 'is_completed', 'completed_at')
        }),
        ('Notes', {
            'fields': ('assignment_notes',)
        }),
        ('Timestamps', {
            'fields': ('assigned_at',),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'mention_reading', 'assigned_by')

    def is_overdue(self, obj):
        return obj.is_overdue
    is_overdue.boolean = True
    is_overdue.short_description = 'Overdue'
