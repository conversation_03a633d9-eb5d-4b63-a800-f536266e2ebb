# Production Deployment Files

This document lists the essential files needed for production deployment using `scripts/docker-build.sh`.

## Essential Production Files

### Core Deployment Files
- `scripts/docker-build.sh` - Main production deployment script
- `docker-compose.yml` - Docker Compose configuration for production
- `Dockerfile` - Container build instructions
- `docker-entrypoint.sh` - Container startup script

### Configuration Files
- `gunicorn.conf.py` - Production WSGI server configuration
- `radio_mentions_project/settings_production.py` - Production Django settings
- `requirements-prod.txt` - Production Python dependencies
- `requirements.txt` - Base Python dependencies

### Nginx Configuration
- `docker/nginx/nginx.conf` - Main nginx configuration
- `docker/nginx/default.conf` - Site-specific nginx configuration

### Application Files
- `manage.py` - Django management script
- `radio_mentions_project/` - Django project directory
- `apps/` - Django applications
- `templates/` - Django templates
- `static/` - Static files
- `media/` - Media files directory

### Database & Storage
- `docker/postgres/` - PostgreSQL configuration
- `backups/` - Database backup directory
- `logs/` - Application logs directory

## Removed Files

The following files were removed as they are not connected to the production deployment:

### Development & Alternative Deployment Files
- `docker-compose.dev.yml` - Development compose file
- `deploy_production.sh` - Alternative deployment script
- `radiomention.service` - Systemd service file
- `nginx_radiomention.conf` - Alternative nginx config

### Documentation Files
- `DOCKER_DEPLOYMENT.md`
- `DOCKER_NPM_SETUP.md`
- `DOCKER_TROUBLESHOOTING.md`
- `DOCKER_UPDATE_SUMMARY.md`
- `OPTIMIZATION_SUMMARY.md`
- `PRODUCTION_DEPLOYMENT.md`

### Unused Scripts
- `scripts/deploy-digital-ocean.sh`
- `scripts/docker-quick-deploy.sh`
- `scripts/docker-troubleshoot.sh`
- `scripts/fix-deployment.sh`
- `scripts/generate-secrets.py`
- `scripts/install-security-packages.sh`
- `scripts/monitor.sh`
- `scripts/reset-database.sh`
- `scripts/restart-web.sh`
- `scripts/setup-ssl.sh`
- `scripts/ssl-renew.sh`
- `scripts/test-deployment.sh`

### Development & Testing Files
- `debug_generation.py`
- `test_presenter_fix.py`
- `test_read_by_fix.py`
- `test_manual_time_input.html`
- `test_full_content_report.pdf`
- `build-css.sh`
- `structure/` directory (entire directory removed)
- `templates/dashboard-old.html`
- `templates/test_csrf_form.html`
- `templates/test_error_index.html`

### Alternative Settings
- `radio_mentions_project/settings_production_updated.py`

## Usage

To deploy to production, use:

```bash
# Build and deploy
./scripts/docker-build.sh build --target production
./scripts/docker-build.sh deploy

# Or combined
./scripts/docker-build.sh build --target production && ./scripts/docker-build.sh deploy
```

## Key Optimizations Applied

1. **Database Configuration**: Fixed PostgreSQL connection issues by removing MySQL-specific `init_command`
2. **Resource Limits**: Added memory and CPU limits to all services
3. **Gunicorn Optimization**: Reduced workers and memory usage
4. **Nginx Optimization**: Reduced worker processes and connection limits
5. **Celery Optimization**: Added task limits and memory management

The deployment is now optimized for resource efficiency and should resolve the 502 Bad Gateway errors and high resource usage.
