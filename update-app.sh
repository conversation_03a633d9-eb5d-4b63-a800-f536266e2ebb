#!/bin/bash

# =============================================================================
# Enhanced AppRadio Deployment Script
# =============================================================================
# Production-ready deployment script with automated migrations, backups, and rollback
#
# Usage:
#   ./update-app.sh [OPTIONS]
#
# Options:
#   --apps "app1,app2"           Django apps to migrate (e.g., "core,mentions")
#   --commands "cmd1,cmd2"       Management commands to run (e.g., "populate_recurring_history")
#   --skip-backup               Skip database backup (NOT RECOMMENDED)
#   --skip-build                Skip Docker image rebuild
#   --rollback                  Rollback to previous version
#   --dry-run                   Show what would be done without executing
#   --help                      Show this help message
#
# Examples:
#   ./update-app.sh --apps "core,mentions" --commands "populate_recurring_history"
#   ./update-app.sh --rollback
#   ./update-app.sh --dry-run --apps "core"
#
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BACKUP_DIR="./backups"
BACKUP_RETENTION_DAYS=7
COMPOSE_PROJECT_NAME="${COMPOSE_PROJECT_NAME:-appradio}"
HEALTH_CHECK_TIMEOUT=60
ROLLBACK_TAG="previous"

# Default values
APPS_TO_MIGRATE=""
MANAGEMENT_COMMANDS=""
SKIP_BACKUP=false
SKIP_BUILD=false
DRY_RUN=false
ROLLBACK=false

# Logging functions
log_info() {
    echo -e "${GREEN}ℹ️  $1${NC}"
}

log_warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_step() {
    echo -e "${BLUE}� $1${NC}"
}

log_debug() {
    echo -e "${PURPLE}🐛 $1${NC}"
}

# Help function
show_help() {
    cat << EOF
Enhanced AppRadio Deployment Script

Usage: $0 [OPTIONS]

Options:
    --apps "app1,app2"           Django apps to migrate (e.g., "core,mentions")
    --commands "cmd1,cmd2"       Management commands to run (e.g., "populate_recurring_history")
    --skip-backup               Skip database backup (NOT RECOMMENDED)
    --skip-build                Skip Docker image rebuild
    --rollback                  Rollback to previous version
    --dry-run                   Show what would be done without executing
    --help                      Show this help message

Examples:
    $0 --apps "core,mentions" --commands "populate_recurring_history"
    $0 --rollback
    $0 --dry-run --apps "core"

EOF
}

echo -e "${GREEN}🚀 Enhanced AppRadio Deployment Script${NC}"
echo -e "${CYAN}================================================${NC}"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --apps)
            APPS_TO_MIGRATE="$2"
            shift 2
            ;;
        --commands)
            MANAGEMENT_COMMANDS="$2"
            shift 2
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --rollback)
            ROLLBACK=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Utility functions
execute_command() {
    local cmd="$1"
    local description="$2"

    if [ "$DRY_RUN" = true ]; then
        log_debug "[DRY RUN] Would execute: $cmd"
        return 0
    fi

    log_step "$description"
    if eval "$cmd"; then
        log_success "$description completed"
        return 0
    else
        log_error "$description failed"
        return 1
    fi
}

check_prerequisites() {
    log_step "Checking prerequisites..."

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running or not accessible"
        exit 1
    fi

    # Check if docker-compose is available
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker compose is not installed"
        exit 1
    fi

    # Check if required files exist
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml not found"
        exit 1
    fi

    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"

    log_success "Prerequisites check passed"
}

get_ssl_config() {
    if [ -d "docker/certbot/conf/live" ] && [ "$(ls -A docker/certbot/conf/live 2>/dev/null)" ]; then
        echo "ssl"
    else
        echo "http"
    fi
}

get_compose_command() {
    local ssl_config=$(get_ssl_config)

    if [ "$ssl_config" = "ssl" ]; then
        DOMAIN_NAME=$(ls docker/certbot/conf/live/ | head -n 1)
        if [ -z "$DOMAIN_NAME" ]; then
            log_error "Could not determine domain name from certificates"
            exit 1
        fi
        export NGINX_SERVER_NAME=$DOMAIN_NAME
        export DOMAIN_NAME=$DOMAIN_NAME
        echo "DOMAIN_NAME=$DOMAIN_NAME NGINX_SERVER_NAME=$NGINX_SERVER_NAME docker compose -f docker-compose.yml -f docker-compose.ssl.yml"
    else
        echo "docker compose"
    fi
}

create_backup() {
    if [ "$SKIP_BACKUP" = true ]; then
        log_warn "Skipping backup as requested"
        return 0
    fi

    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/backup_${timestamp}.sql"
    local compose_cmd=$(get_compose_command)

    log_step "Creating database backup..."

    # Create database backup
    execute_command "$compose_cmd exec -T db pg_dump -U postgres radio_mentions > $backup_file" \
        "Database backup creation"

    if [ -f "$backup_file" ]; then
        log_success "Backup created: $backup_file"

        # Compress backup
        execute_command "gzip $backup_file" "Compressing backup"

        # Clean old backups
        execute_command "find $BACKUP_DIR -name 'backup_*.sql.gz' -mtime +$BACKUP_RETENTION_DAYS -delete" \
            "Cleaning old backups (older than $BACKUP_RETENTION_DAYS days)"

        # Store backup path for potential rollback
        echo "$backup_file.gz" > "$BACKUP_DIR/latest_backup.txt"
    else
        log_error "Backup creation failed"
        exit 1
    fi
}

tag_current_images() {
    log_step "Tagging current images for rollback..."
    local compose_cmd=$(get_compose_command)

    # Get current image IDs
    local web_image=$($compose_cmd images -q web 2>/dev/null || echo "")
    local celery_image=$($compose_cmd images -q celery 2>/dev/null || echo "")
    local celery_beat_image=$($compose_cmd images -q celery-beat 2>/dev/null || echo "")

    if [ -n "$web_image" ]; then
        execute_command "docker tag $web_image ${COMPOSE_PROJECT_NAME}_web:$ROLLBACK_TAG" \
            "Tagging web image for rollback"
    fi

    if [ -n "$celery_image" ]; then
        execute_command "docker tag $celery_image ${COMPOSE_PROJECT_NAME}_celery:$ROLLBACK_TAG" \
            "Tagging celery image for rollback"
    fi

    if [ -n "$celery_beat_image" ]; then
        execute_command "docker tag $celery_beat_image ${COMPOSE_PROJECT_NAME}_celery-beat:$ROLLBACK_TAG" \
            "Tagging celery-beat image for rollback"
    fi
}

perform_rollback() {
    log_warn "Performing rollback to previous version..."
    local compose_cmd=$(get_compose_command)

    # Check if rollback images exist
    if ! docker image inspect "${COMPOSE_PROJECT_NAME}_web:$ROLLBACK_TAG" >/dev/null 2>&1; then
        log_error "No rollback images found. Cannot rollback."
        exit 1
    fi

    # Stop current services
    execute_command "$compose_cmd down" "Stopping current services"

    # Restore previous images
    execute_command "docker tag ${COMPOSE_PROJECT_NAME}_web:$ROLLBACK_TAG ${COMPOSE_PROJECT_NAME}_web:latest" \
        "Restoring web image"
    execute_command "docker tag ${COMPOSE_PROJECT_NAME}_celery:$ROLLBACK_TAG ${COMPOSE_PROJECT_NAME}_celery:latest" \
        "Restoring celery image"
    execute_command "docker tag ${COMPOSE_PROJECT_NAME}_celery-beat:$ROLLBACK_TAG ${COMPOSE_PROJECT_NAME}_celery-beat:latest" \
        "Restoring celery-beat image"

    # Start services with previous images
    execute_command "$compose_cmd up -d" "Starting services with previous version"

    # Wait for services
    sleep 10

    # Verify rollback
    if verify_deployment; then
        log_success "Rollback completed successfully"
    else
        log_error "Rollback verification failed"
        exit 1
    fi

    exit 0
}

run_migrations() {
    if [ -z "$APPS_TO_MIGRATE" ]; then
        log_info "No apps specified for migration"
        return 0
    fi

    local compose_cmd=$(get_compose_command)
    log_step "Running database migrations..."

    # Convert comma-separated apps to array
    IFS=',' read -ra APPS <<< "$APPS_TO_MIGRATE"

    for app in "${APPS[@]}"; do
        app=$(echo "$app" | xargs) # trim whitespace

        log_step "Creating migrations for app: $app"
        execute_command "$compose_cmd run --rm web python manage.py makemigrations $app" \
            "Creating migrations for $app"

        log_step "Applying migrations for app: $app"
        execute_command "$compose_cmd run --rm web python manage.py migrate $app" \
            "Applying migrations for $app"
    done

    # Run general migrate to catch any other pending migrations
    log_step "Applying any remaining migrations..."
    execute_command "$compose_cmd run --rm web python manage.py migrate" \
        "Applying remaining migrations"
}

run_management_commands() {
    if [ -z "$MANAGEMENT_COMMANDS" ]; then
        log_info "No management commands specified"
        return 0
    fi

    local compose_cmd=$(get_compose_command)
    log_step "Running management commands..."

    # Convert comma-separated commands to array
    IFS=',' read -ra COMMANDS <<< "$MANAGEMENT_COMMANDS"

    for cmd in "${COMMANDS[@]}"; do
        cmd=$(echo "$cmd" | xargs) # trim whitespace

        log_step "Running management command: $cmd"
        execute_command "$compose_cmd run --rm web python manage.py $cmd" \
            "Running management command: $cmd"
    done
}

verify_deployment() {
    local compose_cmd=$(get_compose_command)
    log_step "Verifying deployment..."

    # Check if all services are running
    local running_services=$($compose_cmd ps --services --filter "status=running" | wc -l)
    local total_services=$($compose_cmd ps --services | wc -l)

    if [ "$running_services" -ne "$total_services" ]; then
        log_error "Not all services are running ($running_services/$total_services)"
        return 1
    fi

    # Wait for web service to be healthy
    local timeout=$HEALTH_CHECK_TIMEOUT
    local count=0

    while [ $count -lt $timeout ]; do
        if $compose_cmd exec -T web python manage.py check --deploy >/dev/null 2>&1; then
            log_success "Health check passed"
            return 0
        fi

        sleep 2
        count=$((count + 2))
        log_debug "Waiting for health check... ($count/$timeout seconds)"
    done

    log_error "Health check failed after $timeout seconds"
    return 1
}

collect_static_files() {
    local compose_cmd=$(get_compose_command)
    log_step "Collecting static files..."

    execute_command "$compose_cmd run --rm web python manage.py collectstatic --noinput" \
        "Collecting static files"
}

# =============================================================================
# MAIN EXECUTION FLOW
# =============================================================================

# Handle rollback request
if [ "$ROLLBACK" = true ]; then
    perform_rollback
fi

# Show configuration
log_info "Deployment Configuration:"
log_info "  Apps to migrate: ${APPS_TO_MIGRATE:-'None'}"
log_info "  Management commands: ${MANAGEMENT_COMMANDS:-'None'}"
log_info "  Skip backup: $SKIP_BACKUP"
log_info "  Skip build: $SKIP_BUILD"
log_info "  Dry run: $DRY_RUN"

if [ "$DRY_RUN" = true ]; then
    log_warn "DRY RUN MODE - No actual changes will be made"
fi

# Check prerequisites
check_prerequisites

# Determine SSL configuration
SSL_CONFIG=$(get_ssl_config)
COMPOSE_CMD=$(get_compose_command)

if [ "$SSL_CONFIG" = "ssl" ]; then
    DOMAIN_NAME=$(ls docker/certbot/conf/live/ | head -n 1)
    log_success "SSL certificates found - using SSL configuration"
    log_info "Domain: $DOMAIN_NAME"
else
    log_warn "No SSL certificates found - using HTTP-only configuration"
    log_info "Run './enable-https.sh your-domain.com' to enable HTTPS"
fi

# Create backup before any changes
create_backup

# Tag current images for potential rollback
tag_current_images

# Handle nginx configuration for HTTP-only mode
if [ "$SSL_CONFIG" = "http" ]; then
    if [ ! -f "docker/nginx/default.conf.http-only" ]; then
        log_error "HTTP-only nginx configuration not found"
        exit 1
    fi

    # Backup current config and use HTTP-only
    if [ -f "docker/nginx/default.conf" ]; then
        execute_command "cp docker/nginx/default.conf docker/nginx/default.conf.backup" \
            "Backing up nginx configuration"
    fi
    execute_command "cp docker/nginx/default.conf.http-only docker/nginx/default.conf" \
        "Switching to HTTP-only nginx configuration"
fi

# Build and deploy
if [ "$SKIP_BUILD" = false ]; then
    log_step "Building and deploying services..."
    execute_command "$COMPOSE_CMD up -d --build" "Building and starting services"
else
    log_step "Deploying services without rebuild..."
    execute_command "$COMPOSE_CMD up -d" "Starting services"
fi

# Restore nginx config for HTTP-only mode
if [ "$SSL_CONFIG" = "http" ] && [ -f "docker/nginx/default.conf.backup" ]; then
    execute_command "mv docker/nginx/default.conf.backup docker/nginx/default.conf" \
        "Restoring nginx configuration"
fi

# Wait for services to stabilize
log_step "Waiting for services to stabilize..."
sleep 15

# Run migrations
run_migrations

# Collect static files
collect_static_files

# Run management commands
run_management_commands

# Verify deployment
if ! verify_deployment; then
    log_error "Deployment verification failed!"
    log_warn "Consider running: $0 --rollback"
    exit 1
fi

# Show final status
log_step "Checking final service status..."
execute_command "$COMPOSE_CMD ps" "Service status check"

log_success "Deployment completed successfully!"

# Show access information
if [ "$SSL_CONFIG" = "ssl" ]; then
    log_success "🌐 Your app is available at: https://$DOMAIN_NAME"
else
    log_success "🌐 Your app is available at: http://localhost (or your server IP)"
fi

# Show summary
echo -e "${CYAN}================================================${NC}"
echo -e "${GREEN}📋 Deployment Summary:${NC}"
[ -n "$APPS_TO_MIGRATE" ] && echo -e "  ✅ Migrated apps: $APPS_TO_MIGRATE"
[ -n "$MANAGEMENT_COMMANDS" ] && echo -e "  ✅ Ran commands: $MANAGEMENT_COMMANDS"
[ "$SKIP_BACKUP" = false ] && echo -e "  ✅ Database backup created"
echo -e "  ✅ Services verified and running"
echo -e "${CYAN}================================================${NC}"
