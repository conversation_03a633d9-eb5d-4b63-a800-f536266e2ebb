# Docker Build Configuration for CPU Optimization
# Edit these values to control resource usage during builds

# CPU and Memory Limits
CPU_LIMIT=2
MEMORY_LIMIT=2g
PARALLEL_JOBS=2

# Build Options
BUILD_TARGET=production
NO_CACHE=false
VERBOSE=false

# Docker Build Arguments
DOCKER_BUILDKIT=1
BUILDKIT_PROGRESS=plain

# Node.js Optimization
NODE_OPTIONS=--max-old-space-size=1024
NPM_CONFIG_MAXSOCKETS=2

# Python Optimization
PIP_COMPILE_JOBS=2
MAKEFLAGS=-j2

# Build Context Optimization
DOCKER_CONTEXT_SIZE_LIMIT=100M

# Comments:
# - CPU_LIMIT: Number of CPU cores to use (1-4 recommended)
# - MEMORY_LIMIT: RAM limit for build process (1g-4g recommended)
# - PARALLEL_JOBS: Number of parallel compilation jobs
# - Set CPU_LIMIT=1 and MEMORY_LIMIT=1g for very limited systems
# - Set CPU_LIMIT=4 and MEMORY_LIMIT=4g for powerful systems
